const getTemplateDetails = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/gettemplatedetails', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const updateEmailTemplate = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/updateemailtemplate', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const sendMail = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/sendmail', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};


export default {
    getTemplateDetails,
    updateEmailTemplate,
    sendMail
};
