<?php

namespace App\Imports;

use App\Models\ConsumerMigration;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class UserStatusImport implements ToModel, WithHeadingRow, WithBatchInserts, WithChunkReading
{
    /**
     * @param array $row
     * This function actully imports the data as row from Excel Sheet. Here we used the WithHeadingRow to get the Data with Heading. Do Not try to get the rows with index.
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row)
    {
        $email = $row['email1'];
        if (!empty($email)) {
            try {
                // Check if consumer already exists in Database
                $checkConsumerExists = ConsumerMigration::where('email', $email)->first();
                if (!empty($checkConsumerExists)) { // If exists update the card number
                    $action = 'added'; // For logging purpose
                    // Updating the status of the consumer
                    $user = ConsumerMigration::find($checkConsumerExists->id);
                    $status = returnUserStatusId($row['statusdescription']); // Fetching the id for the status
                    if ($user->status != '' && $user->status != $status) {
                        $user->updated = 1;
                        $action = 'updated'; // For logging purpose
                    } else if ($user->status != '' && $user->status == $status) {
                        $action = 'insertion/updation skipped due to duplicate entry'; // For logging purpose
                        insertSkippedDataLog('Consumer', 'email1', $row['email1'], "Consumer status insertion/updation skipped due to duplicate entry in Consumer Migration Table for User Email : " . $email . " with User ID : " . $checkConsumerExists->id, json_encode($row), 'Enrollment Data');
                    }
                    $user->status = $status;
                    $user->save();

                    Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer status " . $action . " in Consumer Migration Table for User Email : " . $email . " with User ID : " . $checkConsumerExists->id);
                } else {
                    Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer status updatation skipped for non availability of email in Consumer Migration Table for User Email : " . $email);
                    insertSkippedDataLog('Consumer', 'email1', $row['email1'], "Consumer status updatation skipped for non availability of email in Consumer Migration Table for User Email : " . $email, json_encode($row), 'Enrollment Data');
                }
            } catch (\Exception $e) {
                Log::channel('datamigration')->error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured during updation of status in Consumer Migration Table for User Email : " . $email . ".", [EXCEPTION => $e]);
                insertSkippedDataLog('Consumer', 'email1', $row['email1'], $e, json_encode($row), 'Enrollment Data');
            }
        }
    }

    public function batchSize(): int
    {
        return 1000;
    }

    public function chunkSize(): int
    {
        return 5000;
    }
}
