<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;


class EcommerceMerchantKey extends Model 
{
    

    protected $table = 'ecommerce_merchant_keys';

    /**
     * __construct
     * This function will insert an alphanumeric 32 character unique id as primary key in the table whenever a data get inserted
     * @param  mixed $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        $this->id = generateUUID();
        parent::__construct($attributes);
    }

    protected $fillable = [
        'merchant_id',
        'app_key',
        'api_secret',
        'ecommerce_category_id',
        'ecommerce_integrator_id',
        'status',
    ];
    public $timestamps = true;
    public $incrementing = false;
    //
}
