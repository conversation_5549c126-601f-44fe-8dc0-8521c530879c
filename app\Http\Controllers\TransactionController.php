<?php

namespace App\Http\Controllers;

use App\Exports\AllTransactionExport;
use App\Exports\ConsumerTransactionExport;
use App\Exports\ConsumerVoidTransactionExport;
use App\Exports\MonthlySalesGrowthExport;
use App\Exports\ReturnTransactionDailyExport;
use App\Exports\ReturnTransactionExport;
use App\Exports\ReturnTransactionGroupExport;
use App\Exports\UsersPurchaseExport;
use App\Exports\UsersPurchasePowerExport;
use App\Http\Clients\Acheck21HttpClient;
use App\Http\Clients\ApiHttpClient;
use App\Http\Factories\EmailExecutor\EmailExecutorFactory;
use App\Http\Factories\Transaction\TransactionFactory;
use App\Models\FeesMaster;
use App\Models\GlobalReasonMaster;
use App\Models\MerchantStores;
use App\Models\StatusMaster;
use App\Models\TimezoneMaster;
use App\Models\TransactionDetails;
use App\Models\TransactionModificationCustomReason;
use App\Models\TransactionModificationHistory;
use App\Models\TransactionModificationReason;
use App\Models\User;
use App\Models\UserBankAccountInfo;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;

class TransactionController extends Controller
{
    public function __construct(Request $request)
    {
        $this->request = $request;
        // $this->bankValidator = new BankValidatorFactory();
        $this->acheck = new Acheck21HttpClient();
        $this->api = new ApiHttpClient();
        $this->transaction = new TransactionFactory();
        $this->emailexecutor = new EmailExecutorFactory();
    }

    /**
     * This function cancels a consumer transaction that has been posted into acheck21
     */

    public function cancelTransaction(Request $request)
    {
        $data = $request->all();
        $data['user_id'] = Auth::user()->user_id;
        Log::info("Void Transaction Initiated: " . getTimeElapsed($request));

        $requestTimeFloat = $request->server('REQUEST_TIME_FLOAT');
        $requestdata['server'] = $requestTimeFloat;

        //get the transaction for the given transaction id
        $transaction = TransactionDetails::where('id', $data['transaction_id'])->first();
        $scheduled_posting_date = $transaction->scheduled_posting_date;
        if ($transaction->is_ecommerce == 1) {
            // Check if transaction is eligible for void or not
            $local_transaction_time = $transaction->scheduled_posting_time;
            // check modifield transction then get modified transaction scheduled_posting_time
            if ($transaction->change_request_transaction_ref_no != null) {
                $m_transaction = TransactionDetails::where('id', $transaction->change_request_transaction_ref_no)->first();
                if ($m_transaction) {
                    $local_transaction_time = $m_transaction->scheduled_posting_time;
                    $scheduled_posting_date = $m_transaction->scheduled_posting_date;
                }
            }
            $is_admin_driven = 1;
        } else {
            $local_transaction_time = $transaction->transaction_time;
            $is_admin_driven = 0;
        }

        if (checkTransactionEligibleForCancellation($local_transaction_time, $is_admin_driven, $scheduled_posting_date)) {
            // if v2 transaction call to acheck21 for v2 void
            if ($transaction->is_v1 == 0) {
                $result = $this->transaction->voidV2Transaction($transaction, $data, $requestdata);
                Log::info("Void Transaction Completed successfully: " . getTimeElapsed($request));
                return $result;
            }
            $result = $this->transaction->voidV1Transaction($data);
            return renderResponse($result->code, $result->message, $result->data); // response returned
        } else {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Failed to cancel void Transaction ID: " . $data['transaction_id']);
            $message = trans('message.transaction_cancelation_time_exceeded');
            return renderResponse(FAIL, $message, null); // response returned
        }
    }

    /**
     * This function cancels a consumer transaction that has been posted into acheck21
     */
    public function revokeVoidTransaction(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Revoke Void Transaction procedure started...");
        $data = $request->all();
        $data['user_id'] = Auth::user()->user_id;

        // Fetch the transaction details for the given transaction id
        $transaction = TransactionDetails::where('id', $data['transaction_id'])->first();
        $scheduled_posting_date = $transaction->scheduled_posting_date;
        if ($transaction->is_ecommerce == 1) {
            // Check if transaction is eligible for void or not
            $local_transaction_time = $transaction->scheduled_posting_time;
            // check modifield transction then get modified transaction scheduled_posting_time
            if ($transaction->change_request_transaction_ref_no != null) {
                $m_transaction = TransactionDetails::where('id', $transaction->change_request_transaction_ref_no)->first();
                if ($m_transaction) {
                    $local_transaction_time = $m_transaction->scheduled_posting_time;
                    $scheduled_posting_date = $m_transaction->scheduled_posting_date;
                }
            }
            $is_admin_driven = 1;
        } else {
            $local_transaction_time = $transaction->transaction_time;
            $is_admin_driven = 0;
        }
        if (checkTransactionEligibleForCancellation($local_transaction_time, $is_admin_driven, $scheduled_posting_date)) {
            // Function to revoke void transaction
            $res = json_decode(json_encode($this->transaction->revokeVoidTransaction($transaction, $data), true), true);
            // Formatting response as it comes from transaction factory
            $message = $res['original']['message'];
            $code = $res['original']['code'];
            $data = $res['original']['data'];
            if ($code == SUCCESS) {
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction ID: " . $transaction->id . " revoked successfully.");
            }
            return renderResponse($code, $message, $data); // response returned
        } else {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Failed to revoke void Transaction ID: " . $data['transaction_id']);
            $message = trans('message.transaction_cancelation_time_exceeded');
            return renderResponse(FAIL, $message, null); // response returned
        }
    }

    /**
     * getConsumerTransactionReport
     * This function is used to get the consumer transactions
     * @param  mixed $request
     * @return void
     */
    public function getConsumerTransactionReport(Request $request)
    {
        $declined_status = StatusMaster::where('code', DECLINED)->first();
        $from_date = $request->get('from_date');
        $to_date = $request->get('to_date');

        $sql = "SELECT transaction_details.transaction_number,concat_ws(' ', users.first_name, users.middle_name, users.last_name) as consumer_name, transaction_details.id, registered_merchant_master.merchant_name,  merchant_stores.retailer, CASE WHEN transaction_details.pos_web_identifier != '' THEN transaction_details.pos_web_identifier ELSE terminal_master.terminal_name END as terminal_name, transaction_details.transaction_time, transaction_details.local_transaction_time, transaction_details.is_v1,  timezone_masters.timezone_name, transaction_details.scheduled_posting_date, modified_transaction.scheduled_posting_date as m_scheduled_posting_date, transaction_details.scheduled_posting_time, modified_transaction.scheduled_posting_time as m_scheduled_posting_time,transaction_details.consumer_approval_for_change_request, transaction_details.expiration_datetime, modified_status_master.code as m_code, status_master.code, transaction_details.change_request, transaction_details.amount, transaction_details.updated_amount, transaction_details.attempt_count,transaction_details.local_transaction_date, transaction_details.is_ecommerce, transaction_details.admin_driven, transaction_details.delivery_fee,
        CASE
            WHEN transaction_details.change_request = 1 AND (modified_status_master.code='" . VOIDED . "' OR modified_status_master.status IS NULL) THEN casm.`status`
            WHEN modified_status_master.status IS NOT NULL THEN modified_status_master.status
            WHEN status_master.status = '" . GENERAL_EXCEPTION . "' THEN '" . TRANSACTION_DECLINED . "'
            ELSE status_master.status
        END AS status,
        (CASE
            WHEN (modified_transaction.id != '') THEN modified_transaction.tip_amount
            ELSE transaction_details.tip_amount
        END) as last_approve_tip_amount,
        (CASE
            WHEN (modified_transaction.consumer_bank_posting_amount != '') THEN modified_transaction.consumer_bank_posting_amount
            ELSE transaction_details.consumer_bank_posting_amount
        END) as consumer_bank_posting_amount,
        (CASE
            WHEN (modified_transaction.reward_amount_used != '') THEN modified_transaction.reward_amount_used
            ELSE transaction_details.reward_amount_used
        END) as reward_amount_used
        FROM transaction_details
        left join `transaction_details` as `modified_transaction` on `modified_transaction`.`id` = `transaction_details`.`change_request_transaction_ref_no`
        left join `status_master` as `modified_status_master` on `modified_status_master`.`id` = `modified_transaction`.`status_id`
        left join `status_master` as `casm` on `casm`.`id` = `transaction_details`.`consumer_approval_for_change_request`
        JOIN users ON users.user_id = transaction_details.consumer_id
        JOIN terminal_master ON terminal_master.id = transaction_details.terminal_id
        JOIN merchant_stores ON merchant_stores.id = terminal_master.merchant_store_id
        JOIN timezone_masters ON timezone_masters.id = transaction_details.timezone_id
        JOIN status_master ON status_master.id = transaction_details.status_id
        JOIN registered_merchant_master ON registered_merchant_master.id = merchant_stores.merchant_id
        WHERE transaction_details.transaction_ref_no is null and ((`transaction_details`.`attempt_count` = 0 and transaction_details.change_request_transaction_ref_no IS NULL) or (`transaction_details`.`attempt_count` != 0))AND transaction_details.isCanpay = 0 ";

        if ($request->get('from_date')) {
            $sql .= " AND transaction_details.local_transaction_date >= ?";
        }
        if ($request->get('to_date')) {
            $sql .= " AND transaction_details.local_transaction_date <= ?";
        }
        if ($request->get('email')) {
            $sql .= " AND users.email = ? ";
        }
        if ($request->get('phone_no')) {
            $sql .= " AND users.phone = ? ";
        }
        if ($request->get('consumer')) {
            $sql .= " AND users.user_id = ? ";
        }
        $sql .= " GROUP BY transaction_details.id ORDER BY transaction_details.transaction_time DESC";

        $searchArray = [
            $from_date,
            $to_date,
        ];
        if ($request->get('email')) {
            array_push($searchArray, $request->get('email'));
        }
        if ($request->get('phone_no')) {
            array_push($searchArray, $request->get('phone_no'));
        }
        if ($request->get('consumer')) {
            array_push($searchArray, $request->get('consumer'));
        }

        $transactions = DB::connection(MYSQL_RO)->select($sql, $searchArray);

        $data = array();
        if (!empty($transactions)) {
            // Creating array to show the values in frontend
            foreach ($transactions as $transaction) {
                $ecommerce_status_code = $transaction->m_code ? $transaction->m_code : $transaction->code;
                $nestedData['transaction_number'] = $transaction->transaction_number;
                $nestedData['consumer_name'] = $transaction->consumer_name;
                $nestedData['merchant_name'] = $transaction->merchant_name;
                $nestedData['store_name'] = $transaction->retailer;
                $nestedData['terminal_name'] = $transaction->terminal_name;
                $nestedData['edit'] = $transaction->id;
                $nestedData['attempt_count'] = $transaction->attempt_count;
                $nestedData['amount'] = number_format($transaction->amount - $transaction->delivery_fee, 2);
                $nestedData['updated_amount'] = number_format($transaction->updated_amount, 2);
                $nestedData['delivery_fee'] = number_format($transaction->delivery_fee, 2);
                $nestedData['last_approve_tip_amount'] = number_format($transaction->last_approve_tip_amount, 2);
                $nestedData['transaction_time'] = date('m-d-Y H:i:s', strtotime($transaction->local_transaction_time)) . ' <br/> (' . $transaction->timezone_name . ')';
                $nestedData['consumer_bank_posting_amount'] = $transaction->consumer_bank_posting_amount;
                $nestedData['reward_amount_used'] = $transaction->reward_amount_used;

                $nestedData['status'] = $transaction->status;

                $scheduled_posting_date = $transaction->m_scheduled_posting_date ? $transaction->m_scheduled_posting_date : $transaction->scheduled_posting_date;
                $scheduled_posting_time = $transaction->m_scheduled_posting_time ? $transaction->m_scheduled_posting_time : $transaction->scheduled_posting_time;
                // when transaction need merchant admin approval then show Pending Store's Approval message
                if (!$scheduled_posting_date && $transaction->status == PENDING_STATUS_NAME) {
                    $nestedData['status'] = PENDING_STORE_APPROVAL;
                }
                if ($nestedData['status'] == $declined_status->status) {
                    // if consumer declined the transaction then no need to show cancel button
                    $nestedData['cancelable'] = 0;
                } else {
                    if ((!in_array($ecommerce_status_code, [PENDING]) && $transaction->change_request == 0) || $ecommerce_status_code == EXPIRED) {
                        $nestedData['cancelable'] = 0;
                    } else {
                        if ($transaction->is_ecommerce == 1) {
                            $local_transaction_time = $scheduled_posting_time;
                            $is_admin_driven = 1;
                        } else {
                            $local_transaction_time = $transaction->transaction_time;
                            $is_admin_driven = 0;
                        }
                        $nestedData['cancelable'] = checkTransactionEligibleForCancellation($local_transaction_time, $is_admin_driven, $scheduled_posting_date);
                    }
                }
                $nestedData['is_v1'] = $transaction->is_v1;
                $data[] = $nestedData;
            }
        }

        $message = trans('message.return_transaction_fetch_success');
        return renderResponse(SUCCESS, $message, $data); // Returning response
    }

    /**
     * getConsumerTransactionExport
     * This is the export function for Consumer Transaction Report
     * @param  mixed $request
     * @return void
     */
    public function getConsumerTransactionExport(Request $request)
    {
        return Excel::download(new ConsumerTransactionExport($request), 'consumer_transaction_' . date('m-d-Y H:i:s') . '.xlsx');
    }

    /**
     * _getAllTransactionsCommon
     * This function is used to get the all the transactions
     * @param  mixed $request
     */
    private function _getAllTransactionsCommonQuery($request, $is_count = false)
    {
        // Main Query with Read Replica database passes inside ON
        // Main Query
        $query = DB::connection(MYSQL_RO)->table('transaction_details')->leftJoin('users', 'users.user_id', '=', 'transaction_details.consumer_id')
            ->leftJoin('transaction_details as modified_transaction', 'modified_transaction.id', '=', 'transaction_details.change_request_transaction_ref_no')
            ->leftJoin('status_master as modified_status_master', 'modified_status_master.id', '=', 'modified_transaction.status_id')
            ->leftJoin('status_master as casm', 'casm.id', '=', 'transaction_details.consumer_approval_for_change_request')
            ->join('terminal_master', 'terminal_master.id', '=', 'transaction_details.terminal_id')
            ->join('merchant_stores', 'merchant_stores.id', '=', 'terminal_master.merchant_store_id')
            ->leftJoin('timezone_masters', 'timezone_masters.id', '=', 'transaction_details.timezone_id')
            ->join('status_master', 'status_master.id', '=', 'transaction_details.status_id')
            ->join('registered_merchant_master', 'registered_merchant_master.id', '=', 'merchant_stores.merchant_id')
            ->from(DB::raw('transaction_details force index(date_key)'));

        if ($is_count) {
            $query = $query->selectRaw('COUNT(DISTINCT transaction_details.id) as total_count');
        } else {
            $query = $query->select('transaction_details.transaction_number', 'transaction_details.zipline_trans_id', 'transaction_details.id', 'registered_merchant_master.merchant_name', 'users.first_name', 'users.middle_name', 'users.last_name', 'merchant_stores.retailer', 'transaction_details.transaction_time', 'transaction_details.local_transaction_time', 'transaction_details.is_v1', 'timezone_masters.timezone_name', 'transaction_details.scheduled_posting_date', 'modified_transaction.scheduled_posting_date as m_scheduled_posting_date', 'transaction_details.scheduled_posting_time', 'modified_transaction.scheduled_posting_time as m_scheduled_posting_time', 'transaction_details.consumer_approval_for_change_request', 'transaction_details.expiration_datetime', 'transaction_details.amount', 'transaction_details.updated_amount', 'transaction_details.attempt_count', 'transaction_details.local_transaction_date', 'transaction_details.is_ecommerce', 'transaction_details.admin_driven', 'registered_merchant_master.merchant_profile_name', 'transaction_details.delivery_fee')
                ->selectRaw("
                (CASE
                    WHEN (modified_transaction.id != '') THEN modified_transaction.tip_amount
                    ELSE transaction_details.tip_amount
                END) as last_approve_tip_amount")
                ->selectRaw("CASE WHEN transaction_details.pos_web_identifier != '' THEN transaction_details.pos_web_identifier ELSE terminal_master.terminal_name END as terminal_name")
                ->selectRaw(
                    "CASE
                    WHEN transaction_details.change_request = 1 AND (modified_status_master.code='" . VOIDED . "' OR modified_status_master.status IS NULL) THEN casm.`status`
                    WHEN modified_status_master.status IS NOT NULL THEN modified_status_master.status
                    WHEN status_master.status = '" . GENERAL_EXCEPTION . "' THEN '" . TRANSACTION_DECLINED . "'
                    ELSE status_master.status
                END AS status"
                )->groupBy('transaction_details.id');

            // When we access this API from Remote Pay Transaction Report page
            if ($request->get('is_remote_pay_report') == '1') {
                $awaiting_consumer_approval = getStatus(AWAITING_CONSUMER_APPROVAL);
                $approved_by_consumer = getStatus(APPROVED_BY_CONSUMER);
                $query = $query->leftJoin('transaction_modification_history as tmh', function ($query) use ($awaiting_consumer_approval, $approved_by_consumer) {
                    $query->on('tmh.transaction_id', '=', 'transaction_details.id')
                        ->join('transaction_modification_reason as tmr', 'tmr.id', '=', 'tmh.reason_id')
                        ->where(function ($q1) use ($awaiting_consumer_approval, $approved_by_consumer) {
                            $q1->where(function ($q2) use ($awaiting_consumer_approval) {
                                $q2->where('tmh.status_id', $awaiting_consumer_approval);
                            })->orWhere(function ($q2) use ($approved_by_consumer) {
                                $q2->where('tmr.reason_type', 'decrease')->where('tmh.status_id', $approved_by_consumer);
                            });
                        });
                })->selectRaw("GROUP_CONCAT(tmh.created_at) AS modification_time");
            }
        }

        $query->whereRaw('transaction_details.transaction_ref_no is null')
            ->where(function ($q1) {
                $q1->where(function ($q2) {
                    $q2->where('transaction_details.attempt_count', '=', 0)->whereRaw('transaction_details.change_request_transaction_ref_no IS NULL');
                })->orWhere(function ($q2) {
                    $q2->where('transaction_details.attempt_count', '!=', 0);
                });
            })
            ->where('transaction_details.isCanpay', 0);

        $from_date = $request->get('from_date');
        $to_date = $request->get('to_date');

        if ($request->get('search_by_scheduled_posting_date') == '1') {
            $query = $query->where(function ($q1) use ($from_date, $to_date) {
                $q1->whereBetween('transaction_details.scheduled_posting_date', [$from_date, $to_date])
                    ->orWhereBetween('modified_transaction.scheduled_posting_date', [$from_date, $to_date]);
            });
        } else {
            $query = $query->whereRaw("transaction_details.local_transaction_date BETWEEN ? AND ?", [$from_date, $to_date]);
        }

        // When we access this API from Remote Pay Transaction Report page
        // Get Only pending, successful, return, voided, unpaid, and modified e-commerce transactions
        if ($request->get('is_remote_pay_report') == '1') {
            $pending_statu_id = getStatus(PENDING);
            $success_status_id = getStatus(SUCCESS);
            $return_status_id = getStatus(RETURNED);
            $voided_status_id = getStatus(VOIDED);
            $unpaid_status_id = getStatus(UNPAID);
            $expired_status_id = getStatus(EXPIRED);
            $status_arr = [$pending_statu_id, $success_status_id, $return_status_id, $voided_status_id, $unpaid_status_id, $expired_status_id];
            $query = $query->where('transaction_details.is_ecommerce', '=', 1)->whereIn('transaction_details.status_id', $status_arr);
        }

        if (strlen($request->get('consumer')) >= 3) {
            $query = $query->whereRaw('lower(REPLACE(CONCAT(COALESCE(REPLACE(users.first_name, " ",""), "")," ",COALESCE(REPLACE(users.middle_name, " ",""), "")," ",COALESCE(REPLACE(users.last_name, " ",""), "")),"  "," ")) LIKE ?', ['%' . $request->get('consumer') . '%']);
        }

        if ($request->get('store_id')) {
            $query = $query->where("merchant_stores.id", $request->get('store_id'));
        }

        if ($is_count) {
            $totalData = $query->first()->total_count; // Getting total no of rows
            $totalFiltered = $totalData;
            return [$totalData, $totalFiltered];
        } else {
            if (!$request->has('is_export')) {
                $limit = intval($request->input('length'));
                $start = intval($request->input('start'));
                $query = $query->offset($start)->limit(intval($limit));
            }

            return $query->orderBy('transaction_details.transaction_time', 'DESC')->get();
        }
    }
    /**
     * _getAllTransactionsCommon
     * This function is used to get the all the transactions
     * @param  mixed $request
     */
    private function _getAllTransactionsCommon($request)
    {
        $declined_status = getStatus(DECLINED);
        $process_for_acheck21 = getStatus(PROCESSED_FOR_ACHECK21);
        $awaiting_consumer_approval_status = getStatus(AWAITINGCONSUMERAPPROVAL);

        $transactions = $this->_getAllTransactionsCommonQuery($request, false);
        $transactions_count = $this->_getAllTransactionsCommonQuery($request, true);
        $totalData = $transactions_count[0];
        $totalFiltered = $transactions_count[1];
        $data = array();
        if (!empty($transactions)) {
            // Creating array to show the values in frontend
            foreach ($transactions as $transaction) {
                $scheduled_posting_date = $transaction->m_scheduled_posting_date ? $transaction->m_scheduled_posting_date : $transaction->scheduled_posting_date;

                // When we access this API from Remote Pay Transaction Report page
                if ($request->get('is_remote_pay_report') == '1') {
                    $store_name = ($transaction->merchant_profile_name || $transaction->merchant_profile_name != '') ? $transaction->merchant_profile_name : $transaction->retailer;
                    $nestedData['scheduled_posting_date'] = $scheduled_posting_date ? date('m-d-Y', strtotime($scheduled_posting_date)) : '';
                    $modification_timeArray = [];
                    if ($transaction->modification_time) {
                        $modification_time = explode(",", $transaction->modification_time);
                        $modification_timeArray = array_map(function ($value) {
                            return $value ? date('m-d-Y H:i:s', strtotime($value)) : '';
                        }, $modification_time);
                    }
                    $nestedData['modification_time'] = $modification_timeArray;
                } else {
                    $store_name = $transaction->retailer;
                }

                $nestedData['transaction_number'] = $transaction->transaction_number;
                $nestedData['consumer_name'] = $transaction->first_name . ' ' . $transaction->middle_name . ' ' . $transaction->last_name;
                $nestedData['merchant_name'] = $transaction->merchant_name;
                $nestedData['store_name'] = $store_name;
                $nestedData['terminal_name'] = $transaction->terminal_name;
                $nestedData['amount'] = number_format($transaction->amount - $transaction->delivery_fee, 2);
                $nestedData['delivery_fee'] = number_format($transaction->delivery_fee, 2);
                $nestedData['updated_amount'] = number_format($transaction->updated_amount, 2);
                $nestedData['last_approve_tip_amount'] = number_format($transaction->last_approve_tip_amount, 2);
                $nestedData['transaction_time'] = date('m-d-Y H:i:s', strtotime($transaction->local_transaction_time)) . ' <br/> (' . $transaction->timezone_name . ')';

                $nestedData['status'] = $transaction->status;
                $scheduled_posting_time = $transaction->m_scheduled_posting_time ? $transaction->m_scheduled_posting_time : $transaction->scheduled_posting_time;
                // when transaction need merchant admin approval then show Pending Store's Approval message
                if (!$scheduled_posting_date && $transaction->status == PENDING_STATUS_NAME) {
                    $nestedData['status'] = PENDING_STORE_APPROVAL;
                }
                if (!$request->has('is_export')) {
                    $nestedData['cancelable'] = 0;
                    if ($transaction->consumer_approval_for_change_request != $declined_status) {
                        if ($transaction->is_ecommerce == 1) {
                            $local_transaction_time = $scheduled_posting_time;
                            $is_admin_driven = 1;
                        } else {
                            $local_transaction_time = $transaction->transaction_time;
                            $is_admin_driven = 0;
                        }
                        $nestedData['cancelable'] = checkTransactionEligibleForCancellation($local_transaction_time, $is_admin_driven, $scheduled_posting_date);
                    }
                    $nestedData['zipline_trans_id'] = $transaction->zipline_trans_id;
                    $nestedData['edit'] = $transaction->id;
                    $nestedData['is_v1'] = $transaction->is_v1;
                }
                $nestedData['attempt_count'] = $transaction->attempt_count;
                $data[] = $nestedData;
            }
        }
        if (!$request->has('is_export')) {
            // Drawing the Datatable
            $json_data = array(
                "draw" => intval($request->input('draw')),
                "recordsTotal" => intval($totalData),
                "recordsFiltered" => intval($totalFiltered),
                "data" => $data,
            );
        } else {
            $json_data = array(
                'from_date' => $request->get('from_date'),
                'to_date' => $request->get('to_date'),
                'is_remote_pay_report' => $request->get('is_remote_pay_report'),
                'search_by_scheduled_posting_date' => $request->get('search_by_scheduled_posting_date'),
                'allTransaction' => $data,
            );
        }
        return $json_data;
    }
    /**
     * getAllTransactions
     * This function is used to get the all the transactions
     * @param  mixed $request
     * @return void
     */
    public function getAllTransactions(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "All Transactions Report started.");
        // Validating input request
        $this->validate($request, [
            'from_date' => VALIDATION_REQUIRED,
            'to_date' => VALIDATION_REQUIRED,
        ]);

        $returnResponse = $this->_getAllTransactionsCommon($request);
        // Adding delivery fee to the amount
        array_walk($returnResponse, function(&$item) {
            if (is_array($item) && isset($item['amount']) && isset($item['delivery_fee'])) {
                $item['amount'] = $item['amount'] + $item['delivery_fee'];
            }
        });

        Log::info(addslashes(__METHOD__) . "(" . LINE . __LINE__ . ") : Transaction Details List fetched successfully");
        echo json_encode($returnResponse); // Rerurning the data
    }

    /**
     * getAllTransactionExport
     * This is the export function for Store API Keys
     * @param  mixed $request
     * @return void
     */
    public function getAllTransactionExport(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transactions Report Export started.");
        // Validating input request
        $this->validate($request, [
            'from_date' => VALIDATION_REQUIRED,
            'to_date' => VALIDATION_REQUIRED,
        ]);
        $request->merge(['is_export' => true]);
        $returnResponse = $this->_getAllTransactionsCommon($request);
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transactions Report Export complete.");
        return Excel::download(new AllTransactionExport($returnResponse), 'all_transaction' . date('m-d-Y H:i:s') . '.xlsx');
    }

    /**
     * modifiedtransactionhistory
     * Listing page for all the transactions modified history
     * @param  mixed $request
     * @return void
     */
    public function modifiedtransactionhistory(Request $request)
    {

        $this->validate($request, [
            'transaction_id' => VALIDATION_REQUIRED,
        ]);
        $transaction_id = $request->get('transaction_id');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "All Modified Transactions History Fetch for  transaction_id : " . $transaction_id);
        $tr_history = TransactionModificationHistory::on(MYSQL_RO)
            ->join('transaction_details', 'transaction_details.id', '=', 'transaction_modification_history.transaction_id')
            ->leftJoin('transaction_modification_reason as tmr', 'tmr.id', '=', 'transaction_modification_history.reason_id')
            ->leftJoin('status_master as sm', 'sm.id', '=', 'transaction_modification_history.status_id')
            ->where('transaction_id', $transaction_id)
            ->select('transaction_modification_history.local_transaction_time', 'sm.status', 'transaction_modification_history.amount', 'transaction_modification_history.tip_amount', 'tmr.reason', 'additional_reason', 'transaction_details.delivery_fee')
            ->groupBy('transaction_modification_history.id')
            ->orderBy('transaction_modification_history.local_transaction_time', 'DESC')->get();
        $his_data = [];
        if ($tr_history) {
            foreach ($tr_history as $tr_hi) {
                $nestedData['status'] = $tr_hi->status;
                $nestedData['reason'] = $tr_hi->reason;
                $nestedData['additional_reason'] = $tr_hi->additional_reason;
                $nestedData['amount'] = $tr_hi->amount;
                $nestedData['tip_amount'] = $tr_hi->tip_amount;
                $nestedData['delivery_fee'] = $tr_hi->delivery_fee;
                $nestedData['local_transaction_time'] = date('m-d-Y H:i:s', strtotime($tr_hi->local_transaction_time));
                $his_data[] = $nestedData;
            }
        }
        if (count($his_data) > 0) {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "All Modified Transactions History Fetched Successfully for  transaction_id : " . $transaction_id);
            $message = trans('message.fetch_mofied_transaction_history');
            // API Response returned with 200 status
            return renderResponse(SUCCESS, $message, $his_data);
        } else {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No Modified Transactions History found for transaction_id : " . $transaction_id);
            $message = trans('message.fetch_mofied_transaction_history_not_found');
            return renderResponse(FAIL, $message, null);
        }
    }
    public function getAllPendingTransactions()
    {
        $last_processed_to_bank = DB::select(DB::raw("SELECT ptd.local_transaction_date AS last_bank_posting_date FROM transaction_details AS ptd LEFT JOIN transaction_details AS ctd ON ctd.transaction_ref_no = ptd.id INNER JOIN status_master AS sm ON ctd.status_id = sm.id WHERE ptd.transaction_ref_no IS NULL AND sm.code = " . PROCESSED_TO_BANK . " ORDER BY ptd.created_at DESC LIMIT 1"));

        $transactions = [];
        if (!empty($last_processed_to_bank)) {
            $sql = "SELECT DISTINCT td.local_transaction_date AS transaction_date, DATE_ADD(td.local_transaction_date, INTERVAL 1 DAY) AS expected_posting_date, date(td1.local_transaction_date) AS actual_posting_date, td1.id is null processed_to_bank, tdb.id IS null marchant_scheduler_required
            from transaction_details td
            left join transaction_details td1 on td.id = td1.transaction_ref_no and td1.status_id=?
            left join  transaction_details tdb on tdb.isCanpay = 1 and tdb.local_transaction_time between date_add(td.local_transaction_date,interval 1 DAY) and date_add(td.local_transaction_date,interval 1 DAY)
            where td.local_transaction_date > ? and td.status_id = ?
            ORDER BY td.local_transaction_date";

            $transactions = DB::select($sql, [getStatus(PROCESSED_TO_BANK), $last_processed_to_bank[0]->last_bank_posting_date, getStatus(PENDING)]);
        }

        $message = trans('message.transaction_fetch_success');
        return renderResponse(SUCCESS, $message, $transactions); // Returning response
    }

    /**
     * updateTransactionStatus
     * This function will update all the transactions status of the previous day to Sent to bank once ACHECK21 calls this API.
     * @return void
     */
    public function updateTransactionStatus(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction status updation process started");
        $transaction_date = $request->get('transaction_date');

        $user_details = Auth::user();
        // Get all transactions for the specific transaction date
        $all_transactions = TransactionDetails::join('status_master', 'status_master.id', '=', 'transaction_details.status_id')->select('transaction_details.*')->where('transaction_details.transaction_ref_no', '!=', null)->where('transaction_details.consumer_id', '>', '')->where('status_master.code', PROCESSED_FOR_ACHECK21)->whereRaw("transaction_details.local_transaction_date = '" . date('Y-m-d', strtotime($transaction_date)) . "' AND transaction_details.transaction_ref_no NOT IN (SELECT COALESCE(transaction_details.transaction_ref_no,0) FROM transaction_details inner join `status_master` on `transaction_details`.`status_id` = `status_master`.`id` WHERE `status_master`.`code` IN (" . PROCESSED_TO_BANK . ", " . RETURNED . "))")->orderBy('transaction_details.local_transaction_time', 'DESC')->get();

        DB::beginTransaction();
        try {
            if (!$all_transactions->isEmpty()) {
                foreach ($all_transactions as $transaction) {
                    // Creating a new transaction record with transaction reference no as parent transaction id
                    $transaction_details = new TransactionDetails();
                    $transaction_details->transaction_number = generateTransactionId();
                    $transaction_details->transaction_ref_no = $transaction->transaction_ref_no;
                    $transaction_details->merchant_id = $transaction->merchant_id;
                    $transaction_details->consumer_id = $transaction->consumer_id;
                    $transaction_details->terminal_id = $transaction->terminal_id;
                    $transaction_details->transaction_time = Carbon::now();
                    $transaction_details->local_transaction_time = Carbon::now($transaction->timezone_name);
                    $transaction_details->local_transaction_year = date("Y", strtotime($transaction_details->local_transaction_time));
                    $transaction_details->local_transaction_month = date("m", strtotime($transaction_details->local_transaction_time));
                    $transaction_details->local_transaction_date = date("Y-m-d", strtotime($transaction_details->local_transaction_time));
                    $transaction_details->timezone_id = $transaction->timezone_id;
                    $transaction_details->amount = $transaction->amount;
                    $transaction_details->tip_amount = $transaction->tip_amount;
                    $transaction_details->status_id = getStatus(PROCESSED_TO_BANK);
                    $transaction_details->transaction_place = ACHECK21;
                    $transaction_details->threshold_value_for_transaction_failure = $transaction->threshold_value_for_transaction_failure;
                    $transaction_details->acheck_document_id = $transaction->acheck_document_id;
                    $transaction_details->acheck_document_name = 'From Admin by ' . $user_details->user_id;
                    $transaction_details->is_v1 = $transaction->is_v1;
                    $transaction_details->isCanpay = $transaction->isCanpay;
                    $transaction_details->save();
                    DB::commit();
                    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction details was stored successfully with status Processed to Bank for Transaction ID: " . $transaction->id);
                }
                $message = trans('message.transaction_status_update_success');
            } else {
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No Processed for Acheck21 transaction found for Transaction date: " . $transaction_date);
                $message = trans('message.no_transaction_found');
            }
            return renderResponse(SUCCESS, $message, null);
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured during Transaction", [EXCEPTION => $e]);
            DB::rollback();
            $message = trans('message.transaction_error');
            return renderResponse(FAIL, $message, null); // Exception Returned
        }
    }

    public function runMerchantScheduler(Request $request)
    {
        // Check wheather new ach process is enabled or disabled
        $checkNewAchEnabled = getSettingsValue('enable_new_ach_process_for_all_merchant', 0);
        if ($checkNewAchEnabled == 1) {
            Log::channel('transaction-scheduler')->info(addslashes(__METHOD__) . "(" . LINE . ": " . __LINE__ . ") - " . "New ACH process is enabled.");
            $message = trans('message.new_ach_process_is_enabled');
            return renderResponse(FAIL, $message, null); // Exception Returned
        }
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Merchant scheduler started running");
        $transaction_date = $request->get('transaction_date');

        // Check if the date's transaction already poseted in ACHECK for Merchants and Canpay
        $posting_date = Carbon::parse($transaction_date)->addDays(1)->toDateString();
        $checkDuplicateSchedulerCall = TransactionDetails::whereRaw("local_transaction_date = ?", [$posting_date])->where('isCanpay', 1)->count();
        if ($checkDuplicateSchedulerCall == 0) { // If Canpay posting row not available for that day then proceed with the scheduler
            // Update Previous day's CanPay, Merchant Transaction status to Success
            $this->_updateMerchantCanpayTransactions(date('Y-m-d', strtotime($posting_date)));
            // Check if there is any V2 transaction on that day
            $checkTransactionExists = TransactionDetails::join('status_master', 'status_master.id', '=', 'transaction_details.status_id')->where('transaction_details.is_v1', 0)->whereRaw("transaction_details.local_transaction_date = '" . $transaction_date . "'")->where('status_master.code', PENDING)->count();
            if ($checkTransactionExists > 0) { // If Transaction Exists then proceed for next Step
                // Get last date of transaction before the date we got from parameter as we need to check if the webhook processed for that day
                $getLastTransactiondate = TransactionDetails::join('status_master', 'status_master.id', '=', 'transaction_details.status_id')->where('transaction_details.is_v1', 0)->whereRaw("transaction_details.local_transaction_date < '" . $transaction_date . "'")->where('status_master.code', PENDING)->orderBy('transaction_details.local_transaction_time', 'DESC')->first();

                // Check if any transaction got updated with Processed to Bank status
                $checkForAcheckWebhook = TransactionDetails::join('status_master', 'status_master.id', '=', 'transaction_details.status_id')->where('transaction_details.transaction_ref_no', '!=', null)->whereRaw("`status_master`.`code` = " . PROCESSED_TO_BANK . " AND `transaction_details`.`transaction_ref_no` IN (SELECT transaction_details.id FROM transaction_details inner join `status_master` on `status_master`.`id` = `transaction_details`.`status_id`
                WHERE transaction_details.local_transaction_date = '" . date('Y-m-d', strtotime($getLastTransactiondate->local_transaction_time)) . "' AND status_master.code = " . PENDING . ")")->count();
                if ($checkForAcheckWebhook > 0 || empty($lastDateOfCanpayPosting)) { // If previous day's transaction got updated or if this is the first time the scheduler is running then proceed with the process
                    Log::channel('transaction-scheduler')->info(addslashes(__METHOD__) . "(" . LINE . __LINE__ . ") - " . "Merchant transaction scheduler started running at " . Carbon::now() . " UTC for Transaction date " . $transaction_date);
                    $this->_createMerchantCreditTransaction($transaction_date);
                    Log::channel('transaction-scheduler')->info(addslashes(__METHOD__) . "(" . LINE . __LINE__ . ") - " . "Merchant credit transaction finished at " . Carbon::now() . " UTC for Transaction date " . $transaction_date);
                    $this->_createMerchantDebitTransaction($transaction_date);
                    Log::channel('transaction-scheduler')->info(addslashes(__METHOD__) . "(" . LINE . __LINE__ . ") - " . "Merchant debit transaction finished at " . Carbon::now() . " UTC for Transaction date " . $transaction_date);
                    $this->_createCanPayTransaction($transaction_date);
                    Log::channel('transaction-scheduler')->info(addslashes(__METHOD__) . "(" . LINE . __LINE__ . ") - " . "CanPay credit transaction finished at " . Carbon::now() . " UTC for Transaction date " . $transaction_date);
                    Log::channel('transaction-scheduler')->info(addslashes(__METHOD__) . "(" . LINE . __LINE__ . ") - " . "Merchant transaction scheduler finished running at " . Carbon::now() . " UTC for Transaction date " . $transaction_date);
                    $message = trans('message.transaction_post_success');
                    return renderResponse(SUCCESS, $message, null); // Exception Returned
                } else {
                    Log::channel('transaction-scheduler')->info(addslashes(__METHOD__) . "(" . LINE . __LINE__ . ") - " . "Merchant transaction scheduler skipped running at " . Carbon::now() . " UTC as V2 transaction didn't get updated on " . $transaction_date);
                    $message = trans('message.transaction_not_updated');
                    return renderResponse(FAIL, $message, null); // Exception Returned
                }
            } else { // If Transaction deosn't exists then Skip the scheduler
                Log::channel('transaction-scheduler')->info(addslashes(__METHOD__) . "(" . LINE . __LINE__ . ") - " . "Merchant transaction scheduler skipped running at " . Carbon::now() . " UTC as there was no V2 transaction found on " . $transaction_date);
                $message = trans('message.no_transaction_found');
                return renderResponse(FAIL, $message, null); // Exception Returned
            }
        } else { // If Canpay posting row is available for that day then don't run the scheduler
            Log::channel('transaction-scheduler')->info(addslashes(__METHOD__) . "(" . LINE . __LINE__ . ") - " . "Merchant transaction scheduler skipped running at " . Carbon::now() . " UTC as the data of " . $transaction_date . " already posted for Merchants and Canpay");
            $message = trans('message.transaction_already_posted');
            return renderResponse(FAIL, $message, null); // Exception Returned
        }
    }

    /**
     * _updateMerchantCnapyTransactions
     * This function will update the previous day's Merchant and Canpay transaction status to Success
     * @param  mixed $date
     * @return void
     */
    private function _updateMerchantCanpayTransactions($date)
    {
        $getTransactions = TransactionDetails::leftJoin('timezone_masters', 'timezone_masters.id', '=', 'transaction_details.timezone_id')->select('transaction_details.*', 'timezone_masters.timezone_name')->whereRaw("local_transaction_date = ?", [Carbon::parse($date)->subDays(1)->toDateString()])->whereRaw('(`transaction_details`.`merchant_id` is not NULL or `transaction_details`.`isCanpay` = 1) AND transaction_details.id NOT IN (SELECT COALESCE(transaction_details.transaction_ref_no,0) FROM transaction_details inner join status_master on transaction_details.status_id = status_master.id WHERE status_master.code = ' . SUCCESS . ')')->get();
        foreach ($getTransactions as $transaction) {
            $transaction_details = new TransactionDetails();
            $transaction_details->transaction_number = generateTransactionId();
            $transaction_details->transaction_ref_no = $transaction->id;
            $transaction_details->merchant_id = $transaction->merchant_id;
            $transaction_details->transaction_time = Carbon::now();
            $transaction_details->local_transaction_time = Carbon::now($transaction->timezone_name);
            $transaction_details->local_transaction_year = date("Y", strtotime($transaction_details->local_transaction_time));
            $transaction_details->local_transaction_month = date("m", strtotime($transaction_details->local_transaction_time));
            $transaction_details->local_transaction_date = date("Y-m-d", strtotime($transaction_details->local_transaction_time));
            $transaction_details->timezone_id = $transaction->timezone_id;
            $transaction_details->amount = $transaction->amount;
            $transaction_details->entry_type = $transaction->entry_type;
            $transaction_details->status_id = getStatus(SUCCESS);
            $transaction_details->transaction_place = ACHECK21;
            $transaction_details->isCanpay = $transaction->isCanpay;
            $transaction_details->save();
        }
        $message = trans('message.merchant_canpay_transaction_success');
        return renderResponse(SUCCESS, $message, null);
    }

    /**
     * Create merchant end transaction with all the accumulated amount
     */
    private function _createMerchantCreditTransaction($transaction_date)
    {
        $transactions = MerchantStores::join('terminal_master', 'terminal_master.merchant_store_id', '=', 'merchant_stores.id')
            ->join('transaction_details', 'terminal_master.id', '=', 'transaction_details.terminal_id')
            ->join('registered_merchant_master', 'registered_merchant_master.id', '=', 'merchant_stores.merchant_id')
            ->join('user_bank_account_info', 'user_bank_account_info.merchant_id', '=', 'merchant_stores.merchant_id')
            ->leftJoin('timezone_masters', 'timezone_masters.id', '=', 'transaction_details.timezone_id')
            ->select(DB::raw('sum(transaction_details.amount) as sum'), DB::raw('sum(transaction_details.tip_amount) as tip_sum'), 'user_bank_account_info.*', 'registered_merchant_master.merchant_name', 'timezone_masters.timezone_name')
            ->where('transaction_details.status_id', getStatus(PENDING))
            ->where('registered_merchant_master.is_enabled_new_ach_process', 0)
            ->whereRaw("transaction_details.local_transaction_date = ?", [$transaction_date])
            ->whereRaw('transaction_details.transaction_ref_no is null AND transaction_details.merchant_id is null and transaction_details.consumer_id is not null AND transaction_details.id NOT IN (SELECT COALESCE(transaction_details.transaction_ref_no,0) FROM transaction_details inner join `status_master` on `transaction_details`.`status_id` = `status_master`.`id` WHERE `status_master`.`code` IN (' . PROCESSED_TO_BANK . ', ' . RETURNED . '))')
            ->groupBy('merchant_stores.merchant_id')
            ->get();
        DB::beginTransaction();
        try {
            foreach ($transactions as $transaction) {
                //create the transaction through acheck21
                Log::channel('transaction-scheduler')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Posting merchant credit transaction to acheck21 for merchant id: " . $transaction->merchant_id);
                $total_amount = ($transaction->sum + $transaction->tip_sum);
                $params['amount'] = -$total_amount;
                $params['merchant_id'] = $transaction->merchant_id;
                $params['acheck_account_id'] = config('app.canpay_settlement_id');
                //calling the factory function to create merchant credit transaction into acheck21
                $response = $this->transaction->createMerchantDepositTransaction($params);
                $response_decoded = json_decode($response, true);
                //create a new transaction
                $transaction_details = new TransactionDetails();
                $transaction_details->transaction_number = generateTransactionId();
                $transaction_details->transaction_ref_no = $transaction->id;
                $transaction_details->merchant_id = $transaction->merchant_id;
                if (Carbon::parse($transaction_date)->addDays(1)->lt(Carbon::now())) {
                    $transaction_details->transaction_time = Carbon::parse($transaction_date)->addDays(1);
                    $transaction_details->local_transaction_time = Carbon::parse($transaction_date)->addDays(1);
                } else {
                    $transaction_details->transaction_time = Carbon::now();
                    $transaction_details->local_transaction_time = Carbon::now($transaction->timezone_name);
                }
                $transaction_details->local_transaction_year = date("Y", strtotime($transaction_details->local_transaction_time));
                $transaction_details->local_transaction_month = date("m", strtotime($transaction_details->local_transaction_time));
                $transaction_details->local_transaction_date = date("Y-m-d", strtotime($transaction_details->local_transaction_time));
                $transaction_details->timezone_id = $transaction->timezone_id;
                $transaction_details->amount = $total_amount;
                $transaction_details->entry_type = 'Cr';
                $transaction_details->status_id = getStatus(PROCESSED_FOR_ACHECK21);
                $transaction_details->transaction_place = ACHECK21;
                $transaction_details->acheck_document_id = $response_decoded['documentId'];
                $transaction_details->save();
                DB::commit();
                Log::channel('transaction-scheduler')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction details was stored successfully for merchant id: " . $transaction->merchant_id);
                //store transaction details into transaction table
            }
            $message = trans('message.transaction_success');
            return renderResponse(SUCCESS, $message, null);
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured during Transaction", [EXCEPTION => $e]);
            DB::rollback();
            $message = trans('message.transaction_error');
            return renderResponse(FAIL, $message, null); // Exception Returned
        }
    }

    /**
     * Create merchant end Debit transaction with all the commission amount
     */
    private function _createMerchantDebitTransaction($transaction_date)
    {
        $transactions = MerchantStores::join('terminal_master', 'terminal_master.merchant_store_id', '=', 'merchant_stores.id')
            ->join('transaction_details', 'terminal_master.id', '=', 'transaction_details.terminal_id')
            ->join('registered_merchant_master', 'registered_merchant_master.id', '=', 'merchant_stores.merchant_id')
            ->join('user_bank_account_info', 'user_bank_account_info.merchant_id', '=', 'merchant_stores.merchant_id')
            ->leftJoin('timezone_masters', 'timezone_masters.id', '=', 'transaction_details.timezone_id')
            ->select(DB::raw('count(*) as count'), DB::raw('SUM(transaction_details.amount) as sum'), DB::raw('IFNULL(SUM(transaction_details.tip_amount),0) as tip_sum'), 'user_bank_account_info.*', 'registered_merchant_master.merchant_name', 'timezone_masters.timezone_name')
            ->whereRaw('transaction_details.transaction_ref_no is null and transaction_details.merchant_id is null and transaction_details.consumer_id is not null')
            ->where('transaction_details.status_id', getStatus(PENDING))
            ->where('registered_merchant_master.is_enabled_new_ach_process', 0)
            ->whereRaw("DATE(transaction_details.local_transaction_time) = ?", [$transaction_date])
            ->whereRaw('transaction_details.transaction_ref_no is null AND transaction_details.merchant_id is null and transaction_details.consumer_id is not null AND transaction_details.id NOT IN (SELECT COALESCE(transaction_details.transaction_ref_no,0) FROM transaction_details inner join `status_master` on `transaction_details`.`status_id` = `status_master`.`id` WHERE `status_master`.`code` IN (' . PROCESSED_TO_BANK . ', ' . RETURNED . '))')
            ->groupBy('merchant_stores.merchant_id')
            ->get();
        $fee = FeesMaster::Where('fee_type', FEE_TYPE_RETAIL)->first();

        try {
            foreach ($transactions as $transaction) {
                //create the transaction through acheck21
                Log::channel('transaction-scheduler')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Posting merchant debit transaction to acheck21 for merchant id: " . $transaction->merchant_id);
                //calculate the commission amount that needs to be debited from the merchant account
                //volumn calculation
                $total_amount = ($transaction->sum + $transaction->tip_sum);
                if ($fee->volume_value_type == PERCENTAGE) {
                    $fee_percentage = ($fee->volume_value / 100);
                    $volume_amount = $total_amount * $fee_percentage;
                } else {
                    $volume_amount = $total_amount * $fee->volume_value;
                }
                $volume_amount = round($volume_amount, 2);
                //per transaction calculation
                if ($fee->per_transaction_value_type == PERCENTAGE) {
                    $fee_percentage = ($fee->per_transaction_value / 100);
                    $per_transaction_amount = $transaction->count * $fee_percentage;
                } else {
                    $per_transaction_amount = $transaction->count * $fee->per_transaction_value;
                }
                $per_transaction_amount = round($per_transaction_amount, 2);
                $params['amount'] = $per_transaction_amount + $volume_amount;
                $params['merchant_id'] = $transaction->merchant_id;
                $params['acheck_account_id'] = config('app.canpay_fees_id');
                //calling the factory function to create merchant debit transaction into acheck21
                $response = $this->transaction->createMerchantFeeTransaction($params);
                $response_decoded = json_decode($response, true);
                //create a new transaction
                $transaction_details = new TransactionDetails();
                $transaction_details->transaction_number = generateTransactionId();
                $transaction_details->transaction_ref_no = $transaction->id;
                $transaction_details->merchant_id = $transaction->merchant_id;
                if (Carbon::parse($transaction_date)->addDays(1)->lt(Carbon::now())) {
                    $transaction_details->transaction_time = Carbon::parse($transaction_date)->addDays(1);
                    $transaction_details->local_transaction_time = Carbon::parse($transaction_date)->addDays(1);
                } else {
                    $transaction_details->transaction_time = Carbon::now();
                    $transaction_details->local_transaction_time = Carbon::now($transaction->timezone_name);
                }
                $transaction_details->local_transaction_year = date("Y", strtotime($transaction_details->local_transaction_time));
                $transaction_details->local_transaction_month = date("m", strtotime($transaction_details->local_transaction_time));
                $transaction_details->local_transaction_date = date("Y-m-d", strtotime($transaction_details->local_transaction_time));
                $transaction_details->timezone_id = $transaction->timezone_id;
                $transaction_details->amount = $params['amount'];
                $transaction_details->status_id = getStatus(PROCESSED_FOR_ACHECK21);
                $transaction_details->transaction_place = ACHECK21;
                $transaction_details->acheck_document_id = $response_decoded['documentId'];
                $transaction_details->save();
                DB::commit();
                Log::channel('transaction-scheduler')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction details was stored successfully for merchant id: " . $transaction->merchant_id);
                //store transaction details into transaction table
            }
            $message = trans('message.transaction_success');
            return renderResponse(SUCCESS, $message, null);
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured during Transaction", [EXCEPTION => $e]);
            DB::rollback();
            $message = trans('message.transaction_error');
            return renderResponse(FAIL, $message, null); // Exception Returned
        }
    }
    /**
     * Creates CanPay end transactions along with the accumulated commission amount
     */
    private function _createCanPayTransaction($transaction_date)
    {
        $transactions = MerchantStores::join('terminal_master', 'terminal_master.merchant_store_id', '=', 'merchant_stores.id')
            ->join('transaction_details', 'terminal_master.id', '=', 'transaction_details.terminal_id')
            ->join('registered_merchant_master', 'registered_merchant_master.id', '=', 'merchant_stores.merchant_id')
            ->join('user_bank_account_info', 'user_bank_account_info.merchant_id', '=', 'merchant_stores.merchant_id')
            ->leftJoin('timezone_masters', 'timezone_masters.id', '=', 'transaction_details.timezone_id')
            ->select(DB::raw('count(*) as count'), DB::raw('sum(transaction_details.amount) as sum'), DB::raw('sum(transaction_details.tip_amount) as tip_sum'), 'timezone_masters.timezone_name')
            ->whereRaw('transaction_details.transaction_ref_no is null and transaction_details.merchant_id is null and transaction_details.consumer_id is not null')
            ->where('transaction_details.status_id', getStatus(PENDING))
            ->where('registered_merchant_master.is_enabled_new_ach_process', 0)
            ->whereRaw("local_transaction_date = ?", [$transaction_date])
            ->whereRaw('transaction_details.transaction_ref_no is null AND transaction_details.merchant_id is null and transaction_details.consumer_id is not null AND transaction_details.id NOT IN (SELECT COALESCE(transaction_details.transaction_ref_no,0) FROM transaction_details inner join `status_master` on `transaction_details`.`status_id` = `status_master`.`id` WHERE `status_master`.`code` IN (' . PROCESSED_TO_BANK . ', ' . RETURNED . '))')
            ->groupBy('merchant_stores.merchant_id')
            ->get();
        $fee = FeesMaster::Where('fee_type', FEE_TYPE_RETAIL)->first();
        $total_fee = 0;
        DB::beginTransaction();
        try {
            foreach ($transactions as $transaction) {
                $total_amount = ($transaction->sum + $transaction->tip_sum);
                //calculate the commission amount that needs to be debited from the merchant account
                //volumn calculation
                if ($fee->volume_value_type == PERCENTAGE) {
                    $fee_percentage = ($fee->volume_value / 100);
                    $volume_amount = $total_amount * $fee_percentage;
                } else {
                    $volume_amount = $total_amount * $fee->volume_value;
                }
                $volume_amount = round($volume_amount, 2);
                //per transaction calculation
                if ($fee->per_transaction_value_type == PERCENTAGE) {
                    $fee_percentage = ($fee->per_transaction_value / 100);
                    $per_transaction_amount = $transaction->count * $fee_percentage;
                } else {
                    $per_transaction_amount = $transaction->count * $fee->per_transaction_value;
                }
                $per_transaction_amount = round($per_transaction_amount, 2);
                $total_fee = $total_fee + $per_transaction_amount + $volume_amount;
            }
            //create the transaction through acheck21
            Log::channel('transaction-scheduler')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Posting merchant transaction to acheck21 for CanPay Commission");
            $params['amount'] = -round_up($total_fee, 2);
            $params['acheck_account_id'] = config('app.canpay_deposit');
            //calling the factory function to create canpay credit transaction into acheck21
            $response = $this->transaction->createCanPayTransaction($params);
            $response_decoded = json_decode($response, true);
            //create a new transaction
            $transaction_details = new TransactionDetails();
            $transaction_details->transaction_number = generateTransactionId();
            if (Carbon::parse($transaction_date)->addDays(1)->lt(Carbon::now())) {
                $transaction_details->transaction_time = Carbon::parse($transaction_date)->addDays(1);
                $transaction_details->local_transaction_time = Carbon::parse($transaction_date)->addDays(1);
            } else {
                $transaction_details->transaction_time = Carbon::now();
                $transaction_details->local_transaction_time = Carbon::now($transaction->timezone_name);
            }
            $transaction_details->local_transaction_year = date("Y", strtotime($transaction_details->local_transaction_time));
            $transaction_details->local_transaction_month = date("m", strtotime($transaction_details->local_transaction_time));
            $transaction_details->local_transaction_date = date("Y-m-d", strtotime($transaction_details->local_transaction_time));
            $transaction_details->timezone_id = $transaction->timezone_id;
            $transaction_details->amount = $total_fee;
            $transaction_details->entry_type = 'Cr';
            $transaction_details->status_id = getStatus(PROCESSED_FOR_ACHECK21);
            $transaction_details->transaction_place = ACHECK21;
            $transaction_details->acheck_document_id = $response_decoded['documentId'];
            $transaction_details->isCanpay = 1;
            $transaction_details->save();
            DB::commit();
            Log::channel('transaction-scheduler')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction details was stored successfully for CanPay Commission");
            $message = trans('message.transaction_success');
            return renderResponse(SUCCESS, $message, null);
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured during Transaction", [EXCEPTION => $e]);
            DB::rollback();
            $message = trans('message.transaction_error');
            return renderResponse(FAIL, $message, null); // Exception Returned
        }
    }

    /**
     * generateTransactionReport
     * This function generate the Transaction report in the Admin panel
     * @param  mixed $request
     * @return void
     */
    public function generateTransactionReport(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction report generation Process started.");

        // Get the Status ID
        $pending = getStatus(PENDING);
        $success = getStatus(SUCCESS);
        $process_for_acheck21 = getStatus(PROCESSED_FOR_ACHECK21);

        $report_date_merchant = Carbon::now()->toDateString();
        $report_date = Carbon::parse($report_date_merchant)->subDays(1)->toDateString();
        $last_time_of_canpay_posting = TransactionDetails::where('isCanpay', 1)->orderBy('created_at', 'DESC')->first();
        $return_represent_start_time = Carbon::parse($last_time_of_canpay_posting->created_at)->subDays(1);
        $return_represent_end_time = $last_time_of_canpay_posting->created_at;
        $canpay_offset_post_date = date("Y-m-d", strtotime($last_time_of_canpay_posting->created_at)) . ' 07:00:00';
        $date = Carbon::createFromFormat('Y-m-d H:i:s', $canpay_offset_post_date, 'America/New_York');
        $represented_date = date('Y-m-d', strtotime(Carbon::parse($last_time_of_canpay_posting->created_at)->subDays(1)));

        $transaction_report = "SELECT CONCAT('Total Consumer Transaction (',COUNT(*),')') AS head,0.00 AS credit,COALESCE(SUM(td.consumer_bank_posting_amount),0) AS debit,'' AS rowClass  FROM transaction_details td STRAIGHT_JOIN transaction_details AS td1 ON td.id = td1.transaction_ref_no WHERE td.scheduled_posting_date = ? AND td.transaction_ref_no IS NULL AND td.consumer_id!='' AND td.status_id = ? and td.isCanpay = 0 AND td1.status_id = ?
        UNION
        SELECT 'Total Reward Amount Used' AS head,0.00 AS credit, COALESCE(SUM(amount),0) AS debit,'' AS rowClass FROM transaction_details WHERE reward_acheck_posting = 1 AND scheduled_posting_date = ?
        UNION
        SELECT 'Total Merchant Transaction' AS head,COALESCE(SUM(amount),0) AS credit,0.00 AS debit,'' AS rowClass  FROM transaction_details WHERE entry_type = '" . CREDIT . "' AND merchant_id IS NOT NULL AND scheduled_posting_date = ?
        UNION
        SELECT 'CanPay Fee (Merchant Debit)' AS head,0.00 AS credit, COALESCE(SUM(amount),0) AS debit,'' AS rowClass FROM transaction_details WHERE entry_type = '" . DEBIT . "' AND merchant_id IS NOT NULL AND scheduled_posting_date = ? AND is_cashback = 0
        UNION
        SELECT 'CanPay Fee' AS head, COALESCE(SUM(amount),0) AS credit,0.00 AS debit,'' AS rowClass FROM transaction_details WHERE isCanpay = 1 AND scheduled_posting_date = ?
        UNION
        SELECT 'Merchant Points Program Debit' AS head, 0.00 AS credit, COALESCE(SUM(amount),0) AS debit,'' AS rowClass FROM transaction_details WHERE merchant_id IS NOT NULL AND entry_type = '" . DEBIT . "' AND is_cashback = 1 AND scheduled_posting_date = ?
        UNION
        SELECT 'CanPay Points Program Credit' AS head, COALESCE(SUM(amount),0) AS credit, 0.00 AS debit,'' AS rowClass FROM transaction_details WHERE entry_type = '" . CREDIT . "' AND is_cashback = 1 AND scheduled_posting_date = ? AND merchant_id IS NULL";

        $transaction_report_array = DB::connection(MYSQL_RO)->select($transaction_report, [$report_date, $pending, $process_for_acheck21, $report_date, $report_date, $report_date, $report_date, $report_date, $report_date]);

        $total_represented = "SELECT COALESCE(SUM(amount+tip_amount),0) AS represented_amount
        FROM transaction_details AS td
        LEFT JOIN return_manual_post_logs rmpl ON td.transaction_ref_no = rmpl.primary_identifier_value AND rmpl.return_posted = '1'
        WHERE entry_type = '" . DEBIT . "' AND is_represented = 1 AND transaction_ref_no IS NOT NULL AND td.status_id = ?
        AND td.created_at >= ? AND td.created_at <= ? AND rmpl.id IS null";
        $total_represented_array = DB::connection(MYSQL_RO)->select($total_represented, [$process_for_acheck21, $return_represent_start_time, $return_represent_end_time]);

        $return_transaction_amounts = "SELECT COALESCE(SUM(if((td1.return_from_primary_account = 1 AND td1.transaction_returned != td1.transaction_represented) OR (td1.represent_count >= 3), td.amount+td.tip_amount, 0)),0) AS new_transaction
        FROM transaction_details AS td
        INNER JOIN transaction_details AS td1 ON td.transaction_ref_no = td1.id
        INNER JOIN return_reason_masters rrm ON td1.return_reason = rrm.id
        INNER JOIN users AS us ON td.consumer_id = us.user_id
        WHERE td.entry_type = '" . DEBIT . "' AND td.is_represented = 1 AND td.transaction_ref_no IS NOT NULL AND td.status_id = ? AND td.local_transaction_date = ? ";
        $return_transaction_amounts_array = DB::connection(MYSQL_RO)->select($return_transaction_amounts, [$process_for_acheck21, $represented_date]);

        $canpay_offset = "SELECT COALESCE(SUM(amount),0) AS amount FROM transaction_details  WHERE entry_type = '" . CREDIT . "' AND merchant_id IS NULL AND created_at >= ?  AND created_at <= ? and is_represented = 1";
        $canpay_offset_array = DB::connection(MYSQL_RO)->select($canpay_offset, [$date->setTimezone('UTC'), Carbon::parse($date->setTimezone('UTC'))->addHour()]);

        $report = [];
        $total_debit = 0;
        $total_credit = 0;
        foreach ($transaction_report_array as $val_arr) {
            $reportArr = [];
            $total_debit += $val_arr->debit;
            $total_credit += $val_arr->credit;
            if (trim($val_arr->head) != '') {
                $reportArr['head'] = $val_arr->head;
                $reportArr['credit'] = "$" . $val_arr->credit;
                $reportArr['debit'] = "$" . $val_arr->debit;
                $reportArr['rowClass'] = $val_arr->rowClass;
                array_push($report, $reportArr);
            } else {
                break;
            }
        }

        //Add class for the summation row
        if (number_format($total_debit, 2) == number_format($total_credit, 2)) {
            $rowClass = 'text-green';
        } else {
            $rowClass = 'text-red';
        }

        //Shift the sum array to the top
        $total_arr = [
            'head' => 'Transaction date: ' . date('m-d-Y', strtotime($report_date)),
            'debit' => "$" . number_format($total_debit, 2),
            'credit' => "$" . number_format($total_credit, 2),
            'rowClass' => $rowClass,
        ];
        array_unshift($report, $total_arr);

        $returnResponse = [
            'report' => $report,
            'total_represented' => $total_represented_array[0]->represented_amount,
            'new_transaction_amount' => $return_transaction_amounts_array[0]->new_transaction,
            'represented_transaction_amount' => $total_represented_array[0]->represented_amount - $return_transaction_amounts_array[0]->new_transaction,
            'canpay_offset' => $canpay_offset_array[0]->amount,
        ];

        $message = trans('message.transaction_report_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction report generated : ", (array) $returnResponse);
        return renderResponse(SUCCESS, $message, $returnResponse); // Returning response
    }
    /**
     * getTransactionDetails
     * This function is to get the Transaction Details in the Dashboard
     * @param  mixed $request
     * @return void
     */
    public function getTransactionDetails(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Dashboard Info Box population started.");
        // Validating input request
        $this->validate($request, [
            'searchDate' => VALIDATION_REQUIRED,
        ]);
        $pending = getStatus(PENDING);
        $success = getStatus(SUCCESS);
        $returned = getStatus(RETURNED);

        $searchDate = $request->get('searchDate');
        //last Seven days transaction
        $startDate = date('Y-m-d', strtotime('-1 day', strtotime($request->get('searchDate'))));
        $endDate = date('Y-m-d', strtotime('-7 day', strtotime($request->get('searchDate'))));
        $searchDateArr = explode("-", $request->get('searchDate'));
        // Create a Carbon instance from the given date
        $carbonDate = Carbon::parse($request->get('searchDate'));
        // Get the first day of the month
        $firstDateOfMonth = $carbonDate->firstOfMonth()->toDateString();
        // Get the last day of the month
        $lastDateOfMonth = $carbonDate->endOfMonth()->toDateString();

        //Transaction details
        $transactionDetails = "SELECT
        COALESCE(SUM(if(is_v1 = 1, amount+tip_amount, 0)),0) as total_monthly_sales_v1,
        COALESCE(SUM(if(is_v1 = 0, amount+tip_amount, 0)),0) as total_monthly_sales_v2,
        COALESCE(SUM(if(is_v1 = 1, 1, 0)),0) as total_monthly_transactions_v1,
        COALESCE(SUM(if(is_v1 = 0, 1, 0)),0) as total_monthly_transactions_v2,
        COALESCE(SUM(if(is_v1 = 1 AND scheduled_posting_date = ?, amount+tip_amount, 0)),0) as total_sales_v1,
        COALESCE(SUM(if(is_v1 = 0 AND scheduled_posting_date = ?, amount+tip_amount, 0)),0) as total_sales_v2,
        COALESCE(SUM(if(is_v1 = 1 AND scheduled_posting_date = ?, 1, 0)),0) as total_transactions_v1,
        COALESCE(SUM(if(is_v1 = 0 AND scheduled_posting_date = ?, 1, 0)),0) as total_transactions_v2
        FROM transaction_details FORCE INDEX(idx_scheduled_posting_date)
        WHERE status_id in ('" . $pending . "', '" . $success . "', '" . $returned . "') AND scheduled_posting_date BETWEEN ? AND  ? AND transaction_ref_no IS NULL  AND isCanpay = 0 AND merchant_id IS NULL";
        $totalTransactions = DB::connection(MYSQL_RO)->select($transactionDetails, [$searchDate, $searchDate, $searchDate, $searchDate, $firstDateOfMonth, $lastDateOfMonth]);

        //Monthly Admin driven not posted Transaction details
        $adminDrivenNotPostedTransactionDetails = "SELECT COALESCE(SUM(amount+tip_amount),0) as total_monthly_admin_driven_not_posted_amount,
        COALESCE(SUM(if(local_transaction_date = ?, amount+tip_amount, 0)),0) as total_daily_admin_driven_not_posted_amount
        FROM transaction_details FORCE INDEX(composite_year_month)
        WHERE status_id = '" . $pending . "' and local_transaction_month = ? AND local_transaction_year = ? AND scheduled_posting_date IS NULL AND transaction_ref_no IS NULL  AND isCanpay = 0 AND merchant_id IS NULL";
        $totalAdminDrivenNotPostedTransactions = DB::connection(MYSQL_RO)->select($adminDrivenNotPostedTransactionDetails, [$request->get('searchDate'), $searchDateArr[1], $searchDateArr[0]]);

        // //Enrollments
        $enrollments = "SELECT sum(if(u.existing_user = 1 AND DATE(u.migrated_at) = ?,1,0)) as new_enrollmentsV1,sum(if(u.existing_user = 1,1,0)) as total_enrollmentsV1,sum(if(u.existing_user = 0 AND DATE(u.created_at) = ?,1,0)) as new_enrollmentsV2,sum(if(u.existing_user = 0,1,0)) as total_enrollmentsV2 FROM users AS u INNER JOIN user_roles AS ur ON u.role_id = ur.role_id WHERE ur.role_name = '" . CONSUMER . "' AND PASSWORD!=''";
        $allEnrollments = DB::connection(MYSQL_RO)->select($enrollments, [$request->get('searchDate'), $request->get('searchDate')]);

        //Average Transactions over the last 7 days
        $averageTransactionDetailsLastWeek = "SELECT IFNULL(avg(amount+tip_amount),0) as weekly_sales,COUNT(*) as total_weekly_transactions
        FROM transaction_details FORCE INDEX(idx_scheduled_posting_date)
        WHERE status_id in ('" . $pending . "', '" . $success . "', '" . $returned . "') AND scheduled_posting_date BETWEEN ? AND  ? AND transaction_ref_no IS NULL  AND isCanpay = 0 AND merchant_id IS NULL";
        $averageWeeklyTransactions = DB::connection(MYSQL_RO)->select($averageTransactionDetailsLastWeek, [$endDate, $startDate]);

        // Daily and monthly no. of Remotepay registrations
        $remotepayRegistrationsSql = "SELECT COUNT(IF(DATE(created_at) = CURRENT_DATE(), 1, NULL)) AS daily_count,
        COUNT(IF(MONTH(created_at) = ? AND YEAR(created_at) = ?, 1, NULL)) AS monthly_count
        FROM users
        WHERE from_ecommerce = 1";
        $remotepayRegistrations = DB::connection(MYSQL_RO)->select($remotepayRegistrationsSql, [$searchDateArr[1], $searchDateArr[0]]);

        $liteRegistrationsSql = "SELECT 
            COUNT(CASE WHEN u.consumer_type = '".LITE_CONSUMER."' AND DATE(u.created_at) = ? THEN 1 END) AS lite_users_today,
            COUNT(CASE WHEN u.consumer_type = '".LITE_CONSUMER."' AND YEAR(u.created_at) = ? 
                    AND MONTH(u.created_at) = ? THEN 1 END) AS lite_users_monthly,
            COUNT(CASE WHEN c.created_at IS NOT NULL AND DATE(c.created_at) = ? THEN 1 END) AS lite_to_standard_conversions_today,
            COUNT(CASE WHEN c.created_at IS NOT NULL AND YEAR(c.created_at) = ? 
                    AND MONTH(c.created_at) = ? THEN 1 END) AS lite_to_standard_conversions_monthly
        FROM users u
        LEFT JOIN consumer_type_conversion_history c ON u.user_id = c.consumer_id";
        
        $liteRegistrations = DB::connection(MYSQL_RO)->select($liteRegistrationsSql, [$searchDate, $searchDateArr[0], $searchDateArr[1], $searchDate, $searchDateArr[0], $searchDateArr[1]]);
        $returnResponse = array(
            'total_sales_v1' => number_format($totalTransactions[0]->total_sales_v1, 2),
            'total_transactions_v1' => $totalTransactions[0]->total_transactions_v1,
            'total_sales_v2' => number_format($totalTransactions[0]->total_sales_v2, 2),
            'total_transactions_v2' => $totalTransactions[0]->total_transactions_v2,
            'total_monthly_sales_v1' => number_format($totalTransactions[0]->total_monthly_sales_v1, 2),
            'total_monthly_transactions_v1' => $totalTransactions[0]->total_monthly_transactions_v1,
            'total_monthly_sales_v2' => number_format($totalTransactions[0]->total_monthly_sales_v2, 2),
            'total_monthly_transactions_v2' => $totalTransactions[0]->total_monthly_transactions_v2,
            'new_enrollments_v1' => $allEnrollments[0]->new_enrollmentsV1,
            'total_enrollments_v1' => $allEnrollments[0]->total_enrollmentsV1,
            'new_enrollments_v2' => $allEnrollments[0]->new_enrollmentsV2,
            'total_enrollments_v2' => $allEnrollments[0]->total_enrollmentsV2,
            'avg_weekly_sales' => number_format($averageWeeklyTransactions[0]->weekly_sales, 2),
            'avg_weekly_transactions' => number_format($averageWeeklyTransactions[0]->total_weekly_transactions / 7),
            'start_date' => date('m/d/Y', strtotime($startDate)),
            'end_date' => date('m/d/Y', strtotime($endDate)),
            'remotepay_daily_registration' => $remotepayRegistrations[0]->daily_count,
            'remotepay_monthly_registration' => $remotepayRegistrations[0]->monthly_count,
            'total_monthly_admin_driven_not_posted_amount' => number_format($totalAdminDrivenNotPostedTransactions[0]->total_monthly_admin_driven_not_posted_amount, 2),
            'total_daily_admin_driven_not_posted_amount' => number_format($totalAdminDrivenNotPostedTransactions[0]->total_daily_admin_driven_not_posted_amount, 2),
            'lite_users_today' => $liteRegistrations[0]->lite_users_today,
            'lite_users_monthly' => $liteRegistrations[0]->lite_users_monthly,
            'lite_to_standard_conversions_today' => $liteRegistrations[0]->lite_to_standard_conversions_today,
            'lite_to_standard_conversions_monthly' => $liteRegistrations[0]->lite_to_standard_conversions_monthly,
        );

        $message = trans('message.dashboard_details_fetched');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Dashboard Information fetched : ", (array) $returnResponse);
        return renderResponse(SUCCESS, $message, $returnResponse); // Returning response
    }

    public function getManualvsDirectLinkedData()
    {
        $active_user_status = getStatus('701');
        $pending_success_returned_status = getPendingSuccessReturnedStatus();
        $consumer_role_id = getRole(CONSUMER);

        // Total Active consumers
        $totalActiveConsumers = User::where(['role_id' => $consumer_role_id, 'status' => $active_user_status])->whereNotNull('password')->count();

        $manualVsDirect = DB::connection(MYSQL_RO)->select("SELECT u.bank_link_type, COUNT(*) cnt
        FROM users u
        INNER JOIN transaction_details td FORCE INDEX(consumer_id) ON td.consumer_id = u.user_id AND td.transaction_ref_no IS NULL AND td.isCanpay = 0 AND td.merchant_id IS NULL AND td.status_id IN (" . $pending_success_returned_status . ") AND td.is_v1 = 0
        GROUP BY u.bank_link_type
        ORDER BY u.bank_link_type ASC");

        $directCount = $manualVsDirect[1]->cnt;
        $manualCount = $manualVsDirect[0]->cnt;
        $totalCount = ($directCount + $manualCount);

        $manualTransactionPercent = number_format(($manualCount * 100) / $totalCount, 2);
        $directTransactionPercent = number_format(($directCount * 100) / $totalCount, 2);

        $returnResponse = array(
            'total_active_conusmers' => number_format($totalActiveConsumers),
            'manual_link_consumer_count' => number_format($manualCount),
            'direct_link_consumer_count' => number_format($directCount),
            'manual_link_transaction_percent' => $manualTransactionPercent,
            'direct_link_transaction_percent' => $directTransactionPercent,
        );

        $message = trans('message.manual_vs_direct_details_fetched');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Manual vs Direct linked Transaction Information fetched : ", (array) $returnResponse);
        return renderResponse(SUCCESS, $message, $returnResponse); // Returning response
    }

    /**
     * generateReturnTransactionReport
     * This function is used to get the return transactions
     * @param  mixed $request
     * @return void
     */
    public function generateReturnTransactionReport(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Return Transaction Report Fetch Started");
        // Validating input request
        $this->validate($request, [
            'from_date' => VALIDATION_REQUIRED,
            'to_date' => VALIDATION_REQUIRED,
        ]);
        $from_date = $request->get('from_date');
        $to_date = $request->get('to_date') . ' 23:59:59';
        $from_date_carbon = Carbon::parse($from_date);
        $to_data_carbon = Carbon::parse($to_date);
        if ($from_date_carbon->diffInDays($to_data_carbon) > config('app.mix_api_return_transaction_max_difference')) {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Return Transaction Report from date and to date difference is greater than " . config('app.mix_api_return_transaction_max_difference') . " days");
            $message = trans('message.date_range_difference_for_return_transaction');
            return renderResponse(FAIL, $message, '');
        }
        // Get status IDs
        $processed_to_acheck = getStatus(PROCESSED_FOR_ACHECK21);
        $success = getStatus(SUCCESS);
        $returned = getStatus(RETURNED);
        $forgiven = getStatus(FORGIVEN);

        $sql = "SELECT transaction_details.id, transaction_details.transaction_number,transaction_details.return_reason,users.user_id,users.first_name, users.middle_name, users.last_name,users.phone,users.existing_user,users.bank_link_type,transaction_details.represent_count, transaction_details.id, registered_merchant_master.merchant_name,  merchant_stores.retailer, terminal_master.terminal_name, transaction_details.amount, transaction_details.tip_amount, transaction_details.transaction_time, transaction_details.local_transaction_time, transaction_details.is_v1, status_master.status, timezone_masters.timezone_name, IF(unknown_return_reasons.return_code IS NOT NULL, unknown_return_reasons.return_code, return_reason_masters.reason_code) reason_code, return_reason_masters.title,return_reason_masters.description, td.local_transaction_time as represented_on, (transaction_details.amount + transaction_details.tip_amount) as total_amount, transaction_details.transaction_returned, transaction_details.represent_block, return_reason_masters.new_banking FROM transaction_details LEFT JOIN transaction_details td ON td.transaction_ref_no = transaction_details.id AND td.is_represented = 1 LEFT JOIN users ON users.user_id = transaction_details.consumer_id INNER JOIN terminal_master ON terminal_master.id = transaction_details.terminal_id INNER JOIN merchant_stores ON merchant_stores.id = terminal_master.merchant_store_id INNER JOIN timezone_masters ON timezone_masters.id = transaction_details.timezone_id LEFT JOIN return_reason_masters ON return_reason_masters.id = transaction_details.return_reason LEFT JOIN unknown_return_reasons ON unknown_return_reasons.transaction_id = transaction_details.id INNER JOIN status_master ON status_master.id = transaction_details.status_id INNER JOIN registered_merchant_master ON registered_merchant_master.id = merchant_stores.merchant_id WHERE transaction_details.transaction_ref_no is null AND transaction_details.isCanpay = 0 AND transaction_details.return_reason is not null ";

        if ($request->get('new_representable') && $request->get('new_representable') == 2) {
            $sql = "SELECT transaction_details.id, transaction_details.transaction_number,transaction_details.return_reason,users.user_id,users.first_name, users.middle_name, users.last_name,users.phone,users.existing_user,users.bank_link_type,transaction_details.represent_count, transaction_details.id, registered_merchant_master.merchant_name,  merchant_stores.retailer, terminal_master.terminal_name, transaction_details.amount, transaction_details.tip_amount, transaction_details.transaction_time, transaction_details.local_transaction_time, transaction_details.is_v1, status_master.status, timezone_masters.timezone_name, IF(unknown_return_reasons.return_code IS NOT NULL, unknown_return_reasons.return_code, return_reason_masters.reason_code) reason_code, return_reason_masters.title,return_reason_masters.description, td.local_transaction_time as represented_on, (transaction_details.amount + transaction_details.tip_amount) as total_amount, transaction_details.transaction_returned, transaction_details.represent_block, return_reason_masters.new_banking
            FROM transaction_details
            JOIN transaction_details td ON transaction_details.id = td.transaction_ref_no
            LEFT JOIN users ON transaction_details.consumer_id = users.user_id
            INNER JOIN terminal_master ON terminal_master.id = transaction_details.terminal_id
            INNER JOIN merchant_stores ON merchant_stores.id = terminal_master.merchant_store_id
            INNER JOIN registered_merchant_master ON registered_merchant_master.id = merchant_stores.merchant_id
            INNER JOIN timezone_masters ON timezone_masters.id = transaction_details.timezone_id
            LEFT JOIN return_reason_masters ON return_reason_masters.id = transaction_details.return_reason
            LEFT JOIN unknown_return_reasons ON unknown_return_reasons.transaction_id = transaction_details.id
            INNER JOIN status_master ON status_master.id = transaction_details.status_id
            WHERE transaction_details.transaction_ref_no IS NULL ";
        }

        $searchArray = [];
        if ($request->get('from_date') && $request->get('to_date')) {
            if ($request->get('toggle_local_transaction_time_search')) {
                $sql .= " AND transaction_details.local_transaction_date BETWEEN ? AND ?";
            } else {
                $sql .= " AND transaction_details.returned_on BETWEEN ? AND ?";
            }
            array_push($searchArray, $from_date, $to_date);
        }

        if ($request->get('phone_no')) {
            $sql .= " AND users.phone = ? ";
            array_push($searchArray, $request->get('phone_no'));
        }
        if ($request->get('consumer')) {
            $sql .= " AND users.user_id = ? ";
            array_push($searchArray, $request->get('consumer'));
        }
        if ($request->get('email')) {
            $sql .= " AND users.email = ? ";
            array_push($searchArray, $request->get('email'));
        }
        if ($request->get('return_reason_id')) {
            $sql .= " AND transaction_details.return_reason = ? ";
            array_push($searchArray, $request->get('return_reason_id'));
        }
        if ($request->get('new_representable') && $request->get('new_representable') == 0) {
            $sql .= " AND transaction_details.represent_count = 0 ";
        }
        if ($request->get('new_representable') && $request->get('new_representable') == 1) {
            $sql .= " AND transaction_details.represent_count > 0 ";
        }
        if ($request->get('new_representable') && $request->get('new_representable') == 2) {
            $sql .= " AND transaction_details.status_id = ? AND transaction_details.return_reason IS NOT NULL AND td.status_id = ? ";
            array_push($searchArray, $success, $forgiven);
        }
        if ($request->has('hide_unknown_retrun_reason')) {
            $sql .= " AND status_master.code != 200 AND (unknown_return_reasons.id IS NULL OR return_reason_masters.id IS NOT NULL) ";
        }

        $sql .= " GROUP BY transaction_details.transaction_number ORDER BY transaction_details.transaction_time DESC";
        $transactions = DB::select($sql, $searchArray);

        $data = array();
        if (!empty($transactions)) {
            // Creating array to show the values in frontend
            foreach ($transactions as $transaction) {
                $nestedData['edit'] = $transaction->id;
                $nestedData['transaction_number'] = $transaction->transaction_number;
                $nestedData['consumer_name'] = $transaction->first_name . ' ' . $transaction->middle_name . ' ' . $transaction->last_name;
                $nestedData['current_purchase_power'] = $this->getCurrentPurchasePower($transaction->user_id);
                $nestedData['user_type'] = $transaction->existing_user == 0 ? 'V2' : 'V1';
                $nestedData['bank_link_type'] = $transaction->bank_link_type == 0 ? 'Manual' : 'Direct';
                $nestedData['transaction_time'] = date('m-d-Y H:i:s', strtotime($transaction->local_transaction_time)) . ' <br/> (' . $transaction->timezone_name . ')';
                $nestedData['total_amount'] = $transaction->total_amount;
                $nestedData['phone'] = $transaction->phone;
                $nestedData['status'] = $transaction->status;
                $nestedData['reason_code'] = $transaction->reason_code;
                $nestedData['represent_count'] = $transaction->represent_count;
                $nestedData['consumer_represented'] = $transaction->represent_block;
                $nestedData['transaction_returned'] = $transaction->transaction_returned;
                $nestedData['new_banking'] = is_null($transaction->new_banking) ? 0 : $transaction->new_banking;
                $nestedData['represented_on'] = date('m-d-Y H:i:s', strtotime($transaction->represented_on)) . ' <br/> (' . $transaction->timezone_name . ')';
                $nestedData['expected_clearance'] = date('m-d-Y', strtotime($transaction->represented_on) + (24 * 3600 * 6));
                $nestedData['title'] = $transaction->title;
                $nestedData['description'] = $transaction->description;
                $nestedData['user_id'] = $transaction->user_id;
                $data[] = $nestedData;
            }
        }

        $returnDetails = [];
        //Fetch Data for Top Table in The Return Transaction Page
        //UnPaid Amount
        if ($request->get('toggle_local_transaction_time_search')) {
            $date_column = 'scheduled_posting_date';
            $table_alias = 'b'; // For pending purpose only
        } else {
            $date_column = 'returned_on';
            $table_alias = 'a'; // For pending purpose only
        }
        if ($request->get('new_representable') != null && $request->get('new_representable') == 1) {
            $unpaidAmount = TransactionDetails::on(MYSQL_RO)->selectRaw('COUNT(*) as unpaid_count, IFNULL(SUM(transaction_details.amount+transaction_details.tip_amount),0) as unpaid')
                ->whereRaw('transaction_ref_no IS NULL')
                ->where('status_id', $returned)
                ->whereRaw("transaction_details." . $date_column . " BETWEEN '" . $from_date . "' AND '" . $to_date . "' AND transaction_details.represent_count > 0")
                ->first();
        } else {
            $unpaidAmount = TransactionDetails::on(MYSQL_RO)->selectRaw('COUNT(*) as unpaid_count, IFNULL(SUM(transaction_details.amount+transaction_details.tip_amount),0) as unpaid')
                ->whereRaw('transaction_ref_no IS NULL')
                ->where('status_id', $returned)
                ->whereRaw("transaction_details." . $date_column . " BETWEEN '" . $from_date . "' AND '" . $to_date . "'")
                ->first();
        }
        $returnDate['title'] = 'Unpaid (' . $unpaidAmount->unpaid_count . ')';
        $returnDate['amount'] = number_format($unpaidAmount->unpaid, 2);
        $returnDetails[] = $returnDate;

        //Pending Amount
        $pendingAmount = DB::connection(MYSQL_RO)->table('transaction_details as a')
            ->join('transaction_details as b', 'a.id', '=', 'b.transaction_ref_no')
            ->selectRaw('IFNULL(SUM(distinct(a.amount+a.tip_amount)),0) as pending_amount')
            ->whereRaw('a.transaction_ref_no IS NULL AND b.transaction_ref_no IS NOT NULL AND a.return_reason IS NOT NULL')
            ->where('b.is_represented', 1)
            ->where('b.status_id', $processed_to_acheck)
            ->where('b.entry_type', DEBIT)
            ->whereRaw($table_alias . "." . $date_column . " BETWEEN '" . $from_date . "' AND '" . $to_date . "'")
            ->first();
        $returnDate['title'] = 'Pending';
        $returnDate['amount'] = $request->get('new_representable') != null && $request->get('new_representable') == 0 ? 0 : number_format($pendingAmount->pending_amount, 2);
        $returnDetails[] = $returnDate;

        //Paid Amount
        $paidAmount = TransactionDetails::on(MYSQL_RO)->selectRaw('IFNULL(SUM(amount+tip_amount),0) as paid_amount')
            ->whereRaw('transaction_ref_no IS NULL')
            ->where('status_id', $success)
            ->whereRaw("transaction_details." . $date_column . " BETWEEN '" . $from_date . "' AND '" . $to_date . "'");

        if ($paidAmount == 'returned_on') {
            $paidAmount = $paidAmount->whereNotNull('transaction_details.return_reason');
        }

        $paidAmount = $paidAmount->first();
        $returnDate['title'] = 'Paid';
        $returnDate['amount'] = $request->get('new_representable') != null && $request->get('new_representable') == 0 ? 0 : number_format($paidAmount->paid_amount, 2);
        $returnDetails[] = $returnDate;

        $returnDate['title'] = 'Total Returns Value';
        if ($request->get('new_representable') != null && $request->get('new_representable') == 0) {
            $returnDate['amount'] = number_format($unpaidAmount->unpaid, 2);
        } else if ($request->get('new_representable') != null && $request->get('new_representable') == 1) {
            $returnDate['amount'] = number_format($paidAmount->paid_amount + $pendingAmount->pending_amount, 2);
        } else {
            $returnDate['amount'] = number_format($unpaidAmount->unpaid + $paidAmount->paid_amount + $pendingAmount->pending_amount, 2);
        }
        $returnDetails[] = $returnDate;

        $returnResponse = [
            'report' => $data,
            'returnDetails' => $returnDetails,
        ];

        $message = trans('message.consumer_transaction_fetch_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Return Transaction Report Fetched Successfully for the Period : " . $from_date . " AND " . $to_date);
        return renderResponse(SUCCESS, $message, $returnResponse); // Returning response
    }

    /**
     * getCurrentPurchasePower
     * This function will return the Consumer's current Purchase Power i.e; (Purchase Power - Non-Settled Transactions) and for those who haven't linked their bank account through Finicity this API will return a Text Message to Display in place of Purchase Power.
     * @return void
     */
    public function getCurrentPurchasePower($user_id)
    {
        $user_details = User::where('user_id', $user_id)->first(); // Fetching Logged In User Details

        // if consumer is v1 consumer
        if ($user_details->existing_user == 1 && $user_details->disable_automatic_purchase_power == 1) {
            $purchase_power = $this->_getCustomPurchasePower($user_details);
            return number_format($purchase_power, 2); // API Response returned with 200 status
        }
        if ($user_details->purchase_power) { // Check if the Purchase Power is Null or Not
            // Getting the total Non-Settled Transactions for the Consumer
            $pending_transactions = TransactionDetails::join('status_master', 'transaction_details.status_id', '=', 'status_master.id')->select(DB::raw("IFNULL(sum(transaction_details.amount + transaction_details.tip_amount),0) as pending_transaction"))->where(['transaction_details.transaction_place' => 'POS', 'transaction_details.consumer_id' => $user_details->user_id, 'transaction_details.is_v1' => 0])->whereRaw('transaction_details.transaction_ref_no is null')->where('status_master.code', PENDING)->first();

            $current_purchase_power = floor($user_details->purchase_power - $pending_transactions->pending_transaction); // Calculating the current Purchase Power

            return number_format($current_purchase_power, 2); // API Response returned with 200 status
        } else {
            //Return a Text Message in Place of Purchase Power Amount as they are not eligible for Purchase Power
            return null; // API Response returned with 200 status
        }
    }

    private function _getCustomPurchasePower($user_details)
    {
        $pending = getStatus(PENDING);
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "User is an existing consumer with id: " . $user_details->user_id);
        //get the timezone of last transaction and check if they have made any transactions today
        $last_transaction = TransactionDetails::leftJoin('timezone_masters', 'timezone_masters.id', '=', 'transaction_details.timezone_id')->select('timezone_masters.timezone_name', 'transaction_details.*')
            ->where('transaction_details.transaction_ref_no', null)->where('transaction_details.isCanpay', 0)->where('transaction_details.is_v1', 0)->where('transaction_details.consumer_id', $user_details->user_id)->orderBy('transaction_details.created_at', 'desc')->first();
        $today = empty($last_transaction) ? Carbon::now()->toDateString() : Carbon::now($last_transaction->timezone_name)->toDateString();

        $transaction = TransactionDetails::select(DB::raw('sum(amount) as sum'), DB::raw('sum(tip_amount) as tip_sum'))->where('transaction_ref_no', null)->where('isCanpay', 0)->where('is_v1', 0)->where('status_id', $pending)->where('consumer_id', $user_details->user_id)->where("local_transaction_date", $today)->first();
        $amount_sum = ($transaction->sum != null) ? $transaction->sum : 0;
        $tip_sum = ($transaction->tip_sum != null) ? $transaction->tip_sum : 0;
        $sum = $amount_sum + $tip_sum;
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Today's total pending amount: " . $sum . " for consumer with id: " . $user_details->user_id);
        $purchase_power = $user_details->purchase_power - $sum;
        return $purchase_power;
    }

    /**
     * getReturnTransactionReportExport
     * This is the export function for Store API Keys
     * @param  mixed $request
     * @return void
     */
    public function getReturnTransactionReportExport(Request $request)
    {
        return Excel::download(new ReturnTransactionExport($request), 'return_transaction' . date('m-d-Y H:i:s') . '.xlsx');
    }

    /**
     * generateReturnTransactionGroupReport
     * This function is used to get the return transactions
     * @param  mixed $request
     * @return void
     */
    public function generateReturnTransactionGroupReport(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Return Transaction Group Report Fetch Started");
        // Validating input request
        $this->validate($request, [
            'from_date' => VALIDATION_REQUIRED,
            'to_date' => VALIDATION_REQUIRED,
        ]);
        $from_date = $request->get('from_date');
        $to_date = $request->get('to_date') . ' 23:59:59';

        $sql = "SELECT
        IF(urr.return_code IS NOT NULL, urr.return_code, rrm.reason_code) reason_code, COUNT(*) AS total_count, SUM(td.amount+td.tip_amount) AS 'total_return_amount', sm.status
        FROM transaction_details AS td FORCE INDEX(date_key)
        INNER JOIN return_reason_masters rrm ON td.return_reason = rrm.id
        LEFT JOIN unknown_return_reasons urr ON urr.transaction_id = td.id
        INNER JOIN users AS us ON td.consumer_id = us.user_id
        INNER JOIN status_master AS sm ON td.status_id = sm.id
        WHERE td.entry_type = 'Dr' AND td.transaction_ref_no IS NULL AND return_reason IS NOT NULL";

        $searchArray = [];
        if ($request->get('from_date') && $request->get('to_date')) {
            if ($request->get('toggle_local_transaction_time_search')) {
                $sql .= " AND td.local_transaction_date BETWEEN ? AND ?";
            } else {
                $sql .= " AND td.returned_on BETWEEN ? AND ?";
            }
            array_push($searchArray, $from_date, $to_date);
        }
        if ($request->get('new_representable') && $request->get('new_representable') == 0) {
            $sql .= " AND td.represent_count = 0 ";
        }
        if ($request->get('new_representable') && $request->get('new_representable') == 1) {
            $sql .= " AND td.represent_count > 0 ";
        }
        $sql .= " GROUP BY rrm.reason_code, sm.status WITH ROLLUP;";
        $transactions = DB::connection(MYSQL_RO)->select($sql, $searchArray);

        $data = array();
        if (!empty($transactions)) {
            // Creating array to show the values in frontend
            foreach ($transactions as $transaction) {
                $nestedData['reason_code'] = $transaction->reason_code;
                $nestedData['total_count'] = $transaction->total_count;
                $nestedData['total_return_amount'] = $transaction->total_return_amount;
                $nestedData['status'] = $transaction->status;
                $data[] = $nestedData;
            }
        }

        $processed_to_acheck = getStatus(PROCESSED_FOR_ACHECK21);
        $success = getStatus(SUCCESS);
        $returned = getStatus(RETURNED);

        $returnDetails = [];
        //Fetch Data for Top Table in The Return Transaction Page
        //UnPaid Amount
        if ($request->get('toggle_local_transaction_time_search')) {
            $date_column = 'local_transaction_time';
            $table_alias = 'b'; // For pending purpose only
        } else {
            $date_column = 'returned_on';
            $table_alias = 'a'; // For pending purpose only
        }
        if ($request->get('new_representable') != null && $request->get('new_representable') == 1) {
            $unpaidAmount = TransactionDetails::on(MYSQL_RO)->selectRaw('COUNT(*) as unpaid_count, IFNULL(SUM(transaction_details.amount+transaction_details.tip_amount),0) as unpaid')
                ->whereRaw('transaction_ref_no IS NULL')
                ->where('status_id', $returned)
                ->whereRaw("transaction_details." . $date_column . " BETWEEN '" . $from_date . "' AND '" . $to_date . "' AND transaction_details.represent_count > 0")
                ->first();
        } else {
            $unpaidAmount = TransactionDetails::on(MYSQL_RO)->selectRaw('COUNT(*) as unpaid_count, IFNULL(SUM(transaction_details.amount+transaction_details.tip_amount),0) as unpaid')
                ->whereRaw('transaction_ref_no IS NULL')
                ->where('status_id', $returned)
                ->whereRaw("transaction_details." . $date_column . " BETWEEN '" . $from_date . "' AND '" . $to_date . "'")
                ->first();
        }
        $returnDate['title'] = 'Unpaid (' . $unpaidAmount->unpaid_count . ')';
        $returnDate['amount'] = number_format($unpaidAmount->unpaid, 2);
        $returnDetails[] = $returnDate;

        //Pending Amount
        $pendingAmount = DB::connection(MYSQL_RO)->table('transaction_details as a')
            ->join('transaction_details as b', 'a.id', '=', 'b.transaction_ref_no')
            ->selectRaw('IFNULL(SUM(distinct(a.amount+a.tip_amount)),0) as pending_amount')
            ->whereRaw('a.transaction_ref_no IS NULL AND b.transaction_ref_no IS NOT NULL AND a.return_reason IS NOT NULL')
            ->where('b.is_represented', 1)
            ->where('b.status_id', $processed_to_acheck)
            ->where('b.entry_type', DEBIT)
            ->whereRaw($table_alias . "." . $date_column . " BETWEEN '" . $from_date . "' AND '" . $to_date . "'")
            ->first();
        $returnDate['title'] = 'Pending';
        $returnDate['amount'] = $request->get('new_representable') != null && $request->get('new_representable') == 0 ? 0 : number_format($pendingAmount->pending_amount, 2);
        $returnDetails[] = $returnDate;

        //Paid Amount
        $paidAmount = TransactionDetails::on(MYSQL_RO)->selectRaw('IFNULL(SUM(amount+tip_amount),0) as paid_amount')
            ->whereRaw('transaction_ref_no IS NULL')
            ->where('status_id', $success)
            ->whereRaw("transaction_details." . $date_column . " BETWEEN '" . $from_date . "' AND '" . $to_date . "'")
            ->first();
        $returnDate['title'] = 'Paid';
        $returnDate['amount'] = $request->get('new_representable') != null && $request->get('new_representable') == 0 ? 0 : number_format($paidAmount->paid_amount, 2);
        $returnDetails[] = $returnDate;

        $returnDate['title'] = 'Total Returns Value';
        if ($request->get('new_representable') != null && $request->get('new_representable') == 0) {
            $returnDate['amount'] = number_format($unpaidAmount->unpaid, 2);
        } else if ($request->get('new_representable') != null && $request->get('new_representable') == 1) {
            $returnDate['amount'] = number_format($paidAmount->paid_amount + $pendingAmount->pending_amount, 2);
        } else {
            $returnDate['amount'] = number_format($unpaidAmount->unpaid + $paidAmount->paid_amount + $pendingAmount->pending_amount, 2);
        }
        $returnDetails[] = $returnDate;

        $returnResponse = [
            'report' => $data,
            'returnDetails' => $returnDetails,
        ];

        $message = trans('message.consumer_transaction_fetch_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Return Transaction Report Fetched Successfully for the Period : " . $from_date . " AND " . $to_date);
        return renderResponse(SUCCESS, $message, $returnResponse); // Returning response
    }

    /**
     * getReturnTransactionReportExport
     * This is the export function for Store API Keys
     * @param  mixed $request
     * @return void
     */
    public function getReturnTransactionGroupReportExport(Request $request)
    {
        return Excel::download(new ReturnTransactionGroupExport($request), 'group_return_transaction' . date('m-d-Y H:i:s') . '.xlsx');
    }

    /**
     * Fetches all return status from table
     */
    public function getReturnStatus()
    {
        $status = DB::select("SELECT id, code, status FROM status_master WHERE code IN (901,902)  order by status");
        $message = trans('message.user_status_success');
        return renderResponse(SUCCESS, $message, $status); // API Response returned with 200 status
    }

    /**
     * Fetches all return reasons from table
     */
    public function getReturnReasons()
    {
        $reasons = GlobalReasonMaster::on(MYSQL_RO)->where('is_return', 1)->orderBy('reason')->get();
        $message = trans('message.return_reasons_success');
        return renderResponse(SUCCESS, $message, $reasons); // API Response returned with 200 status
    }

    /**
     * waiveTransaction
     * This function will update the parent transaction status to success and will add a new row against that transaction with the status given by Admin
     * @param  mixed $request
     * @return void
     */
    public function waiveTransaction(Request $request)
    {
        $rule = array(
            'transaction_id' => VALIDATION_REQUIRED,
            'status' => VALIDATION_REQUIRED,
            'reason' => VALIDATION_REQUIRED,
        );
        $this->__validate($this->request->all(), $rule);
        $user_details = Auth::user();
        $transaction = TransactionDetails::find($request->get('transaction_id'));
        if (empty($transaction)) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction not found during waiving return.");
            $message = trans('message.transaction_error');
            return renderResponse(FAIL, $message, null); // Exception Returned
        }
        DB::beginTransaction();
        try {
            // Insert another row for return waiver with new status
            $transaction_details = new TransactionDetails();
            $transaction_details->transaction_number = $transaction->transaction_number;
            $transaction_details->transaction_ref_no = $transaction->id;
            $transaction_details->user_id = $transaction->user_id;
            $transaction_details->consumer_id = $transaction->consumer_id;
            $transaction_details->terminal_id = $transaction->terminal_id;
            $transaction_details->transaction_time = Carbon::now();
            $transaction_details->local_transaction_year = $transaction->local_transaction_year;
            $transaction_details->local_transaction_month = $transaction->local_transaction_month;
            $transaction_details->local_transaction_date = $transaction->local_transaction_date;
            $timezone = TimezoneMaster::find($transaction->timezone_id);
            $transaction_details->local_transaction_time = Carbon::now($timezone->timezone_name);
            $transaction_details->timezone_id = $transaction->timezone_id;
            $transaction_details->amount = $transaction->amount;
            $transaction_details->tip_amount = $transaction->tip_amount;
            $transaction_details->tip_type = $transaction->tip_type;
            $transaction_details->tip_add_time = $transaction->tip_add_time;
            $transaction_details->used_qr_id = $transaction->used_qr_id;
            $transaction_details->status_id = $request->get('status');
            $transaction_details->global_reason_id = $request->get('reason');
            $transaction_details->transaction_type_id = $transaction->transaction_type_id;
            $transaction_details->transaction_place = 'Admin';
            $transaction_details->acheck_document_id = $transaction->documentId;
            $transaction_details->save();

            // Update parent row to success
            $transaction->status_id = getStatus(SUCCESS);
            $transaction->save();

            DB::commit();
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Return waived for transaction ID: " . $transaction->transaction_number . " by User: " . $user_details->user_id);
            $message = trans('message.return_waived_successfully');
            return renderResponse(SUCCESS, $message, null); // API Response returned with 200 status
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured during Waiving return transaction.", [EXCEPTION => $e]);
            DB::rollback();
            $message = trans('message.return_waived_error');
            // Exception Returned
            return renderResponse(FAIL, $message, null);
        }
    }

    /**
     * generateReturnTransactionDetails
     * Fetch the Transaction Details for a particular Transaction Number
     * @param  mixed $request
     * @return void
     */
    public function generateReturnTransactionDetails(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Return Transaction Details Fetch Started");
        // Validating input request
        $this->validate($request, [
            'transaction_number' => VALIDATION_REQUIRED,
        ]);

        $returntransactionDetails = TransactionDetails::join('status_master', 'status_master.id', '=', 'transaction_details.status_id')
            ->select('transaction_details.*', 'status_master.*', 'transaction_details.created_at as tr_created_at')
            ->where('transaction_details.transaction_number', $request->get('transaction_number'))
            ->orderBy('transaction_details.created_at', 'ASC')
            ->get();

        $returnData = [];
        $inc = 0;
        $representCnt = 1;
        foreach ($returntransactionDetails as $valTransaction) {
            $data['local_transaction_date'] = $valTransaction->code == FORGIVEN ? date("m-d-Y", strtotime($valTransaction->tr_created_at)) : date("m-d-Y", strtotime($valTransaction->local_transaction_date));
            if ($valTransaction->code == PROCESSED_FOR_ACHECK21 && $valTransaction->is_represented == 1) {
                $data['status'] = numToOrdinalWord($representCnt) . " Representment";
                array_push($returnData, $data);
                $representCnt++;
            } else if ($valTransaction->code == RETURNED && $inc != 0) {
                $data['status'] = "Return Received";
                array_push($returnData, $data);
            } else if ($valTransaction->code != PROCESSED_FOR_ACHECK21 && $valTransaction->code != PROCESSED_TO_BANK) {
                $data['status'] = $inc == 0 ? 'Transaction Date' : $valTransaction->status;
                array_push($returnData, $data);
            }
            $inc++;
        }

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Return transaction Details Fetched Successfully.");
        $message = trans('message.return_transaction_details_success');
        return renderResponse(SUCCESS, $message, $returnData);
    }

    /**
     * getUsersPpLists
     * Fetch the list of Users having Purchase Power more than 1500
     * @return void
     */
    public function getUsersPpLists()
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Users List having PP > 1500 Fetch Started");

        $userList = User::join('user_roles', function ($join) {
            $join->on("users.role_id", "=", "user_roles.role_id");
            $join->on("user_roles.role_name", "=", DB::raw('"' . CONSUMER . '"'));
        })
            ->where('users.purchase_power', '>', 1500)
            ->selectRaw('REPLACE(CONCAT(COALESCE(REPLACE(users.first_name, " ",""), "")," ",COALESCE(REPLACE(users.middle_name, " ",""), "")," ",COALESCE(REPLACE(users.last_name, " ",""), "")),"  "," ") as name, users.email, users.phone as phone_no,users.purchase_power')
            ->orderBy('users.purchase_power', 'DESC')
            ->get();

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Users List having PP > 1500 Fetched Successfully.");
        $message = trans('message.user_list_fetch_success');
        return renderResponse(SUCCESS, $message, $userList);
    }

    /**
     * getUsersPurchase
     * Fetch the Transaction Details for a particular Transaction Number
     * @return void
     */
    public function getUsersPurchase()
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Users with Purchase > 700 in last 48 hrs Fetch Started");

        $userList = TransactionDetails::join('users', 'transaction_details.consumer_id', '=', 'users.user_id')
            ->join('status_master', function ($join) {
                $join->on("status_master.id", "=", "transaction_details.status_id");
                $join->on("status_master.code", "=", DB::raw('"' . PENDING . '"'));
            })
            ->whereRaw('transaction_details.local_transaction_time > NOW() - INTERVAL 48 HOUR')
            ->selectRaw('REPLACE(CONCAT(COALESCE(REPLACE(users.first_name, " ",""), "")," ",COALESCE(REPLACE(users.middle_name, " ",""), "")," ",COALESCE(REPLACE(users.last_name, " ",""), "")),"  "," ") as name, users.email, users.phone as phone_no,COALESCE(SUM(transaction_details.amount + transaction_details.tip_amount),0) as total_purchase, COALESCE(SUM(1),0) as no_of_transactions')
            ->groupBy('transaction_details.consumer_id')
            ->havingRaw('COALESCE(SUM(transaction_details.amount + transaction_details.tip_amount),0) >= 700')
            ->orderBy('total_purchase', 'DESC')
            ->get();

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Users with Purchase > 700 in last 48 hrs Fetched Successfully.");
        $message = trans('message.user_list_fetch_success');
        return renderResponse(SUCCESS, $message, $userList);
    }

    /**
     * getUsersPpListsExport
     * This is the export function for Exporting users with Purchase Power > 1500
     * @return void
     */
    public function getUsersPpListsExport()
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Users List having PP > 1500 Export Started.");

        $userList = User::join('user_roles', function ($join) {
            $join->on("users.role_id", "=", "user_roles.role_id");
            $join->on("user_roles.role_name", "=", DB::raw('"' . CONSUMER . '"'));
        })
            ->where('users.purchase_power', '>', 1500)
            ->selectRaw('REPLACE(CONCAT(COALESCE(REPLACE(users.first_name, " ",""), "")," ",COALESCE(REPLACE(users.middle_name, " ",""), "")," ",COALESCE(REPLACE(users.last_name, " ",""), "")),"  "," ") as name, users.email, users.phone as phone_no,users.purchase_power')
            ->orderBy('users.purchase_power', 'DESC')
            ->get();

        $returnResponse = array(
            'userList' => $userList,
        );

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Users List having PP > 1500 Export Complete.");
        return Excel::download(new UsersPurchasePowerExport($returnResponse), 'users_pp_list' . date('m-d-Y H:i:s') . '.csv');
    }

    /**
     * getUsersPurchaseExport
     * This is the export function for Exporting users with Purchase > 700
     * @param  mixed $request
     * @return void
     */
    public function getUsersPurchaseExport(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Users with Purchase > 700 in last 48 hrs Export started.");

        $userList = TransactionDetails::join('users', 'transaction_details.consumer_id', '=', 'users.user_id')
            ->join('status_master', function ($join) {
                $join->on("status_master.id", "=", "transaction_details.status_id");
                $join->on("status_master.code", "=", DB::raw('"' . PENDING . '"'));
            })
            ->whereRaw('transaction_details.local_transaction_time > NOW() - INTERVAL 48 HOUR')
            ->selectRaw('REPLACE(CONCAT(COALESCE(REPLACE(users.first_name, " ",""), "")," ",COALESCE(REPLACE(users.middle_name, " ",""), "")," ",COALESCE(REPLACE(users.last_name, " ",""), "")),"  "," ") as name, users.email, users.phone as phone_no,COALESCE(SUM(transaction_details.amount + transaction_details.tip_amount),0) as total_purchase, COALESCE(SUM(1),0) as no_of_transactions')
            ->groupBy('transaction_details.consumer_id')
            ->havingRaw('COALESCE(SUM(transaction_details.amount + transaction_details.tip_amount),0) >= 700')
            ->orderBy('total_purchase', 'DESC')
            ->get();

        $returnResponse = array(
            'userList' => $userList,
        );

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Users with Purchase > 700 in last 48 hrs Export complete.");
        return Excel::download(new UsersPurchaseExport($returnResponse), 'users_purchase' . date('m-d-Y H:i:s') . '.csv');
    }

    /**
     * getReturnReasonList
     * This is the fetch function for Return Reason list
     * @param  mixed $request
     * @return void
     */
    public function getReturnReasonList(Request $request)
    {
        $returnReasonMasters = DB::table('return_reason_masters')
            ->select('return_reason_masters.id as return_reason_id', 'return_reason_masters.reason_code', 'return_reason_masters.title')
            ->where('return_reason_masters.reason_code', '!=', 'D100')
            ->get();
        $message = trans('message.return_reason_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . $message);
        return renderResponse(SUCCESS, $message, $returnReasonMasters);
    }

    /**
     * sendMailBasedOnReturn
     * This is the  function for Send Email to Consumer based on return transaction
     * @param  mixed $request
     * @return void
     */
    public function sendMailBasedOnReturn(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Return Transaction Report Fetch Started for send mail ");
        // Validating input request
        $this->validate($request, [
            'from_date' => VALIDATION_REQUIRED,
            'to_date' => VALIDATION_REQUIRED,
        ]);

        $from_date = $request->get('from_date');
        $to_date = $request->get('to_date') . ' 23:59:59';

        $sql = "SELECT transaction_details.id, transaction_details.transaction_number,transaction_details.return_reason,transaction_details.consumer_id,users.email,transaction_details.represent_count, transaction_details.id, registered_merchant_master.merchant_name,  merchant_stores.retailer, terminal_master.terminal_name, transaction_details.amount, transaction_details.tip_amount, transaction_details.transaction_time, transaction_details.local_transaction_time, transaction_details.is_v1, status_master.status, timezone_masters.timezone_name ,return_reason_masters.reason_code,return_reason_masters.title,return_reason_masters.description, return_reason_masters.new_title, td.local_transaction_time as represented_on, (transaction_details.amount + transaction_details.tip_amount) as total_amount FROM transaction_details force index(date_key) LEFT JOIN transaction_details td ON td.transaction_ref_no = transaction_details.id AND td.is_represented = 1 LEFT JOIN users ON users.user_id = transaction_details.consumer_id INNER JOIN terminal_master ON terminal_master.id = transaction_details.terminal_id INNER JOIN merchant_stores ON merchant_stores.id = terminal_master.merchant_store_id LEFT JOIN timezone_masters ON timezone_masters.id = transaction_details.timezone_id LEFT JOIN return_reason_masters ON return_reason_masters.id = transaction_details.return_reason LEFT JOIN unknown_return_reasons ON unknown_return_reasons.transaction_id = transaction_details.id INNER JOIN status_master ON status_master.id = transaction_details.status_id INNER JOIN registered_merchant_master ON registered_merchant_master.id = merchant_stores.merchant_id WHERE transaction_details.transaction_ref_no is null AND transaction_details.isCanpay = 0 AND transaction_details.return_reason is not null AND status_master.code != 200 AND return_reason_masters.reason_code != 'D100'";

        $searchArray = [];
        if ($request->get('from_date') && $request->get('to_date')) {
            if ($request->get('toggle_local_transaction_time_search')) {
                $sql .= " AND transaction_details.local_transaction_date BETWEEN ? AND ?";
            } else {
                $sql .= " AND transaction_details.returned_on BETWEEN ? AND ?";
            }
            array_push($searchArray, $from_date, $to_date);
        }

        if ($request->get('phone_no')) {
            $sql .= " AND users.phone = ? ";
            array_push($searchArray, $request->get('phone_no'));
        }
        if ($request->get('consumer')) {
            $sql .= " AND users.user_id = ? ";
            array_push($searchArray, $request->get('consumer'));
        }
        if ($request->get('return_reason_id')) {
            $sql .= " AND transaction_details.return_reason = ? ";
            array_push($searchArray, $request->get('return_reason_id'));
        }

        $sql .= " AND (unknown_return_reasons.id IS NULL OR return_reason_masters.id IS NOT NULL) GROUP BY transaction_details.transaction_number ORDER BY transaction_details.transaction_time DESC";
        $transactions = DB::connection(MYSQL_RO)->select($sql, $searchArray);
        try {
            if (!empty($transactions)) {
                ini_set('max_execution_time', 6000);
                foreach ($transactions as $value) {
                    $transaction_details_params = [
                        'user_id' => $value->consumer_id,
                        'transaction_id' => $value->id,
                        'return_reason_description' => $value->new_title,
                        'return_reason_id' => $value->return_reason,
                    ];
                    $this->emailexecutor->consumerReturnTransactionMail($transaction_details_params);
                }
                $message = trans('message.mail_to_consumer_based_on_return_transaction_success');
            } else {
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No records found for sending return transactions email.");
                $message = trans('message.no_transaction_found');
            }
            return renderResponse(SUCCESS, $message, $transactions);
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured during Waiving return transaction.", [EXCEPTION => $e]);

            $message = trans('message.mail_to_consumer_based_on_return_transaction_fail');
            // Exception Returned
            return renderResponse(FAIL, $message, null);
        }
    }

    /**
     * exportReturnList
     * Export the List of return transactions for a particular day
     * @param  mixed $request
     * @return void
     */
    public function exportReturnList(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Return Transaction List export started.");

        // Validating input request
        $this->validate($request, [
            'transaction_date' => VALIDATION_REQUIRED,
            'ach_file_id' => VALIDATION_REQUIRED,
        ]);

        $return_transaction_list = "SELECT
        REPLACE(CONCAT(COALESCE(
        REPLACE(us.first_name, ' ',''), ''),' ', COALESCE(
        REPLACE(us.middle_name, ' ',''), ''), ' ', COALESCE(
        REPLACE(us.last_name, ' ',''), '')),'  ',' ') AS head,0.00 AS credit, COALESCE(td.amount+td.tip_amount,0) AS debit, td.transaction_time, IF(urr.return_code IS NOT NULL, urr.return_code, rrm.reason_code) reason_code,
        CASE WHEN (td1.return_from_primary_account = 1 and td1.transaction_returned != td1.transaction_represented) THEN 'New Transaction' ELSE 'Represented' END as type, td1.represent_count
        FROM ach_six_record_details ach JOIN `transaction_details` AS `td` ON ach.transaction_id = td.id
        INNER JOIN transaction_details AS td1 ON td.transaction_ref_no = td1.id
        INNER JOIN return_reason_masters rrm ON td1.return_reason = rrm.id
        LEFT JOIN unknown_return_reasons urr ON urr.transaction_id = td1.id
        INNER JOIN users AS us ON td.consumer_id = us.user_id
        INNER JOIN status_master AS sm ON td.status_id = sm.id
        LEFT JOIN return_manual_post_logs rmpl ON td.transaction_ref_no = rmpl.primary_identifier_value AND rmpl.return_posted = '1'
        WHERE transaction_date = ? AND is_voided = 0 AND is_return = 1 AND ach_file_id = ?
        ORDER BY td.local_transaction_time DESC";
        $return_transaction_array = DB::connection(MYSQL_RO)->select($return_transaction_list, [$request->get('transaction_date'), $request->get('ach_file_id')]);

        $returnResponse = array(
            'return_transactions' => $return_transaction_array,
        );

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Return Transaction List export complete.");
        return Excel::download(new ReturnTransactionDailyExport($returnResponse), 'return_transaction_daily_list' . $request->get('transaction_date') . '.xlsx');
    }

    /**
     * getMonthlySalesGrowth
     * This function is used to get monthly sales growth
     * @param  mixed $request
     * @return void
     */
    public function getMonthlySalesGrowth(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Monthly Sales Growth report fetch started...");

        // Validating input request
        $this->validate($request, [
            'store_id' => VALIDATION_REQUIRED,
        ]);

        $store_id = $request->get('store_id');
        $from_date = date('Y-m-01', strtotime("-6 months", strtotime(date('Y-m-d'))));
        $to_date = date('Y-m-t', strtotime("-1 months", strtotime(date('Y-m-d'))));

        //Get Status ID
        $pending_status_id = getStatus(PENDING);
        $success_status_id = getStatus(SUCCESS);
        $return_status_id = getStatus(RETURNED);

        $sql = "SELECT monthname(str_to_date(td.local_transaction_month,'%m')) as month , COUNT(*) as transaction_count
        FROM transaction_details td
        INNER JOIN terminal_master tm ON td.terminal_id = tm.id
        INNER JOIN merchant_stores ms ON tm.merchant_store_id = ms.id
        INNER JOIN users u ON td.consumer_id = u.user_id
        WHERE ms.store_id = ? AND td.transaction_ref_no IS NULL AND td.isCanpay = 0 AND td.scheduled_posting_date BETWEEN ? AND ? AND td.status_id IN ('" . $pending_status_id . "','" . $success_status_id . "','" . $return_status_id . "')
        GROUP BY td.local_transaction_month ";

        $results = DB::connection(MYSQL_RO)->select($sql, [$store_id['store_id'], $from_date, $to_date]);

        $data = array();
        if (!empty($results)) {
            // Creating array to show the values in frontend
            foreach ($results as $result) {
                $nestedData['month'] = $result->month;
                $nestedData['transaction_count'] = $result->transaction_count;
                $data[] = $nestedData;
            }
        }

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Monthly Sales Growth report fetch successfully.");
        $message = trans('message.monthly_sales_growth_fetch_success');
        return renderResponse(SUCCESS, $message, $data); // Returning response
    }

    /**
     * getMonthlySalesGrowthExport
     * This is the export function for Monthly Sales Growth
     * @param  mixed $request
     * @return void
     */
    public function getMonthlySalesGrowthExport(Request $request)
    {
        return Excel::download(new MonthlySalesGrowthExport($request), 'consumer_transaction_' . date('m-d-Y H:i:s') . '.xlsx');
    }

    /**
     * getVoidTransactionReport
     * This function is used to get the consumer transactions for Void
     * @param  mixed $request
     * @return void
     */
    public function getVoidTransactionReport(Request $request)
    {
        $declined_status = StatusMaster::where('code', DECLINED)->first();
        $from_date = $request->get('from_date');
        $to_date = $request->get('to_date');

        $sql = "SELECT transaction_details.transaction_number, users.status AS user_status,users.first_name, users.middle_name, users.last_name, transaction_details.id, registered_merchant_master.merchant_name,  merchant_stores.retailer, CASE WHEN transaction_details.pos_web_identifier != '' THEN transaction_details.pos_web_identifier ELSE terminal_master.terminal_name END as terminal_name, transaction_details.transaction_time, transaction_details.local_transaction_time, transaction_details.is_v1,  timezone_masters.timezone_name, transaction_details.scheduled_posting_date, modified_transaction.scheduled_posting_date as m_scheduled_posting_date, transaction_details.scheduled_posting_time, modified_transaction.scheduled_posting_time as m_scheduled_posting_time,transaction_details.consumer_approval_for_change_request, transaction_details.expiration_datetime, transaction_details.change_request_transaction_ref_no, modified_status_master.code as m_code, status_master.code, transaction_details.change_request, transaction_details.local_transaction_date, transaction_details.is_ecommerce, transaction_details.admin_driven, transaction_details.delivery_fee,
        CASE
            WHEN transaction_details.change_request = 1 AND (modified_status_master.code='" . VOIDED . "' OR modified_status_master.status IS NULL) THEN casm.`status`
            WHEN modified_status_master.status IS NOT NULL THEN modified_status_master.status
            ELSE status_master.status
        END AS status,
        (CASE
            WHEN (modified_transaction.id != '') THEN modified_transaction.amount
            ELSE transaction_details.amount
        END) as amount,
        (CASE
            WHEN (modified_transaction.id != '') THEN modified_transaction.tip_amount
            ELSE transaction_details.tip_amount
        END) as tip_amount
        FROM transaction_details force index(date_key)
        left join `transaction_details` as `modified_transaction` on `modified_transaction`.`id` = `transaction_details`.`change_request_transaction_ref_no`
        left join `status_master` as `modified_status_master` on `modified_status_master`.`id` = `modified_transaction`.`status_id`
        left join `status_master` as `casm` on `casm`.`id` = `transaction_details`.`consumer_approval_for_change_request`
        JOIN users ON users.user_id = transaction_details.consumer_id
        JOIN terminal_master ON terminal_master.id = transaction_details.terminal_id
        JOIN merchant_stores ON merchant_stores.id = terminal_master.merchant_store_id
        JOIN timezone_masters ON timezone_masters.id = transaction_details.timezone_id
        JOIN status_master ON status_master.id = transaction_details.status_id
        JOIN registered_merchant_master ON registered_merchant_master.id = merchant_stores.merchant_id
        WHERE transaction_details.transaction_ref_no is null and ((`transaction_details`.`attempt_count` = 0 and transaction_details.change_request_transaction_ref_no IS NULL) or (`transaction_details`.`attempt_count` != 0))AND transaction_details.isCanpay = 0 ";

        $searchArray = [];
        if ($request->get('from_date')) {
            $sql .= " AND transaction_details.local_transaction_date >= ?";
            array_push($searchArray, $request->get('from_date'));
        }
        if ($request->get('to_date')) {
            $sql .= " AND transaction_details.local_transaction_date <= ?";
            array_push($searchArray, $request->get('to_date'));
        }
        if ($request->get('store_id')) {
            $sql .= " AND merchant_stores.id = ? ";
            array_push($searchArray, $request->get('store_id'));
        }
        $sql .= " GROUP BY transaction_details.id ";
        if ($request->get('amount')) {
            $sql .= " HAVING (SUM(modified_transaction.amount+modified_transaction.tip_amount) = ?) OR (SUM(transaction_details.amount+transaction_details.tip_amount) = ?)";
            array_push($searchArray, $request->get('amount'), $request->get('amount'));
        }
        $sql .= " ORDER BY transaction_details.transaction_time DESC";
        $transactions = DB::select($sql, $searchArray);

        $data = array();
        if (!empty($transactions)) {
            // Creating array to show the values in frontend
            foreach ($transactions as $transaction) {
                $ecommerce_status_code = $transaction->m_code ? $transaction->m_code : $transaction->code;

                $nestedData['transaction_number'] = $transaction->transaction_number;
                $nestedData['consumer_name'] = $transaction->first_name . ' ' . $transaction->middle_name . ' ' . $transaction->last_name;
                $nestedData['merchant_name'] = $transaction->merchant_name;
                $nestedData['store_name'] = $transaction->retailer;
                $nestedData['terminal_name'] = $transaction->terminal_name;
                $nestedData['edit'] = $transaction->id;
                $nestedData['amount'] = number_format($transaction->amount - $transaction->delivery_fee, 2);
                $nestedData['tip_amount'] = number_format($transaction->tip_amount, 2);
                $nestedData['delivery_fee'] = number_format($transaction->delivery_fee, 2);
                $nestedData['transaction_time'] = date('m-d-Y H:i:s', strtotime($transaction->local_transaction_time)) . ' <br/> (' . $transaction->timezone_name . ')';

                $nestedData['status'] = $transaction->status;

                $scheduled_posting_date = $transaction->m_scheduled_posting_date ? $transaction->m_scheduled_posting_date : $transaction->scheduled_posting_date;
                $scheduled_posting_time = $transaction->m_scheduled_posting_time ? $transaction->m_scheduled_posting_time : $transaction->scheduled_posting_time;
                // when transaction need merchant admin approval then show Pending Store's Approval message
                if (!$scheduled_posting_date && $transaction->status == PENDING_STATUS_NAME) {
                    $nestedData['status'] = PENDING_STORE_APPROVAL;
                }

                $nestedData['is_v1'] = $transaction->is_v1;
                $nestedData['user_status'] = StatusMaster::where('id', $transaction->user_status)->first();

                if ($nestedData['status'] == $declined_status->status) {
                    // if consumer declined the transaction then no need to show cancel button
                    $nestedData['cancelable'] = 0;
                } else {
                    if ((!in_array($ecommerce_status_code, [PENDING, VOIDED]) && $transaction->change_request == 0) || $ecommerce_status_code == EXPIRED) {
                        $nestedData['cancelable'] = 0;
                    } else {
                        if ($transaction->is_ecommerce == 1) {
                            $local_transaction_time = $scheduled_posting_time;
                            $is_admin_driven = 1;
                        } else {
                            $local_transaction_time = $transaction->transaction_time;
                            $is_admin_driven = 0;
                        }
                        $nestedData['cancelable'] = checkTransactionEligibleForCancellation($local_transaction_time, $is_admin_driven, $scheduled_posting_date);
                    }
                }
                $data[] = $nestedData;
            }
        }

        $message = trans('message.void_transaction_fetch_success');
        return renderResponse(SUCCESS, $message, $data); // Returning response
    }

    /**
     * getVoidTransactionExport
     * This is the export function for Consumer Transaction Report for Void
     * @param  mixed $request
     * @return void
     */
    public function getVoidTransactionExport(Request $request)
    {
        return Excel::download(new ConsumerVoidTransactionExport($request), 'consumer_void_transaction_' . date('m-d-Y H:i:s') . '.xlsx');
    }

    /**
     * getAllTransactionModificationReason
     * This function is used to search transactions modification reason
     * @param  mixed $request
     * @return void
     */
    public function getAllTransactionModificationReason(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction modification reason search started...");
        // Validating input request
        $this->validate($request, [
            'reason' => VALIDATION_REQUIRED_WITHOUT_ALL . ':showall',
            'showall' => VALIDATION_REQUIRED_WITHOUT_ALL . ':reason',
        ]);

        //Search with in transaction modification reason
        $reasons = $this->_getTransactionModificationReasonSearch($request);

        $message = trans('message.transaction_modification_reason_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction Modification Reason search started complete.");
        return renderResponse(SUCCESS, $message, $reasons);
    }

    /**
     * getTransactionModificationReasonSearch
     * This function is used to search transaction modification reason
     */

    private function _getTransactionModificationReasonSearch($request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction modification reason Search Started.");

        $search = $request['reason'];
        if ($request['showall'] == false) {
            $reasonQuesy = TransactionModificationReason::on(MYSQL_RO)->Where('reason', 'LIKE', "%{$search}%");
        } else {
            $reasonQuesy = TransactionModificationReason::on(MYSQL_RO);
        }
        $reasonQuesy->leftJoin('transaction_modification_history as tmh', function ($query) {
            $query->on('tmh.id', '=', DB::raw('(SELECT id FROM transaction_modification_history as ctmh WHERE ctmh.reason_id = transaction_modification_reason.id  LIMIT 1)'));
        });
        $reasons = $reasonQuesy->select('transaction_modification_reason.*', 'tmh.transaction_id')->orderBy('created_at', 'DESC')->get();
        $reasonsArr = [];
        if (!empty($reasons)) {
            foreach ($reasons as $reason) {
                if ($reason->need_approval == 1) {
                    $approval = 'Yes';
                } else {
                    $approval = 'No';
                }

                $data = [];
                $data['edit'] = $reason->id;
                $data['reason'] = $reason->reason;
                $data['reason_type'] = $reason->reason_type;
                $data['is_deletable'] = $reason->transaction_id ? 0 : 1;
                $data['need_approval'] = $approval;
                $data['created_at'] = date('m-d-Y h:i A', strtotime($reason->created_at));
                $data['updated_at'] = date('m-d-Y h:i A', strtotime($reason->updated_at));

                array_push($reasonsArr, $data);
            }
        } else {
            $reasonsArr = [];
        }

        return $reasonsArr;
    }

    /**
     * addTransactionModificationReason
     * this function is used to add transaction modification reason
     */
    public function addTransactionModificationReason(Request $request)
    {
        // Validating input request
        $this->validate($request, [
            'reason' => [VALIDATION_REQUIRED, 'unique:transaction_modification_reason,reason'],
            'reason_type' => VALIDATION_REQUIRED,
            'need_approval' => VALIDATION_REQUIRED,
        ]);
        DB::beginTransaction();
        $checkreason = TransactionModificationReason::on(MYSQL_RO)->where('reason', $request['reason'])->first();
        if (empty($checkreason)) {
            try {
                $reason = new TransactionModificationReason();
                $reason->reason = $request['reason'];
                $reason->reason_type = $request['reason_type'];
                $reason->need_approval = $request['need_approval'];
                $reason->save();
                DB::commit();
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "New transaction modification reason added.");
                $message = trans('message.transaction_modification_reason_add_success');
                return renderResponse(SUCCESS, $message, null);
            } catch (\Exception $e) {
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured during add transaction modification reason.");
            }
        }
    }
    /**
     * editTransactionModificationReason
     * this function is used to edit transaction modification reason
     */
    public function editTransactionModificationReason(Request $request)
    {
        // Validating input request
        $this->validate($request, [
            'editId' => VALIDATION_REQUIRED,
            'reason' => [VALIDATION_REQUIRED, 'unique:transaction_modification_reason,reason,' . $request['editId'] . ',id,reason_type,' . $request['reason_type']],
            'reason_type' => VALIDATION_REQUIRED,
            'need_approval' => VALIDATION_REQUIRED,
        ]);
        DB::beginTransaction();
        $reason = TransactionModificationReason::on(MYSQL_RO)->where('id', $request['editId'])->first();
        if (!empty($reason)) {
            try {
                $reason->reason = $request['reason'];
                $reason->reason_type = $request['reason_type'];
                $reason->need_approval = $request['need_approval'];
                $reason->save();
                DB::commit();
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction modification reason updated.");
                $message = trans('message.transaction_modification_reason_update_success');
                return renderResponse(SUCCESS, $message, null);
            } catch (\Exception $e) {
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured during transaction modification reason update.");
            }
        }
    }

    /**
     * deleteTransactionModificationReason
     * Delete transaction modification reason
     * @param  mixed $request
     * @return void
     */
    public function deleteTransactionModificationReason(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Delete transaction modification reason started...");
        // Validating input request
        $this->validate($request, [
            'id' => VALIDATION_REQUIRED,
        ]);

        // Update is_delete flag
        $reason = TransactionModificationReason::where('id', $request['id'])->first();
        $transactionModificationHistory = TransactionModificationHistory::where('reason_id', $request['id'])->first();
        if (!$transactionModificationHistory) {
            $reason->delete();
        }

        $message = trans('message.delete_transaction_modification_reason_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction modification reason deleted.");
        return renderResponse(SUCCESS, $message, null);
    }

    /**
     * getAllTransactionModificationCustomReason
     * This function is used to search transactions modification custom reason
     * @param  mixed $request
     * @return void
     */
    public function getAllTransactionModificationCustomReason(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction modification reason search started...");
        // Validating input request
        $this->validate($request, [
            'reason' => VALIDATION_REQUIRED_WITHOUT_ALL . ':showall',
            'showall' => VALIDATION_REQUIRED_WITHOUT_ALL . ':reason',
        ]);

        //Search with in transaction modification reason
        $reasons = $this->_getTransactionModificationCustomReasonSearch($request);

        $message = trans('message.transaction_modification_custom_reason_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction Modification Custom Reason search started complete.");
        return renderResponse(SUCCESS, $message, $reasons);
    }

    /**
     * getTransactionModificationCustomReasonSearch
     * This function is used to search transaction modification custom reason
     */

    private function _getTransactionModificationCustomReasonSearch($request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction modification custom reason Search Started.");

        $search = $request['reason'];
        if ($request['showall'] == false) {
            $reasons = TransactionModificationCustomReason::on(MYSQL_RO)->Where('reason', 'LIKE', "%{$search}%")->orderBy('created_at', 'DESC')->get();
        } else {
            $reasons = TransactionModificationCustomReason::on(MYSQL_RO)->orderBy('created_at', 'DESC')->get();
        }

        $reasonsArr = [];
        if (!empty($reasons)) {
            foreach ($reasons as $reason) {
                $user = User::find($reason['added_by']);
                $data = [];
                $data['edit'] = $reason->id;
                $data['reason'] = $reason->reason;
                $data['added_by'] = $user ? $user->contact_person_email : '';
                $data['created_at'] = date('m-d-Y h:i A', strtotime($reason->created_at));
                $data['updated_at'] = date('m-d-Y h:i A', strtotime($reason->updated_at));

                array_push($reasonsArr, $data);
            }
        } else {
            $reasonsArr = [];
        }

        return $reasonsArr;
    }

    /**
     * deleteTransactionModificationCustomReason
     * Delete transaction modification custom reason
     * @param  mixed $request
     * @return void
     */
    public function deleteTransactionModificationCustomReason(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Delete transaction modification custom reason started...");
        // Validating input request
        $this->validate($request, [
            'id' => VALIDATION_REQUIRED,
        ]);

        // Update is_delete flag
        $reason = TransactionModificationCustomReason::where('id', $request['id'])->first();
        $reason->delete();

        $message = trans('message.delete_transaction_modification_custom_reason_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction modification custom reason deleted.");
        return renderResponse(SUCCESS, $message, null);
    }

    public function makeReturnRepayment(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Return transaction repayment started...");
        // Validating input request
        $this->validate($request, [
            'id' => VALIDATION_REQUIRED,
            'bank_account_id' => VALIDATION_REQUIRED,
        ]);
        $transaction = TransactionDetails::where("id", $request->get('id'))->first();
        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer giving permission to re pay transaction with id: " . $request->get('id'));
        $bank_account = UserBankAccountInfo::on(MYSQL_RO)
            ->leftJoin('user_bank_account_info as ubai1', 'user_bank_account_info.id', '=', 'ubai1.ref_no')
            ->selectRaw("user_bank_account_info.id, IF(ubai1.id IS NOT NULL, ubai1.account_no, user_bank_account_info.account_no) account_no")
            ->where(["user_bank_account_info.id" => $request->get('bank_account_id')])
            ->first();

        try {
            // update the approve column
            $transaction->represent_block = 1;
            $transaction->approved_to_represent = 1;
            $transaction->consumer_approval = 1;
            $transaction->return_from_primary_account = $transaction->account_id != $bank_account->id ? 1 : 0;
            $transaction->transaction_represented = $bank_account->id;
            $transaction->save();
            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Admin provided permission to represent return transaction for amount(without tip) : " . $transaction->amount);
            $message = trans('message.repayment_return_success');
            //return the last four digit of bank account details with which the return will be represented
            $account_no = substr($bank_account->account_no, -4);
            return renderResponse(SUCCESS, $message, $account_no);
        } catch (\Exception $e) {
            Log::channel('return-transaction')->error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured while approving transaction representment.", [EXCEPTION => $e]);
            $message = trans('message.repayment_return_error');
            return renderResponse(FAIL, $message, null); // Exception Returned
        }
    }
}
