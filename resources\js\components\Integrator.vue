<template>
  <div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <b-button
                variant="outline-success"
                style="margin-top: -48px"
                @click="openModal('add')"
              >
                <i class="fas fa-user-plus"></i> Add Integrator
              </b-button>
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Integrator</h3>
                  <b-button
                  class="btn-danger export-api-btn"
                  @click="reloadDatatable"
                  v-if="showReloadBtn"
                  >
                    <i class="fas fa-redo"></i> Reload
                  </b-button>
                </div>
                <!-- /.card-header -->
                <div class="card-body">
                  <table
                    id="adminUsersTable"
                    class="table"
                    style="width: 100%; white-space: normal"
                  >
                    <thead>
                      <tr>
                        <th>Ecommerce Integrator Name</th>
                        <th>integrator Id</th>
                        <th>Location Type</th>
                        <th>Allow Ecommerce Transaction</th>
                        <th>Consumer Auth</th>
                        <th>Created At</th>
                        <th>Action</th>
                      </tr>
                    </thead>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
    <!-- CP Modal Start -->
    <b-modal
      id="user-modal"
      ref="modal"
      :header-text-variant="headerTextVariant"
      :title="modalTitle"
      @show="resetModal"
      @hidden="resetModal"
      ok-title="Save"
      ok-variant="success"
      cancel-variant="outline-secondary"
      @ok="handleOk"
      :no-close-on-esc="true"
      :no-close-on-backdrop="true"
    >
      <form ref="form" @submit.stop.prevent="save" class="needs-validation">
        <div class="row">
          <div class="col-md-12">
            <label for="location_type">
              Location Type
              <span class="red">*</span>
            </label>
            <select
              class="form-control"
              id="location_type"
              name="location_type"
              v-model="userModel.location_type"
              v-validate="'required'"
          
            >
              <option
                v-for="(location, index) in locationTypeArray"
                :key="index"
                :value="location.location_type"
              >
                {{ location.location_type }}
              </option>
            </select>
            <span v-show="errors.has('location_type')" class="text-danger">{{
              errors.first("location_type")
            }}</span>
          </div>
        </div>

        <div class="row">
          <div class="col-md-8">
            <label for="ecommerce_integrator_name">
              Ecommerce Integrator Name
              <span class="red">*</span>
            </label>
            <input
              id="ecommerce_integrator_name"
              name="ecommerce_integrator_name"
              v-validate="'required|alpha'"
              type="text"
              v-model="userModel.ecommerce_integrator_name"
              class="form-control"
            />
            <span v-show="errors.has('ecommerce_integrator_name')" class="text-danger">{{
              errors.first("ecommerce_integrator_name")
            }}</span>
          </div>

        </div>

 <br />
        <h4>Ecommerce Setting</h4>

        <div class="row">
          <div class="col-md-6">
            <label for="allow_ecommerce_transaction">
               Ecommerce Transaction
              <span class="red">*</span>
            </label>

            <label class="switch"
              ><input
                type="checkbox"
                id="allow_ecommerce_transaction"
                name="allow_ecommerce_transaction"
                v-model="userModel.allow_ecommerce_transaction"
                true-value="1"
                false-value="0"
                class="enable-employee-login" /><span
                class="slider round"
              ></span
            ></label>
          </div>

          <div class="col-md-6">
            <label for="allow_ecommerce_transaction">
               Consumer Auth
              <span class="red">*</span>
            </label>

            <label class="switch"
              ><input
                type="checkbox"
                id="allow_consumer_auth"
                name="allow_consumer_auth"
                v-model="userModel.allow_consumer_auth"
                true-value="1"
                false-value="0"
                class="enable-employee-login" /><span
                class="slider round"
              ></span
            ></label>
          </div>
        </div>
        
      </form>
    </b-modal>
    <!-- CP Modal End -->
  </div>
</template>
<script>
import api from "@/api/user.js";
import { validationMixin } from "vuelidate";
import { required, minLength } from "vuelidate/lib/validators";
import commonConstants from "@/common/constant.js";
export default {
  mixins: [validationMixin],
  data() {
    return {
      modalTitle: "",
      statusList: [],
      headerTextVariant: "light",
      userModel: {},
      allUserModel: {},
      id: null,
      isDisabled: false,
      locationTypeArray: [{location_type:"Web"},
      {location_type:"Retail" } ],
      currentUser: localStorage.getItem("user")
        ? JSON.parse(localStorage.getItem("user"))
        : null,
      showReloadBtn:false,
      constants: commonConstants,
    };
  },
  created() {
    this.editIntegrator();
  },
  methods: {
    reloadDatatable(){
      var self = this;
      self.loadDT();
    },
    editIntegrator() {
      var self = this;
      $(document).on("click", ".editIntegrator", function (e) {
        console.log( $(e.currentTarget));
        self.id = $(e.currentTarget).attr("data-user-id");
        var integratorDetails = self.allUserModel.find((p) => p.id == self.id);
        self.modalTitle = "Edit Integrator";
        self.$bvModal.show("user-modal");
        self.userModel = integratorDetails;
        self.userModel.id = integratorDetails.id;
        self.isDisabled = true;
        //Fetch the status List
      });
    },
    openModal(type) {
      var self = this;
      self.modalTitle = "Add User";
      self.$bvModal.show("user-modal");
    },
    resetModal() {
      var self = this;
      self.userModel = {};
      self.isDisabled = false;
      self.id = null;
    },
    handleOk(bvModalEvt) {
      var self = this;
      // Prevent modal from closing
      bvModalEvt.preventDefault();
      // Trigger submit handler
      self.save();
    },
    save() {
      var self = this;
      // Exit when the form isn't valid
      this.$validator.validateAll().then((result) => {
        if (result) {
          //call to api to save the details
          if (!self.userModel.id) {
            // Add section
            api
              .addIntegrator(self.userModel)
              .then((response) => {
                if (response.code == 200) {
                  success(response.message);
                  $("#adminUsersTable").DataTable().ajax.reload(null, false);
                  self.$bvModal.hide("user-modal");
                  self.resetModal();
                } else {
                  error(response.message);
                }
              })
              .catch((err) => {
                error(err.response.data.message);
              });
          } else {
            // Edit Section
            api
              .editIntegrator(self.userModel)
              .then((response) => {
                if (response.code == 200) {
                  success(response.message);
                  self.id = null;
                  $("#adminUsersTable").DataTable().ajax.reload(null, false);
                  self.$bvModal.hide("user-modal");
                  self.store_ids = null;
                  self.resetModal();
                } else {
                  error(response.message);
                }
              })
              .catch((err) => {
                error(err.response.data.message);
              });
          }
        }
      });
    },

    loadDT: function () {
      var self = this;
      $("#adminUsersTable").DataTable({
        pagingType: "simple_numbers",
        processing: true,
        serverSide: true,
        destroy: true,
        columnDefs: [
          { orderable: false, targets: [5] },
          { className: "dt-left", targets: [0, 1, 2, 3, 4] },
          { className: "dt-center", targets: [5] },
        ],
        order: [[3, "desc"]],
        orderClasses: false,
        language: {
          processing:
            '<i class="fa fa-spinner fa-spin fa-3x fa-fw"></i><span class="sr-only">Loading...</span> ',
          emptyTable: "No Integrator Available.",
          search: "_INPUT_",
          searchPlaceholder: "Search records",
          oPaginate: {
            sNext: '<i class="fas fa-angle-double-right"></i>',
            sPrevious: '<i class="fas fa-angle-double-left"></i>',
          },
          sLengthMenu:
            "<label class='label_dropdown_dt'>Per page</label> _MENU_",
        },
        ajax: {
          headers: {
            Authorization: "Bearer " + localStorage.getItem("token"),
          },
          url: "/api/adminintegratorlist",
          type: "POST",
          data: { _token: "{{csrf_token()}}" },
          dataType: "json",
          dataSrc: function (result) {
            self.showReloadBtn = false;
            self.allUserModel = result.data;
            return self.allUserModel;
          },
          error: function(data){
            error(commonConstants.datatable_error);
            $('#adminUsersTable_processing').hide();
            self.showReloadBtn = true;
          }
        },
        columns: [
          { data: "ecommerce_integrator_name" },
          { data: "integrator_id" },
          { data: "location_type" },
          { data: "allow_ecommerce_transaction" },
          { data: "allow_consumer_auth" },
          { data: "created_at" },
          {
            render: function (data, type, full, meta) {
              console.log(full);
                return (
                  '<b-button data-user-id="' +
                  full.id +
                  '" class="editIntegrator custom-edit-btn" title="Edit Integrator" variant="outline-success"><i class="nav-icon fas fa-edit"></i></b-button>'
                );
            },
          },
        ],
      });

      $("#adminUsersTable").on("page.dt", function () {
        $("html, body").animate({ scrollTop: 0 }, "slow");
        $("th:first-child").focus();
      });

      //Search in the table only after 3 characters are typed
      // Call datatables, and return the API to the variable for use in our code
      // Binds datatables to all elements with a class of datatable
      var dtable = $("#adminUsersTable").dataTable().api();

      // Grab the datatables input box and alter how it is bound to events
      $(".dataTables_filter input")
      .unbind() // Unbind previous default bindings
      .bind("input", function(e) { // Bind our desired behavior
          // If the length is 3 or more characters, or the user pressed ENTER, search
          if(this.value.length >= 3 || e.keyCode == 13) {
              // Call the API search function
              dtable.search(this.value).draw();
          }
          // Ensure we clear the search if they backspace far enough
          if(this.value == "") {
              dtable.search("").draw();
          }
          return;
      });
    },

  },
  mounted() {
    var self = this;

    setTimeout(function () {
      self.loadDT();
    }, 1000);
    document.title = "CanPay - Integrator";
  },
};
</script>

