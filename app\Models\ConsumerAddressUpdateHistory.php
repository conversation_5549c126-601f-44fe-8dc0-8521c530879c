<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
class ConsumerAddressUpdateHistory extends Model
{
    protected $table = 'consumer_address_update_history';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'consumer_id',
        'street_address',
        'apt_number',
        'city',
        'state',
        'zipcode',
        'source',
    ];
    public $timestamps = true;
    public $incrementing = false;
    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
}
