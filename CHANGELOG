# Release v19.3.2 on 2025-07-30 

    ~ Enhancement: Mail sent to both the primary and secondary contact from the Petition listing page
# Release v19.3.1 on 2025-07-22 

    ~ Enhancement: Composer and NPM has been updated
# Release v19.3.0 on 2025-07-15 

    ~ Enahancement: Send rejection email to consumer when petition is marked as rejected
# Release v19.2.0 on 2025-07-03 

    ~ Enahancement: Enhance Admin Petitions Page with merchant link, email option, and contact info update
# Release v19.1.0 on 2025-06-24 

    ~ Enhancement: Implemented changes based on petition feedback for CanpayCrew
# Release v19.0.1 on 2025-06-05 

    ~ Fix: Secure the admin API by preventing SQL injection
# Release v19.0.0 on 2025-06-04 

    ~ Enhancement: Show merchant points report
    ~ Enhancement: Canpay Crew implementation
# Release v18.9.1 on 2025-06-02 

    ~ Enhancement: Composer has been updated
# Release v18.9.0 on 2025-05-28 

    ~ Fix: Loading issue when clicking "Validate Entry Hash" button
# Release v18.8.7 on 2025-05-26 

    ~ Enhancement: Composer and NPM has been updated
# Release v18.8.6 on 2025-05-20 

    ~ Enhancement: Composer and NPM has been updated
# Release v18.8.5 on 2025-05-13 

    ~ Enhancement: Composer has been updated
# Release v18.8.4 on 2025-05-05 

    ~ Fix: Removed the future date validation from all transactions, consumer transactions and pos void transactions
    ~ Enhancement: Composer has been updated
# Release v18.8.3 on 2025-04-28 

    ~ Enhancement: Composer and NPM has been updated
# Release v18.8.2 on 2025-04-21 

    ~ Enhancement: Composer has been updated
# Release v18.8.1 on 2025-04-16 

    ~ Fix: Convert consumer bank account linking from direct to manual.
# Release v18.8.0 on 2025-04-14 

    ~ Enhancement: Composer and NPM has been updated
    ~ Enhancement: Remote pay transaction using points.
# Release v18.7.2 on 2025-04-08 

    ~ Fix: Enforce 5-digit format for ZIP code validation
# Release v18.7.1 on 2025-04-07 

    ~ Enhancement: Composer and NPM has been updated
# Release v18.7.0 on 2025-04-02 

    ~ Enahancement: Composer hass been updated
    ~ Enhancement: Validate TAN-linked bank account and delink previous accounts if needed
# Release v18.6.1 on 2025-03-24 

    ~ Enhancement: Composer and NPM has been updated
# Release v18.6.0 on 2025-03-18 

    ~ Enhancement: Regular Updates option added to merge request template
    ~ Enhancement: Composer has been updated
    ~ Fix: Optimize User Filtering and Improve Reward Spin Report Query Performance
# Release v18.5.1 on 2025-03-17 

    ~ Fix: Check for duplicate email when admin changes CP email
# Release v18.5.0 on 2025-03-11 

    ~ Enhancement: Composer has been updated
# Release v18.4.0 on 2025-03-10 

    ~ Fix: Correct fee discount calculation for merchant-funded transactions.
# Release v18.3.1 on 2025-03-04 

    ~ Enhancement: Composer and NPM has been updated
# Release v18.3.0 on 2025-02-28 

    ~ Enhancement: Implement merchant health check listing API and frontend page
# Release v18.2.6 on 2025-02-27 

    ~ Fix: Eloquent Query for getAllActiveRemotePay
# Release v18.2.5 on 2025-02-26 

    ~ Enhancement: Update email <NAME_EMAIL>
# Release v18.2.4 on 2025-02-24 

    ~ Enhancement: Composer and NPM has been updated
# Release v18.2.3 on 2025-02-19 

    ~ Fix: Null Fix for Reward Wheel Form for Header Sub Ttile and Button Text
# Release v18.2.2 on 2025-02-18 

    ~ Fix: Corrected cancellation window for remote pay transactions
# Release v18.2.1 on 2025-02-17 

    ~ Enhancement: Composer and NPM has been updated
# Release v18.2.0 on 2025-02-11 

    ~ Enhancement: Display the count of Lite and Lite-to-Standard users on the dashboard on a daily and monthly basis.
    ~ Fix: Ensure email uniqueness when admin updates Corporate Parent email.
# Release v18.1.1 on 2025-02-10 

    ~ Enhancement: Composer and NPM has been updated
# Release v18.1.0 on 2025-02-06 

    ~ Enhancement: Merchant Points Program Report
# Release v18.0.1 on 2025-02-03 

    ~ Enhancement: Composer and NPM has been updated
# Release v18.0.0 on 2025-01-28 

    ~ Enhancement: Added changed for delivery fees in registered merchant
# Release v17.0.2 on 2025-01-27 

    ~ Enhancement: Composer has been updated
# Release v17.0.1 on 2025-01-23 

    ~ Fix: Email copy functionality not working in the alert dashboard.
# Release v17.0.0 on 2025-01-22 

    ~ Enhancement: A new user type, 'Lite User,' has been introduced, enabling payments using brand points.
# Release v16.2.2 on 2025-01-20 

    ~ Enhancement: Composer and NPM has been updated
# Release v16.2.1 on 2025-01-14 

    ~ Enhancement: Composer and NPM has been updated
# Release v16.2.0 on 2025-01-08 

    ~ Enhancement: Centrailizing generate and export for settlement report
# Release v16.1.0 on 2025-01-07 

    ~ Enhancement: Clone Reward Wheel
# Release v16.0.3 on 2025-01-06 

    ~ Enhancement: Composer and NPM has been updated
# Release v16.0.2 on 2025-01-02 

    ~ Fix: Corrected date comparison logic in fetchAchReturnReport method
# Release v16.0.1 on 2024-12-27 

    ~ Fix: Removed default 'Select Merchant Type' filter from Store API Keys page
# Release v16.0.0 on 2024-12-26 

    ~ Enhancement: Learn Brand Implementaion
# Release v15.15.0 on 2024-12-18 

    ~ Fix: resolve email delivery issues with Symfony Mailer 7.2 and SendGrid and AWS-SES
# Release v15.14.1 on 2024-12-17 

    ~ Enhancement: Composer has been updated
# Release v15.14.0 on 2024-12-16 

    ~ Enhancement: Added export all stores
# Release v15.13.2 on 2024-12-10 

    ~ Enhancement: Composer and NPM has been updated
# Release v15.13.1 on 2024-12-04 

    ~ Fix: Resolved issue where all transactions were not appearing in the settlement report
# Release v15.13.0 on 2024-12-03 

    ~ Fix: V1 users see incorrect purchase power when custom purchase power is enabled.
# Release v15.12.0 on 2024-12-02 

    ~ Enhancement: Composer has been updated
    ~ Enhancement: Report modified as per merchant points used
# Release v15.11.0 on 2024-11-26 

    ~ Enahancement: MX Account Owner Infor retry api called from Alert dashboard
# Release v15.10.0 on 2024-11-25 

    ~ Enhancement: Add retry option for MX account owner info poll after 2-minute timeout
# Release v15.9.0 on 2024-11-18 

    ~ Enhancement: Composer and NPM has been updated
    ~ Fix: Replaced old favicon with a new one
# Release v15.8.0 on 2024-11-11 

    ~ Enhancement: Search forgiven returns
# Release v15.7.6 on 2024-11-11 

    ~ Enhancement: Composer and NPM has been updated
# Release v15.7.5 on 2024-11-04 

    ~ Enhancement: Composer and NPM has been updated
# Release v15.7.4 on 2024-11-01 

    ~ Fix: Update microbilt override flag for blocked consumer due to microbilt decision code A
# Release v15.7.3 on 2024-11-01 

    ~ Fix: Override microbilt decision while consumer blocked with microbilt decision code A
# Release v15.7.2 on 2024-11-01 

    ~ Fix: type added to filter out the ACH routing number in Storewise Monthly Sales Report
# Release v15.7.1 on 2024-10-29 

    ~ Enhancement: Composer has been updated
# Release v15.7.0 on 2024-10-22 

    ~ Fix: Improved SQL execution speed for faster void transaction processing
# Release v15.6.2 on 2024-10-21 

    ~ Enhancement: Composer and NPM has been updated
# Release v15.6.1 on 2024-10-17 

    ~ Fix: Gear icon click action in 'Bank Link Failure' section under 'Consumer'
# Release v15.6.0 on 2024-10-16 

    ~ Enhancement:  Show discounted amount in the transaction settlement report.
    ~ Enhancement: Default Cashback Program
    ~ Fix: Refactor Reward Wheel Report Query for Improved Flexibility and Performance
    ~ Fix: Akoya row URL encoded
# Release v15.5.1 on 2024-10-15 

    ~ Fix: MX fix popup incorrectly appearing after manual link completion
# Release v15.5.0 on 2024-10-07 

    ~ Enhancement: Composer and NPM has been updated
    ~ Enhancement: Restrict account owner call in MX is member status found non user actionable
# Release v15.4.1 on 2024-10-01 

    ~ Enhancement: Composer has been updated
# Release v15.4.0 on 2024-09-30 

    ~ Enhancement: Added Reward Wheel Configuration
# Release v15.3.0 on 2024-09-23 

    ~ Fix: Alert Dashbard changes
    ~ Enhancement: Added Mx identification History Page
    ~ Enhancement: Composer and NPM has been updated
# Release v15.2.0 on 2024-09-17 

    ~ Enhancement: Composer has been updated
    Revert "~ Enhancement: Added Mx identification History Page"
# Release v15.1.0 on 2024-09-16 

    ~ Enhancement: Added Mx identification History Page
# Release v15.0.1 on 2024-09-11 

    ~ Enhancment: Added CanPay Loader
    ~ Fix: Points program report hide.
# Release v15.0.0 on 2024-09-11 

    ~ Enhancement: Update store timings during import merchants
    ~ Enhancement: Points back program module integrated
# Release v14.3.2 on 2024-09-10 

    ~ Enhancement: Composer and NPM has been updated
# Release v14.3.1 on 2024-09-03 

    ~ Enhancement: Composer has been updated
# Release v14.3.0 on 2024-08-28 

    ~ Enhancement: Composer has been updated
    ~ Fix: Check if session validity expired then the consumer shouldn't be in manual review
    ~ Fix: Adding condition for secret fetch only in Production and staging environment
# Release v14.2.1 on 2024-08-27 

    Revert "~ Fix: Adding condition for secret fetch only in Production and staging environment"
# Release v14.2.0 on 2024-08-27 

    ~ Enhancement: Open a popup in the Admin Panel if the mx account is in problematic status
# Release v14.1.3 on 2024-08-27 

    ~ Fix: Adding condition for secret fetch only in Production and staging environment
# Release v14.1.2 on 2024-08-26 

    ~ Fix: Original Case Preservation for keys
# Release v14.1.1 on 2024-08-26 

    ~ Fix: Code changed to accomodate true, false in env file
# Release v14.1.0 on 2024-08-26 

    ~ Enhancement: Add command to fetch and cache AWS Secrets Manager data
# Release v14.0.1 on 2024-08-20 

    ~ Enhancement: Composer has been updated
# Release v14.0.0 on 2024-08-19 

    ~ Enhancement: Synchronous mx process
# Release v13.2.0 on 2024-08-14 

    ~ Fix: Disable MX Identify API calls
# Release v13.1.4 on 2024-08-13 

    ~ Enhancement: Composer and NPM has been updated
# Release v13.1.3 on 2024-08-13 

    ~ Fix: Reset button functionality in consumers search page
# Release v13.1.2 on 2024-08-12 

    ~ Enhancement: Consumer search and global search parameter added for name(first_name, middle_name, last_name)
# Release v13.1.1 on 2024-08-02 

    ~ Fix: Fix Update on RO db
# Release v13.1.0 on 2024-07-30 

    ~ Enhancement: Composer and NPM has been updated
# Release v13.0.0 on 2024-07-23 

    ~ Enhancement: Added the new canpay loader for refresh balance in consumer page
# Release v12.11.2 on 2024-07-22 

    ~ Enhancement: Composer and NPM has been updated
# Release v12.11.1 on 2024-07-15 

    ~ Enhancement: Composer and NPM has been updated
# Release v12.11.0 on 2024-07-08 

    ~ Enhancement: Composer has been updated
    ~ Fix: Send test email for ACH daily transactions details
# Release v12.10.1 on 2024-07-01 

    ~ Enhancement: Composer has been updated
# Release v12.10.0 on 2024-06-26 

    ~ Fix: File upload time issue fixed for ACH files
    ~ Fix: Optimized the return transaction details for paid amount
# Release v12.9.6 on 2024-06-25 

    ~ Enhancement: Composer and NPM has been updated
# Release v12.9.5 on 2024-06-24 

    ~ Fix: Negative points issue fixed
    ~ Enhancement: Changed the display of date for export in enrollment report
# Release v12.9.4 on 2024-06-21 

    ~ Enhancement: Columns added for better visibity about ACH file upload in Report
# Release v12.9.3 on 2024-06-20 

    ~ Fix: Enable PPrule change for algo based users as well
# Release v12.9.2 on 2024-06-20 

    ~ Fix: Warning for duplicate uploads of ACH file to SFTP server
# Release v12.9.1 on 2024-06-19 

    ~ Fix: Remove 'Enable Purchase Power Lower Limit' option from admin settings
# Release v12.9.0 on 2024-06-18 

    ~ Fix: Skip iteration if old value is equal to the new value
    ~ Enhancement: Composer and NPM has been updated
# Release v12.8.2 on 2024-06-17 

    ~ Fix: PP rule change option enabled in the Admin Panel
# Release v12.8.1 on 2024-06-17 

    ~ Fix: Null or 0 handled in the history of Consumer Algo responses in the Admin panel.
# Release v12.8.0 on 2024-06-17 

    ~ Enhancement: Display the history of Consumer Algo responses in the Admin panel.
    ~ Enhancment: Consumer balance fetch centralized from API repo
# Release v12.7.0 on 2024-06-11 

    ~ Enhancement: Composer and NPM has been updated
    ~ Enhancement: Add bank modal added in bank manage
# Release v12.6.2 on 2024-06-04 

    ~ Fix: The total v2 pending amount will be deducted from the weekly limit.
# Release v12.6.1 on 2024-06-04 

    ~ Fix: The total pending amount will be deducted from the weekly limit.
# Release v12.6.0 on 2024-06-04 

    ~ Enhancement: Return Transaction page bank link shown as direct/Manual instead of Finicity/Manual
    ~ Enhancement: Composer and NPM has been updated
# Release v12.5.0 on 2024-05-30 

    ~ Enhancement: Bank link survey listing page
# Release v12.4.0 on 2024-05-29 

    ~ Enhancement: Added registration failure page
# Release v12.3.0 on 2024-05-29 

    ~ Enhancement: Composer and NPM has been updated
    ~ Enhancement: Added MR template
# Release v12.2.0 on 2024-05-27 

    ~ Fix: Bank link type based on account ID.
# Release v12.1.5 on 2024-05-24 

    ~ Enhancement: Added risk score for pending, approve, archived and suspected fraud under manual identity review
# Release v12.1.4 on 2024-05-17 

    ~ Fix: Fetch Return Repayment Account List
# Release v12.1.3 on 2024-05-16 

    ~ Fix: Show delink status bank accounts during return repayment
# Release v12.1.2 on 2024-05-16 

    ~ Fix: Show weekly limit if it is disabled, greater than zero, and less than the purchase power for a direct link user.
# Release v12.1.1 on 2024-05-15 

    ~ Fix: Microbilt override for suspended consumers due to consecutive 3rd attempt on restricted banking
# Release v12.1.0 on 2024-05-14 

    ~ Enhancement: Added consumer bank posting amount and reward point amount column in consumer transaction
# Release v12.0.0 on 2024-05-14 

    ~ Enhancement: Laravel upgrded to 11
# Release v11.6.3 on 2024-05-13 

    ~ Fix: Change condition to show return repayment option
# Release v11.6.2 on 2024-05-13 

    ~ Fix: Purchase power lower limit introduced for new rule
    ~ Enhancement: Changes in consumer to display daily limit
    ~ Fix: Effective purchase power for direct link user
# Release v11.6.1 on 2024-05-11 

    ~ Fix: Show Delink bank accounts while converting consumer to direct to manual
# Release v11.6.0 on 2024-05-09 

    ~ Fix: label for blacklisted account
    ~ check acc blacklisted
    ~ show all banks
    ~ Enhancement: Direct Link to Manual Link Conversion and Return Repayment From Return Transaction Report
# Release v11.5.0 on 2024-05-08 

    ~ Enhancement: Added purchase power rule change icon in consumer search and implemented kill switch
    Revert "~ Enhancement: Direct Link to Manual Link Conversion and Return Repayment From Return Transaction Report"
    Revert "~ Enhancement: Added purchase power rule change icon in consumer search and implemented kill switch"
# Release v11.4.0 on 2024-05-08 

    ~ Enhancement: Added purchase power rule change icon in consumer search and implemented kill switch
# Release v11.2.0 on 2024-05-03 

    ~ Enhancement: Override Microbilt Check for the specific account details
# Release v11.1.0 on 2024-05-02 

    ~ Enhancement: Fetch the latest account from MX
# Release v11.0.8 on 2024-04-29 

    ~ Enhancement: Composer has been updated
# Release v11.0.7 on 2024-04-27 

    ~ Fix: Effective purchase power in enrollment report
# Release v11.0.6 on 2024-04-27 

    ~ Fix: Null checked for X_average_score_max in enrollment report
# Release v11.0.5 on 2024-04-25 

    ~ Fix: Disable Weekly Spending Limit
# Release v11.0.4 on 2024-04-24 

    ~ Fix: Prevent weekly limit update if admin disabled automatic weekly spending limit.
# Release v11.0.3 on 2024-04-22 

    ~ Enhancement: Akoya interaction id has been stored in database and akoya-interaction-type has been sent user for all requests.
# Release v11.0.2 on 2024-04-22 

    ~ Enhancement: Composer and NPM has been updated
# Release v11.0.1 on 2024-04-18 

    ~ Fix: Queries optimized for void transaction.
# Release v11.0.0 on 2024-04-14 

    ~ Enhancement: Sposor implementation
# Release v10.8.3 on 2024-04-09 

    ~ Fix: Pending amount calculation issue due to consumer_bank_posting_amount being null solved
# Release v10.8.2 on 2024-04-08 

    ~ Enhancement: Composer and NPM has been updated
# Release v10.8.1 on 2024-04-06 

    ~ Fix: When a Transaction is voided with points then  that should be treated as generic points
# Release v10.8.0 on 2024-04-03 

    ~ Fix: Remove space from link to another page
# Release v10.7.0 on 2024-04-01 

    ~ Fix: ACH File validation changhe to previous date by default
    ~ Enahancement: API added to check if the user is challenged or not
    ~ Enhancement: Akoya refresh token according institution ID
# Release v10.6.3 on 2024-04-01 

    ~ Enhancement: Composer has been updated
# Release v10.6.2 on 2024-03-26 

    ~ Enhancement: Composer has been updated
# Release v10.6.1 on 2024-03-18 

    ~ Enhancement: Composer and NPM has been updated
# Release v10.6.0 on 2024-03-15 

    ~ Fix: UI changes for alert dashboard modal.
    ~ Fix: Destroy Click Events In Stores Page
# Release v10.5.1 on 2024-03-11 

    ~ Enhancement: Composer has been updated
# Release v10.5.0 on 2024-03-05 

    ~ Enhancement: Financial institution new DB architecture
    ~ Enhancement: Show effective purchase power
    ~ Enhancement: Composer and NPM has been updated
    ~ Enhancement: Readme preapred as per proposed format
# Release v10.4.0 on 2024-02-27 

    ~ Enhancement : Added identity validation modal
# Release v10.3.2 on 2024-02-26 

    ~ Enhancement: Composer and NPM has been updated
# Release v10.3.1 on 2024-02-19 

    ~ Fix: Fix the account owner api call for Akoya Capital one
# Release v10.3.0 on 2024-02-19 

    ~ Enhancement: Integrate the Account Owner API call for Akoya for Capital One accounts
# Release v10.2.10 on 2024-02-19 

    ~ Enhancement: Composer has been updated
# Release v10.2.9 on 2024-02-15 

    ~ Fix: Error message change for no accounts found from account owner response
# Release v10.2.8 on 2024-02-15 

    ~ Fix: Call the iidentify member api if response received from account owner api is zero for MX
# Release v10.2.7 on 2024-02-14 

    ~ Fix: Fix the account owner api call to insert owner name
# Release v10.2.6 on 2024-02-13 

    ~ Fix: Columns rearranged in the table and Yes No shown instead of 1, 0
# Release v10.2.5 on 2024-02-13 

    ~ Enhancement: Created mx institution page canpay admin.
# Release v10.2.4 on 2024-02-13 

    ~ Fix: Remove condition for inactive stores fetch in the settlement report
# Release v10.2.3 on 2024-02-12 

    ~ Fix: Import MX Institution Code
# Release v10.2.2 on 2024-02-12 

    ~ Enhancement: Composer and NPM has been updated
# Release v10.2.1 on 2024-02-09 

    ~ Fix: Fix the account owners api for Akoya
# Release v10.2.0 on 2024-02-09 

    ~ Enhancement: Call the Account Owner info for MX banking solution
    ~ Fix: Consumer balance fetch details
# Release v10.1.0 on 2024-02-08 

    ~ Fix: Query optimized for reward wheel report
    ~ Enhancement : Manage bank input field added for akoya provider id and mx institution code.
# Release v10.0.5 on 2024-02-06 

    ~ Fix: The Bank account was delinked due to Akoya refresh token is invalid_request
# Release v10.0.4 on 2024-02-06 

    ~ Fix: Update error flag when token generated failed
    ~ Fix: Icon disabled
    ~ Fix: Akoya token store/update logic changed
    ~ Fix: The Bank account was delinked due to Akoya refresh token is invalid
# Release v10.0.3 on 2024-02-06 

    ~ Fix: Stores list fetched in the Admin Panel
# Release v10.0.2 on 2024-02-05 

    ~ Enhancement: Composer has been updated
# Release v10.0.1 on 2024-02-01 

    ~ Fix: Banking solution response id fixed for Akoya Client and Mx client
# Release v10.0.0 on 2024-02-01 

    ~ Enhancement: MX APIs integrated
    ~ Fix: Banking Solution Response store
# Release v9.2.0 on 2024-01-30 

    ~ Fix: Akoya refresh token update
    ~ Enhancement: Composer and NPM has been updated
# Release v9.1.0 on 2024-01-25 

    ~ Enhancement: Account owner info API created for Akoya
# Release v9.0.2 on 2024-01-23 

    ~ Fix: Balance fetch issue fixed for admin refresh balance for Akoya Banking Solution
# Release v9.0.1 on 2024-01-22 

    ~ Fix: Akoya balance fetch issue fixed
# Release v9.0.0 on 2024-01-22 

    ~ Enhancement: Akoya Implementation
# Release v8.9.2 on 2024-01-10 

    ~ Enhancement: Logo and Webicon updated for Akoya
# Release v8.9.1 on 2024-01-03 

    ~ Fix: Webicon for Akoya changed
# Release v8.9.0 on 2023-12-27 

    ~ Fix: Permission given for help desk user to see the consumers return transactions
    ~ Enhancement: Images added for Akoya
# Release v8.8.2 on 2023-12-11 

    ~ Enhancement: Composer has been updated
# Release v8.8.1 on 2023-12-04 

    ~ Enhancement: Composer has been updated
# Release v8.8.0 on 2023-11-28 

    ~ Enhancement: Reset return count limit, if the user status changed from blocked to active
# Release v8.7.1 on 2023-11-27 

    ~ Enhancement: Composer has been updated
# Release v8.7.0 on 2023-11-13 

    ~ Enhancement: Composer has been updated
# Release v8.6.2 on 2023-11-07 

    ~ Fix: Made the bank acheck field details non madatory in edit merchant.
# Release v8.6.1 on 2023-11-06 

    ~ Fix: Show admin-driven transactions in the dashboard
    ~ Enhancement: Composer and NPM has been updated
# Release v8.6.0 on 2023-11-01 

    ~ Enhancement: Ach Return Report
# Release v8.5.0 on 2023-10-31 

    ~ Enhancement: Report of Consumer Declined during microbilt bank validation
# Release v8.4.2 on 2023-10-30 

    ~ Fix: Sending test email alert to admin if consumer update bank with negative transactions
# Release v8.4.1 on 2023-10-26 

    ~ Enhancement: Separate menu created for ACH
# Release v8.4.0 on 2023-10-25 

    ~ Enhancement: Microbilt error bypass
# Release v8.3.0 on 2023-10-17 

    ~ Enhancement: Change the ACh file format for the ACh File downloading
# Release v8.2.3 on 2023-10-15 

    ~ Enhancement: Import history table filename column added
# Release v8.2.2 on 2023-10-12 

    ~ Fix: upload log history table refreshed after import success
# Release v8.2.1 on 2023-10-12 

    ~ Fix: Sample file replaced for Transaction Trace no. import
# Release v8.2.0 on 2023-10-10 

    ~ Fix: Merchant import time skip google API calls when timezone is present in excel
    ~ Enhancement: Artisan commnd created to test the google map key
# Release v8.1.2 on 2023-10-09 

    ~ Enhancement: Composer has been updated
# Release v8.1.1 on 2023-10-06 

    ~ Fix: Old transaction report
# Release v8.1.0 on 2023-10-06 

    ~ Fix: Old transaction report
    ~ Enahancement: Option added for reverse ach file and download reversed ach file
# Release v8.0.0 on 2023-10-05 

    ~ Enahncement:readme file updated
    ~ Enhancement: Void and Void Revoke functionality edited for ACH file
    ~ Enhancment: Option given in the Admin panle to download ACH and Excel file for a particular date
    ~ Enhancement: is_voided flag updated for void transaction and revoke void transaction
    ~ Enhancement: Option added for File Upload to SFTP Server
    ~ Enhancement: Transaction Report Modified
    ~ Enhancement: Enable new ACH process
    ~ Enhancement: ACH transaction report in canpay admin.
    ~ Enhancment: Option given in the Admin panle to download ACH and Excel file for a particular date
    ~ Enhancement: is_voided flag updated for void transaction and revoke void transaction
    ~ Enhancement: Option added for File Upload to SFTP Server
    ~ Enhancement: Transaction Report Modified
    ~ Enhancement: Enable new ACH process
    ~ Enhancement: Void and Void Revoke functionality edited for ACH file
    ~ Enhancment: Option given in the Admin panle to download ACH and Excel file for a particular date
    ~ Enhancement: is_voided flag updated for void transaction and revoke void transaction
    ~ Enhancement: Option added for File Upload to SFTP Server
    ~ Enhancement: Transaction Report Modified
    ~ Enhancement: Enable new ACH process
# Release v7.23.7 on 2023-09-25 

    ~ Enhancement: Composer and NPM has been updated
# Release v7.23.6 on 2023-09-18 

    ~ Enhancement: Composer and NPM has been updated
# Release v7.23.5 on 2023-09-11 

    ~ Enhancement: Composer and NPM has been updated
# Release v7.23.4 on 2023-09-04 

    ~ Enhancement: Composer and NPM has been updated
# Release v7.23.3 on 2023-08-21 

    ~ Fix: Show alert dashboard for helpdesk users
# Release v7.23.2 on 2023-08-21 

    ~ Enhancement: Composer has been updated
    ~ Enhancement: Admin can review users in the alert dashboard enrollment report section
# Release v7.23.1 on 2023-08-16 

    ~ Enhancement: Composer and NPM has been updated
    ~ Fix: Remote pay transaction report show expired transaction and change menu name
# Release v7.23.0 on 2023-08-07 

    ~ Enhancement: Composer and NPM has been updated
    ~ Enhancement: Listing, Add, Edit for manual bank link restricted routing number
# Release v7.22.0 on 2023-08-02 

    ~ Enhancement: Remote Pay Transaction Report
# Release v7.21.1 on 2023-07-25 

    ~ Fix: Reward Wheel Search
# Release v7.21.0 on 2023-07-24 

    ~ Enhancement: Wheel design template library added
    ~ Enhancement: Canpay Admin Users in one menu with pagination and filtering
    ~ Fix: Hide consumer name search filter from Account Owner Info page
    ~ Enhancement: Account owner info show in alert dashboard
    ~ Enhancement: Composer and NPM has been updated
    ~ Enhancement: Show Transaction Declined instead of General Exception
# Release v7.20.0 on 2023-07-20 

    ~ Enhancement: New Enrollment Report for Alert Dashboard
    ~ Enhancement: Search, assign and remove reward wheels for multiple stores
# Release v7.19.1 on 2023-07-18 

    ~ Fix: Test Mail For Suspected Fraud Transaction
# Release v7.19.0 on 2023-07-17 

    ~ Enhancement: Add field for lottery value change
    ~ Enhancement: Composer has been updated
# Release v7.18.0 on 2023-07-12 

    ~ Enhancement: Sending test email alert to admin if consumer enrolls from North Carolina
# Release v7.17.6 on 2023-07-10 

    ~ Enhancement: Composer has been updated
# Release v7.17.5 on 2023-07-05 

    ~ Fix: Non Admin Driven Modify Transacrion
    ~ Fix: Admin Driven Void Transacrion
# Release v7.17.4 on 2023-07-03 

    ~ Enhancement: Composer has been updated
# Release v7.17.3 on 2023-06-30 

    ~ Enhancement: Manage Account Owner Info menu show for Super Admin and Admin
# Release v7.17.2 on 2023-06-27 

    ~ Enhancement: Composer and NPM has been updated
# Release v7.17.1 on 2023-06-26 

    ~ Fix: Send Test Email
# Release v7.17.0 on 2023-06-21 

    ~ Enhancement: Expiration hours removed from Reward Wheel
# Release v7.16.2 on 2023-06-19 

    ~ Enhancement: Composer and NPM has been updated
# Release v7.16.1 on 2023-06-07 

    ~ Enhancement: Composer has been updated
# Release v7.16.0 on 2023-06-05 

    ~ Fix: Hide modification button for non-admin driven and accepted transaction
# Release v7.15.0 on 2023-05-26 

    ~ Enhancement: Dynamic Wheel Design form feature
# Release v7.14.0 on 2023-05-24 

    ~ Enhancement: Jackpot base value reset
# Release v7.13.2 on 2023-05-22 

    ~ Enhancement: Composer and NPM has been updated
# Release v7.13.1 on 2023-05-15 

    ~ Enhancement: Composer has been updated
# Release v7.13.0 on 2023-05-11 

    ~ Enhancement: Remotepay registration details shown in the dashboard
# Release v7.12.0 on 2023-05-09 

    ~ Fix: Finicity configuration added to ENV example
    ~ Fix: Delete consumer institution time delink consumer accounts associated with this institution
# Release v7.11.1 on 2023-05-08 

    ~ Enhancement: Build with version
    ~ Enhancement: Composer has been updated
    ~ Enhancement: Probability test report generation
# Release v7.11.0 on 2023-05-03 

    ~ Enhancemnet: Transaction cooldown time introduced to prevent duplicate transactions.
# Release v7.10.0 on 2023-05-01 

    ~ Enhancement: Composer and NPM has been updated
    ~ Fix: report Mismatch fixed for Reward Wheel Spin Report
    ~ Fix: Setting data wrong value assign isue fixed
# Release v7.9.1 on 2023-04-28 

    ~ Fix: Delete consumer institution if the consumer doesn't have pending transactions.
# Release v7.9.0 on 2023-04-27 

    ~ Enhancement: Delete consumer finicity institution
# Release v7.8.1 on 2023-04-26 

    ~ Enhancement: Return representment settings added for control in Admin Panel
# Release v7.8.0 on 2023-04-25 

    ~ Fix: Select integrator search
    ~ Fix: Category Code text changed and Merchant Profile Name column added
    ~ Fix: SQL commenter package removed
    ~ Enhancement: NPM has been updated
# Release v7.7.8 on 2023-04-24 

    ~ Enhancement: Add Column Earned Spin to differentiate between Free Spin and Earned Spin
# Release v7.7.7 on 2023-04-17 

    ~ Fix: CORS error fixed in Reward Wheel report.
# Release v7.7.6 on 2023-04-14 

    ~ Fix: Reward Wheel Report Fix done for Report egneration
# Release v7.7.5 on 2023-04-14 

    ~ Enhancement: Pagination implemented for Reward Wheel Spin report
# Release v7.7.4 on 2023-04-10 

    ~ Enhancement: Restriction added if Store is Used for Reward Wheel then cannot delete the Store from CP Edit
# Release v7.7.3 on 2023-04-10 

    ~ Enhancement: NPM has been updated
# Release v7.7.2 on 2023-04-06 

    ~ Fix: remove validation for store, state and CP
# Release v7.7.1 on 2023-04-05 

    ~ Enhancement: Add all select option to States and CP's
# Release v7.7.0 on 2023-04-04 

    ~ Enhancement: Spin data inserted in RW report
# Release v7.6.2 on 2023-04-03 

    ~ Enhancement: NPM has been updated
# Release v7.6.1 on 2023-03-29 

    ~ Fix: Wheel update description length error
# Release v7.6.0 on 2023-03-28 

    ~ Enhancement: Segment drag and drop ordering feature
    ~ Fix: Points reverted on Void Transaction
# Release v7.5.7 on 2023-03-27 

    Revert "~ Enhancement: Segment drag and drop ordering feature"
    ~ Enhancement: Composer has been updated
    ~ Enhancement: Segment drag and drop ordering feature
# Release v7.5.6 on 2023-03-22 

    ~ Fix: Repot generated based on EST
# Release v7.5.5 on 2023-03-20 

    ~ Enhancement: Add Transaction Amount Column for Reward Wheel Report
# Release v7.5.4 on 2023-03-20 

    ~ Enhancement: Composer and NPM has been updated
# Release v7.5.3 on 2023-03-18 

    ~ Fix: Reward Point and Amount added in reward report
# Release v7.5.2 on 2023-03-18 

    ~ Fix: Excel Download issue fixed
# Release v7.5.1 on 2023-03-17 

    ~ Fix: Winning Details added in Reward Spin Report
# Release v7.5.0 on 2023-03-17 

    ~ Enahancement: Reward wheel report table
# Release v7.4.0 on 2023-03-15 

    ~ Enhancement: Changes made for lottery after Void Transaction and Revoke Void Transaction
    ~ Fix: probability used value
    ~ Fix: mulitpler increase number of digits
    ~ Fix: Multiplier number length
# Release v7.3.1 on 2023-03-13 

    ~ Enhancement: NPM and Composer has been updated
# Release v7.3.0 on 2023-03-06 

    ~ Fix: Probability used text & Image upload size validation & Default image gallery
    ~ Fix: Add merchant key API data validation
    ~ Enhancement: Composer and NPM has been updated
# Release v7.2.6 on 2023-03-01 

    ~ Fix: Admin Revoke Void Transaction
# Release v7.2.5 on 2023-02-28 

    ~ Fix: Admin refresh balance call timezone change PST to MST
# Release v7.2.4 on 2023-02-27 

    ~ Fix: Custom validation error message for all fields and display used store error to modal
    ~ Fix: undefiend err
    ~ FIx: image selection
    ~ Fix: Display used store error message to modal
    ~ Enhancement: Composer and NPM has been updated
# Release v7.2.3 on 2023-02-23 

    ~ Fix: Destroy Click Events In Consumers Page
# Release v7.2.2 on 2023-02-22 

    ~ Fix: Default segment image selector
# Release v7.2.1 on 2023-02-21 

    ~ Fix: Number input and probability calculation issues
# Release v7.2.0 on 2023-02-16 

    ~ FIx: Client feedback changes on wheel creation form
# Release v7.1.0 on 2023-02-14 

    ~ Enhancement: View Consumer Balance Fetch Details
    ~ Enhancement: Admin enable bank link & return
# Release v7.0.0 on 2023-02-12 

    ~ Enhancement: Reward wheel API integration and all CURD functionality
    ~ Enhancement: Inviatation Module Implemented
    ~ Enhancement: REward wheel Failover implemented
# Release v6.3.5 on 2023-01-25 

    ~ Fix: Store listing according corporate parent
# Release v6.3.4 on 2023-01-24 

    ~ Fix: Consumer view balance
# Release v6.3.3 on 2023-01-23 

    ~ Enhancement: Composer and NPM has been updated
# Release v6.3.2 on 2023-01-16 

    ~ Enhancement: Composer has been updated
# Release v6.3.1 on 2023-01-09 

    ~ Enhancement: Composer has been updated
# Release v6.3.0 on 2023-01-03 

    ~ Enhancement: Composer and NPM has been updated
    ~ Enhancement: Refresh Customar Accounts API integrated while balance fetch from Admin
# Release v6.2.4 on 2023-01-02 

    ~ Fix: Manual bank list error fixed
# Release v6.2.3 on 2022-12-30 

    ~ Fix: Raw SQL converted to eloquent
# Release v6.2.2 on 2022-12-21 

    ~ Fix: Get ENV data via config
# Release v6.2.1 on 2022-12-20 

    ~ Enhancement: Composer and NPM has been updated
    ~ Fix: Raw SQL converted to Prepared statement
# Release v6.2.0 on 2022-12-13 

    ~ Enhancement: Option to delinked all bank accounts from Admin Panel
# Release v6.1.1 on 2022-12-06 

    ~ Enhancement: Composer and NPM has been updated
# Release v6.1.0 on 2022-12-05 

    ~ Enhancement: Storewise current month's fees for enabled corporate parent
# Release v6.0.1 on 2022-12-03 

    ~ FIx: Setup shell script modified.
# Release v6.0.0 on 2022-11-29 

    ~ Enhancement: SQL commenter added for all queries
    ~ Enhancement: Laravel upgraded to version 9
# Release v5.2.8 on 2022-11-21 

    ~ Enhancement: Remove helpdesk user access from Suspected Fraud list
# Release v5.2.7 on 2022-11-15 

    ~ Enhancement: Composer and NPM has been updated
# Release v5.2.6 on 2022-11-10 

    ~ Enhancement: Fetch void transaction list from main db
# Release v5.2.5 on 2022-11-09 

    ~ Enhancement: Change the Mysql_ro2 connection to Mysql_ro
# Release v5.2.4 on 2022-11-07 

    ~ Enhancement: Composer and NPM  has been updated
# Release v5.2.3 on 2022-11-03 

    ~ Enahancement: Text chnged to Canpay Internal Version
# Release v5.2.2 on 2022-11-03 

    ~ Enhancement: Text change to Canpay Version Name
# Release v5.2.1 on 2022-10-19 

    ~ Fix: V1 Consumer details Edit menu added
# Release v5.2.0 on 2022-10-18 

    ~ Fix: Export Represented Transaction Report
    ~ Enhancement: Call Account Owner Info from Admin Panel
# Release v5.1.1 on 2022-10-11 

    ~ Fix: Failed transaction no need to show the cancel Button
# Release v5.1.0 on 2022-10-11 

    ~ Enhancement: Send email notification to admin when consumer with Chime Institution having balance greater than $300
    ~ Bug Fix: Blacklisted account delete error fixed
# Release v5.0.5 on 2022-10-01 

    ~ FIx: Pending Store's Approval status show in transaction history
    ~ Enhancement: Transaction Report and Settlement Report modified according to ecommerce transactions
    ~ Fix: When the consumer declines the transaction modification request void button will not show.
# Release v5.0.4 on 2022-09-29 

    ~ Fix: Revoke void show
# Release v5.0.3 on 2022-09-29 

    ~ Fix: Status change and cancel button show
# Release v5.0.2 on 2022-09-28 

    ~ Fix: Void status will show when voided transaction
    ~ Fix: Slow query issue fixed for Merchant Transaction Report
# Release v5.0.1 on 2022-09-27 

    ~ Fix: env processed via config file.
# Release v5.0.0 on 2022-09-27 

    ~ Enhancement: Ecommerce Transaction And Ecommerce Transaction Modification
    ~ Enhancement: Transaction Modification Reason
# Release v4.3.2 on 2022-09-21 

    ~ Enhancement: Disbaled store shown in the Transaction menu
# Release v4.3.1 on 2022-09-20 

    ~ Enhancement: Disable Stores needs to be shown in the Dropdown list for Admin Panel
# Release v4.3.0 on 2022-09-20 

    ~ Enhancement: Composer and NPM has been updated
# Release v4.2.1 on 2022-09-14 

    ~ Fix: void transaction fix
# Release v4.2.0 on 2022-09-13 

    ~ Enhancement: Add lockout unlock functionality
# Release v4.1.1 on 2022-09-10 

    ~ Enhancement: Transaction Report query optimization
# Release v4.1.0 on 2022-09-09 

    ~ Enhancement: States (PA nd DE) added in Fifth Third user for direct linked bank accounts
# Release v4.0.1 on 2022-09-07 

    ~ Bug Fix: Remove Auditable from models
# Release v4.0.0 on 2022-09-06 

    ~ Enhancement: Separate Email and SMS has been introduced. Notification resend feature has also been implemented.
# Release v3.40.5 on 2022-09-06 

    ~ Enhancement: consumer status update
    ~ Enhancement: Consumer deactivation check, Multiple file upload
# Release v3.40.5 on 2022-09-06 

    ~ Enhancement: Consumer deactivation check, Multiple file upload
# Release v3.40.4 on 2022-09-03 

    ~ Bug Fix: Query optimised for Return Transaction Reports
# Release v3.40.3 on 2022-08-29 

    ~ Enahncement: Composer ann NPM has been updated
# Release v3.40.2 on 2022-08-23 

    ~ Bug Fix: Auditable removed from models
# Release v3.40.1 on 2022-08-23 

    ~ Enhancement: Larvel has been updated to version 8
# Release v3.40.0 on 2022-08-23 

    ~ Enhancement: Larvel has been updated to version 7
# Release v3.39.2 on 2022-08-16 

    ~ Enhancement: Composer and NPM has been updated
# Release v3.39.1 on 2022-08-11 

    ~ Fix: Address change issue fixed
# Release v3.39.0 on 2022-08-08 

    ~ Enhancement: Composer and NPM has been updated
    ~ Enhancement: All transactions happened today before the store's address update should get the updated timezone
# Release v3.38.3 on 2022-08-02 

    ~ Enhancement: Fifth Third user detection notification feature implemented
# Release v3.38.2 on 2022-08-01 

    ~ Enhancement: Composer and NPM has been updated
# Release v3.38.1 on 2022-07-26 

    ~ Enhancement: Address update history
    ~ Enhancement: Composer and NPM has been updated
# Release v3.38.0 on 2022-07-20 

    ~ Enhancement: Revoke void functionality added in Admin Panel
# Release v3.37.0 on 2022-07-19 

    ~ Enhancement: Composer and NPM has been updated
    ~ Enhancement: Unknown routing number listing and approval feature implemented
    ~ Fix: Unnecessary indexing removed from Transaction report query
# Release v3.36.0 on 2022-07-12 

    ~ Enhancement: Store Locator from Canpay Admin
# Release v3.35.0 on 2022-07-11 

    ~ Enhancement: Query optimization for Transaction Report
    ~ Enhancement: Composer and NPM has been updated
# Release v3.34.3 on 2022-07-05 

    ~ Fix: Query fixed for release consumer data listing.
# Release v3.34.2 on 2022-07-05 

    ~ Enhancement: Composer and Npm has been update
# Release v3.34.1 on 2022-07-01 

    ~ Ehancement: Export full list option added for probable returns.
# Release v3.34.0 on 2022-06-30 

    ~ Enhancement: Consumer release from probable return feature implemented.
# Release v3.33.0 on 2022-06-27 

    ~ Enhancement: DB related helper work moved to dbhelpers
    ~ Enhancement: Composer and NPM has been updated
    ~ Enhancement: Forgot password email validation added & create middleware for refferer policy
# Release v3.32.0 on 2022-06-20 

    ~ Enhancement: Composer and npm has been updated
    ~ Enhancement: Bank details marked as contains probable return if found in uploaded PDF from admin
# Release v3.31.1 on 2022-06-17 

    ~ Fix: Email restricted for Suspected Fraud users from manula identity review.
# Release v3.31.0 on 2022-06-15 

    ~ Enhancement: Suspected Fraud status introduced and transaction declined for those consumers. Also, during identity review admin can also mark a consumer as suspected fraud.
# Release v3.30.3 on 2022-06-14 

    ~ Fix: Consumer bank balance not showing issue fixed
# Release v3.30.2 on 2022-06-14 

    ~ Bug Fix: Consumer View popup last balance fetch should come from scheduled balance fetch
# Release v3.30.1 on 2022-06-13 

    ~ Enhancement: Composer and NPM has been updated on 10-06-2022
# Release v3.30.0 on 2022-06-06 

    ~ Enhancement: Suspected consumer list created and mark/remove from suspect list feature added.
# Release v3.29.4 on 2022-06-04 

    ~ Enhancement: Show Standard daily limit for existing users and disable pp
# Release v3.29.3 on 2022-06-03 

    ~ Bug Fix: Pending Transaction calculation fixed for user and account_no
# Release v3.29.2 on 2022-05-16 

    ~ Enhancement: Composer and NPM has been updated.
# Release v3.29.1 on 2022-05-12 

    ~ Bug Fix: Helpdesk user will be able to view Void Transaction Report
# Release v3.29.0 on 2022-05-10 

    ~ Enhancement: Remove the min 3 chars validation from stores pages
# Release v3.28.1 on 2022-04-28 

    ~ Enhancement: Show void transaction menu to helpdesk users
# Release v3.28.0 on 2022-04-27 

    ~ Bug Fix: Error Code 1002 introduced for FI ID mismatch while consumer refresh balance
# Release v3.27.1 on 2022-04-26 

    ~ Bug Fix: Show the last 4 digits of account number
# Release v3.27.0 on 2022-04-26 

    ~ Enhancement: Search with Consumer name and phone no. added in the blacklisted page
    ~ Enhancement : cognito implementation done
    ~ Enhancement: Composer and NPM has been updated
# Release v3.26.1 on 2022-04-25 

    Revert "~ Enhancement: NPM and Composer has been updated"
# Release v3.26.0 on 2022-04-25 

    ~ Enhancement: NPM and Composer has been updated
    ~ Enhancement: Option added in the corporate parent level to show/hide void transaction menu in the Merchant Admin Panel
# Release v3.25.5 on 2022-04-20 

    ~ Enhancement: Slow Queries connected from separate database
# Release v3.25.4 on 2022-04-11 

    ~ Bug Fix: Refresh balance login error fix due to consumer not found in finicity
# Release v3.25.3 on 2022-04-04 

    ~ Bug Fix : ACH Consumer Accounts API call removed from refresh balance
# Release v3.25.2 on 2022-04-04 

    ~ Enhancement: Composer and NPM has been updated
# Release v3.25.1 on 2022-03-30 

    ~ Bug Fix: Issue with delete alert called multiple times fixed
# Release v3.25.0 on 2022-03-29 

    ~ Enhancement: Monthly Storewise Sales report created with export
    ~ Enhancement : Admin can add account to blacklist
    ~ Enhancement: Add to master option added for Unknown return reasons received from Acheck21
# Release v3.24.0 on 2022-03-28 

    ~ Enhancement: Refresh consumer account balance from admin once during a day (PST)
# Release v3.23.0 on 2022-03-28 

    ~ Enhancement: Composer and NPM has been updated
# Release v3.22.2 on 2022-03-23 

    ~ Enhancement: Void Transaction menu added
# Release v3.22.1 on 2022-03-17 

    ~ Bug Fix: Include settled transacttions in the Return Transactions report
# Release v3.22.0 on 2022-03-15 

    ~ Fix: Unknown return code issue fixed in Return PDF upload
# Release v3.21.7 on 2022-03-15 

    ~ Fix: Return Transaction email page unknown return code issue fixed
# Release v3.21.6 on 2022-03-15 

    ~ Enhancement: Unknown retrun code managed in all Return Transaction Pages
# Release v3.21.5 on 2022-03-15 

    ~ Enhancement: Reloading table stopped after updating consumer details and return transaction page, consumer transaction page
# Release v3.21.4 on 2022-03-11 

    ~ Bug Fix: Search date field increased to 30 days, sorting added and manul review details search w.r.t date fixed
# Release v3.21.3 on 2022-03-09 

    ~ Bug Fix: Date field made non-mandatory for Globalradar, Manual review and V1 manual review
    ~ Enhancement: Fetch data in Consumers and Return Transactions page from main db
# Release v3.21.2 on 2022-03-09 

    ~ Enhancement: Add search with consumer name, email nd phone no. in pages where only search with date is there
# Release v3.21.1 on 2022-03-09 

    ~ Fix: Query fixed for Consumer Transaction History
# Release v3.21.0 on 2022-03-08 

    ~ Enhancement: Manual Identity Review, V1 Manual Review, Global Radar Review, Bank Upload Statement pages, Admin users, Helpdesk Users, Global Notifications, Release Note autoload removed and search added
# Release v3.20.2 on 2022-03-01 

    ~ Enhancement: Corporate Parent, Store, Store Api keys page auto loading replaced with search box
# Release v3.20.1 on 2022-02-16 

    ~ Enhancement: logo to aws cdn
# Release v3.20.0 on 2022-02-16 

    Revert "~ Enhancement: Refresh consumer account balance from admin once during a day (PST)"
# Release v3.19.0 on 2022-02-15 

    ~ Enhancement: Refresh consumer account balance from admin once during a day (PST)
# Release v3.18.3 on 2022-02-14 

    ~ Enhancement: Website Address update facility given in Merchant Edit section
# Release v3.18.2 on 2022-02-11 

    ~ Fix: Same date return issued and import issue fixed
# Release v3.18.1 on 2022-02-10 

    ~ Enhancement : s3 slow image loading issue fixed.
# Release v3.18.0 on 2022-02-09 

    ~ Bug Fix: Purchase power not showing after update fixed
    ~ Enhancement: Import return transaction by parsing a PDF file in database
# Release v3.17.4 on 2022-02-03 

    ~ Enhancement: Added # concat for hex color
# Release v3.17.3 on 2022-01-31 

    ~ Bug Fix : valdation updates
# Release v3.17.2 on 2022-01-28 

    ~ Enhancement: added fileds for corporate url and dispaly name
# Release v3.17.1 on 2022-01-24 

    ~ Bug Fix: Update contact number of retailer instead of merchant
# Release v3.17.0 on 2022-01-20 

    ~ Enhancement: Merchant Landing page
# Release v3.16.0 on 2022-01-18 

    ~ Enhancement : Release note add in admin panel implementation done
# Release v3.15.12 on 2022-01-04 

    ~ Fix: Dashboard V2 monthly sales report issue fixed
# Release v3.15.11 on 2021-12-30 

    ~ Fix: Comma added in consumer count and head changed to v2 with query modification
# Release v3.15.10 on 2021-12-29 

    ~ Enhancement: Manual vs Direct Link report added in Dashboard
# Release v3.15.9 on 2021-12-21 

    ~ Enhancement : Fraud Prevention PP decrease: Bank balance should override default limit implementation done
    ~ Enhancement: Monthly Sales Growth Report added
# Release v3.15.8 on 2021-12-17 

    ~ Enhancement: Report Admin role added and permisssion given
    ~ BugFix : CanPay Admin: Lost ability to edit Birthdates and Consumers can't edit birthdates OR addresses fixed
# Release v3.15.7 on 2021-12-15 

    ~ Fix: Corporate parent update issue fixed
# Release v3.15.6 on 2021-12-15 

    ~ Enhancement: Changes made in te Transaction Report
# Release v3.15.5 on 2021-12-14 

    ~ Enhancement: Scheduled Jobs on/off option added in the Admin Panel
# Release v3.15.4 on 2021-12-09 

    ~ Fix: Consumer's DOB issue fixed if empty or 0000-00-00 in Database
# Release v3.15.3 on 2021-12-09 

    ~ Fix: Permission issue fixed for Helpdesk Users
# Release v3.15.2 on 2021-12-08 

    ~ Bug Fixes: Return dashboard report issues fixed
# Release v3.15.1 on 2021-12-07 

    ~ Fix: Consumer status change permission given to Helpdesk users
# Release v3.15.0 on 2021-12-07 

    ~ Enhancement: XSS middleware implemented for Stored XSS protection
    ~ Bug fix: Remove the js and css version from css and js files
    ~ Bug Fix: JWT encryption method changed to RS256
    ~ Bug Fixes: User level middleware implemented to restrict the access of APIs
# Release v3.14.7 on 2021-12-02 

    ~ Fix: Corporate parent add store issue fixed
# Release v3.14.6 on 2021-11-29 

    ~ Bug Fix: Balance fetch error fix
# Release v3.14.5 on 2021-11-29 

    ~ Bug Fix: Refresh Balance for only active account
# Release v3.14.4 on 2021-11-26 

    ~ Enhancement: Added Sorting based on most recent transaction in >$300 Fraud Report
# Release v3.14.3 on 2021-11-18 

    ~ Fix: Log time issue fixed
    ~ Enhancement : Consumer Bank Statement module implementation done
# Release v3.14.2 on 2021-11-17 

    Revert "~ Bug Fixes: User level middleware implemented to restrict the access of APIs"
    Revert "~ Bug Fix: JWT encryption method changed to RS256"
    Revert "~ Bug fix: Remove the js and css version from css and js files"
# Release v3.14.1 on 2021-11-16 

    ~ Bug Fix: Consumer Account Balance fetch real account no. issue fixed
    ~ Bug Fix: Consumer's return count issue fixed
# Release v3.14.0 on 2021-11-16 

    ~ Bug fix: Remove the js and css version from css and js files
    ~ Bug Fix: JWT encryption method changed to RS256
    ~ Bug Fixes: User level middleware implemented to restrict the access of APIs
# Release v3.13.11 on 2021-11-16 

    ~ Bug Fix: Pending transaction amount fixed for consumer and Return reason modified in Email body
# Release v3.13.10 on 2021-11-11 

    ~ Bug Fix: Slow query issue fixed in Consumer Search
# Release v3.13.9 on 2021-11-10 

    ~ Bug Fix: Dashboard report issue fixed
# Release v3.13.8 on 2021-11-10 

    ~ Bug Fix: Slow query fixed in User search and Dashboard
# Release v3.13.7 on 2021-11-10 

    ~ Bug Fix: Status Dropdown not coming in Consumer LList page issue fixed
    ~ Enhancement :  In admin panel  holiday List import system implementation done
# Release v3.13.6 on 2021-11-09 

    ~ Bug Fix: Fix alignment issue for Consumers page
# Release v3.13.5 on 2021-11-09 

    ~ Bug Fix: Merge conflict error fixed
# Release v3.13.4 on 2021-11-09 

    ~ Enhancement: Consumer Account balances refresh option given for Direct link consumers
# Release v3.13.3 on 2021-11-09 

    ~ Enhancement: Consumer comment addition functionality added
# Release v3.13.2 on 2021-11-03 

    ~ Enhancement: Sample Download file changed as per new format
# Release v3.13.1 on 2021-11-02 

    ~ Bug Fix: Conumser's first transaction date issue fixed in Fraud Report
# Release v3.13.0 on 2021-10-29 

    ~ Bug Fix: Queries changed to prepared statements
# Release v3.12.5 on 2021-10-29 

    ~ Enhancement : Need a tracker in the customer record that shows if a customer has ever had a return, even if they don’t have open returns right now, and how many implementation done
# Release v3.12.4 on 2021-10-27 

    ~ Bug Fixes: Export option issue fixed for Finicity linked and mAnual linked report and return Reportts menu separated from transaction
# Release v3.12.3 on 2021-10-27 

    ~ Enhancement: Count and Count Percentage Columns added and other modifications done in Return Dashboard Report as per client requirement.
# Release v3.12.2 on 2021-10-25 

    ~ Enhancement: User type added in Finicity R01 Returns and Manual Banking Returns Report
# Release v3.12.1 on 2021-10-25 

    ~ Enhancement: Return dashboard report created and export option included
# Release v3.12.0 on 2021-10-22 

    ~ Enhancement: All Finicity R01 Returns Report added
    ~ Bug Fixes: Return transaction report issue fixed for successful returns
# Release v3.11.24 on 2021-10-18 

    ~ Enhancement: Search functionality added and minor changes done in both Fraud report
    ~ Enhancement : Retailer name add on registration merchant edit
# Release v3.11.23 on 2021-10-11 

    ~ Bug Fix: Return Transaction file account_id error fix
    ~ Bug Fixes: Fraud report transaction count issue fixed and User type added in main listing
    ~ Enhancement: Add Consumer Returns Report in the Helpdesk user panel
# Release v3.11.22 on 2021-10-11 

    ~ Enhancement: Option to release Consumers from Admin Panel
# Release v3.11.21 on 2021-10-08 

    ~ BugFix: Pending Transaction Amount information should be displayed in CanPay Admin->Consumer Details done
# Release v3.11.20 on 2021-10-08 

    ~ Enhancement: Automate Return Posting
    ~ Enhancement: Enable Alert dashboard for helpdesk users
# Release v3.11.19 on 2021-10-08 

    ~ Bug Fixes: Fraud report changes done as per client feedback and Return Transaction report missing columns Export issue fixed
# Release v3.11.18 on 2021-10-07 

    ~ Bug Fix: Export button removed
# Release v3.11.17 on 2021-10-07 

    ~ Enhancement: Add reports in the Alert Dashboard for frauds
# Release v3.11.16 on 2021-10-06 

    ~ Enhancement: New return group report created for Return Code wise grouping report
# Release v3.11.15 on 2021-10-06 

    ~ Bug Fixes: Query issue fixed for return report
# Release v3.11.14 on 2021-10-05 

    ~ Bug Fixes: Return report data issue fixed with changed defined by client
# Release v3.11.13 on 2021-10-04 

    ~ Bug Fix: Total amount issue fixed in return Transaction report and All returns option added
# Release v3.11.12 on 2021-10-04 

    ~ Bug Fixes: date issue solved for return transaction report
# Release v3.11.11 on 2021-10-04 

    ~ Enhancement: Modifications done as per client in Return Transaction report in admin
    ~ Bugfix : Purchase Power information in Consumer Details Window within Consumers Listings page needs to be modified done
# Release v3.11.10 on 2021-09-28 

    ~ Enhancement: Audit records moved to CSV file instead of table
# Release v3.11.9 on 2021-09-22 

    ~ Enhancement :  Mail send manually for return transaction through admin panel
# Release v3.11.8 on 2021-09-16 

    ~ BugFix: Purchase power was getting updated while updating standard daily limit.
# Release v3.11.7 on 2021-09-13 

    ~ Enhancement: Transaction report added in the Admin Panel
    ~ Bug Fix: Transaction Report fix for different terminal transactions coming under one
# Release v3.11.6 on 2021-09-10 

    ~ Enhancement: Consumer purchase power inserted in history table in case of PP change from Admin
    ~ Enhancement : Purchase power selected as per white listed routing number by code 29 implementation done
# Release v3.11.5 on 2021-09-05 

    ~ Bug Fix : Settlement Report only parent rows fetched to avoid duplicate
# Release v3.11.4 on 2021-09-03 

    ~ Enhancement: Include Return in Settlement report
# Release v3.11.3 on 2021-09-03 

    ~ Enhancement: Alert Dashboard with purchase more than 700 in last 48 hrs fetched
# Release v3.11.2 on 2021-09-03 

    ~ Bug Fix: Connect consumer details query from read replica
# Release v3.11.1 on 2021-09-01 

    ~ Bug Fix: Alert Dashboard query fix for users with purchase more than 800
    ~ Bug Fix: Apartment number can include alphanumeric characters
# Release v3.11.0 on 2021-09-01 

    ~ Enhancement: Add Settlement report in the Admin Panel
    ~ Enhancement: Create alert Dashboard in Admin Panel
# Release v3.10.17 on 2021-08-27 

    ~ Bug Fixes: Purchase power issue fixed for Consumers
    ~ Bug Fixes: MySQL DATE function removed from all queries
    ~ Enhancement: Searchbox added in the Registered Merchant page
    ~ Enhancement: Datatable count query optimised
    ~ Enhancement: Add Edit Option for Consumer to Edit Name and Address Details
    ~ Enhancement: Fetch All the Dattable result from Read replica database
# Release v3.10.16 on 2021-08-24 

    ~ Bug Fix: Total Transactions Query fixed
# Release v3.10.15 on 2021-08-24 

    ~ Bug Fix: Transaction Report static date removed
# Release v3.10.14 on 2021-08-24 

    ~ Enhancement: Transaction Count shown in Transaction Report
# Release v3.10.13 on 2021-08-20 

    ~ Bug Fixes: Audit Trail menu hidden from Sidebar
# Release v3.10.12 on 2021-08-20 

    ~ Enhancement: V1 Consumer phone No. and email edit and name option added
# Release v3.10.11 on 2021-08-18 

    ~ Bug Fix: Consumer Update error popup removed
# Release v3.10.10 on 2021-08-18 

    ~ Bug Fix: Soring option removed from datatable in registered Merchnat Page
# Release v3.10.9 on 2021-08-18 

    ~ Enhancement: Add reset button in All Transactions page
    ~ Bug Fix: Registered Merchant page slow query fixed
# Release v3.10.8 on 2021-08-17 

    ~ Bug Fix: Query in dashboard optimised
# Release v3.10.7 on 2021-08-17 

    ~ Bug Fix: Dashboard Queries optimised for daily, moonthly and average transactions
# Release v3.10.6 on 2021-08-17 

    ~ Bug Fix: Consumer List page query optimization done
# Release v3.10.5 on 2021-08-17 

    ~ BugFix: Transactions are getting mapped with wrong consumers issue resolved.
# Release v3.10.4 on 2021-08-17 

    ~ Enhancement: Consumer List page query pointed to read replica
# Release v3.10.3 on 2021-08-17 

    ~ Bug Fix: Consumer searched from Global Search not showing in the Consumer List
    ~ Bug Fix: Query changed for Total Consumer Transaction in Transaction Report
    ~ Enhancement: Remove the datatable search and Add search box for Consumer and Store
    ~ Enhancement: User ID added in all logs
# Release v3.10.2 on 2021-08-12 

    ~ Enhancement : In All Stores new column add Transaction Type  implementation done
    ~ Enhancement :  Acheck Account details added on Register merchant edit implementation done
# Release v3.10.1 on 2021-08-12 

    ~ Bug fix : V1-onboarding manual review bug fixed
# Release v3.10.0 on 2021-08-10 

    ~ Bug Fixes: Store checking in terminal existance checking
    ~ Enhancement: Datatable search disabled until user types three characters
    ~ Enhancement: Add Reload button if error occurs in datatable
    ~ Enhancement: Update Transaction menu disabled
    ~ Enhancement: local_transaction_time column changed to local_transaction_date in every reports
# Release v3.9.2 on 2021-08-06 

    ~ Enhancement: Weekly Spending Limit made mandatory if purchase power is given
    ~ Bug Fix: JWT token issue fixed, Logout API implemented and index changed for All Transaction report
# Release v3.9.1 on 2021-08-05 

    ~ Bug Fix: All transaction report query slowness issue fixed
# Release v3.9.0 on 2021-08-05 

    ~ Enhancement: Add Consumer Global Search
    ~ Enhancement: Modifications done on Return transaction report as per client feedback
    ~ Bug Fix: Dashboard blocked queries reverted
    ~ Bug Fix: Datatable Alert message suppressed for all datatables
# Release v3.8.2 on 2021-08-05 

    ~ Bug Fix: Consumer Edit popup data fix
    ~ Bug Fix: Dashboard queries blocked as it is taking huge loading time
    ~ Enhancement: Show the Bank details in the View details popup for Consumers
# Release v3.8.2 on 2021-08-05 

    ~ Bug Fix: Dashboard queries blocked as it is taking huge loading time
# Release v3.8.1 on 2021-08-04 

    ~ Bug Fix: Add Firebase SDK for Production
# Release v3.8.0 on 2021-08-04 

    ~ Enhancement: Read Replica DB connection introduced for Admin Panel Reports
    ~ Enhancement: Option to Edit purchase Power and weekly Limit from Consumer Edit Modal
    ~ Bug fix : In v1 manual review if users's status Suspended By Admin then also admin can change user status Actiavted By Admin fixed
    ~ BugFix: Canpay return credit debit reporting issue fixed.
# Release v3.7.0 on 2021-08-02 

    ~ Enhancement: Rest API logger implemented for parsing in ELK as raw JSON
    ~ Enhancement: Admin can now enable/disable consumers Purchase power calculation from bank blanace
# Release v3.6.13 on 2021-07-29 

    ~ Hot Fix: Set all static 0 fro Dashboard for now
    ~ Enhancement: Start Date and End Date set to Todays date on page load
# Release v3.6.12 on 2021-07-29 

    ~ Bug Fixes: Transaction report Canpay Offset date issue fixed
# Release v3.6.11 on 2021-07-29 

    ~ Bug Fixes: Consumer status inserted as pending issue fixed for Consumer Data Migration
# Release v3.6.10 on 2021-07-27 

    ~ Bug fix : v1 manual identity review where you click on the record and it doesn’t open .  fixed
# Release v3.6.9 on 2021-07-27 

    ~ BugFix: Error while trying to approve global radar pending review issue resolved.
    ~ Bug Fix: Consumer list page Returns Present column fixed
# Release v3.6.8 on 2021-07-24 

    ~ Bug Fixes: Represnt transaction issue fixed in Transaction Report
# Release v3.6.7 on 2021-07-23 

    ~ Bug Fix: registered Merchant Page and Consumer List page the Edit and view popup has been called with API
# Release v3.6.6 on 2021-07-23 

    ~ Enhancement :  Zipline customer migrating from V1 to V2 has a status of: Expired, Pending, Suspended/Blocked and Hold customer need to manual review implementation done
# Release v3.6.5 on 2021-07-23 

    ~ Bug Fixes: Total Sales issue fixed in dashboard for V1 and V2
# Release v3.6.4 on 2021-07-23 

    ~ Bug Fixes: Dashboard null isue fixed for v1 and v2 transactions
# Release v3.6.3 on 2021-07-23 

    ~ Bug Fixes: V2 sales and Transaction count issue fixed in Dashboard
# Release v3.6.2 on 2021-07-22 

    ~ Bug Fix: Total Consumer Transaction amount fixed
    ~ Bug Fix: Consumer Search in the Consumer Transaction History Page Fixed
    ~ Bug Fix: Registered Merchant page, search with Retailer fixed
    ~ Enhancement: dashboard V1 and V2 sales and Transaction shown separately.
# Release v3.6.1 on 2021-07-20 

    ~ BugFix: Canpay fee transactions are getting debited from merchant deposit account issue fixed.
    ~ Enhancement: Remove mandatory checking from DOB Update
# Release v3.6.0 on 2021-07-16 

    ~ Enhancement: Auto store manager creation stopped for V2 Merchant Import
    ~ Enhancement: Loading time in the dashboard minimized
    ~ BugFix: Wrong cognito details showing for consumers having no cognito scores.
    ~ Enhancement: Export feature added in return transaction and amount shown in report
# Release v3.5.0 on 2021-07-15 

    ~ Enhancement: Return waiving off feature introduced in Return Transaction report
# Release v3.4.5 on 2021-07-15 

    ~ Bug Fixes: Export issue in all reports has been resolved
# Release v3.4.4 on 2021-07-14 

    ~ Bug Fix: Customer fetched whose password is not null
# Release v3.4.3 on 2021-07-13 

    ~ Bug Fix: Consumer list loading time issue fixed
    ~ Enhancement: Individual Execution time for each API added in log file
# Release v3.4.2 on 2021-07-13 

    ~ Bug Fixes: V1 Transaction Import duplicated data insertion issue fixed
# Release v3.4.1 on 2021-07-11 

    ~ Bug Fixes: Duplicate transaction issue resolved in All Transaction report
# Release v3.4.0 on 2021-07-10 

    ~ Enhancement: Consumers can register with the Email IDs used for registering other users
    ~ Enhancement: Option to change Consumer DOB from Admin Panel
# Release v3.3.1 on 2021-07-09 

    ~ Enhancement: If cognito returns empty dataset then the score table is removed from the review modal.
    ~ Enhancement: Consumer system generic email address updated with a new one.
    ~ BugFix: Throwing error 599 while trying to fetch manual validation details error resolved.
# Release v3.3.0 on 2021-07-06 

    ~ Enhancement :- Table for Return Transaction in Canpay Admin Panel implemetation done
    ~ Enhancement : Update % of transaction and per transaction fee from Merchant details page where we can upload Banking implementation done
    ~ Enhancement :- Admin can have an option to disable store from viewing in Map implementation done
# Release v3.2.2 on 2021-07-05 

    ~ Bug Fixes: Issue of Represnted transaction not showing in Transaction report got fixed
    ~ Enhancement: Store name shown instead of merchant name in Merchant Listing
    ~ Enhancement: Daily Transaction Email sent to Store Mnagers/Accountant with the transaction details of Stores they are assigned to
# Release v3.2.1 on 2021-07-03 

    ~ Enhancement : Update % of transaction and per transaction fee from Merchant details page where we can upload Banking implementation done
    ~ Enhancement : Add Helpdesk user in CanPay Admin  implementation done
    ~ BugFixes: Error while trying to void transactions from CanPay Admin. Error shown "Store Not Configured" issue fixed.
# Release v3.2.0 on 2021-07-01 

    ~ Enhancement: Dashboard Page implementation
    ~ Enhancement: Dashboard Page's Average  sales and transaction over last 7 days implementation done
    ~ Bug Fixes: Fee update issue fixed in Merchant import and duplicate email check implemented in cousumer data migration
    ~ Enhancement: V1 transaction void feature implemented from canpay admin panel.
# Release v3.1.0 on 2021-07-01 

    ~ Bug Fixes: Merchant Fee info insertion/updation issue fixed
    ~ Enhancement: Generate Transaction Report in the Admin Panel
# Release v3.0.3 on 2021-06-29 

    ~ Bug Fixes: Transaction cancel issue fixed in transaction report
# Release v3.0.2 on 2021-06-29 

    ~ Bug Fixes: Terminal name in transaction report issue fixed and Merchant Import update issue fixed
    ~ Enhancement: Admin Users - username,contact person, phone no. and status field removed
# Release v3.0.1 on 2021-06-26 

    ~ Bug Fix: All Transaction export pagination issue fixed
# Release v3.0.0 on 2021-06-25 

    ~ Bug Fixes: Store showing null issue resolved in CP creation
    ~ Enhancement : all transaction report export implemented done
    ~ Bug fix: Merchant Import and other imports status for store set to active at the time of import
    ~ Enhancement: Manual Acheck21 webhook call and Merchant transaction scheduler running option implemented
    ~ Enhancement: Enable/Disable store
    ~ Enhancement : Implement accountant role for merchant :- Accountant role should be removed from admin users create modal dropdown done
    ~ Bug Fixes: Infinite Scrolling implemented in Transaction History page
    ~ Enhancement: Merchant import update and add sample link in import pages
    ~ Enhancement: Void transaction modified. Status updated for transaction but not getting posted in Acheck21.
    ~ Enhancement: Add Cp in the Stores menu
# Release v2.1.0 on 2021-05-28 

    ~ Bug Fix: Sorting issue fixed for name column in Manual Identity Review Pages
    ~ Bug Fix: Admin Users users status sort issue fixed
    ~ Enhancement: Corporate Parent Exce import functionality in Admin Panel
    ~ Bug Fix : Consumer List sort issue and search issue fixed
    ~ Enhancement: Notification read/unread feature implemented
    ~ Bug Fixes: Issue resolved for Corporate Parent edit with wrong stores getting deselected.
    ~ Enhancement: Every time corporate parent email is changed new password will sent to new CP email
    ~ Enhancement: Column added in Consumer list to show last 4 digit of Bank Account No.
    ~ Enhancement: View and Download Consumer transaction History
    ~ Enhancement: Update Merchant banking Information - Deposit account, fees account, routing number
    ~ Column added in Consumer list to show last 4 digit of Bank Account No.
    ~ Enhancement: Store API Keys Listing Page added along with export functionality
    ~ Bug Fixes: V2 merchant import issue of different dtore ids under same merchant fixed
    ~ Enhancement: During import the email we get from excel file now assigned to store manager instead of a regional manager
    ~ Enhancement: Corporate Parent's Daily Transactgion Activity report sent to their respective mail
    ~ Enhancement: Command created for sending Consumers Monthly Activity emailed to therir respective email ids.
    ~ Enhancement: Global radar review pages implemented along with apis
# Release v2.0.1 on 2021-05-20 

    ~ Enhancement: Store API Keys Listing Page added along with export functionality
# Release v2.0.0 on 2021-03-22 

    ~ Enhancement: Timezone added in Store Listing
    ~ Enhancement: Lat/Long and Timezone fetched from Google Places API and Updated in database in Store upload for both V1 and V2
    ~ Bug Fixes: Issue fixed for Transaction import of those stores who doesn't have a timezone
# Release v1.19.0 on 2021-03-12 

    ~ Enhancement: API key generated during store import in both V1 and V2 and Voided Transaction details stored in Database
    ~ Enhancement: Timezone added while store import and Transaction time modified during import as per Store timezone
    ~ BugFixes: All the datatable pagination not working for more than 1 pages of data issues fixed.
    ~ Bug fixes: Pagination issue fixed for Store and Transaction history page
    ~ Bug fixes: Issue fixed for Create Corporate Parent validation: The Primary Contact Last Name is accepting numbers and the phone number field is accepting alphabets.
    ~ Bug Fixes: Issue fixed for cancel transaction button showing for v1 transactions
    ~ Enhancement: Corporate form changed as per client requirement
# Release v1.18.0 on 2021-02-26 

    ~ BugFix: Error while trying to post merchant transaction into acheck21 due to incorrect bank account status. Issue resolved.
# Release v1.17.0 on 2021-02-24 

    ~ Enhancement: Merchant import modified based on store identifier
    ~ Bug Fixes: Store ID, Successful import count and upload order corrected in Merchant import
# Release v1.16.0 on 2021-02-18 

    ~ Enhancement: Registered merchant removed from sidebar and merchant id added in store listing and width fixed in store UI
    ~ Bug fixes: Pagination issue in Registered merchant page fixed
    ~ Enhancement: Test Email Send button feature implemented beside all the Email templates in admin panel
# Release v1.15.0 on 2021-02-17 

    ~ Enhancement: Global notification send procedure changed as per client requirement
    ~ Enhancement: Automatically remove "R" and "M" from store code while importing V1 stores and stored the relations in separate table
# Release v1.14.0 on 2021-02-02 

    ~ Enhancement: Forgot password feature implemented for Canpay Admin Panel
    ~ Enhancement: Global device manager mapped with every store being imported into the system
    ~ Bug Fixes: File upload removed from consumer v1 migration and batch insert and chunk reading introduced for better performance
    ~ BugFixes: Terminal wise notification count not showing issue resolved.
    ~ ClientFeedback: File imported column removed and validation messages corrected.
# Release v1.13.0 on 2021-01-19 

    ~ Enhancement: Enable/disable POS employee login feature control implemented.
# Release v1.12.0 on 2021-01-15 

    ~ Enhancement: 3rd party integrator onboarding feature implemented along with UI
# Release v1.11.0 on 2020-12-29 

    ~ SecurityIssueFix: Brute force attack related issue resolved and login otp concept implemented
    ~ Enhancement: No follow meta tag has been introduced in the welcome.blade page
# Release v1.10.0 on 2020-12-17 

    ~ Enhancement: 3rd Party Merchant Onboarding function integrated
# Release v1.9.0 on 2020-12-04 

    ~ Enhancement: Identity documents fetched directly from s3 bucket and displayed at the front end
    ~ ClientFeedback: Term Created At is changed with Created On for all the datatables.
# Release v1.8.0 on 2020-11-25 

    ~ ClientFeedback: Created at time format changed to 12 hours format with AM/PM
# Release v1.7.0 on 2020-11-06 

    ~ SecurityIssueFix: Issue fixed for Content Security Policy (CSP) Not Implemented
    ~ SecurityIssueFix: Cookie Not Marked as Secure Issue fixed and JWT expiration time reduced
    ~ Enhancement: Title added in global notifications
    ~ Bug Fixes: Transaction time issue fixed in both Transaction and Voided Transaction import
    ~ Enhancement: New email template seeder created for employee pin change
    ~ Enhancement: Transaction data migration done from V1 to V2 with UI
    ~ SecurityIssueFix: Vulnerability with Weak Logout Management fixed.
    ~ BugFixes: Data not getting rolledback while migrating from larger csv file.
    ~ Enhancement: Merchant listing with update feature implemented.
    ~ Enhancement: Merchant and Merchant Stores data migrated from V1 to V2 and UI implemented
# Release v1.6.0 on 2020-10-14 

    ~ Enhancement: Consumer data imported form  V1 system to V2 system and UI implemented
    ~ BugFixes: Status description not showing for Admin users and Corporate Parents issue fixed.
    ~ Enhancement: User and bank status implementation modified according to the status of v1 system
    ~ Enhancement: Consumer data migrated from V1 to V2 Syatem
    ~ Enhancement: SSN store into DB in encrypted format. While showing on manual review module it is decoded and shown.
    ~ Enhancement: Blacklisted routing number list implemented.
    ~ Enhancement: Corporate Parent store access details modal UI enhanced.
# Release v1.5.0 on 2020-08-18 

    ~ BugFixes: Transaction cancellation comment related bugs fixed.
    ~ Enhancement: Add reason for cancelling transaction feature implemented.
# Release v1.4.0 on 2020-08-05 

    ~ Implementation of apt number and minor bugs fixed
    ~ BugFixes: Transaction listing section bugs fixed
# Release v1.3.0 on 2020-07-20 

    ~ Bug Fixes: Audit Trail page braking issue fixed
    ~ BugFixes: Minor and major blocker bugs fixed
    ~ ClientFeedback: Manual Identity Review status dropdown and functionality implemented
    ~ Enhancement: Transaction cancellation feature implemented.
    ~ Enhancement: Consumer listing and status change feature implemented
    ~ ClientFeedback: Text Canpay changed into "CanPay"
# Release v1.2.0 on 2020-07-06 

    ~ BugFixes: Manual review procedure bugs fixed
    ~ Enhancement: Manual identity review section implemented
    ~ Bug Fixes: User ID column defined as nullable for registration purpose
    ~ Enhancement: Audit Trail done for all tables and Audit trail View Created for Admin
# Release v1.1.0 on 2020-05-22 

    ~ Bug Fixes: The name, phone format validated while adding Corporate parents and Admin users
# Release v1.0.0 on 2020-05-15 

    ~ Enhancement: Change Password feature implemented for admin users
    ~ Bug Fixes: Search issue fixed in all listing pages
    ~ Bug Fixes: Issue fixed for Corporate Parent Edit, Update Profile and general UI issue
    ~ Enhancement: CP and admin user status changed feature has been implemented.
    ~ Enhancement: Database primary key changed from int to varchar with unique alphanumeric string
    ~ Bug Fixes: Response code issue fixed in all vue files
    ~ Enhancement: Global notification creation section added and firestore collection updated for terminals for showing the notification count
    ~ Bug Fixes: Sidebar collapse issue fixed
    ~ Enhancement: Dynamic Email Template created and mail sent by compiling from blade file
    ~ Enhancement: Sonar badges added in Readme
    ~ Enhancement: scss structure optimized. Canpay colors added.
    ~ Bug Fixes: Users menu is not opening on the first time Issue fixed
    ~ Enhancement: Multiselect implemented for Stores in Corporate Praent Add/Edit
    ~ Bug Fixes: Logo Background fixed
    ~ Bug Fixes: Corporate Parent Dropdown UI, Validation, Same Store Selection issue and Phone Number Regex Fixed
    ~ Enhancement: Store list added with Regional Manager Mapping
    ~ Bug Fix: refresh page after profile updation.
    ~ Bug Fixes: Invalid token error and db transaction failed error resolved
    ~ Enhancement: Admin, Accountant, Help Desk User CRUD implemented
    ~ Enhancement: Import regional manager functionality implemented--
    ~ Enhancement: Multi Select implemented for Stores in Corporate Parent CRUD
    ~ Enhancement: Corporate Parent Add/Edit Created and with DT server Side Pagination and UI Fixes
    ~ Enhancement: New screens Import Manager and Corporate parent CRUD created
    ~ Enhancement: user login and update profile pages and functionality implemented
    ~ Enhancement: JWT implemented for login and other APIs
