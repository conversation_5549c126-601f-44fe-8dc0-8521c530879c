<template>
<div>
    <div>
        <Can
    </div>
    <div class="content-wrapper py-3" style="min-height: 36px">
        <div class="container-fluid">
            <h3 class="mb-3">Jackpot and Daily Drawing Value Reset</h3>

            <div class="card">
                <div class="card-body">
                    <label for="">Jackpot Base Value:</label>
                    <h3><strong>{{currentjackpot.old_base_point}}</strong></h3>
                </div>
            </div>

            <div class="card mb-3">
                <div class="card-body">
                    <form ref="jackpotValueResetForm" v-on:submit="resetJackpot">
                        <div class="row">
                            <div class="col-12 col-md-6 col-lg-6 mb-3">
                                <label for="">Jackpot Base Value:</label> <br>
                                <NumberInput
                                style="display: none;"
                                v-model="jackpotFormData.jackpot_value"
                                length-type="jackpot"
                                type="decimal"
                                />
                                <input
                                class="form-control w-100"
                                :name="'jackpot_value'"
                                type="text"
                                v-model="jackpotFormData.jackpot_value"
                                v-validate="'required|min_value:'+jackpot_minimum_value"
                                />
                                <span v-show="errors.has('jackpot_value')" class="text-danger">{{ errors.first('jackpot_value') }}</span>
                            </div>
                            <div class="col-12 col-md-12 col-lg-12 mb-3">
                                <label for="">Action:</label> <br>
                                <div class="form-check-inline">
                                    <label class="form-check-label">
                                        <input 
                                        v-model="jackpotFormData.action" 
                                        v-validate="'required'"
                                        value="immediate" 
                                        type="radio" 
                                        class="form-check-input"
                                        name="jackpot_action"
                                        >Immediate effect
                                    </label>
                                </div>
                                <div class="form-check-inline">
                                    <label class="form-check-label">
                                        <input 
                                        v-model="jackpotFormData.action" 
                                        v-validate="'required'"
                                        value="after_winning" 
                                        type="radio" 
                                        class="form-check-input" 
                                        name="jackpot_action">After winning next Jackpot
                                    </label>
                                </div> <br>
                                <span v-show="errors.has('jackpot_action')" class="text-danger">{{ errors.first('jackpot_action') }}</span>
                            </div>
                            <div class="col-12 col-md-12 col-lg-12">
                                <button v-if="!saving" class="btn btn-success px-3" type="submit">Save</button>
                                <button v-if="saving" class="btn btn-success px-3 d-flex jutify-content-between align-items-center" type="button" disabled>
                                    <span>Save</span> 
                                    <div class="spinner-border spinner-border-sm text-light ml-2" role="status">
                                        <span class="sr-only">Loading...</span>
                                    </div>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-12 col-md-6 col-lg-6 mb-3">
                            <label for="">Winning Text:</label>
                            <h5>{{currentlottery.winning_text}}</h5>
                        </div>
                        <div class="col-12 col-md-6 col-lg-6 mb-3">
                            <label for="">Current Daily Drawing Amount:</label>
                            <h5>{{currentlottery.prize_value}}</h5>
                        </div>
                    </div>
                </div>
            </div>


            <div class="card">
                <div class="card-body">
                    <form ref="lotteryValueForm" v-on:submit="saveLetteryValueConfirmation">
                        <div class="row">
                            <div class="col-12">
                            <div class="row">
                            <div class="col-12 col-md-6 col-lg-6 mb-3">
                                <label for="">Winning Text:</label> <br>
                                <span class="red">The %amount% will be replaced by the actual winning amount.</span>
                                <input
                                class="form-control w-100"
                                :name="'winning_text'"
                                type="text"
                                v-model="lotteryFormData.winning_text"
                                />
                                <span v-show="errors.has('winning_text')" class="text-danger">{{ errors.first('winning_text') }}</span>
                            </div>
                            </div>
                            </div>
                            <div class="col-12">
                            <div class="row">
                            <div class="col-12 col-md-6 col-lg-6 mb-3">
                                <label for="">Daily Drawing:</label> <br>
                                <NumberInput
                                style="display: none;"
                                v-model="lotteryFormData.lottery_value"
                                length-type="jackpot"
                                type="decimal"
                                />
                                <input
                                class="form-control w-100"
                                :name="'lottery_value'"
                                type="text"
                                v-model="lotteryFormData.lottery_value"
                                />
                                <span v-show="errors.has('lottery_value')" class="text-danger">{{ errors.first('lottery_value') }}</span>
                            </div>
                            </div>
                            </div>
                            <div class="col-12 col-md-12 col-lg-12 mb-3">
                                <label for="">Action:</label> <br>
                                <div class="form-check-inline">
                                    <label class="form-check-label">
                                        <input
                                        v-model="lotteryFormData.action"
                                        value="immediately"
                                        type="radio"
                                        class="form-check-input"
                                        name="lottery_action"
                                        >Immediately
                                    </label>
                                </div>
                                <div class="form-check-inline">
                                    <label class="form-check-label">
                                        <input
                                        v-model="lotteryFormData.action"
                                        value="scheduled"
                                        type="radio"
                                        class="form-check-input"
                                        name="lottery_action">Scheduled
                                    </label>
                                </div> <br>
                                <span v-show="errors.has('lottery_action')" class="text-danger">{{ errors.first('lottery_action') }}</span>
                            </div>
                            <div class="col-12 col-md-12 col-lg-12 mb-3" :style="lotteryFormData.action === 'scheduled' ? 'display: block' : 'display: none'">
                                <div class="row">
                                    <div class="col-3">
                                        <label for="">From Date:</label> <br>
                                        <input
                                        autocomplete="off"
                                        class="start-date form-control"
                                        placeholder="From Date"
                                        id="start-date"
                                        onkeydown="return false"
                                        />
                                    </div>
                                    <div class="col-3">
                                        <label for="">To Date:</label> <br>
                                        <input
                                        autocomplete="off"
                                        class="end-date form-control"
                                        placeholder="To Date"
                                        id="end-date"
                                        onkeydown="return false"
                                        />
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-md-12 col-lg-12">
                                <button v-if="!lotterysaving" class="btn btn-success px-3" type="submit">Save</button>
                                <button v-if="lotterysaving" class="btn btn-success px-3 d-flex jutify-content-between align-items-center" type="button" disabled>
                                    <span>Save</span> 
                                    <div class="spinner-border spinner-border-sm text-light ml-2" role="status">
                                        <span class="sr-only">Loading...</span>
                                    </div>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <b-table-simple
                      responsive
                      show-empty
                      bordered
                      sticky-header="800px"
                      v-if="upcoming_lottery_rewards.length > 0"
                    >
                      <b-thead head-variant="light">
                        <tr>
                            <th>Prize Value ($)</th>
                            <th>From Date</th>
                            <th>To Date</th>
                            <th class="text-center">Action(s)</th>
                        </tr>
                      </b-thead>
                      <b-tbody v-for="(row, index) in upcoming_lottery_rewards" :key="index">
                        <b-tr>
                           <b-td class="text-left text-gray">{{
                            row.prize_value
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.from_date
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.to_date
                          }}</b-td>
                          <b-td class="text-center text-gray">
                            <a @click="confirmDeleteLotteryReward(row.id)" class="custom-edit-btn" title="Delete Lottery Reward" variant="outline-success" style="border:none"><i class="nav-icon fas fa-trash"></i></a>
                          </b-td>
                        </b-tr>
                      </b-tbody>
                    </b-table-simple>
                    <p v-else>No upcoming Lottery Rewards Found.</p>
                </div>
            </div>
        </div>
        </div>


        <!-- Confirmation modal start -->
        <b-modal ref="confirmation-modal" hide-footer hide-header id="confirmation-modal">
            <div class="color">
            <div class="col-12 text-center">
                <svg fill="none" width="100px" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M8.87 10.5046L10.8204 12.4504L9.76045 13.511L6 9.75456L9.76045 5.99805L10.8204 7.05871L8.87 9.00456H18V18H11.9532V16.5H16.5V10.5046H8.87Z" fill="#149240"/>
                </svg>
            </div>
            <div class="purchaserpower-modal-text">
                <div class="d-block text-center">
                <label class="text-justify text-secondary h4">
                    Are you sure ?
                </label>
                <br />
                <label class="text-justify text-secondary text-dark">
                    {{confirmationText}}
                </label>
                </div>
                <div class="row">
                <div class="col-12 text-center">
                    <button
                    @click="hideModal('confirmation-modal')"
                    class="btn btn-secondary btn-md center-block mr-2"
                    >
                    <label class="forgetpassword-ok-label my-0">Cancel</label>
                    </button>
                    <button
                    @click="confirmAndSave('confirmation-modal')"
                    class="btn btn-success btn-md center-block ml-2"
                    >
                    <label class="forgetpassword-ok-label my-0">Confirm</label>
                    </button>
                </div>
                </div>
            </div>
            </div>
        </b-modal>
        <!-- Confirmation modal end -->
    </div>
</div>
</template>

<script>
import { validationMixin } from "vuelidate";
import { required, minLength } from "vuelidate/lib/validators";
import { validate } from "json-schema";
import api from "@/api/rewardwheel.js";
import NumberInput from "./NumberInput.vue";
import moment from "moment";
import CanPayLoader from "../CustomLoader/CanPayLoader.vue"
export default {
    mixins: [validationMixin],
    components: {
        NumberInput,
        CanPayLoader
    },
    data(){
        return{
            currentjackpot: {},
            currentlottery: {},
            upcoming_lottery_rewards:{},
            jackpotFormData:{
                jackpot_value: '',
                action: ''
            },
            saving: false,
            loading: false,
            jackpot_minimum_value: process.env.MIX_JACKPOT_MINIMUM_VALUE,
            lotteryFormData: {
                winning_text: '',
                lottery_value: '',
                action: 'immediately',
                from_date: '',
                to_date: ''
            },
            lotterysaving: false,
            confirmationText: "",
            submitionType: "",
            lottery_delete_id:""
        }
    },
    mounted(){
        var self = this;
        $("#start-date").datepicker({
        format: "mm/dd/yyyy",
        autoclose: true,
        todayHighlight: true,
        startDate: new Date() // Set the startDate to today
        }).on('changeDate', function (ev) {
            var selectedDate = new Date(ev.date);
            selectedDate.setDate(selectedDate.getDate()); // Add one day to the selected date
            $("#end-date").datepicker("setStartDate", selectedDate); // Set the start date of the end datepicker
            self.dateDiff();
        });

        $("#end-date").datepicker({
        format: "mm/dd/yyyy",
        autoclose: true,
        todayHighlight: true,
        startDate: new Date() // Set the startDate to today
        }).on('changeDate', function (ev) {
            self.dateDiff();
        });

        $("#start-date , #end-date").datepicker("setDate", new Date());

        self.getCurrentJackpotValue()
    },
    methods:{
        hideModal(modal){
            var self = this;
            self.$bvModal.hide(modal);
        },
        getCurrentJackpotValue(){
            var self = this;
            self.loading = false;
            api
            .getCurrentJackpotValue()
            .then(response => {
                self.currentjackpot = response.data.jackpot_details;
                self.currentlottery = response.data.lottery_winning_details[0];
                self.upcoming_lottery_rewards = response.data.upcoming_lottery_rewards;
                self.loading = false;
            })
            .catch(err => {
                self.loading = false;
            });
        },
        resetJackpot(event){
            var self = this;
            event.preventDefault();
            self.$validator.validateAll(['jackpot_value', 'jackpot_action']).then(result => {
                if(result){
                    self.submitionType = "jackpot"
                    self.confirmationText = "Are you sure want to reset Jackpot base value?"
                    self.$bvModal.show('confirmation-modal');
                }
            })
            .catch(err => {
                error(err);
            });
        },
        saveLetteryValueConfirmation(event){
            var self = this;
            event.preventDefault();
            self.$validator.validateAll(['lottery_value', 'lottery_action']).then(result => {
                if(result){
                    self.submitionType = "lottery"
                    self.confirmationText = "Are you sure want to change daily drawing value?"
                    self.$bvModal.show('confirmation-modal');
                }
            })
            .catch(err => {
                error(err);
            });
        },
        saveJackpotReset() {
            var self = this;
            let requestData = new FormData()
            requestData.append('old_base_point', self.currentjackpot.old_base_point)
            requestData.append('new_base_point', self.jackpotFormData.jackpot_value)
            requestData.append('immediate_effect', self.jackpotFormData.action == 'immediate' ? 1 : 0)
            requestData.append('after_winning_jackpot', self.jackpotFormData.action == 'after_winning' ? 1 : 0)

            self.saving = true;
            self.loading = true;
            api
            .saveJackpotReset(requestData)
            .then(response => {
                self.jackpotFormData = {
                    jackpot_value: '',
                    action: ''
                };
                self.submitionType = '';
                self.saving = false;
                self.getCurrentJackpotValue()
                self.$validator.reset();
                success(response.message);
                self.loading = false;
            })
            .catch(err => {
                self.saving = false;
                self.loading = false;
                if(err.response.data.message){
                    error(err.response.data.message);
                }else{
                    error(err);
                }
            });
        },
        saveLotteryValue(){
            var self = this;
            let requestData = new FormData()

            requestData.append('winning_text', self.lotteryFormData.winning_text)
            requestData.append('action', self.lotteryFormData.action)
            if(self.lotteryFormData.winning_text){
                requestData.append('is_winning_text', 1)
            }else{
                requestData.append('is_winning_text', 0)
            }
            requestData.append('value', self.lotteryFormData.lottery_value)
            if(self.lotteryFormData.action == 'immediately'){
                requestData.append('is_default', 1)
            }else{
                if ($("#start-date").val() != "") {
                    var from_date = moment($("#start-date").val()).format("YYYY-MM-DD");
                } else {
                    var from_date = "";
                }
                if ($("#end-date").val() != "") {
                    var to_date = moment($("#end-date").val()).format("YYYY-MM-DD");
                } else {
                    var to_date = "";
                }
                requestData.append('is_default', 0)
                requestData.append('from_date', from_date)
                requestData.append('to_date', to_date)
            }

            self.lotterysaving = true;
            self.loading = true;
            api
            .saveLotteryValue(requestData)
            .then(response => {
                self.submitionType = '';
                self.lotteryFormData = {
                    winning_text: '',
                    lottery_value: '',
                    action: 'immediately',
                    from_date: '',
                    to_date: ''
                };
                self.lotterysaving = false;
                self.loading = false;
                self.$validator.reset();
                self.getCurrentJackpotValue();
                success(response.message);
            })
            .catch(err => {
                self.lotterysaving = false;
                if(err.response.data.message){
                    error(err.response.data.message);
                }else{
                    error(err);
                }
                self.loading = false;
            });
        },
        confirmDeleteLotteryReward(id){
            var self = this;
            self.submitionType = 'deletelotteryreward'
            self.confirmationText = "Are you sure want to delete the Lottery Amount?"
            self.lottery_delete_id = id
            self.$bvModal.show('confirmation-modal');
        },
        deleteLotteryReward(){
            var self = this;
            var request = {
                'id':self.lottery_delete_id,
            };
            api
            .deleteLotteryReward(request)
            .then(response => {
                self.submitionType = '';
                self.lottery_delete_id = '';
                self.getCurrentJackpotValue();
                success(response.message);
            })
            .catch(err => {
                if(err.response.data.message){
                    error(err.response.data.message);
                }else{
                    error(err);
                }
            });
        },
        confirmAndSave(modal){
            var self = this;

            self.$bvModal.hide(modal);

            if(self.submitionType == 'jackpot'){
                self.saveJackpotReset();
            }else if(self.submitionType == 'lottery'){
                self.saveLotteryValue();
            }else if(self.submitionType == 'deletelotteryreward'){
                self.deleteLotteryReward();
            }
        },
        dateDiff() {
            if ($("#start-date").val() != "") {
                var from_date = moment($("#start-date").val()).format("YYYY-MM-DD");
            } else {
                var from_date = "";
            }
            if ($("#end-date").val() != "") {
                var to_date = moment($("#end-date").val()).format("YYYY-MM-DD");
            } else {
                var to_date = "";
            }
            if (from_date != '' && to_date != '') {
                //calculate the date Difference
                var date1 = new Date(from_date);
                var date2 = new Date(to_date);
                // To calculate the time difference of two dates
                var Difference_In_Time = date2.getTime() - date1.getTime();

                // To calculate the no. of days between two dates
                var Difference_In_Days = Difference_In_Time / (1000 * 3600 * 24);

                if (Difference_In_Days > 180) {
                $("#generateBtn").prop('disabled', true);
                } else {
                $("#generateBtn").prop('disabled', false);
                }
            }
        }
    }
}
</script>