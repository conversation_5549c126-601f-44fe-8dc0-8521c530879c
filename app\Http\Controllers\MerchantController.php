<?php

namespace App\Http\Controllers;

use App\Models\EcommerceIntegrator;
use App\Models\MerchantHealthCheck;
use App\Models\ReleaseNote;
use App\Models\ReleaseNoteReadUnread;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class MerchantController extends Controller
{
    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    /**
     * getAllReleaseNote
     * Listing page for Global Notifications along with Server Side Pagination in Datatable
     * @param  mixed $request
     * @return void
     */
    public function getAllReleaseNote(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Release Notes search started...");
        // Validating input request
        $this->validate($request, [
            'tag' => VALIDATION_REQUIRED,
        ]);

        // Search with in Release Notes
        $releaseNotes = $this->_getAllReleaseNoteSearch($request);

        $message = trans('message.release_notes_fetch_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Release Notes search started complete.");
        return renderResponse(SUCCESS, $message, $releaseNotes);
    }

    /**
     * _getAllReleaseNoteSearch
     * Fetch the Release Notes
     * @param  mixed $searchArray
     * @return void
     */
    private function _getAllReleaseNoteSearch($request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Release Notes Search Started.");

        $sql = "SELECT * FROM release_notes WHERE 1 ";

        $searchStr = [];
        if (trim($request['tag'])) {
            $sql .= " AND tag = ? ";
            array_push($searchStr, $request['tag']);
        }
        $sql .= "  ORDER BY created_at DESC LIMIT 100";
        $releaseNotes = DB::connection(MYSQL_RO)->Select($sql, $searchStr);

        $releaseNotesArr = [];
        if (!empty($releaseNotes)) {
            foreach ($releaseNotes as $notes) {

                $data = [];
                $data['tag'] = $notes->tag;
                $data['release_note'] = $notes->release_note;
                $data['release_note_description'] = $notes->release_note_description;
                $data['created_at'] = date('m-d-Y h:i A', strtotime($notes->created_at));

                array_push($releaseNotesArr, $data);
            }
        } else {
            $releaseNotesArr = [];
        }

        return $releaseNotesArr;
    }

    /**
     * add new routing numbers to the blacklist
     */
    public function addReleaseNote(Request $request)
    {
        $rule = array(
            'tag' => VALIDATION_REQUIRED,
            'release_note' => VALIDATION_REQUIRED,
        );
        $this->__validate($request->all(), $rule);

        $data = $this->request->all();
        $routing_number = ReleaseNote::where('tag', $data['tag'])->where('release_note', $data['release_note'])->first();
        if (!empty($routing_number)) {
            $message = trans('message.release_note_error');
            return renderResponse(FAIL, $message, null);
        }
        try {
            DB::beginTransaction();
            $new = new ReleaseNote();
            $new->tag = $data['tag'];
            $new->release_note = $data['release_note'];
            $new->release_note_description = $data['release_note_description'];
            $new->repo = 'Merchant Admin';
            $new->save();
            $role_array = [CORPORATE_PARENT, REGIONAL_MANAGER, ACCOUNTANT, STORE_MANAGER];
            $users = User::join('user_roles', 'users.role_id', '=', 'user_roles.role_id')->select('users.user_id')->whereIn('user_roles.role_name', $role_array)->get();

            foreach ($users as $user) {
                $release_note_read_unread = new ReleaseNoteReadUnread();
                $release_note_read_unread->release_note_id = $new->id;
                $release_note_read_unread->user_id = $user->user_id;
                $release_note_read_unread->save();
            }
            DB::commit();
            Log::info(addslashes(__METHOD__) . "(" . LINE . __LINE__ . ") : Release note insert successfully");
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception Release note insert", [EXCEPTION => $e]);
            DB::rollback();
            $message = trans('message.release_note_error');
            return renderResponse(FAIL, $message, $e);
        }
        $message = trans('message.release_note_success');
        return renderResponse(SUCCESS, $message, null);
    }

    /**
     * getAllIntegrator
     * Listing page for Admin Users along with Server Side Pagination in Datatable
     * @param  mixed $request
     * @return void
     */
    public function getAllIntegrator(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Integrator search started...");
        // Validating input request
        $this->validate($request, [
            // 'integrator_name' => VALIDATION_REQUIRED_WITHOUT_ALL . ':integrator_id',
            // 'integrator_id' => VALIDATION_REQUIRED_WITHOUT_ALL . ':integrator_name',
        ]);

        //Search with in Corpoarate Parents
        $integrators = $this->_getIntegratorSearch($request);

        $message = trans('Integrators fetched done.');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Integrator search started complete.");
        return renderResponse(SUCCESS, $message, $integrators);
    }

    public function _getIntegratorSearch($request)
    {

        // Main Query
        $integrators = EcommerceIntegrator::select(
            'ecommerce_integrators.id',
            'ecommerce_integrators.ecommerce_integrator_name',
            'ecommerce_integrators.integrator_id',
            'ecommerce_integrators.location_type',
            'ecommerce_integrators.allow_ecommerce_transaction',
            'ecommerce_integrators.allow_consumer_auth',
            'ecommerce_integrators.created_at',
            'ecommerce_integrators.integrator_logo'
        );

        if (strlen(trim($request['integrator_name'])) >= 3) {
            $integrators = $integrators->where(function ($q) use ($request) {
                $q->where('ecommerce_integrators.ecommerce_integrator_name', 'like', '%' . $request->integrator_name . '%');
            });
        }

        if (strlen(trim($request['integrator_id'])) >= 3) {
            $integrators = $integrators->where(function ($q) use ($request) {
                $q->where('ecommerce_integrators.integrator_id', $request->integrator_id);
            });
        }

        if (strlen(trim($request['integrator_name'])) >= 3 && strlen(trim($request['integrator_id'])) > 0) {
            $integrators = $integrators->get();
        } else if (strlen(trim($request['integrator_name'])) >= 3) {
            $integrators = $integrators->get();
        } else if (strlen(trim($request['integrator_id'])) > 0) {
            $integrators = $integrators->get();
        } else {
            $integrators = $integrators->latest()->limit(10)->get();
        }

        $integratorsArr = array();
        if (!empty($integrators)) {
            $disk = Storage::disk('s3');

            // Creating array to show the values in frontend
            foreach ($integrators as $integrator) {

                $data = [];
                $data['ecommerce_integrator_name'] = $integrator->ecommerce_integrator_name;
                $data['integrator_id'] = $integrator->integrator_id;
                $data['location_type'] = $integrator->location_type;
                $data['allow_ecommerce_transaction'] = $integrator->allow_ecommerce_transaction;
                $data['allow_consumer_auth'] = $integrator->allow_consumer_auth;
                $data['created_at'] = date('m-d-Y h:i A', strtotime($integrator->created_at));
                $data['id'] = $integrator->id;
                $data['integrator_logo'] = $integrator->integrator_logo ? $disk->temporaryUrl($integrator->integrator_logo, Carbon::now()->addMinutes(intval(config('app.s3_file_expiry_time'))), []) : null;

                array_push($integratorsArr, $data);
            }
        } else {
            $data = [];
        }

        return $integratorsArr;
    }

    /**
     * editIntegrator
     * This function will update the Admin User
     * @param  mixed $request
     * @return void
     */
    public function editIntegrator(Request $request)
    {
        $rule = array(
            'ecommerce_integrator_name' => VALIDATION_REQUIRED,
            'location_type' => VALIDATION_REQUIRED,
            'allow_ecommerce_transaction' => VALIDATION_REQUIRED,
            'allow_consumer_auth' => VALIDATION_REQUIRED,
        );
        $this->__validate($request->all(), $rule);

        DB::beginTransaction();
        try {
            // Updation begins in User Table
            $integrator = EcommerceIntegrator::find($request->get('id'));
            $integrator->ecommerce_integrator_name = $request->get('ecommerce_integrator_name');
            $integrator->location_type = $request->get('location_type');
            $integrator->allow_ecommerce_transaction = $request->get('allow_ecommerce_transaction');
            $integrator->allow_consumer_auth = $request->get('allow_consumer_auth');
            $integrator->integrator_logo = $request->get('integrator_logo');
            $integrator->save();
            DB::commit();
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "");
            $message = trans('message.user_updation_success');
            // API Response returned with 200 status
            return renderResponse(SUCCESS, $message, $integrator);
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured during User (");
            DB::rollback();
            $message = trans('message.user_updation_error');
            // Exception Returned
            return renderResponse(FAIL, $message, null);
        }
    }

    /**
     * addIntegrator
     * This function will add Role Specific Admin USers
     * @param  mixed $request
     * @return void
     */
    public function addIntegrator(Request $request)
    {
        $rule = array(
            'ecommerce_integrator_name' => VALIDATION_REQUIRED,
            'location_type' => VALIDATION_REQUIRED,
            'allow_ecommerce_transaction' => VALIDATION_REQUIRED,
            'allow_consumer_auth' => VALIDATION_REQUIRED,
        );
        $this->__validate($request->all(), $rule);

        DB::beginTransaction();
        try {
            // Insertion begins in User Table
            $integrator = new EcommerceIntegrator();
            $integrator->ecommerce_integrator_name = $request->get('ecommerce_integrator_name');
            $integrator->location_type = $request->get('location_type');
            $integrator->allow_ecommerce_transaction = $request->get('allow_ecommerce_transaction');
            $integrator->allow_consumer_auth = $request->get('allow_consumer_auth');
            $integrator->integrator_logo = $request->get('integrator_logo');
            $integrator->save();

            DB::commit();

            // API Response returned with 200 status
            return renderResponse(SUCCESS, null, $integrator);
        } catch (\Exception $e) {
            DB::rollback();
            // Exception Returned
            return renderResponse(FAIL, null, null);
        }
    }

    /**
     * Fetch all merchant health checks for a given store on a specific date.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getHealthChecksByStoreAndDate(Request $request)
    {
        // Validate the request
        $this->validate($request, [
            'store_id' => VALIDATION_REQUIRED,
            'from_date' => VALIDATION_REQUIRED . '|date_format:Y-m-d',
        ]);

        $storeId = $request->input('store_id');
        $date = $request->input('from_date');

        // Log the request
        Log::info(__METHOD__ . " - Fetching health checks for store_id: {$storeId} on date: {$date}");

        // Fetch records for the given store and date, ordered by created_at DESC
        $healthChecks = MerchantHealthCheck::join('merchant_stores', 'merchant_health_checks.store_id', '=', 'merchant_stores.id')
            ->select("merchant_stores.retailer", "merchant_health_checks.created_at")
            ->whereDate('merchant_health_checks.created_at', $date)
            ->where('merchant_health_checks.store_id', $storeId)
            ->orderBy('merchant_health_checks.created_at', 'DESC')
            ->get();

        $totalData = count($healthChecks);
        $totalFiltered = count($healthChecks);
        $data = array();
        if (!empty($healthChecks)) {
            // Creating array to show the values in frontend
            foreach ($healthChecks as $healthCheck) {
                $nestedData['store_name'] = $healthCheck->retailer;
                $nestedData['health_check_time'] = Carbon::parse($healthCheck->created_at)->tz('America/New_York')->format('m-d-Y h:i:s A');
                $data[] = $nestedData;
            }
        }

        $json_data = array(
            "draw" => intval($request->input('draw')),
            "recordsTotal" => intval($totalData),
            "recordsFiltered" => intval($totalFiltered),
            "data" => $data,
        );

        return $json_data;
    }
}
