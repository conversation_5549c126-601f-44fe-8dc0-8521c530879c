<template>
<div>
    <div  v-if="loading">
      <CanPayLoader/>
    </div>
    <div class="content-wrapper" style="min-height: 36px">
      <section class="content-header">
        <div class="container-fluid">
          <div class="row mb-2">
            <div class="col-sm-6"></div>
          </div>
        </div>
      </section>
      <div class="hold-transition sidebar-mini">
        <section class="content">
          <div class="container-fluid">
            <div class="row">
              <div class="col-12">
                <div class="card card-success">
                  <div class="card-header">
                    <h3 class="card-title">Reward Wheel Invitation Status</h3>
                  </div>

                  <div class="card-body">
                    <div class="card" style="box-shadow: none;">
                      <div class="card-body">
                        <h6>Consumer Details(Email or Phone)</h6>
                        <div class="row">
                          <div class="col-md-4 d-flex align-items-end">
                            <div class="form-group mb-0 w-100">
                              <input
                                class="form-control"
                                placeholder="Phone No (Exact)"
                                id="phone_no"
                                v-model="phone_no"
                              />
                            </div>
                          </div>
                          <div class="col-md-4 d-flex align-items-end">
                            <div class="form-group mb-0 w-100">
                              <input
                                class="form-control"
                                placeholder="Email (Exact)"
                                id="email"
                                v-model="email"
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="card-footer">
                    <button
                      type="button"
                      class="btn btn-success"
                      @click="searchRWConsumerStatus()"
                    >
                      Search
                    </button>
                    <button
                      type="button"
                      @click="reset()"
                      class="btn btn-success margin-left-5"
                    >
                      Reset
                    </button>
                  </div>
                  <div class="card-body" v-if="consumers.length > 0">
                      <div class="row">
                      <div class="col-12"><b-table-simple
                          responsive
                          show-empty
                          bordered
                          sticky-header="800px"
                        >
                          <b-thead head-variant="light">
                            <b-tr>
                              <b-th class="text-left">Name</b-th>
                              <b-th class="text-left">Phone</b-th>
                              <b-th class="text-left">Email</b-th>
                              <b-th class="text-left">Address</b-th>
                              <b-th class="text-left">State</b-th>
                              <b-th class="text-left">ZIP</b-th>
                              <b-th class="text-left">Type</b-th>
                              <b-th class="text-left">Status</b-th>
                              <b-th class="text-left">Invitation Status</b-th>
                            </b-tr>
                          </b-thead>
                          <b-tbody v-for="(row, index) in consumers" :key="index">
                            <b-tr>
                              <b-td class="text-left text-gray">{{
                                row.consumer_name
                              }}</b-td>
                              <b-td class="text-left text-gray">{{
                                row.phone
                              }}</b-td>
                              <b-td class="text-left text-gray">{{
                                row.email
                              }}</b-td>
                              <b-td class="text-left text-gray">
                                {{row.street_address}},
                                {{row.city}}
                              </b-td>
                              <b-td class="text-left text-gray">
                                {{row.state}}
                              </b-td>
                              <b-td class="text-left text-gray">{{
                                row.zipcode
                              }}</b-td>
                              <b-td class="text-left text-gray">{{
                                row.existing_user == 1 ? 'V1' : 'V2'
                              }}</b-td>
                              <b-td class="text-left text-gray">{{
                                row.status_name
                              }}</b-td>
                              <b-td class="text-left text-gray">{{
                                row.invitation_status.charAt(0).toUpperCase() + row.invitation_status.slice(1)
                              }}</b-td>
                            </b-tr>
                          </b-tbody>
                        </b-table-simple>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
</div>
  </template>
  <script>
  import moment from "moment";
  import api from "@/api/user.js";
  import rewardwheelapi from "@/api/user.js";
  import { validationMixin } from "vuelidate";
  import { required, minLength,decimal } from "vuelidate/lib/validators";
  import commonConstants from "@/common/constant.js";
  import { HourGlass } from "vue-loading-spinner";
  import CanPayLoader from "../CustomLoader/CanPayLoader.vue"
  export default {
    mixins: [validationMixin],
    data() {
      return {
          phone_no:"",
          email:"",
          loading:false,
          consumers: [],
      };
    },
    components: {
      HourGlass,
      CanPayLoader
    },
    methods: {
      reset(){
        var self = this;
        self.phone_no = ''
        self.email = ''
        self.consumers = []
      },
      // API call to fetch all consumers
      searchRWConsumerStatus() {
        var self = this;
  
        if(
          self.phone_no == '' && self.email == ''
        ){
          error('Please fill atleast one field.');
          return
        }
  
        var request = {
          phone_no: self.phone_no,
          email: self.email
        };
      
        self.loading = true;
        api
          .searchRWConsumerStatus(request)
          .then(function (response) {
            if (response.code == 200) {
              self.consumers = response.data;
              self.loading = false;
            } else {
              error(response.message);
              self.loading = false;
            }
          })
          .catch(function (error) {
            self.loading = false;
          });
      }
    },
    mounted() {
      var self = this;
      document.title = "CanPay - Reward Wheel Send Invitation";
    },
  };
  </script>
  
  <style lang="css" scoped>
  .control__indicator{
    cursor: pointer;
  }
  </style>