<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
class ConsumerSessionManagement extends Model
{
    protected $table = 'consumer_session_management';

    /**
     * __construct
     * This function will insert an alphanumeric 32 character unique id as primary key in the table whenever a data get inserted
     * @param  mixed $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        $this->id = generateUUID();

        parent::__construct($attributes);
    }

    protected $fillable = [
        'user_id',
        'token',
        'qr_url',
        'payment_pin',
        'login_time',
        'token_creation_time',
        'logout_time',
        'token_expiry_time',
        'session_type',
        'processing',
        'used',
    ];
    public $timestamps = true;
    public $incrementing = false;
}
