<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
class AuthorisedSignerInfo extends Model
{
    protected $table = 'authorized_signer_info';

    /**
     * __construct
     * This function will insert an alphanumeric 32 character unique id as primary key in the table whenever a data get inserted
     * @param  mixed $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        $this->id = generateUUID();

        parent::__construct($attributes);
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'merchant_id',
        'fullname',
        'title',
        'ssn',
        'date_of_birth',
        'signer_email',
        'street_address',
        'city',
        'state',
        'zip',
        'phone'
    ];
    public $timestamps = true;
    public $incrementing = false;
    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
}
