<template>
  <div class="card">
    <div v-if="!forgot_password" class="card-body login-card-body">
      <p class="login-box-msg">Sign in to access CanPay admin panel</p>
      <div class="input-group mb-3">
        <input
          type="email"
          class="form-control"
          v-model="email"
          placeholder="Email"
        />
        <div class="input-group-append">
          <div class="input-group-text">
            <span class="fas fa-envelope"></span>
          </div>
        </div>
      </div>
      <div class="input-group mb-3">
        <input
          type="password"
          class="form-control"
          v-model="password"
          placeholder="Password"
        />
        <div class="input-group-append">
          <div class="input-group-text">
            <span class="fas fa-lock"></span>
          </div>
        </div>
      </div>
      <div class="input-group mb-3" v-if="is_otp">
        <input
          type="password"
          class="form-control"
          v-model="otp"
          placeholder="Enter OTP"
        />
        <div class="input-group-append">
          <div class="input-group-text">
            <span class="fas fa-lock"></span>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-8">
          <div class="icheck-primary">
              <a class="cursor-pointer" @click="toggleForm()">Forgot Password?</a>
          </div>
        </div>
        <!-- /.col -->
        <div class="col-4">
          <button
            type="button"
            class="btn btn-success btn-block"
            @click="login"
          >
            Sign In
          </button>
        </div>
        <!-- /.col -->
      </div>
    </div>
    <!-- /.login-card-body -->
    <!-- /.forgot password-card-body -->
    <div v-else class="card-body login-card-body">
      <p class="login-box-msg">
        A Temporary Password Will Be Sent To Your Email
      </p>
      <div class="input-group mb-3">
        <input
          type="email"
          class="form-control"
          v-model="forgot_password_email"
          placeholder="Email"
        />
        <div class="input-group-append">
          <div class="input-group-text">
            <span class="fas fa-envelope"></span>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-6">
          <div class="icheck-primary">
            <a class="cursor-pointer" @click="toggleForm">Remember your password? Sign In instead.</a>
          </div>
        </div>
        <!-- /.col -->
        <div class="col-6">
          <!-- <button
          type="button"
          class="btn btn-success btn-block"
          @click="forgotPassword()"
          >
            Send
          </button> -->

          <TimerValidation
          v-model="timeLeft"
          label="Send"
          counter-label="Send After"
          :onClick="forgotPassword"
          />
        </div>
      </div>

      
    </div>
  </div>
</template>
<script>
import api from "@/api/auth.js";
import TimerValidation from './TimerValidation.vue'

export default {
  data() {
    return {
      email: "",
      password: "",
      is_otp: false,
      otp: "",
      forgot_password_email: "",
      forgot_password: false,
      btnSubmitFlag: false,
      timeLeft: null,
    };
  },
  components:{
    TimerValidation
  },
  created() {
    if (localStorage.getItem("token") !== null) {
      localStorage.removeItem("token");
      localStorage.removeItem("user");
    }
  },
  methods: {
    toggleForm() {
        if (!this.forgot_password) {
            this.forgot_password = true;
        } else {
            this.forgot_password = false;
        }
    },
    /**
     * calling login API
     */
    login() {
      let self = this;
      let request = {
        email: self.email,
        password: self.password,
      };
      if (self.is_otp) {
        request.otp = self.otp;
      }
      api
        .login(request)
        .then((response) => {
          if (response.code == 200) {
            //storing the authorization token into the local storage for future use
            localStorage.setItem("token", response.data.token);
            localStorage.setItem("user", JSON.stringify(response.data));
            axios.defaults.headers.common["Authorization"] =
              "Bearer " + localStorage.getItem("token");
            self.$router.push("/dashboard");
          } else {
            error(response.message);
          }
          if (response.data != null) {
            self.is_otp = response.data;
          }
        })
        .catch((err) => {
          error("Invalid Credentials!!");
          console.log(err);
        });
    },
    forgotPassword() {
      let self = this;
      self.btnSubmitFlag = true
      let request = {
        email: self.forgot_password_email,
      };
      api
        .forgotPassword(request)
        .then((response) => {
          if (response.code == 200) {
            if(response.data != null && response.data.time_left){
              self.timeLeft = response.data.time_left
            }
            self.forgot_password_email = "";
            success(response.message);
          }else{
              if(response.data != null && response.data.time_left){
                self.timeLeft = response.data.time_left
              }
              error(response.message);
          }
        })
        .catch((err) => {
          if(err.response.data.data != null && err.response.data.data.time_left){
            self.timeLeft = err.response.data.data.time_left
          }
          error(err.response.data.message);
          console.log(err);
        });
    }
  },
};
</script>
