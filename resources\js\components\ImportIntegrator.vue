<template>
  <div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <section class="content">
      <div class="container-fluid">
        <div class="row">
          <div class="col-md-12">
            <div class="card card-success">
              <div class="card-header">
                <h3 class="card-title">Import 3rd Party Integrators</h3>
                <b-button
                  class="btn-danger export-api-btn"
                  @click="reloadDatatable"
                  v-if="showReloadBtn"
                  >
                    <i class="fas fa-redo"></i> Reload
                  </b-button>
              </div>
              <div class="card-body">
                <div class="form-group">
                  <label for="exampleInputFile">Upload Excel Sheet</label>
                  <div class="input-group">
                    <div class="custom-file">
                      <input
                        type="file"
                        ref="file"
                        id="exampleInputFile"
                        v-on:change="handleFileUpload()"
                        class="custom-file-input"
                        accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
                      />
                      <label for="exampleInputFile" class="custom-file-label">{{
                        fileName
                      }}</label>
                    </div>
                    <div
                      class="input-group-append"
                      style="margin-left: 15px; margin-top: -2px"
                    >
                      <span class="btn btn-success" @click="importExcel"
                        >Import</span
                      >
                    </div>
                  </div>
                </div>
                <!-- /.card-header -->
                <div class="card-body">
                  <table
                    id="importIntegratorsTable"
                    class="table"
                    style="width: 100%; white-space: normal"
                  >
                    <thead>
                      <tr>
                        <th>Imported By</th>
                        <th>Imported On</th>
                      </tr>
                    </thead>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>
<script>
import api from "@/api/import.js";
import commonConstants from "@/common/constant.js";
export default {
  data() {
    return {
      count: 0,
      excel: "",
      fileName: "Choose File",
      showReloadBtn:false,
    };
  },
  methods: {
    reloadDatatable(){
      var self = this;
      self.loadDT();
    },
    /*Handles a change on the file upload*/
    handleFileUpload() {
      this.file = this.$refs.file.files[0];
      this.fileName = this.$refs.file.files[0].name;
    },

    /*Submits the file to the server*/
    importExcel() {
      /*Initialize the form data*/
      let formData = new FormData();
      formData.append("excel", this.file);

      /*call to the import excel api */
      api
        .importIntegrators(formData)
        .then((response) => {
          if (response.code == 200) {
            this.$refs.file.value = null;
            this.fileName = "Choose File";
            success(response.message);
            this.loadDT();
            //call to fetch the log api
          } else {
            error(response.message);
          }
        })
        .catch((err) => {
          console.log(err);
        });
    },
    loadDT: function () {
      var self = this;
      $("#importIntegratorsTable").DataTable({
        pagingType: "simple_numbers",
        processing: true,
        serverSide: true,
        destroy: true,
        columnDefs: [{ className: "dt-left", targets: [0, 1] }],
        order: [[1, "desc"]],
        orderClasses: false,
        language: {
          processing:
            '<i class="fa fa-spinner fa-spin fa-3x fa-fw"></i><span class="sr-only">Loading...</span> ',
          emptyTable: "No Imported Excel Available.",
          search: "_INPUT_",
          searchPlaceholder: "Search records",
          oPaginate: {
            sNext: '<i class="fas fa-angle-double-right"></i>',
            sPrevious: '<i class="fas fa-angle-double-left"></i>',
          },
          sLengthMenu:
            "<label class='label_dropdown_dt'>Per page</label> _MENU_",
        },
        ajax: {
          headers: {
            Authorization: "Bearer " + localStorage.getItem("token"),
          },
          url: "/api/import/merchantDetails",
          type: "GET",
          data: { excel_type: 1 },
          dataType: "json",
          dataSrc: function (result) {
            self.showReloadBtn = false;
            return result.data;
          },
          error: function(data){
            error(commonConstants.datatable_error);
            $('#importIntegratorsTable_processing').hide();
            self.showReloadBtn = true;
          }
        },
        columns: [{ data: "added_by" }, { data: "created_at" }],
      });

      $("#importIntegratorsTable").on("page.dt", function () {
        $("html, body").animate({ scrollTop: 0 }, "slow");
        $("th:first-child").focus();
      });
    },
  },
  mounted() {
    var self = this;
    setTimeout(function () {
      self.loadDT();
    }, 1000);
    document.title = "CanPay - Import Integrators";
  },
};
</script>
