<template>
<div>
  <div v-if="loading">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Valid Account Number</h3>
                </div>

                <div class="card-body">
                  <div class="row">
                    <div class="col-md-4">
                    <div class="form-group">
                     <input
                        class="form-control"
                        placeholder="Consumer Name (min 3 chars)"
                        id="consumer"
                        v-model="consumer"
                      />
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="form-control"
                        placeholder="Phone No (Exact)"
                        id="phone_no"
                        v-model="phone_no"
                      />
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="form-control"
                        placeholder="Email (Exact)"
                        id="email"
                        v-model="email"
                      />
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                     <input
                        class="form-control"
                        placeholder="Account Number (Exact)"
                        id="account_no"
                        v-model="account_no"
                      />
                    </div>
                  </div>
                </div>
                </div>
                <div class="card-footer">
                    <button
                      type="button"
                      class="btn btn-success"
                      @click="searchValidAccountNo()"
                    >
                    Search
                    </button>
                    <button
                    type="button"
                    @click="reset()"
                    class="btn btn-success margin-left-5"
                    >
                    Reset
                    </button>
                    <b-button
                        @click="openModal('add')"
                        class="btn btn-success margin-left-5"
                    >
                        <i class="fas fa-plus"></i> Add New
                    </b-button>
                </div>
                <div class="card-body">
                  <b-table-simple
                      responsive
                      show-empty
                      bordered
                      sticky-header="800px"
                      v-if="allAccountNumbers.length > 0"
                    >
                      <b-thead head-variant="light">
                        <tr>
                            <th>Consumer Name</th>
                            <th>Account Number</th>
                            <th class="text-center">Added On</th>
                            <th class="text-center">Action(s)</th>
                        </tr>
                      </b-thead>
                      <b-tbody v-for="(row, index) in allAccountNumbers" :key="index">
                        <b-tr>
                          <b-td class="text-left text-gray">{{
                            row.name
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.acc_no
                          }}</b-td>
                          <b-td class="text-center text-gray">{{
                            row.created_at
                          }}</b-td>
                          <b-td class="text-center text-gray">
                            <a @click="deleteValidAccountNo(row.edit)" class="custom-edit-btn" title="Delete Valid Account Number" variant="outline-success" style="border:none"><i class="nav-icon fas fa-trash"></i></a>
                          </b-td>
                        </b-tr>
                      </b-tbody>
                    </b-table-simple>
                    <p v-else>No data displayed. Please refine your search criteria.</p>
                    </div>


              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
    <!-- Add routing numbers modal start -->
    <b-modal
      id="routing-number-modal"
      ref="routing-number-modal"
      :title="modalTitle"
      @show="resetModal"
      @hidden="resetModal"
      ok-title="Save"
      ok-variant="success"
      cancel-variant="outline-secondary"
      @ok="handleOk"
      :no-close-on-esc="true"
      :no-close-on-backdrop="true"
    >

      <div class="row">
        <div class="col-12">
          <label for="account_no">
            Enter Consumer Name
            <span class="red">*</span>
          </label>
          <div class="form-group">
            <multiselect
              v-model="selectedConsumer"
              placeholder="Select Consumer (Min 3 chars)"
              id="consumer"
              label="consumer_name"
              :options="consumerList"
              :loading="isLoading"
              :internal-search="false"
              @search-change="getConsumers"
              v-validate="'required'"
            >
            </multiselect>
            <span v-show="errors.has('consumer')" class="text-danger">{{
              errors.first("consumer")
            }}</span>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-12">
          <label for="account_number">
            Enter Account Number
            <span class="red">*</span>
          </label>
          <input
            name="account_number"
            type="text"
            v-model="account_number"
            class="form-control"
            v-validate="'required'"
          />
          <span v-show="errors.has('account_number')" class="text-danger">{{
              errors.first("account_number")
            }}</span>
        </div>
      </div>
    </b-modal>
    <!-- Transaction cancellation comment modal end -->
  </div>
</div>
</template>
<script>
import api from "@/api/blacklistedaccount.js";
import { validationMixin } from "vuelidate";
import { required, minLength } from "vuelidate/lib/validators";
import apiTransaction from "@/api/transaction.js";
import commonConstants from "@/common/constant.js";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "./CustomLoader/CanPayLoader.vue"
export default {
  mixins: [validationMixin],
  data() {
    return {
      modalTitle: "",
      showMsg: false,
      headerTextVariant: "light",
      allAccountNumbers: {},
      account_no: "",
      showReloadBtn: false,
      constants: commonConstants,
      consumer_id: null,
      selectedConsumer: null,
      consumerList: [],
      isLoading: false,
      loading:false,
      account_number:"",
      consumer:"",
      phone_no:"",
      email:"",
    };
  },
  components: {
    HourGlass,
    CanPayLoader
  },
  created() {
  },
  methods: {
    //get the consumer list
    getConsumers(searchtxt) {
      var self = this;
      if(searchtxt.length >= 3){
        self.isLoading = true;
        var request = {
          searchtxt: searchtxt,
        };
        apiTransaction
          .getConsumers(request)
          .then(function (response) {
            if (response.code == 200) {
              self.consumerList = response.data;
              self.isLoading = false;
            } else {
              error(response.message);
            }
          })
          .catch(function (error) {
            error(error);
          });
      }
    },
    searchValidAccountNo(){
      var self = this;
      if((self.consumer).trim().length < 3 &&  $("#email").val().trim() === '' &&  $("#phone_no").val().trim() === '' &&  $("#account_no").val().trim() === ''){
        error("Please provide Customer Name (Min 3 chars) or email(exact) or phone no(exact) or account number(exact)");
        return false;
      }
      var request = {
        account_no: self.account_no,
        consumer: self.consumer,
        email:self.email,
        phone_no:self.phone_no,
      };
      self.loading = true;
      api
      .searchValidAccountNo(request)
      .then(function (response) {
        if (response.code == 200) {
          self.allAccountNumbers = response.data;
          self.loading = false;
        } else {
          error(response.message);
          self.loading = false;
        }
      })
      .catch(function (error) {
        // error(error);
        self.loading = false;
      });
    },
    deleteValidAccountNo(id){
        var self = this;
        var request = {
            id: id,
        };
        var r = confirm(
            "Do you want to delete this account number from valid list?"
        );
        if (r == true) {
            api
            .deleteValidAccountNo(request)
            .then((response) => {
            if (response.code == 200) {
                success(response.message);
                self.searchValidAccountNo();
            } else {
                error(response.message);
            }
            })
            .catch((err) => {
            error(err.response.data.message);
            });
        }
    },
    openModal(type) {
      var self = this;
      self.resetModal();
      self.modalTitle = "Add Valid Account Number";
      self.$bvModal.show("routing-number-modal");
    },
    resetModal() {
      var self = this;
      self.account_no = "";
    },
    handleOk(bvModalEvt) {
      var self = this;
      // Prevent modal from closing
      bvModalEvt.preventDefault();
      // Trigger submit handler
      self.save();
    },
    save() {
      var self = this;
      if(self.selectedConsumer === null){
        var consumer = '';
      }else{
        var consumer = self.selectedConsumer.user_id;
      }
      var request = {
        account_no: self.account_number,
        consumer: consumer,
      };
      this.$validator.validateAll().then((result) => {
        if (result) {
        api
            .addValidAccountNumber(request)
            .then((response) => {
            if (response.code == 200) {
                if(response.data == 1){
                    self.addAccountNumber();
                }else{
                    var r = confirm(response.message);
                    if (r == true) {
                        self.addAccountNumber();
                    }
                }
                self.$bvModal.hide("routing-number-modal");
                self.resetModal();
            } else {
                error(response.message);
            }
            })
            .catch((error) => {
                alert(error.response.data.message);
                error(error.response.data.message);
            });
            }
      });
    },
    addAccountNumber(){
      var self = this;
      if(self.selectedConsumer === null){
        var consumer = '';
      }else{
        var consumer = self.selectedConsumer.user_id;
      }
      var request = {
        account_no: self.account_number,
        consumer: consumer,
      };
      api
        .addAccountNumber(request)
        .then((response) => {
        if (response.code == 200) {
            success(response.message);
            self.$bvModal.hide("routing-number-modal");
            self.resetModal();
        } else {
            error(response.message);
        }
        self.account_number = "";
        self.consumerList = [];
        self.selectedConsumer = null;
        })
        .catch((error) => {
            error(error.response.data.message);
        });
    },
    reset(){
      var self = this;
      self.account_no = "";
      self.consumer = "";
      self.phone_no = "";
      self.email = "";
    }
  },
  mounted() {
    document.title = "CanPay - Valid Account Number";
  },
};
</script>

