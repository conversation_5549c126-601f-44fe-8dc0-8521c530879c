<template>
<div>
  <div v-if="is_visible == 1">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Post V1 Consumer Return</h3>
                  <b-button
                  class="btn-danger export-api-btn"
                  @click="reloadDatatable"
                  v-if="showReloadBtn"
                  >
                    <i class="fas fa-redo"></i> Reload
                  </b-button>
                </div>
                <!-- /.card-header -->
                <div class="card-body">
                  <div
                    class="alert alert-success alert-dismissible"
                    v-if="success_message != null"
                  >
                    <a
                      href="#"
                      class="close"
                      data-dismiss="alert"
                      aria-label="close"
                      style="text-decoration: none"
                      @click="success_message = null"
                      >&times;</a
                    >
                    <strong>Success!</strong> {{ success_message }}
                  </div>
                  <div class="form-group">
                    <label for="exampleInputFile"
                      >Upload Return Data</label
                    >
                    <button
                      type="button"
                      class="btn btn-danger ml-10"
                      style="float:right; margin-top:-12px;"
                      @click="downloadSampleFile();"
                    >
                      Download Sample <i
                        class="fa fa-download ml-10"
                        aria-hidden="true"
                      ></i>
                    </button>
                    <div class="input-group">
                      <div class="custom-file">
                        <input
                          type="file"
                          ref="return_post_file"
                          id="exampleInputFile"
                          v-on:change="handleFileUpload()"
                          class="custom-file-input"
                          accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
                        />
                        <label
                          for="exampleInputFile"
                          class="custom-file-label"
                          >{{ return_post_label }}</label
                        >
                      </div>
                    </div>
                  </div>
                  <span
                    style="float: right"
                    class="btn btn-success"
                    @click="postReturn"
                    >Post Return</span
                  >
                </div>
                <hr />
                <div class="card-body">
                  <h5>Return Post Log</h5>
                  <table
                    id="returnLogTable"
                    class="table"
                    style="width: 100%; white-space: normal"
                  >
                    <thead>
                      <tr>
                        <th>Status</th>
                        <th>Returns Posted</th>
                        <th>Skipped Records</th>
                        <th>Uploaded By</th>
                        <th>Posted On</th>
                        <th>Action</th>
                      </tr>
                    </thead>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</div>
</template>
<script>
import api from "@/api/return.js";
import { saveAs } from "file-saver";
import moment from "moment";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "./CustomLoader/CanPayLoader.vue"
import commonConstants from "@/common/constant.js";
export default {
  components: {
    HourGlass,
    CanPayLoader
  },
  data() {
    return {
      return_post: null,
      return_post_label: "Choose File",
      success_message: null,
      is_visible: 0,
      showReloadBtn:false,
      constants: commonConstants,
      allReturnLogs:{},
      returnLog:{}
    };
  },
  created() {
    this.downloadSkippedList();
  },
  methods: {
    reloadDatatable(){
      var self = this;
      self.loadDT();
    },
    handleFileUpload() {
      this.return_post = this.$refs.return_post_file.files[0];
      this.return_post_label = this.$refs.return_post_file.files[0].name;
    },
    downloadSkippedList() {
      var self = this;
      $(document).on("click", ".downloadSkippedList", function (e) {
        self.returnLog = self.allReturnLogs.find(
          (p) => p.batch_id == $(e.currentTarget).attr("data-batch-id")
        );
        
      console.log(self.returnLog);
      var request = {
        batch_id: self.returnLog.batch_id,
      };
      
      api
        .getReturnSkippedRecords(request)
        .then(function (response) {
          var FileSaver = require("file-saver");
          var blob = new Blob([response], {
            type: "application/xlsx",
          });
          FileSaver.saveAs(
            blob,
            moment().format("MM/DD/YYYY") +
              "_return_skipped_records.xlsx"
          );
        })
        .catch(function (error) {
          // error(error);
        });
      });
    },
    /*Submits the file to the server*/
    postReturn() {
      var self = this;
      self.is_visible = 1;
      if (
        self.return_post == null
      ) {
        self.is_visible = 0;
        error("Please upload the data sheet to post return.");
        return false;
      }
      /*Initialize the form data*/
      let formData = new FormData();
      formData.append(
        "return_post",
        self.return_post
      );
      /*call to the import excel api */
      api
        .postReturn(formData)
        .then((response) => {
          if (response.code == 200) {
            self.$refs.return_post_file.value = null;
            self.return_post = null;
            self.return_post_label = "Choose File";
            self.success_message = response.message;
            self.is_visible = 0;
            self.loadDT();
          } else {
            error(response.message);
            self.is_visible = 0;
          }
        })
        .catch((response) => {
          error(response);
          self.is_visible = 0;
        });
    },
    loadDT: function () {
      var self = this;
      $("#returnLogTable").DataTable({
        searching: false,
        pagingType: "simple_numbers",
        processing: true,
        serverSide: true,
        destroy: true,
        columnDefs: [
          { orderable: false, targets: [0,5] },
          { className: "dt-left", targets: [0, 1, 2, 3, 4] },
          { className: "dt-center", targets: [5] },
        ],
        order: [[4, "desc"]],
        orderClasses: false,
        bLengthChange: false,
        bPaginate: false,
        language: {
          processing:
            '<i class="fa fa-spinner fa-spin fa-3x fa-fw"></i><span class="sr-only">Loading...</span> ',
          emptyTable: "No Return Log Available.",
          search: "_INPUT_",
          searchPlaceholder: "Search records",
          oPaginate: {
            sNext: '<i class="fas fa-angle-double-right"></i>',
            sPrevious: '<i class="fas fa-angle-double-left"></i>',
          },
          sLengthMenu:
            "<label class='label_dropdown_dt'>Per page</label> _MENU_",
        },
        ajax: {
          headers: {
            Authorization: "Bearer " + localStorage.getItem("token"),
          },
          url: "/api/getreturnlog",
          type: "POST",
          data: { _token: "{{csrf_token()}}", type: "ReturnPost" },
          dataType: "json",
          dataSrc: function (result) {
            self.showReloadBtn = false;
            self.allReturnLogs = result.data;
            return self.allReturnLogs;
          },
          error: function(data){
            error(commonConstants.datatable_error);
            $('#returnLogTable_processing').hide();
            self.showReloadBtn = true;
          }
        },
        columns: [
          { data: "summary" },
          { data: "actual_data_imported" },
          { data: "duplicate_date_imported" },
          { data: "uploaded_by" },
          { data: "created_at" },
          {
            render: function (data, type, full, meta) {
              return (
                '<b-button data-batch-id="' +
                full.batch_id +
                '" class="downloadSkippedList custom-edit-btn" title="Download Skipped List" variant="outline-success"><i class="nav-icon fas fa-download"></i>&nbsp;&nbsp;</b-button>'
              );
            }
          },
        ],
      });

      $("#returnLogTable").on("page.dt", function () {
        $("html, body").animate({ scrollTop: 0 }, "slow");
        $("th:first-child").focus();
      });

      //Search in the table only after 3 characters are typed
      // Call datatables, and return the API to the variable for use in our code
      // Binds datatables to all elements with a class of datatable
      var dtable = $("#returnLogTable").dataTable().api();

      // Grab the datatables input box and alter how it is bound to events
      $(".dataTables_filter input")
      .unbind() // Unbind previous default bindings
      .bind("input", function(e) { // Bind our desired behavior
          // If the length is 3 or more characters, or the user pressed ENTER, search
          if(this.value.length >= 3 || e.keyCode == 13) {
              // Call the API search function
              dtable.search(this.value).draw();
          }
          // Ensure we clear the search if they backspace far enough
          if(this.value == "") {
              dtable.search("").draw();
          }
          return;
      });
    },
    downloadSampleFile(){
      window.location.href = "sample_import_excel/Sample_Return_Data.csv";
    }
  },
  mounted() {
    var self = this;
    setTimeout(function () {
      self.loadDT();
    }, 1000);
    document.title = "CanPay - Post Consumer Return";
  },
};
</script>
