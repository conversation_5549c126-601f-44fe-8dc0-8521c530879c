<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;


class ValidationLog extends Model
{


    protected $table = 'validation_log_table';

    /**
     * __construct
     * This function will insert an alphanumeric 32 character unique id as primary key in the table whenever a data get inserted
     * @param  mixed $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        $this->id = generateUUID();

        parent::__construct($attributes);
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'api',
        'phone',
        'first_name',
        'last_name',
        'date_of_birth',
        'ssn',
        'response',
        'type',
        'session_id',
        'account_id',
        'response_code'
    ];
    public $incrementing = false;
}
