<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class MicrobiltResponseDetail extends Model
{

    /**
     * __construct
     * This function will insert an alphanumeric 32 character unique id as primary key in the table whenever a data get inserted
     * @param  mixed $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        $this->id = generateUUID();

        parent::__construct($attributes);
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'id',
        'microbilt_response_id',
        'consumer_id',
        'request_firstname',
        'request_lastname',
        'request_email',
        'request_phone',
        'request_address',
        'request_city',
        'request_zipcode',
        'request_state',
        'request_bankname',
        'request_routing_no',
        'request_account_no',
        'type',
        'bank_link_type',
        'phone',
        'account_no',
        'routing_no',
        'source',
        'rquid',
        'status_status_code',
        'status_severity',
        'status_status_desc',
        'response_status_application_number',
        'response_status_action',
        'response_status_type',
        'decision_code',
        'decision_value',
        'reasons',
        'properties_firstname1',
        'properties_lastname1',
        'properties_firstname2',
        'properties_lastname2',
        'properties_firstnamematch',
        'properties_lastnamematch',
        'properties_telephone1',
        'properties_telephone2',
        'properties_negativetransactions',
        'properties_positivetransactions',
        'properties_datelastunpaiditem',
        'properties_lastseen',
        'properties_firstseen',
        'properties_positiveamount',
        'properties_dollarnegativelinkeddl',
        'properties_noofnegativetxnsonlinkeddl',
        'properties_averagedaystopay',
        'properties_paidamount',
        'properties_paidtransactions',
        'properties_negativeamount',
        'properties_category',
        'properties_messagestr',
        'properties_noofunauthorizeditemsin12months'
    ];
    public $timestamps = true;
    public $incrementing = false;
    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
}
