<template>
<div>
  <div v-if="loading">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px;">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Return Dashboard</h3>
                </div>
 
                <div class="card-body">
                  <div class="row">
                  <div class="col-md-6">
                    <div class="form-group">
                      <input
                        class="start-date form-control"
                        placeholder="Start Date"
                        id="start-date"
                        onkeydown="return false"
                        autocomplete="off"
                      />
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-group">
                      <input
                        class="end-date form-control"
                        placeholder="End Date"
                        id="end-date"
                        onkeydown="return false"
                        autocomplete="off"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div class="card-footer">
                <button
                  type="button"
                  class="btn btn-success"
                  id="generateBtn"
                  @click="generateReport()"
                >
                  Generate
                </button>
                <button
                  type="button"
                  @click="exportReport()"
                  class="btn btn-danger ml-10"
                >
                  Export <i
                    class="fa fa-download ml-10"
                    aria-hidden="true"
                  ></i>
                </button>
              </div>
                <!-- /.card-header -->
                <div class="card-body">
                  <div class="row">
                  <div class="col-12">
                    <b-table-simple
                      responsive
                      show-empty
                      bordered
                      sticky-header="800px"
                    >
                      <b-thead>
                        <b-tr>
                          <b-th class="text-center background-gray">&nbsp;</b-th>
                          <b-th class="text-center background-green">Transaction Value</b-th>
                          <b-th class="text-center background-green">%</b-th>
                          <b-th class="text-center background-green">Count</b-th>
                          <b-th class="text-center background-green">Average Transaction Value</b-th>
                          <b-th class="text-center background-blue">Paid</b-th>
                          <b-th class="text-center background-blue">%</b-th>
                          <b-th class="text-center background-blue">Count</b-th>
                          <b-th class="text-center background-blue">Count %</b-th>
                          <b-th class="text-center background-yellow">Pending</b-th>
                          <b-th class="text-center background-yellow">%</b-th>
                          <b-th class="text-center background-yellow">Count</b-th>
                          <b-th class="text-center background-yellow">Count %</b-th>
                          <b-th class="text-center background-red">Represented But Returned</b-th>
                          <b-th class="text-center background-red">%</b-th>
                          <b-th class="text-center background-red">Count</b-th>
                          <b-th class="text-center background-red">Count %</b-th>
                          <b-th class="text-center background-purple">Not Represented</b-th>
                          <b-th class="text-center background-purple">%</b-th>
                          <b-th class="text-center background-purple">Count</b-th>
                          <b-th class="text-center background-purple">Count %</b-th>
                        </b-tr>
                      </b-thead>
                      <b-tbody >
                        <b-tr v-for="(row, index) in report_body" :key="index">
                          <b-td class="text-left text-bold background-gray" v-if="report_body.length == index+1">Total Returns</b-td>
                          <b-td class="text-left text-bold background-gray" v-else>{{row.heading}}</b-td>
                          <b-td :class="(report_body.length == index+1 || index == 0)?'text-right text-bold background-green':'text-right background-green'">{{row.total_transaction!= null ? '$'+ numberWithCommas(row.total_transaction) : row.total_transaction}}</b-td>
                          <b-td :class="(report_body.length == index+1 || index == 0)?'text-right text-bold background-green':'text-right background-green'">{{row.transaction_percent}}</b-td>
                          <b-td :class="(report_body.length == index+1 || index == 0)?'text-right text-bold background-green':'text-right background-green'">{{numberWithCommas(row.transaction_count)}}</b-td>
                          <b-td :class="(report_body.length == index+1 || index == 0)?'text-right text-bold background-green':'text-right background-green'">{{row.avg_transaction_value!= null ? '$'+numberWithCommas(row.avg_transaction_value) : row.avg_transaction_value}}</b-td>
                          <b-td :class="(report_body.length == index+1 || index == 0)?'text-right text-bold background-blue':'text-right background-blue'">{{row.paid_amount!= null ? '$'+numberWithCommas(row.paid_amount) : row.paid_amount}}</b-td>
                          <b-td :class="(report_body.length == index+1 || index == 0)?'text-right text-bold background-blue':'text-right background-blue'">{{row.paid_percent}}</b-td>
                          <b-td :class="(report_body.length == index+1 || index == 0)?'text-right text-bold background-blue':'text-right background-blue'">{{numberWithCommas(row.paid_count)}}</b-td>
                          <b-td :class="(report_body.length == index+1 || index == 0)?'text-right text-bold background-blue':'text-right background-blue'">{{row.paid_count_percent}}</b-td>
                          <b-td :class="(report_body.length == index+1 || index == 0)?'text-right text-bold background-yellow':'text-right background-yellow'">{{row.pending_amount!= null ? '$'+numberWithCommas(row.pending_amount) : row.pending_amount}}</b-td>
                          <b-td :class="(report_body.length == index+1 || index == 0)?'text-right text-bold background-yellow':'text-right background-yellow'">{{row.pending_percent}}</b-td>
                          <b-td :class="(report_body.length == index+1 || index == 0)?'text-right text-bold background-yellow':'text-right background-yellow'">{{numberWithCommas(row.pending_count)}}</b-td>
                          <b-td :class="(report_body.length == index+1 || index == 0)?'text-right text-bold background-yellow':'text-right background-yellow'">{{row.pending_count_percent}}</b-td>
                          <b-td :class="(report_body.length == index+1 || index == 0)?'text-right text-bold background-red':'text-right background-red'">{{row.represented_amount!= null ? '$'+numberWithCommas(row.represented_amount) : row.represented_amount}}</b-td>
                          <b-td :class="(report_body.length == index+1 || index == 0)?'text-right text-bold background-red':'text-right background-red'">{{row.represented_percent}}</b-td>
                          <b-td :class="(report_body.length == index+1 || index == 0)?'text-right text-bold background-red':'text-right background-red'">{{numberWithCommas(row.represented_count)}}</b-td>
                          <b-td :class="(report_body.length == index+1 || index == 0)?'text-right text-bold background-red':'text-right background-red'">{{row.represented_count_percent}}</b-td>
                          <b-td :class="(report_body.length == index+1 || index == 0)?'text-right text-bold background-purple':'text-right background-purple'">{{row.not_represented_amount!= null ? '$'+numberWithCommas(row.not_represented_amount) : row.not_represented_amount}}</b-td>
                          <b-td :class="(report_body.length == index+1 || index == 0)?'text-right text-bold background-purple':'text-right background-purple'">{{row.not_represented_percent}}</b-td>
                          <b-td :class="(report_body.length == index+1 || index == 0)?'text-right text-bold background-purple':'text-right background-purple'">{{numberWithCommas(row.not_represented_count)}}</b-td>
                          <b-td :class="(report_body.length == index+1 || index == 0)?'text-right text-bold background-purple':'text-right background-purple'">{{row.not_represented_count_percent}}</b-td>
                        </b-tr>
                        <b-tr v-if="report_footer.length!=0">
                          <b-td class="text-left text-bold background-red">Average Ticket</b-td>
                          <b-td class="text-right background-red">{{"$"+numberWithCommas(report_footer.avg_return_value)}}</b-td>
                          <b-td class="text-right background-red"></b-td>
                          <b-td class="text-right background-red"></b-td>
                          <b-td class="text-right background-red"></b-td>
                          <b-td class="text-right background-red"></b-td>
                          <b-td class="text-right background-red"></b-td>
                          <b-td class="text-right background-red"></b-td>
                          <b-td class="text-right background-red"></b-td>
                          <b-td class="text-right background-red"></b-td>
                          <b-td class="text-right background-red"></b-td>
                          <b-td class="text-right background-red"></b-td>
                          <b-td class="text-right background-red"></b-td>
                          <b-td class="text-right background-red"></b-td>
                          <b-td class="text-right background-red"></b-td>
                          <b-td class="text-right background-red"></b-td>
                          <b-td class="text-right background-red"></b-td>
                          <b-td class="text-right background-red"></b-td>
                          <b-td class="text-right background-red"></b-td>
                          <b-td class="text-right background-red"></b-td>
                          <b-td class="text-right background-red"></b-td>
                        </b-tr>
                        <b-tr v-if="report_footer.length!=0">
                          <b-td class="text-left text-bold background-red">Unpaid Transactions</b-td>
                          <b-td class="text-right background-red">{{"$"+numberWithCommas(report_footer.unpaid_transaction_amount)}}</b-td>
                          <b-td class="text-right background-red">{{report_footer.unpaid_transaction_percent+"%"}}</b-td>
                          <b-td class="text-right background-red"></b-td>
                          <b-td class="text-right background-red"></b-td>
                          <b-td class="text-right background-red"></b-td>
                          <b-td class="text-right background-red"></b-td>
                          <b-td class="text-right background-red"></b-td>
                          <b-td class="text-right background-red"></b-td>
                          <b-td class="text-right background-red"></b-td>
                          <b-td class="text-right background-red"></b-td>
                          <b-td class="text-right background-red"></b-td>
                          <b-td class="text-right background-red"></b-td>
                          <b-td class="text-right background-red"></b-td>
                          <b-td class="text-right background-red"></b-td>
                          <b-td class="text-right background-red"></b-td>
                          <b-td class="text-right background-red"></b-td>
                          <b-td class="text-right background-red"></b-td>
                          <b-td class="text-right background-red"></b-td>
                          <b-td class="text-right background-red"></b-td>
                          <b-td class="text-right background-red"></b-td>
                        </b-tr>
                        <b-tr v-if="report_footer.length!=0">
                          <b-td class="text-left text-bold background-yellow">Pending Transactions</b-td>
                          <b-td class="text-right background-yellow">{{"$"+numberWithCommas(report_footer.pending_transaction_amount)}}</b-td>
                          <b-td class="text-right background-yellow">{{report_footer.pending_transaction_percent+"%"}}</b-td>
                          <b-td class="text-right background-yellow"></b-td>
                          <b-td class="text-right background-yellow"></b-td>
                          <b-td class="text-right background-yellow"></b-td>
                          <b-td class="text-right background-yellow"></b-td>
                          <b-td class="text-right background-yellow"></b-td>
                          <b-td class="text-right background-yellow"></b-td>
                          <b-td class="text-right background-yellow"></b-td>
                          <b-td class="text-right background-yellow"></b-td>
                          <b-td class="text-right background-yellow"></b-td>
                          <b-td class="text-right background-yellow"></b-td>
                          <b-td class="text-right background-yellow"></b-td>
                          <b-td class="text-right background-yellow"></b-td>
                          <b-td class="text-right background-yellow"></b-td>
                          <b-td class="text-right background-yellow"></b-td>
                          <b-td class="text-right background-yellow"></b-td>
                          <b-td class="text-right background-yellow"></b-td>
                          <b-td class="text-right background-yellow"></b-td>
                          <b-td class="text-right background-yellow"></b-td>
                        </b-tr>
                        <b-tr v-if="report_footer.length!=0">
                          <b-td class="text-left text-bold background-yellow">Paid Transactions</b-td>
                          <b-td class="text-right background-yellow">{{"$"+numberWithCommas(report_footer.paid_transaction_amount)}}</b-td>
                          <b-td class="text-right background-yellow">{{report_footer.paid_transaction_percent+"%"}}</b-td>
                          <b-td class="text-right background-yellow"></b-td>
                          <b-td class="text-right background-yellow"></b-td>
                          <b-td class="text-right background-yellow"></b-td>
                          <b-td class="text-right background-yellow"></b-td>
                          <b-td class="text-right background-yellow"></b-td>
                          <b-td class="text-right background-yellow"></b-td>
                          <b-td class="text-right background-yellow"></b-td>
                          <b-td class="text-right background-yellow"></b-td>
                          <b-td class="text-right background-yellow"></b-td>
                          <b-td class="text-right background-yellow"></b-td>
                          <b-td class="text-right background-yellow"></b-td>
                          <b-td class="text-right background-yellow"></b-td>
                          <b-td class="text-right background-yellow"></b-td>
                          <b-td class="text-right background-yellow"></b-td>
                          <b-td class="text-right background-yellow"></b-td>
                          <b-td class="text-right background-yellow"></b-td>
                          <b-td class="text-right background-yellow"></b-td>
                          <b-td class="text-right background-yellow"></b-td>
                        </b-tr>
                      </b-tbody>
                    </b-table-simple>
                  </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>

  </div>
</div>
</template>
<script>
import api from "@/api/reports.js";
import moment from "moment";
import { validationMixin } from "vuelidate";
import { required, minLength } from "vuelidate/lib/validators";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "./CustomLoader/CanPayLoader.vue"
export default {
  mixins: [validationMixin],
  data() {
    return {
      loading: false,
      report_body: [],
      report_footer: [],
    };
  },
  components: {
    HourGlass,
    CanPayLoader
  },
  created() {
  },
  methods: {
    numberWithCommas(x) {
        if(x){
            return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        }
        return x;
    },
    // API call to generate the report
    generateReport() {
      var self = this;
      if($("#end-date").val() == '' &&  $("#start-date").val() == ''){
        error("Please select Start Date & End Date then Generate.");
        return false;
      }
      if (
        moment($("#start-date").val()).format("YYYY-MM-DD") >
        moment().format("YYYY-MM-DD") && $("#start-date").val()!= ''
      ) {
        error("Start date cannot be from future.");
        return false;
      }
      if (
        moment($("#end-date").val()).format("YYYY-MM-DD") >
        moment().format("YYYY-MM-DD") && $("#end-date").val()!= ''
      ) {
        error("End date cannot be from future.");
        return false;
      }
      if($("#start-date").val()!=''){
        var from_date = moment($("#start-date").val()).format("YYYY-MM-DD");
      }else{
        var from_date = '';
      }
      if($("#end-date").val()!=''){
        var to_date = moment($("#end-date").val()).format("YYYY-MM-DD");
      }else{
        var to_date = '';
      }
      self.report = [];
      var request = {
        from_date: from_date,
        to_date: to_date
      };
      if(request.from_date > request.to_date){
        error("To Date cannot be greater than From date");
        return false;
      }
      self.loading = true;
      api
        .generateReturnDashboardReport(request)
        .then(function (response) {
          if (response.code == 200) {
            self.report_body = response.data.report_body;
            self.report_footer = response.data.report_footer;
            if(self.report_body.length > 0){
              self.loading = false;
            }else {
              error("No records found!");
              self.loading = false;
            }
          } else {
            error(response.message);
            self.loading = false;
          }
        })
        .catch(function (error) {
          // error(error);
          self.loading = false;
        });
    },

    // exports the report
    exportReport() {
      var self = this;
      self.loading = true;
      if($("#end-date").val() == '' &&  $("#start-date").val() == ''){
        error("Please select Start Date & End Date then Generate.");
        return false;
      }
      if($("#start-date").val()!=''){
        var from_date = moment($("#start-date").val()).format("YYYY-MM-DD");
      }else{
        var from_date = '';
      }
      if($("#end-date").val()!=''){
        var to_date = moment($("#end-date").val()).format("YYYY-MM-DD");
      }else{
        var to_date = '';
      }

      var request = {
        from_date : from_date,
        to_date   : to_date,
      };
      api
        .generateReturnDashboardReportExport(request)
        .then(function (response) {
          var FileSaver = require("file-saver");
          var blob = new Blob([response], {
            type: "application/xlsx",
          });
          FileSaver.saveAs(
            blob,
            moment().format("MM/DD/YYYY") + "_return_dashboard_report.xlsx"
          );
          self.loading = false;
        })
        .catch(function (error) {
          // error(error);
          self.loading = false;
        });
    },
    dateDiff(){
      if ($("#start-date").val() != "") {
        var from_date = moment($("#start-date").val()).format("YYYY-MM-DD");
      } else {
        var from_date = "";
      }
      if ($("#end-date").val() != "") {
        var to_date = moment($("#end-date").val()).format("YYYY-MM-DD");
      } else {
        var to_date = "";
      }
      if(from_date!='' && to_date!=''){
        //calculate the date Difference
        var date1 = new Date(from_date);
        var date2 = new Date(to_date);
        // To calculate the time difference of two dates
        var Difference_In_Time = date2.getTime() - date1.getTime();

        // To calculate the no. of days between two dates
        var Difference_In_Days = Difference_In_Time / (1000 * 3600 * 24);

        if(Difference_In_Days > 90){
          $("#generateBtn").prop('disabled', true);
        }else{
          $("#generateBtn").prop('disabled', false);
        }
      }
    },
  },
  mounted() {
    var self = this;
    $("#start-date").datepicker({
      format: "mm/dd/yyyy",
      autoclose: true,
      todayHighlight: true,
    }).on('changeDate', function (ev) {
        self.dateDiff();
    });
    $("#end-date").datepicker({
      format: "mm/dd/yyyy",
      autoclose: true,
      todayHighlight: true,
    }).on('changeDate', function (ev) {
        self.dateDiff();
    });
    $("#start-date , #end-date").datepicker("setDate", new Date());
  },
};
</script>


