<?php

namespace App\Http\Controllers;

use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SettingController extends Controller
{
    public function __construct(Request $request)
    {
        $this->request = $request;
    }
    /**
     * getSettingsValue
     * This function will send the value for a specified Cron Job
     * @param  mixed $name
     * @param  mixed $defaultValue
     * @return void
     */
    public function getSettingsValue($name, $defaultValue)
    {
        $user_details = Auth::user(); // Fetching user details for logging purpose
        $settings = Setting::where('name', $name)->first();
        if (!empty($settings)) {
            $returnValue = $settings->val;
        }
        $returnValue = $defaultValue; // sending the dafault value specified as the query return null result
        Log::channel('system-settings')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Setting value for: " . $name . " Fetched Successfully by " . $user_details->email);
        $message = trans('message.setting_value_fetch_success');
        return renderResponse(SUCCESS, $message, $returnValue); // API Response returned with 200 status
    }

    /**
     * getAllSettingsValue
     * This function will send all the data from settings table
     * @return void
     */
    public function getAllSettingsValue()
    {
        $user_details = Auth::user(); // Fetching user details for logging purpose
        $settings = Setting::where('type', 'others')->orderBy('created_at')->get();
        Log::channel('system-settings')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "All Setting values Fetched Successfully by " . $user_details->email);
        $message = trans('message.setting_value_fetch_success');
        return renderResponse(SUCCESS, $message, $settings); // API Response returned with 200 status
    }

    /**
     * updateSettingsValue
     * This function will update all the values received with respect to the ids received in request
     * @param  mixed $request
     * @return void
     */
    public function updateSettingsValue(Request $request)
    {
        $this->_validateData(); // Validating input request
        $user_details = Auth::user(); // Fetching user details for logging purpose
        foreach ($request->all() as $key => $value) {
            $setting = Setting::where(['name' => $key])->first();
            if (!empty($setting)) {
                $old_val = $setting->val;
                // Monitoring the change in value for updation
                if ($old_val != $value) {
                    $setting->val = $value;
                    $setting->save();
                    Log::channel('system-settings')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Setting value for " . $setting->name . " updated from " . $old_val . " to " . $value . " Successfully by " . $user_details->email);
                } else {
                    continue;
                }
            } else {
                Log::channel('system-settings')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") -  Updation Skipped. Trying to update " . $key . " with value " . $value . " by user " . $user_details->email);
            }
        }
        $settings = Setting::get();
        Log::channel('system-settings')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "All Setting values updated Successfully by " . $user_details->email);
        $message = trans('message.setting_value_update_success');
        return renderResponse(SUCCESS, $message, $settings); // API Response returned with 200 status
    }

    /**
     * _validateData
     * This function will validate the request received in the updateSettingsValue function
     * @return void
     */
    private function _validateData()
    {
        $rule = array(
            'enable_acheck21_consumer_debit_process' => VALIDATION_REQUIRED . '|numeric|min:0|max:1',
        );
        $this->__validate($this->request->all(), $rule);
    }
}
