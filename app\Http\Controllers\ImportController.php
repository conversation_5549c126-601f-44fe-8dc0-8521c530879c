<?php

// PostController.php

namespace App\Http\Controllers;

use App\Http\Controllers\ManualReviewControllers;
use App\Imports\BankProviderImport;
use App\Imports\CorporateParentImport;
use App\Imports\IntegratorsImport;
use App\Imports\MerchantsImport;
use App\Imports\MxInstitutionCodeImport;
use App\Models\ImportExcelLog;
use App\Models\MigrationLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Storage;

class ImportController extends Controller
{

    public function __construct(Request $request)
    {
        $this->request = $request;
    }
    /**
     * Import canpay merchants to database
     */
    public function importMerchantExcel()
    {
        $excel = $this->request->file('excel');
        if (empty($excel)) {
            $message = trans('message.empty_excel');
            return renderResponse(FAIL, $message, null);
        }
        $allowed = array('xls', 'xlsx', 'csv');
        if (!in_array($excel->getClientOriginalExtension(), $allowed)) {
            $message = trans('message.invalid_excel');
            return renderResponse(FAIL, $message, null);
        }
        $import = new MerchantsImport();
        Excel::import($import, $excel);
        $commaSeparatedIds = '';
        $storeIds = $import->getInsertedStoreIds();
        if(!empty($storeIds)) {
            $commaSeparatedIds = implode(',', $storeIds);
        }
        $message = trans('message.excel_import_success');
        //adding details to import excel log table
        DB::beginTransaction();
        try {
            $importExcel = new ImportExcelLog();
            $importExcel->summary = 'Success';
            $importExcel->data_imported = $import->getRowCount();
            $importExcel->added_by = Auth::user()->user_id;
            $importExcel->save();
            DB::commit();
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured during Database Insertion.", [EXCEPTION => $e]);
            DB::rollback();
        }
        return renderResponse(SUCCESS, $message, $commaSeparatedIds);
    }
    /**
     * Get Import Excel Log
     */
    public function getMerchantImportExcelLog()
    {
        // Columns defined for Sorting
        $columns = array(
            0 => 'import_excel_log.summary',
            1 => 'import_excel_log.data_imported',
            2 => 'import_excel_log.added_by',
            3 => 'import_excel_log.created_at',
        );
        $limit = intval($this->request->input('length'));
        $start = intval($this->request->input('start'));
        $order = $columns[$this->request->input('order.0.column')];
        $dir = $this->request->input('order.0.dir');
        // Main Query
        $query = ImportExcelLog::on(MYSQL_RO)->join('users', 'users.user_id', '=', 'import_excel_log.added_by')->select('users.first_name', 'users.middle_name', 'users.last_name', 'import_excel_log.*')->where('import_excel_log.type', $this->request->get("excel_type"));

        //Count Query
        $queryCount = ImportExcelLog::on(MYSQL_RO)->join('users', 'users.user_id', '=', 'import_excel_log.added_by')
            ->selectRaw('COUNT(*) as total_count')
            ->where('import_excel_log.type', $this->request->get("excel_type"));

        $totalData = $queryCount->first()->total_count; // Getting total no of rows
        $totalFiltered = $totalData;

        if (empty($this->request->input('search.value')) && empty($order) && empty($dir)) {
            $logs = $query->offset($start)->limit(intval($limit))->orderBy('import_excel_log.created_at', 'DESC')->get();
        } else if (empty($this->request->input('search.value'))) {
            $logs = $query->offset($start)->limit(intval($limit))->orderBy($order, $dir)->get();
        } else {
            $search = $this->request->input('search.value');
            $search_query = $query->where('import_excel_log.id', 'LIKE', "%{$search}%")
                ->orWhereRaw("concat(users.first_name, ' ', users.middle_name, ' ', users.last_name) LIKE ? ", ['%' . $search . '%'])
                ->orWhere('import_excel_log.summary', 'LIKE', "%{$search}%")
                ->orWhere('import_excel_log.data_imported', 'LIKE', "%{$search}%")
                ->orWhere('import_excel_log.created_at', 'LIKE', "%{$search}%")
                ->offset($start)
                ->limit($limit)
                ->orderBy($order, $dir);

            $logs = $search_query->get();
            $totalFiltered = $search_query->count();
        }
        $data = array();
        if (!empty($logs)) {
            // Creating array to show the values in frontend
            foreach ($logs as $log) {
                $nestedData['summary'] = $log->summary;
                $nestedData['data_imported'] = $log->data_imported;
                $nestedData['added_by'] = $log->first_name . ' ' . $log->middle_name . ' ' . $log->last_name;
                $nestedData['created_at'] = date('m-d-Y h:i A', strtotime($log->created_at));
                $data[] = $nestedData;
            }
        }
        // Drawing the Datatable
        $json_data = array(
            "draw" => intval($this->request->input('draw')),
            "recordsTotal" => intval($totalData),
            "recordsFiltered" => intval($totalFiltered),
            "data" => $data,
        );
        Log::info(addslashes(__METHOD__) . "(" . LINE . __LINE__ . ") : Import Excel Log fetched successfully");
        echo json_encode($json_data); // Rerurning the data
    }

    /**
     * Import corporate logo to database
     */
    public function importCorporateLogo()
    {
        $logo = $this->request->file('logo');
        if (empty($logo)) {
            $message = trans('message.empty_excel');
            return renderResponse(FAIL, $message, null);
        }
        $allowed = array('png', 'jpeg', 'jpg', 'gif');
        if (!in_array($logo->getClientOriginalExtension(), $allowed)) {
            $message = trans('message.invalid_logo');
            return renderResponse(FAIL, $message, null);
        }
        try {
            $thePath = Storage::disk('s3_logo')->putFile('/logos', $logo);
            $theUrl = Storage::disk('s3_logo')->url($thePath);
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while uploading logo.", [EXCEPTION => $e]);
            $message = trans('message.upload_logo_fail');
            return renderResponse(FAIL, $message, null);
        }
        $message = trans('message.upload_logo_success');
        return renderResponse(SUCCESS, $message, $theUrl);
    }

    /**
     * Import bank logo to database
     */
    public function importBankLogo()
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Import bank logo started...");
        $logo = $this->request->file('logo');
        if (empty($logo)) {
            $message = trans('message.empty_excel');
            return renderResponse(FAIL, $message, null);
        }
        $allowed = array('png', 'jpeg', 'jpg', 'gif');
        if (!in_array($logo->getClientOriginalExtension(), $allowed)) {
            $message = trans('message.invalid_logo');
            return renderResponse(FAIL, $message, null);
        }
        try {
            $thePath = Storage::disk('s3_logo')->putFile('/logos', $logo);
            $theUrl = Storage::disk('s3_logo')->url($thePath);
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while uploading logo.", [EXCEPTION => $e]);
            $message = trans('message.upload_bank_logo_fail');
            return renderResponse(FAIL, $message, null);
        }
        $message = trans('message.upload_bank_logo_success');
        return renderResponse(SUCCESS, $message, $theUrl);
    }

    /**
     * Import canpay merchants to database
     */
    public function importIntegratorExcel()
    {
        $excel = $this->request->file('excel');
        if (empty($excel)) {
            $message = trans('message.empty_excel');
            return renderResponse(FAIL, $message, null);
        }
        $allowed = array('xls', 'xlsx', 'csv');
        if (!in_array($excel->getClientOriginalExtension(), $allowed)) {
            $message = trans('message.invalid_excel');
            return renderResponse(FAIL, $message, null);
        }

        Excel::import(new IntegratorsImport(), $excel);
        $message = trans('message.excel_import_success');
        // adding details to import excel log table
        try {
            $importExcel = new ImportExcelLog();
            $importExcel->file_name = $excel->getClientOriginalName();
            $importExcel->added_by = Auth::user()->user_id;
            $importExcel->type = 1;
            $importExcel->save();
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured during Database Insertion.", [EXCEPTION => $e]);
        }
        return renderResponse(SUCCESS, $message, null);
    }

    /**
     * importCorporateParentFromExcel
     * This function is used to import transactions for consumers in V2 system from V1
     * @return void
     */
    public function importCorporateParentFromExcel()
    {
        DB::beginTransaction();
        try {
            ini_set('max_execution_time', 6000);
            $corporate_parents = $this->request->file('corporate_parents');

            //check if both the sheets are being uploaded or not
            if (empty($corporate_parents)) {
                $message = "Please upload the data sheets in order to import Corporate Parents.";
                return renderResponse(FAIL, $message, null);
            }
            //====================Code Segment For Parsing Corporate Parents==========================
            if (!empty($corporate_parents)) {
                Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Corporate Parents excel upload done and import started");
                $import = new CorporateParentImport();
                Excel::import($import, $corporate_parents);
                Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Corporate Parents excel import completed");
            }

            // Insertion in Migraion Log table Started
            $count = explode('|', $import->getRowCount());
            $log = new MigrationLog();
            $log->type = 'Corporate Parent';
            $log->summary = 'Success';
            $log->actual_data_imported = $count[0];
            $log->duplicate_date_imported = $count[1];
            $log->migrated_data_updated = $count[2];
            $log->uploaded_by = Auth::user()->user_id;
            $log->save();

            DB::commit();

            $message = "Total " . $count[0] . " Corporate Parents imported successfully, " . $count[2] . " updated successfully and " . $count[1] . " duplicate records found.";
            return renderResponse(SUCCESS, $message, null);
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured while importing transaction data.", [EXCEPTION => $e]);
            DB::rollback();
            $message = "There was some problem while trying to import Corporate Parents data. Processed data for current period has been rolled back.";
            return renderResponse(FAIL, $message, null);
        }
    }


    /**
     * Import corporate logo to database
     */
    public function importIntegratorLogo()
    {
        $logo = $this->request->file('logo');
        if (empty($logo)) {
            $message = trans('message.empty_excel');
            return renderResponse(FAIL, $message, null);
        }
        $allowed = array('png', 'jpeg', 'jpg', 'gif');
        if (!in_array($logo->getClientOriginalExtension(), $allowed)) {
            $message = trans('message.invalid_logo');
            return renderResponse(FAIL, $message, null);
        }
        try {
            $filePath = 'integrator_logo';
            $front_image_name = time();
            if (Storage::disk('s3')->putFileAs($filePath, $logo, $front_image_name)) { // Uploading doc front end to S3
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") -  Integrator uploaded to s3 successfully.");
            }
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while uploading logo.", [EXCEPTION => $e]);
            $message = trans('message.upload_logo_fail');
            return renderResponse(FAIL, $message, null);
        }
        $theUrl = $filePath . '/' . $front_image_name;
        $message = trans('message.upload_logo_success');
        return renderResponse(SUCCESS, $message, $theUrl);
    }


    /**
     * updateAkoyaProviderId
     * Update the Provider Id for Banks According to the Excel Uploaded
     * @param  mixed $request
     * @return void
     */
    public function updateAkoyaProviderId(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Update akoya provider ID started...");
        $akoya_provider = $this->request->file('akoya_provider');
        if (empty($akoya_provider)) {
            $message = trans('message.empty_excel');
            return renderResponse(FAIL, $message, null);
        }
        $allowed = array('xls', 'xlsx', 'csv');
        if (!in_array($akoya_provider->getClientOriginalExtension(), $allowed)) {
            $message = trans('message.invalid_excel');
            return renderResponse(FAIL, $message, null);
        }

        DB::beginTransaction();
        try {
            ini_set('max_execution_time', 6000);
            $import = new BankProviderImport();
            Excel::import($import, $akoya_provider);
            //adding details to import excel log table
            // Insertion in Migraion Log table Started
            $count = explode('|', $import->getRowCount());

            $log = new MigrationLog();
            $log->type = 'Provider Excel';
            $log->summary = 'Success';
            $log->actual_data_imported = $count[0];
            $log->duplicate_date_imported = $count[1];
            $log->migrated_data_updated = $count[2];
            $log->uploaded_by = Auth::user()->user_id;
            $log->save();
            DB::commit();
            $message = trans('message.excel_import_success');
            return renderResponse(SUCCESS, $message, null);
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured during Database Update.", [EXCEPTION => $e]);
            DB::rollback();
            $message = "There was some problem while trying to import Akoya Provider ID data. Processed data for current period has been rolled back.";
            return renderResponse(FAIL, $message, null);
        }
    }

    /**
     * updateMxInstitutionCode
     * Update the mx institution code for Banks According to the Excel Uploaded
     * @param  mixed $request
     * @return void
     */
    public function updateMxInstitutionCode(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Update mx institution code started...");
        $mx_institution_code = $this->request->file('mx_institution_code');
        if (empty($mx_institution_code)) {
            $message = trans('message.empty_excel');
            return renderResponse(FAIL, $message, null);
        }
        $allowed = array('xls', 'xlsx', 'csv');
        if (!in_array($mx_institution_code->getClientOriginalExtension(), $allowed)) {
            $message = trans('message.invalid_excel');
            return renderResponse(FAIL, $message, null);
        }

        DB::beginTransaction();
        try {
            ini_set('max_execution_time', 6000);
            $import = new MxInstitutionCodeImport();
            Excel::import($import, $mx_institution_code);
            //adding details to import excel log table
            // Insertion in Migraion Log table Started
            $count = explode('|', $import->getRowCount());

            $log = new MigrationLog();
            $log->type = 'Mx Institution Code Excel';
            $log->summary = 'Success';
            $log->actual_data_imported = $count[0];
            $log->duplicate_date_imported = $count[1];
            $log->migrated_data_updated = $count[2];
            $log->uploaded_by = Auth::user()->user_id;
            $log->save();
            DB::commit();
            $message = trans('message.excel_import_success');
            return renderResponse(SUCCESS, $message, null);
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured during Database Update.", [EXCEPTION => $e]);
            DB::rollback();
            $message = "There was some problem while trying to import mx institution code data. Processed data for current period has been rolled back.";
            return renderResponse(FAIL, $message, null);
        }
    }

    
    /**
     * Import Petition logo to database
     */
    public function importPetitionLogo()
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Import Petition logo started...");
        $logo = $this->request->file('logo');
        if (empty($logo)) {
            $message = trans('message.empty_excel');
            return renderResponse(FAIL, $message, null);
        }
        $allowed = array('png', 'jpeg', 'jpg', 'gif');
        if (!in_array($logo->getClientOriginalExtension(), $allowed)) {
            $message = trans('message.invalid_logo');
            return renderResponse(FAIL, $message, null);
        }
        try {
            $thePath = Storage::disk('s3_logo')->putFile('/logos', $logo);
            $theUrl = Storage::disk('s3_logo')->url($thePath);
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while uploading logo.", [EXCEPTION => $e]);
            $message = trans('message.upload_petition_logo_fail');
            return renderResponse(FAIL, $message, null);
        }
        $message = trans('message.upload_petition_logo_success');
        return renderResponse(SUCCESS, $message, $theUrl);
    }
}
