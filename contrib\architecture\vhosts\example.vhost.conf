server {
    server_name demo-admin.canpaydebit.com;
    root   /opt/cp-dev3-api/www/public;

        index index.html index.htm;

		listen 80;
		listen 443 ssl;

		if ($scheme = http) {
		   return 301 https://$server_name$request_uri;
		}

		gzip on;
		gzip_types text/plain application/javascript application/x-javascript text/javascript text/xml text/css;
		gzip_proxied no-cache no-store private expired auth;
		gzip_min_length 1000;
				add_header 'Access-Control-Allow-Credentials' 'true';
				add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';

		ssl_protocols TLSv1.2;

    ssl_certificate /opt/certs/cp-dev3-admin/rh-cert.pem;
    ssl_certificate_key /opt/certs/cp-dev3-admin/rh-key.pem;

    access_log /opt/cp-dev3-admin/logs/access.log;
    error_log /opt/cp-dev3-admin/logs/error.log;

        index index.php index.html index.htm;

		location / {
				try_files $uri $uri/ /index.php?$query_string;
			}

		location ~ \.php$ {
				try_files $uri =404;
				fastcgi_pass 127.0.0.1:9000;
				fastcgi_index index.php;
				fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
				include fastcgi_params;
				fastcgi_read_timeout 20000;
		}
}