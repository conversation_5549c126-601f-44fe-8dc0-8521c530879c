<?php

namespace App\Http\Controllers;

use App\Exports\StoreApiKeysExport;
use App\Http\Clients\GooglePlacesHttpClient;
use App\Models\MerchantApiKeyMap;
use App\Models\MerchantStores;
use App\Models\RegisteredMerchantMaster;
use App\Models\StatusMaster;
use App\Models\StoreEnableDisableHistory;
use App\Models\StoreEnableDisableMaster;
use App\Models\StoreTransactionTypeMap;
use App\Models\StoreUserMap;
use App\Models\StoreUserTransactionActivityEmailMaps;
use App\Models\TimezoneMaster;
use App\Models\TransactionDetails;
use App\Models\TransactionPostingDecisionTable;
use App\Models\TransactionTypeMaster;
use App\Models\UserBankAccountInfo;
use App\Models\MerchantStore;
use App\Models\StoreDeliveryPartnerMap;
use Carbon\Carbon;
use Datetime;
use DateTimeZone;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;

class StoreController extends Controller
{
    public function __construct()
    {
        $this->googleplaces = new GooglePlacesHttpClient();
    }

    /**
     * getAllStores
     * Listing page for Stores with Regional Manager Mapping along with Server Side Pagination in Datatable
     * @param  mixed $request
     * @return void
     */
    public function getAllStores(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Stores search started...");
        // Validating input request
        $this->validate($request, [
            'merchant_id' => VALIDATION_REQUIRED_WITHOUT_ALL . ':store_id,retailer',
            'store_id' => VALIDATION_REQUIRED_WITHOUT_ALL . ':retailer,merchant_id',
            'retailer' => VALIDATION_REQUIRED_WITHOUT_ALL . ':store_id,merchant_id',
        ]);

        // Search with in Stores
        $stores = $this->_getStores($request);

        $message = trans('message.store_search_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Stores search complete.");
        return renderResponse(SUCCESS, $message, $stores);
    }

    /**
     * _getStores
     * Fetch the Stores
     * @param  mixed $searchArray
     * @return void
     */
    private function _getStores($request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Stores Search Started.");

        $sql = "select * from (
            select me.* ,ms.merchant_name, ms.merchant_id as merchantID, tm.timezone_name,u.first_name cp_name,GROUP_CONCAT(ste.email) as cp_emails,GROUP_CONCAT(ttm.label) as store_type, ms.type as merchant_type
            from merchant_stores me
            straight_join registered_merchant_master ms on ms.id = me.merchant_id
            straight_join timezone_masters tm on tm.id = me.timezone_id
            straight_join store_user_map su on su.store_id = me.id
            straight_join store_transaction_type_maps sttm on sttm.store_id = me.id
            straight_join transaction_type_master ttm on ttm.id = sttm.transaction_type_id
            left join store_user_transaction_activity_email_maps ste on ste.store_id = me.id
            straight_join users u on u.user_id = su.user_id AND u.default_cp = 0
            straight_join user_roles ur on ur.role_id = u.role_id and ur.role_name in ('" . CORPORATE_PARENT . "', '" . BRAND_ADMIN . "') GROUP BY me.id
            union all
            select me.* ,ms.merchant_name, ms.merchant_id as merchantID, tm.timezone_name,null cp_name,GROUP_CONCAT(ste.email) as cp_emails,GROUP_CONCAT(ttm.label) as label, ms.type as merchant_type
            from
            (select in_1.id,(in_1.cp + ifnull(in_2.cp,0)) cp_flag
                from
                (select distinct me.id,0 cp from merchant_stores me) in_1
                left join
                (select distinct me.id,1 cp
                from merchant_stores me
                straight_join store_user_map su on su.store_id = me.id
                straight_join store_transaction_type_maps sttm on sttm.store_id = me.id
                straight_join transaction_type_master ttm on ttm.id = sttm.transaction_type_id
                straight_join users u on u.user_id = su.user_id AND u.default_cp = 0
                straight_join user_roles ur on ur.role_id = u.role_id and ur.role_name in ('" . CORPORATE_PARENT . "', '" . BRAND_ADMIN . "')) in_2 on in_1.id = in_2.id
            ) a
            straight_join merchant_stores me on a.id = me.id
            left join store_user_transaction_activity_email_maps ste on ste.store_id = me.id
            straight_join registered_merchant_master ms on ms.id = me.merchant_id
            straight_join timezone_masters tm on tm.id = me.timezone_id
            straight_join store_transaction_type_maps sttm on sttm.store_id = me.id
            straight_join transaction_type_master ttm on ttm.id = sttm.transaction_type_id
            where a.cp_flag = 0 GROUP BY me.id) b WHERE 1  ";

        $searchStr = [];
        if (trim($request['retailer'])) {
            $sql .= ' AND retailer LIKE ? ';
            array_push($searchStr, '%' . $request['retailer'] . '%');
        }
        if (trim($request['merchant_id'])) {
            $sql .= " AND merchantID = ? ";
            array_push($searchStr, $request['merchant_id']);
        }
        if (trim($request['store_id'])) {
            $sql .= " AND store_id = ? ";
            array_push($searchStr, $request['store_id']);
        }
        $sql .= " LIMIT 100";
        $stores = DB::connection(MYSQL_RO)->Select($sql, $searchStr);

        $storeArr = [];
        if (!empty($stores)) {
            foreach ($stores as $store) {

                $data = [];
                $data['merchantID'] = $store->merchantID;
                $data['store_id'] = $store->store_id;
                $data['retailer'] = $store->retailer;
                $data['store_type'] = $store->store_type;
                $data['id'] = $store->id;
                $data['timezone_name'] = $store->timezone_name;
                $data['merchant_name'] = $store->merchant_name;
                $data['pos_employee_login'] = $store->pos_employee_login;
                $data['cp_name'] = $store->cp_name;
                $data['cp_emails'] = $store->cp_emails;
                $data['created_at'] = date('m-d-Y h:i A', strtotime($store->created_at));
                $data['map_viewable'] = $store->map_viewable == 1 ? "checked" : "";
                $data['check_emp_login'] = $store->pos_employee_login == 1 ? "checked" : "";
                $data['check_ecommerce_admin_driven'] = $store->ecommerce_admin_driven == 1 ? "checked" : "";
                $data['is_ecommerce'] = $store->is_ecommerce;
                $data['is_sponsor'] = $store->merchant_type == SPONSOR ? 1 : 0;
                $data['has_cp'] = !is_null($store->cp_name) ? 1 : 0;

                array_push($storeArr, $data);
            }
        } else {
            $storeArr = [];
        }

        return $storeArr;
    }

    /**
     * This API lets admins update the merchant details
     */
    public function updateMerchant(Request $request)
    {
        $data = $request->all();
        $this->__validate($data, array(
            'merchant_name' => VALIDATION_REQUIRED,
            'merchant_id' => VALIDATION_REQUIRED,
            'account_no' => VALIDATION_REQUIRED,
            'routing_no' => VALIDATION_REQUIRED,
            'retailer' => VALIDATION_REQUIRED,
        ));

        DB::beginTransaction();
        try {
            // Updation begins in registered_merchant Table
            $merchant = RegisteredMerchantMaster::find($data['edit']);
            $merchant->email = $data['email'];
            $merchant->merchant_name = $data['merchant_name'];
            $merchant->volume_value = $data['volume_value'];
            $merchant->per_transaction_value = $data['per_transaction_value'];
            $merchant->acheck_account_name = $data['acheck_account_name'];
            $merchant->acheck_account_id = $data['acheck_account_id'];
            $merchant->allow_ecommerce_transaction = $data['allow_ecommerce_transaction'];
            $merchant->allow_consumer_auth = $data['allow_consumer_auth'];
            $merchant->sandbox_webhook_url = $data['sandbox_webhook_url'];
            $merchant->live_webhook_url = $data['live_webhook_url'];
            $merchant->merchant_profile_name = $data['merchant_profile_name'];
            $merchant->is_enabled_new_ach_process = $data['is_enabled_new_ach_process'];
            $merchant->sponsor_signup_url = $data['sponsor_signup_url'];
            $merchant->split_cashback_posting = $data['split_cashback_posting'];
            $merchant->type = $data['type'];
            $merchant->save();
            // update merchant api key type
            MerchantApiKeyMap::where('merchant_id', $data['edit'])->update(['type' => $data['type']]);
            //first delete the existing delivery partner store present against registered merchant.
            StoreDeliveryPartnerMap::where('delivery_partner_id', $data['edit'])->delete();
            // now add the the store porived in edit
            if($data['type'] == DELIVERY_PARTNER){
                $selectedStore = $data['selectedStore'];
                foreach($selectedStore as $store){
                    $newdeliveryPartner = new StoreDeliveryPartnerMap();
                    $newdeliveryPartner->store_id = $store["id"];
                    $newdeliveryPartner->delivery_partner_id = $data['edit'];
                    $newdeliveryPartner->canpay_commission = $data['canpay_commission'];
                    $newdeliveryPartner->save();
                }
            }

            //updation retailer name
            $marchentStore = MerchantStores::where('merchant_id', $data['edit'])->first();
            $marchentStore->retailer = $data['retailer'];
            $marchentStore->contact_no = $data['store_contact_no'];
            $marchentStore->website_address = $data['website_address'];
            $marchentStore->pos_employee_login = $data['pos_employee_login'];
            $marchentStore->save();
            //Check if Bank Details Exists for the Merchant
            $merchantBankDetails = UserBankAccountInfo::where(['merchant_id' => $data['edit'], 'type' => BANK_TYPE_ACH])->get()->count();
            if ($merchantBankDetails == 0) {
                $bankStatus = StatusMaster::where("code", BANK_ACTIVE)->first();
                //Insert Merchant Bank Details
                $merchantAddBankDetails = new UserBankAccountInfo();
                $merchantAddBankDetails->merchant_id = $data['edit'];
                $merchantAddBankDetails->account_no = $data['account_no'];
                $merchantAddBankDetails->fees_account_number = $data['fees_account_number'];
                $merchantAddBankDetails->routing_no = $data['routing_no'];
                $merchantAddBankDetails->status = $bankStatus->id;
                $merchantAddBankDetails->type = BANK_TYPE_ACH;
                $merchantAddBankDetails->save();
            } else {
                //Update Bank Details
                $merchant = UserBankAccountInfo::find($data['user_account_id']);
                $merchant->account_no = $data['account_no'];
                $merchant->fees_account_number = $data['fees_account_number'];
                $merchant->routing_no = $data['routing_no'];
                $merchant->save();
            }
            //Check if Bank Details Exists for the merchant_points_program
            $merchantBankDetails = UserBankAccountInfo::where(['merchant_id' => $data['edit'], 'type' => BANK_TYPE_MERCHANT_POINTS_PROGRAM])->get()->count();
            if ($merchantBankDetails == 0) {
                $bankStatus = StatusMaster::where("code", BANK_ACTIVE)->first();
                //Insert Merchant Bank Details
                $merchantAddBankDetails = new UserBankAccountInfo();
                $merchantAddBankDetails->merchant_id = $data['edit'];
                $merchantAddBankDetails->account_no = $data['merchant_points_program_account_no'];
                $merchantAddBankDetails->fees_account_number = $data['merchant_points_program_fees_account_number'];
                $merchantAddBankDetails->routing_no = $data['merchant_points_program_routing_no'];
                $merchantAddBankDetails->status = $bankStatus->id;
                $merchantAddBankDetails->type = BANK_TYPE_MERCHANT_POINTS_PROGRAM;
                $merchantAddBankDetails->save();
            } else {
                //Update Bank Details
                $merchant = UserBankAccountInfo::find($data['merchant_points_program_user_account_id']);
                $merchant->account_no = $data['merchant_points_program_account_no'];
                $merchant->fees_account_number = $data['merchant_points_program_fees_account_number'];
                $merchant->routing_no = $data['merchant_points_program_routing_no'];
                $merchant->save();
            }

            DB::commit();
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Merchant details updated successfully");
            $message = trans('message.update_merchant_success');
            // API Response returned with 200 status
            return renderResponse(SUCCESS, $message, null);
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured while updating merchant with merchant id: " . $data['merchant_id'], [EXCEPTION => $e]);
            DB::rollback();
            $message = trans('message.db_transaction_failed');
            // Exception Returned
            return renderResponse(FAIL, $message, null);
        }
    }
    /**
     * This API enables and disables the Ecommerce Admin Driven feature for a perticular store
     */
    public function toggleEcommerceAdminDriven(Request $request)
    {
        //get the store details
        try {
            $store = MerchantStores::where("id", $request->get('id'))->first();
            if ($store && $store->is_ecommerce == 1) {
                // toggle the value
                $store->ecommerce_admin_driven = $store->ecommerce_admin_driven == 0 ? 1 : 0;
                $store->save();
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Ecommerce Admin Driven for store " . $store->retailer . " has been updated");
                $message = $store->ecommerce_admin_driven == 0 ? trans('message.ecommerce_admin_driven_disabled') : trans('message.ecommerce_admin_driven_enabled');
                // API Response returned with 200 status
                return renderResponse(SUCCESS, $message, null);
            } else {
                Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Store is not eCommerce type");
                $message = trans('message.not_ecommerce_store');
                // Exception Returned
                return renderResponse(FAIL, $message, null);
            }
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured while updating store", [EXCEPTION => $e]);
            $message = trans('message.db_transaction_failed');
            // Exception Returned
            return renderResponse(FAIL, $message, null);
        }
    }

    /**
     *
     * getAllApiKeys
     * Listing page for Store API Keys with Merchant ID and Store ID along with Server Side Pagination in Datatable
     * @param  mixed $request
     * @return void
     */
    public function getAllApiKeys(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Store API keys search started...");
        // Validating input request
        $this->validate($request, [
            'merchant_id' => VALIDATION_REQUIRED_WITHOUT_ALL . ':store_id,retailer',
            'store_id' => VALIDATION_REQUIRED_WITHOUT_ALL . ':retailer,merchant_id',
            'retailer' => VALIDATION_REQUIRED_WITHOUT_ALL . ':merchant_id,store_id',
            'type' => VALIDATION_REQUIRED
        ]);

        //Search with in Corpoarate Parents
        $stores_api_keys = $this->_getStoreApiKeySearch($request);

        $message = trans('message.store_api_key_fetch_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Store API Keys List fetched successfully.");
        return renderResponse(SUCCESS, $message, $stores_api_keys);
    }

    /**
     * _getCorporateParentSearch
     * Fetch the Corporate Parents
     * @param  mixed $searchArray
     * @return void
     */
    private function _getStoreApiKeySearch($request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Corporate Parent Search Started.");

        $sql = "SELECT `registered_merchant_master`.`merchant_id` AS `merchantID`, `merchant_stores`.`store_id`, `merchant_stores`.`retailer`, `merchant_api_key_maps`.`app_key`, `merchant_api_key_maps`.`api_secret`
        FROM `registered_merchant_master`
        INNER JOIN `merchant_stores` ON `registered_merchant_master`.`id` = `merchant_stores`.`merchant_id`
        INNER JOIN `merchant_api_key_maps` ON `merchant_stores`.`id` = `merchant_api_key_maps`.`store_id` WHERE 1  ";

        $searchStr = [];
        if ($request['type'] != '') {
            $sql .= ' AND merchant_api_key_maps.type = ?';
            array_push($searchStr, $request['type']);
        }
        if (strlen(trim($request['retailer'])) >= 3) {
            $sql .= ' AND merchant_stores.retailer LIKE ? ';
            array_push($searchStr, '%' . $request['retailer'] . '%');
        }
        if (trim($request['store_id'])) {
            $sql .= " AND merchant_stores.store_id = ? ";
            array_push($searchStr, $request['store_id']);
        }
        if (trim($request['merchant_id'])) {
            $sql .= " AND registered_merchant_master.merchant_id = ? ";
            array_push($searchStr, $request['merchant_id']);
        }
        $sql .= " order by `merchant_stores`.`retailer` LIMIT 100";
        $apiKeys = DB::connection(MYSQL_RO)->Select($sql, $searchStr);

        $apikeysArray = [];
        if (!empty($apiKeys)) {
            foreach ($apiKeys as $keys) {
                $data = [];
                $data['merchantID'] = $keys->merchantID;
                $data['store_id'] = $keys->store_id;
                $data['retailer'] = $keys->retailer;
                $data['app_key'] = $keys->app_key;
                $data['api_secret'] = $keys->api_secret;

                array_push($apikeysArray, $data);
            }
        }

        return $apikeysArray;
    }

    /**
     * getStoreApiKeysExport
     * This is the export function for Store API Keys
     * @param  mixed $request
     * @return void
     */
    public function getStoreApiKeysExport(Request $request)
    {
        return Excel::download(new StoreApiKeysExport($request), 'store_api_keys' . date('m-d-Y H:i:s') . '.xlsx');
    }

    /**
     * getStoresByType
     * Get all the Store Listing By Status type active and Inactive
     * @param  mixed $request
     * @return void
     */
    public function getStoresByType(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Store search started...");
        // Validating input request
        $this->validate($request, [
            'retailer' => VALIDATION_REQUIRED_WITHOUT_ALL . ':merchant_id,store_id',
            'merchant_id' => VALIDATION_REQUIRED_WITHOUT_ALL . ':retailer,store_id',
            'store_id' => VALIDATION_REQUIRED_WITHOUT_ALL . ':retailer,merchant_id',
        ]);

        // Search with in Stores
        $stores = $this->_getEnableDisableStoreSearch($request);

        $message = trans('message.store_fetch_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Store search started complete.");
        return renderResponse(SUCCESS, $message, $stores);
    }

    /**
     * _getEnableDisableStoreSearch
     * Fetch the Enable / Disbale Stores
     * @param  mixed $searchArray
     * @return void
     */
    private function _getEnableDisableStoreSearch($request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Store Search Started.");

        $status = StatusMaster::where('status', $request['storeType'])->first(); // Fetching Status ID with respect to Status

        $sql = "SELECT status_master.status as status_name, merchant_stores.* ,registered_merchant_master.merchant_name, registered_merchant_master.merchant_id as merchantID FROM merchant_stores LEFT JOIN ( SELECT MAX(created_at) max_id, enable_disable_master_id , store_id FROM store_enable_disable_history GROUP BY store_id ) enable_disable_history_max_id ON (enable_disable_history_max_id.store_id = merchant_stores.store_id) INNER JOIN status_master ON status_master.id = merchant_stores.status INNER JOIN registered_merchant_master ON registered_merchant_master.id = merchant_stores.merchant_id WHERE merchant_stores.status = ? ";

        $searchStr = [$status->id];
        if (trim($request['retailer'])) {
            $sql .= ' AND merchant_stores.retailer LIKE ? ';
            array_push($searchStr, '%' . $request['retailer'] . '%');
        }
        if (trim($request['store_id'])) {
            $sql .= " AND merchant_stores.store_id = ? ";
            array_push($searchStr, $request['store_id']);
        }
        if (trim($request['merchant_id'])) {
            $sql .= " AND registered_merchant_master.merchant_id = ? ";
            array_push($searchStr, $request['merchant_id']);
        }
        $sql .= "  ORDER BY merchant_stores.created_at DESC LIMIT 100";
        $stores = DB::connection(MYSQL_RO)->Select($sql, $searchStr);

        $storesArr = [];
        if (!empty($stores)) {
            foreach ($stores as $store) {

                $data = [];
                $data['merchantID'] = $store->merchantID;
                $data['store_id'] = $store->store_id;
                $data['retailer'] = $store->retailer;
                $data['address'] = $store->address;
                $data['city'] = $store->city;
                $data['state'] = $store->state;
                $data['county'] = $store->county;
                $data['zip'] = $store->zip;
                $data['contact_no'] = $store->contact_no;
                $data['id'] = $store->id;
                $data['email'] = $store->email;
                $data['merchant_name'] = $store->merchant_name;
                $data['status'] = $store->status_name;
                $data['created_at'] = date('m-d-Y h:i A', strtotime($store->created_at));

                array_push($storesArr, $data);
            }
        } else {
            $storesArr = [];
        }

        return $storesArr;
    }

    /**
     * getEnableDisableReasons
     * Fetch Enable and Disable Reasons
     * @param  mixed $request
     * @return void
     */
    public function getEnableDisableReasons(Request $request)
    {
        $active_status = StatusMaster::where('status', ACTIVE)->first();
        $inactive_status = StatusMaster::where('status', INACTIVE)->first();

        $enableReasons = StoreEnableDisableMaster::where(['status' => $active_status->id])->orderBy('reason', 'ASC')->get();
        $disableReasons = StoreEnableDisableMaster::where(['status' => $inactive_status->id])->orderBy('reason', 'ASC')->get();

        $returnResponse = array(
            'enableReasons' => $enableReasons,
            'disableReasons' => $disableReasons,
        );

        $message = trans('message.store_fetch_success');
        return renderResponse(SUCCESS, $message, $returnResponse);
    }

    /**
     * disableStore
     * This function will disable Store
     * @param  mixed $request
     * @return void
     */
    public function disableStore(Request $request)
    {
        $rule = array(
            'disable_reasons' => VALIDATION_REQUIRED,
        );
        $this->__validate($request->all(), $rule);

        DB::beginTransaction();
        try {
            //Fetch the Inactive Status ID from Status Master table
            $status = StatusMaster::where('status', INACTIVE)->first();
            $status_id = $status->id;

            // Update the Store status to Disable
            $merchantStores = MerchantStores::find($request->input('id'));
            $merchantStores->status = $status_id;
            $merchantStores->save();

            //Check if Reason Exists in the Master table
            $storeReasonMaster = StoreEnableDisableMaster::where(['status' => $status_id, 'reason' => $request->input('disable_reasons')])->first();
            if (!empty($storeReasonMaster)) {
                $reason_id = $storeReasonMaster->id;
            } else {
                $storeReason = new StoreEnableDisableMaster;
                $storeReason->reason = $request->input('disable_reasons');
                $storeReason->status = $status_id;
                $storeReason->save();
                $reason_id = $storeReason->id;
            }

            //User details
            $user_details = Auth::user();

            //Add Record in the History Table
            $storeStatusHistory = new StoreEnableDisableHistory;
            $storeStatusHistory->store_id = $request->input('id');
            $storeStatusHistory->status_id = $status_id;
            $storeStatusHistory->enable_disable_master_id = $reason_id;
            $storeStatusHistory->created_by = $user_details->user_id;
            $storeStatusHistory->save();

            DB::commit();

            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Store ID (" . $request->input('store_id') . ") disabled successfully");
            $message = trans('message.disable_store_success');
            // API Response returned with 200 status
            return renderResponse(SUCCESS, $message, null);
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured during Disabling Store ID (" . $request->input('store_id') . ") .", [EXCEPTION => $e]);
            DB::rollback();
            $message = trans('message.disable_store_error');
            // Exception Returned
            return renderResponse(FAIL, $message, null);
        }
    }

    /**
     * enableStore
     * This function will enable Store
     * @param  mixed $request
     * @return void
     */
    public function enableStore(Request $request)
    {
        $rule = array(
            'enable_reasons' => VALIDATION_REQUIRED,
        );
        $this->__validate($request->all(), $rule);

        DB::beginTransaction();
        try {
            //Fetch the Active Status ID from Status Master table
            $status = StatusMaster::where('status', ACTIVE)->first();
            $status_id = $status->id;

            // Update the Store status to Enaable
            $merchantStores = MerchantStores::find($request->input('id'));
            $merchantStores->status = $status_id;
            $merchantStores->save();

            //Check if Reason Exists in the Master table
            $storeReasonMaster = StoreEnableDisableMaster::where(['status' => $status_id, 'reason' => $request->input('enable_reasons')])->first();
            if (!empty($storeReasonMaster)) {
                $reason_id = $storeReasonMaster->id;
            } else {
                $storeReason = new StoreEnableDisableMaster;
                $storeReason->reason = $request->input('enable_reasons');
                $storeReason->status = $status_id;
                $storeReason->save();
                $reason_id = $storeReason->id;
            }

            //User details
            $user_details = Auth::user();

            //Add Record in the History Table
            $storeStatusHistory = new StoreEnableDisableHistory;
            $storeStatusHistory->store_id = $request->input('id');
            $storeStatusHistory->status_id = $status_id;
            $storeStatusHistory->enable_disable_master_id = $reason_id;
            $storeStatusHistory->created_by = $user_details->user_id;
            $storeStatusHistory->save();

            DB::commit();

            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Store ID (" . $request->input('store_id') . ") enabled successfully");
            $message = trans('message.enable_store_success');
            // API Response returned with 200 status
            return renderResponse(SUCCESS, $message, null);
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured during Enabling Store ID (" . $request->input('store_id') . ") .", [EXCEPTION => $e]);
            DB::rollback();
            $message = trans('message.enable_store_error');
            // Exception Returned
            return renderResponse(FAIL, $message, null);
        }
    }

    /**
     * addStoreDailyTransactionEmail
     * Add the email for Store specific daily Transaction Email
     * @param  mixed $request
     * @return void
     */
    public function addStoreDailyTransactionEmail(Request $request)
    {
        $rule = array(
            'id' => VALIDATION_REQUIRED,
        );
        $this->__validate($request->all(), $rule);

        DB::beginTransaction();
        try {
            $delete_old_emails = StoreUserTransactionActivityEmailMaps::where('store_id', $request->input('id'))->delete();
            if ($request->input('cp_emails')) {
                foreach (explode(',', $request->input('cp_emails')) as $cp_email) {
                    $store_email_maps = new StoreUserTransactionActivityEmailMaps();
                    $store_email_maps->store_id = $request->input('id');
                    $store_email_maps->email = trim($cp_email);
                    $store_email_maps->save();
                }
            }

            DB::commit();

            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Emails : " . $request->input('cp_emails') . " added for Store ID (" . $request->input('id') . ") successfully");
            $message = trans('message.store_email_add_success');
            // API Response returned with 200 status
            return renderResponse(SUCCESS, $message, null);
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured during adding Emails : " . $request->input('cp_emails') . " for Store ID (" . $request->input('id') . ") .", [EXCEPTION => $e]);
            DB::rollback();
            $message = trans('message.store_email_add_error');
            // Exception Returned
            return renderResponse(FAIL, $message, null);
        }
    }

    /**
     * This API Hide From Map store
     */
    public function storeHideFromMap(Request $request)
    {
        //get the store details
        try {
            $store = MerchantStores::where("id", $request->get('id'))->first();
            // toggle the value
            $store->map_viewable = $store->map_viewable == 0 ? 1 : 0;
            $store->save();
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - This Store has  Hide From Map  " . $store->retailer . " has been updated");
            $message = $store->map_viewable == 0 ? trans('message.store_hide_from_map_disabled') : trans('message.store_hide_from_map_enable');
            // API Response returned with 200 status
            return renderResponse(SUCCESS, $message, null);
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured while updating store", [EXCEPTION => $e]);
            $message = trans('message.db_transaction_failed');
            // Exception Returned
            return renderResponse(FAIL, $message, null);
        }
    }

    /**
     * getMerchantDetails
     * Fetch the Merchant Details for a particular Merchant ID
     * @param  mixed $request
     * @return void
     */
    public function getMerchantDetails(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Merchant Details Fetch w.r.t Merchant ID started.");
        // Validating input request
        $this->validate($request, [
            'merchantID' => VALIDATION_REQUIRED,
        ]);

        //Fetch the Merchant Details for a parcticular Merchant ID
        $merchantDetails = MerchantStores::join('registered_merchant_master', 'registered_merchant_master.id', '=', 'merchant_stores.merchant_id')
            ->leftjoin('user_bank_account_info as ubai', function ($join) {
                $join->on("ubai.merchant_id", "=", "registered_merchant_master.id");
                $join->where("ubai.type", "=", BANK_TYPE_ACH);
            })
            ->leftjoin('user_bank_account_info as ubaimpp', function ($join) {
                $join->on("ubaimpp.merchant_id", "=", "registered_merchant_master.id");
                $join->where("ubaimpp.type", "=", BANK_TYPE_MERCHANT_POINTS_PROGRAM);
            })
            ->select('merchant_stores.pos_employee_login', 'merchant_stores.retailer', 'merchant_stores.store_id', 'merchant_stores.website_address', 'merchant_stores.contact_no as store_contact_no', 'registered_merchant_master.*', 'ubai.fees_account_number', 'ubai.routing_no', 'ubai.account_no', 'ubai.id as user_account_id', 'registered_merchant_master.id as edit', 'ubaimpp.fees_account_number as merchant_points_program_fees_account_number', 'ubaimpp.routing_no as merchant_points_program_routing_no', 'ubaimpp.account_no as merchant_points_program_account_no', 'ubaimpp.id as merchant_points_program_user_account_id', 'registered_merchant_master.ach_identifier')
            ->where('registered_merchant_master.id', $request->get('merchantID'))
            ->first();
        $sql = "SELECT ms.id, ms.retailer, ms.is_ecommerce, sdpm.canpay_commission
        FROM store_delivery_partner_maps AS sdpm
        LEFT JOIN merchant_stores ms ON ms.id = sdpm.store_id
        WHERE sdpm.delivery_partner_id = ?";
        $search_array = [$request->get('merchantID')];
        $selectedStores = DB::connection(MYSQL_RO)->select($sql,$search_array);
        $merchantDetails->selectedStore = $selectedStores;
        if(count($selectedStores)>0){
            $merchantDetails->canpay_commission = $selectedStores[0]->canpay_commission;
        }else{
            $merchantDetails->canpay_commission = null;
        }
        $merchantDetails->enable_new_ach_process_for_all_merchant = getSettingsValue('enable_new_ach_process_for_all_merchant', 0);
        $message = trans('message.merchant_details_fetch_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Merchant Details Fetched for Merchant ID : " . $request->get('merchantID'));
        return renderResponse(SUCCESS, $message, $merchantDetails);
    }

    /**
     * getStoreDetails
     * Fetch the Store Details for a particular Merchant ID
     * @param  mixed $request
     * @return void
     */
    public function getStoreDetails(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . " Merchant Store Details Fetch w.r.t Merchant ID started.");
        // Validating input request
        $this->validate($request, [
            'id' => VALIDATION_REQUIRED,
        ]);

        //Fetch the Merchant Store Details for a parcticular Merchant ID
        $merchantDetails = MerchantStores::where('merchant_stores.id', $request->get('id'))
            ->first();

        $message = trans('message.merchant_details_fetch_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Merchant Store Details Fetched for Merchant Store ID : " . $request->get('merchantID'));
        return renderResponse(SUCCESS, $message, $merchantDetails);
    }

    /**
     * getMerchentStoreTransactionType
     * Fetch the Store Details for a particular Merchant ID
     * @param  mixed $request
     * @return void
     */
    public function getMerchentStoreTransactionType(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . " Merchant Store transaction type  Details Fetch w.r.t Merchant Store ID started.");
        // Validating input request
        $this->validate($request, [
            'marchentStoreId' => VALIDATION_REQUIRED,
        ]);

        // Fetch the Merchant Store transaction type  Details for a parcticular Merchant Store ID

        $merchantStoreTrasactionTypes = MerchantStores::leftjoin('store_transaction_type_maps', 'store_transaction_type_maps.store_id', '=', 'merchant_stores.id')
            ->leftjoin('transaction_type_master', 'transaction_type_master.id', '=', 'store_transaction_type_maps.transaction_type_id')
            ->select('transaction_type_master.type', 'transaction_type_master.label', 'transaction_type_master.id')
            ->where('merchant_stores.id', $request->get('marchentStoreId'))
            ->get();

        // fetch transaction type

        $transactionType = TransactionTypeMaster::whereNotIn('label', ['N', 'B', 'RE'])
            ->select('transaction_type_master.id', 'transaction_type_master.type', 'transaction_type_master.label')
            ->get();

        $merchantDetails = MerchantStores::where('merchant_stores.id', $request->get('marchentStoreId'))->first();

        $data['merchantStoreTrasactionTypes'] = $merchantStoreTrasactionTypes;
        $data['transactionType'] = $transactionType;
        $data['merchantDetails'] = $merchantDetails;
        $message = trans('message.merchant_details_fetch_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Merchant Store transaction type  Details Fetched for Merchant ID : " . $request->get('merchantID'));
        return renderResponse(SUCCESS, $message, $data);
    }
    /**
     * updateMerchantStoreTransactionType
     * This function will update Merchant store transaction type along with Store mapping
     * @param  mixed $request
     * @return void
     */
    public function updateMerchantStoreTransactionType(Request $request)
    {

        $rule = array(
            'store_id' => VALIDATION_REQUIRED,
        );
        $this->__validate($request->all(), $rule);
        DB::beginTransaction();
        try {
            // Delete Previous Records
            $deleteOldData = StoreTransactionTypeMap::where('store_id', $request->get('store_id'))->delete();
            if (!empty($request->get('transaction_type_ids'))) {
                // Mapping Creation started in Store Transaction Type  Map
                foreach ($request->get('transaction_type_ids') as $transactionType) {
                    $storeTransactionTypeMap = new StoreTransactionTypeMap();
                    $storeTransactionTypeMap->store_id = $request->get('store_id');
                    $storeTransactionTypeMap->transaction_type_id = $transactionType['id'];
                    $storeTransactionTypeMap->save();
                }
                DB::commit();
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction type Updated Successfully Store ID : " . $request->get('store_id'));
                $message = trans('message.transaction_type_updation_success');
                // API Response returned with 200 status
                return renderResponse(SUCCESS, $message, null);
            }
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured during Transaction type Updation By Store ID :  " . $request->get('store_id') . ".", [EXCEPTION => $e]);
            DB::rollback();
            $message = trans('message.transaction_type_updation_error');
            // Exception Returned
            return renderResponse(FAIL, $message, null);
        }
    }

    private function _getTimezone($lat, $long)
    {
        $response = $this->googleplaces->getTimezone($lat, $long);

        $result = json_decode($response, true);
        Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "after google locate.") . $result['status'];

        return $result['status'] == 'OK' ? $result['timeZoneId'] : '';
    }

    /**
     * update store location
     */
    public function updatestorelocation(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . " Update process for store location started...");
        // Validating input request
        $data = $request->all();
        $this->__validate($data, array(
            'lat' => VALIDATION_REQUIRED,
            'long' => VALIDATION_REQUIRED,
            'address' => VALIDATION_REQUIRED,
            'city' => VALIDATION_REQUIRED,
            'country' => VALIDATION_REQUIRED,
            'state' => VALIDATION_REQUIRED,
            'zip' => VALIDATION_REQUIRED,
            'store_id' => VALIDATION_REQUIRED,
        ));

        DB::beginTransaction();
        try {
            $merchantDetails = MerchantStores::find($data['store_id']);
            $timezone_details = $this->_getTimezone($data['lat'], $data['long']);
            $checkTimezoneExists = TimezoneMaster::where('timezone_name', $timezone_details)->first();
            if (empty($checkTimezoneExists)) {
                $timezone = new TimezoneMaster();
                $timezone->timezone_name = $timezone_details;
                $timezone->save();
                $merchantDetails->timezone_id = $timezone->id;

                // Insert the new timezone in Transaction posting decision table
                $time = Carbon::now()->format('Y-m-d') . ' ' . config('app.transaction_posting_block_end_time') . ':00:00';
                $date = DateTime::createFromFormat(DB_DATE_FORMAT, $time, new DateTimeZone('America/New_York'));
                $date->setTimeZone(new DateTimeZone($timezone->timezone_name));
                $transaction_posting_decision_table = new TransactionPostingDecisionTable();
                $transaction_posting_decision_table->timezone_id = $timezone->id;
                $transaction_posting_decision_table->start_time = "00:00:01";
                $transaction_posting_decision_table->end_time = $date->format('H:i:s');
                $transaction_posting_decision_table->save();
            } else {
                $merchantDetails->timezone_id = $checkTimezoneExists->id;
            }
            $merchantDetails->lat = $data['lat'];
            $merchantDetails->long = $data['long'];
            $merchantDetails->address = $data['address'];
            $merchantDetails->city = $data['city'];
            $merchantDetails->zip = $data['zip'];
            $merchantDetails->state = $data['state'];
            $merchantDetails->county = $data['country'];
            $merchantDetails->save();

            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . " Fetching transactions for store happend today before the location update for Store ID: " . $merchantDetails->id);
            $current_date = Carbon::now()->timezone($timezone_details)->toDateString(); // Get current date for Store's timezone
            // Fetch all the previous transactions timezone happened today from this store
            $previous_transaction_for_today = TransactionDetails::join('terminal_master', 'terminal_master.id', '=', 'transaction_details.terminal_id')->join('merchant_stores', 'merchant_stores.id', '=', 'terminal_master.merchant_store_id')->selectRaw("GROUP_CONCAT(DISTINCT(transaction_details.id)  SEPARATOR',') as transaction_ids")->whereNull('transaction_details.transaction_ref_no')->where(['transaction_details.is_v1' => 0, 'transaction_details.local_transaction_date' => $current_date, 'merchant_stores.id' => $merchantDetails->id])->first();
            if ($previous_transaction_for_today->transaction_ids != '') { // Check if any transaction happened before the update
                $transaction_id_array = explode(',', $previous_transaction_for_today->transaction_ids); // Create an array to pass in update eloquent
                TransactionDetails::whereIn('id', $transaction_id_array)->update(['timezone_id' => $merchantDetails->timezone_id]); // Update the transactions timezone
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . " Timezone updated for the transactions for Store ID: " . $merchantDetails->id);
            } else {
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . " No transactions found for today for Store ID: " . $merchantDetails->id);
            }

            DB::commit();
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Store Location updated successfully for Store ID: " . $merchantDetails->id);
            $message = trans('message.update_store_location_success');
            // API Response returned with 200 status
            return renderResponse(SUCCESS, $message, null);
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured while updating store location with store id: " . $data['store_id'], [EXCEPTION => $e]);
            DB::rollback();
            $message = trans('message.store_update_location_failed');
            // Exception Returned
            return renderResponse(FAIL, $message, null);
        }
    }

    /**
     * getAuthorizedStores
     * Get All the active stores
     * @return void
     */
    public function getAuthorizedStores(Request $request)
    {
        $this->validate($request, [
            'searchtxt' => VALIDATION_REQUIRED,
        ]);
        // if is_ecommerce = 1 only ecommerce, is_ecommerce = 0 non ecommerce , if null then all
        $is_ecommerce = $request->get('is_ecommerce');

        //Fetch the stores
        // Client request to show disabled store as well in the drop down
        $stores = MerchantStores::on(MYSQL_RO)->select('merchant_stores.*')->whereRaw('merchant_stores.retailer LIKE ?', ['%' . $request->get('searchtxt') . '%']);

        if ($request->has('is_ecommerce')) {
            $stores = $stores->where('merchant_stores.is_ecommerce', $is_ecommerce);
        }
        $stores = $stores->orderBy('merchant_stores.retailer', 'ASC')->get();
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "All Stores Fetched Successfully.");
        $message = trans('message.store_fetch_success');
        // API Response returned with 200 status
        return renderResponse(SUCCESS, $message, $stores);
    }

    /**
     * searchRegisteredConsumer
     * Search the Consumers based on search Criteria
     * @param  mixed $request
     * @return void
     */
    public function searchRegisteredMerchant(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Registered Merchant Search Started.");

        $registeredMerchants = MerchantStores::on(MYSQL_RO)
            ->join('registered_merchant_master', 'registered_merchant_master.id', '=', 'merchant_stores.merchant_id')
            ->leftjoin('user_bank_account_info', 'user_bank_account_info.merchant_id', '=', 'registered_merchant_master.id');

        if (strlen(trim($request['merchant_id'])) >= 3) {
            $registeredMerchants = $registeredMerchants->whereRaw('registered_merchant_master.merchant_id LIKE ?', ['%' . $request->get('merchant_id') . '%']);
        }
        if (strlen(trim($request['retailer'])) >= 3) {
            $registeredMerchants = $registeredMerchants->whereRaw('merchant_stores.retailer LIKE ?', ['%' . $request->get('retailer') . '%']);
        }

        if (trim($request['email'])) {
            $registeredMerchants = $registeredMerchants->where('registered_merchant_master.email', $request['email']);
        }
        $registeredMerchants = $registeredMerchants->select('merchant_stores.retailer', 'merchant_stores.store_id', 'registered_merchant_master.*', 'user_bank_account_info.fees_account_number', 'user_bank_account_info.routing_no', 'user_bank_account_info.account_no', 'user_bank_account_info.id as user_account_id')->groupBy('merchant_stores.id')->limit(100)->get();

        $message = trans('message.registered_merchant_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Registered Merchant Search Complete");
        return renderResponse(SUCCESS, $message, $registeredMerchants);
    }

    /**
     * getAllActiveStores
     * This function will fetch All the Active Stores
     * @param  mixed $request
     * @return void
     */
    public function getAllActiveStores(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Active Stores Fetch Started.");

        $active_status = StatusMaster::where('status', ACTIVE)->pluck('id');
        // Status master join and search with Active stores remove as client asked to show disable stores as well in the dropdown
        $activeStores = StoreUserMap::on(MYSQL_RO)->join('merchant_stores', 'store_user_map.store_id', '=', 'merchant_stores.id')
            ->selectRaw('DISTINCT merchant_stores.*');

        if ($request->get('searchtxt') != '') {
            $activeStores = $activeStores->whereRaw('merchant_stores.retailer LIKE ?', ['%' . $request->get('searchtxt') . '%']);
        }
        if ($request->get('user_id') != '') {
            $activeStores = $activeStores->where('store_user_map.user_id', $request->get('user_id'));
        }
        // for reward wheel search we need to fetch active stores only and check if assigned in a corporate parent
        if ($request->get('is_reward_wheel_search') != '') {
            $activeStores->where('merchant_stores.status', $active_status)->whereNotNull('store_user_map.user_id');
        }
        $activeStores = $activeStores->orderBy('merchant_stores.retailer', 'ASC')->get();

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Active Stores Fetched Successfully.");
        $message = trans('message.fetch_active_stores');
        // API Response returned with 200 status
        return renderResponse(SUCCESS, $message, $activeStores);
    }

    /**
     * This API enables and disables the sponsor feature for a perticular store
     */
    public function toggleStoreAsSponsor(Request $request)
    {
        //get the store details
        try {
            $store = MerchantStores::join('registered_merchant_master', 'registered_merchant_master.id', '=', 'merchant_stores.merchant_id')
                ->select('merchant_stores.merchant_id as merchant_id', 'registered_merchant_master.type')
                ->where("merchant_stores.id", $request->get('id'))->first();
            if (!empty($store)) {
                $value = $store->type == SPONSOR ? MERCHANT : SPONSOR;
                // toggle the value
                RegisteredMerchantMaster::where('id', $store->merchant_id)->update(['type' => $value]);
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Enable/Disable Sponsor for store " . $store->retailer . " has been updated");
                $message = $store->type == SPONSOR ? trans('message.sponsor_feature_disabled') : trans('message.sponsor_feature_enabled');
                // API Response returned with 200 status
                return renderResponse(SUCCESS, $message, null);
            }
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured while toggle store as sponsor", [EXCEPTION => $e]);
            $message = trans('message.db_transaction_failed');
            // Exception Returned
            return renderResponse(FAIL, $message, null);
        }
    }
    /**
     * getAllActiveRemotePayStore
     * This function will fetch all the active RemotePayStore
     */
    public function getAllActiveRemotePayStore(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Active RemotePay Stores Search Started.");
        $active_status = getStatus(ACTIVE_CODE); // Ensure this function returns a valid status

        $searchText = $request->get('searchtxt', ''); // Default to an empty string if null
        
        // Sanitize input to prevent excessive wildcard use
        $searchText = trim($searchText); 
        $searchText = preg_replace('/[%_]/', '', $searchText); // Remove % and _ to prevent abuse
        
        $activeRemotePayStores = MerchantStores::select('id', 'retailer', 'is_ecommerce')
            ->where('is_ecommerce', 1)
            ->where('retailer', 'LIKE', "%$searchText%") // Safe as Laravel uses bindings
            ->where('status', $active_status)
            ->orderBy('retailer', 'ASC')
            ->limit(20)
            ->get();

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Active Remote Pay Stores Fetched Successfully.");
        $message = trans('message.fetch_active_remotepay_stores');
        // API Response returned with 200 status
        return renderResponse(SUCCESS, $message, $activeRemotePayStores);
    }
}
