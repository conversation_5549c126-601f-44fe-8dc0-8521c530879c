<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Events\AfterSheet;
use Illuminate\Support\Facades\Log;

class AllTransactionExport implements FromCollection, WithHeadings, ShouldAutoSize, WithEvents
{
    protected $request;

    public function __construct($request)
    {
        $this->request = $request; // Declaring the request variable
    }

    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        $collection_array = $this->request['allTransaction']; // Storing the array received from request

        $transactions = array();
        foreach ($collection_array as $transaction_data) {
            // When we access this API from Remote Pay Transaction Report page
            if ($this->request['is_remote_pay_report'] == '1') {
                $nestedData['transaction_number'] = $transaction_data['transaction_number'];
                $nestedData['transaction_time'] = str_replace('<br/>', '', $transaction_data['transaction_time']);
                $nestedData['modification_time'] = implode(", ", $transaction_data['modification_time']);
                $nestedData['scheduled_posting_date'] = $transaction_data['scheduled_posting_date'];
                $nestedData['amount'] = $transaction_data['attempt_count'] > 0 ? '$' . $transaction_data['amount'] . ' ... ' . $transaction_data['updated_amount'] : '$' . $transaction_data['amount'];
                $nestedData['last_approve_tip_amount'] = $transaction_data['last_approve_tip_amount'] > 0 ? '$' . $transaction_data['last_approve_tip_amount'] : '';
                $nestedData[ 'delivery_fee' ] = $transaction_data[ 'delivery_fee' ] > 0 ? '$' . $transaction_data[ 'delivery_fee' ] : '';
                $nestedData['store_name'] = $transaction_data['store_name'];
                $nestedData['consumer_name'] = $transaction_data['consumer_name'];
                $nestedData['status'] = $transaction_data['status'];;
            } else {
                $nestedData['transaction_number'] = $transaction_data['transaction_number'];
                $nestedData['consumer_name'] = $transaction_data['consumer_name'];
                $nestedData['merchant_name'] = $transaction_data['merchant_name'];
                $nestedData['store_name'] = $transaction_data['store_name'];
                $nestedData['terminal_name'] = $transaction_data['terminal_name'];
                $nestedData['amount'] = $transaction_data['amount'];
                $nestedData['updated_amount'] = $transaction_data['updated_amount'];
                $nestedData['last_approve_tip_amount'] = $transaction_data['last_approve_tip_amount'];
                $nestedData[ 'delivery_fee' ] = $transaction_data[ 'delivery_fee' ];
                $nestedData['transaction_time'] = str_replace('<br/>', '', $transaction_data['transaction_time']);
                $nestedData['status'] = $transaction_data['status'];
            }
            array_push($transactions, $nestedData);
        }
        return collect([
            $transactions,
        ]);
    }

    public function headings(): array
    {
        Log::info($this->request);

        // When we access this API from Remote Pay Transaction Report page
        if ($this->request['is_remote_pay_report'] == '1') {
            $returnArray[] = [
                'Remote Pay Transaction Report',
            ];
            $returnArray[] = [];
            $date_range_header =  ($this->request['search_by_scheduled_posting_date'] == '1') ? 'Settlement Date Range' : 'Transaction Date Range:';
            $returnArray[] = [
                $date_range_header,
                date('m/d/Y', strtotime($this->request['from_date'])) . ' - ' . date('m/d/Y', strtotime($this->request['to_date'])),
            ];
            $returnArray[] = [];
            $returnArray[] = [
                'Transaction Number',
                'Transaction Date',
                'Modification Date (UTC)',
                'Settlement Date',
                'Amount($)',
                'Tip Amount($)',
                'Delivery Fee($)',
                'Store',
                'Consumer',
                'Status'
            ];
        } else {
            $returnArray = array(
                [
                    'CanPay All transaction ',
                ],
                [],
                [
                    'Transaction Date Range:', '',
                    date('m/d/Y', strtotime($this->request['from_date'])) . ' - ' . date('m/d/Y', strtotime($this->request['to_date'])),
                ],
                [],
                [
                    'Transaction Number',
                    'Consumer',
                    'Merchant',
                    'Store',
                    'Terminal',
                    'Amount($)',
                    'Updated Amount($)',
                    'Tip Amount($)',
                    'Delivery Fee($)',
                    'Transaction Time',
                    'Status'
                ],
            );
        }
        return $returnArray;
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event){
                $event->sheet->getStyle('A1:L5')->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);

                //Apply Center Alignment
                $event->sheet->getStyle('A:E')->getAlignment()->applyFromArray(
                    array('horizontal' => 'center')
                );
            },
        ];
    }
}
