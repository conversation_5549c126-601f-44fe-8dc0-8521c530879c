<template>
<div>
  <div v-if="loading">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Store Locator</h3>
                </div>

                <!-- /.card-header -->
                <div class="card-body">
                  <div class="row">
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="form-control"
                        placeholder="Merchant ID (Exact)"
                        id="merchant_id"
                        v-model="merchant_id"
                      />
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="form-control"
                        placeholder="Store ID (Exact)"
                        id="store_id"
                        v-model="store_id"
                      />
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                     <input
                        class="form-control"
                        placeholder="Retailer Name"
                        id="retailer"
                        v-model="retailer"
                      />
                    </div>
                  </div>
                </div>
                </div>
                  <div class="card-footer">
                    <button
                      type="button"
                      class="btn btn-success"
                      @click="searchStores()"
                    >
                      Search
                    </button>
                    <button
                      type="button"
                      @click="reset()"
                      class="btn btn-success margin-left-5"
                    >
                      Reset
                    </button>
                  </div>
                  <div class="card-body">
                  <b-table-simple
                      responsive
                      show-empty
                      bordered
                      sticky-header="800px"
                      v-if="allStoreModel.length > 0"
                    >
                      <b-thead head-variant="light">
                        <tr>
                            <th width="10%">Merchant ID</th>
                            <th width="10%">Store ID</th>
                            <th width="10%">Retailer</th>
                            <th width="10%">Timezone</th>
                            <th width="10%">Corporate Parent</th>
                            <th width="10%">Created On</th>
                            <th width="10%">Action</th>
                        </tr>
                      </b-thead>
                      <b-tbody v-for="(row, index) in allStoreModel" :key="index">
                        <b-tr>
                          <b-td class="text-left text-gray">{{
                            row.merchantID
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.store_id
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.retailer
                          }}</b-td>

                          <b-td class="text-left text-gray">{{
                            row.timezone_name
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.cp_name
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.created_at
                          }}</b-td>

                          <b-td class="text-left text-gray">
                            <a :data-user-id="row.id" class="viewStoreLocation custom-edit-btn" title="Update Store Location" variant="outline-success" style="border:none"><b>Locate</b></a>

                          </b-td>

                        </b-tr>
                      </b-tbody>
                    </b-table-simple>
                    <p v-else>No data displayed. Please refine your search criteria.</p>
                    </div>
              </div>
            </div>
          </div>
      </div>
    </section>
    <!-- Locate Modal -->
    <b-modal
      class="locate-modal"
      id="locate-modal"
      ref="modal"
      :header-text-variant="headerTextVariant"
      title="Locate Store"
      @hidden="resetLocationModal"
      @ok="showGMapModal"
      ok-title="Update"
      ok-variant="success"
      cancel-variant="outline-secondary"
      :no-close-on-esc="true"
      :no-close-on-backdrop="true"
    >

    <div class="row">
        <div class="col-md-12">
            <label for="name">
            Current Address
            </label>
            <input
            type="text"
            :value="currentAddress"
            class="form-control" disabled
            />
        </div>
    </div>


    </b-modal>
    <!-- GMAP Modal -->
    <b-modal
      class="store-locator-modal"
      id="update-store-location"
      ref="modal"
      :header-text-variant="headerTextVariant"
      title="Edit Store Location"
      @hidden="resetLocationModal"
      @ok="handleOk"
      ok-title="Save"
      ok-variant="success"
      cancel-variant="outline-secondary"
      :no-close-on-esc="true"
      :no-close-on-backdrop="true"
    >

    <div class="row">
        <gmap-map
        :center="center"
        :zoom="12"
        style="width:100%;  height: 400px;"
        >
            <gmap-marker
                :key="index"
                v-for="(m, index) in markers"
                :position="m.position"
                @click="center=m.position"
                :draggable="true"
                @dragend="dragStoreLocation"
                :icon="markerOptions"
            ></gmap-marker>
        </gmap-map>
    </div>
    <div class="row dob-row">
        <div class="col-12">
            <br>
            <h2>Search Address</h2>
            <!-- <span class="form-control-icon more-details-icon">
              <svg
                version="1.1"
                id="Layer_1"
                xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink"
                x="0px"
                y="0px"
                viewBox="0 0 307 495"
                style="enable-background: new 0 0 307 495"
                xml:space="preserve"
                height="20"
                width="20"
              >
                <g>
                  <path
                    d="M153,0c85,0,154,69,154,153c0,88-90,191-154,243C90,344,0,241,0,153C0,69,69,0,153,0z M153,27C84,27,27,84,27,153
		c0,71,76,164,126,208c51-44,127-137,127-208C280,84,223,27,153,27z"
                  />
                  <path
                    d="M153,88c36,0,66,30,66,65c0,36-30,66-66,66c-35,0-65-30-65-66C88,118,118,88,153,88z M153,115c-21,0-38,17-38,38
		c0,22,17,38,38,38c22,0,38-16,38-38C191,132,175,115,153,115z"
                  />
                </g>
              </svg>
            </span> -->
            <!-- <GmapAutocomplete class="autocomplete" country="us" @place_changed="setPlace" /> -->
            <gmap-autocomplete :value="autocomplete" placeholder="Search Your Address" @place_changed="setPlace" id="auto-search-input" class="autocomplete form-control"></gmap-autocomplete>
        </div>
    </div>
    <form ref="form" @submit.stop.prevent="update" class="needs-validation">
        <div class="row">
            <div class="col-md-12">
              <label for="name">
                Street Address
                <span class="red">*</span>
              </label>
              <input
                id="address"
                name="address"
                v-validate="'required'"
                type="text"
                v-model="address"
                class="form-control"
              />
              <span v-show="errors.has('address')" class="text-danger">{{
                errors.first("address")
              }}</span>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
              <label for="name">
                Country
                <span class="red">*</span>
              </label>
              <input
                id="country"
                name="country"
                v-validate="'required'"
                type="text"
                v-model="country"
                class="form-control"
              />
              <span v-show="errors.has('country')" class="text-danger">{{
                errors.first("country")
              }}</span>
            </div>
            <div class="col-md-6">
              <label for="name">
                State
                <span class="red">*</span>
              </label>
              <input
                id="state"
                name="state"
                v-validate="'required'"
                type="text"
                v-model="state"
                class="form-control"
              />
              <span v-show="errors.has('state')" class="text-danger">{{
                errors.first("state")
              }}</span>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
              <label for="name">
                City
                <span class="red">*</span>
              </label>
              <input
                id="city"
                name="city"
                v-validate="'required'"
                type="text"
                v-model="city"
                class="form-control"
              />
              <span v-show="errors.has('city')" class="text-danger">{{
                errors.first("city")
              }}</span>
            </div>
            <div class="col-md-6">
              <label for="name">
                Postal Code
                <span class="red">*</span>
              </label>
              <input
                id="zip"
                name="zip"
                v-validate="'required'"
                type="text"
                v-model="zip"
                class="form-control"
              />
              <span v-show="errors.has('zip')" class="text-danger">{{
                errors.first("zip")
              }}</span>
            </div>
        </div>
    </form>

    </b-modal>
   </div>
</div>
</template>
<script>
import api from "@/api/stores.js";
import commonConstants from "@/common/constant.js";
import { validationMixin } from "vuelidate";
import { required, minLength } from "vuelidate/lib/validators";
import VueGoogleAutocomplete from "vue-google-autocomplete";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "./CustomLoader/CanPayLoader.vue";
export default {
  mixins: [validationMixin],
  data() {
    return {
      add_id: String(new Date().getTime()),
      modalTitle: "",
      autocomplete: "",
      currentAddress: '',
      storeDetails: {},
      allStoreModel: {},
      headerTextVariant: "light",
      currentUser: localStorage.getItem("user")
      ? JSON.parse(localStorage.getItem("user"))
      : null,
      showReloadBtn: false,
      constants: commonConstants,
      store: {},
      transaction_type_ids: null,
      transaction_type : [],
      loading:false,
      merchant_id:"",
      store_id:"",
      retailer:"",
      center:{},
      markers: [],
      markerOptions: {
        url: '/img/green_marker.png',
        size: { width: 30, height: 35, f: "px", b: "px" },
        scaledSize: { width: 30, height: 35, f: "px", b: "px" },
     },
      places: [],
      currentPlace: null,
      fulladdress: '',
      address: '',
      state: '',
      checkAddressNotCome: false,
      googleNotReadAddress: [],
      city: '',
      zip: '',
      lat: '',
      long: '',
      country: '',
      marchentStoreId: ''
    };
  },
  created() {
    this.viewStoreLocation();
  },
  components: {
    VueGoogleAutocomplete,
    HourGlass,
    CanPayLoader
  },
  methods: {
    handleOk(bvModalEvt) {
      var self = this;
      // Prevent modal from closing
      bvModalEvt.preventDefault();
      // Trigger submit handler
      self.update();
    },
    showGMapModal(){
        var self = this;
        self.$bvModal.hide("locate-modal");
        self.$bvModal.show("update-store-location");
    },
    setPlace(place) {
        console.log(place);
        var self = this;
        self.fulladdress = place.formatted_address;
        self.autocomplete = self.fulladdress;
        self.currentPlace = place;
        self.lat = place.geometry.location.lat();
        self.long = place.geometry.location.lng();
        self.center = {
            lat: self.lat,
            lng: self.long
        };
        self.addMarker(self.lat, self.long);
        self.setLocation(place.address_components);
    },

    setLocation(addressArray){
        var self = this;
        var streetAddress = '';
        var streetNumber = '';
        var route = '';
        self.address = '';
        self.country = '';
        self.state = '';
        self.city = '';
        self.zip = '';
        for (var i = 0; i < addressArray.length; i++) {
            // street number
            if(addressArray[i]['types'][0] == 'street_number'){
                streetNumber = addressArray[i].long_name;
            }
            // route
            if(addressArray[i]['types'][0] == 'route'){
                route = addressArray[i].long_name;
            }
            // street address
            if(streetNumber !== '' || route!==''){
                streetAddress = streetNumber+" "+route;
                self.address = streetAddress;
            }
            // country
            if(addressArray[i]['types'][0] == 'country'){
                self.country = addressArray[i].short_name;
            }
            // state
            if(addressArray[i]['types'][0] == 'administrative_area_level_1'){
                self.state = addressArray[i].short_name;
            }
            // city
            if(addressArray[i]['types'][0] == 'locality'){
                self.city = addressArray[i].long_name;
            }
            // zip
            if(addressArray[i]['types'][0] == 'postal_code'){
                self.zip = addressArray[i].long_name;
            }


        }
    },

    searchStores(){
      var self = this;
      if($("#retailer").val().trim() === '' && $("#merchant_id").val().trim() === '' &&  $("#store_id").val().trim() === ''){
        error("Please provide Retailer Name or Merchant ID(exact) or Store ID(exact)");
        return false;
      }
      var request = {
        retailer: self.retailer,
        merchant_id:self.merchant_id,
        store_id:self.store_id,
      };
      self.loading = true;
      api
      .searchStores(request)
      .then(function (response) {
        if (response.code == 200) {
          self.allStoreModel = response.data;
          self.loading = false;
        } else {
          error(response.message);
          self.loading = false;
        }
      })
      .catch(function (error) {
        // error(error);
        self.loading = false;
      });
    },
    viewStoreLocation() {
      var self = this;
      $(document).on("click", ".viewStoreLocation", function(e) {
        var id = $(e.currentTarget).attr("data-user-id");
        var request = {
          id: id
        };
        api
          .getStoreDetails(request)
          .then(response => {
            if (response.code == 200) {
              self.store = response.data;
              var lat = parseFloat(self.store.lat);
              var lng = parseFloat(self.store.long);
              self.center = {lat: lat, lng: lng};
              self.lat = self.store.lat;
              self.long = self.store.long;
              self.address = self.store.address;
              self.country = self.store.county;
              self.state = self.store.state;
              self.city = self.store.city;
              self.zip = self.store.zip;
              self.marchentStoreId = self.store.id;
              self.currentAddress = self.address + ', ' + self.city + ', ' +self.state + ', ' + self.zip + ', ' +self.country;
              self.addMarker(response.data.lat, response.data.long);
              self.$bvModal.show("locate-modal");
            } else {
              error(response.message);
            }
          })
          .catch(err => {
            error(err.response.data.message);
          });
      });
    },
    resetLocationModal() {
      var self = this;
      self.store= {};

    },
    addMarker(lat, lng,store_id) {
      var self = this;
      const marker = {
        lat: parseFloat(lat),
        lng: parseFloat(lng),
        icon: self.markerOptions,
        id:   store_id
      };
      self.markers = [];
      self.markers.push({ position: marker });
      self.center = {
        lat: parseFloat(lat),
        lng: parseFloat(lng),
      };
    },
    dragStoreLocation(location){
        var self = this;
        self.center = {
            lat: location.latLng.lat(),
            lng: location.latLng.lng(),
        };
        var latlng = new google.maps.LatLng(location.latLng.lat(), location.latLng.lng());
        // This is making the Geocode request
        var geocoder = new google.maps.Geocoder();
        geocoder.geocode({ 'latLng': latlng },  (results, status) =>{
            if (status !== google.maps.GeocoderStatus.OK) {
                alert(status);
            }
            // This is checking to see if the Geoeode Status is OK before proceeding
            if (status == google.maps.GeocoderStatus.OK) {
                self.autocomplete = results[0].formatted_address;
                self.lat = results[0].geometry.location.lat();
                self.long = results[0].geometry.location.lng();
                self.center = {
                    lat: self.lat,
                    lng: self.long
                };
                // console.log(results[0].address_components);
                self.setLocation(results[0].address_components);
            }
        });
    },
    update(){
        var self =this;
        var request = {
            lat: self.lat,
            long: self.long,
            address: self.address,
            country: self.country,
            city: self.city,
            state: self.state,
            zip: self.zip,
            store_id: self.marchentStoreId
        };
        // console.log(request);
        this.$validator.validateAll().then((result) => {
            if(result){

                api
                .updateStoreLocation(request)
                .then(response => {
                    if (response.code == 200) {
                        success(response.message);
                        self.allStoreModel = {};
                        self.reset();
                        self.$bvModal.hide("update-store-location");
                        self.resetModal();
                    }
                    else{
                        error(response.message);
                    }
                })
                .catch((err) => {
                    // error(err.response.data.message);
                });
            }
            else{
                console.log('show validation error');
            }
        });
    },
    reset(){
        var self = this;
        self.retailer = "";
        self.merchant_id = "";
        self.store_id = "";
    },

  },

  mounted() {
    document.title = "CanPay - Store Locator";
  }
};
</script>
<style>
.store-locator-modal { z-index: 1001 !important;}
.pac-container {z-index: 1055 !important;}
.autocomplete { width: 45em !important;}
</style>
