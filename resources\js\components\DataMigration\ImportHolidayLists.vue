<template>
<div>
  <div v-if="is_visible == 1">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Import Holiday List</h3>
                  <b-button
                    class="btn-danger export-api-btn"
                    @click="reloadDatatable"
                    v-if="showReloadBtn"
                  >
                    <i class="fas fa-redo"></i> Reload
                  </b-button>
                </div>
                <!-- /.card-header -->
                <div class="card-body">
                  <div
                    class="alert alert-success alert-dismissible"
                    v-if="success_message != null"
                  >
                    <a
                      href="#"
                      class="close"
                      data-dismiss="alert"
                      aria-label="close"
                      style="text-decoration: none"
                      @click="success_message = null"
                      >&times;</a
                    >
                    <strong>Success!</strong> {{ success_message }}
                  </div>
                  <div class="form-group">
                    <label for="exampleInputFile">Upload Holiday List</label>
                    <button
                      type="button"
                      class="btn btn-danger ml-10"
                      style="float: right; margin-top: -12px"
                      @click="downloadSampleFile()"
                    >
                      Download Sample
                      <i class="fa fa-download ml-10" aria-hidden="true"></i>
                    </button>
                    <div class="input-group">
                      <div class="custom-file">
                        <input
                          type="file"
                          ref="holiday_list_file"
                          id="exampleInputFile"
                          v-on:change="handleFileUpload()"
                          class="custom-file-input"
                          accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
                        />
                        <label
                          for="exampleInputFile"
                          class="custom-file-label"
                          >{{ holiday_list_label }}</label
                        >
                      </div>
                    </div>
                  </div>

                  <span
                    style="float: right"
                    class="btn btn-success"
                    @click="migrate"
                    >Import Holiday List</span
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
     <section class="content-header">
        <div class="container-fluid">
          <div class="row mb-2">
            <div class="col-sm-6"></div>
          </div>
        </div>
      </section>
      <div class="hold-transition sidebar-mini">
        <section class="content">
          <div class="container-fluid">
            <div class="row">
              <div class="col-12">
                <div class="card card-success">
                  <div class="card-header">
                    <h3 class="card-title">Holiday List</h3>
                    <b-button
                      class="btn-danger export-api-btn"
                      @click="reloadDatatable"
                      v-if="showReloadBtn"
                    >
                      <i class="fas fa-redo"></i> Reload
                    </b-button>
                  </div>
                  <!-- /.card-header -->
                  <div class="card-body">
                    <table
                      id="holidayListTable"
                      class="table"
                      style="width: 100%; white-space: normal"
                    >
                      <thead>
                        <tr>
                          <th>Holiday</th>
                          <th>Holiday Year</th>
                          <th>Holiday Date</th>
                          <th>Reason</th>
                          <th>Uploaded By</th>
                          <th>Created At</th>
                        </tr>
                      </thead>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
  </div>
</div>
</template>
<script>
import api from "@/api/import.js";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "../CustomLoader/CanPayLoader.vue";
import commonConstants from "@/common/constant.js";
export default {
  components: {
    HourGlass,
    CanPayLoader
  },
  data() {
    return {
      holiday_list: null,
      holiday_list_label: "Choose File",
      success_message: null,
      is_visible: 0,
      showReloadBtn: false,
      constants: commonConstants,
    };
  },
  methods: {
    reloadDatatable() {
      var self = this;
      self.loadDT();
    },
    handleFileUpload() {
      this.holiday_list = this.$refs.holiday_list_file.files[0];
      this.holiday_list_label = this.$refs.holiday_list_file.files[0].name;
    },
    /*Submits the file to the server*/
    migrate() {
      var self = this;
      self.is_visible = 1;
      if (self.holiday_list == null) {
        self.is_visible = 0;
        error("Please upload data sheet to import holiday list.");
        return false;
      }
      /*Initialize the form data*/
      let formData = new FormData();
      formData.append("excel", self.holiday_list);
      /*call to the import excel api */
      api
        .migrateHolidayList(formData)
        .then((response) => {
          if (response.code == 200) {
            self.$refs.holiday_list_file.value = null;
            self.holiday_list = null;
            self.holiday_list_label = "Choose File";
            self.success_message = response.message;
            self.is_visible = 0;
            self.loadDT();
          } else {
            error(response.message);
            self.is_visible = 0;
          }
        })
        .catch((response) => {
          error(response);
          self.is_visible = 0;
        });
    },
    loadDT: function () {
      var self = this;
    $("#holidayListTable").DataTable({
        pagingType: "simple_numbers",
        processing: true,
        serverSide: true,
        destroy: true,
        columnDefs: [
          { orderable: false, targets: [2] },
          { className: "dt-left", targets: [0, 1, 2, 3, 4, 5] },
        ],
        order: [[2, "asc"]],
        orderClasses: false,
        language: {
          processing:
            '<i class="fa fa-spinner fa-spin fa-3x fa-fw"></i><span class="sr-only">Loading...</span> ',
          emptyTable: "No Holiday List Available.",
          search: "_INPUT_",
          searchPlaceholder: "Search records",
          oPaginate: {
            sNext: '<i class="fas fa-angle-double-right"></i>',
            sPrevious: '<i class="fas fa-angle-double-left"></i>',
          },
          sLengthMenu:
            "<label class='label_dropdown_dt'>Per page</label> _MENU_",
        },
        ajax: {
          headers: {
            Authorization: "Bearer " + localStorage.getItem("token"),
          },
          url: "/api/holidaylist",
          type: "POST",
          data: { _token: "{{csrf_token()}}" },
          dataType: "json",
          dataSrc: function (result) {
            self.showReloadBtn = false;
            return result.data;
          },
          error: function (data) {
            error(commonConstants.datatable_error);
            $("#holidayListTable_processing").hide();
            self.showReloadBtn = true;
          },
        },
        columns: [
          { data: "holiday_name" },
          { data: "holiday_year" },
          { data: "holiday_date" },
          { data: "reason" },
          { data: "added_by" },
          { data: "created_at" },
        ],
      });


      $("#holidayListTable").on("page.dt", function () {
        $("html, body").animate({ scrollTop: 0 }, "slow");
        $("th:first-child").focus();
      });

      //Search in the table only after 3 characters are typed
      // Call datatables, and return the API to the variable for use in our code
      // Binds datatables to all elements with a class of datatable
      var dtable = $("#holidayListTable").dataTable().api();

      // Grab the datatables input box and alter how it is bound to events
      $(".dataTables_filter input")
        .unbind() // Unbind previous default bindings
        .bind("input", function (e) {
          // Bind our desired behavior
          // If the length is 3 or more characters, or the user pressed ENTER, search
          if (this.value.length >= 3 || e.keyCode == 13) {
            // Call the API search function
            dtable.search(this.value).draw();
          }
          // Ensure we clear the search if they backspace far enough
          if (this.value == "") {
            dtable.search("").draw();
          }
          return;
        });
    },
    downloadSampleFile() {
      window.location.href = "sample_import_excel/holiday_list.csv";
    },
  },
  mounted() {
    var self = this;
    setTimeout(function () {
      self.loadDT();
    }, 1000);
    document.title = "CanPay - Import Holiday List";
  },
};
</script>
