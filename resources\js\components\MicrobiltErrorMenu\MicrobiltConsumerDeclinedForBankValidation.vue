<template>
<div>
    <div v-if="loading">
    <CanPayLoader/>
    </div>
    <div class="content-wrapper" style="min-height: 36px">
        <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
            <div class="col-sm-6"></div>
            </div>
        </div>
        </section>
        <div class="hold-transition sidebar-mini">
            <section class="content">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-12">
                            <!-- Card Start -->
                            <div class="card card-success">
                                <!-- Card Title -->
                                <div class="card-header">
                                    <h3 class="card-title">Consumer Declined During Microbilt Bank Validation</h3>
                                </div>

                                 <!-- Card Body -->
                                 <div class="card-body">

                                    <!-- Input box for start date -->
                                    <div class="row" >
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <input
                                                    class="start-date form-control"
                                                    placeholder="From Date"
                                                    id="start-date"
                                                    onkeydown="return false"
                                                    autocomplete="off"
                                                />

                                            </div>
                                        </div>

                                    <!-- Input box for end date -->
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <input
                                                    class="start-date form-control"
                                                    placeholder="To Date"
                                                    id="end-date"
                                                    onkeydown="return false"
                                                    autocomplete="off"
                                                />

                                            </div>
                                        </div>
                                    </div>

                                    <div class="row ml-1">
                                        <p style="font-size:12px; color:red; font-weight:bolder;">Maximum accepted range between start date and end date is 7 days</p>
                                    </div>

                                </div>

                                <!-- Card for the buttons -->
                                <div class="card-footer">
                                    <button
                                        type="button"
                                        class="btn btn-success"
                                        @click="getReportOfConsumerAcceptedWithWarning"
                                    >
                                        Search
                                    </button>
                                    <button
                                        type="button"
                                        @click="reset"
                                        class="btn btn-success margin-left-5"
                                    >
                                        Reset
                                    </button>
                                </div>

                                <!--  To show no data found -->
                                <div class="card-body" v-if="customerDetails.length == 0 || customerDetails == null">
                                    <div>
                                        <p>No data displayed. Please refine your search criteria.</p>
                                    </div>
                                </div>

                                <!-- To show table  -->
                                <div class="container-fluid p-3" v-if="customerDetails.length>0">
                                    <b-table
                                        bordered
                                        head-variant=light
                                        :items="customerDetails"
                                        :fields="fields"
                                        responsive
                                        aria-controls="consumerBlockedTable"
                                    >

                                    </b-table>

                                    <!-- for pagination -->

                                    <div>
                                        <b-pagination
                                            id="consumerBlockedTable"
                                            v-model="currentPage"
                                            :total-rows="totalItems"
                                            :per-page="perPage"
                                            align="right"
                                            size="sm"
                                            class="mb-3"
                                        ></b-pagination>
                                    </div>


                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </div>
</div>
</template>
<script>
import api from "@/api/MicrobiltErrorReport";
import moment from "moment";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "../CustomLoader/CanPayLoader.vue"
export default {
    components: {
        HourGlass,
        CanPayLoader
    },
    data(){
        return {
            // defining the b-table columns
            fields:[
                {
                   key: "consumer_name",
                   label: "Name"
                },
                {
                    key: "phone",
                    label: "Phone"
                },
                {
                    key: "address",
                    label: "Address"
                },
                {
                    key: "bank_link_type",
                    label: 'Bank Link Type'
                },
                {
                    key: "account_no",
                    label: "Account Number"
                },
                {
                    key: "routing_no",
                    label: "Routing Number"
                },
                {
                    key: "return_transactions",
                    label: "Return Transactions"
                },
                {
                    key: "total_transactions",
                    label: "Total Transactions"
                },
                ],
            customerDetails:[],
            currentPage:1,
            perPage:10,
            totalItems: 0,
            loading:false,
        }
    },
    mounted(){
        $("#start-date,#end-date").datepicker({
            format: "mm/dd/yyyy",
            autoclose: true,
            todayHighlight: true,
        });
        $("#start-date").val(moment(new Date()).format("MM/DD/yyyy"));
        $("#end-date").val(moment(new Date()).format("MM/DD/yyyy"));
    },
    watch: {
        currentPage(newVal){
            this.getReportOfConsumerAcceptedWithWarning();
        }
    },
    computed: {
    },
    methods:{
        getReportOfConsumerAcceptedWithWarning(){

            let self = this;
            self.start_date = moment($("#start-date").val()).format("YYYY-MM-DD");
            self.end_date = moment($("#end-date").val()).format("YYYY-MM-DD");
            const startDate = new Date(self.start_date);
            const endDate = new Date(self.end_date);
            const differenceInMilliseconds = endDate - startDate;
            const differenceInDays = differenceInMilliseconds / (24 * 60 * 60 * 1000);

            if (differenceInDays<0 || differenceInDays>7) {
                return error("Maximum accepted date range is of 7 days")
            } else if(self.start_date > self.end_date) {
                return error("Please provide a valid date range")
            } else if(self.start_date == '' || self.end_date == '') {
                return error("Please provide a valid date")
            }
            self.loading = true;
            const request = {
                "from_date": self.start_date,
                "to_date": self.end_date,
                "perPage": self.perPage,
                "currentPage": self.currentPage,
            }

            api
                .getReportOfConsumerDeclinedForBankValidation(request)
                .then((response)=>{

                    self.customerDetails = response.data.result;
                    self.totalPage = response.data.total_pages;
                    self.totalItems = response.data.total;
                    self.loading = false;

                })
                .catch((err)=>{

                    error(err.response.data.message);
                    self.loading = false;

                })
        },
        reset(){

            let self = this;
            self.start_date = "";
            self.end_date = "";
            $("#start-date").val('');
            $("#end-date").val('');

        },
    }
}
</script>
