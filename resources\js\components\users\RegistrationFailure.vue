<template>
<div>
    <div v-if="loading">
        <CanPayLoader/>
    </div>
    <div class="content-wrapper" style="min-height: 36px;">
        <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
            <div class="col-sm-6"></div>
            </div>
        </div>
        </section>
        <div class="hold-transition sidebar-mini">
            <section class="content">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-12">
                            <div class="card card-success">
                                <div class="card-header">
                                    <h3 class="card-title">MX Related Bank Link Failure During Registration</h3>
                                </div>

                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4">
                                        <div class="form-group">
                                            <input
                                            class="form-control"
                                            placeholder="Consumer Name (min 3 chars)"
                                            id="consumer"
                                            v-model="consumer"
                                            />
                                        </div>
                                        </div>
                                        <div class="col-md-4">
                                        <div class="form-group">
                                            <input
                                            class="form-control"
                                            placeholder="Phone No (Exact)"
                                            id="phone_no"
                                            v-model="phone_no"
                                            />
                                        </div>
                                        </div>
                                        <div class="col-md-4">
                                        <div class="form-group">
                                            <input
                                            class="form-control"
                                            placeholder="Email (Exact)"
                                            id="email"
                                            v-model="email"
                                            />
                                        </div>
                                        </div>
                                    </div>
                                <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                    <input
                                        class="start-date form-control"
                                        placeholder="From Date"
                                        id="start-date"
                                        onkeydown="return false"
                                        autocomplete="off"
                                    />
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                    <input
                                        class="end-date form-control"
                                        placeholder="To Date"
                                        id="end-date"
                                        onkeydown="return false"
                                        autocomplete="off"
                                    />
                                    </div>
                                </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                        <input type="checkbox" name="v1_consumer" id="v1_consumer" v-model="v1_consumer" /> V1 Consumer
                                        </div>
                                    </div>
                                </div>
                                </div>
                                <div class="card-footer">
                                    <button
                                    type="button"
                                    class="btn btn-success"
                                    @click="searchConsumer()"
                                    >
                                    Search
                                    </button>
                                    <button
                                    type="button"
                                    class="btn btn-success"
                                    @click="reset()"
                                    >
                                    Reset
                                    </button>
                                </div>
                                <div class="card-body">
                                <div class="row"  v-if="allConsumer.length>0">
                                    <div class="col-12">
                                        <b-table-simple
                                            id="registrationFailureTable"
                                            responsive
                                            show-empty
                                            bordered
                                            sticky-header="800px"
                                            >
                                            <b-thead head-variant="light">
                                                <b-tr>
                                                <b-th class="text-left">Name</b-th>
                                                <b-th class="text-left">Email</b-th>
                                                <b-th class="text-left">Phone</b-th>
                                                <b-th class="text-center">Action</b-th>
                                                </b-tr>
                                            </b-thead>
                                            <b-tbody v-for="(row, index) in allConsumer" :key="index">
                                                <b-tr>
                                                <b-td class="text-left text-gray">{{
                                                    row.name
                                                }}</b-td>
                                                <b-td class="text-left text-gray">{{
                                                    row.email
                                                }}</b-td>
                                                <b-td class="text-left text-gray">{{
                                                    row.phone
                                                }}</b-td>
                                                <b-td class="text-center text-gray">
                                                    <a :data-session-id="row.session_id" :data-mx-consumer-id="row.mx_consumer_id" class="retryMXBankFetchOnError custom-edit-btn" title="Fetch the latest account from MX" variant="outline-success" style="border:none" v-if="row.mx_consumer_id"><i class="nav-icon fa fa-cog"></i></a>                   
                                                </b-td>
                                                </b-tr>
                                            </b-tbody>
                                        </b-table-simple>
                                <b-pagination
                                    v-model="currentPage"
                                    :total-rows="totalItems"
                                    :per-page="perPage"
                                    aria-controls="registrationFailureTable"
                                    align="right"
                                    prev-text="Prev"
                                    next-text="Next"
                                    :ellipsis="true"
                                    :limit="5"
                                ></b-pagination>
                                    </div>
                                </div>
                                <div class="row" v-else>
                                  <div class="p-4 col-12">
                                    <p>No data displayed. Please refine your search criteria.</p>
                                </div>
                                </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </div>
</div>
</template>
<script>
import api from "@/api/user.js";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "../CustomLoader/CanPayLoader.vue";
import moment from "moment";
export default {
  components: {
    HourGlass,
    CanPayLoader
  },
    data() {
        return {
            consumer: "",
            email:"",
            phone_no:"",
            v1_consumer:false,
            loading:false,
            allConsumer:[],
            mxConsumerID:"",
            session_id:"",
            from_date:"",
            to_date:"",
            currentPage: 1,
            totalItems: 0,
            perPage: 10,
        }
    },
    created(){
    this.fetchMXBankData();
    },
    methods: {
        validateEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        },
        reset(){
         let self = this;
         self.consumer = "";
         self.phone_no = "";
         self.email = "";
        },
        fetchMXBankData() {
        var self = this;
        $(document).on("click", ".retryMXBankFetchOnError", function (e) {
            var session_id = $(e.currentTarget).attr("data-session-id");
            var mxConsumerID = $(e.currentTarget).attr("data-mx-consumer-id");
            self.session_id = session_id;
            self.mxConsumerID = mxConsumerID;
            var swal_title = 'Are you sure?';
            var swalText = 'Do you want to fetch the latest account from MX for this consumer?';
            self.mxBankDataFetchAlert(swal_title, swalText, false);
        });
        },
        mxBankDataFetchAlert(swal_title, swalText, skipNotConnectedMember) {
        var self = this;
        Vue.swal({
            title: swal_title,
            text: swalText,
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#149240",
            confirmButtonText: "Yes",
            cancelButtonText: "No, cancel it!",
            closeOnConfirm: true,
            closeOnCancel: true,
            width: '800px'
            }).then((result) => {
                if (result.isConfirmed) {
                self.callMXBankData(skipNotConnectedMember);
                }
            });
        },
        callMXBankData(skipNotConnectedMember) {
        var self = this;
        var request = {
            'userID':self.session_id,
            'mxConsumerID':self.mxConsumerID,
            'skipNotConnectedMember':skipNotConnectedMember,
        };
        api.fetchMXBankData(request)
        .then(response => {
            if (response.code == 200) {
                delete response.data.consumer_id;
                if(self.v1_consumer == false){
                    response.data["sessionId"] = self.session_id;           
                }
                else{
                    response.data["session_id"] = self.session_id;
                }
                self.callConsumerRegistrationBankAPI(response.data);
            } else {
                error(response.message);
            }
        })
        .catch(err => {
            if(err.response.data.code ==597){
                var swal_title = 'Are you sure?';
                var swalText = err.response.data.message;
                self.mxBankDataFetchAlert(swal_title, swalText, true);
            } else {
                Vue.swal(err.response.data.message, '', 'error')
            }
        });
        },
        callConsumerRegistrationBankAPI(request) {
        var self = this; 
        api.callConsumerRegistrationBankAPI(request, self.v1_consumer)
        .then(response => {
            if (response.code == 200) {
                if(self.v1_consumer == false){
                    Vue.swal("Done!", 'Consumer Registered Successfully.', "success");
                }
                else{
                    Vue.swal("Done!", 'V1 Consumer Onboarded Successfully.', "success");
                }
                self.searchConsumer();
            } else {
                error(response.message);
            }
        })
        .catch(err => {
            Vue.swal(err.response.data.message, '', 'error')
        });
        },
        checkValidation(){
            var self = this;

            if( self.consumer!="" && self.consumer.length<3){
                error("Please provide Name (Min 3 Chars) or email(exact) or phone no(exact)");
                return false;
            }
            else if(self.phone_no !="" && self.phone_no.length < 10){
                error("Please provide Name (Min 3 Chars) or email(exact) or phone no(exact) or Date Range");
                return false;                
            }
            else if(self.email!="" && self.validateEmail(email)){
                error("Please provide Name (Min 3 Chars) or email(exact) or phone no(exact) or Date Range");
                return false;    
            }
            else if(self.consumer == "" && self.phone_no == "" && self.email == "" && self.to_date == "" && self.from_date == ""){
                error("Please provide Name (Min 3 Chars) or email(exact) or phone no(exact) or Date Range");
                return false;               
            }
            else if(self.from_date != "" && self.to_date == ""){
                error("Please provide Name (Min 3 Chars) or email(exact) or phone no(exact) or Date Range");
                return false;      
            }
            else if(self.to_date != "" && self.from_date == ""){
                error("Please provide Name (Min 3 Chars) or email(exact) or phone no(exact) or Date Range");
                return false;   
            }
            else if (moment($("#start-date").val()).format("YYYY-MM-DD") >moment().format("YYYY-MM-DD") && $("#start-date").val()!= '') {
             error("Start date cannot be from future.");
             return false;
            }
            else if (moment($("#end-date").val()).format("YYYY-MM-DD") >moment().format("YYYY-MM-DD") && $("#end-date").val()!= ''
            ) {
              error("End date cannot be from future.");
              return false;
            }
            else if (moment($("#end-date").val()).diff(moment($("#start-date").val()), 'days')+1 > 14) {
              error("The date range difference should not be greater than 14 days.");
              return false;
            }
            return true;
        },
        searchConsumer(){
            var self = this;
             if($("#start-date").val()!=''){
               self.from_date = moment($("#start-date").val()).format("YYYY-MM-DD");
             }else{
               self.from_date = '';
             }
             if($("#end-date").val()!=''){
               self.to_date = moment($("#end-date").val()).format("YYYY-MM-DD");
             }else{
               self.to_date = '';
            }
            if(self.checkValidation()){
                self.loading = true;
                const payload = {
                    consumer: self.consumer,
                    phone_no: self.phone_no,
                    email: self.email,
                    v1_consumer: self.v1_consumer == false ? 0 : 1,
                    to_date: self.to_date,
                    from_date: self.from_date,
                    page: self.currentPage,
                    perPage: self.perPage,
                    currentPage: self.currentPage
                }

                api
                .getRegistrationFailure(payload)
                .then((response) => {
                    self.allConsumer = response.data.result;
                    self.totalItems = response.data.total; 
                    self.loading = false;
                })
                .catch((err)=>{
                    error(err.response.data.message);
                    self.loading = false;
                })
            }
        }
    },
    mounted() {
        $("#start-date,#end-date").datepicker({
        format: "mm/dd/yyyy",
        autoclose: true,
        todayHighlight: true,
        });
        $("#start-date , #end-date").datepicker("setDate", new Date());
        document.title = "CanPay - Registration Failure";
    },
    beforeDestroy(){
        $(document).off('click', '.retryMXBankFetchOnError');
    },
    watch: {
        currentPage(newVal){
            self.currentPage = newVal;
            this.searchConsumer();
        }
    },
}
</script>
