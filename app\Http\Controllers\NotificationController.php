<?php

namespace App\Http\Controllers;

use App\Http\Factories\Firebase\FirebaseFactory;
use App\Models\GlobalNotification;
use App\Models\TerminalMaster;
use App\Models\NotificationTerminalMap;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class NotificationController extends Controller
{
    public function __construct()
    {
        $this->firebase = new FirebaseFactory();
    }

    /**
     * getAllNotifications
     * Listing page for Global Notifications along with Server Side Pagination in Datatable
     * @param  mixed $request
     * @return void
     */
    public function getAllNotifications(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Global notification search started...");
        // Validating input request
        $this->validate($request, [
            'title' => VALIDATION_REQUIRED
        ]);

        // Search with in Global Notifications
        $notifications = $this->_getGlobalNotificationSearch($request);

        $message = trans('message.global_notification_fetch_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Global notification search started complete.");
        return renderResponse(SUCCESS, $message, $notifications);
    }


    /**
     * _getGlobalNotificationSearch
     * Fetch the Global Notifications
     * @param  mixed $searchArray
     * @return void
     */
    private function _getGlobalNotificationSearch($request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Global notification Search Started.");

        $sql = "SELECT * FROM global_notifications WHERE 1 ";

        $searchStr = [];
        if (trim($request['title'])) {
            $sql .= " AND title LIKE ? ";
            array_push($searchStr,'%' . $request['title'] . '%');
        }
        $sql .= "  ORDER BY created_at DESC LIMIT 100";
        $notifications = DB::connection(MYSQL_RO)->Select($sql,$searchStr);

        $notificationsArr = [];
        if (!empty($notifications)) {
            foreach ($notifications as $notification) {

                $data = [];
                $data['title'] = $notification->title;
                $data['notification'] = $notification->notification;
                $data['sent'] = $notification->sent;
                $data['sent_by'] = $notification->sent_by;
                $data['sent_at'] = date('m-d-Y h:i A', strtotime($notification->sent_at));
                $data['edit'] = $notification->id;
                $data['created_at'] = date('m-d-Y h:i A', strtotime($notification->created_at));

                array_push($notificationsArr, $data);
            }
        } else {
            $notificationsArr = [];
        }

        return $notificationsArr;
    }

    /**
     * addNotification
     * This function will add the Global Notification in database
     * @param  mixed $request
     * @return void
     */
    public function addNotification(Request $request)
    {
        $rule = array(
            'title' => VALIDATION_REQUIRED,
            'notification' => VALIDATION_REQUIRED,
        );
        $this->__validate($request->all(), $rule);

        $user_details = Auth::user(); // Fetching Logged In User Details

        // Insertion begins
        $global_notifications = new GlobalNotification();
        $global_notifications->title = $request->get('title');
        $global_notifications->notification = $request->get('notification');
        $global_notifications->added_by = $user_details->user_id;
        $global_notifications->save();

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Global Notification Created Successfully and inserted in Database with Notification ID : " . $global_notifications->id . " by User ID : " . $user_details->user_id);
        $message = trans('message.global_notification_creation_success');
        // API Response returned with 200 status
        return renderResponse(SUCCESS, $message, $global_notifications);
    }

    /**
     * sendNotification
     * This function will update the firebase for notification count
     * @param  mixed $request
     * @return void
     */
    public function sendNotification(Request $request)
    {
        $rule = array(
            'notification_id' => VALIDATION_REQUIRED,
        );
        $this->__validate($request->all(), $rule);
        $user_details = Auth::user(); // Fetching Logged In User Details

        // // Fetching all terminals to send notifications
        $terminals = TerminalMaster::all();
        // Sending notifications to the terminals
        foreach ($terminals as $terminal) {
            //After implementing the read/unread count this logic is changed, the unread notifications are counted from DB and then incremented + 1
            $unreadNotifications = GlobalNotification::join('notification_terminal_maps','global_notifications.id','=','notification_terminal_maps.global_notification_id')
            ->join('terminal_master','terminal_master.id','=','notification_terminal_maps.terminal_id')
            ->where('terminal_master.unique_identification_id',$terminal->unique_identification_id)
            ->where('notification_terminal_maps.is_read','0')
            ->select('global_notifications.title','global_notifications.created_at','notification_terminal_maps.id','notification_terminal_maps.is_read','global_notifications.notification')
            ->orderBy('notification_terminal_maps.created_at','DESC')->get()->count();

            $notification_count = $unreadNotifications!=0 ? $unreadNotifications + 1 : 1;
            $data = array(
                'id' => $terminal->id,
                'unique_identification_id' => $terminal->unique_identification_id,
                'notification_count' => $notification_count,
            );
            $this->firebase->updateTerminalNotificationCountIntoFireStore($data);

            //Add notification in the Notification Terminal Map table
            $notification_terminal_map = new NotificationTerminalMap;
            $notification_terminal_map->global_notification_id = $request->get('notification_id');
            $notification_terminal_map->terminal_id = $terminal->id;
            $notification_terminal_map->save();
        }
        
        //Update the global notification status to sent
        $global_notification = GlobalNotification::find($request->get('notification_id')); // Fetching Global Notification details
        $global_notification->sent = 1;
        $global_notification->sent_by = $user_details->user_id;
        $global_notification->sent_at = Carbon::now();
        $global_notification->save();

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Global Notification Sent Successfully and updated in firebase with Notification ID : " . $global_notification->id . " by User ID : " . $user_details->user_id);
        $message = trans('message.global_notification_sent_success');
        // API Response returned with 200 status
        return renderResponse(SUCCESS, $message, null);
    }
}
