<template>
<div>
<div v-if="loading">
  <CanPayLoader/>
</div>
  <div class="content-wrapper" style="min-height: 36px;">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">V1 Consumer Details Edit</h3>
                </div>

                <div class="card-body">
                <div class="row">
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="form-control"
                        placeholder="Consumer Name (Min 3 chars)"
                        id="consumer"
                        v-model="consumer"
                      />
                    </multiselect>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="form-control"
                        placeholder="Phone No. (exact)"
                        id="phone_no"
                        v-model="phone_no"
                      />
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="form-control"
                        placeholder="Email (exact)"
                        id="email"
                        v-model="email"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div class="card-footer">
                <button
                  type="button"
                  class="btn btn-success"
                  @click="searchConsumer()"
                >
                  Search
                </button>
                <button
                  type="button"
                  class="btn btn-success margin-left-5"
                  @click="reset()"
                >
                  Reset
                </button>
              </div>
                <!-- /.card-header -->
                <div class="card-body">
                  <div class="row">
                  <div class="col-12"><b-table-simple
                      responsive
                      show-empty
                      bordered
                      sticky-header="800px"
                    >
                      <b-thead head-variant="light">
                        <b-tr>
                          <b-th class="text-left">Name</b-th>
                          <b-th class="text-left">Email</b-th>
                          <b-th class="text-left">Phone</b-th>
                          <b-th class="text-center">Action</b-th>
                        </b-tr>
                      </b-thead>
                      <b-tbody v-for="(row, index) in allConsumerModel" :key="index">
                        <b-tr>
                          <b-td class="text-left text-gray">{{
                            row.consumer_name
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.email
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.phone
                          }}</b-td>
                          <b-td class="text-center text-gray"><a @click="viewConsumerDetails(row.user_id)" class="custom-edit-btn" title="Edit Consumer Details" variant="outline-success" style="border:none"><i class="nav-icon fas fa-edit"></i></a></b-td>
                        </b-tr>
                      </b-tbody>
                    </b-table-simple>
                  </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>

    <!-- Consumer Modal Details Start -->
    <b-modal
      id="consumer-modal"
      ref="modal"
      :header-text-variant="headerTextVariant"
      title="Edit Consumer Details"
      @hidden="resetModal"
      ok-title="Save"
      ok-variant="success"
      cancel-variant="outline-secondary"
      @ok="handleOk"
      :no-close-on-esc="true"
      :no-close-on-backdrop="true"
    >
      <form ref="form" @submit.stop.prevent="save" class="needs-validation">
        <div class="row">
          <div class="col-md-4" >
            <label for="user_type">
              First Name
            </label>
            <span class="red">*</span>
            <input
              name="first_name"
              class="form-control"
              placeholder="First Name"
              id="first_name"
              autocomplete="off"
              v-model="consumerDetails.first_name"
              v-validate="'required|alpha_spaces'"
            />
            <span v-show="errors.has('first_name')" class="text-danger">{{
              errors.first("first_name")
            }}</span>
            </div>
            <div class="col-md-4" >
            <label for="user_type">
              Middle Name
            </label>
            <input
              name="middle_name"
              class="form-control"
              placeholder="Middle Name"
              id="middle_name"
              autocomplete="off"
              v-validate="'alpha_spaces'"
              v-model="consumerDetails.middle_name"
            />
            <span v-show="errors.has('middle_name')" class="text-danger">{{
              errors.first("middle_name")
            }}</span>
            </div>
            <div class="col-md-4" >
            <label for="user_type">
              Last Name
            </label>
            <span class="red">*</span>
            <input
              name="last_name"
              class="form-control"
              placeholder="Last Name"
              id="last_name"
              autocomplete="off"
              v-model="consumerDetails.last_name"
              v-validate="'required|alpha_spaces'"
            />
            <span v-show="errors.has('last_name')" class="text-danger">{{
              errors.first("last_name")
            }}</span>
            </div>
            <div class="col-md-12" >
            <label for="user_type">
              Email
            </label>
            <span class="red">*</span>
            <input
              name="email"
              class="form-control"
              placeholder="Email"
              id="email"
              autocomplete="off"
              v-model="consumerDetails.email"
              v-validate="'required'"
            />
            <span v-show="errors.has('email')" class="text-danger">{{
              errors.first("email")
            }}</span>
            </div>
            <div class="col-md-12">
            <label for="user_type">
              Phone No.
            </label>
            <span class="red">*</span>
            <input
              name="phone"
              class="form-control"
              placeholder="Phone No."
              id="phone"
              autocomplete="off"
              v-model="consumerDetails.phone"
              v-validate="'required'"
            />
            <span v-show="errors.has('phone')" class="text-danger">{{
              errors.first("phone")
            }}</span>
            </div>
        </div>
      </form>
    </b-modal>
    <!-- Consumer Modal Details End -->

  </div>
</div>
</template>
<script>
import api from "@/api/user.js";
import moment from "moment";
import { validationMixin } from "vuelidate";
import { required, minLength } from "vuelidate/lib/validators";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "../CustomLoader/CanPayLoader.vue"
export default {
  mixins: [validationMixin],
  data() {
    return {
      allConsumerModel: {},
      currentUser: localStorage.getItem("user")
        ? JSON.parse(localStorage.getItem("user"))
        : null,
      consumer:"",
      email:"",
      phone_no:"",
      isLoading: false,
      loading:false,
      consumerDetails:{},
      headerTextVariant: "light",
    };
  },
  components: {
    HourGlass,
    CanPayLoader
  },
  created() {
  },
  methods: {
    reset(){
      var self = this;
      self.consumer = "";
      self.email = "";
      self.phone_no = "";
    },
    resetModal() {
      var self = this;
      self.status = "";
    },
    handleOk(bvModalEvt) {
      var self = this;
      // Prevent modal from closing
      bvModalEvt.preventDefault();
      // Trigger submit handler
      self.editConsumerDetails();
    },
    searchConsumer(){
      var self = this;
      if((self.consumer).trim().length < 3 && $("#phone_no").val().trim() === '' &&  $("#email").val().trim() === ''){
        error("Please provide Consumer name (Min 3 chars) or email(exact) or phone no(exact)");
        return false;
      }
      var request = {
        consumer: self.consumer,
        email:self.email,
        phone_no:self.phone_no,
      };
      self.loading = true;
      api
      .searchV1Consumer(request)
      .then(function (response) {
        if (response.code == 200) {
          self.allConsumerModel = response.data;
          self.loading = false;
        } else {
          error(response.message);
          self.loading = false;
        }
      })
      .catch(function (error) {
        // error(error);
        self.loading = false;
      });
    },
    viewConsumerDetails(userID){
      var self = this;
      var request = {
          'userID':userID,
      };
      api.getV1consumerdetails(request)
        .then(response => {
          if (response.code == 200) {
              self.consumerDetails = response.data;
              self.$bvModal.show("consumer-modal");
          } else {
              error(response.message);
          }
      })
      .catch(err => {
          error(err.response.data.message);
      });
    },
    editConsumerDetails(){
      var self = this;
      
      this.$validator.validateAll().then((result) => {
      if (result) {
        var request = {
          id: self.consumerDetails.user_id,
          first_name: self.consumerDetails.first_name,
          middle_name: self.consumerDetails.middle_name,
          last_name: self.consumerDetails.last_name,
          phone: self.consumerDetails.phone,
          email: self.consumerDetails.email
        };
        api
        .updateConsumerDetails(request)
        .then((response) => {
          if ((response.code = 200)) {
            success(response.message);
            self.$bvModal.hide("consumer-modal");
            self.consumerDetails = {};
            self.searchConsumer();
          } else {
            error(response.message);
          }
        })
        .catch((err) => {
          error(err);
        });
        }
      });
    }
  },
  mounted() {
  }
};
</script>

