const addRoutingNumber = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/addWhitelistRoutingNumber', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const addRoutingToMaster = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/addroutingtomaster', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const declineRouting = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/declinerouting', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const viewTransactionHistory = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/viewtransactionhistory', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

export default {
    addRoutingNumber,
    addRoutingToMaster,
    declineRouting,
    viewTransactionHistory
};