<template>
<div>
    <div v-if="is_loading == 1">
        <CanPayLoader/>
    </div> 
<div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>   
    <div class="hold-transition sidebar-mini">
        <section class="content">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-12">
                        <div class="card card-success">
                            <div class="card-header">
                                <h3 class="card-title">Missing Bank Survey</h3>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-4">
                                        <div class="form-group">
                                            <input
                                                class="form-control"
                                                placeholder="Search by Routing Number"
                                                id="routing_no"
                                                v-model="routing_no"
                                                :maxlength="9"
                                                @keypress="isNumber($event)"
                                            />
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="form-group">
                                            <input
                                                class="form-control"
                                                placeholder="Search by Email"
                                                id="consumer_detail"
                                                v-model="consumer_email"
                                            />
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="form-group">
                                            <input
                                                class="form-control"
                                                placeholder="Search by Phone"
                                                id="consumer_detail"
                                                v-model="consumer_phone"                                                
                                                :maxlength="10"
                                                @keypress="isNumber($event)"
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer">
                                <button
                                    type="button"
                                    class="btn btn-success"
                                    @click="fetchBankLinkSurvey"
                                >
                                Search
                                </button>
                                <button
                                    type="button"
                                    class="btn btn-success margin-left-5"
                                    @click="reset"
                                >
                                Reset
                                </button>
                            </div>
                            <div class="card-body" v-if="items.length>0">
                                <b-table-simple
                                    bordered
                                    id="missing_bank_survey_id"
                                    sticky-header="900px"
                                >
                                <b-thead head-variant="light">
                                <tr>
                                    <th>Bank Name</th>
                                    <th>Routing Number</th>
                                    <th>Reason</th>
                                    <th>Bank URL</th>
                                    <th>Consumer Email</th>
                                    <th>Consumer Phone</th>
                                    <th>Event Type</th>
                                </tr>
                                </b-thead>
                                <b-tbody v-for="(row, index) in items" :key="index">
                                <b-tr>
                                    <b-td class="text-gray">
                                     {{row.bank_name}}
                                    </b-td>
                                    <b-td class="text-gray">{{
                                    row.routing_no
                                    }}</b-td>
                                    <b-td class="text-gray">{{
                                    row.reason
                                    }}</b-td>
                                    <b-td :class="row.bank_url == null ?'text-gray':'text-primary'">
                                        <span v-if="row.bank_url == null" >Not Provided</span>
                                        <span v-else>
                                            <a :href="getweburl(row.bank_url)" target="_blank">{{row.bank_url}}</a>
                                        </span>

                                    </b-td>
                                    <b-td class="text-gray">{{
                                    row.consumer_email.toLowerCase()
                                    }}</b-td>
                                    <b-td class="text-gray">{{
                                    row.consumer_phone
                                    }}</b-td>
                                    <b-td class="text-gray first-letter-capitalize">
                                    {{ row.event_type == "bankchange"?"Bank Change":row.event_type }}
                                    </b-td>
                                </b-tr>
                                </b-tbody>
                                </b-table-simple>
                                <b-pagination
                                    v-model="currentPage"
                                    :total-rows="totalItems"
                                    :per-page="perPage"
                                    aria-controls="missing_bank_survey_id"
                                    align="right"
                                    prev-text="Prev"
                                    next-text="Next"
                                    :ellipsis="true"
                                    :limit="5"
                                ></b-pagination>
                            </div>
                            <div v-else class="p-4">
                                <p>No data displayed. Please refine your search criteria.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>     
    </div>
</div>
</div>
</template>
<script>
import api from '@/api/bank.js'
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "./CustomLoader/CanPayLoader.vue"
export default {
components: {
    HourGlass,
    CanPayLoader
},
data(){
    return{
        consumer_email:"",
        consumer_phone:"",
        routing_no:"",
        items:[],
        currentPage:1,
        totalItems:0,
        perPage:10,
        is_loading:false
    }
},
methods:{
    getweburl(url){
        if (url.indexOf("https://") !== -1) {
            return url;
        }
        return "https://"+url;
    },
    reset(){
        let self = this;
        self.routing_no = '';
        self.consumer_email = '';
        self.consumer_phone = '';
    },
    fetchBankLinkSurvey(){
        let self = this;
        if(self.routing_no == '' && self.consumer_email == '' && self.consumer_phone == ''){
            self.currentPage = 1;
        }
        if(self.routing_no.length != 9 && self.routing_no.length>0){
            error("Please provide valid routing number");
            return;
        }
        if(self.consumer_phone.length != 10 && self.consumer_phone.length>0){
            error("Please provide valid phone number");
            return;
        }
        self.fetchBankLinkSurveyHistory();
    },
    isNumber: function (evt) {
        evt = evt ? evt : window.event;
        var charCode = evt.which ? evt.which : evt.keyCode;
        if (
        charCode != 46 &&
        charCode != 45 &&
        charCode > 31 &&
        (charCode < 48 || charCode > 57)
        ) {
        evt.preventDefault();
        } else {
        return true;
        }
    },
    fetchBankLinkSurveyHistory(){
        let self = this;
        self
        const payload = {
            currentPage:self.currentPage,
            perPage:self.perPage,
            consumer_email:self.consumer_email,
            consumer_phone:self.consumer_phone,
            routing_no:self.routing_no
        }
        self.is_loading = true;
        api
        .getBankLinkSurveyHistory(payload)
        .then((response) => {
            self.totalItems = response.data.total;
            self.items = response.data.result;
            self.is_loading = false; 
        })
        .catch((err) => {
            error(response.data.message);
            self.is_loading = false;
        })
    }
},
watch: {
    currentPage(newVal){
        this.fetchBankLinkSurveyHistory();
    }
},
mounted(){
    let self = this;
    self.fetchBankLinkSurveyHistory();
}
}
</script>
<style scoped>
.first-letter-capitalize{
    text-transform: capitalize;
}
</style>
