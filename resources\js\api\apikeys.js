const getstoreapikeysexport = (request) => {
    var header = {
        responseType: 'blob',
    };
    return new Promise((res, rej) => {
        axios.post('/api/export/storeapikeys', request, header)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const searchApiKeys = (request) => {
    return new Promise((res, rej) => {
        axios.post('/api/getallapikeys', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
export default {
    getstoreapikeysexport,
    searchApiKeys
};