<template>
<div>
  <div v-if="loading">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Transaction Report</h3>
                </div>
                <div class="card-body">
                <div class="row">
                  <div class="col-md-4">
                    <div class="form-group">
                      <multiselect
                        id="corporateparent"
                        name="corporateparent"
                        v-model="selectedCp"
                        placeholder="Select Corporate Parent (Min 3 chars)"
                        label="corporateparent"
                        track-by="id"
                        :options="cpList"
                        :multiple="false"
                        :loading="isLoadingCp"
                        :internal-search="false"
                        @search-change="getAllActiveCorporateParent"
                        @select="refreshStores"
                      ></multiselect>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <multiselect
                        id="store"
                        name="store"
                        v-model="selectedStore"
                        placeholder="Select Store (Min 3 chars)"
                        label="retailer"
                        track-by="id"
                        :options="storeList"
                        :loading="isLoadingSt"
                        :internal-search="true"
                      ></multiselect>
                    </div>
                  </div>
                </div>
              </div>
              <div class="card-footer">
                <button
                  type="button"
                  class="btn btn-success"
                  @click="generateReport(false)"
                >
                  Generate
                </button>
                <button
                  type="button"
                  @click="generateReport(true)"
                  class="btn btn-danger ml-10"
                >
                  Generate & Export<i
                    class="fa fa-download ml-10"
                    aria-hidden="true"
                  ></i>
                </button>
                <button
                  type="button"
                  @click="reset()"
                  class="btn btn-success margin-left-5"
                >
                  Reset
                </button>
              </div>

              
              <div class="card-body">
                <div class="row">
                  <div class="col-12">
                    <b-table-simple
                      responsive
                      show-empty
                      bordered
                      sticky-header="800px"
                    >
                      <b-thead head-variant="light">
                        <b-tr>
                          <b-th class="text-center">Month</b-th>
                          <b-th class="text-center">Transaction Count</b-th>
                        </b-tr>
                      </b-thead>
                      <b-tbody v-for="(row, index) in report" :key="index">
                        <b-tr>
                          <b-td class="text-center text-gray">{{
                            row.month
                          }}</b-td>
                          <b-td class="text-center text-gray">{{
                            row.transaction_count
                          }}</b-td>
                        </b-tr>
                      </b-tbody>
                    </b-table-simple>
                  </div>
                </div>
              </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</div>
</template>
<script>
import api from "@/api/reports.js";
import apiuser from "@/api/user.js";
import moment from "moment";
import { saveAs } from "file-saver";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "./CustomLoader/CanPayLoader.vue"
export default {
  data() {
    return {
      report: [],
      loading: false,
      storeList: [],
      selectedStore: "",
      cpList: [],
      selectedCp: "",
      isLoadingCp: false,
      isLoadingSt: false
    };
  },
  components: {
    HourGlass,
    CanPayLoader
  },
  watch: {
  },
  created() {
  },
  mounted() {
  },
  methods: {
    //get the list of All CP
    getAllActiveCorporateParent(searchtxt) {
      var self = this;
      if(searchtxt.length >= 3){
        self.isLoadingCp = true;
        self.cpList = [];
        var request = {
          searchtxt: searchtxt,
        };
        api
          .getAllActiveCorporateParent(request)
          .then(function (response) {
            if (response.code == 200) {
              self.cpList = response.data;
              self.storeList = [];
              self.isLoadingCp = false;
            } else {
              error(response.message);
            }
          })
          .catch(function (error) {
            error(error);
          });
      }
    },
    //get the list of All Stores
    getAllActiveStores() {
      var self = this;
      self.isLoadingSt = true;
      self.storeList = [];
      var request = {
        user_id: self.selectedCp.id,
      };
      api
        .getAllActiveStores(request)
        .then(function (response) {
          if (response.code == 200) {
            self.storeList = response.data;
            self.isLoadingSt = false;
          } else {
            error(response.message);
          }
        })
        .catch(function (error) {
          error(error);
        });
    },
    // API call to generate the merchant location transaction report
    generateReport(reportExport) {
      var self = this;
      if (self.selectedCp == "") {
        error("Please select a Corpoarate Parent in order to generate the report.");
        return false;
      }
      if(self.selectedStore == 0){
        error("Please select a store in order to generate the report.");
        return false;
      }
      self.report = [];
      var request = {
        store_id: self.selectedStore,
      };
      self.loading = true;
      api
        .generateMonthlySalesGrowth(request)
        .then(function (response) {
          if (response.code == 200) {
            self.report = response.data;
            if(self.report.length > 0){
              if (reportExport) {
                self.exportReport();
              } else {
                self.loading = false;
              }
            }else {
              error("No records found!");
              self.loading = false;
            }
          } else {
            error(response.message);
            self.loading = false;
          }
        })
        .catch(function (error) {
          // error(error);
          self.loading = false;
        });
    },

    // exports the report
    exportReport() {
      var self = this;
      self.loading = true;
      var request = {
        report: self.report,
      };
      api
        .exportGenerateMonthlySalesGrowth(request)
        .then(function (response) {
          var FileSaver = require("file-saver");
          var blob = new Blob([response], {
            type: "application/xlsx",
          });
          FileSaver.saveAs(
            blob,
            moment().format("MM/DD/YYYY") + "_monthly_sales_growth.xlsx"
          );
          self.loading = false;
        })
        .catch(function (error) {
          // error(error);
          self.loading = false;
        });
    },
    reset(){
      var self = this;
      self.selectedCp = "";
      self.selectedStore = [];
    },
    refreshStores(selectedOption){
      var self = this;
      self.storeList = [];
      self.selectedStore = [];
      self.selectedCp = selectedOption;
      self.getAllActiveStores();
    }
  },
};
</script>