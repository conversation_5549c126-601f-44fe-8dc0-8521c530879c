const addReleaseNote = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/addreleasenote', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const searchReleaseNotes = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/getallreleasenote', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

export default {
    addReleaseNote,
    searchReleaseNotes
};