<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Events\AfterSheet;

class RewardSpinReportExport implements FromCollection, WithHeadings, ShouldAutoSize, WithEvents
{
    protected $request;

    public function __construct($request)
    {
        $this->request = $request; // Declaring the request variable
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        $collection_array = $this->request->get('report'); // Storing the array received from request

        $rewards = array();
        foreach ($collection_array[0] as $reward_data) {
            $nestedData['consumer_name'] = $reward_data->consumer_name;
            $nestedData['email'] = $reward_data->email;
            $nestedData['phone'] = $reward_data->phone;
            $nestedData['used_at'] = $reward_data->used_at != '' && $reward_data->used_at != 'N/A' ? date('m-d-Y H:i:s', strtotime($reward_data->used_at)) : '';
            $nestedData['winning_details'] = ucfirst($reward_data->winning_details);
            $nestedData['reward_point'] = $reward_data->reward_point;
            $nestedData['reward_amount'] = $reward_data->reward_amount;
            $nestedData['transaction_amount'] = $reward_data->transaction_amount;
            $nestedData['retailer'] = $reward_data->retailer;
            $nestedData['reward_wheel'] = $reward_data->reward_wheel;
            $nestedData['is_earned_spin'] = $reward_data->is_earned_spin;

            array_push($rewards, $nestedData);
        }
        return collect([
            $rewards,
        ]);
    }

    public function headings(): array
    {
        $returnArray = array(
            // 1st header
            [
                'Canpay Points Report ',
            ],
            [
                'Report Date:',
                date('m/d/Y', strtotime($this->request->get('from_date'))) . ' 07:00:00 - ' . date('m/d/Y', strtotime($this->request->get('to_date'))) . ' 06:59:59',
            ],
            [],
            [
                'Total Spin Earned',
                $this->request->get('report')['total_spins_earned'],
            ],
            [

                'Total Spin Spent',
                $this->request->get('report')['total_spins_spent'],
            ],
            [
                'Total Amount Earned via Points',
                $this->request->get('report')['total_amount_earned_via_points'],
            ],
            [

                'Total Amount Spent with Points',
                $this->request->get('report')['total_amount_spent_with_points'],
            ],
            [],
            [
                'Consumer',
                'Email',
                'Phone',
                'Spin Time',
                'Winning Details',
                'Reward Point',
                'Reward Amount ($)',
                'Transaction Amount ($)',
                'Store',
                'Reward Wheel',
                'Earned Spin',
            ],
        );

        return $returnArray;
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $event->sheet->getStyle('A1:K7')->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);

                $event->sheet->getStyle('A9:K9')->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);

                //Apply Center Alignment
                $event->sheet->getStyle('A:K')->getAlignment()->applyFromArray(
                    array('horizontal' => 'center')
                );
            },
        ];
    }
}
