<?php

namespace App\Http\Controllers;

use App\Models\BrandCampaign;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class BrandController extends Controller
{
    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    /**
     * getCampaignLists
     * Listing page for Campaigns for a particular Merchant
     * @param  mixed $request
     * @return void
     */
    public function getCampaignLists(Request $request)
    {
        // Columns defined for Sorting
        $columns = array(
            0 => 'campaign_id',
            1 => 'campaign_name',
            2 => 'use_as_generic_points',
            3 => 'status_id',
            4 => 'created_at',
        );

        // Main Query
        $query = BrandCampaign::on(MYSQL_RO)->join('status_master', 'brand_campaigns.status_id', '=', 'status_master.id')
            ->join('campaign_store_maps', 'campaign_store_maps.user)_id', '=', 'brand_campaigns.created_by')
            ->select('brand_campaigns.*', 'status_master.status');

        //Count Query
        $queryCount = BrandCampaign::on(MYSQL_RO)->join('status_master', 'brand_campaigns.status_id', '=', '.id')
            ->join('campaign_store_maps', 'campaign_store_maps.user)_id', '=', 'brand_campaigns.created_by');
        $totalData = $queryCount->first()->total_count; // Getting total no of rows

        $totalFiltered = $totalData;
        $limit = intval($request->input('length'));
        $start = intval($request->input('start'));
        $order = $columns[$request->input('order.0.column')];
        $dir = $request->input('order.0.dir');
        if (empty($request->input('search.value')) && empty($order) && empty($dir)) {
            $campaigns = $query->offset($start)->limit(intval($limit))->orderBy('brand_campaigns.created_at', 'DESC')->get();
        } else if (empty($request->input('search.value'))) {
            $campaigns = $query->offset($start)->limit(intval($limit))->orderBy($order, $dir)->get();
        } else {
            $search = $request->input('search.value');

            $search_query = $query->where(function ($q) use ($search) {
                $q->where('brand_campaigns.id', 'LIKE', "%{$search}%")
                    ->orWhere('brand_campaigns.campaign_id', 'LIKE', "%{$search}%")
                    ->orWhere('brand_campaigns.campaign_name', 'LIKE', "%{$search}%")
                    ->orWhere('status_master.status', 'LIKE', "%{$search}%");
            });

            $search_query_count = $queryCount->where(function ($q) use ($search) {
                $q->where('brand_campaigns.id', 'LIKE', "%{$search}%")
                    ->orWhere('brand_campaigns.campaign_id', 'LIKE', "%{$search}%")
                    ->orWhere('brand_campaigns.campaign_name', 'LIKE', "%{$search}%")
                    ->orWhere('status_master.status', 'LIKE', "%{$search}%");
            });

            $totalFiltered = $search_query_count->first()->total_count;

            $campaigns = $search_query->offset($start)->limit(intval($limit))->orderBy($order, $dir)->get();
        }

        $data = array();
        if (!empty($campaigns)) {
            // Creating array to show the values in frontend
            foreach ($campaigns as $val) {
                $nestedData['campaign_id'] = $val->campaign_id;
                $nestedData['campaign_name'] = $val->campaign_name;
                $nestedData['status'] = $val->status;
                $nestedData['created_at'] = date('m-d-Y h:i A', strtotime($val->created_at));
                $data[] = $nestedData;
            }
        }
        // Drawing the Datatable
        $json_data = array(
            "draw" => intval($request->input('draw')),
            "recordsTotal" => intval($totalData),
            "recordsFiltered" => intval($totalFiltered),
            "data" => $data,
        );

        Log::info(addslashes(__METHOD__) . "(" . LINE . __LINE__ . ") : Brand Campaigns List fetched successfully");
        echo json_encode($json_data); // Rerurning the data
    }
}
