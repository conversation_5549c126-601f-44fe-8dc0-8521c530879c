import { loadProgressBar } from 'axios-progress-bar'

const searchPetitions = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/getpetitions', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const getPetitionStatus = () => {
    return new Promise((res, rej) => {
        axios.get('api/getpetitionstatus')
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const importLogo = (formData) => {
    return new Promise((res, rej) => {
        axios.defaults.headers.common["Content-Type"] = 'multipart/form-data';
        axios.post('api/import/petitionlogo', formData)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const editPetition = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/editpetition', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const getPetitionSignedUsers = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/getpetitionsignedusers', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const linkPetitionToStore = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/linkpetitiontostore', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const sendEmailToPetitionPrimaryContact = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/sendpetitionemail', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
export default {
    searchPetitions,
    getPetitionStatus,
    importLogo,
    editPetition,
    getPetitionSignedUsers,
    linkPetitionToStore,
    sendEmailToPetitionPrimaryContact,
};
