<template>
<div>
  <div v-if="loading">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px">
    <!-- <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <b-button
                variant="outline-success"
                style="margin-top: -48px"
                @click="openModal('add')"
              >
                <i class="far fa-plus-square"></i> Add Category
              </b-button>
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Canpay Internal Version</h3>
                  <b-button
                  class="btn-danger export-api-btn"
                  @click="reloadDatatable"
                  v-if="showReloadBtn"
                  >
                    <i class="fas fa-redo"></i> Reload
                  </b-button>
                </div>
                <div class="card-body">
                  <table
                    id="adminUsersTable"
                    class="table"
                    style="width: 100%; white-space: normal"
                  >
                    <thead>
                      <tr>
                        <th>Category Name</th>
                        <th>Category Code</th>
                        <th>Nomenclature</th>
                        <th>Created At</th>
                        <th>Action</th>
                      </tr>
                    </thead>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div> -->

    <section class="content-header">
      <div class="container-fluid">
        <div class="row">
          <div class="col-12">
            <div class="card card-success">
              <div class="card-header">
                <h3 class="card-title">Canpay Internal Version</h3>
              </div>

              <!-- /.card-header -->
              <div class="card-body">
                <div class="row">
                <div class="col-md-4">
                  <div class="form-group">
                    <input
                      class="form-control"
                      placeholder="Category name (min 3 chars)"
                      id="category_name"
                      v-model="category_name"
                    />
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="form-group">
                    <input
                      class="form-control"
                      placeholder="CanPay Internal Version (Exact)"
                      id="category_code"
                      v-model="category_code"
                    />
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="form-group">
                    <input
                      class="form-control"
                      placeholder="Nomenclature (Exact)"
                      id="nomenclature"
                      v-model="nomenclature"
                    />
                  </div>
                </div>
              </div>
              </div>
                <div class="card-footer">
                  <button
                    type="button"
                    class="btn btn-success"
                    @click="searchCategory()"
                  >
                    Search
                  </button>
                  <button
                    type="button"
                    @click="reset()"
                    class="btn btn-success margin-left-5"
                  >
                    Reset
                  </button>
                  <b-button
                @click="openModal('add')"
                class="btn btn-success margin-left-5"
              >
                <i class="far fa-plus-square"></i> Add Category
              </b-button>
                </div>
                <div class="card-body">
                <b-table-simple
                    responsive
                    show-empty
                    bordered
                    sticky-header="800px"
                    v-if="allCategories.length > 0"
                  >
                    <b-thead head-variant="light">
                      <tr>
                        <th>Category Name</th>
                        <th>CanPay Internal Version <i v-b-tooltip.hover title="This code will be shared to the integrator. This can be digits, characters or combo of both. This should be min of 1 char and max of 10 chars." class="nav-icon fas fa-question-circle"></i></th>
                        <th>Nomenclature <i v-b-tooltip.hover title="This will be used to generate the APP Key for the merchants. This should be min of 1 char and max of 3 chars." class="nav-icon fas fa-question-circle"></i</i></th>
                        <th>Created At</th>
                        <th class="text-center">Action</th>
                      </tr>
                    </b-thead>
                    <b-tbody v-for="(row, index) in allCategories" :key="index">
                      <b-tr>
                        <b-td class="text-left text-gray">{{
                          row.category_name
                        }}</b-td>
                        <b-td class="text-left text-gray">{{
                          row.category_code
                        }}</b-td>
                        <b-td class="text-left text-gray">{{
                          row.nomenclature
                        }}</b-td>
                        <b-td class="text-left text-gray">{{
                          row.created_at
                        }}</b-td>
                        <b-td class="text-center text-gray">
                          <a @click="editCategory(row.id)" class="editCategory custom-edit-btn" title="Edit Integrator" variant="outline-success"  style="border:none"><i class="nav-icon fas fa-edit"></i></a>
                        </b-td>
                      </b-tr>
                    </b-tbody>
                  </b-table-simple>
                  <p v-else>No data displayed. Please refine your search criteria.</p>
                  </div>
            </div>
          </div>
        </div>
      </div>
    </section>


    <!-- CP Modal Start -->
    <b-modal
      id="category-modal"
      ref="modal"
      :header-text-variant="headerTextVariant"
      :title="modalTitle"
      @show="resetModal"
      @hidden="resetModal"
      ok-title="Save"
      ok-variant="success"
      cancel-variant="outline-secondary"
      @ok="handleOk"
      :no-close-on-esc="true"
      :no-close-on-backdrop="true"
    >
      <form ref="form" @submit.stop.prevent="save" class="needs-validation">
        <div class="row">
          <div class="col-md-9">
            <label for="category_name">
              Category Name
              <span class="red">*</span>
            </label>
            <input
            id="category_name"
            name="category_name"
            v-validate="'required|alpha|max:255'"
            type="text"
            v-model="categoryModel.category_name"
            class="form-control"
            />
                <span v-show="errors.has('category_name')" class="text-danger">{{errors.first("category_name")}}</span>
            </div>
        </div>

        <div class="row">
          <div class="col-md-9">
            <label for="category_code">
              CanPay Internal Version
              <span class="red">*</span>
            </label>
            <input
            id="category_code"
            name="category_code"
            v-validate="'required|alpha_num|max:10'"
            type="text"
            v-model="categoryModel.category_code"
            class="form-control"
            />
            <small class="text-green">This code will be shared to the integrator. This can be digits, characters or combo of both. This should be min of 1 char and max of 10 chars.</small>
            <span v-show="errors.has('category_code')" class="text-danger">{{errors.first("category_code")}}</span>
            </div>
        </div>

        <div class="row">
          <div class="col-md-9">
            <label for="nomenclature">
              Nomenclature
              <span class="red">*</span>
            </label>
            <input
            id="nomenclature"
            name="nomenclature"
            v-validate="'required|alpha|min:1|max:3'"
            type="text"
            v-model="categoryModel.nomenclature"
            class="form-control"
            />
            <small class="text-green">This will be used to generate the APP Key for the merchants. This should be min of 1 char and max of 3 chars.</small><br>
                <span v-show="errors.has('nomenclature')" class="text-danger">{{errors.first("nomenclature")}}</span>
            </div>
        </div>
      </form>
    </b-modal>
    <!-- CP Modal End -->
  </div>
</div>
</template>
<script>
import api from "@/api/intregator.js";
import { validationMixin } from "vuelidate";
import { required, minLength } from "vuelidate/lib/validators";
import commonConstants from "@/common/constant.js";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "../CustomLoader/CanPayLoader.vue"
export default {
  mixins: [validationMixin],
  data() {
    return {
      modalTitle: "",
      statusList: [],
      headerTextVariant: "light",
      id: null,
      isDisabled: false,
      locationTypeArray: [{location_type:"Web"},
      {location_type:"Retail" } ],
      currentUser: localStorage.getItem("user")
        ? JSON.parse(localStorage.getItem("user"))
        : null,
      showReloadBtn:false,
      constants: commonConstants,

      allCategories: [],
      categoryModel: {},
      loading: false,
      category_name: "",
      category_code: "",
      nomenclature: "",
    };
  },
  components: {
    HourGlass,
    CanPayLoader
  },
  methods: {
    editCategory(id) {
      var self = this;
      self.id = id;
      var categoryDetails = self.allCategories.find((p) => p.id == self.id);
      self.modalTitle = "Edit Category";
      self.$bvModal.show("category-modal");
      self.categoryModel = Object.assign({}, categoryDetails);
      self.categoryModel.id = id;
      self.isDisabled = true;
    },
    openModal(type) {
      var self = this;
      self.modalTitle = "Add Category";
      self.$bvModal.show("category-modal");
    },
    resetModal() {
      var self = this;
      self.categoryModel = {};
      self.isDisabled = false;
      self.id = null;
    },
    reset(){
      var self = this;
      self.allCategories = [];
      self.category_name = "";
      self.category_code = "";
      self.nomenclature = "";
      self.id = null;
      self.loadDT();
    },
    handleOk(bvModalEvt) {
      var self = this;
      // Prevent modal from closing
      bvModalEvt.preventDefault();
      // Trigger submit handler
      self.save();
    },
    save() {
      var self = this;
      // Exit when the form isn't valid
      this.$validator.validateAll().then((result) => {
        if (result) {
          //call to api to save the details
          if (!self.categoryModel.id) {
            // Add section
            api
              .addCategory(self.categoryModel)
              .then((response) => {
                if (response.code == 200) {
                  success(response.message);
                  $("#adminUsersTable").DataTable().ajax.reload(null, false);
                  self.$bvModal.hide("category-modal");
                  self.resetModal();
                  self.loadDT();
                } else {
                  error(response.message);
                }
              })
              .catch((err) => {
                error(err.response.data.message);
              });
          } else {
            // Edit Section
            api
              .editCategory(self.categoryModel)
              .then((response) => {
                if (response.code == 200) {
                  success(response.message);
                  self.id = null;
                  $("#adminUsersTable").DataTable().ajax.reload(null, false);
                  self.$bvModal.hide("category-modal");
                  self.store_ids = null;
                  // self.searchCategory();
                  self.resetModal();
                  self.loadDT();
                } else {
                  error(response.message);
                }
              })
              .catch((err) => {
                error(err.response.data.message);
              });
          }
        }
      });
    },
    searchCategory(){
      var self = this;

      var validate = false;

      if((self.category_name).trim() != ''){
        if((self.category_name).trim().length < 3){
          validate = true
        }else{
          validate = false
        }
      }else if((self.category_code).trim() != ''){
          validate = false
      }else if((self.nomenclature).trim() != ''){
          validate = false
      }else{
          validate = true
      }

      if(validate){
        error("Please provide Category Name (Min 3 chars) OR Category ID OR Nomenclature");
        return false;
      }
      self.loadDT();
    },
    loadDT(){
      var self = this;
      var request = {
        category_name: self.category_name,
        category_code: self.category_code,
        nomenclature: self.nomenclature,
      };
      self.loading = true;
      api
      .searchEmcommerceCategory(request)
      .then(function (response) {
        if (response.code == 200) {
          self.allCategories = response.data;
          self.loading = false;
        } else {
          error(response.message);
          self.loading = false;
        }
      })
      .catch(function (error) {
        error(error);
        self.loading = false;
      });
    },
    // loadDT: function () {
    //   var self = this;
    //   $("#adminUsersTable").DataTable({
    //     pagingType: "simple_numbers",
    //     processing: true,
    //     serverSide: true,
    //     destroy: true,
    //     columnDefs: [
    //       { orderable: false, targets: [4] },
    //       { className: "dt-left", targets: [0, 1, 2, 3] },
    //       { className: "dt-center", targets: [4] },
    //     ],
    //     order: [[3, "desc"]],
    //     orderClasses: false,
    //     language: {
    //       processing:
    //         '<i class="fa fa-spinner fa-spin fa-3x fa-fw"></i><span class="sr-only">Loading...</span> ',
    //       emptyTable: "No Integrator Available.",
    //       search: "_INPUT_",
    //       searchPlaceholder: "Search records",
    //       oPaginate: {
    //         sNext: '<i class="fas fa-angle-double-right"></i>',
    //         sPrevious: '<i class="fas fa-angle-double-left"></i>',
    //       },
    //       sLengthMenu:
    //         "<label class='label_dropdown_dt'>Per page</label> _MENU_",
    //     },
    //     ajax: {
    //       headers: {
    //         Authorization: "Bearer " + localStorage.getItem("token"),
    //       },
    //       url: "/api/ecommerceCategoryList",
    //       type: "POST",
    //       data: { _token: "{{csrf_token()}}" },
    //       dataType: "json",
    //       dataSrc: function (result) {
    //         self.showReloadBtn = false;
    //         self.allCategoryModel = result.data;
    //         return self.allCategoryModel;
    //       },
    //       error: function(data){
    //         error(commonConstants.datatable_error);
    //         $('#adminUsersTable_processing').hide();
    //         self.showReloadBtn = true;
    //       }
    //     },
    //     columns: [
    //       { data: "category_name" },
    //       { data: "category_code" },
    //       { data: "nomenclature" },
    //       { data: "created_at" },
    //       {
    //         render: function (data, type, full, meta) {
    //             return (
    //               '<b-button data-user-id="' +
    //               full.edit +
    //               '" class="editIntegrator custom-edit-btn" title="Edit Integrator" variant="outline-success"><i class="nav-icon fas fa-edit"></i></b-button>'
    //             );
    //         },
    //       },
    //     ],
    //   });

    //   $("#adminUsersTable").on("page.dt", function () {
    //     $("html, body").animate({ scrollTop: 0 }, "slow");
    //     $("th:first-child").focus();
    //   });

    //   //Search in the table only after 3 characters are typed
    //   // Call datatables, and return the API to the variable for use in our code
    //   // Binds datatables to all elements with a class of datatable
    //   var dtable = $("#adminUsersTable").dataTable().api();

    //   // Grab the datatables input box and alter how it is bound to events
    //   $(".dataTables_filter input")
    //   .unbind() // Unbind previous default bindings
    //   .bind("input", function(e) { // Bind our desired behavior
    //       // If the length is 3 or more characters, or the user pressed ENTER, search
    //       if(this.value.length >= 3 || e.keyCode == 13) {
    //           // Call the API search function
    //           dtable.search(this.value).draw();
    //       }
    //       // Ensure we clear the search if they backspace far enough
    //       if(this.value == "") {
    //           dtable.search("").draw();
    //       }
    //       return;
    //   });
    // },

  },
  mounted() {
    var self = this;

    setTimeout(function () {
      self.loadDT();
    }, 1000);
    document.title = "CanPay - CanPay Category";
  },
};
</script>

