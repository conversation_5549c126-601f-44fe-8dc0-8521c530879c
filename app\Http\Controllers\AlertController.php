<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AlertController extends Controller
{
    /**
     * generateFraudReport
     * This function used to generate consumers transaction behaviour base on some parameters, which will help to determine if a consumer is a fraud or not
     * @param  mixed $request
     * @return void
     */
    public function generateFraudReport(Request $request)
    {
        // Validating input request
        $this->validate($request, [
            'report_type' => VALIDATION_REQUIRED,
        ]);

        if ($request->get('report_type') == 'single_day_250') {
            // Any consumer that spends a combined $250 or more in a single day
            $sql = "SELECT concat_ws(' ', u.first_name, u.middle_name, u.last_name) as 'consumer_name', u.phone as 'phone_no', u.email, IF(u.existing_user = 0, 'V2', 'V1') as user_type, u.user_id, td.local_transaction_time as date, SUM(td.amount+td.tip_amount) as 'total_amount', usm.code as action
            FROM transaction_details td FORCE INDEX(date_key)
            INNER JOIN users u ON td.consumer_id = u.user_id
            INNER JOIN status_master sm On td.status_id = sm.id
            INNER JOIN status_master usm On u.status = usm.id
            WHERE sm.code IN ('" . SUCCESS . "','" . PENDING . "','" . RETURNED . "') AND td.transaction_ref_no is null AND td.isCanpay = 0 AND is_v1 = 0 AND td.local_transaction_date >= DATE_ADD(CURDATE(), INTERVAL -3 DAY)
            GROUP BY td.consumer_id, td.local_transaction_date
            HAVING SUM(td.amount+td.tip_amount) >= 350
            ORDER BY td.local_transaction_time DESC;";
        } else if ($request->get('report_type') == 'single_day_300_with_more_than_3_transactions') {
            // Any consumer that shops in the prior 3 days (plus today) with a combined total between the 3+ transactions of $300 or more
            $sql = "SELECT concat_ws(' ', u.first_name, u.middle_name, u.last_name) as 'consumer_name', COUNT(*), u.phone as 'phone_no', u.email, IF(u.existing_user = 0, 'V2', 'V1') as user_type, u.user_id, td.local_transaction_time as date, SUM(td.amount+td.tip_amount) as 'total_amount', usm.code as action
            FROM transaction_details td FORCE INDEX(date_key)
            INNER JOIN users u ON td.consumer_id = u.user_id
            INNER JOIN status_master sm On td.status_id = sm.id
            INNER JOIN status_master usm On u.status = usm.id
            WHERE sm.code IN ('" . SUCCESS . "','" . PENDING . "','" . RETURNED . "') AND td.transaction_ref_no is null AND td.isCanpay = 0 AND is_v1 = 0 AND td.local_transaction_date >= DATE_ADD(CURDATE(), INTERVAL -3 DAY)
            GROUP BY td.consumer_id, td.local_transaction_date
            HAVING SUM(td.amount+td.tip_amount) >= 300 AND COUNT(*) > 2
            ORDER BY td.local_transaction_time DESC;";
        }
        $transactions = DB::connection(MYSQL_RO)->select($sql);
        $message = trans('message.fraud_report_fetch_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Fraud Consumer Report Fetched Successfully.");
        return renderResponse(SUCCESS, $message, $transactions); // Returning response
    }

    /**
     * getConsumerTransactionHistory
     * This function will get a specific consumers all time transaction data along with all the detailed row wise data from the begining
     * @param  mixed $request
     * @return void
     */
    public function getConsumerTransactionHistory(Request $request)
    {
        // Validating input request
        $this->validate($request, [
            'consumer_id' => VALIDATION_REQUIRED,
        ]);

        // Combined or Grouped data for the consumer
        $header_sql = "select u.first_name, u.last_name, u.email, u.phone as phone_no, TIMESTAMPDIFF(YEAR, date_of_birth, CURDATE()) AS age, DATE_FORMAT(MIN(td.local_transaction_date),'%m-%d-%Y') as 'first_transaction_date', SUM(amount+tip_amount) as 'total_amount', COUNT(*) as transaction_count, SUM(if(sm.code = '" . RETURNED . "', 1, 0)) as return_count, u.state as customer_state, u.phone as customer_phone, u.street_address as customer_street, u.city as customer_city, IF(u.existing_user = 0, 'V2', 'V1') as user_type
        FROM transaction_details td FORCE INDEX(date_key)
        INNER JOIN users u ON td.consumer_id = u.user_id
        INNER JOIN status_master sm On td.status_id = sm.id
        WHERE td.consumer_id = ? AND sm.code IN ('" . SUCCESS . "','" . PENDING . "','" . RETURNED . "') AND td.transaction_ref_no is null AND td.isCanpay = 0 AND is_v1 = 0
        ORDER BY td.local_transaction_time ASC;";
        $header_data = DB::connection(MYSQL_RO)->select($header_sql, [$request->get('consumer_id')]);

        // Detailed row wise data for the consumer
        $body_sql = "SELECT td.amount, CONCAT(DATE_FORMAT(td.local_transaction_time,'%m-%d-%Y %h:%i:%s'), ' (', ttm.timezone_name, ')') as transaction_date, ms.store_id as store_id, ms.retailer as store_name, sm.status as transaction_status
        FROM transaction_details td FORCE INDEX(date_key)
        INNER JOIN terminal_master tm ON td.terminal_id = tm.id
        INNER JOIN merchant_stores ms ON tm.merchant_store_id = ms.id
        INNER JOIN timezone_masters ttm On ttm.id = ms.timezone_id
        INNER JOIN users u ON td.consumer_id = u.user_id
        INNER JOIN status_master sm On td.status_id = sm.id
        WHERE td.consumer_id = ? AND td.transaction_ref_no IS NULL AND td.isCanpay = 0 AND is_v1 = 0
        ORDER BY local_transaction_time DESC;";
        $body_data = DB::connection(MYSQL_RO)->select($body_sql, [$request->get('consumer_id')]);

        $returnData['header'] = $header_data;
        $returnData['body'] = $body_data;

        $message = trans('message.consumer_transaction_history_fetch_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction Details Fetched Successfully for Consumer ID:" . $request->get('consumer_id'));
        return renderResponse(SUCCESS, $message, $returnData); // Returning response
    }

    /**
     * makeUserInactive
     * THis function is used to make the user inactive due to suspected fraud
     * @param  mixed $request
     * @return void
     */
    public function makeUserInactive(Request $request)
    {
        // Validating input request
        $this->validate($request, [
            'consumer_id' => VALIDATION_REQUIRED,
        ]);

        $inactive_status = getStatus(INACTIVED_DUE_TO_SUSPECTED_FRAUD);
        $update = User::where('user_id', $request->get('consumer_id'))->update(['status' => $inactive_status]);
        if ($update) {
            $message = trans('message.consumer_inactive_success');
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Status changed to inactived due to suspected fraud Successfully for Consumer ID:" . $request->get('consumer_id'));
            return renderResponse(SUCCESS, $message, null); // Returning response
        } else {
            $message = trans('message.consumer_inactive_failed');
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Status changed to inactived due to suspected fraud Failed for Consumer ID:" . $request->get('consumer_id'));
            return renderResponse(FAIL, $message, null); // Returning response
        }
    }
}
