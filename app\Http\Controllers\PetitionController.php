<?php

namespace App\Http\Controllers;

use App\Models\MerchantStores;
use App\Models\Petition;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PetitionController extends Controller
{
    private $request;

    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    /**
     * getAllBank
     * Listing page for Admin Users along with Server Side Pagination in Datatable
     * @param  mixed $request
     * @return void
     */
    public function getpetitions(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Admin banks search started...");

        // Search with in Admin Banks
        $adminBanks = $this->_getpetitions($request);

        $message = trans('message.admin_banks_fetch_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Admin banks search started complete.");
        return renderResponse(SUCCESS, $message, $adminBanks);
    }

    /**
     * _getAdminBanksSearch
     * Fetch the Admin Banks
     * @param  mixed $searchArray
     * @return void
     */
    private function _getpetitions($request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Admin banks Search Started.");

        $page    = $request['page'];       // Get the current page from the request
        $perPage = $request['per_page'];   // Set the number of items per page
        $offset  = ($page - 1) * $perPage; // Calculate the offset
        $sql     = "SELECT
                    pt.*,
                    CASE WHEN sm.status = '" . ACTIVE . "' THEN 'Onboarded' ELSE sm.status END AS status,
                    ms.retailer,
                    u.first_name, u.middle_name, u.last_name, u.email, u.phone,
                    CONCAT(
                        IF(
                            ms.address IS NOT NULL
                            AND ms.address != '',
                            ms.address,
                            ''
                        ),
                        IF(
                            ms.city IS NOT NULL
                            AND ms.city != '',
                            CONCAT(', ', ms.city),
                            ''
                        ),
                        IF(
                            ms.state IS NOT NULL
                            AND ms.state != '',
                            CONCAT(', ', ms.state),
                            ''
                        ),
                        IF(
                            ms.zip IS NOT NULL
                            AND ms.zip != '',
                            CONCAT(' ', ms.zip),
                            ''
                        )
                    ) AS onboarded_address,
                    DATE_FORMAT(
                        pt.created_at, '%m-%d-%Y %h:%i %p'
                    ) AS created_at,
                    DATE_FORMAT(
                        pt.updated_at, '%m-%d-%Y %h:%i %p'
                    ) AS updated_at
                FROM
                    petitions AS pt
                    JOIN `status_master` AS sm ON `sm`.`id` = `pt`.`status_id`
                    LEFT JOIN `merchant_stores` AS ms ON `ms`.`petition_id` = `pt`.`id`
                    LEFT JOIN users u ON u.user_id = pt.consumer_id ";

        $searchStr  = [];
        $whereAdded = false;

        if (strlen(trim($request['store_name'])) >= 3) {
            $sql .= $whereAdded ? " AND" : " WHERE";
            $sql .= " pt.store_name LIKE ?";
            $whereAdded  = true;
            $searchStr[] = '%' . $request['store_name'] . '%';
        }

        if (! empty($request['status_id'])) {
            $sql .= $whereAdded ? " AND" : " WHERE";
            $sql .= " pt.status_id = ?";
            $searchStr[] = $request['status_id'];
        }

        $totalCount = count(DB::connection(MYSQL_RO)->select($sql, $searchStr));
        $sql .= " group by pt.id ORDER BY pt.created_at ASC LIMIT ? OFFSET ?";
        array_push($searchStr, $perPage, $offset);
        $petitions = DB::connection(MYSQL_RO)->Select($sql, $searchStr);
        $data      = [
            'data'         => $petitions,
            'current_page' => $page,
            'per_page'     => $perPage,
            'total'        => $totalCount,
            'total_pages'  => ceil($totalCount / $perPage),
        ];
        return $data;
    }

    public function getPetitionStatus()
    {
        //fetch all the petition status
        $status  = DB::select("SELECT id, code, CASE WHEN code = " . ACTIVE_CODE . " THEN 'Onboarded' ELSE REPLACE(STATUS, 'User ', '') END AS status FROM status_master WHERE code IN (" . PENDING . "," . AWAITING_ADMIN_APPROVAL . "," . PROVISIONED . "," . ACTIVE_CODE . "," . REJECTED . ")  order by status");
        $message = trans('message.petition_status_success');
        return renderResponse(SUCCESS, $message, $status); // API Response returned with 200 status
    }
    public function editPetition(Request $request)
    {
        $rule = [
            'id'              => VALIDATION_REQUIRED,
            'petition_number' => [
                VALIDATION_REQUIRED,
                'unique:petitions,petition_number,' . $request->get('id') . ',id',
            ],
        ];
        $this->__validate($request->all(), $rule);

        $petition_details = Petition::find($request->get('id'));
        if ($petition_details) {
            $petition_details->store_name                         = $request->get('store_name');
            $petition_details->logo_url                           = $request['logo_url'] == '' ? null : $request['logo_url'];
            $petition_details->store_short_name                   = $request->get('store_short_name') == '' ? null : $request->get('store_short_name');
            $petition_details->petition_number                    = $request->get('petition_number');
            $petition_details->primary_contact_person_firstname   = $request->get('primary_contact_person_firstname');
            $petition_details->primary_contact_person_lastname    = $request->get('primary_contact_person_lastname');
            $petition_details->primary_contact_person_email       = $request->get('primary_contact_person_email');
            $petition_details->primary_contact_person_title       = $request->get('primary_contact_person_title');
            $petition_details->secondary_contact_person_firstname = $request->get('secondary_contact_person_firstname');
            $petition_details->secondary_contact_person_lastname  = $request->get('secondary_contact_person_lastname');
            $petition_details->secondary_contact_person_email     = $request->get('secondary_contact_person_email');
            $petition_details->secondary_contact_person_title     = $request->get('secondary_contact_person_title');
            $petition_details->street_address                     = $request->get('street_address');
            $petition_details->city                               = $request->get('city');
            $petition_details->state                              = $request->get('state');
            $petition_details->zipcode                            = $request->get('zipcode');
            $petition_details->status_id                          = $request->get('status_id');
            $petition_details->rejection_reason                   = $request->get('rejection_reason');
            $petition_details->rejection_comments                 = $request->get('rejection_comments');

            $petition_details->save();

            if ($request->get('status_id') == getStatus(REJECTED)) {
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . 'Petition rejection flow triggered.', [
                    'petition_id' => $petition_details->id,
                    'status_id' => $request->get('status_id')
                ]);

                $user_details = User::find($petition_details->consumer_id);
                $emailExecutor = new \App\Http\Factories\EmailExecutor\EmailExecutorFactory();

                $rejection_reason = !empty($petition_details->rejection_comments)
                    ? $petition_details->rejection_comments
                    : $petition_details->rejection_reason;
                $params['email'] = $user_details->email;
                $params['petition_title'] = $petition_details->store_name;
                $params['first_name'] = $user_details->first_name;
                $params['rejection_reason'] = $rejection_reason;

                Log::info($params);

                $emailExecutor->petitionRejectionEmail($params);

                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . 'Petition rejection email triggered.', [
                    'email' => $params['email'],
                    'petition_number' => $params['petition_title']
                ]);
            }

            $message = trans('message.petition_updation_success');
            // API Response returned with 200 status
            return renderResponse(SUCCESS, $message, null);
        } else {
            $message = trans('message.petition_not_found');
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . $message);
            return renderResponse(FAIL, $message, null);
        }
    }

    public function getPetitionSignedUsers(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Fetching petition signed users started...");

        $rule = [
            'petition_id' => VALIDATION_REQUIRED,
        ];
        $this->__validate($request->all(), $rule);

        $petitionId = $request->get('petition_id');

        $allPetitionSignedUsers = DB::select("
            SELECT
                CONCAT_WS(' ', u.first_name, u.middle_name, u.last_name) AS name,
                u.state,
                petition_details.type,
                petition_details.consumer_id,
                DATE_FORMAT(petition_details.created_at, '%m-%d-%Y %h:%i %p') AS created_at
            FROM petition_details
            JOIN users AS u ON petition_details.consumer_id = u.user_id
            WHERE petition_details.petition_id = ?
            AND petition_details.deleted_at IS NULL
            ORDER BY petition_details.created_at
        ", [$petitionId]);

        $message = trans('message.petition_signed_users_fetch_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Fetching petition signed users completed.");

        return renderResponse(SUCCESS, $message, $allPetitionSignedUsers);
    }
    /**
     * Links a petition to a store.
     *
     * This function validates the request to ensure the presence of 'petition_id'
     * and 'store_id'. It checks if the petition and store are both unlinked
     * before mapping them. It logs each step of the process and returns a
     * response indicating success or failure of the linking operation.
     *
     * @param Request $request The HTTP request containing 'petition_id' and 'store_id'.
     * @return \Illuminate\Http\Response A response indicating the success or failure of the operation.
     */
    public function linkPetitionToStore(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Starting petition-to-store linking process.");

        $rule = [
            'petition_id' => VALIDATION_REQUIRED,
            'store_id'    => VALIDATION_REQUIRED,
        ];
        $this->__validate($request->all(), $rule);

        $petitionId = $request->get('petition_id');
        $storeId    = $request->get('store_id');

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Request validated. Petition ID: {$petitionId}, Store ID: {$storeId}");

        $petition = Petition::where('id', $petitionId)
            ->whereNull('merchant_store_id')
            ->first();

        if (! $petition) {
            $message = trans('message.petition_not_found');
            Log::warning(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Petition not found or already linked. Petition ID: {$petitionId}");
            return renderResponse(FAIL, $message, null);
        }

        $merchant = MerchantStores::where('id', $storeId)
            ->whereNull('petition_id')
            ->first();

        if (! $merchant) {
            $message = trans('message.store_not_found');
            Log::warning(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Store not found or already linked. Store ID: {$storeId}");
            return renderResponse(FAIL, $message, null);
        }

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Valid petition and store found. Proceeding with linking.");

        merchantStorePetitionMap($merchant->merchant_id, $petitionId, 'daily');

        $message = trans('message.petition_link_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Petition linked successfully. Petition ID: {$petitionId}, Store ID: {$storeId}");

        return renderResponse(SUCCESS, $message, null);
    }

    public function sendPetitionEmail(Request $request)
    {
        Log::info(__METHOD__ . ' - Petition email sending initiated.', ['request_data' => $request->all()]);

        $rule = [
            'petition_id' => VALIDATION_REQUIRED,
        ];
        $this->__validate($request->all(), $rule);

        $petition = Petition::find($request->get('petition_id'));

        if (! $petition || ! $petition->primary_contact_person_email) {
            Log::warning(__METHOD__ . ' - Petition or primary contact email not found.', [
                'petition' => $petition,
            ]);
            return renderResponse(FAIL, trans('message.petition_or_primary_contact_email_not_found'), null);
        }

        $emailExecutor = new \App\Http\Factories\EmailExecutor\EmailExecutorFactory();
        $emailsSent = [];
        $emailErrors = [];

        // Send email to primary contact
        $primaryParams = [
            'email'                    => $petition->primary_contact_person_email,
            'business_name'            => $petition->store_name,
            'store_contact_first_name' => $petition->primary_contact_person_firstname,
        ];

        Log::info(__METHOD__ . ' - Prepared primary contact email params.', $primaryParams);

        try {
            $emailExecutor->petitionOnboardEmail($primaryParams);
            $emailsSent[] = $petition->primary_contact_person_email;

            Log::info(__METHOD__ . ' - Petition onboarding email successfully sent to primary contact.', [
                'email'       => $primaryParams['email'],
                'petition_id' => $petition->id,
            ]);
        } catch (\Exception $e) {
            $emailErrors[] = [
                'email' => $petition->primary_contact_person_email,
                'error' => $e->getMessage(),
                'type'  => 'primary'
            ];

            Log::error(__METHOD__ . ' - Failed to send petition onboarding email to primary contact.', [
                'error_message' => $e->getMessage(),
                'stack_trace'   => $e->getTraceAsString(),
                'petition_id'   => $petition->id,
                'email'         => $primaryParams['email'],
            ]);
        }

        // Send email to secondary contact if email exists
        if (!empty($petition->secondary_contact_person_email)) {
            $secondaryParams = [
                'email'                    => $petition->secondary_contact_person_email,
                'business_name'            => $petition->store_name,
                'store_contact_first_name' => $petition->secondary_contact_person_firstname,
            ];

            Log::info(__METHOD__ . ' - Prepared secondary contact email params.', $secondaryParams);

            try {
                $emailExecutor->petitionOnboardEmail($secondaryParams);
                $emailsSent[] = $petition->secondary_contact_person_email;

                Log::info(__METHOD__ . ' - Petition onboarding email successfully sent to secondary contact.', [
                    'email'       => $secondaryParams['email'],
                    'petition_id' => $petition->id,
                ]);
            } catch (\Exception $e) {
                $emailErrors[] = [
                    'email' => $petition->secondary_contact_person_email,
                    'error' => $e->getMessage(),
                    'type'  => 'secondary'
                ];

                Log::error(__METHOD__ . ' - Failed to send petition onboarding email to secondary contact.', [
                    'error_message' => $e->getMessage(),
                    'stack_trace'   => $e->getTraceAsString(),
                    'petition_id'   => $petition->id,
                    'email'         => $secondaryParams['email'],
                ]);
            }
        } else {
            Log::info(__METHOD__ . ' - No secondary contact email found, skipping secondary email.', [
                'petition_id' => $petition->id,
            ]);
        }

        // Determine response based on results
        if (count($emailsSent) > 0 && count($emailErrors) === 0) {
            // All emails sent successfully
            $message = count($emailsSent) === 1
                ? trans('message.petition_email_sent_success')
                : trans('message.petition_email_sent_success_to_both');

            Log::info(__METHOD__ . ' - All petition onboarding emails sent successfully.', [
                'emails_sent' => $emailsSent,
                'petition_id' => $petition->id,
            ]);

            return renderResponse(SUCCESS, $message, ['emails_sent' => $emailsSent]);
        } elseif (count($emailsSent) > 0 && count($emailErrors) > 0) {
            // Some emails sent, some failed
            $message = 'Petition onboarding emails partially sent. Some emails failed to send.';

            Log::warning(__METHOD__ . ' - Petition onboarding emails partially sent.', [
                'emails_sent' => $emailsSent,
                'email_errors' => $emailErrors,
                'petition_id' => $petition->id,
            ]);

            return renderResponse(SUCCESS, $message, [
                'emails_sent' => $emailsSent,
                'email_errors' => $emailErrors
            ]);
        } else {
            // All emails failed
            $errorMessage = 'Failed to send petition onboarding emails to any contact.';

            Log::error(__METHOD__ . ' - Failed to send petition onboarding emails to any contact.', [
                'email_errors' => $emailErrors,
                'petition_id' => $petition->id,
            ]);

            return renderResponse(FAIL, $errorMessage, ['email_errors' => $emailErrors]);
        }
    }
}
