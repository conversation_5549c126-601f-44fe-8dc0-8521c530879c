<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
class BlacklistedRoutingNumbers extends Model
{
    protected $table = 'blacklisted_routing_numbers';

    /**
     * __construct
     * This function will insert an alphanumeric 32 character unique id as primary key in the table whenever a data get inserted
     * @param  mixed $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        $this->id = generateUUID();

        parent::__construct($attributes);
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'routing_numbers',
    ];
    public $incrementing = false;
}
