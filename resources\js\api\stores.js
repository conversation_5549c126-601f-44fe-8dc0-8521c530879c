import { loadProgressBar } from 'axios-progress-bar'

const toggleEcommerceAdminDriven = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/toggleEcommerceAdminDriven', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getEnableDisableReasons = () => {
    return new Promise((res, rej) => {
        axios.post('api/getenabledisablereasons')
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const disableStore = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/disablestore', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const enableStore = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/enablestore', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const addStoreDailyTransactionEmail = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/addstoredailytransactionemail', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
}
const storeHideFromMap = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/storeHideFromMap', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getStoreDetails = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/getstoredetails', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const getMerchentStoreTransactionType = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/getmerchentstoretransactiontype', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const updateMerchantStoreTransactionType = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/updatemerchantstoretransactiontype', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const searchStores = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/getallstores', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const searchEnableDisableStores = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/getstoresbytype', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const updateStoreLocation = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/updatestorelocation', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const toggleStoreAsSponsor = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/toggleStoreAsSponsor', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const updateStoreTiming = (request) => {
    var instance = axios.create({
        baseURL: process.env.MIX_API_URL,
    });
    instance.defaults.headers.common["Authorization"] = localStorage.getItem("token");
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.post('/admin/updatestoretimings', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getActiveRemotePayStore = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/getActiveRemotepayStoreList', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })  
}

export default {
    toggleEcommerceAdminDriven,
    getEnableDisableReasons,
    disableStore,
    enableStore,
    addStoreDailyTransactionEmail,
    storeHideFromMap,
    getStoreDetails,
    getMerchentStoreTransactionType,
    updateMerchantStoreTransactionType,
    searchStores,
    searchEnableDisableStores,
    updateStoreLocation,
    toggleStoreAsSponsor,
    updateStoreTiming,
    getActiveRemotePayStore
};
