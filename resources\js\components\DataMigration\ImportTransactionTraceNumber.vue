<template>
<div>
  <div v-if="is_visible == 1">
    <CanPayLoader/>
  </div>
<div class="content-wrapper" style="min-height: 36px">
  <section class="content-header">
    <div class="container-fluid">
      <div class="row mb-2">
        <div class="col-sm-6"></div>
      </div>
    </div>
  </section>
  <div class="hold-transition sidebar-mini">
  <section class="content">
  <div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div  class="card card-success">
        <div class="card-header">
            <h3 class="card-title">Import Transaction Trace No.</h3>
            <b-button
            class="btn-danger export-api-btn"
            @click="reloadTransactiontable"
            v-if="showReloadBtn"
            >
              <i class="fas fa-redo"></i> Reload
            </b-button>
        </div>
        <div class="card-body">
        <div
          class="alert alert-success alert-dismissible"
          v-if="success_message != null"
        >
          <a
            href="#"
            class="close"
            data-dismiss="alert"
            aria-label="close"
            style="text-decoration: none"
            @click="success_message = null"
            >&times;</a
          >
          <strong>Success!</strong> {{ success_message }}
        </div>
          <div class="form-group">
            <label for="exampleInputFile"
              >Upload File</label
            >
            <button
              type="button"
              class="btn btn-danger ml-10"
              style="float:right; margin-top:-12px;"
              @click="downloadSampleFile();"
            >
              Download Sample <i
                class="fa fa-download ml-10"
                aria-hidden="true"
              ></i>
            </button>
            <div class="input-group">
              <div class="custom-file">
                <input
                  type="file"
                  ref="canpay_transaction_trace_number_summary_file"
                  v-on:change="handleTheFileUpload('canpay_transaction_trace_number_summary')"
                  class="custom-file-input"
                  accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
                />
                <label
                  class="custom-file-label"
                  >{{ canpay_transaction_trace_number_summary_label }}</label
                >
              </div>
            </div>
            <span
              style="float: right"
              class="btn btn-success mt-3"
              @click="migrate()"
              >Import</span
            >
          </div>
        </div>
        <hr>
        <div class="card-body">
          <h5>Import History</h5>
          <table
            id="transactionMigrationTable"
            class="table"
            style="width: 100%; white-space: normal"
          >
            <thead>
              <tr>
                <th>Status</th>
                <th>Data Imported</th>
                <th>Data Skipped</th>
                <th>Filename</th>
                <th>Uploaded By</th>
                <th>Uploaded On</th>
              </tr>
            </thead>
          </table>
        </div>
      </div>
    </div>
  </div>
  </div>
  </section>
  </div>

</div>
</div>
</template>
<script>
import api from "@/api/transactionTraceNumber.js";
import commonConstants from "@/common/constant.js";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "../CustomLoader/CanPayLoader.vue"
export default {
  components: {
    HourGlass,
    CanPayLoader
  },
  data(){
    return {
      ach_base_url:process.env.MIX_ACH_APP_URL,
      canpay_transaction_trace_number_summary:null,
      canpay_transaction_trace_number_summary_label:'Choose File',
      showReloadBtn:false,
      constants: commonConstants,
      success_message: null,
      is_visible: 0,
    }
  },
  mounted() {
    var self = this;
    setTimeout(function () {
      self.loadDT();
    }, 1000);
    document.title = "CanPay - Import Transactions";
  },
  methods:{
    reloadTransactiontable(){
      var self = this;
      self.loadDT();
    },
    handleTheFileUpload(sheet_name) {
      let self = this;
      if(sheet_name === "canpay_transaction_trace_number_summary") {
        self.canpay_transaction_trace_number_summary = self.$refs.canpay_transaction_trace_number_summary_file.files[0];
        self.canpay_transaction_trace_number_summary_label = self.$refs.canpay_transaction_trace_number_summary_file.files[0].name;
      }
    },
    migrate(){
      var self = this;
      self.is_visible = 1;
      if (self.canpay_transaction_trace_number_summary == null) {
        self.is_visible = 0;
        error("Please upload the data sheet to migrate.");
        return false;
      }
      /*Initialize the form data*/
      let formData = new FormData();
      formData.append(
        "transactions",
        self.canpay_transaction_trace_number_summary
      );
      api
        .importTransactionTraceNumber(formData)
        .then((response)=>{
          if(response.code == 200){
            self.$refs.canpay_transaction_trace_number_summary_file.value = null;
            self.canpay_transaction_trace_number_summary = null;
            self.canpay_transaction_trace_number_summary_label = "Choose File";
            self.success_message = response.message;
            self.is_visible = 0;
            self.loadDT();
          }else{
            error(response.message);
            self.is_visible = 0;
          }
        })
        .catch((err)=>{
          error(err.response.data.message);
          self.is_visible = 0;
        })
    },
    loadDT: function () {
      var self = this;

      $("#transactionMigrationTable").DataTable({
        searching: false,
        pagingType: "simple_numbers",
        processing: true,
        serverSide: true,
        destroy: true,
        columnDefs: [
          { orderable: false, targets: [0] },
          { className: "dt-left", targets: [0, 1, 2, 3, 4,5] },
        ],
        order: [[5, "desc"]],
        orderClasses: false,
        bLengthChange: false,
        bPaginate: false,
        language: {
          processing:
            '<i class="fa fa-spinner fa-spin fa-3x fa-fw"></i><span class="sr-only">Loading...</span> ',
          emptyTable: "No Import Log Available.",
          search: "_INPUT_",
          searchPlaceholder: "Search records",
          oPaginate: {
            sNext: '<i class="fas fa-angle-double-right"></i>',
            sPrevious: '<i class="fas fa-angle-double-left"></i>',
          },
          sLengthMenu:
            "<label class='label_dropdown_dt'>Per page</label> _MENU_",
        },
        ajax: {
          headers: {
            Authorization: "Bearer " + localStorage.getItem("token"),
          },
          url: self.ach_base_url+"/api/admin/getmigrationlog",
          type: "POST",
          crossDomain: true, // Enable cross-origin requests
          data: { _token: "{{csrf_token()}}"},
          dataType: "json",
          dataSrc: function (result) {
            self.showReloadBtn = false;
            return result.data;
          },
          error: function(data){
            error(commonConstants.datatable_error);
            $('#transactionMigrationTable_processing').hide();
            self.showReloadBtn = true;
          }
        },
        columns: [
          { data: "summary" },
          { data: "data_imported" },
          { data: "data_skipped" },
          { data: "filename" },
          { data: "uploaded_by" },
          { data: "created_at" },
        ],
      });

      $("#transactionMigrationTable").on("page.dt", function () {
        $("html, body").animate({ scrollTop: 0 }, "slow");
        $("th:first-child").focus();
      });

      //Search in the table only after 3 characters are typed
      // Call datatables, and return the API to the variable for use in our code
      // Binds datatables to all elements with a class of datatable
      var dtable = $("#transactionMigrationTable").dataTable().api();

      // Grab the datatables input box and alter how it is bound to events
      $(".dataTables_filter input")
      .unbind() // Unbind previous default bindings
      .bind("input", function(e) { // Bind our desired behavior
          // If the length is 3 or more characters, or the user pressed ENTER, search
          if(this.value.length >= 3 || e.keyCode == 13) {
              // Call the API search function
              dtable.search(this.value).draw();
          }
          // Ensure we clear the search if they backspace far enough
          if(this.value == "") {
              dtable.search("").draw();
          }
          return;
      });
    },
    downloadSampleFile(){
      window.location.href = "sample_import_excel/Trance_Number_Report_Sample_Data.csv";
    }
  }
}
</script>
