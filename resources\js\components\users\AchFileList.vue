<template>
<div>
  <div v-if="loading">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">ACH Files</h3>
                </div>

                <div class="card-body">
                <div class="row">
                  <div class="col-md-4">
                    <div class="form-group">
                        <label for="exampleInputFile">Transaction Date</label>
                      <input
                        class="start-date form-control"
                        placeholder="Date"
                        id="start-date"
                        onkeydown="return false"
                        autocomplete="off"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div class="card-footer">
                <button
                  type="button"
                  class="btn btn-success"
                  id="generateBtn"
                  @click="generateReport()"
                >
                  Generate
                </button>
              </div>

              <div class="card-body">
                <div class="row">
                  <div class="col-12">
                    <b-table-simple
                      responsive
                      show-empty
                      bordered
                      sticky-header="800px"
                    >
                      <b-thead head-variant="light">
                        <b-tr>
                          <b-th class="text-center">File Name</b-th>
                          <b-th class="text-center">File Uploaded At (PT)</b-th>
                          <b-th class="text-center">Upload Folder</b-th>
                          <b-th class="text-center">Processed By Fed</b-th>
                          <b-th class="text-center">Upload</b-th>
                          <b-th class="text-center">Action</b-th>
                          <b-th class="text-center">Reverse File</b-th>
                        </b-tr>
                      </b-thead>
                      <b-tbody v-if="report.length > 0">
                        <b-tr v-for="(row, index) in report" :key="index">
                          <b-td class="text-center text-gray">{{
                            row.file_name
                          }}</b-td>
                          <b-td class="text-center text-gray" v-if="row.file_upload_time != null">{{
                            formatDate(row.file_upload_time)
                          }}</b-td>
                          <b-td class="text-center text-gray" v-else> Not Uploaded Yet</b-td>
                          <b-td class="text-center text-gray" v-if="row.folder_path != null">{{
                            extractName(row.folder_path)
                          }}</b-td>
                          <b-td class="text-center text-gray" v-else> N/A </b-td>
                          <b-td class="text-center text-gray" v-if="row.file_processing_checked == 1">Processed</b-td>
                          <b-td class="text-center text-gray" v-else> Yet to Process</b-td>
                          <b-td class="text-center text-gray" v-if="row.can_be_uploaded == 1">
                            <button
                            type="button"
                            @click="uploadAchFile(row)"
                            class="btn btn-success ml-10"
                            ><i
                                class="fa fa-upload ml-10"
                                aria-hidden="true"
                            ></i>
                            Upload
                            </button>
                          </b-td>
                          <b-td class="text-center text-gray" v-else>
                            N/A
                          </b-td>
                          <b-td class="text-center text-gray">
                            <i class="nav-icon fa fa-file-excel custom-edit-btn" title="Download Excel File" @click="downloadExcelFile(row.id);"></i>&nbsp;
                            <i class="nav-icon fa fa-download custom-edit-btn" title="Download ACH File" @click="downloadAchFile(row.id,0,row.file_name)"></i>
                            <i class="nav-icon fa fa-arrow-down custom-edit-btn" title="Download Delimiterized ACH File" @click="downloadDeliliterizedAchFile(row.id)"></i>
                            <i class="nav-icon fa fa-check custom-edit-btn" title="Validate Entry Hash" @click="validateEntryHash(row.id)"></i>
                          </b-td>
                          <b-td class="text-center text-gray">
                            <i class="nav-icon fa fa-undo custom-edit-btn" title="Reverse ACH File" @click="reverseAchFile(row.id);"></i>&nbsp;
                            <i class="nav-icon fa fa-download custom-edit-btn" title="Download Reversed ACH File" @click="downloadAchFile(row.id,1,row.file_name)" v-if="row.is_reversed == 1"></i>
                          </b-td>
                        </b-tr>
                      </b-tbody>
                      <b-tbody v-else>
                          <b-tr>
                            <b-td colspan="7" class="text-center text-gray">There are no records to show</b-td>
                          </b-tr>
                      </b-tbody>
                    </b-table-simple>
                  </div>
                </div>
              </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</div>
</template>
<script>
import api from "@/api/reports.js";
import moment from 'moment-timezone';
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "../CustomLoader/CanPayLoader.vue"
export default {
  data() {
    return {
      report: [],
      loading: false,
    };
  },
  components: {
    HourGlass,
    CanPayLoader
  },
  watch: {
  },
  created() {
  },
  mounted() {
    $("#start-date").datepicker({
        format: "mm/dd/yyyy",
        autoclose: true,
        todayHighlight: true
        }).datepicker("setDate", new Date(new Date().setDate(new Date().getDate() - 1)));
  },
  methods: {
    extractName(value) {
      // Ensure value is a string before using match
      if (typeof value === 'string') {
        // Using a regular expression to extract the desired part
        const match = value.match(/\/([^/]+)\/$/);
        return match ? match[1] : '';
      }
      return '';
    },
    // API call to generate the storewise monthly sales report
    generateReport() {
      var self = this;
      if (
        moment($("#start-date").val()).format("YYYY-MM-DD") >
        moment($().val()).format("YYYY-MM-DD")
      ) {
        error("Date cannot be from future.");
        return false;
      }

      self.report = [];
      var request = {
        transaction_date: moment($("#start-date").val()).format("YYYY-MM-DD"),
      };
      self.loading = true;
      api
        .getAchFileLists(request)
        .then(function (response) {
        if (response.code == 200 && response.data.length > 0) {
            self.report = response.data;
            success(response.message);
            self.loading = false;
        } else {
            self.loading = false;
        }
        })
        .catch(function (error) {
          self.loading = false;
        });
    },
    downloadAchFile(ach_file_id,reversed_file,saved_file_name) {
        var self = this;
        self.loading = true;
        var request = {
        ach_file_id: ach_file_id,
        reversed_file:reversed_file
      };
      api
        .downloadAchFile(request)
        .then(function (response) {
            var FileSaver = require("file-saver");
            var blob = new Blob([response], {
                type: "application/octet-stream",
            });
            if(reversed_file == 1){
                var file_name = saved_file_name;
            }else{
                var file_name = saved_file_name;
            }
            FileSaver.saveAs(
                blob,
                file_name
            );
            self.loading = false;
        })
        .catch(function (error) {
          self.loading = false;
        });
    },
    uploadAchFile(row) {
      var self = this;
      var title = "Are you sure you want to upload file to SFTP Server?";
      var icon = "";
      var text = "";
      var titleCustomClass = "";
      if (row.file_upload_decision_id != null && row.file_upload_decision_id != null) {
        title = "The ACH file "+row.file_name+" has already been uploaded to the SFTP server.";
        text = "Do you still wish to upload it again?";
        icon = "warning";
        titleCustomClass = "upload-ach-title-container";
      }
      Vue.swal({
        title: title,
        type: "warning",
        text: text,
        icon: icon,
        showCancelButton: true,
        confirmButtonColor: "#149240",
        confirmButtonText: "Yes, proceed!",
        cancelButtonText: "No, cancel it!",
        closeOnConfirm: true,
        closeOnCancel: true,
        width: '800px',
        customClass: {
          title: titleCustomClass
        }
        }).then((result) => {
            if (result.isConfirmed) {
                self.loading = true;
                var request = {
                    id: row.id,
                };
                api.uploadAchFile(request)
                .then(function (response) {
                if (response.code == 200) {
                    Vue.swal("Done!", response.message, "success");
                } else {
                    Vue.swal(response.message, '', 'error');
                }
                self.loading = false;
                })
                .catch(function (error) {
                    Vue.swal(error.response.data.message, '', 'error')
                    self.loading = false;
                });
            }
        });
    },
    downloadDeliliterizedAchFile(ach_file_id){
        var self = this;
        self.loading = true;
        var transaction_date = moment($("#start-date").val()).format("YYYY-MM-DD");
        var request = {
        ach_file_id: ach_file_id,
      };
      api
        .downloadDeliliterizedAchFile(request)
        .then(function (response) {
            var FileSaver = require("file-saver");
            var blob = new Blob([response], {
                type: "application/octet-stream",
            });
            FileSaver.saveAs(
                blob,
                transaction_date + "_delimitered_ach_file.ach"
            );
            self.loading = false;
        })
        .catch(function (error) {
          self.loading = false;
        });
    },
    downloadExcelFile(ach_file_id){
        var self = this;
        self.loading = true;
        var transaction_date = moment($("#start-date").val()).format("YYYY-MM-DD");
        var request = {
        ach_file_id: ach_file_id,
        transaction_date: transaction_date
      };
      api
        .generateAchTransactionsExcel(request)
        .then(function (response) {
        var FileSaver = require("file-saver");
            var blob = new Blob([response], {
                type: "application/xlsx",
            });
            FileSaver.saveAs(
                blob,
                transaction_date + "_ach_transaction_excel.xlsx"
            );
            self.loading = false;
        })
        .catch(function (error) {
          self.loading = false;
        });
    },
    formatDate(value) {
      return moment.tz(value, "UTC").tz("America/Los_Angeles").format("MM-DD-YYYY hh:mm A");
    },
    validateEntryHash(ach_file_id){
        var self = this;
        var request = {
            ach_file_id: ach_file_id,
        };
        Vue.swal({
        title: "Are you sure you want to validate Entry Hash?",
        type: "warning",
        showCancelButton: true,
        confirmButtonColor: "#149240",
        confirmButtonText: "Yes, proceed!",
        cancelButtonText: "No, cancel it!",
        closeOnConfirm: true,
        closeOnCancel: true,
        width: '800px'
        }).then((result) => {
            if (result.isConfirmed) {
                self.loading = true;
                api
                .validateEntryHash(request)
                .then(function (response) {
                    if ((response.code = 200)) {
                        Vue.swal("Done!", response.message, "success");
                    } else {
                        Vue.swal(response.message, '', 'error')
                    }
                    self.loading = false;
                })
                .catch(function (error) {
                    Vue.swal(error.response.data.message, '', 'error')
                    self.loading = false;
                });
            }else{
                self.loading = false;
            }
        });
    },
    reverseAchFile(ach_file_id){
        var self = this;
        self.loading = true;
        var request = {
            ach_file_id: ach_file_id,
        };
        Vue.swal({
        title: "Are you sure you want to reverse this ACH File?",
        type: "warning",
        showCancelButton: true,
        confirmButtonColor: "#149240",
        confirmButtonText: "Yes, proceed!",
        cancelButtonText: "No, cancel it!",
        closeOnConfirm: true,
        closeOnCancel: true,
        width: '800px'
        }).then((result) => {
            if (result.isConfirmed) {
                api
                .reverseAchFile(request)
                .then(function (response) {
                    if ((response.code = 200)) {
                        Vue.swal("Done!", response.message, "success");
                    } else {
                        Vue.swal(response.message, '', 'error')
                    }
                    self.loading = false;
                })
                .catch(function (error) {
                    Vue.swal(error.response.data.message, '', 'error')
                    self.loading = false;
                });
            }else{
                self.loading = false;
            }
        });
    }
  },
};
</script>
<style>
.upload-ach-title-container{
  color: #ff0000 !important; /* Replace with your desired text color */
}
</style>
