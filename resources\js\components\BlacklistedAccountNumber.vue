<template>
<div>
  <div  v-if="loading">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Blacklisted Account Number</h3>
                </div>
                <!-- /.card-header -->
                <div class="card-body">
                  <div class="row">
                  <div class="col-md-4">
                    <div class="form-group">
                     <input
                        class="form-control"
                        placeholder="Account Number (Min 3 chars)"
                        id="account_no"
                        v-model="account_no"
                      />
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                     <multiselect
                        v-model="selectedConsumerName"
                        placeholder="Select Consumer (Min 3 chars)"
                        id="consumer"
                        label="consumer_name"
                        :options="consumers"
                        :loading="isLoading"
                        :internal-search="false"
                        @search-change="getConsumerslist"
                        >
                        </multiselect>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                     <input id="consumer_phone_no" name="consumer_phone_no" v-validate="'required'" type="text" v-model="consumer_phone_no" class="form-control" placeholder="Phone (Exact)">
                    </div>
                  </div>
                </div>
                </div>
                <div class="card-footer">
                    <button
                      type="button"
                      class="btn btn-success"
                      @click="searchBlacklistedAccountNo()"
                    >
                    Search
                    </button>
                    <button
                    type="button"
                    @click="reset()"
                    class="btn btn-success margin-left-5"
                    >
                    Reset
                    </button>
                    <b-button
                        @click="openModal('add')"
                        class="btn btn-success margin-left-5"
                    >
                        <i class="fas fa-plus"></i> Add New
                    </b-button>
                    <div class="card mt-7" v-if="allAccountNumbers.length > 0">
                        <div class="card-body">
                            <ul>
                                <li><b>Yes</b> - Account number is blacklisted</li>
                                <li><b>No</b> - Account number is not blacklisted</li>
                                <li><b>Whitelisted</b> - Account number is whitelisted for this consumer but blacklisted for others</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                  <b-table-simple
                      responsive
                      show-empty
                      bordered
                      sticky-header="800px"
                      v-if="allAccountNumbers.length > 0"
                    >
                      <b-thead head-variant="light">
                        <tr>
                            <th>Consumer Name</th>
                            <th>Account Number</th>
                            <th>Bank Link Type</th>
                            <th>Status</th>
                            <th class="text-center">Account No. Blacklisted</th>
                            <th class="text-center">Action(s)</th>
                        </tr>
                      </b-thead>
                      <b-tbody v-for="(row, index) in allAccountNumbers" :key="index">
                        <b-tr>
                           <b-td class="text-left text-gray">{{
                            row.consumer_name
                          }}</b-td>
                          <b-td class="text-left text-gray">
                              <a @click="showConsumerList(row.bank_account_id)" class="account-link">{{row.acc_no}}</a>
                          </b-td>
                          <b-td class="text-left text-gray">{{
                            row.bank_link_type
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.status
                          }}</b-td>
                          <b-td class="text-center text-gray">{{
                            row.account_number_blacklisted
                          }}</b-td>
                          <b-td class="text-center text-gray">
                            <a @click="deleteBlacklistedAccountNo(row.account_no)" class="custom-edit-btn" title="Remove Account Number from Blacklist" variant="outline-success" style="border:none"><i class="nav-icon fas fa-trash" v-if="row.account_number_blacklisted == 'Yes'"></i></a>
                          </b-td>
                        </b-tr>
                      </b-tbody>
                    </b-table-simple>
                    <p v-else>No data displayed. Please refine your search criteria.</p>
                    </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
    <!-- Search Consumer Account Number Modal Start -->
    <b-modal
      id="add-blacklisted-account-number-modal"
      ref="modal"
      :header-text-variant="headerTextVariant"
      :title="modalTitle"
      @show="resetModal"
      @hidden="resetModal"
      ok-title="Search"
      ok-variant="success"
      cancel-variant="outline-secondary"
      @ok="handleOk"
      :no-close-on-esc="true"
      :no-close-on-backdrop="true"
    >
      <form ref="form" @submit.stop.prevent="save" class="needs-validation">
        <div class="row">
          <div class="col-md-12">
            <label for="account_number">Account Number </label>
            <input id="account_number" name="account_number" type="text" v-model="account_number" class="form-control" placeholder="Account Number (Exact)">
          </div>
        </div>
        <div class="row">
          <div class="col-md-12">
            <label for="phone_no">Consumer </label>
            <multiselect
              v-model="selectedConsumer"
              placeholder="Select Consumer (Min 3 chars)"
              id="consumer"
              label="consumer_name"
              :options="consumerList"
              :loading="isLoading"
              :internal-search="false"
              @search-change="getConsumers"
            >
            </multiselect>
          </div>
        </div>
        <div class="row">
          <div class="col-md-12">
            <label for="phone_no">Phone </label>
            <input id="phone_no" name="phone_no" v-validate="'required'" type="text" v-model="phone_no" class="form-control" placeholder="Phone (Exact)">
          </div>
        </div>
        <div class="row">
          <div class="col-md-12">
            <label for="email">Email </label>
            <input id="email" name="email" v-validate="'required'" type="text" v-model="email" class="form-control" placeholder="Email (Exact)">
          </div>
        </div>
      </form>
    </b-modal>
    <!-- Search Consumer Account Number Modal End -->

    <!-- Consumer Account Number List Modal Start -->
    <b-modal
      id="consumer-account-list-modal"
      ref="modal"
      :header-text-variant="headerTextVariant"
      :title="modalTitle"
      @show="resetModal"
      @hidden="resetModal"
      ok-title="Add Account Number to Blacklist"
      ok-variant="success"
      cancel-variant="outline-secondary"
      @ok="addAccountToBlacklist"
      :no-close-on-esc="true"
      :no-close-on-backdrop="true"
    >
    <b-table-simple
        responsive
        show-empty
        bordered
        sticky-header="800px"
        v-if="allConsumerAccountNumbers.length > 0"
    >
        <b-thead head-variant="light">
        <tr>
            <th>Consumer Name</th>
            <th>Account Number</th>
            <th>Bank Link Type</th>
            <th>Status</th>
            <th class="text-center">Action <input type="checkbox" @click='checkAll()' v-model='isCheckAll'></th>
        </tr>
        </b-thead>
        <b-tbody v-for="(row, index) in allConsumerAccountNumbers" :key="index">
        <b-tr>
            <b-td class="text-left text-gray">{{row.consumer_name}}</b-td>
            <b-td class="text-left text-gray">{{row.account_no}}</b-td>
            <b-td class="text-left text-gray">{{row.bank_link_type}}</b-td>
            <b-td class="text-left text-gray">{{row.status}}</b-td>
            <b-td class="text-center text-gray">
                <input type="checkbox" v-bind:value='row.edit' v-model='selrows' @change='updateCheckall()' />
            </b-td>
        </b-tr>
        </b-tbody>
    </b-table-simple>
    <p v-else>No data displayed. Please refine your search criteria.</p>
    </b-modal>
    <!-- Consumer Account Number List Modal End -->

    <!-- Consumer Account Nos. Modal Start -->
    <b-modal
      id="show-blacklisted-account-number-modal"
      ref="modal"
      :header-text-variant="headerTextVariant"
      :title="modalTitle"
      @show="resetModal"
      @hidden="resetModal"
      ok-variant="success"
      cancel-variant="outline-secondary"
      @ok="handleOk"
      :no-close-on-esc="true"
      :no-close-on-backdrop="true"
      hide-footer="true"
    >
    <b-table-simple
        responsive
        show-empty
        bordered
        sticky-header="800px"
        v-if="consumer_account_list.length > 0"
    >
        <b-thead head-variant="light">
        <tr>
            <th>Consumer Name</th>
            <th>Account Number</th>
            <th>Bank Link Type</th>
            <th>Status</th>
        </tr>
        </b-thead>
        <b-tbody v-for="(row, index) in consumer_account_list" :key="index">
        <b-tr>
            <b-td class="text-left text-gray">{{row.consumer_name}}</b-td>
            <b-td class="text-left text-gray">{{row.account_no}}</b-td>
            <b-td class="text-left text-gray">{{row.bank_link_type}}</b-td>
            <b-td class="text-left text-gray">{{row.status}}</b-td>
        </b-tr>
        </b-tbody>
    </b-table-simple>

    </b-modal>
    <!-- Consumer Account Nos. Modal End -->
  </div>
</div>
</template>
<script>
import api from "@/api/blacklistedaccount.js";
import apiTransaction from "@/api/transaction.js";
import commonConstants from "@/common/constant.js";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "./CustomLoader/CanPayLoader.vue"
export default {
  data() {
    return {
      showMsg: false,
      headerTextVariant: "light",
      allAccountNumbers: {},
      account_no: "",
      constants: commonConstants,
      id:null,
      modalTitle:"",
      isLoading: false,
      loading:false,
      selectedConsumer: null,
      consumerList: [],
      account_number:"",
      phone_no:"",
      email:"",
      allConsumerAccountNumbers:{},
      isCheckAll: false,
      selectedVal: "",
      selrows:[],
      selectedConsumerName: null,
      consumer_phone_no:"",
      consumers: [],
      consumer_account_list:[]
    };
  },
  components: {
    HourGlass,
    CanPayLoader
  },
  created() {
  },
  methods: {
    getConsumers(searchtxt) {
      var self = this;
      if(searchtxt.length >= 3){
        self.isLoading = true;
        var request = {
          searchtxt: searchtxt,
        };
        apiTransaction
          .getConsumers(request)
          .then(function (response) {
            if (response.code == 200) {
              self.consumerList = response.data;
              self.isLoading = false;
            } else {
              error(response.message);
            }
          })
          .catch(function (error) {
            error(error);
          });
      }
    },
    getConsumerslist(searchtxt) {
      var self = this;
      if(searchtxt.length >= 3){
        self.isLoading = true;
        var request = {
          searchtxt: searchtxt,
        };
        apiTransaction
          .getConsumers(request)
          .then(function (response) {
            if (response.code == 200) {
              self.consumers = response.data;
              self.isLoading = false;
            } else {
              error(response.message);
            }
          })
          .catch(function (error) {
            error(error);
          });
      }
    },
    searchBlacklistedAccountNo(){
      var self = this;
      if(self.selectedConsumerName === null){
        var consumer_name = '';
      }else{
        var consumer_name = self.selectedConsumerName.user_id;
      }
      if((self.account_no).trim().length < 3 && consumer_name === '' &&  $("#consumer_phone_no").val().trim() === ''){
        error("Please provide Customer Name or phone no(exact) or account number(exact)");
        return false;
      }
      var request = {
        consumer: consumer_name,
        phone_no: self.consumer_phone_no,
        account_no: self.account_no,
      };
      self.loading = true;
      api
      .searchBlacklistedAccountNo(request)
      .then(function (response) {
        if (response.code == 200) {
          self.allAccountNumbers = response.data;
          self.loading = false;
        } else {
          error(response.message);
          self.loading = false;
        }
      })
      .catch(function (error) {
        // error(error);
        self.loading = false;
      });
    },
    deleteBlacklistedAccountNo(blacklist_account_no){
      var self = this;
        var request = {
            account_no: blacklist_account_no,
        };
        var r = confirm(
            "Do you want to delete this account number from blacklist?"
        );
        if (r == true) {
            api
                .deleteBlacklistedAccountNo(request)
                .then((response) => {
                if (response.code == 200) {
                    success(response.message);
                    self.searchBlacklistedAccountNo();
                } else {
                    error(response.message);
                }
                })
                .catch((err) => {
                error(err.response.data.message);
                });
        }
    },
    openModal(type) {
        var self = this;
        self.modalTitle = "Add Blacklisted Account Number";
        self.$bvModal.show("add-blacklisted-account-number-modal");
    },
    resetModal() {
      var self = this;
      self.notificationModel = {};
    },
    handleOk(bvModalEvt) {
      var self = this;
      // Prevent modal from closing
      bvModalEvt.preventDefault();
      // Trigger submit handler
      self.save();
    },
    reset(){
      var self = this;
      self.account_no = "";
    },
    save(){
        var self = this;
        if(self.selectedConsumer === null){
            var consumer = '';
        }else{
            var consumer = self.selectedConsumer.user_id;
        }
        if(consumer === '' &&  $("#email").val().trim() === '' &&  $("#phone_no").val().trim() === '' &&  $("#account_number").val().trim() === ''){
            error("Please provide Customer Name or email(exact) or phone no(exact) or account number(exact)");
            return false;
        }
        var request = {
            account_no: self.account_number,
            consumer: consumer,
            email:self.email,
            phone_no:self.phone_no,
        };
        self.loading = true;
        api
        .searchConsumerAccountNo(request)
        .then(function (response) {
            if (response.code == 200) {
                self.allConsumerAccountNumbers = response.data;
                self.$bvModal.hide("add-blacklisted-account-number-modal");
                self.modalTitle = "Consumer Account Numbers List";
                self.$bvModal.show("consumer-account-list-modal");
                self.account_number = "";
                self.consumer = "";
                self.email = "";
                self.phone_no = "";
                self.loading = false;
            } else {
                error(response.message);
                self.loading = false;
            }
        })
        .catch(function (error) {
            // error(error);
            self.loading = false;
        });
    },
    checkAll(){
      var self = this;
      self.isCheckAll = !self.isCheckAll;
      self.selrows = [];
      if(self.isCheckAll){ // Check all
        for (var key in self.allConsumerAccountNumbers) {
          self.selrows.push(self.allConsumerAccountNumbers[key].edit);
        }
        self.isCheckAll = true;
      }
    },
    updateCheckall(){
      var self = this;
      if(self.allConsumerAccountNumbers.length == self.selrows.length){
         self.isCheckAll = true;
      }else{
         self.isCheckAll = false;
      }
    },
    addAccountToBlacklist(){
        var self = this;
        if(self.selrows.length  == 0){
            error("Please select atleast one account number to blacklist.");
            return false;
        }
        var request = {
            selrows: self.selrows,
        };
        self.loading = true;
        api
        .addAccountToBlacklist(request)
        .then(function (response) {
            if (response.code == 200) {
                success(response.message);
                self.$bvModal.hide("consumer-account-list-modal");
                self.loading = false;
                self.selrows = [];
                self.isCheckAll = false;
                self.searchBlacklistedAccountNo();
            } else {
                error(response.message);
                self.loading = false;
            }
        })
        .catch(function (error) {
            // error(error);
            self.loading = false;
        });
    },
    showConsumerList(id){
       var self = this;
       self.modalTitle = "Consumer Account Numbers List";
       self.$bvModal.show("show-blacklisted-account-number-modal");
       self.loading = true;
       var request = {
            id: id,
        };
        api
        .consumerAccountList(request)
        .then(function (response) {
            if (response.code == 200) {
                self.consumer_account_list = response.data;
                self.loading = false;
            } else {
                error(response.message);
                self.loading = false;
            }
        })
        .catch(function (error) {
            // error(error);
            self.loading = false;
        });
    }
  },
  mounted() {
    document.title = "CanPay - BlackListed Account Number";
  },
};
</script>

