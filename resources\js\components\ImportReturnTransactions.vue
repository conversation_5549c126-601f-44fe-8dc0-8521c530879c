<template>
<div>
  <div v-if="is_visible == 1">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Import Return Transactions</h3>
                  <b-button
                  class="btn-danger export-api-btn"
                  @click="reloadDatatable"
                  v-if="showReloadBtn"
                  >
                    <i class="fas fa-redo"></i> Reload
                  </b-button>
                </div>
                <!-- /.card-header -->
                <div class="card-body">

                  <div
                    class="alert alert-success alert-dismissible"
                    v-if="success_message != null"
                  >
                    <a
                      href="#"
                      class="close"
                      data-dismiss="alert"
                      aria-label="close"
                      style="text-decoration: none"
                      @click="success_message = null"
                      >&times;</a
                    >
                    <strong>Success!</strong> {{ success_message }}
                  </div>
                  <div class="form-group">
                    <label for="exampleInputFile"
                      >Upload Return Transactions</label
                    >
                    <div class="input-group">
                      <div class="custom-file">
                        <input
                          type="file"
                          ref="return_transactions_file"
                          id="exampleInputFile"
                          v-on:change="handleFileUpload('return_transactions')"
                          class="custom-file-input"
                          accept=".pdf, application/pdf"
                        />
                        <label
                          for="exampleInputFile"
                          class="custom-file-label"
                          >{{ return_transactions_label }}</label
                        >
                      </div>
                    </div>
                  </div>
                  <span
                    style="float: right"
                    class="btn btn-success"
                    @click="migrate"
                    >Import Return Transactions</span
                  >
                </div>
                <hr />
                <div class="card-body">
                  <h5>Import History</h5>
                  <table
                    id="ReturnTransactionsImportTable"
                    class="table"
                    style="width: 100%; white-space: normal"
                  >
                    <thead>
                      <tr>
                        <th>Status</th>
                        <th>Data Imported</th>
                        <th>Duplicate Data Found</th>
                        <th>Imported Data Updated</th>
                        <th>Uploaded By</th>
                        <th>Imported On</th>
                      </tr>
                    </thead>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</div>
</template>
<script>
import api from "@/api/import.js";
import { HourGlass } from "vue-loading-spinner";
import commonConstants from "@/common/constant.js";
import CanPayLoader from "./CustomLoader/CanPayLoader.vue";
export default {
  components: {
    HourGlass,
    CanPayLoader
  },
  data() {
    return {
      return_transactions: null,
      canpay_transaction_fees: null,
      return_transactions_label: "Choose File",
      success_message: null,
      is_visible: 0,
      showReloadBtn:false,
    };
  },
  methods: {
    reloadDatatable(){
      var self = this;
      self.loadDT();
    },
    handleFileUpload(sheet_name) {
      if (sheet_name === "return_transactions") {
        this.return_transactions = this.$refs.return_transactions_file.files[0];
        this.return_transactions_label = this.$refs.return_transactions_file.files[0].name;
      }
    },
    /*Submits the file to the server*/
    migrate() {
      var self = this;
      self.is_visible = 1;
      if (
        self.return_transactions == null
      ) {
        self.is_visible = 0;
        error("Please select a file to import.");
        return false;
      }
      /*Initialize the form data*/
      let formData = new FormData();
      formData.append(
        "return_transactions",
        self.return_transactions
      );
      /*call to the import excel api */
      api
        .importreturnTransactions(formData)
        .then((response) => {
          if (response.code == 200) {
            self.$refs.return_transactions_file.value = null;
            self.return_transactions = null;
            self.return_transactions_label = "Choose File";
            self.success_message = response.message;
            self.is_visible = 0;
            self.loadDT();
          } else {
            error(response.message);
            self.is_visible = 0;
          }
        })
        .catch((response) => {
          error(response);
          self.is_visible = 0;
        });
    },
    loadDT: function () {
      var self = this;
      $("#ReturnTransactionsImportTable").DataTable({
        searching: false,
        pagingType: "simple_numbers",
        processing: true,
        serverSide: true,
        destroy: true,
        columnDefs: [
          { orderable: false, targets: [0] },
          { className: "dt-left", targets: [0, 1, 2, 3, 4, 5] },
        ],
        order: [[5, "desc"]],
        orderClasses: false,
        bLengthChange: false,
        bPaginate: false,
        language: {
          processing:
            '<i class="fa fa-spinner fa-spin fa-3x fa-fw"></i><span class="sr-only">Loading...</span> ',
          emptyTable: "No Import Log Available.",
          search: "_INPUT_",
          searchPlaceholder: "Search records",
          oPaginate: {
            sNext: '<i class="fas fa-angle-double-right"></i>',
            sPrevious: '<i class="fas fa-angle-double-left"></i>',
          },
          sLengthMenu:
            "<label class='label_dropdown_dt'>Per page</label> _MENU_",
        },
        ajax: {
          headers: {
            Authorization: "Bearer " + localStorage.getItem("token"),
          },
          url: "/api/getmigrationlog",
          type: "POST",
          data: { _token: "{{csrf_token()}}", type: "ReturnTransactions" },
          dataType: "json",
          dataSrc: function (result) {
            self.showReloadBtn = false;
            return result.data;
          },
          error: function(data){
            error(commonConstants.datatable_error);
            $('#ReturnTransactionsImportTable_processing').hide();
            self.showReloadBtn = true;
          }
        },
        columns: [
          { data: "summary" },
          { data: "actual_data_imported" },
          { data: "duplicate_date_imported" },
          { data: "migrated_data_updated" },
          { data: "uploaded_by" },
          { data: "created_at" },
        ],
      });

      $("#ReturnTransactionsImportTable").on("page.dt", function () {
        $("html, body").animate({ scrollTop: 0 }, "slow");
        $("th:first-child").focus();
      });

      //Search in the table only after 3 characters are typed
      // Call datatables, and return the API to the variable for use in our code
      // Binds datatables to all elements with a class of datatable
      var dtable = $("#ReturnTransactionsImportTable").dataTable().api();

      // Grab the datatables input box and alter how it is bound to events
      $(".dataTables_filter input")
      .unbind() // Unbind previous default bindings
      .bind("input", function(e) { // Bind our desired behavior
          // If the length is 3 or more characters, or the user pressed ENTER, search
          if(this.value.length >= 3 || e.keyCode == 13) {
              // Call the API search function
              dtable.search(this.value).draw();
          }
          // Ensure we clear the search if they backspace far enough
          if(this.value == "") {
              dtable.search("").draw();
          }
          return;
      });
    },
  },
  mounted() {
    var self = this;
    setTimeout(function () {
      self.loadDT();
    }, 1000);
    document.title = "CanPay - Import Return Transactions";
  },
};
</script>
