<template>
  <div class="content-wrapper" style="min-height: 36px;">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <section class="content">
      <div class="container-fluid">
        <div class="row">
          <div class="col-md-12">
            <div class="card card-success">
              <div class="card-header">
                <h3 class="card-title">Update User Profile</h3>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-md-4">
                    <div class="form-group">
                      <label for="first_name">
                        First Name
                        <span class="red">*</span>
                      </label>
                      <input
                        type="text"
                        v-model="userDetails.first_name"
                        placeholder="Enter First Name"
                        class="form-control"
                        v-validate="'required'"
                        id="first_name"
                        name="first_name"
                      />
                      <span
                        v-show="errors.has('first_name')"
                        class="text-danger"
                      >{{ errors.first('first_name') }}</span>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <label for="middle_name">Middle Name</label>
                      <input
                        type="text"
                        v-model="userDetails.middle_name"
                        placeholder="Enter Middle Name"
                        class="form-control"
                        id="middle_name"
                        name="middle_name"
                      />
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <label for="last_name">
                        Last Name
                        <span class="red">*</span>
                      </label>
                      <input
                        type="text"
                        v-model="userDetails.last_name"
                        placeholder="Enter Last Name"
                        class="form-control"
                        v-validate="'required'"
                        id="last_name"
                        name="last_name"
                      />
                      <span
                        v-show="errors.has('last_name')"
                        class="text-danger"
                      >{{ errors.first('last_name') }}</span>
                    </div>
                  </div>
                </div>
                <div class="row">
                  <div class="col-md-6">
                    <div class="form-group">
                      <label for="email">
                        Email address
                        <span class="red">*</span>
                      </label>
                      <input
                        readonly
                        type="email"
                        v-model="userDetails.email"
                        placeholder="Enter email"
                        class="form-control"
                        autocomplete="off"
                        v-validate="'required'"
                        id="email"
                        name="email"
                      />
                      <span
                        v-show="errors.has('email')"
                        class="text-danger"
                      >{{ errors.first('email') }}</span>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-group">
                      <label for="phone">
                        Phone Number
                        <span class="red">*</span>
                      </label>
                      <input
                        type="text"
                        placeholder="Phone Number"
                        v-model="userDetails.phone"
                        class="form-control"
                        v-validate="'required'"
                        id="phone"
                        name="phone"
                      />
                      <span
                        v-show="errors.has('phone')"
                        class="text-danger"
                      >{{ errors.first('phone') }}</span>
                    </div>
                  </div>
                </div>

                <div class="row">
                  <div class="col-md-3">
                    <div class="form-group">
                      <label for="exampleInputEmail1">Street Address</label>
                      <input
                        type="text"
                        v-model="userDetails.street_address"
                        placeholder="Enter Street Address"
                        class="form-control"
                      />
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="form-group">
                      <label for="exampleInputEmail1">City</label>
                      <input
                        type="text"
                        v-model="userDetails.city"
                        placeholder="Enter City"
                        class="form-control"
                      />
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="form-group">
                      <label for="exampleInputEmail1">State</label>
                      <input
                        type="text"
                        v-model="userDetails.state"
                        placeholder="Enter State"
                        class="form-control"
                      />
                    </div>
                  </div>
                  <div class="col-md-3">
                    <div class="form-group">
                      <label for="exampleInputEmail1">Zip Code</label>
                      <input
                        type="text"
                        v-model="userDetails.zipcode"
                        placeholder="Enter Zip Code"
                        class="form-control"
                      />
                    </div>
                  </div>
                </div>
                <div class="row">
                  <div class="col-md-12">
                    <router-link to="/changePassword" class="cp-green">Change Password</router-link>
                  </div>
                </div>
              </div>
              <div class="card-footer">
                <button type="button" @click="updateProfile" class="btn btn-success">Save</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>
<script>
import api from "@/api/user.js";
import { validationMixin } from "vuelidate";
import { required, minLength } from "vuelidate/lib/validators";
export default {
  mixins: [validationMixin],
  data() {
    return {
      userDetails: JSON.parse(localStorage.getItem("user"))
    };
  },
  methods: {
    updateProfile() {
      let self = this;
      this.$validator.validateAll().then(result => {
        if (result) {
          //update profile
          api
            .updateUser(self.userDetails)
            .then(response => {
              if (response.code == 200) {
                localStorage.setItem("user", JSON.stringify(response.data));
                success(response.message);
                self.$router.go();
              } else {
                error(response.message);
              }
            })
            .catch(err => {
              error(err);
            });
        }
      });
    }
  },
  mounted() {
    var self = this;
    document.title = "CanPay - Edit Profile";
  }
};
</script>
