<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
class OtpAccessToken extends Model
{
    /**
     * __construct
     * This function will insert an alphanumeric 32 character unique id as primary key in the table whenever a data get inserted
     * @param  mixed $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        $this->id = generateUUID();

        parent::__construct($attributes);
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'phone_no',
        'email',
        'value',
        'expiration_datetime',
        'verified',
        'type',
        'status_id',
        'session_id',
    ];
    public $table = 'otp_access_token';
    public $incrementing = false;
}
