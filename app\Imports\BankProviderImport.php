<?php

namespace App\Imports;

use App\Models\FinancialInstitutionMaster;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class BankProviderImport implements ToModel, WithHeadingRow, WithBatchInserts, WithChunkReading
{
    use Importable;
    private $rows = 0;
    private $duplicate_rows = 0;
    private $updated_rows = 0;

    public function __construct()
    {
    }

    /**
     * @param array $row
     * This function actully imports the data as row from Excel Sheet. Here we used the WithHeadingRow to get the Data with Heading. Do Not try to get the rows with index.
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row)
    {
        Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Update akoya provider ID started... ");
        $provider = FinancialInstitutionMaster::where('bank_name', 'like', $row['provider_name'] . '%')->get();
        ++$this->rows;
        if (!empty($provider)) {
            $is_akoya = $row['subscription_status'] == 'ACTIVE' ?  1 : 0;
            $akoya_provider_id = $row['provider_id'];
            FinancialInstitutionMaster::where('bank_name', 'like', $row['provider_name'] . '%')->update(['is_akoya' => $is_akoya, 'akoya_provider_id' => $akoya_provider_id]);
            ++$this->updated_rows;
        } else {
            Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Provider Not found in our DB with provider Name: " . $row['provider_name']);
        }
    }

    public function getRowCount()
    {
        return $this->rows . '|' . $this->duplicate_rows . '|' . $this->updated_rows;
    }

    public function batchSize(): int
    {
        return 1000;
    }

    public function chunkSize(): int
    {
        return 5000;
    }
}
