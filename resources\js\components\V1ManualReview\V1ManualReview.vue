<template>
<div>
  <div v-if="loading">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">V1 Review Identity</h3>
                </div>
                <div class="card-body">
                  <div class="row">
                      <div class="col-md-4">
                    <div class="form-group">
                     <input
                        class="form-control"
                        placeholder="Consumer Name (min 3 chars)"
                        id="consumer"
                        v-model="consumer"
                      />
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="form-control"
                        placeholder="Phone No (Exact)"
                        id="phone_no"
                        v-model="phone_no"
                      />
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="form-control"
                        placeholder="Email (Exact)"
                        id="email"
                        v-model="email"
                      />
                    </div>
                  </div>
                    <div class="col-md-4">
                        <div class="form-group">
                        <input
                            class="start-date form-control"
                            placeholder="Start Date"
                            id="start-date"
                            autocomplete="off"
                        />
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                        <input
                            class="end-date form-control"
                            placeholder="End Date"
                            id="end-date"
                            autocomplete="off"
                        />
                        </div>
                    </div>
                  </div>
                  <p class="red" v-show="text_show">Search will not work if the difference between start and end days is greater than 30 days</p>
                </div>
                  <div class="card-footer">
                    <button
                      type="button"
                      class="btn btn-success"
                      id="searchBtn"
                      @click="searchConsumers()"
                    >
                      Search
                    </button>
                    <button
                      type="button"
                      @click="reset()"
                      class="btn btn-success margin-left-5"
                    >
                      Reset
                    </button>
                  </div>
                <!-- /.card-header -->
                <div class="card-body">
                  <b-table-simple
                      responsive
                      show-empty
                      bordered
                      sticky-header="800px"
                      v-if="allDetails.length > 0"
                    >
                      <b-thead head-variant="light">
                        <tr>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Phone</th>
                        <th class="text-center">Created On</th>
                        <th class="text-center">Status</th>
                        <th class="text-center">Action(s)</th>
                      </tr>
                      </b-thead>
                      <b-tbody v-for="(row, index) in allDetails" :key="index">
                        <b-tr>
                          <b-td class="text-left text-gray">{{
                            row.name
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.email
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.phone
                          }}</b-td>
                          <b-td class="text-center text-gray">{{
                            row.created_at
                          }}</b-td>
                          <b-td class="text-center text-gray">{{
                            row.status
                          }}</b-td>
                          <b-td class="text-center text-gray">
                            <a :data-id="row.edit" :data-session="row.session_id" class="get-scores custom-edit-btn" title="Change Status" variant="outline-success"><i class="nav-icon fas fa-edit"></i></a></b-td>
                        </b-tr>
                      </b-tbody>
                    </b-table-simple>
                    <p v-else>No data displayed. Please refine your search criteria.</p>
                    </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
    <!-- Edit Modal Start -->
    <b-modal
      id="pending-edit-modal"
      ref="pending-edit-modal"
      ok-title="Save"
      ok-variant="success"
      @ok="updateStatus"
      title="More Identity Validation Details"
      :no-close-on-esc="false"
      :no-close-on-backdrop="false"
    >
      <div class="row">
        <div class="col-12">
          <span>
            <b>V1 Consumer Identity Document:</b>
          </span>
        </div>
      </div>
      <hr />
      <div class="row">
        <div class="col-6" style="text-align: center">
          <img
            slot="image"
            disabled
            v-bind:src="imagesrcfront"
            class="id-preview"
          />
        </div>
        <div class="col-6" style="text-align: center">
          <img
            slot="image"
            disabled
            v-bind:src="imagesrcback"
            class="id-preview"
          />
        </div>
      </div>
      <hr />
      <div class="row">
        <div class="col-12">
          <span>
            <b>Consumer Input:</b>
          </span>
        </div>
      </div>
      <hr />
      <div class="row">
        <div class="col-6">
          <span>
            <b>Name:</b>
            {{ input_name }}
          </span>
        </div>
        <div class="col-6">
          <span>
            <b>Mobile Number:</b>
            {{ input_phone }}
          </span>
        </div>
      </div>

      <br />
      <div class="row">
        <div class="col-6">
          <span>
            <b>Email:</b>
            {{ input_email }}
          </span>
        </div>
           <div class="col-6">
          <span>
            <b>Address:</b>
            {{ user_address}}
          </span>
        </div>
      </div>
      <div class="row" v-if="items.length > 0">
        <div class="col-12">
          <b-table striped hover :items="items" :fields="fields"></b-table>
        </div>
      </div>
      <hr />
      <div class="row">
        <div class="col-12">
          <span>
            <b>Change Review Status:</b>
          </span>
        </div>
      </div>
      <div class="row">
        <div class="col-12">
          <v-select
            id="change-status"
            label="status"
            v-model="review_status"
            :options="options"
          ></v-select>
        </div>
      </div>
    </b-modal>
    <!-- Edit Modal End -->

    <b-modal
      id="approved-edit-modal"
      ref="approved-edit-modal"
      hide-footer
      title="More Identity Validation Details"
      :no-close-on-esc="false"
      :no-close-on-backdrop="false"
    >
      <div class="row">
        <div class="col-12">
          <span>
            <b>V1 Consumer Identity Document:</b>
          </span>
        </div>
      </div>
      <hr />
      <div class="row">
        <div class="col-6" style="text-align: center">
          <img
            slot="image"
            disabled
            v-bind:src="imagesrcfront"
            class="id-preview"
          />
        </div>
        <div class="col-6" style="text-align: center">
          <img
            slot="image"
            disabled
            v-bind:src="imagesrcback"
            class="id-preview"
          />
        </div>
      </div>
      <hr />
      <div class="row">
        <div class="col-12">
          <span>
            <b>Consumer Input:</b>
          </span>
        </div>
      </div>
      <hr />
      <div class="row">
        <div class="col-6">
          <span>
            <b>Name:</b>
            {{ input_name }}
          </span>
        </div>
        <div class="col-6">
          <span>
            <b>Mobile Number:</b>
            {{ input_phone }}
          </span>
        </div>
      </div>

      <br />
       <div class="row">
        <div class="col-6">
          <span>
            <b>Email:</b>
            {{ input_email }}
          </span>
        </div>
           <div class="col-6">
          <span>
            <b>Address:</b>
            {{ user_address}}
          </span>
        </div>
      </div>
      <div class="row" v-if="items.length > 0">
        <div class="col-12">
          <b-table striped hover :items="items" :fields="fields"></b-table>
        </div>
      </div>
      <hr />
    </b-modal>
  </div>
</div>
</template>
<script>
import api from "@/api/review.js";
import moment from "moment";
import commonConstants from "@/common/constant.js";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "../CustomLoader/CanPayLoader.vue"
export default {
  data() {
    return {
      fields: ["source", "score"],
      items: [],
      review_flag: "",
      modalTitle: "",
      options: [
        { code: 706, status: "Activated By Admin" }, //code is based on db status code
      ],
      review_status: {}, //code is based on db status code
      record_id: "",
      allDetails: {},
      imagesrcfront: "",
      imagesrcback: "",
      source_name: "",
      source_phone: "",
      source_address: "",
      source_dob: "",
      source_ssn: "",
      input_name: "",
      input_phone: "",
      input_address: "",
      input_dob: "",
      input_ssn: "",
      name_score: "",
      address_score: "",
      phone_score: "",
      ssn_score: "",
      dob_score: "",
      average_score: "",
      reason: "",
      headerTextVariant: "light",
      input_email: "",
      currentUser: "",
      user_address: "",
      searchvalue:"",
      showReloadBtn:false,
      loading:false,
      text_show:false,
      consumer:"",
      phone_no:"",
      email:"",
    };
  },
  components: {
    HourGlass,
    CanPayLoader
  },
  created() {
    this.getDetails();
    this.email = this.$route.query.email;
  },
  methods: {
    searchConsumers(){
      var self = this;
      if (
        moment($("#start-date").val()).format("YYYY-MM-DD") >
        moment().format("YYYY-MM-DD") && $("#start-date").val()!= ''
      ) {
        error("Start date cannot be from future.");
        return false;
      }
      if (
        moment($("#end-date").val()).format("YYYY-MM-DD") >
        moment().format("YYYY-MM-DD") && $("#end-date").val()!= ''
      ) {
        error("End date cannot be from future.");
        return false;
      }
      if($("#start-date").val()!=''){
        var from_date = moment($("#start-date").val()).format("YYYY-MM-DD");
      }else{
        var from_date = '';
      }
      if($("#end-date").val()!=''){
        var to_date = moment($("#end-date").val()).format("YYYY-MM-DD");
      }else{
        var to_date = '';
      }
      if(from_date > to_date){
        error("End Date cannot be greater than Start Date");
        return false;
      }
      if((self.consumer).trim().length < 3 && (self.consumer).trim().length > 0){
        error("Please provide Consumer Name (Min 3 chars)");
        return false;
      }
      var request = {
        from_date: from_date,
        to_date: to_date,
        consumer: self.consumer,
        email:self.email,
        phone_no:self.phone_no,
        type:202
      };

      self.loading = true;
      api
      .getAllManualReviewDetailsForV1(request)
      .then(function (response) {
        if (response.code == 200) {
          self.allDetails = response.data;
          self.loading = false;
        } else {
          error(response.message);
          self.loading = false;
        }
      })
      .catch(function (error) {
        // error(error);
        self.loading = false;
      });
    },
    getDetails() {
      var self = this;
      $(document).on("click", ".get-scores", function (e) {
        self.record_id = $(e.currentTarget).attr("data-id");
        var request = {
          id: self.record_id,
        };
        api
          .getDetailsV1(request)
          .then((response) => {
            if ((response.code == 200)) {
              self.input_name =
                response.data.details.suffix +
                " " +
                response.data.details.first_name +
                " " +
                response.data.details.middle_name +
                " " +
                response.data.details.last_name;
              self.input_phone = response.data.details.phone;
              self.imagesrcfront = response.data.image_front;
              self.imagesrcback = response.data.image_back;
              self.input_email = response.data.details.email;
                 self.user_address = response.data.address;
              self.review_status = {
                code: response.data.details.status_code,
                status: response.data.details.status_name,
              };
              self.options = [
                { code: 706, status: "Activated By Admin" },
                { code: 707, status: "Suspended By Admin" },
              ];
              self.items = [];
              if (response.data.details.status_code == 706 ) {
                self.$refs["approved-edit-modal"].show();
              } else {
                self.$refs["pending-edit-modal"].show();
              }
            } else {
              error(response.message);
            }
          })
          .catch((err) => {
            console.log(err);
          });
      });
    },
    updateStatus() {
      var self = this;
      if(self.review_status.code == 707)
      {
         var r = confirm(
        "Do you want to update the user review status?"
      );
      }
      else{
       var r = confirm(
        "Do you want to update the review status? Please note once you update it you cannot change it back."
      );
      }

      if (r == true) {
        var request = {
          id: self.record_id,
          code: self.review_status.code,
          status_updated_by : self.currentUser.user_id,
        };
        api
          .updateReviewStatusForV1(request)
          .then((response) => {
            if ((response.code == 200)) {
              success(response.message);
              self.searchConsumers();
            } else {
              error(response.message);
            }
          })
          .catch((err) => {
            error(err);
          });
      } else {
        return false;
      }
    },
    dateDiff(){
      var self = this;
      if ($("#start-date").val() != "") {
        var from_date = moment($("#start-date").val()).format("YYYY-MM-DD");
      } else {
        var from_date = "";
      }
      if ($("#end-date").val() != "") {
        var to_date = moment($("#end-date").val()).format("YYYY-MM-DD");
      } else {
        var to_date = "";
      }
      if(from_date!='' && to_date!=''){
        //calculate the date Difference
        var date1 = new Date(from_date);
        var date2 = new Date(to_date);
        // To calculate the time difference of two dates
        var Difference_In_Time = date2.getTime() - date1.getTime();

        // To calculate the no. of days between two dates
        var Difference_In_Days = Difference_In_Time / (1000 * 3600 * 24);

        if(Difference_In_Days > 30){
          $("#searchBtn").prop('disabled', true);
          self.text_show = true;
        }else{
          $("#searchBtn").prop('disabled', false);
          self.text_show = false;
        }
      }
    },
    reset(){
      var self = this;
      self.consumer = "";
      self.phone_no = "";
      self.email = "";
    }
  },
  mounted() {
    var self = this;
    if(self.email){
      self.searchConsumers();
    }
    $("#start-date").datepicker({
      format: "mm/dd/yyyy",
      autoclose: true,
      todayHighlight: true,
    }).on('changeDate', function (ev) {
        self.dateDiff();
    });
    $("#end-date").datepicker({
      format: "mm/dd/yyyy",
      autoclose: true,
      todayHighlight: true,
    }).on('changeDate', function (ev) {
        self.dateDiff();
    });
    $("#start-date , #end-date").datepicker("setDate", new Date());
    document.title = "CanPay - V1 Pending Identity";

   self.currentUser= localStorage.getItem("user")? JSON.parse(localStorage.getItem("user")): null;
  },
};
</script>

