<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class AkoyaRefreshToken extends Model
{

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'id',
        'consumer_id',
        'refresh_token',
        'id_token',
        'token_expiration_time',
    ];
    public $timestamps = true;
    public $incrementing = false;
    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
}
