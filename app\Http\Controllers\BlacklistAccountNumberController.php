<?php

// PostController.php

namespace App\Http\Controllers;

use App\Models\BlacklistedAccountNumber;
use App\Models\ValidAccountNumber;
use App\Models\UserBankAccountInfo;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use Carbon\Carbon;

class BlacklistAccountNumberController extends Controller
{

    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    /**
     * searchBlacklistedAccountNumber
     * This function is used to get all the blacklisted account number
     * @param  mixed $request
     * @return void
     */
    public function searchBlacklistedAccountNumber(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Blacklisted account number search started...");
        // Validating input request
        $this->validate($request, [
            'account_no' => VALIDATION_REQUIRED_WITHOUT_ALL . ':phone_no,consumer',
            'consumer' => VALIDATION_REQUIRED_WITHOUT_ALL . ':phone_no,account_no',
            'phone_no' => VALIDATION_REQUIRED_WITHOUT_ALL . ':consumer,account_no',
        ]);

        // Search with in Blacklisted account numbers
        $blacklistedAccountNumbers = $this->_getBlacklistedAccountNumber($request);

        $message = trans('message.blacklisted_account_numbers_fetch_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Blacklisted account number search complete.");
        return renderResponse(SUCCESS, $message, $blacklistedAccountNumbers);
    }

    /**
     * _getBlacklistedAccountNumber
     * Fetch the Blacklisted account numbers
     * @param  mixed $request
     * @return void
     */
    private function _getBlacklistedAccountNumber($request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Blacklisted account number search Started.");

        $sql = "SELECT DISTINCT user_bank_account_info.account_no,user_bank_account_info.id as bank_account_id,user_bank_account_info.created_at, user_bank_account_info.account_id, user_bank_account_info.finicity_id, user_bank_account_info.banking_solution_id, users.first_name,users.middle_name,users.last_name,users.bank_link_type, blacklisted_account_numbers.account_no as blacklisted_account_no,

        CASE WHEN valid_account_numbers.id IS NOT NULL AND (status_master.code = " . BANK_ACTIVE . " OR status_master.code = " . BANK_DISABLE . " OR status_master.code = " . BANK_INACTIVE . ")  THEN 'Whitelisted'
        WHEN blacklisted_account_numbers.account_no IS NOT NULL THEN 'Yes'
        ELSE 'No' END as account_no_status,

        CASE WHEN user_bank_account_info.ref_no IS NULL THEN status_master.status
        WHEN ref_sm.id IS NOT NULL AND ref_sm.code = " . BANK_ACTIVE . "  THEN 'Hybrid Account (Active)'
        ELSE 'Hybrid Account' END as status

        FROM users JOIN user_bank_account_info ON users.user_id = user_bank_account_info.user_id
        JOIN status_master ON status_master.id = user_bank_account_info.status

        LEFT JOIN user_bank_account_info as ref_ubai ON ref_ubai.id = user_bank_account_info.ref_no
        LEFT JOIN status_master as ref_sm ON ref_sm.id = ref_ubai.status

        LEFT JOIN blacklisted_account_numbers ON blacklisted_account_numbers.account_no = user_bank_account_info.account_no AND blacklisted_account_numbers.is_delete = 0
        LEFT JOIN valid_account_numbers ON valid_account_numbers.account_no = user_bank_account_info.account_no AND valid_account_numbers.is_delete = 0 AND valid_account_numbers.consumer_id = user_bank_account_info.user_id WHERE 1 ";

        $searchStr = [];
        if (trim($request['consumer'])!='') {
            $sql .= " AND users.user_id = ? ";
            array_push($searchStr,$request['consumer']);
        }
        if (trim($request['phone_no'])!='') {
            $sql .= " AND users.phone = ? ";
            array_push($searchStr,$request['phone_no']);
        }
        if (strlen(trim($request['account_no'])) >= 3) {
            $sql .= " AND user_bank_account_info.account_no LIKE ? ";
            array_push($searchStr,'%' . $request['account_no'] . '%');
        }
        $sql .= "  ORDER BY users.created_at DESC LIMIT 100 ";
        $blacklistedAccountNumbers = DB::connection(MYSQL_RO)->Select($sql,$searchStr);

        $blacklistedAccountNumbersArr = [];
        if (!empty($blacklistedAccountNumbers)) {
            foreach ($blacklistedAccountNumbers as $account_nos) {

                $data = [];
                $data['consumer_name'] = $account_nos->first_name . " " . $account_nos->middle_name . " " . $account_nos->last_name;
                $data['bank_link_type'] = $account_nos->account_id != '' && $account_nos->banking_solution_id != '' ? 'Direct Link' : 'Manual Link';
                $data['status'] = $account_nos->status;
                $data['account_no'] = $account_nos->account_no;
                $data['acc_no'] = 'XXXXXXXX' . substr($account_nos->account_no, -4);
                $data['bank_account_id'] = $account_nos->bank_account_id;
                $data['account_number_blacklisted'] = $account_nos->account_no_status;
                $data['created_at'] = date('m-d-Y h:i A', strtotime($account_nos->created_at));

                array_push($blacklistedAccountNumbersArr, $data);
            }
        } else {
            $blacklistedAccountNumbersArr = [];
        }

        return $blacklistedAccountNumbersArr;
    }

    /**
     * searchConsumerAccountNo
     * Search the Consumer and their linked accounts
     * @param  mixed $request
     * @return void
     */
    public function searchConsumerAccountNo(Request $request){
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer linked accounts search started...");
        // Validating input request
        $this->validate($request, [
            'consumer' => VALIDATION_REQUIRED_WITHOUT_ALL . ':phone_no,email,account_no',
            'phone_no' => VALIDATION_REQUIRED_WITHOUT_ALL . ':email,consumer,account_no',
            'email' => VALIDATION_REQUIRED_WITHOUT_ALL . ':phone_no,consumer,account_no',
            'account_no' => VALIDATION_REQUIRED_WITHOUT_ALL . ':phone_no,consumer,email',
        ]);

        // Search the linked accounts for a consumer
        $consumerAccountNumbers = $this->_getConsumerLinkedAccountNumbers($request);

        $message = trans('message.consumer_linked_accounts_fetch_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer linked accounts search complete.");
        return renderResponse(SUCCESS, $message, $consumerAccountNumbers);
    }

    private function _getConsumerLinkedAccountNumbers($request){
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer linked accounts search Started.");

        $sql = "SELECT user_bank_account_info.account_no,user_bank_account_info.id,user_bank_account_info.created_at, user_bank_account_info.account_id, user_bank_account_info.finicity_id, user_bank_account_info.banking_solution_id, users.first_name,users.middle_name,users.last_name,users.bank_link_type,status_master.status FROM users JOIN user_bank_account_info ON users.user_id = user_bank_account_info.user_id JOIN status_master ON status_master.id = user_bank_account_info.status WHERE 1 ";

        $searchStr = [];
        if (trim($request['consumer'])!='') {
            $sql .= " AND users.user_id = ? ";
            array_push($searchStr,$request['consumer']);
        }
        if (trim($request['phone_no'])!='') {
            $sql .= " AND users.phone = ? ";
            array_push($searchStr,$request['phone_no']);
        }
        if (trim($request['email'])!='') {
            $sql .= " AND users.email = ? ";
            array_push($searchStr,$request['email']);
        }
        if (trim($request['account_no'])!='') {
            $sql .= " AND user_bank_account_info.account_no = ? ";
            array_push($searchStr,$request['account_no']);
        }
        $sql .= "  ORDER BY users.created_at DESC LIMIT 100 ";
        $consumerAccountNumbers = DB::connection(MYSQL_RO)->Select($sql,$searchStr);

        $consumerAccountNumbersArr = [];
        if (!empty($consumerAccountNumbers)) {
            foreach ($consumerAccountNumbers as $account_nos) {

                $data = [];
                $data['consumer_name'] = $account_nos->first_name." ".$account_nos->middle_name." ".$account_nos->last_name;
                $data['account_no'] = 'XXXXXXXX'.substr($account_nos->account_no, -4);
                $data['bank_link_type'] = $account_nos->account_id != '' && $account_nos->banking_solution_id != '' ? 'Direct Link' : 'Manual Link';
                $data['status'] = $account_nos->status;
                $data['edit'] = $account_nos->id;
                $data['created_at'] = date('m-d-Y h:i A', strtotime($account_nos->created_at));

                array_push($consumerAccountNumbersArr, $data);
            }
        } else {
            $consumerAccountNumbersArr = [];
        }

        return $consumerAccountNumbersArr;
    }

    /**
     * addAccountToBlacklist
     * Add Seelected account to Blacklist
     * @param  mixed $request
     * @return void
     */
    public function addAccountToBlacklist(Request $request){
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Add selected account numbers to blacklist started...");
        // Validating input request
        $this->validate($request, [
            'selrows' => VALIDATION_REQUIRED,
        ]);

        $user = Auth::user(); // Fetching the logged in user details
        // Get users whose accounts are to be blacklisted
        $users = UserBankAccountInfo::whereIn('id',$this->request->get('selrows'))->select('user_id')->get();
        $blacklist_account_no_array = [];
        foreach($users as $val){
            // Fetch all the accounts associated with user to blacklist
            $blacklistedAccountNo = UserBankAccountInfo::where('user_id',$val->user_id)->select('account_no')->get();
            foreach($blacklistedAccountNo as $acc){
                // Check if the account number is already blacklisted
                $checkAccountNumberExists = BlacklistedAccountNumber::where(['account_no' => $acc->account_no,'is_delete' => 0])->first();
                if(empty($checkAccountNumberExists)){
                    $data = [];
                    $data['id'] = generateUUID();
                    $data['account_no'] = $acc->account_no;
                    $data['created_by'] = $user->user_id;
                    $data['created_at'] = Carbon::now();
                    $data['updated_at'] = Carbon::now();
                    $blacklist_account_no_array[] = $data;
                }
            }
        }
        BlacklistedAccountNumber::insert($blacklist_account_no_array); // Insert into blacklist table

        $message = trans('message.add_account_to_blacklist_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Selected account added to blacklist complete.");
        return renderResponse(SUCCESS, $message, null);
    }

    /**
     * deleteBlacklistedAccountNumber
     * Delete account number from blacklist
     * @param  mixed $request
     * @return void
     */
    public function deleteBlacklistedAccountNumber(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Delete account number from blacklist started...");
        // Validating input request
        $this->validate($request, [
            'account_no' => VALIDATION_REQUIRED,
        ]);

        // Update is_delete flag
        BlacklistedAccountNumber::where(['account_no' => $this->request->get('account_no'), 'is_delete' => 0])->update(['is_delete' => 1]);

        $message = trans('message.delete_blacklisted_account_number_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Blacklisted account number deleted.");
        return renderResponse(SUCCESS, $message, null);
    }

    /**
     * searchValidAccountNumber
     * This function is used to get all the Valid Account Number
     * @param  mixed $request
     * @return void
     */
    public function searchValidAccountNumber(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Valid account number search started...");
        // Validating input request
        $this->validate($request, [
            'consumer' => VALIDATION_REQUIRED_WITHOUT_ALL . ':phone_no,email,account_no',
            'phone_no' => VALIDATION_REQUIRED_WITHOUT_ALL . ':email,consumer,account_no',
            'email' => VALIDATION_REQUIRED_WITHOUT_ALL . ':phone_no,consumer,account_no',
            'account_no' => VALIDATION_REQUIRED_WITHOUT_ALL . ':phone_no,consumer,email',
        ]);

        // Search with in Valid account numbers
        $blacklistedAccountNumbers = $this->_getValidAccountNumber($request);

        $message = trans('message.valid_account_numbers_fetch_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Valid account number search complete.");
        return renderResponse(SUCCESS, $message, $blacklistedAccountNumbers);
    }

    /**
     * _getValidAccountNumber
     * Fetch the Blacklisted account numbers
     * @param  mixed $request
     * @return void
     */
    private function _getValidAccountNumber($request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Valid account number search Started.");

        $sql = "SELECT valid_account_numbers.*,users.first_name,users.middle_name,users.last_name FROM valid_account_numbers JOIN users ON valid_account_numbers.consumer_id = users.user_id WHERE valid_account_numbers.is_delete = 0 ";

        $searchStr = [];
        if (strlen(trim($request['consumer'])) >= 3) {
            $sql .= ' AND LOWER(
                REPLACE(CONCAT(COALESCE(
                REPLACE(users.first_name, " ",""), "")," ", COALESCE(
                REPLACE(users.middle_name, " ",""), "")," ", COALESCE(
                REPLACE(users.last_name, " ",""), "")),"  "," ")) LIKE ? ';
            array_push($searchStr,'%' . $request['consumer'] . '%');
        }
        if (trim($request['email'])) {
            $sql .= " AND users.email = ? ";
            array_push($searchStr,$request['email']);
        }
        if (trim($request['phone_no'])) {
            $sql .= " AND users.phone = ? ";
            array_push($searchStr,$request['phone_no']);
        }
        if (trim($request['account_no'])) {
            $sql .= " AND valid_account_numbers.account_no = ? ";
            array_push($searchStr,$request['account_no']);
        }
        $sql .= "  ORDER BY valid_account_numbers.created_at DESC LIMIT 100 ";
        $validAccountNumbers = DB::connection(MYSQL_RO)->Select($sql,$searchStr);

        $validAccountNumbersArr = [];
        if (!empty($validAccountNumbers)) {
            foreach ($validAccountNumbers as $account_nos) {

                $data = [];
                $data['name'] = $account_nos->first_name . ' ' . $account_nos->middle_name . ' ' . $account_nos->last_name;
                $data['account_no'] = $account_nos->account_no;
                $data['acc_no'] = 'XXXXXXXX'.substr($account_nos->account_no, -4);
                $data['edit'] = $account_nos->id;
                $data['created_at'] = date('m-d-Y h:i A', strtotime($account_nos->created_at));

                array_push($validAccountNumbersArr, $data);
            }
        } else {
            $validAccountNumbersArr = [];
        }

        return $validAccountNumbersArr;
    }

    /**
     * add new account numbers to the blacklist
     */
    public function addValidAccountNumber(Request $request)
    {
        $rule = array(
            'account_no' => VALIDATION_REQUIRED,
            'consumer' => VALIDATION_REQUIRED,
        );
        $this->__validate($request->all(), $rule);

        // check if the account number is blacklisted or not
        $checkBlacklistedAccountNumber = BlacklistedAccountNumber::where(['account_no' => $this->request->get('account_no'),'is_delete' => 0])->first();
        if(empty($checkBlacklistedAccountNumber)){
            $message = trans('message.account_number_not_blacklisted');
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Account number is not blacklisted.");
            return renderResponse(FAIL, $message, null);
        }

        // check if the account number is already added to the valid account number table for the consumer
        $checkAccountNumberExists = ValidAccountNumber::where(['account_no' => $this->request->get('account_no'),'consumer_id' => $this->request->get('consumer'),'is_delete' => 0])->first();
        if (!empty($checkAccountNumberExists)) {
            $message = trans('message.valid_account_already_exists');
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Valid account number already exists for consumer with User ID:" . $this->request->get('consumer'));
            return renderResponse(FAIL, $message, null);
        }

        // check if the account number is manually linked with the consumer
        $userAccountDetails = User::join('user_bank_account_info','users.user_id','=','user_bank_account_info.user_id')->where(['users.bank_link_type' => 0,'user_bank_account_info.account_no' => $this->request->get('account_no'),'user_bank_account_info.user_id' => $this->request->get('consumer')])->get();
        if(count($userAccountDetails) > 0){
            $message = trans('message.account_manually_linked');
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Account is manually linked for the consumer with User ID:" . $this->request->get('consumer'));
            return renderResponse(SUCCESS, $message, 0);
        }else{
            $message = trans('message.account_directly_linked');
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Account is directly linked for the consumer with User ID:" . $this->request->get('consumer'));
            return renderResponse(SUCCESS, $message, 1);
        }
    }

    /**
     * addConsumerAccountNumber
     * Add valid account number to the list for a consumer
     * @param  mixed $request
     * @return void
     */
    public function addConsumerAccountNumber(Request $request){
        $rule = array(
            'account_no' => VALIDATION_REQUIRED,
            'consumer' => VALIDATION_REQUIRED,
        );
        $this->__validate($request->all(), $rule);

        $user_details = Auth::user(); // Fetching Logged In User Details

        // Add account number to valid account list
        $validAccountNumber = new ValidAccountNumber();
        $validAccountNumber->account_no = $this->request->get('account_no');
        $validAccountNumber->consumer_id = $this->request->get('consumer');
        $validAccountNumber->created_by =$user_details->user_id;
        $validAccountNumber->save();
        $message = trans('message.valid_account_number_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Valid account number added for consumer with User ID:" . $this->request->get('consumer'));
        return renderResponse(SUCCESS, $message, null);
    }

    /**
     * deleteValidAccountNumber
     * Delete account number from valid account number list
     * @param  mixed $request
     * @return void
     */
    public function deleteValidAccountNumber(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Delete account number from valid account number list started...");
        // Validating input request
        $this->validate($request, [
            'id' => VALIDATION_REQUIRED,
        ]);

        // Update is_delete flag
        $validAccountNo = ValidAccountNumber::find($request->get('id'));
        $validAccountNo->is_delete = 1;
        $validAccountNo->save();

        $message = trans('message.delete_valid_account_number_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Valid account number deleted.");
        return renderResponse(SUCCESS, $message, null);
    }

    /**
     * consumerAccountList
     * This function is used to get list of all consumer with this account no.
     * @param  mixed $request
     * @return void
     */
    public function consumerAccountList(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer account number search started...");
        // Validating input request
        $this->validate($request, [
            'id' => VALIDATION_REQUIRED,
        ]);

        // Search consumer with the given account number
        $accountNumbers = $this->_getConsumerList($request);

        $message = trans('message.consumer_account_number_fetch_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer account number search complete.");
        return renderResponse(SUCCESS, $message, $accountNumbers);
    }

    /**
     * _getConsumerList
     * Fetch the list of account numbers
     * @param  mixed $request
     * @return void
     */
    private function _getConsumerList($request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer account number search started.");

        // Fetch Account No
        $account = UserBankAccountInfo::find($request['id']);

        $sql = "SELECT users.first_name,users.middle_name,users.last_name,status_master.status,user_bank_account_info.account_no,users.bank_link_type FROM users JOIN user_bank_account_info ON users.user_id = user_bank_account_info.user_id JOIN status_master ON status_master.id = user_bank_account_info.status WHERE user_bank_account_info.account_no = ? ";

        $sql .= "  ORDER BY users.created_at DESC";
        $accounts = DB::connection(MYSQL_RO)->Select($sql,[$account->account_no]);

        $accountsArr = [];
        if (!empty($accounts)) {
            foreach ($accounts as $account_nos) {

                $data = [];
                $data['consumer_name'] = $account_nos->first_name." ".$account_nos->middle_name." ".$account_nos->last_name;
                $data['account_no'] = 'XXXXXXXX'.substr($account_nos->account_no, -4);
                $data['bank_link_type'] = $account_nos->bank_link_type == 1 ? 'Direct Link' : 'Manual Link';
                $data['status'] = $account_nos->status;

                array_push($accountsArr, $data);
            }
        } else {
            $accountsArr = [];
        }

        return $accountsArr;
    }
}
