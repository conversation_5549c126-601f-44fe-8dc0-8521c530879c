{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.3", "bensontrent/firestore-php": "^3.1", "laravel/framework": "^11.0", "laravel/tinker": "^2.7.2", "league/flysystem": "^3.3.0", "league/flysystem-aws-s3-v3": "^3.3.0", "maatwebsite/excel": "^3.1", "smalot/pdfparser": "^2.0", "symfony/amazon-mailer": "^7.2", "symfony/sendgrid-mailer": "^7.2", "twilio/sdk": "^6.40", "tymon/jwt-auth": "^2.1"}, "require-dev": {"spatie/laravel-ignition": "^2.5", "mockery/mockery": "^1.0", "nunomaduro/collision": "^8.1", "phpunit/phpunit": "^10.0"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"php-http/discovery": false}}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/"}, "classmap": ["database/seeds", "database/factories"], "files": ["app/Utils/helpers.php", "app/Utils/constant.php", "app/Utils/dbhelpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}