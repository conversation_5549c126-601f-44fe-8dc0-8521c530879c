<template>
    <div>
        <button
        :disabled="submitBtn"
        v-if="!submitFlag"
        @click="clickEvent"
        type="button"
        class="btn btn-success btn-block"
        >
        {{label}}
        </button>

        <button
        disabled
        v-if="submitFlag"
        type="button"
        class="btn btn-success btn-block"
        >
        {{counterLabel}} {{timerCount}}s
        </button>
    </div>
</template>

<script>
import api from "@/api/auth.js";
import moment from "moment";

export default {
    props:{
        onClick: {
            type: Function,
            required: true
        },
        label: {
            type: String,
            default: "Send"
        },
        counterLabel: {
            type: String,
            default: "Send After"
        },
    },
    data(){
        return{
            timerCounts: [],
            maxSendEmailCount: null,
            timerCount: null,
            sendEmailBlockDuration: null,
            submitFlag: false,
            submitBtn: true
        }
    },
    methods:{
        clickEvent(){
            var self = this;

            // fetch number of max email send from local storage 
            var sendEmailCount = parseInt(localStorage.getItem("MAX_EMAIL_SEND"));

            if(sendEmailCount){

                if(sendEmailCount == self.maxSendEmailCount)
                {
                    const secs = self.sendEmailBlockDuration;
                    var duration = Math.floor(secs / 60);
                    var message = "Can\'t send more email, you have been blocked for " + duration +" minute";
                    error(message);
                }
                else
                {
                    if(sendEmailCount == 1)
                    {
                        localStorage.removeItem("MAX_EMAIL_SEND")
                        localStorage.setItem("MAX_EMAIL_SEND", 2)
                        self.timerCount = self.timerCounts.second_attempt;
                        self.submitFlag = true;
                        self.onClick();
                    }
                    if(sendEmailCount == 2)
                    {
                        localStorage.removeItem("MAX_EMAIL_SEND")
                        localStorage.setItem("MAX_EMAIL_SEND", 3)
                        self.timerCount = self.timerCounts.third_attempt;
                        self.submitFlag = true;
                        self.onClick();
                    }
                    if(sendEmailCount >= 3)
                    {
                        localStorage.removeItem("MAX_EMAIL_SEND")
                        localStorage.setItem("MAX_EMAIL_SEND", (sendEmailCount+1))
                        self.timerCount = self.timerCounts.third_attempt;
                        self.submitFlag = true;
                        self.onClick();
                    }
                }
            }else{
                localStorage.setItem("MAX_EMAIL_SEND", 1)
                self.timerCount = self.timerCounts.first_attempt;
                self.submitFlag = true;
                self.onClick();
            }

           
        },
        validateForgotPassword(){
            var self = this;

            self.submitBtn = true
            
            api
            .validateForgotPassword()
            .then((response) => {
                if (response.code == 200) {

                    self.submitBtn = false
                    self.timerCounts = response.data.send_email_after
                    self.maxSendEmailCount = response.data.send_email_count
                    self.sendEmailBlockDuration = response.data.send_email_block_duration
                  
                } else {
                    self.submitBtn = false
                    error(response.message);
                }
            })
            .catch((err) => {
                console.log(err);
            });
        }
    },
    created(){
        var self = this;
        self.validateForgotPassword();
    },
    watch:{
        timerCount: {
            handler(value) {
                if (value > 0) {
                    setTimeout(() => {
                        this.timerCount--;
                    }, 1000);
                }else{
                    var self = this;
                    // localStorage.removeItem("IS_MAX_SEND_EMAIL")
                    // localStorage.removeItem("MAX_EMAIL_SEND")
                    // localStorage.setItem("MAX_EMAIL_SEND", (self.maxSend - 1))
                    self.submitFlag = false
                }
            },
            immediate: false, // This ensures the watcher is triggered upon creation
        },
    }
}
</script>