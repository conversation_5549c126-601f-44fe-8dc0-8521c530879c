<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class NotificationLog extends Model
{
    /**
     * __construct
     * This function will insert an alphanumeric 32 character unique id as primary key in the table whenever a data get inserted
     * @param  mixed $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        $this->id = generateUUID();

        parent::__construct($attributes);
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'parent_id',
        'type',
        'notification_event',
        'from_email',
        'to_email',
        'to_phone',
        'gateway_used',
        'email_subject',
        'email_body',
        'sms_body',
        'resend_expiration_time',
        'notification_time',
        'exception',
        'is_resend',
    ];
    public $timestamps = true;
    public $incrementing = false;
}
