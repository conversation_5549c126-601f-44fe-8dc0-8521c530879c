<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;


class EcommerceCategoryCodes extends Model 
{
    

    protected $table = 'ecommerce_category_codes';

    /**
     * __construct
     * This function will insert an alphanumeric 32 character unique id as primary key in the table whenever a data get inserted
     * @param  mixed $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        $this->id = generateUUID();
        parent::__construct($attributes);
    }

    protected $fillable = [
        'category_name',
        'category_code',
        'nomenclature',
    ];
    public $timestamps = true;
    public $incrementing = false;
}
