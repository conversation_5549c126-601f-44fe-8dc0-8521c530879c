<?php

use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * Generates random strings
 */
function generateUUID()
{
    $currentTime = (string) microtime(true);

    $randNumber = (string) rand(10000, 1000000);

    $shuffledString = str_shuffle("ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789");

    return md5($currentTime . $randNumber . $shuffledString);
}

/**
 * generateRandomString
 * This function generates a Random String with the given length
 * @param  mixed $stringLength
 * @return void
 */
function generateRandomString($stringLength)
{
    $permitted_chars = '0123456789abcdefghijklmnopqrstuvwxyz';

    return substr(str_shuffle($permitted_chars), 0, $stringLength);
}

/**
 * Chnage the Date Format relevant to Database
 *
 * @param  mixed $date
 *
 * @return void
 */
function change_dateformat_reverse($date)
{
    $exp_date = explode('-', $date);
    return $exp_date[2] . '-' . $exp_date[0] . '-' . $exp_date[1];
}

/**
 * date_convert_timezone
 * Converts Datetime based on the given Timezone
 * @param  mixed $source_date
 * @param  mixed $to_timezone
 * @return void
 */
function date_convert_timezone($source_date, $to_timezone, $from_timezone = false)
{
    $from_timezone = $from_timezone != '' ? $from_timezone : 'UTC';
    $from_dateformat = DB_DATE_FORMAT;
    $to_dateformat = DB_DATE_FORMAT;
    $date = DateTime::createFromFormat($from_dateformat, $source_date, new DateTimeZone($from_timezone));
    $date->setTimeZone(new DateTimeZone($to_timezone));
    return $date->format($to_dateformat);
}

/**
 * Formatting json result
 * @param type $statusCode
 * @param type $data
 * @param type $message
 * @param type $http_response only sent when exception is thrown
 * @return type
 */
function renderResponse($statusCode, $message, $data, $http_response = true)
{
    $response = array(
        'code' => $statusCode,
        'message' => $message,
        'data' => $data,
    );
    return $http_response ? response()->json($response, $statusCode) : response()->json($response);
}

/**
 * error2ul
 * Formats the validation message
 * @param  mixed $errors
 * @return void
 */
function error2ul($errors)
{
    if (empty($errors)) {
        return null;
    }
    // Top level UL
    $out = null;
    foreach ($errors as $elem) {
        $out = ucwords(str_replace('_', ' ', $elem));
    }

    return $out;
}

/**
 * Checks if the transaction is cancellable or not
 */
function checkTransactionEligibleForCancellation($transaction_time, $is_admin_driven = 0, $scheduled_posting_date = null)
{
    //convert transaction time to the timezone given
    $from_timezone = 'UTC';
    $to_timezone = config('app.transaction_settlement_timezone');
    $end_time = config('app.transaction_settlement_start');

    if($is_admin_driven == 1){
        // If scheduled posting time is null but scheduled posting date is not null, we'll not cancel the transaction
         if(!isset($transaction_time) && isset($scheduled_posting_date)){
            return 0;
        }
        // If scheduled posting time is null, we'll cancel the transaction
        if(!isset($transaction_time)){
            return 1;
        }
    }
    $date = DateTime::createFromFormat(DB_DATE_FORMAT, $transaction_time, new DateTimeZone($from_timezone));
    $date->setTimeZone(new DateTimeZone($to_timezone));
    $timezone_transaction_time = $date->format(DB_DATE_FORMAT);

    if ($is_admin_driven == 1 && $scheduled_posting_date) {
        // Extract the date part from $timezone_transaction_time
        $transaction_date = date("Y-m-d", strtotime($timezone_transaction_time));
        if ($scheduled_posting_date < $transaction_date) {
            Log::info(__METHOD__ . " (" . __LINE__ . ") - Adjusted transaction time", [
                'original_timezone_transaction_time' => $timezone_transaction_time,
                'new_timezone_transaction_time' => $scheduled_posting_date . " 00:00:00"
            ]);
            $timezone_transaction_time = $scheduled_posting_date . " 00:00:00";
        }
    }

    //get current time for that timezone
    $date = new DateTime("now", new DateTimeZone($to_timezone));
    $current_datetime = $date->format(DB_DATE_FORMAT);

    //get the end time for that timezone
    $date = new DateTime(date('Y-m-d', strtotime('+1 day', strtotime($timezone_transaction_time))));
    $new_date = $date->add(new DateInterval("PT{$end_time}H"));
    $end_date = $new_date->format(DB_DATE_FORMAT);
    //if current time falses between end date and transaction time then it is eligible for cancellation
    if (Carbon::parse($current_datetime)->lt($end_date) && Carbon::parse($timezone_transaction_time)->gte($timezone_transaction_time)) {
        return 1;
    }
    return 0;
}

/**
 * generateTransactionId
 * This function will generate a Transaction Number For Each Transactions
 * @return void
 */
function generateTransactionId()
{
    $vowels = 'AEIU'; // O ommited from vowels as zero and O is a bit confusing for the Consumer
    $consonants = '0123456789BCDFGHJKLMNPQRSTVWXYZ';
    $idnumber = '';
    $alt = time() % 2;
    for ($i = 0; $i < 12; $i++) {
        if ($alt == 1) {
            $idnumber .= $consonants[(rand() % strlen($consonants))];
            $alt = 0;
        } else {
            $idnumber .= $vowels[(rand() % strlen($vowels))];
            $alt = 1;
        }
    }
    return $idnumber;
}

/**
 * round_up
 * This function will always round up the value to given decimal number
 * @param  mixed $value
 * @param  mixed $precision
 * @return void
 */
function round_up($value, $precision)
{
    $pow = pow(10, $precision);
    return (ceil($pow * $value) + ceil($pow * $value - ceil($pow * $value))) / $pow;
}

/**
 * numToOrdinalWord
 * Convert Number to words
 * @param  mixed $num
 * @return void
 */
function numToOrdinalWord($num)
{
    $first_word = array('eth', 'First', 'Second', 'Third', 'Fourth', 'Fifth', 'Sixth', 'Seventh', 'Eighth', 'Ninth', 'Tenth', 'Elevents', 'Twelfth', 'Thirteenth', 'Fourteenth', 'Fifteenth', 'Sixteenth', 'Seventeenth', 'Eighteenth', 'Nineteenth', 'Twentieth');
    $second_word = array('', '', 'Twenty', 'Thirty', 'Forty', 'Fifty');

    if ($num <= 20) {
        return $first_word[$num];
    }

    $first_num = substr($num, -1, 1);
    $second_num = substr($num, -2, 1);

    return $string = str_replace('y-eth', 'ieth', $second_word[$second_num] . '-' . $first_word[$first_num]);
}

/**
 * getSettingsValue
 * Time Conversion to PST for comparison with PST time
 * @return void
 */
function getPstTime()
{
    $target_time_zone = new \DateTimeZone('PST');
    $date_time = new \DateTime('now', $target_time_zone);
    $pst_offset = $date_time->format('P');

    return $pst_offset;
}
/**
 * getMstTime
 * Time Conversion to MST for comparison with MST time
 * @return void
 */
function getMstTime()
{
    $target_time_zone = new \DateTimeZone('MST');
    $date_time = new \DateTime('now', $target_time_zone);
    $pst_offset = $date_time->format('P');

    return $pst_offset;
}
/**
 * searchMultiArray
 * This function will search if element exists in array
 * @param  mixed $key
 * @param  mixed $val
 * @param  mixed $array
 * @return void
 */
function searchMultiArray($key, $val, $array)
{
    $result_arr = json_decode(json_encode($array), true);
    foreach ($result_arr as $element) {
        if ($element[$key] == $val) {
            return 1;
        }
    }
    return 0;
}

/**
 * generateAchIdentifier
 * This function will generate an alphanumeric string for Consumers
 * @return void
 */
function generateAchIdentifier()
{
    // Generate a random unique string with 15 alphanumeric characters
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $length = 15;
    $uniqueString = '';
    for ($i = 0; $i < $length; $i++) {
        $uniqueString .= $characters[rand(0, strlen($characters) - 1)];
    }
    return $uniqueString;
}

function getDifferentInDays($start_date, $end_date)
{
    $start_date = explode('-',$start_date);
    $end_date = explode('-',$end_date);

    $startDate = Carbon::create($start_date[0], $start_date[1], $start_date[2]);
    $endDate = Carbon::create($end_date[0], $end_date[1], $end_date[2]);

    return $startDate->diffInDays($endDate);
}

/**
 * getTimeElapsed
 * This function will return the time taken to call the api
 * @param  mixed $request
 * @return void
 */
function getTimeElapsed($request)
{
    return number_format(microtime(true) - $request->server('REQUEST_TIME_FLOAT'), 3);
}


function getTimeElapsedArr($request)
{
    return number_format(microtime(true) - (float) $request['server'], 3);
}

/**
 * Formats a given amount as a currency string.
 *
 * @param float|int $amount The amount to be formatted.
 * @return string The formatted currency string with a dollar sign and two decimal places.
 */

function formatCurrency($amount)
{
    return "$" . number_format($amount, 2);
}

/**
 * Formats a given rate as a percentage string.
 *
 * @param float|int|null $rate The rate to be formatted.
 * @return string The formatted percentage string with a percentage sign.
 *                Returns "0.00%" if the rate is null.
 */

function formatPercentage($rate)
{
    return $rate !== null ? $rate . "%" : "0.00%";
}

/**
 * getAccountNumberLastFourDigit
 * Returns the last 4 digits of the given account number
 * @param  string $accountNo - The account number
 * @return string
 */
function getAccountNumberLastFourDigit($accountNo){
    return substr($accountNo, -4);
 }