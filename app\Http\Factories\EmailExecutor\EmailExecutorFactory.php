<?php

namespace App\Http\Factories\EmailExecutor;

use App\Models\EmailTemplate;
use App\Models\NotificationLog;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class EmailExecutorFactory implements EmailExecutorInterface
{
    public function corporateParentWelcomeEmail($params)
    {
        $user_details = User::where('user_id', $params['user_id'])->first();

        $params['event'] = CORPORATE_PARENT_WELCOME_EMAIL;
        $params['email'] = $user_details->contact_person_email;
        $params['from_email'] = FROM_MAIL_INFO;

        $this->_sendMail($params);
    }

    public function adminUsersWelcomeEmail($params)
    {
        $user_details = User::where('user_id', $params['user_id'])->first();

        $params['event'] = ADMIN_USERS_WELCOME_EMAIL;
        $params['email'] = $user_details->email;
        $params['from_email'] = FROM_MAIL_INFO;

        $this->_sendMail($params);
    }

    public function approvedConsumerIdenity($params)
    {
        $params['event'] = CONSUMER_IDENTITY_VALIDATION_SUCCESS;
        $params['from_email'] = CONSUMER_FROM_MAIL_INFO;

        $this->_sendMail($params);
    }

    public function consecutiveFailedLoginOTPEmail($params)
    {
        $params['event'] = CONSECUTIVE_FAILED_LOGIN;
        $params['from_email'] = CONSUMER_FROM_MAIL_INFO;

        $this->_sendMail($params);
    }

    public function forgotPassword($params)
    {
        $user_details = User::where('user_id', $params['user_id'])->first();
        $email = isset($params['email']) ? $params['email'] : $user_details->email;

        $params['event'] = ADMIN_PASSWORD_RESET;
        $params['email'] = $email;
        $params['from_email'] = FROM_MAIL_INFO;

        $this->_sendMail($params);
    }

    public function testEmail($params)
    {
        $params['event'] = $params['template_name'];
        $params['from_email'] = FROM_MAIL_INFO;
        $params['test_email'] = 1;

        $this->_sendMail($params);
    }

    public function approvedManualReview($params)
    {
        $params['event'] = GLOBAL_RADAR_REVIEW_EMAIL;
        $params['from_email'] = CONSUMER_FROM_MAIL_INFO;

        $this->_sendMail($params);
    }

    public function consumerMonthlyTransactionActivity($params)
    {
        $user_details = User::where('user_id', $params['user_id'])->first();
        $email = isset($params['email']) ? $params['email'] : $user_details->email;

        $params['event'] = CONSUMER_MONTHLY_TRANSACTION_ACTIVITY;
        $params['email'] = $email;
        $params['from_email'] = CONSUMER_FROM_MAIL_INFO;

        $this->_sendMail($params);
    }

    public function corporateParentDailyTransactionActivity($params)
    {
        $user_details = User::where('user_id', $params['user_id'])->first();
        $email = isset($params['email']) ? $params['email'] : $user_details->email;

        $params['event'] = CORPORATE_PARENT_DAILY_TRANSACTION_ACTIVITY;
        $params['email'] = $email;
        $params['from_email'] = FROM_MAIL_INFO;

        $this->_sendMail($params);
    }

    public function storeUserDailyTransactionActivity($params)
    {
        $params['event'] = CORPORATE_PARENT_DAILY_TRANSACTION_ACTIVITY;
        $params['from_email'] = FROM_MAIL_INFO;

        $this->_sendMail($params);
    }

    public function corporateParentEmailChange($params)
    {
        $user_details = User::where('user_id', $params['user_id'])->first();

        $params['event'] = CORPORATE_PARENT_EMAIL_CHANGE;
        $params['email'] = $user_details->contact_person_email;
        $params['from_email'] = FROM_MAIL_INFO;

        $this->_sendMail($params);
    }

    public function helpdeskWelcomeEmail($params)
    {
        $user_details = User::where('user_id', $params['user_id'])->first();

        $params['event'] = HELPDESK_WELCOME_EMAIL;
        $params['email'] = $user_details->email;
        $params['from_email'] = FROM_MAIL_INFO;

        $this->_sendMail($params);
    }

    public function helpdeskloginEmail($params)
    {
        $user_details = User::where('user_id', $params['user_id'])->first();

        $params['event'] = HELPDESK_LOGIN_PASSWORD;
        $params['email'] = $user_details->email;
        $params['from_email'] = FROM_MAIL_INFO;

        $this->_sendMail($params);
    }

    public function suspendConsumerIdenity($params)
    {
        $params['event'] = CONSUMER_IDENTITY_VALIDATION_SUSPENDEND;
        $params['from_email'] = CONSUMER_FROM_MAIL_INFO;

        $this->_sendMail($params);
    }

    public function approvedV1ConsumerIdenity($params)
    {
        $params['event'] = CONSUMER_IDENTITY_VALIDATION_SUCCESS;
        $params['from_email'] = CONSUMER_FROM_MAIL_INFO;

        $this->_sendMail($params);
    }

    public function consumerReturnTransactionMail($params)
    {
        $user_details = User::where('user_id', $params['user_id'])->first();

        $template_details = EmailTemplate::where('return_reason_id', $params['return_reason_id'])->first(); // Fetching the detail of the template whose mail body is to be genearted
        $params['event'] = $template_details->template_name;
        $params['email'] = $user_details->email;
        $params['from_email'] = CONSUMER_FROM_MAIL_INFO;

        $this->_sendMail($params);
    }

    public function consumerRetakeUploadDocumentMail($params)
    {
        $params['event'] = CONSUMER_RETAKE_DOCUMENT_UPLOAD_EMAIL;
        $params['from_email'] = CONSUMER_FROM_MAIL_INFO;

        $this->_sendMail($params);
    }

    public function consumerReturnTransactionFetchMail($params)
    {
        $user_details = User::where('user_id', $params['user_id'])->first();

        $params['event'] = CONSUMER_TRANSACTION_RETURN;
        $params['email'] = $user_details->email;
        $params['from_email'] = CONSUMER_FROM_MAIL_INFO;

        $this->_sendMail($params);
    }

    public function returnUpdateFailureEmailDueToUnkonwnReasonCode($params)
    {
        $emails = config('app.daily_transaction_email');

        $params['event'] = UNKNOWN_RETURN_REASON_RECEIVED;
        $params['from_email'] = FROM_MAIL_INFO;

        foreach (explode(',', $emails) as $valEmails) {
            $params['email'] = $valEmails;
            $this->_sendMail($params);
        }
    }

    public function emailForFifthThirdUserDetected($params)
    {
        $emails = config('app.email_for_fifth_third_user');

        $params['event'] = FIFTH_THIRD_USER_DETECTED;
        $params['from_email'] = FROM_MAIL_INFO;

        foreach (explode(',', $emails) as $valEmails) {
            $params['email'] = $valEmails;
            $this->_sendMail($params);
        }
    }

    public function resendMail($params)
    {
        $notification = NotificationLog::find($params['notification_id']);

        $params['parent_id'] = $notification->id;
        $params['user_id'] = $notification->user_id;
        $params['event'] = $notification->notification_event;
        $params['email'] = $notification->to_email;
        $params['from_email'] = $notification->from_email;
        $params['is_resend'] = 1;
        $params['subject'] = $notification->email_subject;
        $params['body'] = $notification->email_body;

        $this->_sendMail($params);
    }

    private function _sendMail($params, $encode_email = false)
    {
        $params['type'] = EMAIL;
        $notification_channel = getNotificationChannel($params);

        if (config('app.send_mail')) {
            if (!isset($params['test_email'])) {
                $body = isset($params['is_resend']) && $params['is_resend'] == 1 ? $params['body'] : stringBladeCompilationForBody($params['event'], $params);
                $params['subject'] = isset($params['is_resend']) && $params['is_resend'] == 1 ? $params['subject'] : stringBladeCompilationForSubject($params['event'], $params);
            } else {
                $body = isset($params['is_resend']) && $params['is_resend'] == 1 ? $params['body'] : stringBladeCompilationForBodyTest($params['event'], $params);
                $params['subject'] = isset($params['is_resend']) && $params['is_resend'] == 1 ? $params['subject'] : stringBladeCompilationForSubjectTest($params['event'], $params);
            }
            if ($body != null && $params['subject'] != null) {
                // Prepare Notification Data
                $notification_data = [
                    'parent_id' => isset($params['parent_id']) ? $params['parent_id'] : null,
                    'user_id' => isset($params['user_id']) ? $params['user_id'] : null,
                    'type' => $params['type'],
                    'event' => $params['event'],
                    'from_email' => $params['from_email'],
                    'to_email' => $params['email'],
                    'gateway_used' => $notification_channel->id,
                    'email_subject' => $params['subject'],
                    'email_body' => $body,
                    'is_resend' => isset($params['is_resend']) ? $params['is_resend'] : 0,
                ];
                // Insert the notification data into notification log table
                $params['notification_id'] = insertNotificationData($notification_data);
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Notification data inserted and sending data to mail function.");
                try {
                    Mail::mailer($notification_channel->val)->send(['html' => 'emails.mail'], ['html' => stripslashes($body)], function ($message) use ($params) {
                        $message->to($params['email'])->subject($params['subject']);
                        $message->from($params['from_email'], FROM_MAIL_NAME);
                    });
                    Log::channel('email')->info(addslashes(__METHOD__) . "(" . LINE . __LINE__ . ") - " . $params['event'] . " sent to  " . $params['email'] . " from " . strtoupper($notification_channel->name) . " Successfully at " . Carbon::now());
                } catch (\Exception $e) {
                    // Update the exception in the notification log table
                    NotificationLog::find($params['notification_id'])->update(['exception' => $e->getMessage()]);
                    Log::channel('email')->info(addslashes(__METHOD__) . "(" . LINE . __LINE__ . ") - " . "Error while sending while sending email to  " . $params['email'] . " from " . strtoupper($notification_channel->name) . ": " . $e);
                }
            }
        }

        return true;
    }

    public function emailForInstitutionwiseFraudUserDetected($params)
    {
        $emails = config('app.email_for_fifth_third_user');

        $params['event'] = SUSPECTED_DIRECT_LINK_CONSUMER_DETECTED;
        $params['from_email'] = FROM_MAIL_INFO;

        foreach (explode(',', $emails) as $valEmails) {
            $params['email'] = $valEmails;
            $this->_sendMail($params);
        }
    }

    public function petitionOnboardEmail($params)
    {
        // You can customize the event/template name as needed
        $params['event']      = MERCHANT_STORE_ONBOARDING_INTRODUCTORY_EMAIL; // Define this constant or use a string
        $params['from_email'] = CANPAY_CEO_FROM_MAIL_INFO;
        $this->_sendMail($params);
    }

    public function petitionRejectionEmail($params)
    {
        // You can customize the event/template name as needed
        $params['event']      = YOUR_PETITION_HAS_BEEN_REJECTED;
        $params['from_email'] = FROM_MAIL_INFO;
        $this->_sendMail($params);
    }
}
