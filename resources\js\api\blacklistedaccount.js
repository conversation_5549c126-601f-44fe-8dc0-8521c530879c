const searchBlacklistedAccountNo = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/searchblacklistedaccountno', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const deleteBlacklistedAccountNo = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/deleteblacklistedaccountno', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const searchValidAccountNo = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/searchvalidaccountno', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const deleteValidAccountNo = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/deletevalidaccountno', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const addValidAccountNumber = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/addvalidaccountnumber', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const addAccountNumber = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/addaccountnumber', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const searchConsumerAccountNo = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/searchconsumeraccountno', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const addAccountToBlacklist = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/addaccounttoblacklist', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const consumerAccountList = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/consumeraccountlist', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

export default {
    searchBlacklistedAccountNo,
    deleteBlacklistedAccountNo,
    searchValidAccountNo,
    deleteValidAccountNo,
    addValidAccountNumber,
    addAccountNumber,
    searchConsumerAccountNo,
    addAccountToBlacklist,
    consumerAccountList
};