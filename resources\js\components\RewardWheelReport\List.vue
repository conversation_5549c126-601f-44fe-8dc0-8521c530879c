<template>
<div>
    <div v-if="loading">
      <CanPayLoader/>
    </div>
    <div class="content-wrapper" style="min-height: 36px">
      <section class="content-header">
        <div class="container-fluid">
          <div class="row mb-2">
            <div class="col-sm-6"></div>
          </div>
        </div>
      </section>
      <div class="hold-transition sidebar-mini">
        <section class="content">
          <div class="container-fluid">
            <div class="row">
              <div class="col-12">
                <div class="card card-success">
                  <div class="card-header">
                    <h3 class="card-title">Reward Wheel Report</h3>
                    <b-button
                    class="btn-danger export-api-btn"
                    @click="reloadDatatable"
                    v-if="showReloadBtn"
                    >
                      <i class="fas fa-redo"></i> Reload
                    </b-button>
                  </div>

                  <div class="card-body">

                    <div class="card" style="box-shadow: none;">
                      <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                    <label for="start-date">
                                        Report Date
                                        <span class="red">*</span>
                                    </label>
                                    <input
                                        autocomplete="off"
                                        class="start-date form-control"
                                        placeholder="Start Date"
                                        id="start-date"
                                        onkeydown="return false"
                                    />
                                    </div>
                                </div>
                            </div>
                            <div class="row"><div class="col-md-4"><span class="red">Report will be generated form 7AM ET to next day 06:59:59AM ET.</span></div></div>
                        </div>
                    </div>

                    <div class="card" style="box-shadow: none;">
                      <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                    <label for="consumer">
                                        Consumer Name
                                    </label>
                                    <input
                                        autocomplete="off"
                                        class="form-control"
                                        placeholder="Consumer Name (Min 3 chars)"
                                        id="consumer"
                                        v-model="consumer"
                                    />
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                    <label for="consumer">
                                        Consumer Phone
                                    </label>
                                    <input
                                        autocomplete="off"
                                        class="form-control"
                                        placeholder="Consumer Phone (Exact)"
                                        id="consumer"
                                        v-model="phone_no"
                                    />
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                    <label for="consumer">
                                        Consumer Email
                                    </label>
                                    <input
                                        autocomplete="off"
                                        class="form-control"
                                        placeholder="Consumer Email (Exact)"
                                        id="consumer"
                                        v-model="email"
                                    />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card" style="box-shadow: none;">
                      <div class="card-body">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                    <label for="merchant">
                                        Select Store
                                    </label>
                                    <multiselect
                                        id="store"
                                        v-model="selectedStore"
                                        placeholder="Select Store (Min 3 chars)"
                                        label="retailer"
                                        :options="storelist"
                                        :loading="isLoading"
                                        :internal-search="false"
                                        v-validate="'required'"
                                        @search-change="getAllStores"
                                    ></multiselect>
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="form-group">
                                    <label for="merchant">
                                        Select Reward Wheel
                                    </label>
                                    <multiselect
                                        id="reward_wheel"
                                        v-model="selectedRewardWheel"
                                        placeholder="Select Reward Wheel"
                                        label="reward_wheel"
                                        :options="rewardwheellist"
                                        :internal-search="true"
                                        v-validate="'required'"
                                    ></multiselect>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>


                    <div class="row">
                    <div class="col-md-4">
                        <button
                          type="button"
                          class="btn btn-success"
                          @click="generateReport(false)"
                          id="generateBtn"
                          :disabled="email.length == 0 && phone_no.length == 0 && consumer.length == 0"
                        >
                          Generate</button
                        >
                        <button
                          type="button"
                          @click="exportReport()"
                          class="btn btn-danger margin-left-5"
                        >
                          Export
                          <i class="fa fa-download" aria-hidden="true"></i>
                        </button>
                        <button
                          type="button"
                          @click="reset()"
                          class="btn btn-success margin-left-5"
                        >
                          Reset
                        </button>
                    </div>
                  </div>

                  <div class="row">
                    <div class="col-12 mt-3 red" v-if="email.length == 0 && phone_no.length == 0 && consumer.length == 0">
                      <p>Provide consumer name or consumer email or consumer phone to enable generate button.</p>
                    </div>
                  </div>
                  </div>
                  <div class="card-footer"></div>
                  <!-- /.card-header -->
                  <div class="card-body">
                    <div class="row">
                            <div class="col-4">
                            <b-table-simple bordered>
                                <b-tbody>
                                <b-tr>
                                    <b-td class="text-bold text-left">Total Spin Earned</b-td>
                                    <b-td class="text-bold text-center">{{ total_spins_earned }}</b-td>
                                </b-tr>
                                <b-tr>
                                    <b-td class="text-bold text-left">Total Spin Spent</b-td>
                                    <b-td class="text-bold text-center">{{ total_spins_spent }}</b-td>
                                </b-tr>
                                <b-tr>
                                    <b-td class="text-bold text-left">Total Amount Earned via Points</b-td>
                                    <b-td class="text-bold text-center">{{ total_amount_earned_via_points }}</b-td>
                                </b-tr>
                                <b-tr>
                                    <b-td class="text-bold text-left">Total Amount Spent with Points</b-td>
                                    <b-td class="text-bold text-center">{{ total_amount_spent_with_points }}</b-td>
                                </b-tr>
                                </b-tbody>
                            </b-table-simple>
                            </div>
                        </div>
                    <div class="row">
                      <div class="col-12">
                        <table
                          id="rewardWheelReportTable"
                          class="table"
                          style="width: 100%; white-space: normal"
                        >
                          <thead>
                            <tr>
                              <th>Name</th>
                              <th>Email</th>
                              <th>Phone</th>
                              <th>Spin Time</th>
                              <th>Winning Details</th>
                              <th>Reward Point</th>
                              <th>Reward Amount ($)</th>
                              <th>Transaction Amount ($)
                                <a class="mb-1" href="javascript:void(0)" v-b-tooltip.hover title="Transaction Amount will be zero for Free Spin obtained from another Free Spin.">
                                    <svg width="15px" version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
                                    viewBox="0 0 490 490" style="enable-background:new 0 0 490 490; margin-bottom: 2px;" xml:space="preserve"><g><g><g><path d="M245,490C109.9,490,0,380.1,0,245S109.9,0,245,0s245,109.9,245,245S380.1,490,245,490z M245,62C144.1,62,62,144.1,62,245s82.1,183,183,183s183-82.1,183-183S345.9,62,245,62z"/></g><g><g><circle cx="241.3" cy="159.2" r="29.1"/></g><g><polygon points="285.1,359.9 270.4,359.9 219.6,359.9 204.9,359.9 204.9,321 219.6,321 219.6,254.8 205.1,254.8 205.1,215.9 219.6,215.9 263.1,215.9 270.4,215.9 270.4,321 285.1,321 				"/></g></g></g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g>
                                    </svg>
                              </a></th>
                              <th>Store</th>
                              <th>Reward Wheel</th>
                              <th>Earned Spin</th>
                            </tr>
                          </thead>
                        </table>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
</div>
  </template>
  <script>
  import api from "@/api/transaction.js";
  import rewardwheelapi from "@/api/rewardwheel.js";
  import moment from "moment";
  import { validationMixin } from "vuelidate";
  import { required, minLength } from "vuelidate/lib/validators";
  import { HourGlass } from "vue-loading-spinner";
  import CanPayLoader from "../CustomLoader/CanPayLoader.vue"
  import commonConstants from "@/common/constant.js";
  export default {
    mixins: [validationMixin],
    data() {
      return {
        allRewardWheelReports: {},
        currentUser: localStorage.getItem("user")
          ? JSON.parse(localStorage.getItem("user"))
          : null,
        loading: false,
        report: [],
        generateExport: false,
        showReloadBtn:false,
        constants: commonConstants,

        consumer:"",
        phone_no:"",
        email:"",
        selectedStore: null,
        selectedRewardWheel: null,

        storelist: [],
        rewardwheellist: [],

        isLoading: false,
        rewardWheelAPIUrl: process.env.MIX_REWARD_WHEEL_APP_URL,

        total_spins_earned: 0,
        total_spins_spent: 0,

        total_amount_earned_via_points: 0,
        total_amount_spent_with_points: 0,
      };
    },
    components: {
      HourGlass,
      CanPayLoader
    },
    methods: {
      //API call to fetch All stores
      getAllStores(searchtxt) {
        var self = this;
        if(searchtxt.length >= 3){
          self.isLoading = true;
          var request = {
            searchtxt: searchtxt,
          };
        api
          .getStores(request)
          .then(function (response) {
            if (response.code == 200) {
              self.storelist = response.data;
              self.isLoading = false;
            }else {
              error(response.message);
              self.isLoading = false;
            }
          })
          .catch(function (error) {
          });
        }
      },
      getActiveRewardwheels() {
        var self = this;
        rewardwheelapi
          .getActiveRewardwheels()
          .then(function (response) {
            if (response.code == 200) {
              self.rewardwheellist = response.data;
            }else {
              error(response.message);
            }
          })
          .catch(function (error) {
          });
      },
      reloadDatatable(){
        var self = this;
        var from_date = moment($("#start-date").val()).format("YYYY-MM-DD");
        var to_date = moment($("#end-date").val()).format("YYYY-MM-DD");
        var store = "";
        var reward_wheel = "";
        var consumer = "";
        var email = "";
        var phone_no = "";

        this.store = ""
        this.reward_wheel = ""
        this.consumer = ""
        this.email = ""
        this.phone_no = ""

        this.selectedStore = null,
        this.selectedRewardWheel = null,

        self.report = [];
        var request = {
          from_date: from_date,
          to_date: to_date,
          store: store,
          reward_wheel: reward_wheel,
          consumer: consumer,
          email: email,
          phone_no: phone_no,
        };
        self.loading = true;
        self.loadDT(request);
      },
      // API call to generate the all the Transactions Report
      generateReport() {
        var self = this;
        if($("#start-date").val() == ''){
            error("Please select a date to generate the report.");
            return false;
        }
        if (
          moment($("#start-date").val()).format("YYYY-MM-DD") >
            moment().format("YYYY-MM-DD") &&
          $("#start-date").val() != ""
        ) {
          error("Start date cannot be from future.");
          return false;
        }

        var from_date = moment($("#start-date").val()).format("YYYY-MM-DD");

        if(self.selectedStore === null){
          var store = '';
        }else{
          var store = self.selectedStore.id;
        }

        if(self.selectedRewardWheel === null){
          var reward_wheel = '';
        }else{
          var reward_wheel = self.selectedRewardWheel.id;
        }

        self.report = [];
        var request = {
          from_date: from_date,
          store: store,
          reward_wheel: reward_wheel,
          consumer: self.consumer,
          email: self.email,
          phone_no: self.phone_no,
        };
        if (self.generateExport == false) {
          self.loading = true;
        }
        self.loadDT(request);
      },
      loadDT: function (request) {
        var self = this;
        $("#rewardWheelReportTable").DataTable({
            pagingType: "simple_numbers",
            processing: true,
            serverSide: true,
            destroy: true,
            searching: false,
            "lengthChange": false,
            "pageLength": 25,
          columnDefs: [
            { orderable: false, targets: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9,10] },
            { className: "dt-left", targets: [0, 1, 2, 3, 4 , 8, 9] },
            { className: "dt-right", targets: [5, 6, 7] },
          ],
          order: [[3,"desc"]],
          orderClasses: false,
          language: {
            processing:
              '<i class="fa fa-spinner fa-spin fa-3x fa-fw"></i><span class="sr-only"></span> ',
            emptyTable: "No Report Available.",
            oPaginate: {
                sNext: '<i class="fas fa-angle-double-right"></i>',
                sPrevious: '<i class="fas fa-angle-double-left"></i>',
              },
              sLengthMenu:
                "<label class='label_dropdown_dt'>Per page</label> _MENU_",
          },
          ajax: {
            headers: {
              Authorization: "Bearer " + localStorage.getItem("token"),
            },
            url:  "/api/getrewardspinreport",
            type: "POST",
            data: {
              _token: "{{csrf_token()}}",
              from_date: request.from_date,
              store: request.store,
              reward_wheel: request.reward_wheel,
              consumer: request.consumer,
              phone_no: request.phone_no,
              email: request.email,
            },
            dataType: "json",
            dataSrc: function (result) {
              self.showReloadBtn = false;
              self.allRewardWheelReports = result.data;
              self.total_spins_earned = result.total_spins_earned;
              self.total_spins_spent = result.total_spins_spent;
              self.total_amount_earned_via_points = result.total_amount_earned_via_points;
              self.total_amount_spent_with_points = result.total_amount_spent_with_points;
              if (self.generateExport == false) {
                self.loading = false;
              }
              return self.allRewardWheelReports;
            },
            error: function(data){
              error(commonConstants.datatable_error);
              $('#rewardWheelReportTable_processing').hide();
              self.loading = false;
              self.showReloadBtn = true;
            }
          },
          columns: [
            { data: "consumer_name" },
            { data: "email" },
            { data: "phone" },
            {
              render: function (data, type, full, meta) {
                return full.used_at;
              },
            },
            {
              render: function (data, type, full, meta) {
                return full.winning_details ? full.winning_details.charAt(0).toUpperCase() + full.winning_details.slice(1) : '';
              },
            },
            { data: "reward_point" },
            { data: "reward_amount" },
            { data: "transaction_amount" },
            { data: "retailer" },
            { data: "reward_wheel" },
            { data: "is_earned_spin" }
          ],
        });
      },
      exportReport() {
        var self = this;
        self.generateExport = true;
        self.loading = true;
        if($("#start-date").val() == ''){
            error("Please select a date to generate the report.");
            return false;
        }
        if (
          moment($("#start-date").val()).format("YYYY-MM-DD") >
            moment().format("YYYY-MM-DD") &&
          $("#start-date").val() != ""
        ) {
          error("Start date cannot be from future.");
          return false;
        }
        var from_date = moment($("#start-date").val()).format("YYYY-MM-DD");

        if(self.selectedStore === null){
          var store = '';
        }else{
          var store = self.selectedStore.id;
        }

        if(self.selectedRewardWheel === null){
          var reward_wheel = '';
        }else{
          var reward_wheel = self.selectedRewardWheel.id;
        }

        var request = {
          from_date: from_date,
          store: store,
          reward_wheel: reward_wheel,
          consumer: self.consumer,
          email: self.email,
          phone_no: self.phone_no,
        };

        rewardwheelapi
          .getRewardSpinReportExport(request)
          .then(function (response) {
            self.generateExport = false;
            self.loading = false;
            var FileSaver = require("file-saver");
            var blob = new Blob([response], {
              type: "application/xlsx",
            });
            FileSaver.saveAs(
              blob,
              moment().format("MM/DD/YYYY") + "_canpay_points_report_export.xlsx"
            );
          })
          .catch(function (error) {
            // error(error);
            self.generateExport = false;
            self.loading = false;
          });
      },
      reset(){
        var self = this;
        self.consumer = "";
        self.phone_no = "";
        self.email = "";
        self.selectedStore = null;
        self.selectedRewardWheel = null;
      }
    },
    mounted() {
      var self = this;
      $("#start-date").datepicker({
        format: "mm/dd/yyyy",
        autoclose: true,
        todayHighlight: true,
      }).datepicker("setDate", new Date());


      self.getActiveRewardwheels()
    },
  };
  </script>
