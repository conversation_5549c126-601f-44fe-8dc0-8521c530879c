<template>
<div>
  <div v-if="loading">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Remote Pay Transaction Report</h3>
                  <b-button
                  class="btn-danger export-api-btn"
                  @click="reloadDatatable"
                  v-if="showReloadBtn"
                  >
                    <i class="fas fa-redo"></i> Reload
                  </b-button>
                </div>

                <div class="card-body">
                  <div class="row">
                    <div class="col-md-4">
                      <div class="form-group">
                        <input
                          autocomplete="off"
                          class="start-date form-control"
                          placeholder="Start Date"
                          id="start-date"
                          onkeydown="return false"
                        />
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group">
                        <input
                          autocomplete="off"
                          class="end-date form-control"
                          placeholder="End Date"
                          id="end-date"
                          onkeydown="return false"
                        />
                      </div>
                    </div>

                    <div class="col-md-4">
                      <div class="form-group">
                        <input
                          autocomplete="off"
                          class="form-control"
                          placeholder="Consumer Name (Min 3 chars)"
                          id="consumer"
                          v-model="consumer"
                        />
                      </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                        <multiselect
                            id="store"
                            v-model="selectedStore"
                            placeholder="Select Store (Min 3 chars)"
                            label="retailer"
                            :options="storelist"
                            :loading="isLoading"
                            :internal-search="false"
                            v-validate="'required'"
                            @search-change="getAllStores"
                        ></multiselect>
                        </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group">
                        <label class="switch"><input class="enable-employee-login" type="checkbox" v-model="toggle_settlement_date"><span class="slider round"></span></label> Settlement Date
                      </div>
                    </div>
                  </div>
                  <div class="row">
                  <div class="col-md-4">
                      <button
                        type="button"
                        class="btn btn-success"
                        @click="generateReport(false)"
                        id="generateBtn"
                      >
                        Generate</button
                      ><button
                        type="button"
                        @click="generateReport(true)"
                        class="btn btn-danger margin-left-5"
                      >
                        Export
                        <i class="fa fa-download" aria-hidden="true"></i>
                      </button>
                      <button
                        type="button"
                        @click="reset()"
                        class="btn btn-success margin-left-5"
                      >
                        Reset
                      </button>
                  </div>
                </div>
                </div>
                <div class="card-footer"></div>
                <!-- /.card-header -->
                <div class="card-body">
                  <div class="row">
                    <div class="col-12">
                      <table
                        id="transactionTable"
                        class="table"
                        style="width: 100%; white-space: normal"
                      >
                        <thead>
                          <tr>
                            <th>Transaction Number</th>
                            <th>Transaction Date</th>
                            <th>Modification Date (UTC)</th>
                            <th>Settlement Date</th>
                            <th width="12%">Amount ($)</th>
                            <th width="12%">Tip Amount ($)</th>
                            <th width="12%">Delivery Fee ($)</th>
                            <th>Store</th>
                            <th>Consumer</th>
                            <th>Status</th>
                          </tr>
                        </thead>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>

    <!-- Transaction modification history modal start -->
    <b-modal
      id="modified-tr-history-modal"
      ref="modified-tr-history-modal"
      ok-only
      cancel-variant="outline-secondary"
      :header-text-variant="headerTextVariant"
      :title="historyModalTitle"
      :no-close-on-esc="true"
      :no-close-on-backdrop="true"
    >
      <div class="row">
          <div class="col-md-12">
            <b-table-simple
            class="cp-table"
            responsive
            show-empty
            bordered
            >
                <b-thead head-variant="light">
                    <tr>
                        <th width="15%">Amount ($)</th>
                        <th width="20%">Tip Amount ($)</th>
                        <th width="20%">Delivery Fee ($)</th>
                        <th width="20%">Modify Time</th>
                        <th width="20%">Reason</th>
                        <th width="20%">Additional Reason</th>
                        <th width="15%">Status</th>
                    </tr>
                </b-thead>
                <b-tbody v-for="(row, index) in transactionHistory" :key="index">
                   <b-tr>
                          <b-td class="text-left text-gray">${{
                            row.amount
                          }}</b-td>
                          <b-td class="text-left text-gray">${{
                            row.tip_amount
                          }}</b-td>
                          <b-td class="text-left text-gray">${{
                            row.delivery_fee
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.local_transaction_time
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.reason
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.additional_reason
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.status
                          }}</b-td>
                        </b-tr>
                </b-tbody>
            </b-table-simple>
          </div>
        </div>
    </b-modal>
    <!-- Transaction modification history modal end -->
  </div>
</div>
</template>
<script>
import api from "@/api/transaction.js";
import moment from "moment";
import { validationMixin } from "vuelidate";
import { required, minLength } from "vuelidate/lib/validators";
import { HourGlass } from "vue-loading-spinner";
import commonConstants from "@/common/constant.js";
import CanPayLoader from "./CustomLoader/CanPayLoader.vue"
export default {
  mixins: [validationMixin],
  data() {
    return {
      allTransactionModel: {},
      showMsg: false,
      transaction_id: null,
      comment: "",
      currentUser: localStorage.getItem("user")
        ? JSON.parse(localStorage.getItem("user"))
        : null,
      loading: false,
      report: [],
      generateExport: false,
      showReloadBtn:false,
      constants: commonConstants,
      consumer:"",
      storelist: [],
      transactionHistory: [],
      selectedStore: null,
      isLoading: false,
      toggle_settlement_date: false,
      headerTextVariant: "light",
      historyModalTitle: "Transaction history",
    };
  },
  components: {
    HourGlass,
    CanPayLoader
  },
  created() {
    this.trLeaveComment();
  },
  methods: {
    //API call to fetch All stores
    getAllStores(searchtxt) {
      var self = this;
      if(searchtxt.length >= 3){
        self.isLoading = true;
        var request = {
          searchtxt: searchtxt,
          is_ecommerce: 1,
        };
      api
        .getStores(request)
        .then(function (response) {
          if (response.code == 200) {
            self.storelist = response.data;
            self.isLoading = false;
          }else {
            error(response.message);
            self.isLoading = false;
          }
        })
        .catch(function (error) {
        });
      }
    },
    reloadDatatable(){
      var self = this;
      self.loadDT();
    },
    // API call to generate the all the Transactions Report
    generateReport(is_export = false) {
      var self = this;
      if (
        moment($("#start-date").val()).format("YYYY-MM-DD") >
          moment().format("YYYY-MM-DD") &&
        $("#start-date").val() != ""
      ) {
        error("Start date cannot be from future.");
        return false;
      }
      if (
        moment($("#end-date").val()).format("YYYY-MM-DD") >
          moment().format("YYYY-MM-DD") &&
        $("#end-date").val() != ""
      ) {
        error("End date cannot be from future.");
        return false;
      }
      if ($("#start-date").val() != "") {
        var from_date = moment($("#start-date").val()).format("YYYY-MM-DD");
      } else {
        error("Select Start date.");
        return false;
      }
      if ($("#end-date").val() != "") {
        var to_date = moment($("#end-date").val()).format("YYYY-MM-DD");
      } else {
        error("Select End date.");
        return false;
      }
      if(from_date!='' && to_date!=''){
        if(from_date > to_date){
          error("To date cannot be greater than From date");
          return false;
        }
        //calculate the date Difference
        var date1 = new Date(from_date);
        var date2 = new Date(to_date);
        // To calculate the time difference of two dates
        var Difference_In_Time = date2.getTime() - date1.getTime();

        // To calculate the no. of days between two dates
        var Difference_In_Days = Difference_In_Time / (1000 * 3600 * 24);

        if(Difference_In_Days > 7){
          error('The date difference cannot be greater than 7 days.');
          return false;
        }
      }
      if(self.selectedStore === null){
        var store_id = '';
      }else{
        var store_id = self.selectedStore.id;
      }
      self.report = [];
      var request = {
        from_date: from_date,
        to_date: to_date,
        store_id: store_id,
        consumer: self.consumer,
        is_remote_pay_report: 1,
        search_by_scheduled_posting_date: self.toggle_settlement_date ? 1 : 0,
      };
      is_export ? self.exportTransaction(request) : self.loadDT(request);
    },
    loadDT: function (request) {
      var self = this;
      $("#transactionTable").DataTable({
        searching:false,
        pagingType: "simple_numbers",
        processing: true,
        serverSide: true,
        destroy: true,
        ordering: false,
        columnDefs: [
          { className: "dt-left", targets: [0, 1, 2, 3, 4, 5, 6, 7, 8] },
        ],
        orderClasses: false,
        language: {
          processing:
            '<i class="fa fa-spinner fa-spin fa-3x fa-fw"></i><span class="sr-only"></span> ',
          emptyTable: "No Transactions Available.",
          search: "_INPUT_",
          searchPlaceholder: "Search records",
          oPaginate: {
            sNext: '<i class="fas fa-angle-double-right"></i>',
            sPrevious: '<i class="fas fa-angle-double-left"></i>',
          },
          sLengthMenu:
            "<label class='label_dropdown_dt'>Per page</label> _MENU_",
        },
        ajax: {
          headers: {
            Authorization: "Bearer " + localStorage.getItem("token"),
          },
          url: "/api/alltransactions",
          type: "POST",
          data: {
            _token: "{{csrf_token()}}",
            from_date: request.from_date,
            to_date: request.to_date,
            store_id: request.store_id,
            consumer: request.consumer,
            is_remote_pay_report: request.is_remote_pay_report,
            search_by_scheduled_posting_date: request.search_by_scheduled_posting_date,
          },
          dataType: "json",
          dataSrc: function (result) {
            self.showReloadBtn = false;
            self.allTransactionModel = result.data;
            if (self.generateExport == false) {
              self.loading = false;
            }
            return self.allTransactionModel;
          },
          error: function(data){
            error(commonConstants.datatable_error);
            $('#transactionTable_processing').hide();
            self.showReloadBtn = true;
          }
        },
        columns: [
          { data: "transaction_number" },
          { data: "transaction_time" },
          {
            render: function (data, type, full, meta) {
              var modification_time_length = full.modification_time.length;
              if (modification_time_length > 0) {
                var modification_time_str = full.modification_time[0];
                if (modification_time_length == 2) {
                  modification_time_str =modification_time_str + ', ' +full.modification_time[modification_time_length - 1];
                } else if (modification_time_length > 2) {
                  modification_time_str =modification_time_str + ' ... ' +full.modification_time[modification_time_length - 1]
                }
                return modification_time_str + (
                  ' <b-button data-tr-number="' +full.transaction_number +'" data-id="' + full.edit +'" class="viewModifiedTransaction custom-edit-btn" title="View All Modified Transaction" variant="outline-success"><i class="nav-icon fas fa-eye"></i></b-button>'
                );
              } else {
                return '';
              }
            },
          },
          { data: "scheduled_posting_date" },
          {
            render: function (data, type, full, meta) {
              if (full.attempt_count > 0) {
                var amount_str = '$' + full.amount + ' ... ';
                amount_str =amount_str + '$' +full.updated_amount;
                return amount_str;
              } else {
                return '$' + full.amount;
              }
            },
          },
          {
            render: function (data, type, full, meta) {
              return '$' + full.last_approve_tip_amount;
            },
            },
          { data: "delivery_fee" },
          { data: "store_name" },
          { data: "consumer_name" },
          { data: "status" },
        ],
      });

      $("#transactionTable").on("page.dt", function () {
        $("html, body").animate({ scrollTop: 0 }, "slow");
        $("th:first-child").focus();
      });

      //Search in the table only after 3 characters are typed
      // Call datatables, and return the API to the variable for use in our code
      // Binds datatables to all elements with a class of datatable
      var dtable = $("#transactionTable").dataTable().api();

      // Grab the datatables input box and alter how it is bound to events
      $(".dataTables_filter input")
        .unbind() // Unbind previous default bindings
        .bind("input", function (e) {
          // Bind our desired behavior
          // If the length is 3 or more characters, or the user pressed ENTER, search
          if (this.value.length >= 3 || e.keyCode == 13) {
            // Call the API search function
            dtable.search(this.value).draw();
          }
          // Ensure we clear the search if they backspace far enough
          if (this.value == "") {
            dtable.search("").draw();
          }
          return;
        });
    },

    modifiedTransactionHistory(){
      var self = this;
      self.loading = true;
      var request = {
        transaction_id: self.transaction_id,
      };
      api
        .modifiedTransactionHistory(request)
        .then((response) => {
          if ((response.code = 200)) {
            self.transactionHistory = response.data;
            self.$bvModal.show("modified-tr-history-modal");
            success(response.message);
          } else {
            error(response.message);
          }
          self.loading = false;
        })
        .catch((err) => {
          self.loading = false;
          error(err);
        });
    },
    trLeaveComment() {
      var self = this;
      // modify transaction history
      $(document).on("click", ".viewModifiedTransaction", function (e) {
        //open the modal
        self.transactionHistory = [];
        self.transaction_id = $(e.currentTarget).attr("data-id");
        self.historyModalTitle = "Transaction history for " + $(e.currentTarget).attr("data-tr-number");
        self.modifiedTransactionHistory();
      });
    },
    exportTransaction(request) {
      var self = this;
      self.generateExport = true;
      self.loading = true;

      api
        .getalltransactionexport(request)
        .then(function (response) {
          self.generateExport = false;
          self.loading = false;
          var FileSaver = require("file-saver");
          var blob = new Blob([response], {
            type: "application/xlsx",
          });
          FileSaver.saveAs(
            blob,
            moment().format("MM/DD/YYYY") + "_all_remote_pay_transaction_report_export.xlsx"
          );
        })
        .catch(function (error) {
          // error(error);
          self.generateExport = false;
          self.loading = false;
        });
    },
    reset(){
      var self = this;
      self.consumer = "";
      self.selectedStore = null;
      self.toggle_settlement_date = false;
    }
  },
  mounted() {
    var self = this;
    $("#start-date").datepicker({
      format: "mm/dd/yyyy",
      autoclose: true,
      todayHighlight: true,
    });
    $("#end-date").datepicker({
      format: "mm/dd/yyyy",
      autoclose: true,
      todayHighlight: true,
    });
    $("#start-date , #end-date").datepicker("setDate", new Date());
  },
};
</script>
