<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Exception;

class MicrobiltReportController extends Controller
{

    /**
     * searchForRestrictedConsumer
     * Search the restricted consumer in microbilt.
     * @param  mixed $request
     * @return void
     */

    public function searchForRestrictedConsumer(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . " Automatic search started for restricted consumer in microbilt.");
        //Search with in Registered Consumers
        $this->validate($request,[
            'start_date' => VALIDATION_REQUIRED,
            'end_date' => VALIDATION_REQUIRED
        ]);

        $differenceInDays = getDifferentInDays($request->start_date, $request->end_date);

        if($differenceInDays<0 || $differenceInDays>7){

            throw new Exception("Maximum accepted date range is of 7 days");

        }

        $registeredConsumers = $this->_getRestrictedConsumer($request);

        $message = trans('message.consumer_search_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . " Automatic search started for restricted consumer in microbilt is Complete");
        return renderResponse(SUCCESS, $message, $registeredConsumers);

    }

    /**
     * _getRestrictedConsumer
     * Fetch the restricted Consumers in microbilt
     * @param  mixed $searchStr
     * @return void
     */
    private function _getRestrictedConsumer($request)
    {
        $start_date = $request->start_date . " 00:00:00";
        $end_date = $request->end_date . " 23:59:59";

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Restricited consumer Global Search Started.");

        $resctricted_consumer = getStatus(RESTRICTED_USER);

        //Fetch Registered Consumers
        $sql = 'SELECT
        u.*,mrmh.*, CONCAT("xxxx", SUBSTRING(mrmh.account_no, -4)) AS account_number,
        u.user_id AS consumer_id,
        sm.status AS status_code
        FROM
        users u
        JOIN
        microbilt_rule_match_history AS mrmh ON u.user_id = mrmh.consumer_id AND mrmh.is_final_outcome =1
        LEFT JOIN status_master AS sm ON sm.id = u.status
        WHERE
        u.status = ? AND u.updated_at >= ? AND u.updated_at <= ?
        GROUP BY u.user_id order by u.updated_at desc
        ';

        $searchStr = [$resctricted_consumer,$start_date,$end_date];

        $registeredConsumers = DB::connection(MYSQL_RO)->Select($sql, $searchStr);
        $consumerArr = [];
        if (!empty($registeredConsumers)) {
            foreach ($registeredConsumers as $registeredConsumer) {
                $data = [];
                $data["user_id"] = $registeredConsumer->consumer_id;
                $data['name'] = $registeredConsumer->first_name.' '.$registeredConsumer->last_name;
                $data['email'] = $registeredConsumer->email;
                $data['phone'] = $registeredConsumer->phone;
                $data['routing_number'] = $registeredConsumer->routing_no;
                $data['account_number'] = $registeredConsumer->account_number;
                $data['bank_link_type'] = $registeredConsumer->bank_link_type == DIRECT_BANK_LINK ? DIRECT_LINK : MANUAL_LINK;
                $data['address'] = $registeredConsumer->street_address . " , " . $registeredConsumer->city . " , " . $registeredConsumer->zipcode;
                $data["status"] = $registeredConsumer->status_code;
                array_push($consumerArr,$data);
            }
        } else {
            $consumerArr = [];
        }

        return $consumerArr;
    }

    /**
     * updateReviewStatus
     * Update status of user
     * @param  mixed $searchArray
     * @return void
     */

    public function updateReviewStatus(Request $request)
    {
        $this->validate($request, [
            'user_id' => VALIDATION_REQUIRED,
            'code' => VALIDATION_REQUIRED
        ]);

        $code_id = getStatus($request->code);
        $userId = $request->user_id;

        DB::connection(MYSQL_RO)->table('users')
        ->where('user_id', $userId)
        ->update(['status' => $code_id]);
        // save status update history
        saveStatusHistory(STATUS_UPDATED_BY_ADMIN, MICROBILT_BANK_REVIEW, $code_id, $userId);

        $message = trans('message.user_updation_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . " Status updated for restricted Consumer");
        return renderResponse(SUCCESS, $message,'');
    }

    /**
     * updateReviewStatus
     * Update status of user
     * @param  mixed $searchArray
     * @return void
     */

    public function getRequiredConsumerDetails(Request $request)
    {

        $this->validate($request, [
            'user_id' => VALIDATION_REQUIRED,
            'phone' => VALIDATION_REQUIRED
        ]);
        $phone = $request->phone;
        $user_id = $request->user_id;

        $sql ="SELECT ubai.routing_no,
            CONCAT('xxxx', SUBSTRING(ubai.account_no, -4)) AS account_number,
        sm.STATUS,
        ubai.account_type,
        ubai.id as bank_id,
        ubai.user_id
        FROM user_bank_account_info ubai
        LEFT JOIN status_master sm
        ON ubai.status = sm.id
        WHERE ubai.user_id = ?
        ";
        $bankDetails = DB::connection(MYSQL_RO)->Select($sql, [$user_id]);
        $message = trans('message.user_success');

        $sql = "
            SELECT * FROM cognito_rules_log
            WHERE phone=?
            ORDER BY created_at DESC
            limit 1
        ";

        $incognito = DB::connection(MYSQL_RO)->Select($sql, [$phone]);
        $outcome_ssnrequired = '';
        if (count($incognito) > 0) {

            $incognito = $incognito[0]->response;

            $incognito = json_decode($incognito);
            $outcome_ssnrequired = isset($incognito->outcome_ssnrequired) ? $incognito->outcome_ssnrequired : '';
        }

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . " List of Banks for Consumers");

        return renderResponse(SUCCESS, $message, ["bankDetails"=>$bankDetails,"outcome_ssnrequired"=>$outcome_ssnrequired]);

    }

    /**
     * searchForOtherUserWithSameAccount
     * search for the users who holds the same bank account
     * @param  mixed $searchCriteria
     * @return void
     */
    public function searchForOtherUserWithSameAccount(Request $request)
    {
        $this->validate($request, [

            'user_id' => VALIDATION_REQUIRED,
            'bank_id' => VALIDATION_REQUIRED

        ]);

        $user_id = $request->user_id;
        $bank_id = $request->bank_id;

        $sql = "
            SELECT account_no,routing_no
            FROM user_bank_account_info
            WHERE id=?
        ";

        $bankDetails = DB::connection(MYSQL_RO)->Select($sql, [$bank_id]);

        $account_no = $bankDetails[0]->account_no;
        $routing_no = $bankDetails[0]->routing_no;

        $sql = "
            SELECT ubai.user_id, u1.first_name, u1.last_name, u1.email, u1.phone, u1.created_at
            FROM user_bank_account_info ubai
            LEFT JOIN users u1
            ON u1.user_id=ubai.user_id
            WHERE ubai.account_no=? AND ubai.routing_no=? AND u1.user_id!=?
            ORDER BY u1.created_at DESC
            LIMIT 10
        ";

        $userFound = DB::connection(MYSQL_RO)->Select($sql, [$account_no, $routing_no, $user_id]);

        $message = trans('message.user_success');

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . " Initialted search for other user with same bank account or account number");

        return renderResponse(SUCCESS, $message, $userFound);
    }
    /**
     * getReportOfConsumerDeclinedForBankValidation
     * fetch the report of consumer declined for microbilt bank validation
     * @param  mixed $searchArray
     * @return void
     */

    public function getReportOfConsumerDeclinedForBankValidation(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . " Report fetch started for consumer declined during microbilt bank validation...");
        $this->validate($request, [
            'from_date' => VALIDATION_REQUIRED,
            'to_date' => VALIDATION_REQUIRED,
            'currentPage' => VALIDATION_REQUIRED,
            'perPage' => VALIDATION_REQUIRED
        ]);
        $page = $request['currentPage']; // Get the current page from the request
        $perPage = $request['perPage']; // Set the number of items per page
        $offset = ($page - 1) * $perPage; // Calculate the offset
        // pending status
        $pending_status_id = getStatus(PENDING);
        // voided status
        $voided_status_id = getStatus(VOIDED);
        // returned status
        $returned_status_id = getStatus(RETURNED);
        // main query
        $sql = "SELECT
        CASE
            WHEN mrmh.consumer_id IS NOT NULL THEN CONCAT_WS(' ', COALESCE(u.first_name, ''), COALESCE(u.middle_name, ''), COALESCE(u.last_name, ''))
            WHEN rsd.id != '' THEN CONCAT_WS(' ', COALESCE(rsd.first_name, ''), COALESCE(rsd.middle_name, ''), COALESCE(rsd.last_name, ''))
            ELSE CONCAT_WS(' ', COALESCE(rosd.first_name, ''), COALESCE(rosd.middle_name, ''), COALESCE(rosd.last_name, ''))
        END AS consumer_name,
        CASE
            WHEN mrmh.consumer_id IS NOT NULL THEN CONCAT_WS(' ', COALESCE(u.street_address, ''), COALESCE(u.city, ''), COALESCE(u.state, ''), COALESCE(u.zipcode, ''))
            WHEN rsd.id != '' THEN CONCAT_WS(' ', COALESCE(rsd.street_address, ''), COALESCE(rsd.city, ''), COALESCE(rsd.state, ''), COALESCE(rsd.zipcode, ''))
            ELSE CONCAT_WS(' ', COALESCE(rosd.street_address, ''), COALESCE(rosd.city, ''), COALESCE(rosd.state, ''), COALESCE(rosd.zipcode, ''))
        END AS address,
        mrmh.phone, mr.source, IF(mrmh.bank_link_type = '".MANUAL_BANK_LINK."', '".MANUAL_LINK."', '".DIRECT_LINK."') AS bank_link_type, mrmh.account_no, mrmh.routing_no,
        COUNT(td.id) AS total_transactions, SUM(IF((td.status_id = ? || td.status_id = ?) && td.return_reason IS NOT NULL, 1, 0)) AS return_transactions
        FROM microbilt_rule_match_history mrmh
        JOIN microbilt_rules mr ON mr.id = mrmh.rule_id
        LEFT JOIN registration_session_details rsd ON rsd.id = mrmh.registration_session_id
        LEFT JOIN consumer_onboarding_session_details rosd ON rosd.id = mrmh.registration_session_id
        LEFT JOIN users u ON u.user_id = mrmh.consumer_id
        LEFT JOIN transaction_details AS td ON td.consumer_id = mrmh.consumer_id
        AND mrmh.consumer_id IS NOT NULL AND (td.transaction_ref_no IS NULL)
        AND td.status_id != ?
        WHERE (mr.matching_decision_code = '".MICROBILT_OUTCOME_TWO."' || (mr.matching_decision_code = '".MICROBILT_OUTCOME_THREE."' && mrmh.canpay_positive_transaction = 0) || (mr.matching_decision_code = '".MICROBILT_OUTCOME_FOUR."' && mrmh.bank_link_type = 0))
        AND (DATE(mrmh.created_at) BETWEEN ? AND ?)
        AND mrmh.is_final_outcome = 1
        AND mrmh.parent_batch_id IS NULL
        GROUP BY mrmh.phone
        ORDER BY mrmh.created_at DESC";
        $searchArray = [$returned_status_id, $pending_status_id, $voided_status_id, $request->from_date, $request->to_date];
        $totalCount = count(DB::select($sql, $searchArray));
        $sql .= " LIMIT ? OFFSET ?";
        array_push($searchArray, $perPage, $offset);
        $result = DB::select($sql, $searchArray);
        $data = [
            'result' => $result,
            'current_page' => $page,
            'per_page' => $perPage,
            'total' => $totalCount,
            'total_pages' => ceil($totalCount / $perPage),
        ];
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . " Report fetched successfully for consumer declined during microbilt bank validation");
        return renderResponse(SUCCESS, null, $data);
    }
}
