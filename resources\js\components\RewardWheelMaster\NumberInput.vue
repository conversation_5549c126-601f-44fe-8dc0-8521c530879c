<template>
    <div class="number-input">
        <input
        v-model="numberValue"
        type="text"
        class="form-control"
        placeholder="Enter value"
        v-validate="'required'"
        />
    </div>
</template>

<script>
export default {
    props: {
        value: "",
        type: "",
        toFixed: {
            type: Number,
            default: 2
        },
        lengthType: "",
        min: {
            type: Number,
            default: null
        },
        max: {
            type: Number,
            default: null
        },
        allow_zero:{
            type:Boolean,
            default:false
        }
    },
    data(){
        return{
            numberValue: null
        }
    },
    mounted(){
        if(this.value != 0){
            if(this.type == 'decimal'){
                this.numberValue = parseInt(this.value)
            }else{
                this.numberValue = parseFloat(this.value).toFixed(this.toFixed)
                this.numberValue  = this.numberValue.replace(/(?<=\d)0*$/, "");
            }
        }else{
            if(this.allow_zero == true){
                this.numberValue = 0;
            }else this.numberValue = ''
        }
        this.$emit("input", this.numberValue);
    },
    watch: {
        value: function(val){
            this.numberValue = this.value
        },
        numberValue:function(val){
            if(val){
                if(this.type == 'decimal'){

                    var value = this.numberValue.toString()
                    var reg1 = /^0/gi;
                    var reg2 = /[ `!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]/
                    if (value.match(reg1)) {
                        this.numberValue  = value.replace(reg1, '');
                        this.$emit("input", this.numberValue);
                    }
                    if (value.match(reg2)) {
                        this.numberValue  = value.replace(reg2, '');
                        this.$emit("input", this.numberValue);
                    }
                    this.numberValue  = this.numberValue.toString().replace('.', '');
                    this.numberValue  = this.numberValue.toString().replace(/[a-zA-Z]/g, '');

                    if(this.lengthType && this.lengthType == 'jackpot'){
                        if(this.numberValue.match(/\./g) == null && this.numberValue.length > 8){
                            this.numberValue  = this.numberValue.toString().slice(0, -1);
                        }
                    }
                    if(this.value == 0 && this.allow_zero){
                        this.numberValue = 0;
                    }
                    this.$emit("input", value.toString().replace('.', ''));
                }else{

                    var value = this.numberValue.toString()
                    var reg1 = /^0/gi;
                    var reg2 = /[ `!@#$%^&*()_+\-=\[\]{};':"\\|,<>\/?~]/
                    if (value.match(reg1)) {
                        this.numberValue  = value.replace(reg1, '');
                        this.$emit("input", this.numberValue);
                    }
                    if (value.match(reg2)) {
                        this.numberValue  = value.replace(reg2, '');
                        this.$emit("input", this.numberValue);
                    }
                    this.numberValue  = this.numberValue.toString().replace(/[a-zA-Z]/g, '');
                    if(this.numberValue.toString().split('.')[1] && this.numberValue.toString().split('.')[1].length > this.toFixed){
                        this.numberValue  = this.numberValue.toString().slice(0, -1);
                    }
                    if(this.numberValue.match(/\./g) && this.numberValue.match(/\./g).length > 1){
                        this.numberValue  = this.numberValue.toString().slice(0, -1);
                    }
                    if(this.lengthType && this.lengthType == 'multiplier'){
                        if(this.numberValue.match(/\./g) == null && this.numberValue.length > 10){
                            this.numberValue  = this.numberValue.toString().slice(0, -1);
                        }
                    }else{
                        if(this.numberValue.match(/\./g) == null && this.numberValue.length > 3){
                            this.numberValue  = this.numberValue.toString().slice(0, -1);
                        }
                    }
                    
                    this.$emit("input", this.numberValue);
                }
            }else{
                this.$emit("input", val);
            }
        }
    }
}
</script>