<?php

namespace App\Http\Factories\Transaction;

/**
 *
 * @package App\Http\Factories\Transaction
 */
interface TransactionInterface
{

    public function createConsumerTransaction($params);

    public function getAcheckBody($params);

    public function sandboxTransactionForVendors($params);

    public function validateBankAccount($params);

    public function voidV1Transaction($params);

    public function voidV2Transaction($transaction, $data, $requestdata);
}
