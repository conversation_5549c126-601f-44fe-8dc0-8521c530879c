<template>
<div>
  <div v-if="loading">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">HelpDesk User</h3>
                </div>

                <!-- /.card-header -->
                <div class="card-body">
                  <div class="row">
                  <div class="col-md-4">
                    <div class="form-group">
                     <input
                        class="form-control"
                        placeholder="Name (min 3 chars)"
                        id="consumer"
                        v-model="consumer"
                      />
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="form-control"
                        placeholder="Email (Exact)"
                        id="email"
                        v-model="email"
                      />
                    </div>
                  </div>
                </div>
                </div>
                  <div class="card-footer">
                    <button
                      type="button"
                      class="btn btn-success"
                      @click="searchHelpdeskConsumers()"
                    >
                      Search
                    </button>
                    <button
                      type="button"
                      @click="reset()"
                      class="btn btn-success margin-left-5"
                    >
                      Reset
                    </button>
                    <b-button
                        class="btn btn-success margin-left-5"
                        @click="openModal('add')"
                    >
                        <i class="fas fa-user-plus"></i> Add HelpDesk User 
              </b-button>
                  </div>
                <div class="card-body">
                  <b-table-simple
                      responsive
                      show-empty
                      bordered
                      sticky-header="800px"
                      v-if="allUserModel.length > 0"
                    >
                      <b-thead head-variant="light">
                        <tr>
                          <th>Name</th>
                            <th>Email</th>
                            <th>User Type</th>
                            <th class="text-center">Created On</th>
                            <th class="text-center">Status</th>
                            <th class="text-center">Action(s)</th>
                        </tr>
                      </b-thead>
                      <b-tbody v-for="(row, index) in allUserModel" :key="index">
                        <b-tr>
                          <b-td class="text-left text-gray">{{
                            row.name
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.email
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.role_name
                          }}</b-td>
                          <b-td class="text-center text-gray">{{
                            row.created_at
                          }}</b-td>
                          <b-td class="text-center text-gray">{{
                            row.status_name
                          }}</b-td>
                          <b-td class="text-center text-gray">
                            <a :data-user-id="row.edit" class="editHelpdeskUser custom-edit-btn" title="Edit User" variant="outline-success" style="border:none" v-if="row.role_name != 'Super Admin' && row.edit != currentUser.user_id"><i class="nav-icon fas fa-edit"></i></a>
                          </b-td>
                        </b-tr>
                      </b-tbody>
                    </b-table-simple>
                    <p v-else>No data displayed. Please refine your search criteria.</p>
                    </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
    <!-- CP Modal Start -->
    <b-modal
      id="user-modal"
      ref="modal"
      :header-text-variant="headerTextVariant"
      :title="modalTitle"
      @show="resetModal"
      @hidden="resetModal"
      ok-title="Save"
      ok-variant="success"
      cancel-variant="outline-secondary"
      @ok="handleOk"
      :no-close-on-esc="true"
      :no-close-on-backdrop="true"
    >
      <form ref="form" @submit.stop.prevent="save" class="needs-validation">
        <div class="row">
          <div class="col-md-12">
            <label for="user_type">
              User Type
              <span class="red">*</span>
            </label>
            <select
              class="form-control"
              id="user_type"
              name="user_type"
              v-model="userModel.role_id"
              v-validate="'required'"
              :disabled="isDisabled"
            >
              <option
                v-for="(roles, index) in userTypeArray"
                :key="index"
                :value="roles.role_id"
              >
                {{ roles.role_name }}
              </option>
            </select>
            <span v-show="errors.has('user_type')" class="text-danger">{{
              errors.first("user_type")
            }}</span>
          </div>
        </div>

        <div class="row">
          <div class="col-md-4">
            <label for="first_name">
              First Name
              <span class="red">*</span>
            </label>
            <input
              id="first_name"
              name="first_name"
              v-validate="'required|alpha'"
              type="text"
              v-model="userModel.first_name"
              class="form-control"
            />
            <span v-show="errors.has('first_name')" class="text-danger">{{
              errors.first("first_name")
            }}</span>
          </div>

          <div class="col-md-4">
            <label for="middle_name">Middle Name</label>
            <input
              id="middle_name"
              name="middle_name"
              type="text"
              v-validate="'alpha'"
              v-model="userModel.middle_name"
              class="form-control"
            />
            <span v-show="errors.has('middle_name')" class="text-danger">{{
              errors.first("middle_name")
            }}</span>
          </div>

          <div class="col-md-4">
            <label for="last_name">
              Last Name
              <span class="red">*</span>
            </label>
            <input
              id="last_name"
              name="last_name"
              v-validate="'required|alpha'"
              type="text"
              v-model="userModel.last_name"
              class="form-control"
            />
            <span v-show="errors.has('last_name')" class="text-danger">{{
              errors.first("last_name")
            }}</span>
          </div>
        </div>

        <div class="row">
          <div class="col-md-6">
            <label for="email">
              Email Address
              <span class="red">*</span>
            </label>
            <input
              id="email"
              name="email"
              v-validate="'required|email'"
              type="text"
              v-model="userModel.email"
              class="form-control"
            />
            <span v-show="errors.has('email')" class="text-danger">{{
              errors.first("email")
            }}</span>
          </div>
          <div class="col-md-6" v-if="isDisabled == true">
            <label for="user_type">
              Status
              <span class="red">*</span>
            </label>
            <select
              class="form-control"
              id="user_status"
              name="user_status"
              v-model="userModel.status"
              v-validate="'required'"
            >
              <option
                v-for="(status, index) in statusList"
                :key="index"
                :value="status.id"
              >
                {{ status.status }}
              </option>
            </select>
            <span v-show="errors.has('user_status')" class="text-danger">{{
              errors.first("user_status")
            }}</span>
          </div>
        </div>
        
      </form>
    </b-modal>
    <!-- CP Modal End -->
  </div>
</div>
</template>
<script>
import api from "@/api/user.js";
import { validationMixin } from "vuelidate";
import { required, minLength } from "vuelidate/lib/validators";
import commonConstants from "@/common/constant.js";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "../CustomLoader/CanPayLoader.vue"
export default {
  mixins: [validationMixin],
  data() {
    return {
      modalTitle: "",
      statusList: [],
      headerTextVariant: "light",
      userModel: {},
      allUserModel: {},
      userId: null,
      isDisabled: false,
      userTypeArray: {},
      currentUser: localStorage.getItem("user")
        ? JSON.parse(localStorage.getItem("user"))
        : null,
      showReloadBtn:false,
      constants: commonConstants,
      loading:false,
      consumer:"",
      email:"",
    };
  },
  components: {
    HourGlass,
    CanPayLoader
  },
  created() {
    this.editHelpdeskUser();
  },
  methods: {
    searchHelpdeskConsumers(){
      var self = this;
      if((self.consumer).trim().length < 3 &&  $("#email").val().trim() === ''){
        error("Please provide Name (Min 3 chars) or email(exact)");
        return false;
      }
      var request = {
        consumer: self.consumer,
        email:self.email,
      };
      self.loading = true;
      api
      .searchHelpdeskConsumers(request)
      .then(function (response) {
        if (response.code == 200) {
          self.allUserModel = response.data;
          self.loading = false;
        } else {
          error(response.message);
          self.loading = false;
        }
      })
      .catch(function (error) {
        // error(error);
        self.loading = false;
      });
    },
    editHelpdeskUser() {
      var self = this;
      $(document).on("click", ".editHelpdeskUser", function (e) {
        self.userId = $(e.currentTarget).attr("data-user-id");
        var userdetails = self.allUserModel.find((p) => p.edit == self.userId);
        self.modalTitle = "Edit User";
        self.$bvModal.show("user-modal");
        self.userModel = userdetails;
        self.userModel.user_id = userdetails.edit;
        self.isDisabled = true;
        //Fetch the status List
        self.getUserStatus();
      });
    },
    openModal(type) {
      var self = this;
      self.modalTitle = "Add User";
      self.$bvModal.show("user-modal");
    },
    resetModal() {
      var self = this;
      self.userModel = {};
      self.isDisabled = false;
      self.userId = null;
    },
    handleOk(bvModalEvt) {
      var self = this;
      // Prevent modal from closing
      bvModalEvt.preventDefault();
      // Trigger submit handler
      self.save();
    },
    save() {
      var self = this;
      // Exit when the form isn't valid
      this.$validator.validateAll().then((result) => {
        if (result) {
          //call to api to save the details
          if (!self.userModel.user_id) {
            // Add section
            api
              .addUser(self.userModel)
              .then((response) => {
                if (response.code == 200) {
                  success(response.message);
                  $("#helpdeskUsersTable").DataTable().ajax.reload(null, false);
                  self.$bvModal.hide("user-modal");
                  self.resetModal();
                } else {
                  error(response.message);
                }
              })
              .catch((err) => {
                error(err.response.data.message);
              });
          } else {
            // Edit Section
            api
              .editHelpdeskUser(self.userModel)
              .then((response) => {
                if (response.code == 200) {
                  success(response.message);
                  self.userId = null;
                  $("#helpdeskUsersTable").DataTable().ajax.reload(null, false);
                  self.$bvModal.hide("user-modal");
                  self.store_ids = null;
                  self.resetModal();
                } else {
                  error(response.message);
                }
              })
              .catch((err) => {
                error(err.response.data.message);
              });
          }
        }
      });
    },
    getHelpdeskUserRole: function () {
      var self = this;
      api
        .getHelpdeskUserRoles()
        .then((response) => {
          if (response.code == 200) {
            self.userTypeArray = response.data;
          } else {
            error(response.message);
          }
        })
        .catch((err) => {
          error(err.response.data.message);
        });
    },
    getUserStatus() {
      var self = this;
      api
        .getUserStatus()
        .then((response) => {
          if ((response.code = 200)) {
            self.statusList = response.data;
          } else {
            error(response.message);
          }
        })
        .catch((err) => {
          error(err);
        });
    },
    reset(){
      var self = this;
      self.consumer = "";
      self.email = "";
    }
  },
  mounted() {
    var self = this;
    
    self.getHelpdeskUserRole();
    document.title = "CanPay - HelpDesk User";
  },
};
</script>

