<?php

namespace App\Imports;

use App\Models\UserRole;
use App\Models\User;
use App\Models\StoreUserMap;
use App\Models\AccessLevelMaster;
use App\Models\StatusMaster;
use App\Models\MerchantStores;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use App\Http\Factories\EmailExecutor\EmailExecutorFactory;

class CorporateParentImport implements ToModel, WithHeadingRow, WithBatchInserts, WithChunkReading
{
    use Importable;
    private $rows = 0;
    private $duplicate_rows = 0;
    private $updated_rows = 0;

    public function __construct()
    {
        $this->emailexecutor = new EmailExecutorFactory();
    }

    /**
     * @param array $row
     * This function actully imports the data as row from Excel Sheet. Here we used the WithHeadingRow to get the Data with Heading. Do Not try to get the rows with index.
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row)
    {
        
        if(!empty($row['name']) && !empty($row['primary_contact_first_name']) && !empty($row['primary_contact_last_name']) && !empty($row['primary_contact_email']) && !empty($row['primary_contact_phone'])){
            //Validate Email Address
            if (filter_var($row['primary_contact_email'], FILTER_VALIDATE_EMAIL)) {
                //Validate Mobile Number
                if(preg_match('/^[0-9]{10}+$/', $row['primary_contact_phone'])){

                    //Validate Alphanumeric and spaces
                    if (preg_match('/^[a-zA-Z ]+$/', $row['name'])) {   

                        //Validate Alphanumeric for first name
                        if (preg_match('/^[a-zA-Z]+$/', $row['primary_contact_first_name'])) { 
                            //Validate Alphanumeric for last name
                            if(preg_match('/^[a-zA-Z]+$/', $row['primary_contact_last_name'])){
                                if($row['store_id']){
                                    $store = MerchantStores::where(['store_id' => $row['store_id']])->first();
        
                                    if(!empty($store)){
                                        // Check if corporate Parent already exists in Database
                                        $checkCorporateparentExists = User::where('contact_person_email', $row['primary_contact_email'])->first();
        
                                        if (empty($checkCorporateparentExists)) {
                                            // If not exists insert it into database
        
                                            // Insertion Started for Corporate Parent
                                            $role_details = UserRole::where('role_name', CORPORATE_PARENT)->first(); // Fetching Role ID with respect to User Type
                                            $user_details = Auth::user(); // Fetching Logged In User Details
                                            $password = substr(md5(microtime()), rand(0, 26), 7); // Generating Password
        
                                            //Get the Status
                                            $user_status = StatusMaster::where('code', USER_ACTIVE)->first();
        
                                            // Insertion begins in User Table
                                            $user = new User();
                                            $user->first_name = $row['name'];
                                            $user->password = Hash::make($password);
                                            $user->contact_person_first_name = $row['primary_contact_first_name'];
                                            $user->contact_person_last_name = $row['primary_contact_last_name'];
                                            $user->contact_person_email = $row['primary_contact_email'];
                                            $user->contact_person_phone = $row['primary_contact_phone'];
                                            $user->role_id = $role_details->role_id;
                                            $user->added_by = $user_details->user_id;
                                            $user->status = $user_status->id;
                                            $user->save();
        
                                            $user_id = $user->user_id;
        
                                            // Sending Mail with password
                                            $params = [
                                                'user_id' => $user_id,
                                                'password' => $password,
                                            ];
                                            $this->emailexecutor->corporateParentWelcomeEmail($params);
                                            
                                            ++$this->rows;
                                            Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Corporate Parent inserted for Email : " . $row['primary_contact_email']);
        
                                            // Mapping Creation started in Store User Map
                                            $checkStoreCorporateParent = StoreUserMap::join('users','users.user_id','=','store_user_map.user_id')->where(['store_user_map.store_id' => $store->id,'users.role_id' => $role_details->role_id])->first();
                                            if (empty($checkStoreCorporateParent)) {
                                                $access_level_details = AccessLevelMaster::where('label', ADMIN_RIGHT)->first();
                                                $storeusermap = new StoreUserMap();
                                                $storeusermap->store_id = $store->id;
                                                $storeusermap->user_id = $user_id;
                                                $storeusermap->user_access_level_id = $access_level_details->id;
                                                $storeusermap->save();
                                            } else {
                                                Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Nothing to Insert as the Data already exists for Corporate Parent Email : " . $row['primary_contact_email'] . " and Store ID : " . $row['store_id']);
                                                insertSkippedDataLog('Corporate Parent', 'contact_person_email', $row['primary_contact_email'], "Nothing to Insert as the Data already exists for Corporate Parent Email : " . $row['primary_contact_email'] . " and Store ID : " . $row['store_id'], json_encode($row), 'Corporate Parent excel');
                                            }
                                        }else{
                                            // If exists then continue adding stores with the existing user id
                                            $user_id = $checkCorporateparentExists->user_id;
        
                                            // Insertion Started for Corporate Parent
                                            $role_details = UserRole::where('role_name', CORPORATE_PARENT)->first(); // Fetching Role ID with respect to User Type
                                            $user_details = Auth::user(); // Fetching Logged In User Details
        
                                            //Update User Details
                                            $user = User::find($user_id);
                                            $user->first_name = $row['name'];
                                            $user->contact_person_first_name = $row['primary_contact_first_name'];
                                            $user->contact_person_last_name = $row['primary_contact_last_name'];
                                            $user->contact_person_phone = $row['primary_contact_phone'];
                                            $user->role_id = $role_details->role_id;
                                            $user->save();
                                            
                                            Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Corporate Parent updated for Email : " . $row['primary_contact_email']);
        
                                            // Mapping Creation started in Store User Map
                                            $checkStoreCorporateParent = StoreUserMap::join('users','users.user_id','=','store_user_map.user_id')->where(['store_user_map.store_id' => $store->id,'users.role_id' => $role_details->role_id])->first();
                                            if (empty($checkStoreCorporateParent)) {
                                                $access_level_details = AccessLevelMaster::where('label', ADMIN_RIGHT)->first();
                                                $storeusermap = new StoreUserMap();
                                                $storeusermap->store_id = $store->id;
                                                $storeusermap->user_id = $user_id;
                                                $storeusermap->user_access_level_id = $access_level_details->id;
                                                $storeusermap->save();
        
                                                ++$this->updated_rows;
                                            } else {
                                                ++$this->duplicate_rows;
                                                Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Nothing to Insert as the Data already exists for Corporate Parent Email : " . $row['primary_contact_email'] . " and Store ID : " . $row['store_id']);
                                                insertSkippedDataLog('Corporate Parent', 'contact_person_email', $row['primary_contact_email'], "Nothing to Insert as the Data already exists for Corporate Parent Email : " . $row['primary_contact_email'] . " and Store ID : " . $row['store_id'], json_encode($row), 'Corporate Parent excel');
                                            }
                                        }
                                    }else{
                                        Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Nothing to Insert as no store exists with the ID ".$row['store_id']);
                                        insertSkippedDataLog('Corporate Parent', 'contact_person_email', $row['primary_contact_email'], "Nothing to Insert as no store exists with the ID ".$row['store_id'], json_encode($row), 'Corporate Parent excel');
                                    }
                                }else{
                                    // Check if corporate Parent already exists in Database
                                    $checkCorporateparentExists = User::where('contact_person_email', $row['primary_contact_email'])->first();
                                    if (empty($checkCorporateparentExists)) {
                                        // If not exists insert it into database
                
                                        // Insertion Started for Corporate Parent
                                        $role_details = UserRole::where('role_name', CORPORATE_PARENT)->first(); // Fetching Role ID with respect to User Type
                                        $user_details = Auth::user(); // Fetching Logged In User Details
                                        $password = substr(md5(microtime()), rand(0, 26), 7); // Generating Password
                
                                        //Get the Status
                                        $user_status = StatusMaster::where('code', USER_ACTIVE)->first();
                
                                        // Insertion begins in User Table
                                        $user = new User();
                                        $user->first_name = $row['name'];
                                        $user->password = Hash::make($password);
                                        $user->contact_person_first_name = $row['primary_contact_first_name'];
                                        $user->contact_person_last_name = $row['primary_contact_last_name'];
                                        $user->contact_person_email = $row['primary_contact_email'];
                                        $user->contact_person_phone = $row['primary_contact_phone'];
                                        $user->role_id = $role_details->role_id;
                                        $user->added_by = $user_details->user_id;
                                        $user->status = $user_status->id;
                                        $user->save();
                
                                        $user_id = $user->user_id;
                
                                        // Sending Mail with password
                                        $params = [
                                            'user_id' => $user_id,
                                            'password' => $password,
                                        ];
                                        $this->emailexecutor->corporateParentWelcomeEmail($params);
                
                                        ++$this->rows;
                                        Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Corporate Parent inserted for Email : " . $row['primary_contact_email']);
                                    }else{
                                        // If exists then continue adding stores with the existing user id
                                        $user_id = $checkCorporateparentExists->user_id;
        
                                        // Insertion Started for Corporate Parent
                                        $role_details = UserRole::where('role_name', CORPORATE_PARENT)->first(); // Fetching Role ID with respect to User Type
                                        $user_details = Auth::user(); // Fetching Logged In User Details
        
                                        //Update User Details
                                        $user = User::find($user_id);
                                        $user->first_name = $row['name'];
                                        $user->contact_person_first_name = $row['primary_contact_first_name'];
                                        $user->contact_person_last_name = $row['primary_contact_last_name'];
                                        $user->contact_person_phone = $row['primary_contact_phone'];
                                        $user->role_id = $role_details->role_id;
                                        $user->save();
                
                                        ++$this->updated_rows;
                                        Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Corporate Parent updated for Email : " . $row['primary_contact_email']);
                                    }
                                }
                            }else{
                                Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Corporate Parent insertion skipped due to invalid last name (The Primary Contact Last Name field may only contain alphabetic characters).");
                                insertSkippedDataLog('Corporate Parent', 'contact_person_email', $row['primary_contact_email'], "Corporate Parent insertion skipped due to invalid last name (The Primary Contact Last Name field may only contain alphabetic characters) ".$row['primary_contact_email'], json_encode($row), 'Corporate Parent excel');
                            }
                        }else{
                            Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Corporate Parent insertion skipped due to invalid first name (The Primary Contact First Name field may only contain alphabetic characters).");
                            insertSkippedDataLog('Corporate Parent', 'contact_person_email', $row['primary_contact_email'], "Corporate Parent insertion skipped due to invalid first name (The Primary Contact First Name field may only contain alphabetic characters) ".$row['primary_contact_email'], json_encode($row), 'Corporate Parent excel');
                        }
                    }else{
                        Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Corporate Parent insertion skipped due to invalid name (The Name field may only contain alphabetic characters as well as spaces).");
                        insertSkippedDataLog('Corporate Parent', 'contact_person_email', $row['primary_contact_email'], "Corporate Parent insertion skipped due to invalid name (The Name field may only contain alphabetic characters as well as spaces) ".$row['primary_contact_email'], json_encode($row), 'Corporate Parent excel');
                    }
                }else{
                    Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Corporate Parent insertion skipped due to invalid contact phone.");
                    insertSkippedDataLog('Corporate Parent', 'contact_person_email', $row['primary_contact_email'], "Corporate Parent insertion skipped due to invalid contact phone ".$row['primary_contact_phone'], json_encode($row), 'Corporate Parent excel');
                }
            }else{
                Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Corporate Parent insertion skipped due to invalid email address.");
                insertSkippedDataLog('Corporate Parent', 'contact_person_email', $row['primary_contact_email'], "Corporate Parent insertion skipped due to invalid email address ".$row['primary_contact_email'], json_encode($row), 'Corporate Parent excel');
            }
        } else {
            Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Corporate Parent insertion skipped due to non availability of required fields.");
            insertSkippedDataLog('Corporate Parent', 'contact_person_email', $row['primary_contact_email'], "Corporate Parent insertion skipped due to non availability of required fields.", json_encode($row), 'Corporate Parent excel');
        }
    }

    public function getRowCount()
    {
        return $this->rows . '|' . $this->duplicate_rows . '|' . $this->updated_rows;
    }

    public function batchSize(): int
    {
        return 1000;
    }

    public function chunkSize(): int
    {
        return 5000;
    }
    
}
