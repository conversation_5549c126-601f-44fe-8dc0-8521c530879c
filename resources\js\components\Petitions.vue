<template>
<div>
  <div v-if="loading">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Petitions</h3>
                </div>
                <div class="card-body">
                  <div class="row">
                    <div class="col-md-4">
                      <div class="form-group">
                        <input
                          class="form-control"
                          placeholder="Store Name (Min 3 chars)"
                          id="store_name"
                          v-model="store_name"
                        />
                      </div>
                    </div>
                    <!-- User Status Filter -->
                    <div class="col-md-4">
                      <div class="form-group">
                        <select
                          class="form-control"
                          id="user_status"
                          name="user_status"
                          v-model="status_id"
                        >
                          <option value="">All Status</option>
                          <option
                            v-for="(status, index) in statusList"
                            :key="index"
                            :value="status.id"
                          >
                            {{ status.status }}
                          </option>
                        </select>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="card-footer">
                  <button
                    type="button"
                    class="btn btn-success"
                    @click="searchFilter()"
                  >
                    Search
                  </button>
                  <button
                    type="button"
                    @click="reset()"
                    class="btn btn-success margin-left-5"
                  >
                    Reset
                  </button>
                </div>
                <!-- /.card-header -->
                <div class="card-body">
                    <b-table
                        :fields="petitionFields"
                        :items="petitions"
                        responsive
                        show-empty
                        bordered
                        sticky-header="800px"
                        :sort-by.sync="sortBy"
                        :sort-desc.sync="sortDesc"
                        @sort-changed="onSortChanged"
                        >
                        <!-- Custom Cell for Action Column -->
                        <template #cell(actions)="data">
                            <a :data-user-id="data.item.id" class="custom-edit-btn" title="View Signed Users" style="border:none" @click="viewAllSignedUsers(data.item)">
                            <i class="nav-icon fas fa-eye"></i>
                            </a>
                            <a :data-user-id="data.item.id" class="custom-edit-btn" title="Edit Petition" style="border:none" @click="editPetitionModal(data.item)">
                            <i class="nav-icon fas fa-edit"></i>
                            </a>
                            <a :data-user-id="data.item.id" class="custom-edit-btn" title="Link Petition to Store" style="border:none" @click="linkPetitionModal(data.item)">
                            <i class="nav-icon fas fa-link"></i>
                            </a>
                            <a :data-user-id="data.item.id" class="custom-edit-btn" title="Merchant Page" style="border:none" @click="openPetitionMerchantPage(data.item)">
                            <i class="nav-icon fas fa-external-link-alt"></i>
                            </a>
                            <a v-if="data.item.status != 'Onboarded'" :data-user-id="data.item.id" class="custom-edit-btn" title="Email Merchants" style="border:none" @click="sendEmailToMerchants(data.item)">
                            <i class="nav-icon fas fa-envelope"></i>
                            </a>
                        </template>
                        </b-table>
                  <!-- Display pagination controls -->
                  <div v-if="petitions.length > 0" style="float:right">
                      <b-pagination
                          v-model="currentPage"
                          :total-rows="totalItems"
                          :per-page="perPage"
                          prev-text="Prev"
                          next-text="Next"
                          :ellipsis="true"
                          :limit="5"
                      ></b-pagination>
                  </div>
                  <p v-else>No data displayed. Please refine your search criteria.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
    <!-- CP Modal Start -->
    <b-modal
      id="edit-petition-modal"
      ref="modal"
      :header-text-variant="headerTextVariant"
      :title="modalTitle"
      @show="resetModal"
      @hidden="resetModal"
      ok-title="Save"
      ok-variant="success"
      cancel-variant="outline-secondary"
      @ok="handleOk"
      :no-close-on-esc="true"
      :no-close-on-backdrop="true"
    >
      <form ref="form" @submit.stop.prevent="save" class="needs-validation">
        <!-- Mayor Contact Details (Read-only) -->
        <div class="row mb-4">
          <div class="col-md-12">
            <div class="card card-secondary">
              <div class="card-header">
                <h5 class="card-title mb-0">Mayor Contact Details</h5>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-md-12">
                    <div class="mayor-contact-row">
                      <div class="mayor-contact-label-col">
                        <label class="mb-0 mayor-contact-label">Name:</label>
                      </div>
                      <div class="mayor-contact-value-col">
                        <span class="mb-0 mayor-contact-value">
                          {{ (petition.first_name || petition.middle_name || petition.last_name)
                             ? [petition.first_name, petition.middle_name, petition.last_name].filter(Boolean).join(' ')
                             : 'N/A' }}
                        </span>
                      </div>
                    </div>
                    <div class="mayor-contact-row">
                      <div class="mayor-contact-label-col">
                        <label class="mb-0 mayor-contact-label">Phone:</label>
                      </div>
                      <div class="mayor-contact-value-col">
                        <span class="mb-0 mayor-contact-value">{{ petition.phone || 'N/A' }}</span>
                      </div>
                    </div>
                    <div class="mayor-contact-row">
                      <div class="mayor-contact-label-col">
                        <label class="mb-0 mayor-contact-label">Email:</label>
                      </div>
                      <div class="mayor-contact-value-col">
                        <span class="mb-0 mayor-contact-value">{{ petition.email || 'N/A' }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Store Name -->
        <div class="row">
          <div class="col-md-12">
            <label for="store_name">Store Name <span class="red">*</span></label>
            <input
              id="store_name"
              name="store_name"
              type="text"
              v-model="petition.store_name"
              class="form-control"
              v-validate="'required'"
            />
            <span v-show="errors.has('store_name')" class="text-danger">{{ errors.first('store_name') }}</span>
          </div>
        </div>

        <!-- Store Short Name -->
        <div class="row">
          <div class="col-md-12">
            <label for="store_name">Store Short Name</label>
            <input
              id="store_short_name"
              name="store_short_name"
              type="text"
              v-model="petition.store_short_name"
              class="form-control"
              v-validate="'max:12'"
            />
            <span v-show="errors.has('store_short_name')" class="text-danger">{{ errors.first('store_short_name') }}</span>
          </div>
        </div>

        <!-- Store Logo (Optional) -->
        <div class="row mt-3">
          <div class="col-md-12">
            <label for="storeLogo">Store Logo
              <a href="javascript:void(0)" v-b-tooltip.hover title="The petition logo will be displayed on the petition landing page.">
                <svg width="15px" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 490 490" style="margin-bottom: 2px;">
                  <g>
                    <path d="M245,490C109.9,490,0,380.1,0,245S109.9,0,245,0s245,109.9,245,245S380.1,490,245,490z M245,62C144.1,62,62,144.1,62,245s82.1,183,183,183s183-82.1,183-183S345.9,62,245,62z"/>
                    <circle cx="241.3" cy="159.2" r="29.1"/>
                    <polygon points="285.1,359.9 270.4,359.9 219.6,359.9 204.9,359.9 204.9,321 219.6,321 219.6,254.8 205.1,254.8 205.1,215.9 219.6,215.9 263.1,215.9 270.4,215.9 270.4,321 285.1,321"/>
                  </g>
                </svg>
              </a> (120 x 68)
            </label>

            <div class="input-group">
              <div class="custom-file">
                <input
                  type="file"
                  ref="logo_file"
                  name="storeLogo"
                  id="storeLogo"
                  v-on:change="handleFileUpload()"
                  class="custom-file-input"
                  accept="image/*"
                />
                <label for="storeLogo" class="custom-file-label">{{ logo_fileName }}</label>
              </div>
            </div>
          </div>
        </div>

        <div class="row mt-2">
          <div class="col-md-6">
            <div class="float-left mt-2">
              <span class="red-error ml-1">Maximum size 5 mb is allowed</span>
            </div>
          </div>
          <div class="col-md-6">
            <b-button style="float:right; margin-top: 7px" @click="clear">Clear File</b-button>
          </div>
        </div>



        <!-- Petition Number & Title -->
        <div class="row mt-3">
          <div class="col-md-12">
            <label for="petition_number">Petition Number <span class="red">*</span></label>
            <input
              id="petition_number"
              name="petition_number"
              type="text"
              v-model="petition.petition_number"
              class="form-control"
              v-validate="'required'"
            />
            <span v-show="errors.has('petition_number')" class="text-danger">{{ errors.first('petition_number') }}</span>
          </div>
        </div>

        <!-- Primary Contact -->
        <div class="row mt-3">
          <div class="col-md-6">
            <label for="primary_firstname">Primary First Name <span class="red">*</span></label>
            <input
              id="primary_firstname"
              name="primary_firstname"
              type="text"
              v-model="petition.primary_contact_person_firstname"
              class="form-control"
              v-validate="'required'"
            />
            <span v-show="errors.has('primary_firstname')" class="text-danger">{{ errors.first('primary_firstname') }}</span>
          </div>
          <div class="col-md-6">
            <label for="primary_lastname">Primary Last Name <span class="red">*</span></label>
            <input
              id="primary_lastname"
              name="primary_lastname"
              type="text"
              v-model="petition.primary_contact_person_lastname"
              class="form-control"
              v-validate="'required'"
            />
            <span v-show="errors.has('primary_lastname')" class="text-danger">{{ errors.first('primary_lastname') }}</span>
          </div>
        </div>

        <div class="row mt-3">
          <div class="col-md-6">
            <label for="primary_email">Primary Email <span class="red">*</span></label>
            <input
              id="primary_email"
              name="primary_email"
              type="email"
              v-model="petition.primary_contact_person_email"
              class="form-control"
              v-validate="'required|email'"
            />
            <span v-show="errors.has('primary_email')" class="text-danger">{{ errors.first('primary_email') }}</span>
          </div>

          <div class="col-md-6">
            <label for="primary_contact_person_title">Primary Contact Person Title <span class="red">*</span></label>
            <input
              id="primary_contact_person_title"
              name="primary_contact_person_title"
              type="text"
              v-model="petition.primary_contact_person_title"
              class="form-control"
              v-validate="'required'"
            />
            <span v-show="errors.has('primary_contact_person_title')" class="text-danger">{{ errors.first('primary_contact_person_title') }}</span>
          </div>
        </div>

        <!-- Secondary (Optional) -->
        <div class="row mt-3">
          <div class="col-md-6">
            <label for="secondary_firstname">Secondary First Name</label>
            <input
              id="secondary_firstname"
              name="secondary_firstname"
              type="text"
              v-model="petition.secondary_contact_person_firstname"
              class="form-control"
            />
          </div>
          <div class="col-md-6">
            <label for="secondary_lastname">Secondary Last Name</label>
            <input
              id="secondary_lastname"
              name="secondary_lastname"
              type="text"
              v-model="petition.secondary_contact_person_lastname"
              class="form-control"
            />
          </div>
        </div>

        <div class="row mt-3">
          <div class="col-md-6">
            <label for="secondary_email">Secondary Email</label>
            <input
              id="secondary_email"
              name="secondary_email"
              type="email"
              v-model="petition.secondary_contact_person_email"
              class="form-control"
            />
          </div>
          <div class="col-md-6">
            <label for="secondary_contact_person_title">Secondary Contact Person Title <span class="red">*</span></label>
            <input
              id="secondary_contact_person_title"
              name="secondary_contact_person_title"
              type="text"
              v-model="petition.secondary_contact_person_title"
              class="form-control"
            />
          </div>
        </div>

        <!-- Address -->
        <div class="row mt-3">
          <div class="col-md-12">
            <label for="street_address">Street Address <span class="red">*</span></label>
            <input
              id="street_address"
              name="street_address"
              type="text"
              v-model="petition.street_address"
              class="form-control"
              v-validate="'required'"
            />
            <span v-show="errors.has('street_address')" class="text-danger">{{ errors.first('street_address') }}</span>
          </div>
        </div>

        <div class="row mt-3">
          <div class="col-md-4">
            <label for="city">City <span class="red">*</span></label>
            <input
              id="city"
              name="city"
              type="text"
              v-model="petition.city"
              class="form-control"
              v-validate="'required'"
            />
            <span v-show="errors.has('city')" class="text-danger">{{ errors.first('city') }}</span>
          </div>
          <div class="col-md-4">
            <label for="state">State <span class="red">*</span></label>
            <input
              id="state"
              name="state"
              type="text"
              v-model="petition.state"
              class="form-control"
              v-validate="'required'"
            />
            <span v-show="errors.has('state')" class="text-danger">{{ errors.first('state') }}</span>
          </div>
          <div class="col-md-4">
            <label for="zipcode">Zipcode <span class="red">*</span></label>
            <input
              id="zipcode"
              name="zipcode"
              type="text"
              v-model="petition.zipcode"
              class="form-control"
              v-validate="'required'"
            />
            <span v-show="errors.has('zipcode')" class="text-danger">{{ errors.first('zipcode') }}</span>
          </div>
        </div>

        <!-- Status -->
        <div class="row mt-3">
          <div class="col-md-12">
            <label for="user_status">Status <span class="red">*</span></label>
            <select
              class="form-control"
              id="user_status"
              name="user_status"
              v-model="petition.status_id"
              v-validate="'required'"
              :disabled="petition.status === 'Onboarded' && petition.onboarded_date !== null"
            >
              <option v-for="(status, index) in filteredStatusList" :key="index" :value="status.id">
                {{ status.status }}
              </option>
            </select>
            <span v-show="errors.has('user_status')" class="text-danger">{{ errors.first('user_status') }}</span>
          </div>
        </div>

        <div class="row mt-3" v-if="isRejectedStatus">
          <div class="col-md-12">
            <label for="rejection_reason_type">Reason for Rejection <span class="red">*</span></label>
            <select
              id="rejection_reason_type"
              class="form-control"
              name="rejection_reason"
              v-model="petition.rejection_reason"
              v-validate="'required'"
            >
              <option disabled value="">Please select a reason</option>
              <option value="Merchant already accepts CanPay">Merchant already accepts CanPay</option>
              <option value="other">Other</option>
            </select>
            <span v-show="errors.has('rejection_reason')" class="text-danger">{{ errors.first('rejection_reason') }}</span>
          </div>
        </div>
        <div class="row mt-2" v-if="isRejectedStatus && petition.rejection_reason === 'other'">
          <div class="col-md-12">
            <label for="rejection_reason_text">Enter Reason <span class="red">*</span></label>
            <textarea
              id="rejection_reason_text"
              class="form-control"
              name="rejection_comments"
              rows="3"
              placeholder="Enter detailed reason for rejection"
              v-validate="'required'"
              v-model="petition.rejection_comments"
            ></textarea>
            <span v-show="errors.has('rejection_comments')" class="text-danger">{{ errors.first('rejection_comments') }}</span>
          </div>
        </div>
      </form>
    </b-modal>
    <b-modal
      id="petition-signed-user-list-modal"
      ref="petition-signed-user-list-modal"
      :header-text-variant="headerTextVariant"
      title="Petition Signed User List"
      hide-footer
    >
      <div class='row odd-row'>
        <p class="para-settings">{{petition.store_name}}</p>
      </div>
      <div class="card-body">
      <div class="mb-4" style="width:90%;position:absolute;right:2.25rem;">
          <b-input-group size="md" >
            <b-form-input
              id="filter-input"
              v-model="filter"
              type="search"
              placeholder="Search for records..."
            ></b-form-input>
          </b-input-group>
      </div>
        <b-table
          id="petition_no_table"
          responsive
          :per-page="perPagePetitionSigned"
          :current-page="currentPagePetitionSigned"
          bordered
          :items="petitionSignedUsers"
          :filter="filter"
          sticky-header="800px"
          :fields="rpetitionSignedField"
          head-variant="light"
          @filtered="onFiltered"
          class="margin-top-petition-class"
        >
        </b-table>
          <b-pagination
            v-model="currentPagePetitionSigned"
            :total-rows="rowsPetitionSigned"
            :per-page="perPagePetitionSigned"
            aria-controls="petition_no_table"
            class="float-right"
          ></b-pagination>
      </div>
    </b-modal>
    <b-modal
      id="link-petition-modal"
      ref="modal"
      :header-text-variant="headerTextVariant"
      :title="modalTitle"
      @show="resetModal"
      @hidden="resetModal"
      ok-title="Save"
      ok-variant="success"
      cancel-variant="outline-secondary"
      @ok="saveLinkPetition"
      :no-close-on-esc="true"
      :no-close-on-backdrop="true"
    >
      <form ref="form" @submit.stop.prevent="save" class="needs-validation">
        <!-- Store Name (readonly) -->
        <div class="row">
          <div class="col-md-12">
            <label for="store_name">Petition Name</label>
            <input
              id="store_name"
              name="store_name"
              type="text"
              v-model="petition.store_name"
              class="form-control"
              readonly
            />
          </div>
        </div>

        <!-- Store Selection Dropdown -->
        <div class="row mt-3">
          <div class="col-md-12">
            <label for="store_id">Recently Onboarded Store</label>
            <v-select
              id="store_id"
              name="store_id"
              :options="stores"
              label="retailer"
              :reduce="store => store.id"
              v-model="selectedStoreId"
              :class="{ 'is-invalid': errors.has('store_id') }"
              v-validate="'required'"
              data-vv-name="store_id"
              placeholder="Select a store"
            />
            <span v-show="errors.has('store_id')" class="text-danger">
              {{ errors.first('store_id') }}
            </span>
          </div>

        </div>
      </form>
    </b-modal>
    <b-modal
    id="onboarded-store-address-modal"
    ref="onboarded-store-address-modal"
    :header-text-variant="headerTextVariant"
    title="Onboarded Store Information"
    hide-footer
  >
    <div class="modal-body">
      <div class="row odd-row">
          <div class="col-md-4 row-value">
            <label for="name">Onboarded Store Name</label>
          </div>
          <div class="col-md-1 row-value">:</div>
          <div class="col-md-7 row-value">
            <span for="name">{{ petition.retailer }}</span>
          </div>
        </div>
        <div class="row even-row">
          <div class="col-md-4 row-value">
            <label for="access_rights">Onboarded Store Address</label>
          </div>
          <div class="col-md-1 row-value">:</div>
          <div class="col-md-7 row-value">
            <span for="name"
              >{{ petition.onboarded_address }}</span
            >
          </div>
        </div>
    </div>
  </b-modal>


    <!-- CP Modal End -->
  </div>
</div>
</template>
<script>
import CanPayLoader from "./CustomLoader/CanPayLoader.vue"
import moment from "moment";
import api from "@/api/petition.js";
import user_api from "@/api/user.js";
import { validationMixin } from "vuelidate";
import commonConstants from "@/common/constant.js";
export default {
  mixins: [validationMixin],
  components:{
    CanPayLoader
  },
  data() {
    return {
      modalTitle: "Edit Petition",
      currentPagePetitionSigned:1,
      rowsPetitionSigned:0,
      perPagePetitionSigned:10,
      petitionSignedUsers: [],
      filter: "",
      statusList: [],
      filteredStatusList: [],
      headerTextVariant: "light",
      petition: {},
      showReloadBtn: false,
      store_name: "",
      status_id: "",
      loading: false,
      petitions:[],
      currentPage: 1,
      perPage: 10,
      totalPage: 0,
      totalItems: 0,
      logo_fileName: "Choose File",
      upload_changed: false,
      logo_file: null,
      rpetitionSignedField: [
      {
        key: 'name',
        label:'Consumer Name'
      },
      {
        key: 'type',
        label:'Role In This Petition'
      },
      {
        key: 'state',
        label: 'State'
      },
      {
        key: 'created_at',
        label: 'Signed At'
      }],
      stores: {},
      selectedStoreId: '',
      petitionFields: [
        { key: 'store_name', label: 'Petition Name' },
        { key: 'full_address', label: 'Address Of Petition' },
        { key: 'created_at', label: 'Created On', sortable: true },
        { key: 'updated_at', label: 'Last Updated On', sortable: true },
        { key: 'status', label: 'Status' },
        { key: 'retailer', label: 'Onboarded Store' },
        { key: 'actions', label: 'Action' }
        ],
        sortBy: 'created_at',
        sortDesc: true
    };
  },
  computed: {
    isRejectedStatus() {
        const rejectedStatus = this.statusList.find(status => status.status.toLowerCase() === 'rejected');
        return this.petition.status_id === rejectedStatus?.id;
    }
  },
  created() {
    this.getPetitionStatus();
  },
  watch: {
    currentPage(newPage, oldPage) {
      if (newPage !== oldPage) {
        this.searchPetition(); // Call the method only if page actually changed
      }
    }
  },
  methods: {
    async openPetitionMerchantPage(row) {
        try {
        const response = await fetch('/api/config/consumer-app-url');
        const data = await response.json();
        const consumerAppUrl = data.url;

        if (!consumerAppUrl) {
            console.error('CONSUMER_APP_URL is not defined in Laravel env.');
            return;
        }

        const encodedId = btoa(row.id.toString());
        const url = `${consumerAppUrl}petition/${encodedId}`;

        window.open(url, '_blank');
        } catch (error) {
        console.error('Failed to fetch CONSUMER_APP_URL:', error);
        }
    },
    onFiltered(filteredItems) {
      this.rowsPetitionSigned = filteredItems.length
      this.currentPagePetitionSigned = 1
    },
    handleFileUpload() {
      let self = this;
      self.logo_file = self.$refs.logo_file.files[0];
      self.logo_fileName = self.$refs.logo_file.files[0].name;
      self.upload_changed = true;
    },
    isActiveStatus() {
    const activeStatus = this.statusList.find(
      (status) => status.status.toLowerCase() === "onboarded"
    );
    return this.petition.status_id === (activeStatus?.id ?? null);
  },
    getPetitionStatus() {
      var self = this;
      api
        .getPetitionStatus()
        .then((response) => {
          if ((response.code = 200)) {
            self.statusList = response.data;
          } else {
            error(response.message);
          }
        })
        .catch((err) => {
          error(err);
        });
    },
    clear(e) {
      var self = this;
      self.logo_fileName = "Choose File";
      self.$refs.logo_file.value = null;
      self.upload_changed = false;
      self.logo_url = null;
      self.petition.logo_url = "";
      e.preventDefault();
    },
    handleOk(bvModalEvt) {
      var self = this;
      // Prevent modal from closing
      bvModalEvt.preventDefault();
      // Trigger submit handler
      self.editSavPetition();
    },
    editSavPetition() {
        var self = this;
        let formData = new FormData();
        formData.append("logo", self.logo_file);
        formData.append("storeName", self.petition.store_name);

        // Exit when the form isn't valid
        this.$validator.validateAll().then((result) => {
            if (result) {
            // ✅ Extra validation for rejection reason
            if (self.isRejectedStatus) {
                if (!self.petition.rejection_reason) {
                error("Please select a reason for rejection.");
                return;
                }
                if (self.petition.rejection_reason === "other" && (!self.petition.rejection_comments || !self.petition.rejection_comments.trim())) {
                error("Please enter the reason for rejection.");
                return;
                }
            } else {
                // Clear if status is not rejected
                self.petition.rejection_reason = null;
                self.petition.rejection_comments = null;
            }

            self.loading = true;

            if (self.petition.logo_url === "Choose File") {
                self.petition.logo_url = null;
            }

            if (self.upload_changed) {
                self.petition.logo_url = self.logo_fileName.valueOf();
                api.importLogo(formData).then((response) => {
                self.petition.logo_url =
                    process.env.MIX_AWS_CLOUDFRONT_URL + response.data.replace(/^.*[\\\/]/, "");
                self.editSavPetitionApi(self);
                });
            } else {
                self.editSavPetitionApi(self);
            }
            }
        });
    },
    editSavPetitionApi(self) {
      api
        .editPetition(self.petition)
        .then((response) => {
          if (response.code == 200) {
            self.$bvModal.hide("edit-petition-modal");
            success(response.message);
            self.searchPetition();
            self.loading = false;
          } else {
            error(response.message);
            self.loading = false;
          }
        })
        .catch((err) => {
          error(err.response.data.message);
          self.loading = false;
        });
    },
    resetModal() {
      var self = this;
      self.upload_changed = false;
      self.logo_file = null;
      self.logo_fileName = "Choose File";
      // Clear validation errors
      self.$validator.reset();
    },
    toTitleCase(str) {
      if (!str) return '';
      return str.replace(/\w\S*/g, word => {
        return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
      });
    },
    viewAllSignedUsers(petition){
      var self = this;
      self.petition = Object.assign({}, petition);
      var request = {
        petition_id: petition.id
      };
      self.loading = true;
      api
      .getPetitionSignedUsers(request)
      .then(function (response) {
        if (response.code == 200) {
          // self.petitionSignedUsers = response.data;
          self.petitionSignedUsers = response.data.map(user => ({
            ...user,
            type: self.toTitleCase(user.type)
          }));
          self.rowsPetitionSigned = self.petitionSignedUsers.length;
          self.loading = false;
          self.filter = '';
          self.$bvModal.show("petition-signed-user-list-modal");
        } else {
          error(response.message);
          self.loading = false;
        }
      })
      .catch(function (error) {
        // error(error);
        self.loading = false;
      });
    },
    editPetitionModal(petition){
      var self = this;
      self.modalTitle = "Edit Petition";
      self.petition = Object.assign({}, petition);
      self.upload_changed = false;
      self.smallLogoClear
      self.filteredStatusList = self.statusList.filter(status => {
        // Skip 'Onboarded' if petition.status is not 'Onboarded'
        return !(self.petition.status !== 'Onboarded' && status.status === 'Onboarded');
      });
      self.$bvModal.show("edit-petition-modal");
      if (petition.logo_url == null || petition.logo_url == "") {
        self.logo_fileName = "Choose File";
      } else {
        self.logo_fileName = petition.logo_url.replace(/^.*[\\\/]/, '');
      }
      self.small_logo_fileName = "Choose File";

    },
    showAddressPopup(petition) {
      var self = this;
      // Set address based on the retailer data (you might need to fetch address dynamically)
      self.petition = Object.assign({}, petition);
      self.$bvModal.show("onboarded-store-address-modal");
    },
    linkPetitionModal(petition){
      var self = this;
      self.modalTitle = "Link Petition to Store";
      self.petition = Object.assign({}, petition);
      var request = {
        petition_id: petition.id
      };
      self.selectedStoreId = '';
      self.loading = true;
      user_api
      .getStores(request)
      .then(function (response) {
        if (response.code == 200) {
          self.stores= response.data;
          self.loading = false;
          self.$bvModal.show("link-petition-modal");
        } else {
          error(response.message);
          self.loading = false;
        }
      })
      .catch(function (error) {
        // error(error);
        self.loading = false;
      });
    },
    saveLinkPetition(bvModalEvt) {
      var self = this;
      // Prevent modal from closing
      bvModalEvt.preventDefault();
      // Trigger submit handler
      self.saveLinkPetitionApi();
    },
    saveLinkPetitionApi(){
      var self = this;
      // Exit when the form isn't valid
      this.$validator.validateAll().then((result) => {
        if (result) {
          var request = {
            petition_id: self.petition.id,
            store_id: self.selectedStoreId
          };
          api
          .linkPetitionToStore(request)
          .then((response) => {
            if (response.code == 200) {
              self.$bvModal.hide("link-petition-modal");
              success(response.message);
              self.searchPetition();
              self.loading = false;
            } else {
              error(response.message);
              self.loading = false;
            }
          })
          .catch((err) => {
            error(err.response.data.message);
            self.loading = false;
          });
        }
      });
    },
    searchFilter(){
        var self = this;
        self.currentPage = 1;
        self.searchPetition();
    },
    searchPetition(){
      var self = this;
      var request = {
          store_name: self.store_name,
          status_id: self.status_id,
          page: self.currentPage,
          per_page: self.perPage,
          sort_by: self.sortBy,
          sort_desc: self.sortDesc
      };
      self.loading = true;
      api
      .searchPetitions(request)
      .then(function (response) {
        if (response.code == 200) {
            self.petitions = response.data.data.map(petition => ({
            ...petition,
            full_address: self.petitionAddress(petition)
            }));
          self.totalPage = response.data.total_pages;
          self.totalItems = response.data.total;
          self.currentPage = parseInt(response.data.current_page) || 1;
          self.loading = false;
        } else {
          error(response.message);
          self.loading = false;
        }
      })
      .catch(function (error) {
        // error(error);
        self.loading = false;
      });
    },
    onSortChanged({ sortBy, sortDesc }) {
        this.sortBy = sortBy;
        this.sortDesc = sortDesc;
        this.searchPetition();
    },
    petitionAddress(petition) {
      var full_address = "";
      full_address =
          full_address +
          (petition.street_address != null ? petition.street_address + ", " : "");
      full_address =
          full_address + (petition.city != null ? petition.city + ", " : "");
      full_address =
          full_address + (petition.state != null ? petition.state + " " : "");
      full_address =
          full_address + (petition.zipcode != null ? petition.zipcode + " " : "");
      return full_address;
    },
    reset() {
      var self = this;
      self.store_name = "";
      self.status_id = "";
    },
    async sendEmailToMerchants(row) {
      var self = this;
      if (!row.id) {
        error('Petition ID not available.');
        return;
      }
      self.loading = true;
      const request = {
        petition_id: row.id,
      };
      try {
        const response = await api.sendEmailToPetitionPrimaryContact(request);
        if (response.code === 200) {
          // Handle enhanced response format
          if (response.data && response.data.emails_sent) {
            const emailsSent = response.data.emails_sent;
            const emailErrors = response.data.email_errors || [];

            if (emailsSent.length > 1) {
              // Both primary and secondary emails sent
              success(`Emails sent successfully to both contacts: ${emailsSent.join(', ')}`);
            } else if (emailsSent.length === 1) {
              if (emailErrors.length > 0) {
                // Partial success
                warning(`Email sent to ${emailsSent[0]}, but failed to send to ${emailErrors[0].email}: ${emailErrors[0].error}`);
              } else {
                // Single email sent (no secondary contact or only primary contact)
                success(`Email sent successfully to ${emailsSent[0]}`);
              }
            }
          } else {
            // Fallback for legacy response format
            success('Email sent successfully to petition contact(s).');
          }
        } else {
          error(response.message || 'Failed to send email.');
        }
      } catch (err) {
        if (err.response && err.response.data && err.response.data.message) {
          error(err.response.data.message);
        } else {
          error('Failed to send email.');
        }
      } finally {
        self.loading = false;
      }
    },
  },
  mounted() {
    var self = this;
    document.title = "CanPay - Petition";
    this.searchPetition();
  },
};
</script>
<style>
.disabled {
    background-color: #e9ecef;
}

.margin-top-petition-class{
  margin-top:60px!important;
}
.v-select.is-invalid .vs__dropdown-toggle {
  border-color: #dc3545;
}

/* Mayor Contact Details Styling */
.card-secondary .card-header {
  background-color: #000000 !important;
  color: white !important;
  border-bottom: 1px solid #000000 !important;
}

.card-secondary .card-body {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
}

.mayor-contact-row {
  display: flex;
  align-items: baseline;
  margin-bottom: 8px;
  min-height: 24px;
}

.mayor-contact-label-col {
  flex: 0 0 120px;
  min-width: 120px;
  display: flex;
  align-items: baseline;
}

.mayor-contact-value-col {
  flex: 1;
  padding-left: 15px;
  display: flex;
  align-items: baseline;
}

.mayor-contact-label {
  font-weight: 600;
  color: #495057;
  margin: 0;
  line-height: 1.5;
}

.mayor-contact-value {
  color: #6c757d;
  line-height: 1.5;
}

</style>
