<template>
<div>
  <div v-if="loading">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Store API Keys</h3>
                </div>
                <!-- /.card-header -->
                <div class="card-body">
                  <div class="row">
                  <div class="col-md-4">
                    <div class="form-group">
                     <input
                        class="form-control"
                        placeholder="Retailer (min 3 chars)"
                        id="retailer"
                        v-model="retailer"
                      />
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="form-control"
                        placeholder="Merchant ID (Exact)"
                        id="merchant_id"
                        v-model="merchant_id"
                      />
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="form-control"
                        placeholder="Store ID (Exact)"
                        id="store_id"
                        v-model="store_id"
                      />
                    </div>
                  </div>
                </div>
                <div class="row">
                  <div class="col-md-4">
                    <div class="form-group">
                      <select class="selectCssChanges" v-model="selectedMerchantType">
                        <option value="vendor">Merchant</option>
                        <option value="brand">Brand</option>
                      </select>
                    </div>
                  </div>
                </div>
                </div>
                  <div class="card-footer">
                    <button
                      type="button"
                      class="btn btn-success"
                      @click="searchApiKeys()"
                    >
                      Search
                    </button>
                    <button
                      type="button"
                      @click="reset()"
                      class="btn btn-success margin-left-5"
                    >
                      Reset
                    </button>
                    <button
                    type="button"
                    @click="exportApiKeys()"
                    class="btn btn-danger"
                  >
                    Export API Keys <i
                      class="fa fa-download ml-10"
                      aria-hidden="true"
                    ></i>
                  </button>
                  </div>
                <div class="card-body">
                  <b-table-simple
                      responsive
                      show-empty
                      bordered
                      sticky-header="800px"
                      v-if="allApiKeysModel.length > 0"
                    >
                      <b-thead head-variant="light">
                        <tr>
                          <th>Merchant ID</th>
                          <th>Store ID</th>
                          <th>Retailer</th>
                          <th class="text-center">APP Key</th>
                          <th class="text-center">API Secret</th>
                        </tr>
                      </b-thead>
                      <b-tbody v-for="(row, index) in allApiKeysModel" :key="index">
                        <b-tr>
                          <b-td class="text-left text-gray">{{
                            row.merchantID
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.store_id
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.retailer
                          }}</b-td>
                          <b-td class="text-center text-gray">{{
                            row.app_key
                          }}</b-td>
                          <b-td class="text-center text-gray">{{
                            row.api_secret
                          }}</b-td>
                        </b-tr>
                      </b-tbody>
                    </b-table-simple>
                    <p v-else>No data displayed. Please refine your search criteria.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</div>
</template>
<script>
import api from "@/api/apikeys.js";
import { saveAs } from "file-saver";
import moment from "moment";
import commonConstants from "@/common/constant.js";
import CanPayLoader from "./CustomLoader/CanPayLoader.vue"
export default {
  components:{
    CanPayLoader
  },
  data() {
    return {
      allApiKeysModel: {},
      apikeys:{},
      currentUser: localStorage.getItem("user")
        ? JSON.parse(localStorage.getItem("user"))
        : null,
      showReloadBtn:false,
      retailer:"",
      store_id:"",
      merchant_id:"",
      loading:false,
      selectedMerchantType:"vendor"
    };
  },
  created() {
  },
  methods: {
    exportApiKeys(){
      var self = this;
      var request = {
        apikeys: self.allApiKeysModel,
      };

      api
        .getstoreapikeysexport(request)
        .then(function (response) {
          var FileSaver = require("file-saver");
          var blob = new Blob([response], {
            type: "application/xlsx",
          });
          FileSaver.saveAs(
            blob,
            moment().format("MM/DD/YYYY") +
              "_store_api_keys_export.xlsx"
          );
        })
        .catch(function (error) {
          // error(error);
        });
    },
    searchApiKeys(){
      var self = this;
      if((self.retailer).trim().length < 3 && $("#store_id").val().trim() === '' &&  $("#merchant_id").val().trim() === ''){
        error("Please provide Retailer (Min 3 chars) or Store ID(exact) or Merchant ID(exact)");
        return false;
      }
      var request = {
        retailer: self.retailer,
        store_id:self.store_id,
        merchant_id:self.merchant_id,
        type:self.selectedMerchantType,
      };
      self.loading = true;
      api
      .searchApiKeys(request)
      .then(function (response) {
        if (response.code == 200) {
          self.allApiKeysModel = response.data;
          self.loading = false;
        } else {
          error(response.message);
          self.loading = false;
        }
      })
      .catch(function (error) {
        // error(error);
        self.loading = false;
      });
    },
    reset(){
      var self = this;
      self.retailer = "";
      self.merchant_id = "";
      self.store_id = "";
    }
  },
  mounted() {
    var self = this;
    document.title = "CanPay - Store API Keys";
  },
};
</script>
<style scoped>
.selectCssChanges{
    color: #6c757d;
    width: 100%;
    border: 1px solid #cccccc;
    border-radius: 4px;
    height: 38px;
}
</style>
