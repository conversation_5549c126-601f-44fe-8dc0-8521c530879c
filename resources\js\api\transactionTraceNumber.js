import { loadProgressBar } from 'axios-progress-bar'

const transactionTraceNumberMigrationLog = (request) => {
    var instance = axios.create({
        baseURL: process.env.MIX_ACH_APP_URL,
    });
    request.header('Access-Control-Allow-Origin', '*'); // You can specify specific origins instead of '*'
    request.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE');
    request.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    loadProgressBar({}, instance);
    return new Promise((res, rej) => {
        instance.post('api/admin/getmigrationlog', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
}

const importTransactionTraceNumber = (formData) => {
    var instance = axios.create({
        baseURL: process.env.MIX_ACH_APP_URL,
    });
    loadProgressBar({}, instance);
    return new Promise((res, rej) => {
        instance.post('api/admin/import/transactions', formData)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
}

export default {
    transactionTraceNumberMigrationLog,
    importTransactionTraceNumber
}