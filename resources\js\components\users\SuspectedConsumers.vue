<template>
<div>
  <div v-if="loading">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Suspected Consumers</h3>
                </div>

                <!-- /.card-header -->
                <div class="card-body">
                  <div class="row">
                  <div class="col-md-4">
                    <div class="form-group">
                     <input
                        class="form-control"
                        placeholder="Consumer Name (min 3 chars)"
                        id="consumer"
                        v-model="consumer"
                      />
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="form-control"
                        placeholder="Phone No (Exact)"
                        id="phone_no"
                        v-model="phone_no"
                      />
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="form-control"
                        placeholder="Email (Exact)"
                        id="email"
                        v-model="email"
                      />
                    </div>
                  </div>
                </div>

                <div class="row">
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="start-date form-control"
                        placeholder="From Date"
                        id="start-date"
                        onkeydown="return false"
                        autocomplete="off"
                      />
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="end-date form-control"
                        placeholder="To Date"
                        id="end-date"
                        onkeydown="return false"
                        autocomplete="off"
                      />
                    </div>
                  </div>
                </div>
                </div>
                  <div class="card-footer">
                    <button
                      type="button"
                      class="btn btn-success"
                      @click="searchRegisteredConsumer()"
                    >
                      Search
                    </button>
                    <button
                    type="button"
                    @click="exportReport()"
                    class="btn btn-danger ml-10"
                    >
                    Export all Suspected Consumers <i
                        class="fa fa-download ml-10"
                        aria-hidden="true"
                    ></i>
                    </button>
                    <button
                      type="button"
                      @click="reset()"
                      class="btn btn-success margin-left-5"
                    >
                      Reset
                    </button>
                  </div>
                  <div class="card-body">
                  <b-table-simple
                      responsive
                      show-empty
                      bordered
                      sticky-header="800px"
                      v-if="allUserModel.length > 0"
                    >
                      <b-thead head-variant="light">
                        <b-tr>
                          <b-th class="text-left">Name</b-th>
                          <b-th class="text-left">Email</b-th>
                          <b-th class="text-left">Phone</b-th>
                          <b-th class="text-center">Consumer Type</b-th>
                          <b-th class="text-center">Age</b-th>
                          <b-th class="text-center">Address</b-th>
                          <b-th class="text-center">Enroll Date</b-th>
                          <b-th class="text-center">Bank Link Type</b-th>
                          <b-th class="text-center">Routing No.</b-th>
                          <b-th class="text-center">Account No.</b-th>
                          <b-th class="text-center">Suspected</b-th>
                        </b-tr>
                      </b-thead>
                      <b-tbody v-for="(row, index) in allUserModel" :key="index">
                        <b-tr>
                          <b-td class="text-left text-gray">{{
                            row.name
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.email
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.phone
                          }}</b-td>
                          <b-td class="text-center text-gray">{{
                            capitalizeFirstLetter(row.consumer_type)
                          }}</b-td>
                          <b-td class="text-center text-gray" v-html="row.age"></b-td>
                          <b-td width="15%" class="text-center text-gray">{{
                            row.address
                          }}</b-td>
                          <b-td class="text-center text-gray">{{
                            row.enroll_date
                          }}</b-td>
                          <b-td class="text-center text-gray">{{
                            row.bank_link_type
                          }}</b-td>
                          <b-td class="text-center text-gray">{{
                            row.routing_no
                          }}</b-td>
                          <b-td class="text-center text-gray" :id="'acc_'+row.edit">
                              {{row.hidden_account_no}}
                              <a :data-acc-id="row.edit" class="showaccno custom-edit-btn" title="Show Account No." variant="outline-success" style="border:none" v-if="row.account_no && row.account_no != 'N/A'"><i class="nav-icon fas fa-eye-slash"></i></a>
                          </b-td>
                          <b-td class="text-center text-gray" :id="'org_acc_'+row.edit" style="display:none;">
                              {{row.account_no}}
                              <a :data-acc-id="row.edit" class="hideaccno custom-edit-btn" title="Hide Account No." variant="outline-success" style="border:none"><i class="nav-icon fas fa-eye"></i></a>
                          </b-td>
                          <b-td class="text-center text-gray">
                            <label class="switch"><input class="mark-as-auspected" :id="'switch_'+row.edit" :checked="row.is_suspected" type="checkbox" :data-id="row.edit" :data-val="row.is_suspected"><span class="slider round"></span></label>
                          </b-td>
                        </b-tr>
                      </b-tbody>
                    </b-table-simple>
                    <p v-else>No data displayed. Please refine your search criteria.</p>
                    </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>

  </div>
</div>
</template>
<script>
import moment from "moment";
import api from "@/api/user.js";
import { validationMixin } from "vuelidate";
import { saveAs } from "file-saver";
import { required, minLength,decimal } from "vuelidate/lib/validators";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "../CustomLoader/CanPayLoader.vue"
import commonConstants from "@/common/constant.js";

export default {
  mixins: [validationMixin],
  data() {
    return {
      allUserModel: {},
      consumer:"",
      phone_no:"",
      email:"",
      loading:false,
      constants: commonConstants,
    };
  },
  components: {
    HourGlass,
    CanPayLoader
  },
  created() {
    this.toggleSupectedConsumer();
    this.showaccno();
    this.hideaccno();
  },
  methods: {
    capitalizeFirstLetter(str) {
      return str.charAt(0).toUpperCase() + str.slice(1);
    },
    showaccno(){
      $(document).on("click", ".showaccno", function (e) {
        var id = $(e.currentTarget).attr("data-acc-id");
        $('#org_acc_'+id).fadeIn(500);
        $('#acc_'+id).hide();
      });
    },
    hideaccno(){
      $(document).on("click", ".hideaccno", function (e) {
        var id = $(e.currentTarget).attr("data-acc-id");
        $('#org_acc_'+id).hide();
        $('#acc_'+id).fadeIn(500);
      });
    },
    toggleSupectedConsumer() {
      var self = this;
      $(document).on("click", ".mark-as-auspected", function(e) {
        var request = {
          id: $(e.currentTarget).attr("data-id"),
          val: $(e.currentTarget).attr("data-val")
        };
        Vue.swal({
            title: request.val == 1 ? "Are you sure to remove this Consumer from Suspected Fraud List?" : "Are you sure to add this Consumer to Suspected Fraud List?",
            text: request.val == 1 ? "The active account will be whitelisted for this consumer but will remain blacklisted for others!" : "All the bank accounts of this consumer will be marked as balcklisted.",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#149240",
            confirmButtonText: request.val == 1 ? "Yes, remove the consumer!" : "Yes, add the consumer!",
            cancelButtonText: "No, cancel it!",
            closeOnConfirm: true,
            closeOnCancel: true,
            width: '800px'
            }).then((result) => {
                if (result.isConfirmed) {
                    api
                    .toggleSupectedConsumer(request)
                    .then(response => {
                        if ((response.code == 200)) {
                            Vue.swal("Done!", response.message, "success");
                            self.searchRegisteredConsumer();
                        } else {
                            Vue.swal(response.message, '', 'error')
                        }
                    })
                    .catch(err => {
                        Vue.swal(err.response.data.message, '', 'error')
                    });
                }else{
                    var prop_val = request.val == 1 ? true : false;
                    $('#switch_'+request.id).prop('checked', prop_val);
                }
            })
      });
    },
    reset(){
      var self = this;
      self.consumer = "";
      self.phone_no = "";
      self.email = "";
      $("#end-date").val('');
      $("#start-date").val('');
    },
    searchRegisteredConsumer(){
      var self = this;
      if((self.consumer).trim().length < 3 && $("#phone_no").val().trim() === '' &&  $("#email").val().trim() === '' && $("#end-date").val() == '' &&  $("#start-date").val() == ''){
        error("Please provide Consumer name (Min 3 chars) or email(exact) or phone no(exact) or Date Range");
        return false;
      }
      if (
        moment($("#start-date").val()).format("YYYY-MM-DD") >
        moment().format("YYYY-MM-DD") && $("#start-date").val()!= ''
      ) {
        error("Start date cannot be from future.");
        return false;
      }
      if (
        moment($("#end-date").val()).format("YYYY-MM-DD") >
        moment().format("YYYY-MM-DD") && $("#end-date").val()!= ''
      ) {
        error("End date cannot be from future.");
        return false;
      }
      if($("#start-date").val()!=''){
        var from_date = moment($("#start-date").val()).format("YYYY-MM-DD");
      }else{
        var from_date = '';
      }
      if($("#end-date").val()!=''){
        var to_date = moment($("#end-date").val()).format("YYYY-MM-DD");
      }else{
        var to_date = '';
      }
      var request = {
        consumer: self.consumer,
        email:self.email,
        phone_no:self.phone_no,
        start_date: from_date,
        end_date: to_date,
      };
      if(request.start_date > request.end_date){
        error("To Date cannot be greater than From date");
        return false;
      }
      self.loading = true;
      api
      .searchConsumers(request)
      .then(function (response) {
        if (response.code == 200) {
          self.allUserModel = response.data;
          self.loading = false;
        } else {
          error(response.message);
          self.loading = false;
        }
      })
      .catch(function (error) {
        // error(error);
        self.loading = false;
      });
    },
    exportReport() {
      var self = this;
      self.loading = true;
      api
        .exportSuspectedConsumersReport()
        .then(function (response) {
          var FileSaver = require("file-saver");
          var blob = new Blob([response], {
            type: "application/xlsx",
          });
          FileSaver.saveAs(
            blob,
            moment().format("MM/DD/YYYY") + "_CanPay_Suspected_Consumers_Report.xlsx"
          );
          self.loading = false;
        })
        .catch(function (error) {
          // error(error);
          self.loading = false;
        });
    },
  },
  mounted() {
    $("#start-date,#end-date").datepicker({
      format: "mm/dd/yyyy",
      autoclose: true,
      todayHighlight: true,
    });
    document.title = "CanPay - Suspected Consumers";
  },
};
</script>
