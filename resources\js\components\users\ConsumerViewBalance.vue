<template>
<div>
  <div v-if="loading">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Consumer View Balance</h3>
                </div>

                <!-- /.card-header -->
                <div class="card-body">
                  <div class="row">
                  <div class="col-md-4">
                    <div class="form-group">
                      <multiselect
                        v-model="selectedConsumer"
                        placeholder="Select Consumer (Min 3 chars)"
                        id="consumer"
                        label="consumer_name"
                        :options="consumerList"
                        :loading="isLoading"
                        :internal-search="false"
                        @search-change="getConsumers"
                          >
                    </multiselect>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="form-control"
                        placeholder="Phone No (Exact)"
                        id="phone_no"
                        v-model="phone_no"
                      />
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="form-control"
                        placeholder="Email (Exact)"
                        id="email"
                        v-model="email"
                      />
                    </div>
                  </div>
                  <div class="col-md-4">
                      <div class="form-group">
                        <input
                          autocomplete="off"
                          class="start-date form-control"
                          placeholder="Start Date"
                          id="start-date"
                          onkeydown="return false"
                        />
                      </div>
                    </div>
                    <div class="col-md-4">
                      <button
                      type="button"
                      class="btn btn-success"
                      @click="viewConsumerBalance()"
                    >
                      Search
                    </button>
                    <button
                      type="button"
                      @click="reset()"
                      class="btn btn-success margin-left-5"
                    >
                      Reset
                    </button>
                      </div>
                </div>
                </div>
                  <div class="card-footer">
                    
                  </div>
                  <div class="card-body">
                    <div class="row" v-if="consumer_name != ''">
                      <div class="col-md-4">
                        <div class="form-group">
                          <label>Name : </label><span v-html="consumer_name"></span>
                        </div>
                      </div>
                      <div class="col-md-4">
                        <div class="form-group">
                          <label>Phone : </label><span v-html="userDetails.phone"></span>
                        </div>
                      </div>
                      <div class="col-md-4">
                        <div class="form-group">
                          <label>Email : </label><span v-html="userDetails.email"></span>
                        </div>
                      </div>
                    </div>
                  <b-table-simple
                      responsive
                      show-empty
                      bordered
                      sticky-header="800px"
                      v-if="balancesModel.length > 0"
                    >
                      <b-thead head-variant="light">
                        <b-tr>
                          <b-th class="text-left">Source</b-th>
                          <b-th class="text-left">Balance ($)</b-th>
                          <b-th class="text-left">Available Balance ($)</b-th>
                          <b-th class="text-left">Balance Fetch Time (PST)</b-th>
                        </b-tr>
                      </b-thead>
                      <b-tbody v-for="(row, index) in balancesModel" :key="index">
                        <b-tr>
                          <b-td class="text-left text-gray">{{
                            row.source
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.balance
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.available_balance
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.balance_fetch_time
                          }}</b-td>
                        </b-tr>
                      </b-tbody>
                    </b-table-simple>
                    <p v-else>No balances found for the consumer on the searched date.</p>
                    </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
    
  </div>
</div>
</template>
<script>
import moment from "moment";
import api from "@/api/user.js";
import { validationMixin } from "vuelidate";
import { required, minLength,decimal } from "vuelidate/lib/validators";
import commonConstants from "@/common/constant.js";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "../CustomLoader/CanPayLoader.vue"
export default {
  mixins: [validationMixin],
  data() {
    return {
      balancesModel:{},
      showReloadBtn:false,
      phone_no:"",
      email:"",
      start_date:"",
      loading:false,
      userDetails:[],
      consumer_name:"",
      consumerList: [],
      selectedConsumer: null,
      isLoading: false,
    };
  },
  components: {
    HourGlass,
    CanPayLoader
  },
  created() {
  },
  methods: {
    reset(){
      var self = this;
      self.consumer = "";
      self.phone_no = "";
      self.email = "";
    },
    //get the consumer list
    getConsumers(searchtxt) {
      var self = this;
      if(searchtxt.length >= 3){
        self.isLoading = true;
        var request = {
          searchtxt: searchtxt,
        };
        api
          .getConsumersIncUnregistered(request)
          .then(function (response) {
            if (response.code == 200) {
              self.consumerList = response.data;
              self.isLoading = false;
            } else {
              error(response.message);
            }
          })
          .catch(function (error) {
            error(error);
          });
      }
    },
    viewConsumerBalance(){
      var self = this;
      if(!self.selectedConsumer && $("#phone_no").val().trim() === '' &&  $("#email").val().trim() === ''){
        error("Please select either customer or phone no(exact). or email(exact).");
        return false;
      }
      if(!self.selectedConsumer){
        var consumer = '';
      }else{
        var consumer = self.selectedConsumer.consumer_name;
      }
      self.consumer_name = consumer;
      var request = {
        consumer: consumer,
        email:self.email,
        phone_no:self.phone_no,
        start_date:moment($("#start-date").val()).format("YYYY-MM-DD"),
      };
      self.userDetails = [];
      self.loading = true;
      api
      .viewConsumerBalance(request)
      .then(function (response) {
        if (response.code == 200) {
          self.balancesModel = response.data.balanceFetchArr;
          self.userDetails = response.data.userDetails;
          if(self.userDetails.length != 0){
            let nameArray = [self.userDetails.first_name, self.userDetails.middle_name, self.userDetails.last_name];
            nameArray = nameArray.filter(Boolean);
            self.consumer_name = nameArray.join(' ');
          }
          self.loading = false;
        } else {
          error(response.message);
          self.loading = false;
        }
      })
      .catch(function (error) {
        // error(error);
        self.loading = false;
      });
    },
  },
  mounted() {
    $("#start-date").datepicker({
      format: "mm/dd/yyyy",
      autoclose: true,
      todayHighlight: true,
    });
    $("#start-date").datepicker("setDate", new Date());
    document.title = "CanPay - Consumer View Balance";
  },
};
</script>
