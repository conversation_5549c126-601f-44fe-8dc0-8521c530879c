<?php

namespace App\Http\Factories\Webhook;

use App\Models\TransactionDetails;
use App\Models\TransactionWebhookDetail;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;

class WebhookFactory implements WebhookInterface
{
  /**
     * Create a new command instance.
    */
    public function __construct()
    {
    }

    /**
     * sendToWebHook
     * send transaction to merchant
     * @param  mixed $transaction
     * @return void
     */
    public function sendToWebHook($transaction)
    {
        $client = new Client();
        $params['api'] = $transaction->api_url;
        $params['headers'] = [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ];
        $params['body'] = [
            'intent_id' => $transaction->intent_id,
            'amount' => $transaction->tot_amount,
            'tip_amount' => $transaction->tip_amount,
            'status' => $transaction->status,
            'expiry_date_for_book_now' => $transaction->expiry_date_for_book_now ? $transaction->expiry_date_for_book_now : '',
            'passthrough_param' => json_decode($transaction->passthrough_param, true),
        ];
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Calling merchant webhook with body: ", $params['body']);
        try {
            $response = $client->request('POST', $params['api'], [
                'headers' => $params['headers'],
                'json' => json_encode($params['body']),
            ]);
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Merchant Webhook with URL " . $params['api'] . " returned response code : " . $response->getStatusCode());
            // If the response code is anything of 2 then it is a success.
            if (str_starts_with($response->getStatusCode(), 2)) {
                $this->_updateWebhookStatus($transaction->transaction_id, 1);
            } else { // else the call is not a successful one
                $this->_updateWebhookStatus($transaction->transaction_id, 0);
            }
        } catch (\Exception $ex) {
            Log::error(__METHOD__ . "(Line: " . __LINE__ . ") - " . "Exception while calling Merchant Webhook.", ['Exception' => $ex]);
            $this->_updateWebhookStatus($transaction->transaction_id, 0);
        }
    }
    
    /**
     * _updateWebhookStatus
     * Update the webhook status for the transaction
     * @param  mixed $id
     * @param  mixed $status
     * @return void
     */
    private function _updateWebhookStatus($id, $status)
    {
        // Insert in transaction_webhook_details table
        $updateWebhook = new TransactionWebhookDetail();
        $updateWebhook->transaction_id = $id;
        $updateWebhook->webhook_status = $status;
        $updateWebhook->save();
        if ($status == 1) {
            // Update the transaction row in transaction_details table if status is 1
            $transaction = TransactionDetails::find($id);
            $transaction->webhook_called = 1;
            $transaction->save();
        }
        return true;
    }
    
    /**
     * modificationWebhookCall
     * after consumer accept/reject call webhook
     * @param  mixed $transactionId
     * @param  mixed $status
     * @param  mixed $expiry_date_for_book_now use for Accept Payment
     * @return void
     */
    public function modificationWebhookCall($transactionId, $status, $expiry_date_for_book_now = '')
    {
        $web_hook_column_name = config('app.api_environment') . '_webhook_url';
        $transaction = TransactionDetails::select('transaction_details.id as transaction_id', 'transaction_details.transaction_number as transaction_number', 'transaction_details.intent_id as intent_id', 'transaction_details.amount as tot_amount', 'transaction_details.tip_amount as tip_amount', 'emi.passthrough_param as passthrough_param', 'rmm.' . $web_hook_column_name . ' as api_url')
                ->join('terminal_master as tm', 'tm.id', '=', 'transaction_details.terminal_id')
                ->join('merchant_stores as ms', 'ms.id', '=', 'tm.merchant_store_id')
                ->join('registered_merchant_master as rmm', 'rmm.id', '=', 'ms.merchant_id')->join('ecommerce_merchant_intents as emi', 'emi.intent_id', '=', 'transaction_details.intent_id')
                ->where('transaction_details.id', $transactionId)->where('rmm.'.$web_hook_column_name, '!=' , null)->where('transaction_ref_no', null)->first();
        if($transaction){
            $transaction->status = $status;
            $transaction->expiry_date_for_book_now = $expiry_date_for_book_now;
            $this->sendToWebHook($transaction);
        }
    }

}
