<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Factories\EmailExecutor\EmailExecutorFactory;
use App\Models\LoginDetail;
use App\Models\OtpAccessToken;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use JWTAuth;
use Tymon\JWTAuth\Exceptions\JWTException;

class LoginController extends Controller
{
    public function __construct(Request $request)
    {
        $this->request = $request;
        $this->emailexecutor = new EmailExecutorFactory();
    }
    /**
     * Canpay Admin Login API
     * @return mixed
     */
    public function login()
    {
        $credentials = $this->request->only('email', 'password');
        $current_date = Carbon::now();
        try {
            //fetching the role details to check if the user is Admin
            $role_details = DB::table('users')->join('user_roles', 'users.role_id', '=', 'user_roles.role_id')
                ->join('status_master', 'users.status', '=', 'status_master.id')
                ->select('users.*', 'status_master.code as code', 'user_roles.role_name as role_name')
                ->where('users.email', $credentials['email'])->first();
            // get the login attempts from login details table
            $login_details = LoginDetail::where('email', $credentials['email'])->where('created_at', '>=', Carbon::now()->startOfDay())->where('created_at', '<', Carbon::now()->endOfDay())->limit(config('app.max_login_count'))->orderBy('created_at', 'DESC')->get();

            if (!empty($role_details) && ($role_details->role_name == SUPERADMIN || $role_details->role_name == ADMIN || $role_details->role_name == HELPDESK || $role_details->role_name == REPORT_ADMIN)) {
                if (!$token = JWTAuth::attempt($credentials)) { //chekcing for invalid credentials
                    // Checking if temporary password matches
                    if ($role_details->temp_password != null && Hash::check($credentials['password'], $role_details->temp_password) && $current_date->lte(Carbon::parse($role_details->temp_password_expiry_time))) {
                        // Update temp password to null and store the temp password to original password
                        $user = User::find($role_details->user_id);
                        $user->password = Hash::make($credentials['password']);
                        $user->temp_password = null;
                        $user->temp_password_expiry_time = null;
                        $user->save();
                        $token = JWTAuth::attempt($credentials);
                        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "User logged in successfully with temporary password with email id: " . $credentials['email']);
                        $role_details->token = $token;
                        $message = trans('message.login_success');
                        $this->_insertIntoLoginDetails(1);
                        return renderResponse(SUCCESS, $message, $role_details);
                    }

                    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Trying to login with invalid credentials for email id: " . $credentials['email']);
                    $message = trans('message.invalid_credentials');
                    $send_otp = null;
                    /**
                     *  checking and precautions for BRUTE FORCE ATTACK
                     */
                    // if user has already tried to login with wrong passwords for mentioned times we need to promt them for otp and send the otp to their email
                    Log::debug(in_array(1, array_column(json_decode(json_encode($login_details)), 'login_successful')));

                    if (!($this->request->get('otp')) && isset($login_details[config('app.max_login_count') - 1]) && !(in_array(1, array_column(json_decode(json_encode($login_details)), 'login_successful')))) {
                        $this->_generateAndEmailOtp($role_details);
                        $send_otp = true;
                    }

                    $this->_insertIntoLoginDetails(0);
                    return renderResponse(599, $message, $send_otp); //send otp flag determines the UI changes in the front end
                }
                if ($role_details->code != USER_ACTIVE) {
                    $message = trans('message.account_not_active');
                    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Failed Login of " . $role_details->role_name . " with Email ID : " . $credentials['email'] . " due to " . ucfirst($role_details->status) . " Status");
                    $this->_insertIntoLoginDetails(0);
                    return renderResponse(FAIL, $message, null); // Exception returned
                }
                // if otp exists that means the user have had failed login attempts checking for otp validation is required
                $otp = $this->request->get('otp');
                if (!empty($otp) && (!$this->_checkOTPValidity())) {
                    $message = trans('message.invalid_otp');
                    $this->_insertIntoLoginDetails(0);
                    return renderResponse(FAIL, $message, null);
                }
            } else {
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Access denied for email id: " . $credentials['email']);
                $message = trans('message.invalid_credentials');
                $this->_insertIntoLoginDetails(0);
                return renderResponse(FAIL, $message, null);
            }
        } catch (JWTException $e) {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Token could not be created.", [EXCEPTION => $e]);
            $message = trans('message.token_error');
            $this->_insertIntoLoginDetails(0);
            return renderResponse(FAIL, $message, null);
        }

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "User logged in successfully with email id: " . $credentials['email']);
        // Update temp password to null as the user logged in with the existing password
        $user = User::find($role_details->user_id);
        $user->temp_password = null;
        $user->temp_password_expiry_time = null;
        $user->save();
        $role_details->token = $token;
        $message = trans('message.login_success');
        $this->_insertIntoLoginDetails(1);
        return renderResponse(SUCCESS, $message, $role_details);
    }

    /**
     * This function stores the login details into the table
     */
    private function _insertIntoLoginDetails($login_successful)
    {
        $detail = new LoginDetail();
        $detail->email = $this->request->get('email');
        $detail->login_successful = $login_successful;
        $detail->save();
    }
    /**
     * This function checks Login OTP validity in case of multiple failed login attempts
     */
    private function _checkOTPValidity()
    {
        //fetch the last otp generated for given email
        $otp = OtpAccessToken::where('email', $this->request->get('email'))
            ->orderBy('created_at', 'DESC')
            ->first();
        //check if the otp is a valid otp
        if ((Carbon::now()->lte(Carbon::parse($otp->expiration_datetime))) && ($otp->value == $this->request->get('otp')) && ($otp->verified == 0)) {
            $otp->verified = 1;
            $otp->save();
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "OTP verified successsfully for email : " . $this->request->get('email'));
            return true;
        } else { //if otp is invalid then return response accordingly
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Invalid otp for email : " . $this->request->get('email'));
            $message = trans('message.invalid_otp');
            $this->_insertIntoLoginDetails(0);
            return false;
        }
    }
    /**
     * This function generates and stores otp into db and then emails it to given email
     */
    private function _generateAndEmailOtp($role_details)
    {
        //generate otp and save in the database
        $otp = new OtpAccessToken();
        $otp->email = $this->request->get('email');
        $otp->value = (string) rand(100000, 999999);
        $otp->expiration_datetime = Carbon::now()->addMinutes(intval(config('app.login_otp_validity')));
        $otp->type = "login_otp";
        $otp->save();

        $params['otp'] = $otp->value;
        $params['email'] = $this->request->get('email');
        $params['user_id'] = $role_details->user_id;
        //email otp
        $this->emailexecutor->consecutiveFailedLoginOTPEmail($params);
    }

    /**
     * forgotPassword
     * This Function is used to set a temporary password for Corporate Parent and Regional Manager
     * @return void
     */
    public function forgotPassword(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Temporary password set porcess started.");
        // Validating input request
        $this->validate($request, [
            'email' => VALIDATION_REQUIRED,
        ]);

        $checkUserExists = User::join('user_roles', 'users.role_id', '=', 'user_roles.role_id')->select('users.user_id', 'user_roles.role_name')->where('email', $request->get('email'))->first();
        if (empty($checkUserExists)) {
            // If no user found for given inputs return error response
            $message = trans('message.user_not_found');
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No user found for Email Id: " . $request->get('email'));
            // Exception Returned
            return renderResponse(FAIL, $message, null);
        }

        if ($checkUserExists->role_name != SUPERADMIN && $checkUserExists->role_name != ADMIN && $checkUserExists->role_name != HELPDESK && $checkUserExists->role_name != REPORT_ADMIN) {
            // If role mismatched return error response
            $message = trans('message.role_mismatched');
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Forget password request failed due to inavalid role: " . $checkUserExists->role_name . " for Email Id: " . $request->get('email'));
            // Exception Returned
            return renderResponse(FAIL, $message, null);
        }

        $expirationTime = new Carbon();
        $expirationTime->addMinutes(intval(config('app.temp_password_validity')));
        $password = substr(md5(time()), 0, 8);
        $temp_password_array = [
            'temp_password' => Hash::make($password),
            'temp_password_expiry_time' => $expirationTime,
        ];

        DB::beginTransaction();
        try {
            User::where('user_id', $checkUserExists->user_id)->update($temp_password_array);
            DB::commit();
            $email_params = [
                'user_id' => $checkUserExists->user_id,
                'password' => $password,
            ];

            $validationResponse = $this->_sendEmailValidation($checkUserExists);
            if ($validationResponse['success'] == false) {
                return renderResponse(FAIL, $validationResponse['message'], ['time_left' => $validationResponse['time_left']]);
            }

            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Sending temp password email to: " . $request->get('email'));
            $this->emailexecutor->forgotPassword($email_params);
            $message = trans('message.password_reset_succsessful');
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Password reset request successful for User Email : " . $request->get('email'));
            return renderResponse(SUCCESS, $message, ['time_left' => $validationResponse['time_left']]); // API Response returned with 200 status
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while resetting password for Email ID: " . $request->get('email'), [EXCEPTION => $e]);
            DB::rollback();
            $message = trans('message.password_reset_fail');
            return renderResponse(FAIL, $message, $e);
        }
    }

    public function _sendEmailValidation($checkUserExists)
    {

        // Store Send Email Count
        $SEND_EMAIL_AFTER_1st_ATTEMPT = config('app.send_email_after_1st_attempt');
        $SEND_EMAIL_AFTER_2nd_ATTEMPT = config('app.send_email_after_2nd_attempt');
        $SEND_EMAIL_AFTER_3rd_ATTEMPT = config('app.send_email_after_3rd_attempt');
        $SEND_EMAIL_COUNT = config('app.send_email_count');
        $SEND_EMAIL_BLOCK_DURATION = config('app.send_email_block_duration');

        $forgot_password_details = DB::table('password_reset')->where('user_id', $checkUserExists->user_id)
            ->orderBy('created_at', 'DESC');

        $attemptNumber = 1;
        $timeLeft = $SEND_EMAIL_AFTER_1st_ATTEMPT;

        for ($x = 1; $x <= $SEND_EMAIL_COUNT; $x++) {
            if (
                $x == 1 &&
                $forgot_password_details->exists() &&
                $forgot_password_details->first()->attempt == 1 &&
                Carbon::parse($forgot_password_details->first()->created_at)->addSeconds($SEND_EMAIL_AFTER_1st_ATTEMPT)->format('Y-m-d H:i:s') <= Carbon::now()->format('Y-m-d H:i:s')
            ) {
                $attemptNumber = 2;
                $timeLeft = $SEND_EMAIL_AFTER_2nd_ATTEMPT;
            }
            if (
                $x == 2 &&
                $forgot_password_details->exists() &&
                $forgot_password_details->first()->attempt == 2 &&
                Carbon::parse($forgot_password_details->first()->created_at)->addSeconds($SEND_EMAIL_AFTER_2nd_ATTEMPT)->format('Y-m-d H:i:s') <= Carbon::now()->format('Y-m-d H:i:s')
            ) {
                $attemptNumber = 3;
                $timeLeft = $SEND_EMAIL_AFTER_3rd_ATTEMPT;
            }

            if (
                $x == 3 &&
                $forgot_password_details->exists() &&
                $forgot_password_details->first()->attempt == 3 &&
                Carbon::parse($forgot_password_details->first()->created_at)->addSeconds($SEND_EMAIL_AFTER_3rd_ATTEMPT)->format('Y-m-d H:i:s') <= Carbon::now()->format('Y-m-d H:i:s')
            ) {
                $attemptNumber = 4;
                $timeLeft = $SEND_EMAIL_AFTER_3rd_ATTEMPT;
            }
            if (
                $x > 3 &&
                $x < $SEND_EMAIL_COUNT &&
                $forgot_password_details->exists() &&
                $forgot_password_details->first()->attempt == $x &&
                Carbon::parse($forgot_password_details->first()->created_at)->addSeconds($SEND_EMAIL_AFTER_3rd_ATTEMPT)->format('Y-m-d H:i:s') <= Carbon::now()->format('Y-m-d H:i:s')
            ) {
                $attemptNumber = $x + 1;
                $timeLeft = $SEND_EMAIL_AFTER_3rd_ATTEMPT;
            }
        }

        if (
            $forgot_password_details->exists() &&
            $forgot_password_details->first()->attempt == 1 &&
            Carbon::parse($forgot_password_details->first()->created_at)->addSeconds($SEND_EMAIL_AFTER_1st_ATTEMPT)->format('Y-m-d H:i:s') >= Carbon::now()->format('Y-m-d H:i:s')
        ) {
            $message = trans('message.forgot_password_email_validation', ['atr' => Carbon::now()->subSeconds($SEND_EMAIL_AFTER_1st_ATTEMPT)->diffInSeconds($forgot_password_details->first()->created_at)]);
            return [
                'success' => false,
                'message' => $message,
                'time_left' => Carbon::now()->subSeconds($SEND_EMAIL_AFTER_1st_ATTEMPT)->diffInSeconds($forgot_password_details->first()->created_at),
            ];
        } else if (
            $forgot_password_details->exists() &&
            $forgot_password_details->first()->attempt == 2 &&
            Carbon::parse($forgot_password_details->first()->created_at)->addSeconds($SEND_EMAIL_AFTER_2nd_ATTEMPT)->format('Y-m-d H:i:s') >= Carbon::now()->format('Y-m-d H:i:s')
        ) {
            $message = trans('message.forgot_password_email_validation', ['atr' => Carbon::now()->subSeconds($SEND_EMAIL_AFTER_2nd_ATTEMPT)->diffInSeconds($forgot_password_details->first()->created_at)]);
            return [
                'success' => false,
                'message' => $message,
                'time_left' => Carbon::now()->subSeconds($SEND_EMAIL_AFTER_2nd_ATTEMPT)->diffInSeconds($forgot_password_details->first()->created_at),
            ];
        } else if (
            $forgot_password_details->exists() &&
            $forgot_password_details->first()->attempt == 3 &&
            Carbon::parse($forgot_password_details->first()->created_at)->addSeconds($SEND_EMAIL_AFTER_3rd_ATTEMPT)->format('Y-m-d H:i:s') >= Carbon::now()->format('Y-m-d H:i:s')
        ) {
            $message = trans('message.forgot_password_email_validation', ['atr' => Carbon::now()->subSeconds($SEND_EMAIL_AFTER_3rd_ATTEMPT)->diffInSeconds($forgot_password_details->first()->created_at)]);
            return [
                'success' => false,
                'message' => $message,
                'time_left' => Carbon::now()->subSeconds($SEND_EMAIL_AFTER_3rd_ATTEMPT)->diffInSeconds($forgot_password_details->first()->created_at),
            ];
        } else if (
            $forgot_password_details->exists() &&
            ($forgot_password_details->first()->attempt > 3 &&
                $forgot_password_details->first()->attempt < $SEND_EMAIL_COUNT) &&
            Carbon::parse($forgot_password_details->first()->created_at)->addSeconds($SEND_EMAIL_AFTER_3rd_ATTEMPT)->format('Y-m-d H:i:s') >= Carbon::now()->format('Y-m-d H:i:s')
        ) {
            $message = trans('message.forgot_password_email_validation', ['atr' => Carbon::now()->subSeconds($SEND_EMAIL_AFTER_3rd_ATTEMPT)->diffInSeconds($forgot_password_details->first()->created_at)]);
            return [
                'success' => false,
                'message' => $message,
                'time_left' => Carbon::now()->subSeconds($SEND_EMAIL_AFTER_3rd_ATTEMPT)->diffInSeconds($forgot_password_details->first()->created_at),
            ];
        } else if (
            $forgot_password_details->exists() &&
            $forgot_password_details->first()->attempt == $SEND_EMAIL_COUNT &&
            Carbon::parse($forgot_password_details->first()->created_at)->addSeconds($SEND_EMAIL_BLOCK_DURATION)->format('Y-m-d H:i:s') >= Carbon::now()->format('Y-m-d H:i:s')
        ) {
            $message = trans('message.forgot_password_email_validation', ['atr' => Carbon::now()->subSeconds($SEND_EMAIL_BLOCK_DURATION)->diffInSeconds($forgot_password_details->first()->created_at)]);
            return [
                'success' => false,
                'message' => $message,
                'time_left' => Carbon::now()->subSeconds($SEND_EMAIL_BLOCK_DURATION)->diffInSeconds($forgot_password_details->first()->created_at),
            ];
        }

        DB::table('password_reset')->insert([
            'id' => generateUUID(),
            'user_id' => $checkUserExists->user_id,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
            'attempt' => $attemptNumber,
        ]);

        return [
            'success' => true,
            'time_left' => $timeLeft,
        ];
    }
}
