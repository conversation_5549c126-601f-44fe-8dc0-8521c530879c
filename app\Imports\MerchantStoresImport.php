<?php

namespace App\Imports;

use App\Http\Clients\GooglePlacesHttpClient;
use App\Models\AccessLevelMaster;
use App\Models\MerchantApiKeyMap;
use App\Models\MerchantStores;
use App\Models\RegisteredMerchantMaster;
use App\Models\StatusMaster;
use App\Models\StoreTransactionTypeMap;
use App\Models\StoreUserMap;
use App\Models\TerminalMaster;
use App\Models\TimezoneMaster;
use App\Models\TransactionPostingDecisionTable;
use App\Models\User;
use Carbon\Carbon;
use DateTime;
use DateTimeZone;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;

class MerchantStoresImport implements ToModel, WithHeadingRow, WithBatchInserts, WithValidation, WithChunkReading
{
    use Importable;
    private $rows = 0;
    private $updated_rows = 0;

    public function __construct()
    {
        $this->googleplaces = new GooglePlacesHttpClient();
    }

    /**
     * @param array $row
     * This function actully imports the data as row from Excel Sheet. Here we used the WithHeadingRow to get the Data with Heading. Do Not try to get the rows with index.
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row)
    {
        $merchantname = $row['merchantname'];
        if (!empty($merchantname)) {
            $timezone_details = '';
            if (!empty($row['street'])) {
                $address_string = str_replace(' ', '+', $row['street'] . ',' . $row['city'] . ',' . $row['state'] . ',' . $row['zip']);
                $address = preg_replace('/[^A-Za-z0-9\+,]/', '', $address_string);
                $response = $this->googleplaces->getLatLong($address);
                $result = json_decode($response, true);
                if (!empty($result['results'])) {
                    $lat = $result['results'][0]['geometry']['location']['lat'];
                    $long = $result['results'][0]['geometry']['location']['lng'];
                    $timezone_details = $this->_getTimezone($lat, $long);
                }
            }
            if ($timezone_details) {
                try {
                    $storeCode = $row['storecode'];
                    $actualStoreCode = substr($storeCode, 0, 4);
                    $storeTransactionType = substr($storeCode, 4, 1);
                    $storeTransactionTypeId = getTransactionTypeId($storeTransactionType);
                    // Check if the merchant is already added in Database
                    $checkMerchantExists = RegisteredMerchantMaster::where('merchant_name', $merchantname)->first();
                    if (!empty($checkMerchantExists)) {

                        $checkStoreExists = MerchantStores::where('store_id', $actualStoreCode)->first();
                        if (empty($checkStoreExists)) {
                            // Check if timezone exists in master table. If not then insert it and map it with store.
                            $checkTimezoneExists = TimezoneMaster::where('timezone_name', $timezone_details)->first();
                            if (empty($checkTimezoneExists)) {
                                $timezone_master = new TimezoneMaster();
                                $timezone_master->timezone_name = $timezone_details;
                                $timezone_master->save();
                                $timezone_id = $timezone_master->id;

                                // Insert the new timezone in Transaction posting decision table
                                $time = Carbon::now()->format('Y-m-d') . ' ' . config('app.transaction_posting_block_end_time') . ':00:00';
                                $date = DateTime::createFromFormat(DB_DATE_FORMAT, $time, new DateTimeZone('America/New_York'));
                                $date->setTimeZone(new DateTimeZone($timezone_master->timezone_name));
                                $transaction_posting_decision_table = new TransactionPostingDecisionTable();
                                $transaction_posting_decision_table->timezone_id = $timezone_id;
                                $transaction_posting_decision_table->start_time = "00:00:01";
                                $transaction_posting_decision_table->end_time = $date->format('H:i:s');
                                $transaction_posting_decision_table->save();
                            } else {
                                $timezone_id = $checkTimezoneExists->id;
                            }
                            $action = 'added and mapped with transaction type';
                            //Fetch the Active Status ID from Status Master table
                            $storestatus = StatusMaster::where('status',ACTIVE)->first();
                            $storestatusid = $storestatus->id;

                            // Insertion started in Merchant Stores Table
                            $store = new MerchantStores();
                            $store->merchant_id = $checkMerchantExists->id;
                            $store->store_id = $actualStoreCode;
                            $store->retailer = $row['storename'];
                            $store->lat = $lat;
                            $store->long = $long;
                            $store->timezone_id = $timezone_id;
                            $store->address = $row['street'];
                            $store->city = $row['city'];
                            $store->state = $row['state'];
                            $store->zip = $row['zip'];
                            $store->contact_no = $row['phone'];
                            $store->email = $row['email'];
                            $store->status = $storestatusid;
                            $store->save();

                            // mapping started for Store and Transaction type
                            $store_transaction_map = new StoreTransactionTypeMap();
                            $store_transaction_map->store_id = $store->id;
                            $store_transaction_map->transaction_type_id = $storeTransactionTypeId;
                            $store_transaction_map->save();

                            // Mapping Creation for Global Canpay Device Manager started in Store User Map
                            $access_level_details = AccessLevelMaster::where('label', ADMIN_RIGHT)->first();
                            // Fetching Global Canpay Device Manager
                            $device_manager = User::where('email', DEFAULT_DEVICE_MANAGER_EMAIL)->first();
                            $storeusermap = new StoreUserMap();
                            $storeusermap->store_id = $store->id;
                            $storeusermap->user_id = $device_manager->user_id;
                            $storeusermap->user_access_level_id = $access_level_details->id;
                            $storeusermap->save();

                            $status = StatusMaster::where("status", TEMPLATE_ACTIVE)->first();
                            $inactive_status = StatusMaster::where("status", TEMPLATE_INACTIVE)->first();
                            $params['status'] = $status->id;
                            $params['merchant_store_id'] = $store->id;
                            if (strtoupper($row['pos_web_identifier']) == TERMINAL_WEB) {
                                $params['terminal_name'] = "WEB " . $store->store_id;
                                $params['is_web'] = 1;
                                $this->_createTerminal($params);
                            } else if (strtoupper($row['pos_web_identifier']) == TERMINAL_POS) {
                                $params['terminal_name'] = "POS " . $store->store_id;
                                $params['is_web'] = 0;
                                $this->_createTerminal($params);
                            } else {
                                $params['terminal_name'] = "WEB " . $store->store_id;
                                $params['is_web'] = 1;
                                $this->_createTerminal($params);
                                $params['terminal_name'] = "POS " . $store->store_id;
                                $params['is_web'] = 0;
                                $this->_createTerminal($params);
                            }

                            // Check if API Key already exists for that merchant
                            $checkAPiKeyexists = MerchantApiKeyMap::where(['merchant_id' => $store->merchant_id, 'store_id' => $store->id])->first();
                            if (empty($checkAPiKeyexists)) {
                                //create all the api keys which will be used by the 3rd party merchants during API implementation
                                $key_details = new MerchantApiKeyMap();
                                $key_details->merchant_id = $store->merchant_id;
                                $key_details->store_id = $store->id;
                                $key_details->app_key = config('app.api_environment') . "_key_" . generateRandomString(8);
                                $key_details->api_secret = generateRandomString(8);
                                $key_details->status = strtoupper($row['pos_web_identifier']) == 'N' ? $inactive_status->id : $params['status'];
                                $key_details->save();

                                Log::channel('datamigration')->info(addslashes(__METHOD__) . "(" . LINE . ": " . __LINE__ . ") - " . "App Key and API Secret generated successfully for Store ID: " . $store->store_id . "and Merchant ID: " . $store->merchant_id);
                            }

                            ++$this->rows;
                        } else {
                            // Check if the transaction type for the store exists or not
                            $checkStoreTransactionTypeExsists = StoreTransactionTypeMap::where(['store_id' => $checkStoreExists->id, 'transaction_type_id' => $storeTransactionTypeId])->first();
                            if (empty($checkStoreTransactionTypeExsists)) {
                                // mapping started for Store and Transaction type
                                $store_transaction_map = new StoreTransactionTypeMap();
                                $store_transaction_map->store_id = $checkStoreExists->id;
                                $store_transaction_map->transaction_type_id = $storeTransactionTypeId;
                                $store_transaction_map->save();
                                $action = 'mapped with transaction type';
                            } else {
                                $action = 'insertion skipped due to duplicate entry';
                                insertSkippedDataLog('V1 Merchant', 'merchantname', $row['merchantname'], "Merchant Store insertion skipped due to duplicate entry.", json_encode($row), 'Store List By Merchant Group');
                            }

                            // If Store's timezone is null then update it
                            if ($checkStoreExists->timezone_id == null) {
                                $checkTimezoneExists = TimezoneMaster::where('timezone_name', $timezone_details)->first();
                                if (empty($checkTimezoneExists)) {
                                    $timezone_master = new TimezoneMaster();
                                    $timezone_master->timezone_name = $timezone_details;
                                    $timezone_master->save();
                                    $timezone_id = $timezone_master->id;

                                    // Insert the new timezone in Transaction posting decision table
                                    $time = Carbon::now()->format('Y-m-d') . ' ' . config('app.transaction_posting_block_end_time') . ':00:00';
                                    $date = DateTime::createFromFormat(DB_DATE_FORMAT, $time, new DateTimeZone('America/New_York'));
                                    $date->setTimeZone(new DateTimeZone($timezone_master->timezone_name));
                                    $transaction_posting_decision_table = new TransactionPostingDecisionTable();
                                    $transaction_posting_decision_table->timezone_id = $timezone_id;
                                    $transaction_posting_decision_table->start_time = "00:00:01";
                                    $transaction_posting_decision_table->end_time = $date->format('H:i:s');
                                    $transaction_posting_decision_table->save();
                                } else {
                                    $timezone_id = $checkTimezoneExists->id;
                                }

                                //Update Store details with lat, long and timezone details
                                $storeDetails = array();
                                $storeDetails['actualStoreCode'] = $actualStoreCode;
                                $storeDetails['lat'] = $lat;
                                $storeDetails['long'] = $long;
                                $storeDetails['timezone_id'] = $timezone_id;
                                $storeDetails['rowDetails'] = $row;
                                $this->_updateStoreDetails($storeDetails);
                                
                                $action = 'timezone updated';
                                ++$this->updated_rows;
                            }else{
                                //Update Store details without lat, long and timezone details
                                $storeDetails = array();
                                $storeDetails['actualStoreCode'] = $actualStoreCode;
                                $storeDetails['lat'] = null;
                                $storeDetails['long'] = null;
                                $storeDetails['timezone_id'] = null;
                                $storeDetails['rowDetails'] = $row;
                                $this->_updateStoreDetails($storeDetails);
                                
                                $action = 'timezone updated';
                                ++$this->updated_rows;
                            }
                        }
                    } else {
                        $action = 'insertion skipped due to non availability of Merchant : ' . $merchantname;
                        insertSkippedDataLog('V1 Merchant', 'merchantname', $row['merchantname'], "Merchant Store insertion skipped due to non availability of Merchant : " . $merchantname, json_encode($row), 'Store List By Merchant Group');
                    }

                    Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Merchant Store " . $action . " in Merchant Stores Table for Store ID : " . $actualStoreCode);
                } catch (\Exception $e) {
                    Log::channel('datamigration')->error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured during insertion in Merchant Stores Table for Store ID : " . $actualStoreCode . ".", [EXCEPTION => $e]);
                    insertSkippedDataLog('V1 Merchant', 'merchantname', $row['merchantname'], $e, json_encode($row), 'Store List By Merchant Group');
                }
            } else {
                Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Merchant Store insertion skipped due to non availability of Timezone.");
                insertSkippedDataLog('V1 Merchant', 'merchant_id', $row['merchantname'], "Merchant Store insertion skipped due to non availability of Timezone.", json_encode($row), 'Merchant excel');
            }
        } else {
            Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Merchant Store insertion skipped due to non availability of Merchant name.");
            insertSkippedDataLog('V1 Merchant', 'merchantname', $row['merchantname'], "Merchant Store insertion skipped due to non availability of Merchant name.", json_encode($row), 'Store List By Merchant Group');
        }
    }

    private function _createTerminal($params)
    {
        // Check if Terminal already exists for that store
        $checkTerminalExists = TerminalMaster::where(['merchant_store_id' => $params['merchant_store_id'], 'terminal_name' => $params['terminal_name']])->first();
        if (empty($checkTerminalExists)) {
            // Insertion begins in Terminal Master Table
            $terminal = new TerminalMaster();
            $terminal->merchant_store_id = $params['merchant_store_id'];
            $terminal->terminal_name = $params['terminal_name'];
            $terminal->unique_identification_id = generateUUID();
            $terminal->status = $params['status'];
            $terminal->is_web = $params['is_web'];
            $terminal->save();
        }
    }

    private function _getTimezone($lat, $long)
    {
        $response = $this->googleplaces->getTimezone($lat, $long);
        $result = json_decode($response, true);
        return $result['status'] == 'OK' ? $result['timeZoneId'] : '';
    }

    private function _updateStoreDetails($storeDetails){
        $store = MerchantStores::where('store_id', $storeDetails['actualStoreCode'])->first();
        $store->lat = $storeDetails['lat'] ? $storeDetails['lat'] : $store->lat;
        $store->long = $storeDetails['long'] ? $storeDetails['long'] : $store->long;
        $store->timezone_id = $storeDetails['timezone_id'] ? $storeDetails['timezone_id'] : $store->timezone_id;
        $store->retailer = $storeDetails['rowDetails']['storename'] ? $storeDetails['rowDetails']['storename'] : $store->retailer;
        $store->address = $storeDetails['rowDetails']['street'] ? $storeDetails['rowDetails']['street'] : $store->address;
        $store->city = $storeDetails['rowDetails']['city'] ? $storeDetails['rowDetails']['city'] : $store->city;
        $store->state = $storeDetails['rowDetails']['state'] ? $storeDetails['rowDetails']['state'] : $store->state;
        $store->zip = $storeDetails['rowDetails']['zip'] ? $storeDetails['rowDetails']['zip'] : $store->zip;
        $store->contact_no = $storeDetails['rowDetails']['phone'] ? $storeDetails['rowDetails']['phone'] : $store->contact_no;
        $store->email = $storeDetails['rowDetails']['email'] ? $storeDetails['rowDetails']['email'] : $store->email;
        $store->save();
    }

    public function getRowCount()
    {
        return $this->rows . '|' . $this->updated_rows;
    }

    public function batchSize(): int
    {
        return 1000;
    }

    public function chunkSize(): int
    {
        return 5000;
    }

    public function rules(): array
    {
        return [
            '*.merchantname' => 'required',
        ];
    }
}
