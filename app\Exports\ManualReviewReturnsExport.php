<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Events\AfterSheet;

class ManualReviewReturnsExport implements FromCollection, WithHeadings, ShouldAutoSize, WithEvents
{
    protected $request;

    public function __construct($request)
    {
        $this->request = $request; // Declaring the request variable
    }
    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        $collection_array = $this->request['manualReviewReturns']; // Storing the array received from request

        $returns = [];
        foreach ($collection_array as $return) {
            $nestedData['consumer_name'] = $return->consumer_name;
            $nestedData['phone'] = $return->phone;
            $nestedData['email'] = $return->email;
            $nestedData['local_transaction_time'] = $return->local_transaction_time." (".$return->timezone_name.")";
            $nestedData['amount'] = $return->amount;
            $nestedData['retailer'] = $return->retailer;
            $nestedData['return_code'] = $return->reason_code;

            array_push($returns, $nestedData);
        }

        return collect([
            $returns,
        ]);
    }

    public function headings(): array
    {
        $returnArray = array(
            [
                'Percentage',
                $this->request['percentage'],
            ],
            [],
            [
                'Consumer Name',
                'Phone',
                'Email',
                'Local Transaction Date',
                'Amount	($)',
                'Store',
                'Return Code',
            ],
        );

        return $returnArray;
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $event->sheet->getStyle('A1:G1')->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);

                $event->sheet->getStyle('A3:G3')->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);
                
                //Apply Center Alignment
                $event->sheet->getStyle('A:G')->getAlignment()->applyFromArray(
                    array('horizontal' => 'center')
                );
            },
        ];
    }
}
