<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class FedRoutingNumberMaster extends Model
{

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'routing_no',
        'new_routing_no',
        'bank_name',
        'state',
        'is_finicity',
        'is_akoya',
        'logo_url',
        'frontend_visibility',
        'akoya_provider_id',
        'mx_institution_code',
        'deleted_at',
    ];

    public $timestamps = true;
    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
}
