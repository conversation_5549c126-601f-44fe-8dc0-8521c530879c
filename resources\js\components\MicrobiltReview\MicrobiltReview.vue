<template>
<div>
    <div v-if="loading">
        <CanPayLoader/>
    </div>
    <div class="content-wrapper" style="min-height: 36px">
        <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
            <div class="col-sm-6"></div>
            </div>
        </div>
        </section>
        <div class="hold-transition sidebar-mini">
            <section class="content">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-12">
                            <!-- Card Start -->
                            <div class="card card-success">
                                <!-- Card Title -->
                                <div class="card-header">
                                    <h3 class="card-title">Microbilt Bank Review</h3>
                                </div>

                                <!-- Card Body -->
                                <div class="card-body">
                                    
                                    <!-- Input box for start date -->
                                    <div class="row" >
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <input
                                                    class="start-date form-control"
                                                    placeholder="From Date"
                                                    id="start-date"
                                                    onkeydown="return false"
                                                    autocomplete="off"
                                                />
                                            
                                            </div>
                                        </div>

                                    <!-- Input box for end date -->
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <input
                                                    class="start-date form-control"
                                                    placeholder="To Date"
                                                    id="end-date"
                                                    onkeydown="return false"
                                                    autocomplete="off"
                                                />
                                            
                                            </div>
                                        </div> 
                                    </div>   

                                    <div class="row ml-1">
                                        <p style="font-size:12px; color:red; font-weight:bolder;">Maximum accepted range between start date and end date is 7 days</p>
                                    </div>      

                                </div>

                                <!-- Card for the buttons -->
                                <div class="card-footer">
                                    <button
                                        type="button"
                                        class="btn btn-success"
                                        @click="searchForRestrictedConsumer"
                                    >
                                        Search
                                    </button>
                                    <button
                                        type="button"
                                        @click="reset"
                                        class="btn btn-success margin-left-5"
                                    >
                                        Reset
                                    </button>
                                </div>

                                <!--  To show no data found -->
                                <div class="card-body" v-if="customerDetails.length == 0">
                                    <div>
                                        <p>No data to display.</p>
                                    </div>
                                </div>

                                <!-- To show table  -->
                                <div class="container-fluid p-3" v-if="customerDetails.length>0">
                                    <b-table
                                        bordered
                                        head-variant=light
                                        :items="customerDetails"
                                        :fields="fields"
                                        responsive
                                        :per-page="perPage"
                                        :current-page="currentPage"
                                        aria-controls="consumerBlockedTable"
                                    >
                                        <!-- check for action columns -->

                                        <template #cell(action)="row">
                                            <a id="edit-btn" class="editconsumer custom-edit-btn " align-self="center" @click="editDetails(row)" title="Update Consumer Status" variant="outline-success" style="border:none"><i class="nav-icon fas fa-edit"></i></a>
                                            <a id="view-btn" class="editconsumer custom-edit-btn " align-self="center" @click="viewDetails(row)"  title="View Consumer Details" variant="outline-success" style="border:none"><b-icon icon="eye-fill" aria-hidden="true"></b-icon></a>
                                        </template>


                                    </b-table>

                                    <!-- for pagination -->

                                    <div>
                                        <b-pagination
                                            id="consumerBlockedTable"
                                            v-model="currentPage"
                                            :total-rows="totalRows"
                                            :per-page="perPage"
                                            align="right"
                                            size="sm"
                                            class="mb-3"
                                        ></b-pagination>
                                    </div>


                                </div>

                                <!-- for modal -->

                                <div>
                                    <!-- Modal to edit consumer status -->
                                     <b-modal
                                        ref="modal"
                                        hide-footer
                                        title="Update Consumer Status"
                                     >
                                        <div>
                                            <h5 class="mb-3">Change Consumer Status:</h5>
                                            <v-select
                                                id="change-status"
                                                :options="options"
                                                label="status"
                                                v-model="review_status"
                                                variant="underlined"
                                            >
                                            </v-select>
                                            <br>
                                            <b-button variant="success" style="margin-left:82%; float: right" @click="updateStatus">Update</b-button>
                                        </div>
                                     </b-modal>

                                    <!-- Modal to view Consumer bank Details -->
                                    <b-modal
                                        ref="modalConsumerDetails"
                                        hide-footer
                                        title="Consumer Details"
                                    >
                                        <div>
                                            <div class="mb-3">
                                                <h5>Outcome SSN Required : {{outcome_ssnrequired == '' ?"Not Specified":outcome_ssnrequired}}</h5>
                                            </div>
                                            <h6 class="mb-2">Bank Listing: </h6>
                                            <b-table
                                                bordered
                                                head-variant=light
                                                :items="bankDetails"
                                                :fields="bankFields"
                                                responsive
                                            >
                                                <template #cell(action)="row">
                                                    <a id="search-btn" class="editconsumer custom-edit-btn " align-self="center" @click="searchDetails(row)" title="View other consumer details with same banking details" variant="outline-success" style="border:none">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<b-icon icon="search" aria-hidden="true"></b-icon></a>
                                                </template>
                                            </b-table>
                                            <b-button variant="success" @click="toggleModalConsumerDetails" style="float:right;">Ok</b-button>
                                        </div>
                                    </b-modal>

                                    <!-- Modal to view consumer detail -->

                                    <b-modal
                                        ref="modalUserDetails"
                                        hide-footer
                                        title="Other consumer with same banking details"
                                    >
                                        <div>
                                            <b-table
                                                bordered
                                                head-variant=light
                                                :items="userDetails"
                                                :fields="userFields"
                                                responsive
                                            >

                                            </b-table>
                                        </div>
                                        <b-button variant="success" @click="toggleModalUserDetails" style="float:right;">OK</b-button>
                                    </b-modal>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </div>
</div>
</template>
<script>
import api from "@/api/MicrobiltErrorReport";
import userAPI from "@/api/user.js";
import moment from "moment";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "../CustomLoader/CanPayLoader.vue"
import ApiKeysVue from '../ApiKeys.vue';
export default {
    components: {
        HourGlass,
        CanPayLoader
    },
    data(){
        return {
            consumer:null,
            phone_no:null,
            email:null,
            // defining the b-table columns
            fields:[
                {
                   key: "name",
                   label: "Name"
                },
                {
                    key: "phone",
                    label: "Phone"
                },
                {
                    key: "address",
                    label: "Address"
                },
                {
                    key: "bank_link_type",
                    label: 'Bank Link Type'
                },
                {
                    key: "routing_number",
                    label: "Routing Number"
                },
                {
                    key:"account_number",
                    label:"Account Number"
                },
                {
                    key:"status",
                    label: "User Status"
                },
                {
                    key: "action",
                    label: "Action"
                }
                ],
            bankFields:[
                {
                    key: "routing_no",
                    label: "Routing Number"
                },
                {
                    key: "account_number",
                    label: "Account Number"
                },
                {
                    key: "STATUS",
                    label: "Bank Status"
                },
                {
                    key:"account_type",
                    label: "Account Type"
                },
                {
                    key: "action",
                    label: "Action"
                }
            ],
            userFields:[
                {
                    key: "first_name",
                    label: "First Name"
                },
                {
                    key: "last_name",
                    label: "Last Name"
                },
                {
                    key: "phone",
                    label: "Phone"
                },
                {
                    key: "email",
                    label:"Email"
                }
            ],
            customerDetails:[],
            currentPage:1,
            perPage:10,
            loading:false,
            options: [],
            review_status: null,
            selected_row: null,
            bankDetails: [],
            userDetails: [],
            outcome_ssnrequired: '',
            start_date: "",
            end_date: "",

        }
    },
    mounted(){
        let self = this;

        $("#start-date,#end-date").datepicker({
        format: "mm/dd/yyyy",
        autoclose: true,
        todayHighlight: true,
        });

        document.title = "CanPay - Microbilt Bank Review";
        userAPI
               .getUserStatus()
               .then((response) => {
                    self.options = response.data;
               })
               .catch((err) => {
                    error(err.response.data.message);
               })

        $("#start-date").val(moment(new Date()).format("MM/DD/yyyy"));
        $("#end-date").val(moment(new Date()).format("MM/DD/yyyy"));
    },
    computed: {
        totalRows(){

            let self = this;
            return self.customerDetails.length;

        }
    },
    methods:{

        toggleModalConsumerStatus()
        {
            let self = this;
            self.$refs['modal'].toggle('#edit-btn');
        },

        toggleModalConsumerDetails()
        {
            let self = this;
            self.$refs['modalConsumerDetails'].toggle('#view-btn');
        },

        toggleModalUserDetails()
        {
            let self = this;
            self.$refs['modalUserDetails'].toggle('#search-btn');
        },

        searchForRestrictedConsumer()
        {
            let self = this;
            self.start_date = moment($("#start-date").val()).format("YYYY-MM-DD");
            self.end_date = moment($("#end-date").val()).format("YYYY-MM-DD");
            const startDate = new Date(self.start_date);
            const endDate = new Date(self.end_date);
            const differenceInMilliseconds = endDate - startDate;
            const differenceInDays = differenceInMilliseconds / (24 * 60 * 60 * 1000);

            if(differenceInDays<0 || differenceInDays>7)
            {
                return error("Maximum accepted date range is of 7 days")
            }

            if(self.start_date > self.end_date)
            {
                return error("Please provide a valid date range")
            }

            if(self.start_date == '' || self.end_date == '')
            {
                return error("Please provide a valid date")
            }
            self.loading = true;
            const request = {

                "start_date": self.start_date,
                "end_date": self.end_date

            }

            api
                .getAutomaticRestrctedConsumerDetails(request)
                .then((response)=>{
                    self.customerDetails = response.data;
                    self.loading = false;
                })
                .catch((err)=>{
                    error(err.response.data.message);
                    self.loading = false;
                })
        },

        reset()
        {
            let self = this;
            self.start_date = "";
            self.end_date = "";
            $("#start-date").val('');
            $("#end-date").val('');
        },

        editDetails(row)
        {
            let self = this;
            self.toggleModalConsumerStatus();
            self.selected_row = row.item
        },

        viewDetails(row)
        {
            let self = this;
            const user_id = row.item.user_id;
            const phone = row.item.phone;

            const request = {
                "user_id":user_id,
                "phone": phone
            }

            api
                .getConsumerDetails(request)
                .then((response) => {
                    self.bankDetails = response.data.bankDetails;
                    self.outcome_ssnrequired = response.data.outcome_ssnrequired;
                    if(self.outcome_ssnrequired === 'Y'){
                        self.outcome_ssnrequired = "Yes";
                    }else if(self.outcome_ssnrequired === "N"){
                        self.outcome_ssnrequired = "No";
                    }
                    self.toggleModalConsumerDetails();
                })
                .catch((err) => {
                    error(err.response.data.message)
                })

        },

        updateStatus()
        {
            let self = this;
            if(self.selected_row == null){
                return error("Please select a consumer to edit status");
            }
            const request = {
                "user_id":self.selected_row.user_id,
                "code":self.review_status.code
            }
            api
               .editConsumerStatus(request)
               .then((response)=>{
                    success(response.data);
               })
               .catch((err) => {
                    error(err);
               })
        },

        searchDetails(row)
        {
            let self = this;
            const user_id = row.item.user_id;
            const bank_id = row.item.bank_id;

            const request = {
                user_id:user_id,
                bank_id:bank_id
            }
            api
                .getOtherConsumerDetails(request)
                .then( (response) => {
                    self.userDetails = response.data;
                    if(self.userDetails.length > 0)
                        self.toggleModalUserDetails();
                    else
                        error("No consumer found with same banking details")
                })
                .catch( (err) => {
                    error(err.response.data.message)
                })
        }
    }
}
</script>
