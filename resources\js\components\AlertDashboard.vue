<template>
<div>
    <div v-if="loading">
        <CanPayLoader/>
    </div>
    <div class="content-wrapper" style="min-height: 36px;">
        <section class="content-header">
            <div class="container-fluid">
                <div class="row mb-2">
                    <div class="col-sm-6">
                        <h1 class="m-0">Alert Dashboard</h1>
                    </div>
                </div>
            </div>
        </section>
        <section class="content">
         <div class="row">
          <div class="col-md-12">
            <div class="card">
              <div class="card-header border-transparent">
                <h3 class="card-title">Users having Purchase Power > $1500</h3>
                <button
                  type="button"
                  class="btn btn-danger ml-10 export-api-btn margin-left-5"
                  @click="exportUsersPpLists"
                >
                  Export <i
                    class="fa fa-download ml-10"
                    aria-hidden="true"
                  ></i>
                </button>
                <button
                  type="button"
                  class="btn btn-success ml-10 export-api-btn"
                  @click="getUsersPpLists"
                >
                  Generate
                </button>
              </div>
              <div class="card-body p-0">
                <div class="table-responsive">
                  <b-table
                      show-empty
                      responsive
                      head-variant="light"
                      sticky-header="600px"
                      id="usersPpListTable"
                      :items="usersPpList"
                      :fields="fieldsTable1"
                      class="users-pp-table-cls"
                      v-if="usersPpList.length > 0"
                    >
                  </b-table>
                </div>
                <div class="alert alert-danger col-md-12" v-if="showUsersPpList">
                  No records found having purchase power > $1500.
                </div>
              </div>

            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-md-12">
            <div class="card">
              <div class="card-header border-transparent">
                <h3 class="card-title">Users with Purchase > $700 in last 48 hrs</h3>
                <button
                  type="button"
                  class="btn btn-danger ml-10 export-api-btn margin-left-5"
                  @click="exportUsersPurchase"
                >
                  Export <i
                    class="fa fa-download ml-10"
                    aria-hidden="true"
                  ></i>
                </button>
                <button
                  type="button"
                  class="btn btn-success ml-10 export-api-btn"
                  @click="getUsersPurchase"
                >
                  Generate
                </button>
              </div>
              <div class="card-body p-0">
                <div class="table-responsive">
                  <b-table
                      show-empty
                      responsive
                      head-variant="light"
                      sticky-header="600px"
                      id="usersPurchaseListTable"
                      :items="usersPurchaseList"
                      :fields="fieldsTable2"
                      class="users-pp-table-cls"
                      v-if="usersPurchaseList.length > 0"
                    >
                  </b-table>
                </div>
                <div class="alert alert-danger col-md-12" v-if="showUsersPurchaseList">
                  No records found with purchase > $700 in last 48 hrs.
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-md-12">
            <div class="card">
              <div class="card-header border-transparent">
                <h3 class="card-title">Users with Purchase $350 or more in a single day in the prior 3 days (plus today)</h3>
                <button
                  type="button"
                  class="btn btn-success ml-10 export-api-btn"
                  @click="getPurchaseDetails('single_day_250')"
                >
                  Generate
                </button>
              </div>
              <div class="card-body p-0">
                <div class="table-responsive">
                  <div class="col-md-4 mt-2 mb-2 float-right" v-if="usersPurchaseSingleDayList.length > 0">
                    <b-input v-model="fieldsTablePurchasefilter" placeholder="Search"></b-input>
                  </div>
                  <b-table
                      show-empty
                      responsive
                      head-variant="light"
                      sticky-header="600px"
                      id="usersPurchaseSingleDayListable"
                      :items="usersPurchaseSingleDayList"
                      :fields="fieldsTablePurchase"
                      :filter="fieldsTablePurchasefilter"
                      class="users-pp-table-cls"
                      v-if="usersPurchaseSingleDayList.length > 0"
                    >
                    <template #cell(consumer_name)="data">
                      <a style="color: white !important" class="btn btn-success" @click="viewDetails(data);">{{ data.value }}</a>
                    </template>
                    <template #cell(action)="data">
                      <p v-if="data.value == 207">Inactivated</p>
                      <p v-else><a style="color: white !important" class="btn btn-success" @click="activateUser(data,'single_day_250');">Active</a></p>
                    </template>
                  </b-table>
                </div>
                <div class="alert alert-danger col-md-12" v-if="showUsersPurchaseSingleDayList">
                  No records found with Purchase $250 or more in a single day in the prior 3 days (plus today).
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-md-12">
            <div class="card">
              <div class="card-header border-transparent">
                <h3 class="card-title">Users with Purchase > 2 times in the prior 3 days (plus today) with a combined total between the 3+ transactions of $300 or more</h3>
                <button
                  type="button"
                  class="btn btn-success ml-10 export-api-btn"
                  @click="getPurchaseDetails('single_day_300_with_more_than_3_transactions')"
                >
                  Generate
                </button>
              </div>
              <div class="card-body p-0">
                <div class="table-responsive">
                  <div class="col-md-4 mt-2 mb-2 float-right" v-if="purchaseDetailsList.length > 0">
                    <b-input v-model="fieldsTablePurchase1filter" placeholder="Search"></b-input>
                  </div>
                  <b-table
                      show-empty
                      responsive
                      head-variant="light"
                      sticky-header="600px"
                      id="purchaseDetailsListTable"
                      :items="purchaseDetailsList"
                      :fields="fieldsTablePurchase1"
                      :filter="fieldsTablePurchase1filter"
                      class="users-pp-table-cls"
                      v-if="purchaseDetailsList.length > 0"
                    >
                    <template #cell(consumer_name)="data">
                      <a style="color: white !important" class="btn btn-success" @click="viewDetails(data);">{{ data.value }}</a>
                    </template>
                    <template #cell(action)="data">
                      <p v-if="data.value == 207">Inactivated</p>
                      <p v-else><a style="color: white !important" class="btn btn-success" @click="activateUser(data,'single_day_300_with_more_than_3_transactions');">Active</a></p>
                    </template>
                  </b-table>
                </div>
                <div class="alert alert-danger col-md-12" v-if="showPurchaseDetailsList">
                  No records found with purchase > 2 times in the prior 3 days (plus today) with a combined total between the 3+ transactions of $300 or more.
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-md-12">
            <div class="card">
              <div class="card-header border-transparent row" style="align-items: center;">
                <div class="col-md-4">
                  <h3 class="card-title">Enrollment Report </h3>
                </div>
                <div class="col-md-2">
                  <input
                      class="start-date form-control"
                      placeholder="To Date"
                      id="start-date"
                      onkeydown="return false"
                      autocomplete="off"
                    />
                </div>
                <div class="col-md-2">
                  <input
                      class="end-date form-control"
                      placeholder="From Date"
                      id="end-date"
                      onkeydown="return false"
                      autocomplete="off"
                    />
                </div>
                <div class="col-md-4">
                  <button
                    type="button"
                    class="btn btn-danger ml-10 export-api-btn margin-left-5"
                    @click="getExportEnrollmentReport(true)"
                  >
                    Export <i
                      class="fa fa-download ml-10"
                      aria-hidden="true"
                    ></i>
                  </button>
                  <button
                    type="button"
                    class="btn btn-success ml-10 export-api-btn"
                    @click="getExportEnrollmentReport()"
                  >
                    Generate
                  </button>
                  <b-button
                    class="btn btn-info ml-10 export-api-btn"
                    v-if="showRefreshBtn"
                    @click="getExportEnrollmentReport()"
                  ><i class="fas fa-redo"></i></b-button>
                  <b-button
                    class="btn btn-success mr-10 export-api-btn"
                    v-if="consumerIdArray.length > 0"
                    @click="addReview()" hidden
                  >Add Review</b-button>
                </div>
              </div>
              <div class="card-body p-0">
                <div class="table-responsive">
                  <b-table-simple
                        responsive
                        show-empty
                        bordered
                        sticky-header="800px"
                        v-if="enrollmentReport.length > 0"
                      >
                        <b-thead head-variant="light">
                          <b-tr>
                            <b-th class="text-left" width="2%">
                              <label class="form-checkbox" v-if="need_review_user_count > 0" hidden>
                                <input type="checkbox" title="Select All For Review Mark" class="all-checkbox" @change="selectAll($event)">
                                <i class="form-icon"></i>
                              </label>
                              Review
                            </b-th>
                            <b-th class="text-left" width="2%">State</b-th>
                            <b-th class="text-left" width="8%">Name</b-th>
                            <b-th class="text-left" width="8%">Date Of Birth</b-th>
                            <b-th class="text-left" width="10%">Address</b-th>
                            <b-th class="text-left" width="10%">Email</b-th>
                            <b-th class="text-left" width="10%">Consumer Type</b-th>
                            <b-th class="text-left" width="10%">Bank balance</b-th>
                            <b-th class="text-left" width="5%">Purchase Power/Limit <i class="fa fa-info-circle" aria-hidden="true" :title="constants.purchase_power_help_text"></i></b-th>
                            <b-th class="text-left" width="5%">Effective Purchase Power <i class="fa fa-info-circle" aria-hidden="true" :title="constants.effective_purchase_power_help_text"></i></b-th>
                            <b-th class="text-left" width="5%">Purchase Power Rule</b-th>
                            <b-th class="text-left" width="5%">Pending Amount</b-th>
                            <b-th class="text-left" width="10%">Matches any other banking?</b-th>
                            <b-th class="text-left" width="10%">Phone</b-th>
                            <b-th class="text-left" width="10%">Max Average Score</b-th>
                            <b-th class="text-left" width="10%">Risk Score</b-th>
                            <b-th class="text-left" width="10%">Bank name or routing number</b-th>
                            <b-th class="text-left" width="5%">Time of enrollment (PST)</b-th>
                            <b-th class="text-left" width="5%">Action</b-th>
                          </b-tr>
                        </b-thead>
                        <b-tbody v-for="(row, index) in enrollmentReport" :key="index">
                          <b-tr>
                            <b-td class="text-left text-gray">
                              <label class="form-checkbox" v-if="row.reviewed_user_name == ''"><input class="indivisual-checkbox" @change="onCheckEvent($event, row)" type="checkbox" title="Select For Review Mark"></label>
                              <a v-else style="color: #007bff !important" class="custom-edit-btn" @click="viewReviewDetails(row.user_id)" :title="getReviewedTitle(row.reviewed_user_name)">{{getInitials(row.reviewed_user_name)}}</a>
                            </b-td>
                            <b-td class="text-left text-gray">{{row.state}}</b-td>
                            <b-td class="text-left text-gray">{{row.name}}</b-td>
                            <b-td class="text-left text-gray">{{row.date_of_birth}}</b-td>
                            <b-td class="text-left text-gray">{{row.address}}</b-td>
                            <b-td class="text-left text-gray">
                              <a style="color: #007bff !important" class="custom-edit-btn" @click="editconsumer(row.user_id)">{{row.email}}</a>
                              <a v-if="row.email" v-clipboard:copy="row.email" v-clipboard:success="onEmailCopy" v-clipboard:error="onEmailCopyError" class="custom-edit-btn" title="Copy Email"  variant="outline-success" style="border:none;margin-left: 5px;"><i class="nav-icon fas fa-copy"></i></a>
                            </b-td>
                            <b-td class="text-left text-gray">{{capitalizeFirstLetter(row.consumer_type)}}</b-td>
                            <b-td class="text-left text-gray">{{row.bank_balance == 'N/A' ? row.bank_balance:'$'+row.bank_balance}}</b-td>
                            <b-td class="text-left text-gray">${{row.purchase_power}}</b-td>
                            <b-td class="text-left text-gray">${{row.effective_purchase_power}}</b-td>
                            <b-td class="text-left text-gray">{{row.purchase_power_rule}} <br/>
                                <span v-if="row.is_algo_based==1" class="custom-default-algo">(Default Algo)</span></b-td>
                            <b-td class="text-left text-gray">${{row.pending_amount}}</b-td>
                            <b-td class="text-left text-gray" v-html="formattedBankMatchedUsers(row)" ></b-td>
                            <b-td class="text-left text-gray">
                              <a style="color: #007bff !important" class="custom-edit-btn" @click="editconsumer(row.user_id)">{{row.phone}}</a>
                              <a v-clipboard:copy="row.phone" v-clipboard:success="onPhoneCopy" v-clipboard:error="onPhoneCopyError" class="custom-edit-btn" title="Copy Phone"  variant="outline-success" style="border:none;margin-left: 5px;"><i class="nav-icon fas fa-copy"></i></a>
                            </b-td>
                            <b-td class="text-center text-gray">{{row.max_average_score}}</b-td>
                            <b-td class="text-left text-gray">{{row.risk_score}}</b-td>
                            <b-td class="text-left text-gray">
                              <a style="color: #007bff !important" class="custom-edit-btn" v-if="row.bank_link_type == 1 && row.active_bank_id" @click="searchConsumerAccounts(row.phone)">{{row.routing_no}}</a>
                              <span v-else>{{row.routing_no}}</span>
                            </b-td>
                            <b-td class="text-left text-gray">{{row.enrollment_date}}</b-td>
                            <b-td class="text-center text-gray">
                                <a :data-user-id="row.user_id" class=" custom-edit-btn" title="Comment About Customer" variant="outline-success" style="border:none" @click="commentconsumer(row.user_id)"><i class="nav-icon fas fa-clipboard"></i></a>
                            <a :data-user-id="row.user_id" class="viewConsumerDetails custom-edit-btn" title="Identity Validation Details" variant="outline-success" @click="showconsumerdetails(row.phone, row.name, row.bank_link_type, row.user_id)" style="border:none"><i class="nav-icon fas fa-eye"></i></a>
                            </b-td>
                          </b-tr>
                        </b-tbody>
                  </b-table-simple>
                  <!-- Display pagination controls -->
                  <div v-if="enrollmentReport.length > 0" style="float:right">
                      <b-pagination
                          v-model="currentPage"
                          :total-rows="totalEnrollmentItems"
                          :per-page="perPage"
                          prev-text="Prev"
                          next-text="Next"
                          :ellipsis="true"
                          :limit="5"
                      ></b-pagination>
                  </div>
                </div>
                <div class="alert alert-danger col-md-12" v-if="showEnrollmentReport">
                  No records found.
                </div>
              </div>
            </div>
          </div>
        </div>
        </section>

        <!-- Consumer Transaction Details modal start -->
        <b-modal
          id="consumer-transaction-details-modal"
          ref="consumer-transaction-details-modal"
          :header-text-variant="headerTextVariant"
          :title="modal_header"
          hide-footer
        >
        <div class="row mb-2">
            <div class="col-6">
                <b> Customer Name </b> : {{consumerDetails.first_name}} {{consumerDetails.last_name}}
            </div>
            <div class="col-6">
                <b> Email </b> : {{consumerDetails.email}}
            </div>
        </div>
        <div class="row mb-2">
            <div class="col-6">
                <b> Phone No. </b> : {{consumerDetails.phone_no}}
            </div>
            <div class="col-6">
                <b> Age </b> : {{consumerDetails.age}}
            </div>
        </div>
        <div class="row mb-2">
            <div class="col-6">
                <b> Consumer Street </b> :
                {{consumerDetails.customer_street}}
            </div>
            <div class="col-6">
                <b> Consumer State </b> :
                {{consumerDetails.customer_state}}
            </div>
        </div>
        <div class="row mb-2">
            <div class="col-6">
                <b> Consumer City </b> :
                {{consumerDetails.customer_city}}
            </div>
            <div class="col-6">
                <b> First Transaction Date </b> :
                {{consumerDetails.first_transaction_date}}
            </div>
        </div>
        <div class="row mb-2">
            <div class="col-6">
                <b> Total Transaction Count </b> :
                {{consumerDetails.transaction_count}}
            </div>
            <div class="col-6">
                <b> Return Count </b> :
                {{consumerDetails.return_count}}
            </div>
        </div>
        <div class="row mb-2">
            <div class="col-6">
                <b> Total Amount ($)</b> :
                {{consumerDetails.total_amount}}
            </div>
            <div class="col-6">
                <b> User Type</b> :
                {{consumerDetails.user_type}}
            </div>
        </div>
        <div class="row">
            <div class="col-12">
              <b-table-simple
                responsive
                show-empty
                bordered
                sticky-header="800px"
              >
                <b-thead head-variant="light" style="position: sticky; top:0;">
                  <b-tr>
                    <b-th class="text-center">Store Name</b-th>
                    <b-th class="text-center">Store ID</b-th>
                    <b-th class="text-center">Amount($)</b-th>
                    <b-th class="text-center">Transaction date</b-th>
                    <b-th class="text-center">Status</b-th>
                  </b-tr>
                </b-thead>
                <b-tbody v-for="(row, index) in consumerTransactionDetails" :key="index">
                  <b-tr>
                    <b-td class="text-left"
                      >{{ row.store_name }}</b-td
                    >
                    <b-td class="text-left"
                      >{{ row.store_id }}</b-td
                    >
                    <b-td class="text-right"
                      >{{ row.amount }}</b-td
                    >
                    <b-td class="text-center"
                      >{{ row.transaction_date }}</b-td
                    >
                    <b-td class="text-center"
                      >{{ row.transaction_status }}</b-td
                    >
                  </b-tr>
                </b-tbody>
              </b-table-simple>
            </div>
          </div>
        </b-modal>
        <!-- Consumer Transaction Details modal end -->

        <!-- Consumer Modal Edit Start -->
        <b-modal
          id="consumer-modal"
          ref="consumer-modal"
          :header-text-variant="headerTextVariant"
          title="Change Consumer Details"
          @hidden="resetModal"
          ok-title="Save"
          ok-variant="success"
          cancel-variant="outline-secondary"
          @ok="handleOk"
          :no-close-on-esc="true"
          :no-close-on-backdrop="true"
        >
          <form ref="form" @submit.stop.prevent="save" class="needs-validation">
            <div class="row">
              <div class="col-md-4">
                <label for="user_type">
                  First Name
                  <span class="red">*</span>
                </label>
                <input
                  class="form-control"
                  placeholder="First Name"
                  id="first_name"
                  name="first_name"
                  autocomplete="off"
                  v-model="first_name"
                  v-validate="'required|alpha_spaces'"
                />
                <span v-show="errors.has('first_name')" class="text-danger">{{
                  errors.first("first_name")
                }}</span>
              </div>
              <div class="col-md-4">
                <label for="user_type">
                  Middle Name
                </label>
                <input
                  class="form-control"
                  placeholder="Middle Name"
                  id="middle_name"
                  name="middle_name"
                  autocomplete="off"
                  v-model="middle_name"
                  v-validate="'alpha_spaces'"
                />
                <span v-show="errors.has('middle_name')" class="text-danger">{{
                  errors.first("middle_name")
                }}</span>
              </div>
              <div class="col-md-4">
                <label for="user_type">
                  Last Name
                  <span class="red">*</span>
                </label>
                <input
                  class="form-control"
                  placeholder="Last Name"
                  id="last_name"
                  name="last_name"
                  autocomplete="off"
                  v-model="last_name"
                  v-validate="'required|alpha_spaces'"
                />
                <span v-show="errors.has('last_name')" class="text-danger">{{
                  errors.first("last_name")
                }}</span>
              </div>
              <div class="col-md-8">
                <label for="user_type">
                  Street Address
                  <span class="red">*</span>
                </label>
                <input
                  class="form-control"
                  placeholder="Street Address"
                  id="street_address"
                  name="street_address"
                  autocomplete="off"
                  v-model="street_address"
                  v-validate="'required'"
                />
                <span v-show="errors.has('street_address')" class="text-danger">{{
                  errors.first("street_address")
                }}</span>
              </div>
              <div class="col-md-4">
                <label for="user_type">
                  Apartment Number
                </label>
                <input
                  class="form-control"
                  placeholder="Apartment Number"
                  id="apt_number"
                  name="apt_number"
                  autocomplete="off"
                  v-model="apt_number"
                />
              </div>
              <div class="col-md-4">
                <label for="user_type">
                  City
                </label>
                <input
                  class="form-control"
                  placeholder="City"
                  id="city"
                  name="city"
                  autocomplete="off"
                  v-model="city"
                />
              </div>
              <div class="col-md-4">
                <label for="user_type">
                  State
                </label>
                <input
                  class="form-control"
                  placeholder="State"
                  id="state"
                  name="state"
                  autocomplete="off"
                  v-model="state"
                />
              </div>
              <div class="col-md-4">
                <label for="user_type">
                  Zipcode
                </label>
                <input
                  class="form-control"
                  placeholder="Zipcode"
                  id="zipcode"
                  name="zipcode"
                  autocomplete="off"
                  v-model="zipcode"
                />
              </div>
              <div class="col-md-12">
                <label for="user_type">
                  Status
                  <span class="red">*</span>
                </label>
                <select
                  class="form-control"
                  id="user_status"
                  name="user_status"
                  v-model="status"
                  v-validate="'required'"
                >
                  <option
                    v-for="(status, index) in statusList"
                    :key="index"
                    v-bind:value="{ id: status.id, text: status.status }"
                  >
                    {{ status.status }}
                  </option>
                </select>
                <span v-show="errors.has('user_status')" class="text-danger">{{
                  errors.first("user_status")
                }}</span>
              </div>
              <div class="col-md-12">
                <label for="user_type"> Date of Birth </label>
                <input

                  class="form-control"
                  placeholder="Date of Birth (Format:- mm-dd-yyyy)"
                  id="dob"
                  autocomplete="off"
                  v-model="date_of_birth"
                  :maxlength="10"
                  @keypress="isNumber($event)"
                />
              </div>
                <div class="col-md-12">
                <input type="checkbox" id="automatic_pp" name="automatic_pp" :disabled="is_automatic_purchase_power_enable == false" v-model="automatic_purchase_power" true-value="1" false-value="0" @click="getPurchasePower"> <label for="automatic_pp"> Automatic Purchase Power</label>
                </div>
                <div class="col-md-12" v-if="show_purchase_power">
                <label for="user_type">
                  Daily Spending Limit
                </label>
                <input
                  name="purchase_power"
                  class="form-control"
                  placeholder="Purchase Power"
                  id="purchase_power"
                  ref="purchase_power"
                  autocomplete="off"
                  v-model="purchase_power"
                  v-validate="'decimal'"
                />
                <span v-show="errors.has('purchase_power')" class="text-danger">{{
                  errors.first("purchase_power")
                }}</span>
                </div>
                <div class="col-md-12">
                <input type="checkbox" id="automatic_weekly_spending_limit" name="automatic_weekly_spending_limit" v-model="automatic_weekly_spending_limit" true-value="1" false-value="0"> <label for="automatic_weekly_spending_limit">Disable Weekly Spending Limit  </label><i class="fa fa-info-circle" aria-hidden="true" :title="constants.disable_weekly_spending_limit_help_text"></i>
                </div>
                <div class="col-md-12" v-if="automatic_weekly_spending_limit == 0">
                <label for="user_type">
                  Weekly Spending Limit
                </label>
                <input
                  name="weekly_spending_limit"
                  class="form-control"
                  placeholder="Weekly Spending Limit"
                  id="weekly_spending_limit"
                  autocomplete="off"
                  v-model="weekly_spending_limit"
                  v-validate="'required|decimal|min_value:'+purchase_power"
                  v-if="purchase_power!=''"
                />
                <input
                  name="weekly_spending_limit"
                  class="form-control"
                  placeholder="Weekly Spending Limit"
                  id="weekly_spending_limit"
                  autocomplete="off"
                  v-model="weekly_spending_limit"
                  v-validate="'decimal|min_value:'+purchase_power"
                  v-else
                />
                <span v-show="errors.has('weekly_spending_limit')" class="text-danger">{{
                  errors.first("weekly_spending_limit")
                }}</span>
                </div>
                <div class="col-md-12">
                <input type="checkbox" id="active_allow_transaction" name="active_allow_transaction" v-model="active_allow_transaction" true-value="1" false-value="0" :disabled="is_active_allow_transaction == false"> <label for="active_allow_transaction">Active Allow Transaction</label>
                </div>
                <div class="col-md-12">

                <label class="switch"><input type="checkbox" id="required_upload_document" name="required_upload_document" v-model="required_upload_document" true-value="1" false-value="0" class="enable-employee-login"><span class="slider round"></span></label> <label for="required_upload_document"> Bank Statement Upload Required</label>
                </div>
            </div>
          </form>
        </b-modal>
        <!-- Consumer Modal Edit End -->

        <!-- View All Bank Matched Users modal start -->
        <b-modal
          id="all-bank-matched-users-modal"
          ref="all-bank-matched-users-modal"
          :header-text-variant="headerTextVariant"
          title="Matches other banking users"
          :no-close-on-esc="true"
          :no-close-on-backdrop="true"
          v-model="viewMatchedUsersList"
          ok-variant="success"
          hide-footer ok-only
        >
        <div class="card-body">
          <ul>
              <li v-for="(value, index) in bankMatchedUsersList" :key="index">{{ value }}</li>
          </ul>
        </div>
        </b-modal>
        <!-- View All Bank Matched Users modal end -->

        <!-- View Account Owner Info modal start -->
        <b-modal
          id="manage-account-owner-info-modal"
          ref="manage-account-owner-info-modal"
          :header-text-variant="headerTextVariant"
          title="Manage Account Owner Info"
          :no-close-on-esc="true"
          :no-close-on-backdrop="true"
          ok-variant="success"
          hide-footer ok-only
        >
        <div class="card-body">
          <div class="row">
              <div class="col-8 mb-10" v-if="allUserAccounts.length > 0">
                  <span class="text-bold">User Details</span>
              </div>
          </div>
          <b-table-simple
            responsive
            show-empty
            bordered
            sticky-header="800px"
            v-if="allUserAccounts.length > 0"
          >
            <b-thead head-variant="light">
              <b-tr>
                <b-th class="text-left">Name</b-th>
                <b-th class="text-left">Phone</b-th>
                <b-th class="text-left">Email</b-th>
              </b-tr>
            </b-thead>
            <b-tbody>
            <b-tr>
              <b-td class="text-left text-gray">{{allUserAccounts[0].name}}</b-td>
              <b-td class="text-left text-gray">{{allUserAccounts[0].phone}}</b-td>
              <b-td class="text-left text-gray">{{allUserAccounts[0].email}}</b-td>
            </b-tr>
            </b-tbody>
          </b-table-simple>
          <div class="row">
              <div class="col-8 mb-10" v-if="allUserAccounts.length > 0">
                  <span class="text-red">Real Account No. is shown in bracket.</span>
              </div>
          </div>
          <b-table-simple
            responsive
            show-empty
            bordered
            sticky-header="800px"
            v-if="allUserAccounts.length > 0"
          >
            <b-thead head-variant="light">
              <b-tr>
                <b-th class="text-left">Account No.</b-th>
                <b-th class="text-left">Routing No.</b-th>
                <b-th class="text-left">Bank Name</b-th>
                <b-th class="text-left">Banking Solution</b-th>
                <b-th class="text-left">Status</b-th>
                <b-th class="text-left">Created On</b-th>
                <b-th class="text-center">Action</b-th>
              </b-tr>
            </b-thead>
            <b-tbody v-for="(row, index) in allUserAccounts" :key="index">
              <b-tr>
                <b-td class="text-left text-gray">{{
                  row.account_no
                }}</b-td>
                <b-td class="text-left text-gray">{{
                  row.routing_no
                }}</b-td>
                <b-td class="text-left text-gray">{{
                  row.bank_name
                }}</b-td>
                <b-td class="text-left text-gray text-capitalize">{{
                  row.banking_solution_name
                }}</b-td>
                <b-td class="text-left text-gray">{{
                  row.status
                }}</b-td>
                <b-td class="text-left text-gray">{{
                  row.created_on
                }}</b-td>
                <b-td class="text-center text-gray" v-if="row.banking_solution_name =='akoya' && row.akoya_provider_id =='capitalone'">
                </b-td>
                <b-td class="text-center text-gray" v-else>
                  <a :data-user-id="row.edit" class="callAccountOwnerApi custom-edit-btn" title="Call Account Owner API" variant="outline-success" style="border:none" @click="callAccountOwnerApi(row)"><i class="nav-icon fas fa-redo"></i></a>
                  <a :data-user-id="row.edit"
                    class="viewHistory custom-edit-btn"
                    title="View Account Owner History"
                    variant="outline-success"
                    style="border:none"
                    v-if="row.account_owner_called === 1"
                    @click="showHistory(row)">
                        <i class="nav-icon fas fa-eye"></i>
                    </a>

                    <a :data-user-id="row.edit"
                    class="retryApiCall custom-edit-btn"
                    title="Retry Account Owner API Call"
                    variant="outline-warning"
                    style="border:none"
                    v-if="'mx_record_id' in row && row.mx_record_id !== null && row.account_owner_called !== 1"
                    @click="retryApiCall(row.mx_record_id)">
                        <i class="nav-icon fas fa-sync"></i>
                    </a>
                </b-td>
              </b-tr>
            </b-tbody>
          </b-table-simple>
          <p v-else>No data displayed. Please refine your search criteria.</p>
        </div>
        </b-modal>
        <!-- View Account Owner Info modal end -->

        <!-- Account Owner History Modal Start -->
        <b-modal
          id="account-owner-history-modal"
          ref="account-owner-history-modal"
          :header-text-variant="headerTextVariant"
          title="Account Owner History"
          :no-close-on-esc="true"
          :no-close-on-backdrop="true"
          hide-footer
        >
          <div class="card-body">
            <div class="row" v-if="account_owner_history.length > 0">
        <div class="col-md-12">
            <label>Account No:  </label><span>&nbsp;{{account_owner_history[0].account_no}}</span>
        </div>
        <div class="col-md-12">
            <label>Routing No:  </label><span>&nbsp;{{account_owner_history[0].routing_no}}</span>
        </div>
        </div>
            <b-table-simple
              responsive
              show-empty
              bordered
              sticky-header="800px"
              v-if="account_owner_history.length > 0"
            >

            <b-thead head-variant="light">
            <b-tr>
              <b-th class="text-left">Name from Registration</b-th>
              <b-th class="text-left">Name from Banking Solution</b-th>
              <b-th class="text-left">Name Match Percentage(%)</b-th>
              <b-th class="text-left">Address from Registration</b-th>
              <b-th class="text-left">Address from Banking Solution</b-th>
              <b-th class="text-left">Address Match Percentage(%)</b-th>
              <b-th class="text-center">API Called On</b-th>
            </b-tr>
          </b-thead>
          <b-tbody v-for="(row, index) in account_owner_history" :key="index">
            <b-tr>
              <b-td class="text-left text-gray">{{ row.name_during_registration  }}</b-td>
              <b-td class="text-left text-gray">{{ row.name_from_banking_solution }}</b-td>
              <b-td class="text-left text-gray">{{ row.name_match_percentage }}</b-td>
              <b-td class="text-left text-gray">{{ row.address_during_registration }}</b-td>
              <b-td class="text-left text-gray">{{ row.address_from_banking_solution }}</b-td>
              <b-td class="text-left text-gray">{{ row.address_match_percentage }}</b-td>
              <b-td class="text-left text-gray">{{ row.created_date }}</b-td>
            </b-tr>
          </b-tbody>
            </b-table-simple>
            <p v-else>No data displayed. Please refine your search criteria.</p>
          </div>
        </b-modal>
        <!-- Account Owner History Modal End -->
        <!-- User Review Comments History Modal Start -->
        <b-modal
          id="get-user-review-comments-modal"
          ref="get-user-review-comments-modal"
          :header-text-variant="headerTextVariant"
          title="Review Details"
          :no-close-on-esc="true"
          :no-close-on-backdrop="true"
          hide-footer
        >
          <div class="card-body">
            <b-table-simple
              responsive
              show-empty
              bordered
              sticky-header="800px"
              v-if="review_history.length > 0"
            >

              <b-thead head-variant="light">
                <b-tr>
                  <b-th class="text-left">User Name</b-th>
                  <b-th class="text-left">Reviewed By</b-th>
                  <b-th class="text-center">Comment</b-th>
                </b-tr>
              </b-thead>
              <b-tbody v-for="(row, index) in review_history" :key="index">
                <b-tr>
                  <b-td class="text-left text-gray">{{ row.consumer_name }}</b-td>
                  <b-td class="text-left text-gray">{{ row.reviewed_user_name }}</b-td>
                  <b-td class="text-left text-gray">{{ row.comment }}</b-td>
                </b-tr>
              </b-tbody>
            </b-table-simple>
            <p v-else>No data displayed.</p>
          </div>
        </b-modal>
        <!-- User Review Comments History Modal End -->
        <!-- User Review Comments Modal Start -->
        <b-modal
          id="user-review-comments-modal"
          ref="user-review-comments-modal"
          :header-text-variant="headerTextVariant"
          title="Review Comments"
          ok-title="Save"
          ok-variant="success"
          :no-close-on-esc="true"
          :no-close-on-backdrop="true"
          @hidden="resetUserRevirwModal"
          @ok="handleOkReviewComment"
          cancel-title="Close"
          cancel-variant="outline-secondary"
          :cancel-disabled = "false"
        >
        <div class="row">
              <div class="col-8 mb-10" v-if="selected_review_users.length > 0">
                  <span class="text-bold">User Details</span>
              </div>
          </div>
          <b-table-simple
            responsive
            show-empty
            bordered
            sticky-header="800px"
            v-if="selected_review_users.length > 0"
          >
            <b-thead head-variant="light">
              <b-tr>
                <b-th class="text-left">Name</b-th>
                <b-th class="text-left">Phone</b-th>
                <b-th class="text-left">Email</b-th>
              </b-tr>
            </b-thead>
            <b-tbody v-for="(row, index) in selected_review_users" :key="index">
            <b-tr>
              <b-td class="text-left text-gray">{{row.name}}</b-td>
              <b-td class="text-left text-gray">{{row.phone}}</b-td>
              <b-td class="text-left text-gray">{{row.email}}</b-td>
            </b-tr>
            </b-tbody>
          </b-table-simple>
        <form ref="form" @submit.stop.prevent="save" class="needs-validation">
            <div class="row">
                <div class="col-md-12 row-value">
                    <label for="email">
                    Comment
                    </label>
                    <textarea name="comment" id="comment" class="form-control" v-model="user_review_comment"></textarea>
                </div>
            </div>
        </form>
        </b-modal>
        <!-- User Review Comments Modal End -->
        <!-- Consumer Comments Modal Start -->
        <b-modal
        id="consumer-comments-modal"
        ref="consumer-comments-modal"
        :header-text-variant="headerTextVariant"
        title="Consumer Comments"
        ok-title="Save"
        ok-variant="success"
        @ok="handleOkComment"
        cancel-title="Close"
        cancel-variant="outline-secondary"
        :cancel-disabled = "false"
        >
        <div class="auto-overflow" v-if="userComments.length > 0">
            <div class="col-md-12" v-for="(comments, index) in userComments" :key="index">
                <div class="card">
                    <div class="card-body">
                        <label for="name">{{comments.comment}}</label>
                    </div>
                </div>
                <span class="card-comment-box">{{comments.added_by_name}}&nbsp;{{comments.post_time}}</span>
            </div>
        </div>

        <form ref="form" @submit.stop.prevent="save" class="needs-validation">
            <div class="row">
                <div class="col-md-12 row-value">
                    <label for="email">
                    Comment
                    <span class="red">*</span>
                    </label>
                    <textarea name="comment" id="comment" class="form-control" v-model="consumer_comment" v-validate="'required'"></textarea>
                    <span v-show="errors.has('comment')" class="text-danger">{{
                        errors.first("comment")
                    }}</span>
                </div>
            </div>
        </form>
        </b-modal>
        <!-- Consumer Comments Modal End -->
        <!-- View Consumer Details Modal Start -->
        <b-modal
          id="view-consumer-details-modal"
          ref="view-consumer-details-modal"
          hide-footer
          :header-text-variant="headerTextVariant"
          :no-close-on-backdrop="true"
          title="Identity Validation Details"
        >
        <div>
          <!-- show user basic details -->
          <div class="row">
            <div class="col-12">
              <span class="font-weight-bold">Consumer Name: </span>
              <span>{{consumer_name}}</span>
            </div>
            <div class="col-12 mt-2">
              <span class="font-weight-bold">Phone: </span>
              <span>{{consumer_phone}}</span>
            </div>
          </div>

          <!-- Outcome reason -->
          <div class="mt-2">
            <span class="font-weight-bold">Outcome Reason:  </span>
            <span> {{reason}} </span>
          </div>

          <!-- table -->
          <div class="mt-4">
            <b-table
              striped
              hover
              :items="rowData"
              :fields="columnData"
            >
            <!-- striped is for row color -->
            <!-- filed is for columns -->
            <!-- items is for rows data -->
            </b-table>
          </div>

          <!-- Purchase power percentage table -->
          <div class="mt-3" v-if="riskRowData.length>0" >
            <b-table
              striped
              hover
              :items="riskRowData"
              :fields="riskColumnData"
            >
            <!-- striped is for row color -->
            <!-- filed is for columns -->
            <!-- items is for rows data -->
            </b-table>
          </div>

        </div>

        </b-modal>
        <!-- View Consumer Details Modal End -->
    </div>
</div>
</template>
<script>
import api from "@/api/transaction.js";
import user_api from "@/api/user.js";
import review_api from "@/api/review.js";
import commonConstants from "@/common/constant.js";
import moment from "moment";
import CanPayLoader from "./CustomLoader/CanPayLoader.vue"
import { saveAs } from "file-saver";
import { HourGlass } from "vue-loading-spinner";
export default {
    data() {
        return {
            fieldsTable1: [
                {"key":"name" ,"class":"text-left"},
                {"key":"email" ,"class":"text-left"},
                {"key":"phone_no" ,"class":"text-center"},
                {"key":"purchase_power" ,"class":"text-center"},
            ],
            fieldsTable2: [
                {"key":"name" ,"class":"text-left"},
                {"key":"email" ,"class":"text-left"},
                {"key":"phone_no" ,"class":"text-center"},
                {"key":"total_purchase" ,"class":"text-center"},
                {"key":"no_of_transactions" ,"class":"text-center"},
            ],
            fieldsTablePurchase: [
                {"key":"consumer_name" ,"class":"text-left"},
                {"key":"phone_no" ,"class":"text-center"},
                {"key":"email" ,"class":"text-left"},
                {"key":"user_type" ,"class":"text-left"},
                {"key":"date" ,"class":"text-center"},
                {"key":"total_amount" ,"class":"text-center"},
                {"key":"action" ,"class":"text-center"},
            ],
            fieldsTablePurchase1: [
                {"key":"consumer_name" ,"class":"text-left"},
                {"key":"phone_no" ,"class":"text-center"},
                {"key":"email" ,"class":"text-left"},
                {"key":"user_type" ,"class":"text-left"},
                {"key":"date" ,"class":"text-center"},
                {"key":"total_amount" ,"class":"text-center"},
                {"key":"action" ,"class":"text-center"},
            ],
            usersPpList: [],
            usersPurchaseList: [],
            loading: false,
            showUsersPurchaseList:false,
            showUsersPpList:false,
            showUsersPurchaseSingleDayList:false,
            usersPurchaseSingleDayList:[],
            showPurchaseDetailsList:false,
            purchaseDetailsList:[],
            consumerDetails:{},
            consumerTransactionDetails:{},
            modal_header: null,
            headerTextVariant: "light",
            fieldsTablePurchase1filter: '',
            fieldsTablePurchasefilter: '',
            showEnrollmentReport:false,
            enrollmentReport: [],
            totalEnrollmentItems: 0,
            currentPage: 1,
            perPage: 20,
            selectedUser: [],
            userId: "",
            first_name: "",
            middle_name: "",
            last_name: "",
            street_address: "",
            apt_number: "",
            city: "",
            state: "",
            zipcode: "",
            status: {},
            date_of_birth:"",
            purchase_power:"",
            weekly_spending_limit:"",
            automatic_purchase_power:"",
            existing_user:"",
            required_upload_document:"",
            active_allow_transaction:"",
            is_active_allow_transaction: "",
            automatic_weekly_spending_limit:"",
            is_automatic_purchase_power_enable: "",
            show_purchase_power:true,
            statusList: [],
            showRefreshBtn:false,
            viewMatchedUsersList:false,
            bankMatchedUsersList: [],
            allUserAccounts: {},
            account_owner_history: {},
            consumerIdArray: [],
            user_review_comment: "",
            need_review_user_count: 0,
            review_history: {},
            selected_review_users: {},
            userComments: {},
            consumer_comment: null,
            columnData:[{label: 'Source', key: 'source', thStyle:"width:78%!important;"},
                            {label: 'Score ', key:'score',thStyle:"width:22%!important;"}],
            rowData:[],
            source_name: "",
            source_phone: "",
            source_address: "",
            source_dob: "",
            source_ssn: "",
            input_name: "",
            input_phone: "",
            input_address: "",
            risk_score_value:"",
            input_dob: "",
            input_ssn: "",
            name_score: "",
            address_score: "",
            phone_score: "",
            ssn_score: "",
            dob_score: "",
            average_score: "",
            reason: "",
            consumer_name:"",
            consumer_phone:"",
            riskRowData:[],
            riskColumnData:[{label: 'Account Number', key: 'masked_account_no',  thStyle:"width:60%!important;"},
                            {label: 'Purchase Power Percentage(%)', key:'latest_percentage', thClass:"text-center", tdClass:"text-center", thStyle:"width:40%!important"}],
            bank_link:1,
            constants: commonConstants,
        };
    },
    computed: {
        filtered () {
            const filtered = this.purchaseDetailsList.filter(item => {
            return Object.keys(this.filters).every(key =>
                String(item[key]).includes(this.filters[key]))
            })
            return filtered.length > 0 ? filtered : [{
                consumer_name: '',
                phone_no: '',
                email: '',
                user_type: '',
                date: '',
                total_amount: '',
                action: ''
            }]
        }
    },
    components: {
        HourGlass,
        CanPayLoader
    },
    created() {
      this.showAllBankMatchedUsers();
    },
    watch: {
      currentPage(newVal){
          this.getEnrollmentReport();
      },
    },
    mounted() {
      var self = this;
      self.getUserStatus();
      $("#start-date").datepicker({
        format: "mm/dd/yyyy",
        autoclose: true,
        todayHighlight: true,
      });
      $("#end-date").datepicker({
        format: "mm/dd/yyyy",
        autoclose: true,
        todayHighlight: true,
      });
      $("#start-date , #end-date").datepicker("setDate", new Date());
      $("#start-date, #end-date").on("change", function() {
        self.showRefreshBtn = false;
      });
    },
    methods: {
        capitalizeFirstLetter(str) {
          return str.charAt(0).toUpperCase() + str.slice(1);
        },
        showconsumerdetails(phone_no, name, bank_link_type, user_id){
          let self = this;
          // intitalising few constraints
          self.riskRowData = [];
          self.risk_score_value = '';

          // providing consumer details
          self.consumer_name = name;
          self.consumer_phone = phone_no;
          self.bank_link =  bank_link_type;

          // define the paylaod
          const payload = {
            phone : phone_no,
            bank_type : bank_link_type,
            user_id : user_id
          }
          // start the loader
          self.loading = true;
          //  now call the api
          review_api
          .getconsumercognitodetail(payload)
          .then((response) => {
            if(response.code == 200){
             if(response.data.cognito_response == "success")
              {
                self.riskRowData = response.data.risk_table;
                self.risk_score_value = response.data.risk_score;

                self.source_name = response.data.log.source_name;
                self.source_phone = response.data.log.source_phone;
                self.source_address = response.data.log.source_address;
                self.source_dob = response.data.log.source_dob;
                self.source_ssn = response.data.log.source_ssn;
                self.input_name = response.data.log.input_name;
                self.input_phone = response.data.log.input_phone;
                self.input_address = response.data.log.input_address;
                self.input_dob = moment(response.data.log.input_dob).format(
                  "MM-DD-YYYY"
                );
                self.input_ssn = response.data.log.input_ssn;
                self.name_score = response.data.log.X_name_score_max;
                self.address_score = response.data.log.X_address_score_max;
                self.phone_score = response.data.log.X_phone_score_max;
                self.ssn_score = response.data.log.X_ssn_score_max;
                self.dob_score = response.data.log.X_dob_score_max;
                self.average_score = response.data.log.X_average_score_max;
                self.reason = response.data.log.outcome_reason;
                self.rowData = [];
                if (response.data.log.scores.length > 0) {
                  if(self.source_name != undefined){
                    if(self.source_name != 'N/A'){
                      self.rowData.push({ source: self.source_name, score: self.name_score })
                    }
                  }
                  if(self.source_phone != undefined){
                    if(self.source_phone != 'N/A'){
                      self.rowData.push({ source: self.source_phone, score: self.phone_score });
                    }
                  }
                  if(self.source_address != undefined){
                    if(self.source_address != 'N/A'){
                      self.rowData.push({ source: self.source_address, score: self.address_score });
                    }
                  }
                  if(self.source_dob != undefined){
                    if(self.source_dob != 'N/A'){
                      self.rowData.push({ source: self.source_dob, score: self.dob_score });
                    }
                  }
                  if(self.source_ssn != undefined){
                    if(self.source_ssn != 'N/A'){
                      self.rowData.push({ source: self.source_ssn, score: self.ssn_score });
                    }
                  }
                }
                // show the modal.
                self.$bvModal.show("view-consumer-details-modal");
              }else{
                error("Idendity validation details are not present for the consumer.")
              }
            }
            self.loading = false;
          })
          .catch((error) => {
            self.loading = false;
          })

        },
        getUsersPpLists() {
        var self = this;
        self.loading = true;
        api
            .getUsersPpLists()
            .then(function (response) {
            if (response.code == 200) {
                self.usersPpList = response.data;
                if(self.usersPpList.length > 0){
                  self.showUsersPpList = false;
                }else{
                  self.showUsersPpList = true;
                }
                self.loading = false;
            } else {
                error(response.message);
                self.loading = false;
            }
            })
            .catch(function (error) {
              // error(error);
              self.loading = false;
            });
        },
        getUsersPurchase() {
        var self = this;
        self.loading = true;
        api
            .getUsersPurchase()
            .then(function (response) {
            if (response.code == 200) {
                self.usersPurchaseList = response.data;
                if(self.usersPurchaseList.length > 0){
                  self.showUsersPurchaseList = false;
                }else{
                  self.showUsersPurchaseList = true;
                }
                self.loading = false;
            } else {
                error(response.message);
                self.loading = false;
            }
            })
            .catch(function (error) {
              // error(error);
              self.loading = false;
            });
        },
        exportUsersPpLists(){
          var self = this;
          self.loading = true;
          api
            .exportUsersPpLists()
            .then(function (response) {
              var FileSaver = require("file-saver");
              var blob = new Blob([response], {
                type: "application/xlsx",
              });
              FileSaver.saveAs(
                blob,
                moment().format("MM/DD/YYYY") + "_Users_PP_List.csv"
              );
              self.loading = false;
            })
            .catch(function (error) {
              // error(error);
              self.loading = false;
            });
        },
        exportUsersPurchase(){
          var self = this;
          self.loading = true;
          api
            .exportUsersPurchase()
            .then(function (response) {
              var FileSaver = require("file-saver");
              var blob = new Blob([response], {
                type: "application/xlsx",
              });
              FileSaver.saveAs(
                blob,
                moment().format("MM/DD/YYYY") + "_Users_Purchase.csv"
              );
              self.loading = false;
            })
            .catch(function (error) {
              // error(error);
              self.loading = false;
            });
        },
        getPurchaseDetails(report_type){
          var self = this;
          self.loading = true;
          var request = {
              'report_type':report_type,
          };
          api
              .getPurchaseDetails(request)
              .then(function (response) {
              if (response.code == 200) {
                if(report_type == 'single_day_250'){
                  self.usersPurchaseSingleDayList = response.data;
                  if(self.usersPurchaseSingleDayList.length > 0){
                    self.showUsersPurchaseSingleDayList = false;
                  }else{
                    self.showUsersPurchaseSingleDayList = true;
                  }
                }else{
                  self.purchaseDetailsList = response.data;
                  if(self.purchaseDetailsList.length > 0){
                    self.showPurchaseDetailsList = false;
                  }else{
                    self.showPurchaseDetailsList = true;
                  }
                }
                self.loading = false;
              } else {
                  error(response.message);
                  self.loading = false;
              }
              })
              .catch(function (error) {
                // error(error);
                self.loading = false;
              });
        },
        viewDetails(data){
          var self = this;
          self.loading = true;
          var request = {
              consumer_id: data.item.user_id
          };
          api
            .getConsumerTransactionHistory(request)
            .then((response) => {
              if ((response.code = 200)) {
                self.consumerDetails = response.data.header[0];
                self.consumerTransactionDetails = response.data.body;
                self.modal_header = 'Consumer Transaction Details';
                self.$bvModal.show("consumer-transaction-details-modal");
                self.loading = false;
              } else {
                error(response.message);
                self.loading = false;
              }
            })
            .catch((err) => {
              error(err);
              self.loading = false;
            });
        },
        activateUser(data,param){
          var self = this;
          var r = confirm(
            "Do you want to inactive this Consumer?"
          );
          if (r == true) {
            self.loading = true;
            var request = {
                consumer_id: data.item.user_id
            };
            api
              .activateUser(request)
              .then((response) => {
                if ((response.code = 200)) {
                  success(response.message);
                  self.getPurchaseDetails(param);
                } else {
                  error(response.message);
                  self.loading = false;
                }
              })
              .catch((err) => {
                error(err);
                self.loading = false;
              });
            } else {
              return false;
            }
        },
        getEnrollmentReport() {
          var self = this;
          $(".indivisual-checkbox").prop('checked', false);
          $(".all-checkbox").prop('checked', false);
          self.consumerIdArray = [];
          var request = {
            from_date: moment($("#start-date").val()).format("YYYY-MM-DD"),
            to_date: moment($("#end-date").val()).format("YYYY-MM-DD"),
            currentPage: self.currentPage,
            perPage: self.perPage
          };
          self.loading = true;
          api
            .getEnrollmentReport(request)
            .then(function (response) {
            if (response.code == 200) {
                self.enrollmentReport = response.data.users;
                self.need_review_user_count = self.enrollmentReport.filter((element) => element.reviewed_user_name == '').length;
                self.totalEnrollmentItems = response.data.total;
                if(self.enrollmentReport.length > 0){
                  self.showEnrollmentReport = false;
                  self.showRefreshBtn = true;
                }else{
                  self.showEnrollmentReport = true;
                  self.showRefreshBtn = false;
                }
                self.loading = false;
            } else {
                error(response.message);
                self.loading = false;
            }
            })
            .catch(function (error) {
              // error(error);
              self.loading = false;
            });
        },
        exportEnrollmentReport() {
        var self = this;
        var request = {
          from_date: moment($("#start-date").val()).format("YYYY-MM-DD"),
          to_date: moment($("#end-date").val()).format("YYYY-MM-DD"),
          is_export: true,
        };
        self.loading = true;
        api
            .exportEnrollmentReport(request)
            .then(function (response) {
              var FileSaver = require("file-saver");
              var blob = new Blob([response], {
                type: "application/xlsx",
              });
              FileSaver.saveAs(
                blob,
                moment().format("MM/DD/YYYY") + "_Users_Enrollment_List.csv"
              );
              self.loading = false;
            })
            .catch(function (error) {
              // error(error);
              self.loading = false;
            });
        },
        getExportEnrollmentReport(is_export = false) {
          var self = this;
          if ($("#start-date").val() != "") {
            var from_date = moment($("#start-date").val()).format("YYYY-MM-DD");
          } else {
            var from_date = "";
          }
          if ($("#end-date").val() != "") {
            var to_date = moment($("#end-date").val()).format("YYYY-MM-DD");
          } else {
            var to_date = "";
          }
          if(from_date!='' && to_date!=''){
            if(from_date > to_date){
              error("To date cannot be greater than From date");
              return false;
            }
            //calculate the date Difference
            var date1 = new Date(from_date);
            var date2 = new Date(to_date);
            // To calculate the time difference of two dates
            var Difference_In_Time = date2.getTime() - date1.getTime();

            // To calculate the no. of days between two dates
            var Difference_In_Days = Difference_In_Time / (1000 * 3600 * 24);

            if(Difference_In_Days > 3){
              error('The date difference cannot be greater than 3 days.');
            }else{
              is_export ? self.exportEnrollmentReport() : self.getEnrollmentReport();
            }
          }
        },
        editconsumer(userID) {
          var self = this;
          self.userId = userID;
          var request = {
              'userID':userID,
          };
          user_api.getconsumerdetails(request)
          .then(response => {
              if (response.code == 200) {
                  self.first_name = response.data.consumerDetails.first_name;
                  self.middle_name = response.data.consumerDetails.middle_name;
                  self.last_name = response.data.consumerDetails.last_name;
                  self.street_address = response.data.consumerDetails.street_address;
                  self.apt_number = response.data.consumerDetails.apt_number;
                  self.city = response.data.consumerDetails.city;
                  self.state = response.data.consumerDetails.state;
                  self.zipcode = response.data.consumerDetails.zipcode;
                  self.status.id = response.data.consumerDetails.status;
                  self.status.text = (response.data.consumerDetails.status_name).replace("User ", "");
                  if(response.data.consumerDetails.date_of_birth!=null && response.data.consumerDetails.date_of_birth!='0000-00-00'){
                    self.date_of_birth = moment(response.data.consumerDetails.date_of_birth).format("MM-DD-YYYY");
                  }else{
                    self.date_of_birth = "";
                  }
                  self.purchase_power = response.data.consumerDetails.standard_daily_limit!=null ? response.data.consumerDetails.standard_daily_limit : response.data.consumerDetails.purchase_power;
                  self.weekly_spending_limit = response.data.consumerDetails.weekly_spending_limit;
                  self.automatic_purchase_power = response.data.consumerDetails.disable_automatic_purchase_power == 0 ? 1 : 0;
                  self.automatic_weekly_spending_limit = response.data.consumerDetails.disable_automatic_weekly_spending_limit == 0 ? 1 : 0;
                  self.existing_user = response.data.consumerDetails.existing_user;
                  self.required_upload_document = response.data.consumerDetails.required_upload_document;
                  self.active_allow_transaction = response.data.consumerDetails.active_allow_transaction;
                  if(self.automatic_purchase_power == 1){
                    self.show_purchase_power = false;
                    self.purchase_power = "";
                  }else{
                    self.show_purchase_power = true;
                  }
                  self.$bvModal.show("consumer-modal");
                  self.is_active_allow_transaction = response.data.is_active_allow_transaction;
                  self.is_automatic_purchase_power_enable = response.data.is_automatic_purchase_power_enable;
              } else {
                  error(response.message);
              }
          })
          .catch(err => {
              error(err.response.data.message);
          });
        },
        resetModal() {
          var self = this;
          self.selectedUser = [];
        },
        handleOk(bvModalEvt) {
          var self = this;
          // Prevent modal from closing
          bvModalEvt.preventDefault();
          // Trigger submit handler
          self.changeStatus();
        },
        changeStatus() {
          var self = this;
          if(self.yearsDiff(moment($("#dob").val())) < process.env.MIX_REGISTRATION_AGE_LIMIT){
            error("Minimum age should be "+process.env.MIX_REGISTRATION_AGE_LIMIT);
            return false;
          }else{
            this.$validator.validateAll().then((result) => {
            if (result) {
            if($("#dob").val()!=''){
              var date_of_birth = moment($("#dob").val()).format("YYYY-MM-DD")
            }else{
              var date_of_birth = '';
            }

            if(date_of_birth=="Invalid date")
            {
              error("please enter in valid dob format mm-dd-yyyy");
              return false;
            }

              var request = {
                id: self.userId,
                status: self.status.id,
                first_name: self.first_name,
                middle_name: self.middle_name,
                last_name: self.last_name,
                street_address: self.street_address,
                apt_number: self.apt_number,
                city: self.city,
                state: self.state,
                zipcode: self.zipcode,
                date_of_birth: date_of_birth,
                purchase_power: self.purchase_power,
                weekly_spending_limit: self.weekly_spending_limit,
                automatic_purchase_power: self.automatic_purchase_power,
                required_upload_document: self.required_upload_document,
                active_allow_transaction: self.active_allow_transaction,
                automatic_weekly_spending_limit : self.automatic_weekly_spending_limit,

              };
              user_api
              .changeUserStatus(request)
              .then((response) => {
                if ((response.code = 200)) {
                  success(response.message);
                  self.$bvModal.hide("consumer-modal");
                  self.dob_year = '';
                  self.dob_day = '';
                  self.month_number = '';
                  self.dob_month = "Month";
                  self.automatic_purchase_power = '';
                  self.getEnrollmentReport();
                } else {
                  error(response.message);
                }
              })
              .catch((err) => {
                error(err.response.data.message || err);
              });
              }
            });
          }
        },

        getPurchasePower(){
          var self = this;
          if($("#automatic_pp").prop("checked") == true){
            self.show_purchase_power = false;
            self.purchase_power = "";
          }else{
            self.show_purchase_power = true;
          }
        },
        hideModal(modalID){
          var self = this;
          self.$bvModal.hide(modalID);
        },
        getUserStatus() {
          var self = this;
          user_api
            .getUserStatus()
            .then((response) => {
              if ((response.code = 200)) {
                self.statusList = response.data;
              } else {
                error(response.message);
              }
            })
            .catch((err) => {
              error(err);
            });
        },
        isNumber: function (evt) {
          evt = evt ? evt : window.event;
          var charCode = evt.which ? evt.which : evt.keyCode;
          if (
            charCode != 46 &&
            charCode != 45 &&
            charCode > 31 &&
            (charCode < 48 || charCode > 57)
          ) {
          evt.preventDefault();
          } else {
            return true;
          }
        },
        yearsDiff(d1) {
          let date1 = new Date(d1);
          let date2 = new Date();
          let yearsDiff =  date2.getFullYear() - date1.getFullYear();
          return yearsDiff;
        },
        formattedBankMatchedUsers(row) {
          var bankMatchedUsers = row.bank_matched_users;
          if(bankMatchedUsers != null){
              var bankMatchedUsersArray = bankMatchedUsers.split("|");
              if (bankMatchedUsersArray.length === 0) {
                  return '';
              } else if (bankMatchedUsersArray.length === 1) {
                  return bankMatchedUsersArray[0];
              } else if (bankMatchedUsersArray.length === 2) {
                  return bankMatchedUsersArray.slice(0, 2).join('|');
              } else {
                  const truncatedValue = bankMatchedUsersArray.slice(0, 2).join('|');
                  const linkHtml = `<a type="button" class="viewAllBankMatchedUsers custom-edit-btn" title="View All Matched Users" variant="outline-success" data-users="${row.bank_matched_users}"><i class="nav-icon fas fa-eye"></i></a>`;
                  return `${truncatedValue}... ${linkHtml}`;
              }
          }
          return '';
        },
        showAllBankMatchedUsers(){
          var self = this;
          $(document).on("click", ".viewAllBankMatchedUsers", function (e) {
            //open the modal
            var bankMatchedUsers = $(e.currentTarget).attr("data-users");
            self.bankMatchedUsersList = bankMatchedUsers.split("|");
            self.viewMatchedUsersList = true;
            self.$bvModal.show("all-bank-matched-users-modal");
          });

        },
        onEmailCopy(){
          success('Email copied successfully');
        },
        onEmailCopyError(){
          error('Failed to copy Email');
        },
        onPhoneCopy(){
          success('Phone copied successfully');
        },
        onPhoneCopyError(){
          error('Failed to copy Phone');
        },
        searchConsumerAccounts(phone_no){
          var self = this;
          var request = {
            phone_no:phone_no,
          };
          self.loading = true;
          user_api
          .searchConsumerAccounts(request)
          .then(function (response) {
            if (response.code == 200) {
              self.allUserAccounts = response.data;
              self.$bvModal.show("manage-account-owner-info-modal");
              self.loading = false;
            } else {
              error(response.message);
              self.loading = false;
            }
          })
          .catch(function (error) {
            // error(error);
            self.loading = false;
          });
        },
        callAccountOwnerApi(row){
          var self = this;
          if(row.last_call_date!=null){
              var alert_msg = 'Account Owner API last called for this Account on '+row.last_call_date+'. Do you want to call it again?';
          }else{
              var alert_msg = 'Account Owner API is a paid API? Do you want to proceed?';
          }
          Vue.swal({
                title: "Warning!!!",
                text: alert_msg,
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#149240",
                confirmButtonText: "Yes, proceed!",
                cancelButtonText: "No, cancel it!",
                closeOnConfirm: true,
                closeOnCancel: true,
                width: '800px'
                }).then((result) => {
                    if (result.isConfirmed) {
                        var request = {
                            user_id: row.edit,
                            account_id: row.account_id,
                        };
                        self.loading = true;
                        user_api
                        .callAccountOwnerApi(request)
                        .then(function (response) {
                            if (response.code == 200) {
                                Vue.swal("Done!", response.message, "success");
                                self.searchConsumerAccounts(row.phone);
                            }
                            self.loading = false;
                        })
                        .catch(function (error) {
                            // Retrieve error message from the response
                    var message = error.response?.data?.message || "An error occurred.";
                    console.error("Error:", error);
                    Vue.swal("Error!", message, "error");
                    self.loading = false;
                        });
                    }
                })
        },
        showHistory(row){
          var self = this;
          var request = {
              user_id: row.edit,
              account_id: row.account_id,
          };
          self.loading = true;
          user_api
          .showAccountOwnerHistory(request)
          .then(function (response) {
              self.account_owner_history = response.data;
              self.$bvModal.show("account-owner-history-modal");
              self.loading = false;
          })
          .catch(function (error) {
              // error(error);
              self.loading = false;
          });
        },
        resetUserRevirwModal() {
          $(".indivisual-checkbox").prop('checked', false);
        },
        onCheckEvent(event, row) {
          var self = this;
          self.consumerIdArray = []; // Remove this when admin need multiple select
          if (event.target.checked) {
            self.consumerIdArray.push(row.user_id);
            self.addReview(); // Remove this when admin need multiple select
          } else {
            self.consumerIdArray = self.consumerIdArray.filter((element) => element !== row.user_id);
          }
          if (self.need_review_user_count == self.consumerIdArray.length) {
            $(".all-checkbox").prop('checked', true);
          } else {
            $(".all-checkbox").prop('checked', false);
          }
        },
        // on select all users
        selectAll(event){
          var self = this;
          self.consumerIdArray = [];
          if (event.target.checked) {
            $(".indivisual-checkbox").prop('checked', true);
            var needReviewUsers = self.enrollmentReport.filter((element) => element.reviewed_user_name == '');
            needReviewUsers.forEach(user => {
                self.consumerIdArray.push(user.user_id);
            });
          } else {
            $(".indivisual-checkbox").prop('checked', false);
          }
        },
        getInitials(name) {
          const words = name.split(' ');
          let initials = '';

          words.forEach(word => {
            initials += word.charAt(0).toUpperCase();
          });

          return initials;
        },
        getReviewedTitle(name) {
          return 'Reviewed By '+name;
        },
        viewReviewDetails(user_id) {
          var self = this;
          var request = {
            userID: user_id
          };
          self.loading = true;
          api
          .getUserReviewData(request)
          .then((response) => {
            if ((response.code = 200)) {
              success(response.message);
              self.review_history = response.data;;
              self.$bvModal.show("get-user-review-comments-modal");
            } else {
              error(response.message);
            }
            self.loading = false;
          })
          .catch((err) => {
            error(err);
          });
        },
        addReview() {
          var self = this;
          self.selected_review_users = self.enrollmentReport.filter((element) => self.consumerIdArray.includes(element.user_id));
          this.$bvModal.show("user-review-comments-modal");
        },
        handleOkReviewComment(bvModalEvt) {
          var self = this;
          // Prevent modal from closing
          bvModalEvt.preventDefault();
          // Trigger submit handler
          self.saveReviewComment();
        },
        saveReviewComment(){
          var self = this;
          var request = {
            userIDs: self.consumerIdArray,
            comment: self.user_review_comment
          };
          api
          .saveUserReviewComment(request)
          .then((response) => {
            if ((response.code = 200)) {
              success(response.message);
              self.getEnrollmentReport();
              self.user_review_comment = null;
            } else {
              error(response.message);
            }
            self.$bvModal.hide("user-review-comments-modal");
          })
          .catch((err) => {
            error(err);
          });
        },
        commentconsumer(userID) {
          var self = this;
          self.loading = true;
          self.userId = userID;
          var request = {
              'userID':userID,
          };
          user_api.getconsumercomments(request)
          .then(response => {
              if (response.code == 200) {
                  self.userComments = response.data;
                  self.loading = false;
                  self.$bvModal.show("consumer-comments-modal");
              } else {
                  self.loading = false;
                  error(response.message);
              }
          })
          .catch(err => {
              self.loading = false;
              error(err.response.data.message);
          });
        },
        handleOkComment(bvModalEvt) {
            var self = this;
            // Prevent modal from closing
            bvModalEvt.preventDefault();
            // Trigger submit handler
            self.saveComment();
        },
        saveComment(){
          this.$validator.validateAll().then((result) => {
            if (result) {
              var self = this;
              self.loading = true;
              var request = {
                  userID: self.userId,
                  comment: self.consumer_comment
              };
              user_api
              .saveconsumercomment(request)
              .then((response) => {
                  if ((response.code = 200)) {
                      self.loading = false;
                      success(response.message);
                      self.userComments = response.data;
                      self.consumer_comment = null;
                  } else {
                      self.loading = false;
                      error(response.message);
                  }
              })
              .catch((err) => {
                  self.loading = false;
                  error(err);
              });
            }
          });
        },
        retryApiCall(record_id){
        var self = this;
        var alert_msg = 'Do you want to retry?';
        Vue.swal({
            title: "Warning!!!",
            text: alert_msg,
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#149240",
            confirmButtonText: "Yes, proceed!",
            cancelButtonText: "No, cancel it!",
            closeOnConfirm: true,
            closeOnCancel: true,
            width: '800px'
            }).then((result) => {
                if (result.isConfirmed) {
                    var request = {
                        record_id: record_id,
                    };
                    self.loading = true;
                    user_api
                    .retryAccountOwnerApiCall(request)
                    .then(function (response) {
                        if (response.code == 200) {
                            Vue.swal('Done!',response.message,'success');

                            if (self.allUserAccounts && Array.isArray(self.allUserAccounts)) {
                                self.allUserAccounts.forEach((row) => {
                                    row.account_owner_called = 1; // Set to 1 for all records
                                });
                            }
                            self.loading = false;
                        }
                        self.loading = false;
                    })
                    .catch(function (error) {
                        var message = error.response.message;
                        // error(error);
                        Vue.swal('Error!', message, 'error');
                        self.loading = false;
                    });
                }
            });
    },

    },
};
</script>
<style scoped>
.td-data-risk-score-percentage {
    margin-left: 10px; /* Adjust as needed */
}
.fa-info-circle {
  margin-left: 6px !important;
}
</style>
