<template>
<div>
    <div v-if="loading">
      <CanPayLoader/>
    </div>
    <div class="content-wrapper" style="min-height: 36px">
      <section class="content-header">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Merchant Key</h3>
                </div>

                <!-- /.card-header -->
                <div class="card-body">
                  <div class="row">
                  <div class="col-md-4">
                    <div class="form-group">
                     <input
                        class="form-control"
                        placeholder="Retailer (min 3 chars)"
                        id="merchant_name"
                        v-model="merchant_name"
                      />
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                     <input
                        class="form-control"
                        placeholder="Store ID (Exact)"
                        id="merchant_id"
                        v-model="merchant_id"
                      />
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <select
                      id="category_id"
                      name="category_id"
                      placeholder="Select ecommerce category"
                      type="text"
                      v-model="category_id"
                      class="form-control custom-select"
                      >
                        <option selected hidden value="">Select ecommerce category</option>
                        <option v-for="category in ecomCategoryList" :key="category.id" :value="category.id">{{category.category_name}}</option>
                      </select>
                    </div>
                  </div>
                </div>
                <div class="row">
                  <div class="col-md-4">
                    <label for="merchant">
                      Select Corporate Parent
                    </label>
                    <multiselect
                    name="cp_search"
                    id="cp_search"
                    v-model="selected_cp"
                    placeholder="Select Corporate Parent (Min 3 chars)"
                    label="name"
                    :options="CPList"
                    :loading="isLoading"
                    :internal-search="false"
                    @search-change="getAllCP"
                    ></multiselect>
                    </div>
                </div>
                </div>
                  <div class="card-footer">
                    <button
                      type="button"
                      class="btn btn-success"
                      @click="searchMerchantKeys(false)"
                    >
                      Search
                    </button>
                    <button
                      type="button"
                      @click="reset()"
                      class="btn btn-success margin-left-5"
                    >
                      Reset
                    </button>
                    <button
                      type="button"
                      @click="searchMerchantKeys(true)"
                      class="btn btn-danger ml-10"
                    >Export<i
                        class="fa fa-download ml-10"
                        aria-hidden="true"
                      ></i>
                    </button>
                    <b-button
                  @click="openModal('add')"
                  class="btn btn-success margin-left-5"
                >
                  <i class="far fa-plus-square"></i> Add Merchant Key
                </b-button>
                  </div>
                  <div class="card-body">
                  <b-table-simple
                      responsive
                      show-empty
                      bordered
                      sticky-header="800px"
                      v-if="allMerchantKeys.length > 0"
                    >
                      <b-thead head-variant="light">
                        <tr>
                          <th>Store ID</th>
                          <th>Retailer</th>
                          <th>Integrator ID</th>
                          <th>CanPay Internal Version</th>
                          <th>App Key</th>
                          <th>Api Secret</th>
                          <th>Created At</th>
                          <th class="text-center">Action(s)</th>
                        </tr>
                      </b-thead>
                      <b-tbody v-for="(row, index) in allMerchantKeys" :key="index">
                        <b-tr>
                          <b-td class="text-left text-gray">{{
                            row.store_id
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.retailer
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.integrator_id
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.category_code
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.app_key
                          }}</b-td>
                          <b-td class="text-left text-gray"><span :id="row.id">{{row.api_secret.replace(row.api_secret.substring(0, 5),'*****')}}</span>
                          <a :data-user-id="row.id" v-clipboard:copy="row.api_secret" v-clipboard:success="onCopy" v-clipboard:error="onCopyError" class="custom-edit-btn" title="Copy API Secret"  variant="outline-success" style="border:none"><i class="nav-icon fas fa-copy"></i></a>
                          <a :data-user-id="row.id" class="viewStoreApiSecret custom-edit-btn" title="View API Secret" variant="outline-success" style="border:none"><i class="nav-icon fas fa-eye"></i></a>
                          </b-td>
                          <b-td class="text-left text-gray">{{
                            row.created_at
                          }}</b-td>
                          <b-td class="text-center text-gray">
                            <label v-if="!isStatusLoading" class="switch"><input @click="changeStatus(row)" class="enable-employee-login" type="checkbox" true-value="1" false-value="0" :checked="row.status_master == 'Active' ? 1 : 0"><span class="slider round"></span></label>
                            <span v-if="isStatusLoading == row.id" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                          </b-td>
                        </b-tr>
                      </b-tbody>
                    </b-table-simple>
                    <p v-else>No data displayed. Please refine your search criteria.</p>
                    </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- CP Modal Start -->
      <b-modal
        id="cp-modal"
        ref="modal"
        :header-text-variant="headerTextVariant"
        :title="modalTitle"
        @show="resetModal"
        @hidden="resetModal"
        :ok-title="modalOkTitle"
        ok-variant="success"
        cancel-variant="outline-secondary"
        @ok="handleOk"
        :no-close-on-esc="true"
        :no-close-on-backdrop="true"
      >
        <form ref="form" @submit.stop.prevent="save" class="needs-validation">
          <div v-if="modelstep == 1" class="step-1">
            <div class="row">
               <div class="col-md-12">
                  <p> You need to select or create integrator for Create Merchant Key</p>
               </div>
            </div>
            <div class="row">
              <div class="col-md-12">
                <input type="radio" v-validate="'required'" name="integrator_select_type" id="integrator_select_type_select" value="select" v-model="merchantKeyDetails.integratorSelectType">&nbsp;<label for="integrator_select_type_select">Select Integrator <span class="red">*</span></label> &nbsp;&nbsp;
                <input type="radio" v-validate="'required'" name="integrator_select_type" id="integrator_select_type_create" value="create" v-model="merchantKeyDetails.integratorSelectType">&nbsp;<label for="integrator_select_type_create">Create New Integrator <span class="red">*</span></label> &nbsp;&nbsp;<br />
                <span v-show="errors.has('integrator_select_type')" class="text-danger">Please Choose one</span>
              </div>
            </div>
            <div class="row" v-if="merchantKeyDetails.integratorSelectType == 'select'">
              
              <div class="col-md-8">
                <label for="category">
                  Select Integrator
                  <span class="red">*</span>
                </label>
                <multiselect
                    name="select_integrator"
                    id="select_integrator"
                    v-validate="'required'"
                    v-model="selectedIntegrator"
                    placeholder="Select Integrator (Min 3 chars)"
                    label="ecommerce_integrator_name"
                    :options="ecomIntegratorList"
                    :loading="isLoading"
                    :internal-search="false"
                    :multiselect="false"
                    @search-change="getAllEcommerceIntegrators"
              ></multiselect>
                <span v-show="errors.has('select_integrator')" class="text-danger">The select integrator field is required</span>
              </div>
            </div>
            <div v-if="merchantKeyDetails.integratorSelectType == 'create'">
               <div class="row">
                    <div class="col-md-12">
                        <label for="location_type">
                            Location Type
                            <span class="red">*</span>
                        </label>
                        <select
                            class="form-control"
                            id="location_type"
                            name="location_type"
                            v-model="integratorModel.location_type"
                            v-validate="'required'"
                        >
                            <option
                                v-for="(location, index) in locationTypeArray"
                                :key="index"
                                :value="location.location_type"
                            >
                                {{ location.location_type }}
                            </option>
                        </select>
                        <span
                            v-show="errors.has('location_type')"
                            class="text-danger"
                            >{{ errors.first("location_type") }}</span
                        >
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-8">
                        <label for="ecommerce_integrator_name">
                            Ecommerce Integrator Name
                            <span class="red">*</span>
                        </label>
                        <input
                            id="ecommerce_integrator_name"
                            name="ecommerce_integrator_name"
                            v-validate="'required|min:3'"
                            type="text"
                            v-model="integratorModel.ecommerce_integrator_name"
                            class="form-control"
                        />
                        <span
                            v-show="errors.has('ecommerce_integrator_name')"
                            class="text-danger"
                            >{{
                                errors.first("ecommerce_integrator_name")
                            }}</span
                        >
                    </div>
                </div>

                <br />
                <h4>RemotePay Setting</h4>

                <div class="row">
                    <div class="col-md-6">
                        <label for="allow_ecommerce_transaction">
                            RemotePay Transaction
                        </label>

                        <label class="switch"
                            ><input
                                type="checkbox"
                                id="allow_ecommerce_transaction"
                                name="allow_ecommerce_transaction"
                                v-model="
                                    integratorModel.allow_ecommerce_transaction
                                "
                                true-value="1"
                                false-value="0"
                                class="enable-employee-login" /><span
                                class="slider round"
                            ></span
                        ></label>
                    </div>

                    <div class="col-md-6">
                        <label for="allow_ecommerce_transaction">
                            Consumer Auth
                            <span class="red">*</span>
                        </label>

                        <label class="switch"
                            ><input
                                type="checkbox"
                                id="allow_consumer_auth"
                                name="allow_consumer_auth"
                                v-model="integratorModel.allow_consumer_auth"
                                true-value="1"
                                false-value="0"
                                class="enable-employee-login" /><span
                                class="slider round"
                            ></span
                        ></label>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <label for="integrator_logo"> Integrator Logo </label>
                        <div class="input-group">
                            <div class="custom-file">
                                <input
                                    type="file"
                                    ref="integrator_logo_file"
                                    name="integrator_logo"
                                    id="integrator_logo"
                                    class="custom-file-input"
                                    accept="image/*"
                                    v-on:change="handleFileUpload()"
                                />
                                <label
                                    for="integrator_logo"
                                    class="custom-file-label"
                                    >{{ integrator_logo_fileName }}</label
                                >
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6"></div>
                    <div class="col-md-6">
                        <b-button
                            style="float: right; margin-top: 7px"
                            @click="clear"
                        >
                            Clear File
                        </b-button>
                    </div>
                </div>
            </div>
          </div>
          <div v-if="modelstep == 2" class="step-2">
            <div class="row">
               <div class="col-md-12">
                  <p> You need to select or create category for Create Merchant Key</p>
               </div>
            </div>
            <div class="row">
              <div class="col-md-12">
                <input type="radio" v-validate="'required'" name="category_select_type" id="category_select_type_select" value="select" v-model="merchantKeyDetails.categorySelectType">&nbsp;<label for="category_select_type_select">Select Category <span class="red">*</span></label> &nbsp;&nbsp;
                <input type="radio" v-validate="'required'" name="category_select_type" id="category_select_type_create" value="create" v-model="merchantKeyDetails.categorySelectType">&nbsp;<label  for="category_select_type_create">Create New Category <span class="red">*</span></label> &nbsp;&nbsp;<br />
                <span v-show="errors.has('integrator_select_type')" class="text-danger">Please Choose one</span>
              </div>
            </div>
            
            <div class="row" v-if="merchantKeyDetails.categorySelectType == 'select'">
              <div class="col-md-8">
                <label for="category">
                  Select Category
                  <span class="red">*</span>
                </label>
                <select
                id="category"
                name="category"
                v-validate="'required'"
                placeholder="Select ecommerce category"
                type="text"
                v-model="merchantKeyDetails.selected_category"
                class="form-control custom-select"
                >
                  <option v-for="category in ecomCategoryList" :key="category.id" :value="category.id">{{category.category_name}}</option>
                </select>
                <span v-show="errors.has('category')" class="text-danger">{{errors.first("category")}}</span>
              </div>
            </div>
            <div v-if="merchantKeyDetails.categorySelectType == 'create'">
              <div class="row">
                <div class="col-md-9">
                  <label for="category_name">
                    Category Name
                    <span class="red">*</span>
                  </label>
                  <input
                  id="category_name"
                  name="category_name"
                  v-validate="'required|alpha|max:255'"
                  type="text"
                  v-model="categoryModel.category_name"
                  class="form-control"
                  />
                      <span v-show="errors.has('category_name')" class="text-danger">{{errors.first("category_name")}}</span>
                  </div>
              </div>

              <div class="row">
                <div class="col-md-9">
                  <label for="category_code">
                    CanPay Internal Version
                    <span class="red">*</span>
                  </label>
                  <input
                  id="category_code"
                  name="category_code"
                  v-validate="'required|alpha_num|max:10'"
                  type="text"
                  v-model="categoryModel.category_code"
                  class="form-control"
                  />
                  <small class="text-green">This code will be shared to the integrator. This can be digits, characters or combo of both. This should be min of 1 char and max of 10 chars.</small>
                  <span v-show="errors.has('category_code')" class="text-danger">{{errors.first("category_code")}}</span>
                  </div>
              </div>

              <div class="row">
                <div class="col-md-9">
                  <label for="nomenclature">
                    Nomenclature
                    <span class="red">*</span>
                  </label>
                  <input
                  id="nomenclature"
                  name="nomenclature"
                  v-validate="'required|alpha|min:1|max:3'"
                  type="text"
                  v-model="categoryModel.nomenclature"
                  class="form-control"
                  />
                  <small class="text-green">This will be used to generate the APP Key for the merchants. This should be min of 1 char and max of 3 chars.</small><br>
                      <span v-show="errors.has('nomenclature')" class="text-danger">{{errors.first("nomenclature")}}</span>
                  </div>
              </div>
            </div>
          </div>
          <div v-if="modelstep == 3" class="step-3">
            <div class="row">
              <div class="col-md-8">
                <label for="merchant">
                  Select Retailer
                  <span class="red">*</span>
                </label>
                <multiselect
                name="reatiler"
                id="reatiler"
                v-model="selectedRetailer"
                placeholder="Select Retailer (Min 3 chars)"
                label="retailer"
                :options="merchantList"
                :loading="isLoading"
                :internal-search="false"
                @search-change="getAllMerchants"
                v-validate="'required'"
                ></multiselect>
                    <span v-show="errors.has('reatiler')" class="text-danger">{{errors.first("reatiler")}}</span>
                </div>
            </div>

          </div>
        </form>
      </b-modal>
      <!-- CP Modal End -->
    </div>
</div>
</template>
<script>
import api from "@/api/intregator.js";
import user_api from "@/api/user.js";
import { validationMixin } from "vuelidate";
import { required, minLength } from "vuelidate/lib/validators";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "../CustomLoader/CanPayLoader.vue"
import moment from "moment";
export default {
  mixins: [validationMixin],
  data() {
    return {
      modalTitle: "",
      modalOkTitle: "Next",
      modelstep: 1,
      headerTextVariant: "light",
      merchantKeyDetails: {},
      allMerchantKeys: {},
      merchantList: [],
      CPList: [],
      ecomCategoryList: [],
      ecomIntegratorList: [],
      locationTypeArray: [
        { location_type: "Web" },
        { location_type: "Retail" },
      ],
      loading:false,
      isLoading: false,
      isStatusLoading: undefined,
      merchant_name:"",
      merchant_id:"",
      category_id:"",
      selected_cp:"",
      integrator_logo_file: null,
      integrator_logo_fileName: "Choose File",
      integrator_logo_image:null,
      integratorModel: {},
      categoryModel: {},
      selectedRetailer: {},
      selectedIntegrator: {},
    };
  },
  components: {
    HourGlass,
    CanPayLoader
  },
  created(){
    this.getAllEcommerceCategories();
    this.viewStoreApiSecret();
  },
  methods: {
    clear(e) {
      var self = this;
      self.integrator_logo_file = null;
      self.integrator_logo_fileName = "Choose File";
      self.$refs.integrator_logo_file.value = null;
      e.preventDefault();
    },
    handleFileUpload() {
      let self = this;
      self.integrator_logo_file = self.$refs.integrator_logo_file.files[0];
      self.integrator_logo_fileName = self.$refs.integrator_logo_file.files[0].name;
      self.upload_changed = true;
      self.integrator_logo_image=null
    },
    onCopy(){
      success('API Secret copied successfully');
    },
    onCopyError(){
      error('Failed to copy API Secret');
    },
    viewStoreApiSecret(){
      var self = this;
      $(document).on("click", ".viewStoreApiSecret", function (e) {
        self.findedMerchantKeys = self.allMerchantKeys.find(
          (p) => p.id == $(e.currentTarget).attr("data-user-id")
        );
        $("#"+self.findedMerchantKeys.id).html(self.findedMerchantKeys.api_secret);
      });
    },
    //API call to fetch All stores
    getAllMerchants(searchtxt) {
      var self = this;
      if(searchtxt.length >= 3){
        self.isLoading = true;
        var request = {
          searchtxt: searchtxt,
        };
      api
        .getAllMerchants(request)
        .then(function (response) {
          if (response.code == 200) {
            self.merchantList = response.data;
            self.isLoading = false;
          }else {
            error(response.message);
            self.isLoading = false;
          }
        })
        .catch(function (error) {
        });
      }
    },
    //API call to fetch All CP
    getAllCP(searchtxt) {
      var self = this;
      if(searchtxt.length >= 3){
        self.isLoading = true;
        var request = {
          corporate_parent: searchtxt,
        };
      user_api
        .searchCorporateParents(request)
        .then(function (response) {
          if (response.code == 200) {
            self.CPList = response.data;
            self.isLoading = false;
          }else {
            error(response.message);
            self.isLoading = false;
          }
        })
        .catch(function (error) {
        });
      }
    },
    //API call to fetch All ecommerce categories
    getAllEcommerceCategories() {
      var self = this;
      api
        .getAllEcommerceCategory()
        .then(function (response) {
          if (response.code == 200) {
            self.ecomCategoryList = response.data;
          }else {
            error(response.message);
          }
        })
        .catch(function (error) {
        });
    },
    //API call to fetch All ecommerce Integrators
    getAllEcommerceIntegrators(searchtxt) {
      if(searchtxt.length >= 3){
        var self = this;
        self.isLoading = true;
        var request = {
          integrator_name: searchtxt,
        };
        api
          .getAllEcommerceIntegrators(request)
          .then(function (response) {
            if (response.code == 200) {
              self.ecomIntegratorList = response.data;
            }else {
              error(response.message);
            }
            self.isLoading = false;
          })
          .catch(function (error) {
            self.isLoading = false;
          });
      }
    },
    openModal(type) {
      var self = this;
      if (type == "add") {
        self.modalTitle = "Create Merchant Key";
        self.modalOkTitle = "Next";
        self.ecomIntegratorList = [];
        self.modelstep = 1;
        self.$bvModal.show("cp-modal");
      }
    },
    resetModal() {
      var self = this;
      self.merchantKeyDetails = {};
      self.integratorModel = {};
      self.categoryModel = {};
      self.selectedRetailer = {};
      self.selectedIntegrator = {};
    },
    handleOk(bvModalEvt) {
      var self = this;
      // Prevent modal from closing
      bvModalEvt.preventDefault();
      // Trigger submit handler
      self.save();
     
    },
    save() {
      var self = this;
      // Exit when the form isn't valid
      this.$validator.validateAll().then((result) => {
        if (result) {
          if(self.modelstep == 1){
            self.modelstep = 2;
            return false;
          }else if(self.modelstep == 2){
            self.modelstep = 3;
            self.modalOkTitle = 'Submit';
            return false;
          }
          if(self.modelstep == 3){
            self.merchantKeyDetails.integratorModel = self.integratorModel;
            self.merchantKeyDetails.categoryModel = self.categoryModel;
            //call to api to save the details
            // Add section
            if(self.merchantKeyDetails.integratorSelectType == 'create' && self.integrator_logo_file){
              let formData = new FormData();
              formData.append("logo", self.integrator_logo_file);
              user_api.importIntegratorLogo(formData).then(
                  (response) => {
                      self.merchantKeyDetails.integratorModel.integrator_logo =
                          response.data;
                      self.addMerchantKeys();
                  }
              );
            }else{
              if(self.merchantKeyDetails.integratorSelectType == 'select') {
                self.merchantKeyDetails.selected_integrator = self.selectedIntegrator.id;
              }
              self.addMerchantKeys();
            }
                    
          }
        }
      }).catch((err) => {
        error(err.response.data.message);
      });;
    },
    addMerchantKeys(){
      var self = this;
      let merchant_id = self.selectedRetailer.store_id;
      self.merchantKeyDetails.retailer = self.selectedRetailer.id;
      api
        .addMerchantKey(self.merchantKeyDetails)
        .then((response) => {
          self.merchant_id = merchant_id;
          if (response.code == 200) {
            success(response.message);
            self.$bvModal.hide("cp-modal");
            self.resetModal();
          } else {
            error(response.message);
          }
          self.searchMerchantKeys(false);
          self.getAllEcommerceCategories();
        })
        .catch((err) => {
          error(err.response.data.message);
        });
    },
    searchMerchantKeys(is_export){
      var self = this;

      var validate = false;
      if((self.merchant_name).trim() != ''){
        if((self.merchant_name).trim().length < 3){
          validate = true
        }else{
          validate = false
        }
      }else if((self.merchant_id).trim() != ''){
        if((self.merchant_id).trim().length < 3){
          validate = true
        }else{
          validate = false
        }
      }else if((self.category_id).trim() != ''){
        validate = false
      }else if(self.selected_cp){
        validate = false
      }else{
          validate = true
      }

      if(validate){
        error("Please provide Merchant Name (Min 3 chars) OR Merchant ID (Min 3 chars) OR a Category OR a Corporate Parent");
        return false;
      }

      var request = {
        merchant_name: self.merchant_name,
        merchant_id: self.merchant_id,
        category_id: self.category_id,
        cp_id: self.selected_cp ? self.selected_cp.edit : '',
      };
      console.log(request);
      self.loading = true;
      if(is_export){
        request['is_export'] = true;
        api
        .exportSearchMerchantKeys(request)
        .then(function (response) {
          var FileSaver = require("file-saver");
          var blob = new Blob([response], {
            type: "application/xlsx",
          });
          FileSaver.saveAs(
            blob,
            moment().format("MM/DD/YYYY") + "_StoreApiKeys.xlsx"
          );
          self.loading = false;
        })
        .catch(function (error) {
          console.log(error);
          self.loading = false;
        });
      }else{
        api
        .searchMerchantKeys(request)
        .then(function (response) {
          if (response.code == 200) {
            self.allMerchantKeys = response.data;
            self.loading = false;
          } else {
            error(response.message);
            self.loading = false;
          }
        })
        .catch(function (error) {
          error(error);
          self.loading = false;
        });
      }
    },
    reset(){
      var self = this;
      self.merchant_name = "";
      self.merchant_id = "";
      self.category_id = "";
    },
    changeStatus(row){
      // console.log("row", row)
      var self = this;
      self.isStatusLoading = row.id;

      var status = ''
      if(row.status_master == 'Active'){
        status = 'Inactive';
      }else if(row.status_master == 'Inactive'){
        status = 'Active';
      }

      var request = {
        merchant_key_id: row.id,
        status: status
      }
      api
      .merchantKeyStatusChange(request)
      .then(function (response) {
        if (response.code == 200) {
          self.allMerchantKeys.find(allMerchantKey => { if (allMerchantKey.id == row.id) { row.status_master = status } });
          success(response.message);
          self.isStatusLoading = undefined;

        } else {
          error(response.message);
          self.isStatusLoading = undefined;
        }
      })
      .catch(function (error) {
        error(error);
        self.isStatusLoading = undefined;
      });
    }
  },
  mounted() {
    document.title = "CanPay - Merchant Key Master";
  },
};
</script>

