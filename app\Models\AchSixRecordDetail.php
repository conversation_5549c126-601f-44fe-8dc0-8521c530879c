<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class AchSixRecordDetail extends Model
{
    /**
     * __construct
     * This function will insert an alphanumeric 32 character unique id as primary key in the table whenever a data get inserted
     * @param  mixed $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        $this->id = generateUUID();

        parent::__construct($attributes);
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'transaction_id',
        'store_id',
        'merchant_id',
        'transaction_date',
        'transaction_amount',
        'consumer_routing_no',
        'consumer_bank_posting_amount',
        'total_amount',
        'reward_amount_used',
        'record_value',
        'is_voided',
        'sequence_no',
        'batch_id'
    ];

    // Set the data type of the primary key
    protected $keyType = 'string';
    public $timestamps = false;
    public $incrementing = false;
    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
}
