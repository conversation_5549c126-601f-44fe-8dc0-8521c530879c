<?php

namespace App\Imports;

use App\Models\ConsumerMigration;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class UserCardImport implements ToModel, WithHeadingRow, WithBatchInserts, WithChunkReading
{

    /**
     * @param array $row
     * This function actully imports the data as row from Excel Sheet. Here we used the WithHeadingRow to get the Data with Heading. Do Not try to get the rows with index.
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row)
    {
        $email = $row['email1'];
        if (!empty($email)) {
            try {
                // Check if consumer already exists in Database
                $checkConsumerExists = ConsumerMigration::where('email', $email)->first();
                if (!empty($checkConsumerExists)) { // If exists update the card number
                    $action = 'added'; // For logging purpose
                    // Updating the card number of the consumer
                    $user = ConsumerMigration::find($checkConsumerExists->id);
                    if ($user->card_number != '' && $user->card_number != $row['cardnumber1']) {
                        $user->updated = 1;
                        $action = 'updated'; // For logging purpose
                    } else if ($user->card_number != '' && $user->card_number == $row['cardnumber1']) {
                        $action = 'insertion/updation skipped due to duplicate entry'; // For logging purpose
                        insertSkippedDataLog('Consumer', 'email1', $row['email1'], "Consumer card number insertion/updation skipped due to duplicate entry in Consumer Migration Table for User Email : " . $email, json_encode($row), 'Card List By Email');
                    }
                    $user->card_number = $row['cardnumber1'];
                    $user->save();

                    Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer card number " . $action . " in Consumer Migration Table for User Email : " . $email . " with User ID : " . $checkConsumerExists->id);
                } else {
                    Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer card number updatation skipped for non availability of email in Consumer Migration Table for User Email : " . $email);
                    insertSkippedDataLog('Consumer', 'email1', $row['email1'], "Consumer card number updatation skipped for non availability of email in Consumer Migration Table for User Email : " . $email, json_encode($row), 'Card List By Email');
                }
            } catch (\Exception $e) {
                Log::channel('datamigration')->error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured during updation of card number in Consumer Migration Table for User Email : " . $email . ".", [EXCEPTION => $e]);
                insertSkippedDataLog('Consumer', 'email1', $row['email1'], $e, json_encode($row), 'Card List By Email');
            }
        }
    }

    public function batchSize(): int
    {
        return 1000;
    }

    public function chunkSize(): int
    {
        return 5000;
    }
}
