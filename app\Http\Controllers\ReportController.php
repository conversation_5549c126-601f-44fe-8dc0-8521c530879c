<?php

namespace App\Http\Controllers;

use App\Exports\DeliveryFeeTransactionExport;
use App\Exports\DeliverySettlementReportExport;
use App\Exports\FinicityReturnsExport;
use App\Exports\HistoricalSettlementReportExport;
use App\Exports\ManualBankingReturnsExport;
use App\Exports\ManualReviewReturnsExport;
use App\Exports\MerchantPointReportExport;
use App\Exports\MerchantTransactionExport;
use App\Exports\ReturnReportDashboardExport;
use App\Exports\RewardSpinReportExport;
use App\Exports\SettlementReportExport;
use App\Exports\StorewiseMonthlySalesExport;
use App\Models\RegisteredMerchantMaster;
use App\Models\StoreUserMap;
use App\Models\TerminalMaster;
use App\Models\UserRewardUsageHistory;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;

class ReportController extends Controller
{

    /**
     * settlementReport
     * This function is used to get the summary of the transaction of a merchant
     * @param  mixed $request
     * @return void
     */
    public function settlementReport(Request $request)
    {
        // Validating the input requests
        $rule = array(
            'from_date' => VALIDATION_REQUIRED,
            'to_date' => VALIDATION_REQUIRED,
            'store_id' => VALIDATION_REQUIRED,
        );
        $this->__validate($request->all(), $rule);

        $fromDate = $request->get('from_date');
        $singularDebit = $request->get('singular_debit');
        $showOldTransaction = false;
        if ($fromDate <= config('app.v1_transaction_cutoff_date')) {
            $showOldTransaction = true;
        }
        //Get Status Pending and Success
        $pending_status_id = getStatus(PENDING);
        $success_status_id = getStatus(SUCCESS);
        $return_status_id = getStatus(RETURNED);

        $status_str = "'" . $pending_status_id . "','" . $success_status_id . "','" . $return_status_id . "'";

        $terminals = TerminalMaster::join('merchant_stores', 'merchant_stores.id', '=', 'terminal_master.merchant_store_id')
            ->select(DB::raw("GROUP_CONCAT(CONCAT('''', terminal_master.id, '''' )) as terminal_ids"));
        if (!empty($request->get('store_id'))) {
            $terminals = $terminals->whereIn('merchant_stores.id', $request->get('store_id'));
        }
        $terminals = $terminals->first();

        // Fetching Transaction fees
        $transaction_fees = DB::connection(MYSQL_RO)->select("SELECT rmm.* from registered_merchant_master as rmm INNER JOIN merchant_stores as ms On ms.merchant_id = rmm.id WHERE ms.id = ?", [$request->get('store_id')[0]]);
        $retail_vol_fees = $transaction_fees[0]->volume_value;
        $retail_trans_fees = $transaction_fees[0]->per_transaction_value;
        $web_vol_fees = $transaction_fees[0]->volume_value;
        $web_trans_fees = $transaction_fees[0]->per_transaction_value;
        if ($showOldTransaction) {
            $v1_activity_sql = "WITH recursive all_dates(dt) AS (
                SELECT ? dt
                UNION ALL
                SELECT dt + interval 1 day FROM all_dates WHERE dt + interval 1 DAY <= ?)
                SELECT d.dt AS sales_date, IFNULL(SUM(amount+tip_amount),0) AS v1_volume, " . $retail_vol_fees . " AS v1_vol_rate, IFNULL((SUM(amount+tip_amount)/100)*" . $retail_vol_fees . ", 0) AS v1_vol_fee, COUNT(td.id) AS v1_no_of_trans, " . $retail_trans_fees . " AS v1_fee_rate, IFNULL((COUNT(td.id) * " . $retail_trans_fees . "), 0) AS v1_trans_fee, IFNULL(((SUM(amount+tip_amount)/100)*" . $retail_vol_fees . " + COUNT(td.id) * " . $retail_trans_fees . "), 0) AS v1_sub_total_fees,td.is_web
                FROM all_dates AS d
                LEFT JOIN transaction_details AS td   ON td.scheduled_posting_date = d.dt AND td.status_id IN ($status_str) AND td.is_v1 = 1 AND td.transaction_ref_no is null and td.isCanpay = 0 AND td.merchant_id is NULL ";
            if ($terminals->terminal_ids != '') {
                $v1_activity_sql .= "     AND td.terminal_id IN (" . $terminals->terminal_ids . ") ";
            }
            $v1_activity_sql .= "  GROUP BY d.dt DESC WITH ROLLUP";
            $v1_activity = DB::connection(MYSQL_RO)->select($v1_activity_sql, [$request->get('from_date'), $request->get('to_date')]);
            array_unshift($v1_activity, end($v1_activity)); // Prepending the total to the first object of the array
        }

        $v2_retail_sql = "WITH recursive all_dates(dt) AS (
            SELECT ? dt
              UNION ALL
            SELECT dt + interval 1 day FROM all_dates WHERE dt + interval 1 DAY <= ?)";

        if ($terminals->terminal_ids != '') {
            $v2_retail_sql .= ", combined_data AS (
                SELECT dt, td.id AS transaction_id,
                    amount + tip_amount AS volume,
                    0 AS reward_amount,
                    cashback_amount_earned,
                    'transaction' AS source,
                    merchant_fees_waived,
                    merchant_fee_discount_id,
                    merchant_fee_discount_basis_point,
                    SUM(trd.reward_amount) as merchant_reward_amount_used,
                    td.consumer_bank_posting_amount,
                    rmm.volume_value
                FROM all_dates AS d
                LEFT JOIN transaction_details AS td ON td.scheduled_posting_date = d.dt AND td.status_id IN ($status_str) AND td.is_v1 = 0 AND td.transaction_ref_no IS NULL AND td.isCanpay = 0 AND td.terminal_id IN (" . $terminals->terminal_ids . ")
                LEFT JOIN terminal_master tm ON tm.id = td.terminal_id
                LEFT JOIN merchant_stores ms ON tm.merchant_store_id = ms.id
                LEFT JOIN registered_merchant_master rmm ON rmm.id = ms.merchant_id
                LEFT JOIN transaction_reward_details trd ON trd.transaction_id = td.id AND trd.points_type = 'merchant_points'
                WHERE td.id IS NOT NULL
                GROUP BY td.id
                UNION
                SELECT dt, mrh.id AS reward_id,
                    0 AS volume,
                    mrh.reward_amount,
                    0 AS cashback_amount_earned,
                    'reward' AS source,
                    0 AS merchant_fees_waived,
                    NULL AS merchant_fee_discount_id,
                    NULL AS merchant_fee_discount_basis_point,
                    NULL AS merchant_reward_amount_used,
                    NULL AS consumer_bank_posting_amount,
                    NULL AS volume_value
                FROM all_dates AS d
                LEFT JOIN ach_merchant_reward_history AS mrh
                ON mrh.scheduled_posting_date = d.dt
                AND mrh.is_voided = 0
                AND mrh.terminal_id IN (" . $terminals->terminal_ids . ")
            )
            SELECT
                d.dt AS sales_date,
                IFNULL(SUM(volume), 0) AS v2_retail_volume,
                " . $retail_vol_fees . " AS v2_retail_vol_rate,
                IFNULL((SUM(volume) / 100) * " . $retail_vol_fees . ", 0) AS v2_retail_vol_fee,
                COUNT(CASE WHEN source = 'transaction' THEN volume ELSE NULL END) AS v2_retail_no_of_trans,
                " . $retail_trans_fees . " AS v2_retail_fee_rate,
                IFNULL(COUNT(CASE WHEN source = 'transaction' THEN volume ELSE NULL END) * " . $retail_trans_fees . ", 0) AS v2_retail_trans_fee,
                IFNULL(((SUM(volume) / 100) * " . $retail_vol_fees . " + COUNT(CASE WHEN source = 'transaction' THEN volume ELSE NULL END) * " . $retail_trans_fees . "), 0) AS v2_retail_sub_total_fees,
                IFNULL(
                    SUM(
                        CASE
                            WHEN merchant_fee_discount_id IS NOT NULL THEN
                                ROUND(volume * (IFNULL(merchant_fee_discount_basis_point, 0) / 100), 2)
                            ELSE 0
                        END
                    ) +
                    SUM(
                        CASE
                            WHEN COALESCE(merchant_reward_amount_used, 0) > 0 THEN
                                ROUND(COALESCE(merchant_reward_amount_used, 0) * (volume_value - IFNULL(merchant_fee_discount_basis_point, 0)) / 100, 2)
                            ELSE 0
                        END
                    ) +
                    SUM(
                        CASE
                            WHEN consumer_bank_posting_amount = 0 AND COALESCE(merchant_reward_amount_used, 0) > 0 AND volume = COALESCE(merchant_reward_amount_used, 0)
                            THEN $retail_trans_fees ELSE 0
                        END
                    ),
                    0
                ) AS v2_retail_sub_total_fees_waived,
                IFNULL(SUM(reward_amount), 0) AS v2_merchant_reward_volume,
                0 AS is_web,
                IFNULL(SUM(cashback_amount_earned), 0) AS v2_retail_cashback_volume
            FROM all_dates AS d
            LEFT JOIN combined_data AS cd ON cd.dt = d.dt";
        } else {
            $v2_retail_sql .= " SELECT d.dt AS sales_date,
                0 AS v2_retail_volume,
                " . $retail_vol_fees . " AS v2_retail_vol_rate,
                0 AS v2_retail_vol_fee,
                0 AS v2_retail_no_of_trans,
                " . $retail_trans_fees . " AS v2_retail_fee_rate,
                0 AS v2_retail_trans_fee,
                0 AS v2_retail_sub_total_fees,
                0 AS v2_retail_sub_total_fees_waived,
                0 as is_web,
                0 AS v2_merchant_reward_volume,
                0 AS v2_retail_cashback_volume
            FROM all_dates AS d";
        }
        $v2_retail_sql .= " GROUP BY d.dt DESC
            WITH ROLLUP";
        $v2_retail = DB::connection(MYSQL_RO)->select($v2_retail_sql, [$request->get('from_date'), $request->get('to_date')]);
        array_unshift($v2_retail, end($v2_retail)); // Prepending the total to the first object of the array

        $result = [];
        $i = 0;
        foreach ($v2_retail as $value) {
            if ($showOldTransaction) {
                $totalVolume = number_format(($v1_activity[$i]->v1_volume + $v2_retail[$i]->v2_retail_volume), 2);
                $totalFees = ($v1_activity[$i]->v1_sub_total_fees + $v2_retail[$i]->v2_retail_sub_total_fees);
                $activity = new \stdClass();
                $activity->sales_date = $v1_activity[$i]->sales_date == null ? "Totals" : date('m/d/Y', strtotime($v1_activity[$i]->sales_date));
                if ($v2_retail[$i]->v2_retail_no_of_trans > 0 || $v1_activity[$i]->v1_no_of_trans > 0) {
                    $activity->total_volume = $totalVolume > 0 ? "$" . $totalVolume : "Pending";
                } else {
                    $activity->total_volume = "$0.00";
                }

                $activity->total_fees = "$" . number_format(round_up($totalFees, 2), 2);
                $activity->v1_deposit = "$" . number_format($v1_activity[$i]->v1_volume, 2);
                $activity->v1_fees = "$" . number_format(round_up($v1_activity[$i]->v1_sub_total_fees, 2), 2);
                $activity->v2_deposit = "$" . number_format($v2_retail[$i]->v2_retail_volume, 2);
                $activity->v2_fees = "$" . number_format(round_up(($v2_retail[$i]->v2_retail_sub_total_fees), 2), 2);
                $activity->v1_volume = "$" . number_format($v1_activity[$i]->v1_volume, 2);
                $activity->v1_vol_rate = $v1_activity[$i]->sales_date == null ? "" : $v1_activity[$i]->v1_vol_rate . "%";
                $activity->v1_vol_fee = "$" . number_format(round_up($v1_activity[$i]->v1_vol_fee, 2), 2);
                $activity->v1_no_of_trans = $v1_activity[$i]->v1_no_of_trans;
                $activity->v1_fee_rate = $v1_activity[$i]->sales_date == null ? "" : "$" . $v1_activity[$i]->v1_fee_rate;
                $activity->v1_trans_fee = "$" . number_format($v1_activity[$i]->v1_trans_fee, 2);
                $activity->v1_sub_total_fees = "$" . number_format(round_up($v1_activity[$i]->v1_sub_total_fees, 2), 2);
                $activity->v2_retail_volume = "$" . number_format($v2_retail[$i]->v2_retail_volume, 2);
                $activity->v2_retail_vol_rate = $v1_activity[$i]->sales_date == null ? "" : $v2_retail[$i]->v2_retail_vol_rate . "%";
                $activity->v2_retail_vol_fee = "$" . number_format(round_up($v2_retail[$i]->v2_retail_vol_fee, 2), 2);
                $activity->v2_retail_no_of_trans = $v2_retail[$i]->v2_retail_no_of_trans;
                $activity->v2_retail_fee_rate = $v1_activity[$i]->sales_date == null ? "" : "$" . $v2_retail[$i]->v2_retail_fee_rate;
                $activity->v2_retail_trans_fee = "$" . number_format($v2_retail[$i]->v2_retail_trans_fee, 2);
                $activity->v2_retail_sub_total_fees = "$" . number_format(round_up($v2_retail[$i]->v2_retail_sub_total_fees, 2), 2);
            } else {
                $v2_final_fees = ($v2_retail[$i]->v2_retail_sub_total_fees - $v2_retail[$i]->v2_retail_sub_total_fees_waived);
                $activity = new \stdClass();
                $activity->sales_date = $v2_retail[$i]->sales_date == null ? "Totals" : date('m/d/Y', strtotime($v2_retail[$i]->sales_date));

                $activity->v2_retail_volume = "$" . number_format($v2_retail[$i]->v2_retail_volume, 2);
                $activity->v2_retail_vol_rate = $v2_retail[$i]->sales_date == null ? "" : $v2_retail[$i]->v2_retail_vol_rate . "%";
                $activity->v2_retail_vol_fee = "$" . number_format(round_up($v2_retail[$i]->v2_retail_vol_fee, 2), 2);
                $activity->v2_retail_no_of_trans = $v2_retail[$i]->v2_retail_no_of_trans;
                $activity->v2_retail_fee_rate = $v2_retail[$i]->sales_date == null ? "" : "$" . $v2_retail[$i]->v2_retail_fee_rate;
                $activity->v2_retail_trans_fee = "$" . number_format($v2_retail[$i]->v2_retail_trans_fee, 2);
                $activity->v2_retail_sub_total_fees = "$" . number_format(round_up($v2_retail[$i]->v2_retail_sub_total_fees, 2), 2);
                $activity->v2_retail_sub_total_fees_waived = "$" . number_format($v2_retail[$i]->v2_retail_sub_total_fees_waived, 2);
                $activity->v2_final_fees = "$" . number_format(round_up($v2_final_fees, 2), 2);
                $activity->v2_retail_cashback_volume = "$" . number_format($v2_retail[$i]->v2_retail_cashback_volume, 2);
                $activity->v2_merchant_reward_volume = "$" . number_format($v2_retail[$i]->v2_merchant_reward_volume, 2);

                $activity->v2_retail_total_debit = "$" . number_format(round_up($v2_final_fees + $v2_retail[$i]->v2_retail_cashback_volume + $v2_retail[$i]->v2_merchant_reward_volume, 2), 2);
            }
            $i++;
            array_push($result, $activity);
        }

        $message = trans('message.settlement_report_fetch_success');
        return renderResponse(SUCCESS, $message, $result); // Returning response
    }
    /**
     * getSettlementExport
     * This is the export function for Settlement Report
     * @param  mixed $request
     * @return void
     */
    public function getSettlementExport(Request $request)
    {
        // Validating the input requests
        $this->validate($request, [
            'from_date' => VALIDATION_REQUIRED,
            'to_date' => VALIDATION_REQUIRED,
        ]);

        //Get Status Pending and Success
        $pending_status_id = getStatus(PENDING);
        $success_status_id = getStatus(SUCCESS);
        $return_status_id = getStatus(RETURNED);

        $fromDate = $request->get('from_date');
        $singularDebit = $request->get('singular_debit');
        $showOldTransaction = false;
        if ($fromDate <= config('app.v1_transaction_cutoff_date')) {
            $showOldTransaction = true;
        }
        $status_str = "'" . $pending_status_id . "','" . $success_status_id . "','" . $return_status_id . "'";

        //fetch All Stores for the merchant
        $stores = StoreUserMap::join('merchant_stores', 'store_user_map.store_id', '=', 'merchant_stores.id')
            ->join('status_master', 'merchant_stores.status', '=', 'status_master.id')
            ->select('merchant_stores.*');
        if ($request->get('user_id') != '') {
            $stores = $stores->where('store_user_map.user_id', $request->get('user_id'));
        }
        if (!empty($request->get('store_id'))) {
            $stores = $stores->whereIn('merchant_stores.id', $request->get('store_id'));
        }
        $stores = $stores->orderBy('merchant_stores.retailer', 'ASC')->get();

        $result = [];
        $header = [];
        foreach ($stores as $valStores) {
            if ($showOldTransaction) {
                //Add header For Each Stores (Store Name Header)
                $header['sales_date'] = $valStores->retailer;
                $header['location_name'] = '';
                $header['store_id'] = '';
                $header['total_volume'] = '';
                $header['total_fees'] = '';
                $header['v1_deposit'] = '';
                $header['v1_fees'] = '';
                $header['v2_deposit'] = '';
                $header['v2_fees'] = '';
                $header['v1_volume'] = 'Old Canpay Sales Activity';
                $header['v1_vol_rate'] = '';
                $header['v1_vol_fee'] = '';
                $header['v1_no_of_trans'] = '';
                $header['v1_fee_rate'] = '';
                $header['v1_trans_fee'] = '';
                $header['v1_sub_total_fees'] = '';
                $header['v2_retail_volume'] = 'New Canpay Sales Activity';
                $header['v2_retail_vol_rate'] = '';
                $header['v2_retail_vol_fee'] = '';
                $header['v2_retail_no_of_trans'] = '';
                $header['v2_retail_fee_rate'] = '';
                $header['v2_retail_trans_fee'] = '';
                $header['v2_retail_sub_total_fees'] = '';

                array_push($result, $header);

                //Add header For Each Stores (Column Header)
                $header['sales_date'] = 'Sales Date';
                $header['location_name'] = 'Location Name';
                $header['store_id'] = 'Store ID';
                $header['total_volume'] = 'Total volume';
                $header['total_fees'] = 'Total Fees';
                $header['v1_deposit'] = 'Old CanPay Deposit';
                $header['v1_fees'] = 'Fees 1';
                $header['v2_deposit'] = 'New CanPay Deposit';
                $header['v2_fees'] = 'Fees 2';
                $header['v1_volume'] = 'Volume';
                $header['v1_vol_rate'] = 'Rate';
                $header['v1_vol_fee'] = 'Vol Fee';
                $header['v1_no_of_trans'] = '# of Trans';
                $header['v1_fee_rate'] = 'Rate';
                $header['v1_trans_fee'] = 'Trans Fee';
                $header['v1_sub_total_fees'] = 'Sub Total Fees';
                $header['v2_retail_volume'] = 'Volume';
                $header['v2_retail_vol_rate'] = 'Rate';
                $header['v2_retail_vol_fee'] = 'Vol Fee';
                $header['v2_retail_no_of_trans'] = '# of Trans';
                $header['v2_retail_fee_rate'] = 'Rate';
                $header['v2_retail_trans_fee'] = 'Trans Fee';
                $header['v2_retail_sub_total_fees'] = 'Sub Total Fees';

                array_push($result, $header);
            } else {
                $header1 = [
                    'sales_date' => $valStores->retailer,
                    'location_name' => '',
                    'store_id' => '',
                    'v2_retail_volume' => 'Canpay Sales Activity',
                    'v2_retail_vol_rate' => '',
                    'v2_retail_vol_fee' => '',
                    'v2_retail_no_of_trans' => '',
                    'v2_retail_fee_rate' => '',
                    'v2_retail_trans_fee' => '',
                    'v2_retail_sub_total_fees' => '',
                    'v2_retail_sub_total_fees_waived' => '',
                    'v2_final_fees' => '',
                    'v2_retail_cashback_volume' => 'Merchant Rewards',
                    'v2_merchant_reward_volume' => '',
                ];

                $header2 = [
                    'sales_date' => 'Sales Date',
                    'location_name' => 'Location Name',
                    'store_id' => 'Store ID',
                    'v2_retail_volume' => 'Sales Volume',
                    'v2_retail_vol_rate' => 'Rate',
                    'v2_retail_vol_fee' => 'Vol Fee',
                    'v2_retail_no_of_trans' => '# of Trans	Rate',
                    'v2_retail_fee_rate' => 'Rate',
                    'v2_retail_trans_fee' => 'Trans Fee',
                    'v2_retail_sub_total_fees' => 'Sub Total Fees',
                    'v2_retail_sub_total_fees_waived' => 'Reduced Fee Amount',
                    'v2_final_fees' => 'Final Total Fees',
                    'v2_retail_cashback_volume' => 'Points Program Contribution',
                    'v2_merchant_reward_volume' => 'Reward Points Contribution',
                ];
                if ($singularDebit) {
                    $header1['v2_retail_total_debit'] = '';
                    $header2['v2_retail_total_debit'] = 'Total Debit';
                }

                array_push($result, $header1);
                array_push($result, $header2);
            }

            $terminals = TerminalMaster::join('merchant_stores', 'merchant_stores.id', '=', 'terminal_master.merchant_store_id')->select(DB::raw("GROUP_CONCAT(CONCAT('''', terminal_master.id, '''' )) as terminal_ids"))->where('merchant_stores.id', $valStores->id)->first();

            // Fetching Transaction fees
            $transaction_fees = DB::connection(MYSQL_RO)->select("SELECT rmm.* from registered_merchant_master as rmm INNER JOIN merchant_stores as ms On ms.merchant_id = rmm.id WHERE ms.id = ?", [$valStores->id]);
            $retail_vol_fees = $transaction_fees[0]->volume_value;
            $retail_trans_fees = $transaction_fees[0]->per_transaction_value;
            $web_vol_fees = $transaction_fees[0]->volume_value;
            $web_trans_fees = $transaction_fees[0]->per_transaction_value;
            if ($showOldTransaction) {
                $v1_activity_sql = "WITH recursive all_dates(dt) AS (
                    SELECT ? dt
                    UNION ALL
                    SELECT dt + interval 1 day FROM all_dates WHERE dt + interval 1 DAY <= ?)
                    SELECT d.dt AS sales_date, IFNULL(SUM(amount+tip_amount),0) AS v1_volume, " . $retail_vol_fees . " AS v1_vol_rate, IFNULL((SUM(amount+tip_amount)/100)*" . $retail_vol_fees . ", 0) AS v1_vol_fee, COUNT(td.id) AS v1_no_of_trans, " . $retail_trans_fees . " AS v1_fee_rate, IFNULL((COUNT(td.id) * " . $retail_trans_fees . "), 0) AS v1_trans_fee, IFNULL(((SUM(amount+tip_amount)/100)*" . $retail_vol_fees . " + COUNT(td.id) * " . $retail_trans_fees . "), 0) AS v1_sub_total_fees,td.is_web
                    FROM all_dates AS d
                    LEFT JOIN transaction_details AS td  ON td.scheduled_posting_date = d.dt AND td.status_id IN ($status_str) AND td.is_v1 = 1 AND td.transaction_ref_no is null and td.isCanpay = 0 AND td.merchant_id is NULL ";
                if ($terminals->terminal_ids != '') {
                    $v1_activity_sql .= "     AND td.terminal_id IN (" . $terminals->terminal_ids . ") ";
                }
                $v1_activity_sql .= "  GROUP BY d.dt DESC WITH ROLLUP";
                $v1_activity = DB::connection(MYSQL_RO)->select($v1_activity_sql, [$request->get('from_date'), $request->get('to_date')]);
                array_unshift($v1_activity, end($v1_activity)); // Prepending the total to the first object of the array
            }

            $v2_retail_sql = "WITH recursive all_dates(dt) AS (
                SELECT ? dt
                UNION ALL
                SELECT dt + interval 1 day FROM all_dates WHERE dt + interval 1 DAY <= ?)";

            if ($terminals->terminal_ids != '') {
                $v2_retail_sql .= ", combined_data AS (
                    SELECT dt, td.id AS transaction_id,
                        amount + tip_amount AS volume,
                        0 AS reward_amount,
                        cashback_amount_earned,
                        'transaction' AS source,
                        merchant_fees_waived,
                        merchant_fee_discount_id,
                        merchant_fee_discount_basis_point,
                        SUM(trd.reward_amount) as merchant_reward_amount_used,
                        td.consumer_bank_posting_amount,
                        rmm.volume_value
                    FROM all_dates AS d
                    LEFT JOIN transaction_details AS td ON td.scheduled_posting_date = d.dt AND td.status_id IN ($status_str) AND td.is_v1 = 0 AND td.transaction_ref_no IS NULL AND td.isCanpay = 0 AND td.terminal_id IN (" . $terminals->terminal_ids . ")
                    LEFT JOIN terminal_master tm ON tm.id = td.terminal_id
                    LEFT JOIN merchant_stores ms ON tm.merchant_store_id = ms.id
                    LEFT JOIN registered_merchant_master rmm ON rmm.id = ms.merchant_id
                    LEFT JOIN transaction_reward_details trd ON trd.transaction_id = td.id AND trd.points_type = 'merchant_points'
                    WHERE td.id IS NOT NULL
                    GROUP BY td.id
                    UNION
                    SELECT dt, mrh.id AS reward_id,
                        0 AS volume,
                        mrh.reward_amount,
                        0 AS cashback_amount_earned,
                        'reward' AS source,
                        0 AS merchant_fees_waived,
                        NULL AS merchant_fee_discount_id,
                        NULL AS merchant_fee_discount_basis_point,
                        NULL AS merchant_reward_amount_used,
                        NULL AS consumer_bank_posting_amount,
                        NULL AS volume_value
                    FROM all_dates AS d
                    LEFT JOIN ach_merchant_reward_history AS mrh
                    ON mrh.scheduled_posting_date = d.dt
                    AND mrh.is_voided = 0
                    AND mrh.terminal_id IN (" . $terminals->terminal_ids . ")
                )
                SELECT
                    d.dt AS sales_date,
                    IFNULL(SUM(volume), 0) AS v2_retail_volume,
                    " . $retail_vol_fees . " AS v2_retail_vol_rate,
                    IFNULL((SUM(volume) / 100) * " . $retail_vol_fees . ", 0) AS v2_retail_vol_fee,
                    COUNT(CASE WHEN source = 'transaction' THEN volume ELSE NULL END) AS v2_retail_no_of_trans,
                    " . $retail_trans_fees . " AS v2_retail_fee_rate,
                    IFNULL(COUNT(CASE WHEN source = 'transaction' THEN volume ELSE NULL END) * " . $retail_trans_fees . ", 0) AS v2_retail_trans_fee,
                    IFNULL(((SUM(volume) / 100) * " . $retail_vol_fees . " + COUNT(CASE WHEN source = 'transaction' THEN volume ELSE NULL END) * " . $retail_trans_fees . "), 0) AS v2_retail_sub_total_fees,
                    IFNULL(
                        SUM(
                            CASE
                                WHEN merchant_fee_discount_id IS NOT NULL THEN
                                    ROUND(volume * (IFNULL(merchant_fee_discount_basis_point, 0) / 100), 2)
                                ELSE 0
                            END
                        ) +
                        SUM(
                            CASE
                                WHEN COALESCE(merchant_reward_amount_used, 0) > 0 THEN
                                    ROUND(COALESCE(merchant_reward_amount_used, 0) * (volume_value - IFNULL(merchant_fee_discount_basis_point, 0)) / 100, 2)
                                ELSE 0
                            END
                        ) +
                        SUM(
                            CASE
                                WHEN consumer_bank_posting_amount = 0 AND COALESCE(merchant_reward_amount_used, 0) > 0 AND volume = COALESCE(merchant_reward_amount_used, 0)
                                THEN $retail_trans_fees ELSE 0
                            END
                        ),
                        0
                    ) AS v2_retail_sub_total_fees_waived,
                    IFNULL(SUM(reward_amount), 0) AS v2_merchant_reward_volume,
                    0 AS is_web,
                    IFNULL(SUM(cashback_amount_earned), 0) AS v2_retail_cashback_volume
                FROM all_dates AS d
                LEFT JOIN combined_data AS cd ON cd.dt = d.dt";
            } else {
                $v2_retail_sql .= " SELECT d.dt AS sales_date,
                    0 AS v2_retail_volume,
                    " . $retail_vol_fees . " AS v2_retail_vol_rate,
                    0 AS v2_retail_vol_fee,
                    0 AS v2_retail_no_of_trans,
                    " . $retail_trans_fees . " AS v2_retail_fee_rate,
                    0 AS v2_retail_trans_fee,
                    0 AS v2_retail_sub_total_fees,
                    0 AS v2_retail_sub_total_fees_waived,
                    0 as is_web,
                    0 AS v2_merchant_reward_volume,
                    0 AS v2_retail_cashback_volume
                FROM all_dates AS d";
            }

            $v2_retail_sql .= " GROUP BY d.dt DESC
                WITH ROLLUP";
            $v2_retail = DB::connection(MYSQL_RO)->select($v2_retail_sql, [$request->get('from_date'), $request->get('to_date')]);
            array_unshift($v2_retail, end($v2_retail)); // Prepending the total to the first object of the array

            $i = 0;
            foreach ($v2_retail as $value) {
                if ($showOldTransaction) {
                    $totalVolume = number_format(($v1_activity[$i]->v1_volume + $v2_retail[$i]->v2_retail_volume), 2);
                    $totalFees = ($v1_activity[$i]->v1_sub_total_fees + $v2_retail[$i]->v2_retail_sub_total_fees);
                    $activity = [];
                    $activity['sales_date'] = $v1_activity[$i]->sales_date == null ? "Totals" : date('m/d/Y', strtotime($v1_activity[$i]->sales_date));
                    $activity['location_name'] = $valStores->retailer;
                    $activity['store_id'] = $valStores->store_id;
                    if ($v2_retail[$i]->v2_retail_no_of_trans > 0 || $v1_activity[$i]->v1_no_of_trans > 0) {
                        $activity['total_volume'] = $totalVolume > 0 ? "$" . $totalVolume : "Pending";
                    } else {
                        $activity['total_volume'] = "$0.00";
                    }

                    $activity['total_fees'] = "$" . number_format(round_up($totalFees, 2), 2);
                    $activity['v1_deposit'] = "$" . number_format($v1_activity[$i]->v1_volume, 2);
                    $activity['v1_fees'] = "$" . number_format(round_up($v1_activity[$i]->v1_sub_total_fees, 2), 2);
                    $activity['v2_deposit'] = "$" . number_format($v2_retail[$i]->v2_retail_volume, 2);
                    $activity['v2_fees'] = "$" . number_format(round_up(($v2_retail[$i]->v2_retail_sub_total_fees), 2), 2);
                    $activity['v1_volume'] = "$" . number_format($v1_activity[$i]->v1_volume, 2);
                    $activity['v1_vol_rate'] = $v1_activity[$i]->sales_date == null ? "" : $v1_activity[$i]->v1_vol_rate . "%";
                    $activity['v1_vol_fee'] = "$" . number_format(round_up($v1_activity[$i]->v1_vol_fee, 2), 2);
                    $activity['v1_no_of_trans'] = $v1_activity[$i]->v1_no_of_trans;
                    $activity['v1_fee_rate'] = $v1_activity[$i]->sales_date == null ? "" : "$" . $v1_activity[$i]->v1_fee_rate;
                    $activity['v1_trans_fee'] = "$" . number_format($v1_activity[$i]->v1_trans_fee, 2);
                    $activity['v1_sub_total_fees'] = "$" . number_format(round_up($v1_activity[$i]->v1_sub_total_fees, 2), 2);
                    $activity['v2_retail_volume'] = "$" . number_format($v2_retail[$i]->v2_retail_volume, 2);
                    $activity['v2_retail_vol_rate'] = $v1_activity[$i]->sales_date == null ? "" : $v2_retail[$i]->v2_retail_vol_rate . "%";
                    $activity['v2_retail_vol_fee'] = "$" . number_format(round_up($v2_retail[$i]->v2_retail_vol_fee, 2), 2);
                    $activity['v2_retail_no_of_trans'] = $v2_retail[$i]->v2_retail_no_of_trans;
                    $activity['v2_retail_fee_rate'] = $v1_activity[$i]->sales_date == null ? "" : "$" . $v2_retail[$i]->v2_retail_fee_rate;
                    $activity['v2_retail_trans_fee'] = "$" . number_format($v2_retail[$i]->v2_retail_trans_fee, 2);
                    $activity['v2_retail_sub_total_fees'] = "$" . number_format(round_up($v2_retail[$i]->v2_retail_sub_total_fees, 2), 2);
                } else {
                    $v2_final_fees = ($value->v2_retail_sub_total_fees - $value->v2_retail_sub_total_fees_waived);
                    $activity = [];
                    $activity['sales_date'] = $value->sales_date == null ? "Totals" : date('m/d/Y', strtotime($value->sales_date));
                    $activity['location_name'] = $valStores->retailer;
                    $activity['store_id'] = $valStores->store_id;

                    $activity['v2_retail_volume'] = "$" . number_format($value->v2_retail_volume, 2);
                    $activity['v2_retail_vol_rate'] = $value->sales_date == null ? "" : $value->v2_retail_vol_rate . "%";
                    $activity['v2_retail_vol_fee'] = "$" . number_format(round_up($value->v2_retail_vol_fee, 2), 2);
                    $activity['v2_retail_no_of_trans'] = $value->v2_retail_no_of_trans;
                    $activity['v2_retail_fee_rate'] = $value->sales_date == null ? "" : "$" . $value->v2_retail_fee_rate;
                    $activity['v2_retail_trans_fee'] = "$" . number_format($value->v2_retail_trans_fee, 2);
                    $activity['v2_retail_sub_total_fees'] = "$" . number_format(round_up($value->v2_retail_sub_total_fees, 2), 2);
                    $activity['v2_retail_sub_total_fees_waived'] = "$" . number_format($value->v2_retail_sub_total_fees_waived, 2);
                    $activity['v2_final_fees'] = "$" . number_format(round_up($v2_final_fees, 2), 2);
                    $activity['v2_retail_cashback_volume'] = "$" . number_format($value->v2_retail_cashback_volume, 2);
                    $activity['v2_merchant_reward_volume'] = "$" . number_format($value->v2_merchant_reward_volume, 2);
                    if ($singularDebit) {
                        $activity['v2_retail_total_debit'] = "$" . number_format(round_up($v2_final_fees + $value->v2_retail_cashback_volume + $value->v2_merchant_reward_volume, 2), 2);
                    }
                }
                $i++;
                array_push($result, $activity);
            }
        }

        $returnResponse = array(
            'from_date' => $request->get('from_date'),
            'to_date' => $request->get('to_date'),
            'singular_debit' => $request->get('singular_debit'),
            'date_diff' => (strtotime($request->get('to_date')) - strtotime($request->get('from_date'))) / 60 / 60 / 24,
            'stores' => count($stores),
            'report' => $result,
        );
        if ($showOldTransaction) {
            return Excel::download(new HistoricalSettlementReportExport($returnResponse), 'settlement_report_all_stores' . date('m-d-Y H:i:s') . '.xlsx');
        } else {
            return Excel::download(new SettlementReportExport($returnResponse), 'settlement_report_all_stores' . date('m-d-Y H:i:s') . '.xlsx');
        }
    }

    /**
     * getMerchantTransactionReport
     * This function is used to get the transactions of the Terminals of the store under a merchant
     * @param  mixed $request
     * @return void
     */
    public function getMerchantTransactionReport(Request $request)
    {
        // Validating the input requests
        $this->validate($request, [
            'from_date' => VALIDATION_REQUIRED,
            'to_date' => VALIDATION_REQUIRED,
            // 'store_id' => VALIDATION_REQUIRED,
        ]);

        $from_date = $request->get('from_date');
        $to_date = $request->get('to_date');

        //Creating Store String
        $strSep = '';
        $stores = '';
        if ($request->get('store_id')) { // Checking if report is needed for specific terminals
            foreach ($request->get('store_id') as $store) { // Creating the string for in clause
                $stores = $stores . $strSep . "'" . $store . "'";
                $strSep = ',';
            }
        }

        $status_str = SUCCESS . "," . VOIDED . "," . PENDING . "," . RETURNED;

        // Fetching total amount of sale from the store
        $store_wise_total_sql = "SELECT ms.retailer as store_name, ms.id, CONCAT('$', FORMAT(SUM(if(sm.code=510,0,td.amount + td.tip_amount)), 2)) AS total_payment, CONCAT('$', FORMAT(SUM(if(sm.code=510,0,td.amount)), 2)) AS base_amount, CONCAT('$', FORMAT(SUM(if(sm.code=510,0,td.tip_amount)), 2)) AS tip, COUNT(*) AS trans_count, td.transaction_number as transaction_no
        FROM transaction_details AS td  INNER JOIN status_master AS sm ON td.status_id = sm.id
        INNER JOIN terminal_master AS tm ON td.terminal_id = tm.id
        INNER JOIN merchant_stores AS ms ON ms.id = tm.merchant_store_id
        WHERE ms.id IN (" . $stores . ") AND td.scheduled_posting_date >= ? AND td.scheduled_posting_date <= ?  AND td.isCanpay = 0  AND td.transaction_ref_no IS NULL AND sm.code IN (" . $status_str . ") AND IF(td.is_ecommerce = 1,td.modification_latest_row = 1,td.modification_latest_row = 0) ";

        if ($request->get('show_void') == 0) {
            $store_wise_total_sql .= " AND sm.code != " . VOIDED;
        }

        $store_wise_total_sql .= " GROUP BY ms.id";

        $searchArray = [$from_date, $to_date];
        $store_wise_total = DB::connection(MYSQL_RO)->select($store_wise_total_sql, $searchArray);

        $final_result = [];
        foreach ($store_wise_total as $valStores) {
            // Fetching the transactions with details
            $sql = "SELECT ms.retailer as store_name,if(td.is_web=0, 'Retail','Web') AS store_type, ttm.`type` AS store_category,";
            $sql .= " tm.terminal_name AS terminalID, ";

            $sql .= " IF(td.is_ecommerce = 0, td.local_transaction_time, td.scheduled_posting_date) as transaction_time, DATE_ADD(td.local_transaction_date, INTERVAL 1 DAY) AS posting_date, CONCAT('$', FORMAT((td.amount + td.tip_amount), 2)) AS total_payment, CONCAT('$', FORMAT(td.amount, 2)) AS base_amount, CONCAT('$', FORMAT(td.tip_amount, 2)) AS tip, '1' AS trans_count, CASE WHEN td.is_v1 = 1 THEN 'Settled' WHEN sm.code = " . VOIDED . " THEN sm.status WHEN td.local_transaction_date < CURDATE() THEN 'Settled' ELSE 'Approved' END AS transaction_status, u.user_identifier, c.user_identifier AS consumer_identifier, td.pos_web_identifier, CASE WHEN td1.id IS NOT NULL THEN td1.transaction_number ELSE td.transaction_number END as transaction_no
            FROM transaction_details AS td FORCE INDEX(idx_scheduled_posting_date) INNER JOIN terminal_master AS tm ON td.terminal_id = tm.id
            LEFT JOIN transaction_details td1 ON td.id = td1.change_request_transaction_ref_no
            INNER JOIN merchant_stores AS ms ON ms.id = tm.merchant_store_id
            LEFT JOIN transaction_type_master AS ttm ON td.transaction_type_id = ttm.id
            INNER JOIN status_master AS sm ON td.status_id = sm.id
            LEFT JOIN users AS u ON IF(td1.id IS NOT NULL, td1.user_id = u.user_id, td.user_id = u.user_id)
            LEFT JOIN users AS c ON IF(td1.id IS NOT NULL, td1.consumer_id = c.user_id, td.consumer_id = c.user_id)
            WHERE ms.id = ? AND td.scheduled_posting_date >= ? AND td.scheduled_posting_date <= ?  AND td.isCanpay = 0  AND td.transaction_ref_no IS NULL AND sm.code IN (" . $status_str . ") AND IF(td.is_ecommerce = 1,td.modification_latest_row = 1,td.modification_latest_row = 0) ";

            if ($request->get('show_void') == 0) {
                $sql .= " AND sm.code != " . VOIDED;
            }

            $sql .= " ORDER BY ttm.type, terminalID, td.transaction_time";
            $searchArray = [$valStores->id, $from_date, $to_date];
            $result = DB::connection(MYSQL_RO)->select($sql, $searchArray);

            // Fetching total amount of sale from the store by the type of the store
            $store_type_wise_total_sql = "SELECT if(td.is_web=0,'Retail','Web') AS store_type, CONCAT('$', FORMAT(SUM(if(sm.code=510,0,td.amount + td.tip_amount)), 2)) AS total_payment, CONCAT('$', FORMAT(SUM(if(sm.code=510,0,td.amount)), 2)) AS base_amount, CONCAT('$', FORMAT(SUM(if(sm.code=510,0,td.tip_amount)), 2)) AS tip, COUNT(*) AS trans_count, td.transaction_number as transaction_no, ms.is_ecommerce as ecommerce_store
            FROM transaction_details AS td  INNER JOIN status_master AS sm ON td.status_id = sm.id
            INNER JOIN terminal_master AS tm ON td.terminal_id = tm.id
            INNER JOIN merchant_stores AS ms ON ms.id = tm.merchant_store_id
            WHERE ms.id = ? AND td.scheduled_posting_date >= ? AND td.scheduled_posting_date <= ?  AND td.isCanpay = 0  AND td.transaction_ref_no IS NULL AND sm.code IN (" . $status_str . ") AND IF(td.is_ecommerce = 1,td.modification_latest_row = 1,td.modification_latest_row = 0) ";

            if ($request->get('show_void') == 0) {
                $store_type_wise_total_sql .= " AND sm.code != " . VOIDED;
            }

            $store_type_wise_total_sql .= "  GROUP BY store_type";
            $searchArray = [$valStores->id, $from_date, $to_date];
            $store_type_wise_total = DB::connection(MYSQL_RO)->select($store_type_wise_total_sql, $searchArray);

            // Fetching total amount of sale from the store by its category
            $store_category_wise_total_sql = "SELECT if(td.is_web=0,'Retail','Web') AS store_type, ttm.type AS store_category, CONCAT('$', FORMAT(SUM(if(sm.code=510,0,td.amount + td.tip_amount)), 2)) AS total_payment, CONCAT('$', FORMAT(SUM(if(sm.code=510,0,td.amount)), 2)) AS base_amount, CONCAT('$', FORMAT(SUM(if(sm.code=510,0,td.tip_amount)), 2)) AS tip, COUNT(*) AS trans_count, td.transaction_number as transaction_no
            FROM transaction_details AS td  INNER JOIN status_master AS sm ON td.status_id = sm.id
            INNER JOIN terminal_master AS tm ON td.terminal_id = tm.id
            INNER JOIN merchant_stores AS ms ON ms.id = tm.merchant_store_id
            LEFT JOIN transaction_type_master AS ttm ON td.transaction_type_id = ttm.id
            WHERE ms.id = ? AND td.scheduled_posting_date >= ? AND td.scheduled_posting_date <= ?  AND td.isCanpay = 0  AND td.transaction_ref_no IS NULL AND sm.code IN (" . $status_str . ") AND IF(td.is_ecommerce = 1,td.modification_latest_row = 1,td.modification_latest_row = 0) ";

            if ($request->get('show_void') == 0) {
                $store_category_wise_total_sql .= " AND sm.code != " . VOIDED;
            }

            $store_category_wise_total_sql .= " GROUP BY store_type, store_category";
            $searchArray = [$valStores->id, $from_date, $to_date];
            $store_category_wise_total = DB::connection(MYSQL_RO)->select($store_category_wise_total_sql, $searchArray);

            // Fetching total amount of sale from the store in respect of its terminal
            $terminal_wise_total_sql = "SELECT if(td.is_web=0,'Retail','Web') AS store_type, ttm.type AS store_category,";
            $terminal_wise_total_sql .= " tm.terminal_name AS terminalID, ";

            $terminal_wise_total_sql .= " CONCAT('$', FORMAT(SUM(if(sm.code=510,0,td.amount + td.tip_amount)), 2)) AS total_payment, CONCAT('$', FORMAT(SUM(if(sm.code=510,0,td.amount)), 2)) AS base_amount, CONCAT('$', FORMAT(SUM(if(sm.code=510,0,td.tip_amount)), 2)) AS tip, COUNT(*) AS trans_count, td.pos_web_identifier,td.transaction_number as transaction_no
            FROM transaction_details AS td  INNER JOIN status_master AS sm ON td.status_id = sm.id
            INNER JOIN terminal_master AS tm ON td.terminal_id = tm.id
            INNER JOIN merchant_stores AS ms ON ms.id = tm.merchant_store_id
            LEFT JOIN transaction_type_master AS ttm ON td.transaction_type_id = ttm.id
            WHERE ms.id = ? AND td.scheduled_posting_date >= ? AND td.scheduled_posting_date <= ?  AND td.isCanpay = 0  AND td.transaction_ref_no IS NULL AND sm.code IN (" . $status_str . ") AND IF(td.is_ecommerce = 1,td.modification_latest_row = 1,td.modification_latest_row = 0) ";

            if ($request->get('show_void') == 0) {
                $terminal_wise_total_sql .= " AND sm.code != " . VOIDED;
            }

            $terminal_wise_total_sql .= " GROUP BY store_type, store_category, terminalID";
            $searchArray = [$valStores->id, $from_date, $to_date];
            $terminal_wise_total = DB::connection(MYSQL_RO)->select($terminal_wise_total_sql, $searchArray);

            array_push($final_result, $valStores);
            array_push($final_result, $store_type_wise_total);
            array_push($final_result, $store_category_wise_total);
            array_push($final_result, $terminal_wise_total);
            array_push($final_result, $result);
        }

        $message = trans('message.merchant_location_transaction_report_fetch_success');
        return renderResponse(SUCCESS, $message, $final_result); // Returning response
    }

    /**
     * getMerchantTransactionExport
     * This is the export function for Merchant Transaction Report
     * @param  mixed $request
     * @return void
     */
    public function getMerchantTransactionExport(Request $request)
    {
        return Excel::download(new MerchantTransactionExport($request), 'merchant_transaction_report' . date('m-d-Y H:i:s') . '.xlsx');
    }

    /**
     * getFinicityReturns
     * This function is used to get Finicity R01 Returns
     * @param  mixed $request
     * @return void
     */
    public function getFinicityReturns(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Finicity R01 Returns Report Fetch Started");
        // Validating input request
        $this->validate($request, [
            'from_date' => VALIDATION_REQUIRED,
            'to_date' => VALIDATION_REQUIRED,
        ]);

        $from_date = $request->get('from_date');
        $to_date = $request->get('to_date');

        //Fetch Finicity R01 Returns for a particular date range
        $finicityReturns = $this->_getFinicityReturnsData($request);

        $message = trans('message.fincity_returns_fetch_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Finicity R01 Returns Report Fetched Successfully for the Period : " . $from_date . " AND " . $to_date);
        return renderResponse(SUCCESS, $message, $finicityReturns); // Returning response
    }

    private function _getFinicityReturnsData($request)
    {
        $from_date = $request['from_date'];
        $to_date = $request['to_date'];

        $sql = "SELECT concat_ws(' ', u.first_name, u.middle_name, u.last_name) as consumer_name, u.phone, u.email, IF(u.existing_user = 1, 'V1', 'V2') as user_type, (td.amount + td.tip_amount) AS amount, COALESCE(td.bank_balance_at_transation_time, 0) AS balance, DATE_FORMAT(td.local_transaction_time, '%m-%d-%Y %H:%i:%S') as local_transaction_time, tz.timezone_name, ms.retailer, td.pp_value as purchase_power
        FROM transaction_details td FORCE INDEX(date_key)
        INNER JOIN users u ON td.consumer_id = u.user_id
        INNER JOIN return_reason_masters rrm ON td.return_reason = rrm.id
        INNER JOIN terminal_master tm ON td.terminal_id = tm.id
        INNER JOIN merchant_stores ms ON tm.merchant_store_id = ms.id
        INNER JOIN timezone_masters tz ON td.timezone_id = tz.id
        WHERE td.is_v1 = 0 AND td.transaction_ref_no IS NULL AND td.isCanpay = 0 AND td.return_reason IS NOT NULL AND u.bank_link_type = 1 AND rrm.reason_code = 'R01' AND (td.pp_type IS NULL OR td.pp_type = '" . PURCHASE_POWER . "') ";

        $searchArray = [];
        if ($from_date && $to_date) {
            $sql .= " AND td.local_transaction_date BETWEEN ? AND ? ";
            array_push($searchArray, $from_date, $to_date);
        }

        $sql .= " ORDER BY td.local_transaction_time ASC";
        $finicityReturns = DB::connection(MYSQL_RO)->select($sql, $searchArray);

        return $finicityReturns;
    }

    /**
     * getFinicityReturnsExport
     * This is the export function for Finicity R01 Returns
     * @param  mixed $request
     * @return void
     */
    public function getFinicityReturnsExport(Request $request)
    {
        //Fetch Finicity R01 Returns for a particular date range
        $finicityReturns = $this->_getFinicityReturnsData($request);

        $returnResponse = [
            'finicityReturns' => $finicityReturns,
        ];

        return Excel::download(new FinicityReturnsExport($returnResponse), 'finicity_returns_' . date('m-d-Y H:i:s') . '.xlsx');
    }

    /**
     * getManualBankingReturns
     * This function is used to get Manual Linked banking Returns
     * @param  mixed $request
     * @return void
     */
    public function getManualBankingReturns(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Manual Linked Banking Returns Report Fetch Started");
        // Validating input request
        $this->validate($request, [
            'from_date' => VALIDATION_REQUIRED,
            'to_date' => VALIDATION_REQUIRED,
        ]);

        $from_date = $request->get('from_date');
        $to_date = $request->get('to_date');

        //Fetch Manual Linked Banking Returns for a particular date range
        $manualBankingReturns = $this->_getManualBankingReturnsData($request);

        $message = trans('message.manual_banking_returns_fetch_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Manual Linked Banking Returns Report Fetched Successfully for the Period : " . $from_date . " AND " . $to_date);
        return renderResponse(SUCCESS, $message, $manualBankingReturns); // Returning response
    }

    private function _getManualBankingReturnsData($request)
    {
        $from_date = $request['from_date'];
        $to_date = $request['to_date'];

        $sql = "SELECT concat_ws(' ', u.first_name, u.middle_name, u.last_name) as consumer_name, u.phone, u.email, IF(u.existing_user = 1, 'V1', 'V2') as user_type, (td.amount + td.tip_amount) AS amount, td.local_transaction_time, tz.timezone_name, ms.retailer, vlt.response AS microbilt_response, vlt.created_at, IF(urr.return_code IS NOT NULL, urr.return_code, rrm.reason_code) reason_code, rrm.description,vlt.response_code,td.pp_value as spending_limit
        FROM transaction_details td FORCE INDEX(date_key)
        INNER JOIN users u ON td.consumer_id = u.user_id
        left JOIN validation_log_table vlt FORCE INDEX(idx_phone_type) ON u.phone = vlt.phone AND td.account_id = vlt.account_id AND vlt.`type` = 'microbilt'
        INNER JOIN return_reason_masters rrm ON td.return_reason = rrm.id
        LEFT JOIN unknown_return_reasons urr ON urr.transaction_id = td.id
        INNER JOIN terminal_master tm ON td.terminal_id = tm.id
        INNER JOIN merchant_stores ms ON tm.merchant_store_id = ms.id
        INNER JOIN timezone_masters tz ON td.timezone_id = tz.id
        WHERE td.is_v1 = 0 AND td.transaction_ref_no IS NULL AND td.isCanpay = 0 AND td.return_reason IS NOT NULL AND u.bank_link_type = 0 ";

        $searchArray = [];
        if ($from_date && $to_date) {
            $sql .= " AND td.local_transaction_date BETWEEN ? AND ? ";
            array_push($searchArray, $from_date, $to_date);
        }

        $sql .= " AND (td.pp_type IS NULL OR td.pp_type = '" . SPENDING_LIMIT . "') ORDER BY td.local_transaction_time ASC";
        $manualBankingReturns = DB::connection(MYSQL_RO)->select($sql, $searchArray);

        return $manualBankingReturns;
    }

    /**
     * getManualBankingReturnsExport
     * This is the export function for Manual Linked Banking Returns
     * @param  mixed $request
     * @return void
     */
    public function getManualBankingReturnsExport(Request $request)
    {
        //Fetch Manual Linked Banking Returns for a particular date range
        $manualBankingReturns = $this->_getManualBankingReturnsData($request);

        $returnResponse = [
            'manualBankingReturns' => $manualBankingReturns,
        ];

        return Excel::download(new ManualBankingReturnsExport($returnResponse), 'manual_banking_returns_' . date('m-d-Y H:i:s') . '.xlsx');
    }

    /**
     * getManualReviewReturns
     * This function is used to get Manual Review Returns
     * @param  mixed $request
     * @return void
     */
    public function getManualReviewReturns(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Manual Review Returns Report Fetch Started");
        // Validating input request
        $this->validate($request, [
            'from_date' => VALIDATION_REQUIRED,
            'to_date' => VALIDATION_REQUIRED,
        ]);

        $from_date = $request->get('from_date');
        $to_date = $request->get('to_date');
        $manual_review = $request->get('manual_review');

        //Fetch Manual Review Returns for a particular date range
        $manualReviewReturns = $this->_getManualReviewReturnsData($request);
        $totalReturns = $this->_getTotalReturns($request);
        $reviewReturns = $this->_getReviewReturns($request);
        $percentage = 0;
        if ($reviewReturns != 0 && $totalReturns != 0) {
            $percentage = number_format(($reviewReturns / $totalReturns) * 100, 2);
        }

        $returnResponse = [
            'report' => $manualReviewReturns,
            'percentage' => $percentage,
        ];

        $message = trans('message.manual_review_returns_fetch_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Manual Review Returns Report Fetched Successfully for the Period : " . $from_date . " AND " . $to_date . " AND Review Type : " . $manual_review);
        return renderResponse(SUCCESS, $message, $returnResponse); // Returning response
    }

    private function _getManualReviewReturnsData($request)
    {
        $from_date = $request['from_date'];
        $to_date = $request['to_date'];
        $manual_review = $request['manual_review'];

        $sql = "SELECT td.id,concat_ws(' ', u.first_name, u.middle_name, u.last_name) as consumer_name, u.phone, u.email, (td.amount + td.tip_amount) AS amount, td.local_transaction_time, tz.timezone_name, ms.retailer,IF(urr.return_code IS NOT NULL, urr.return_code, rrm.reason_code) reason_code
        FROM transaction_details td FORCE INDEX(date_key)
        INNER JOIN users u ON td.consumer_id = u.user_id
        INNER JOIN return_reason_masters rrm ON td.return_reason = rrm.id
        LEFT JOIN unknown_return_reasons urr ON urr.transaction_id = td.id
        INNER JOIN terminal_master tm ON td.terminal_id = tm.id
        INNER JOIN merchant_stores ms ON tm.merchant_store_id = ms.id
        INNER JOIN timezone_masters tz ON td.timezone_id = tz.id ";

        if ($manual_review == 'V1 Manual Review') {
            $sql .= " INNER JOIN v1_onboarding_manual_reviews mr on mr.user_id = u.user_id ";
        } else {
            $sql .= " INNER JOIN manual_review_details mr on mr.user_id = u.user_id ";
        }

        $sql .= " WHERE td.is_v1 = 0 AND td.transaction_ref_no IS NULL AND td.isCanpay = 0 AND td.return_reason IS NOT NULL ";

        $searchArray = [];
        if ($from_date && $to_date) {
            $sql .= " AND td.local_transaction_date BETWEEN ? AND ? ";
            array_push($searchArray, $from_date, $to_date);
        }

        $sql .= " ORDER BY td.local_transaction_time ASC";
        $manualReviewReturns = DB::connection(MYSQL_RO)->select($sql, $searchArray);

        return $manualReviewReturns;
    }

    private function _getTotalReturns($request)
    {
        $from_date = $request['from_date'];
        $to_date = $request['to_date'];
        $manual_review = $request['manual_review'];

        $sql = "SELECT COALESCE(SUM((td.amount + td.tip_amount)),0) AS amount
        FROM transaction_details td FORCE INDEX(date_key)
        INNER JOIN users u ON td.consumer_id = u.user_id
        INNER JOIN return_reason_masters rrm ON td.return_reason = rrm.id
        INNER JOIN terminal_master tm ON td.terminal_id = tm.id
        INNER JOIN merchant_stores ms ON tm.merchant_store_id = ms.id
        INNER JOIN timezone_masters tz ON td.timezone_id = tz.id WHERE td.is_v1 = 0 AND td.transaction_ref_no IS NULL AND td.isCanpay = 0 AND td.return_reason IS NOT NULL ";

        $searchArray = [];
        if ($from_date && $to_date) {
            $sql .= " AND td.local_transaction_date BETWEEN ? AND ? ";
            array_push($searchArray, $from_date, $to_date);
        }

        $totalReturns = DB::connection(MYSQL_RO)->select($sql, $searchArray);
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Total returns: ", (array) $totalReturns[0]->amount);
        return $totalReturns[0]->amount;
    }

    private function _getReviewReturns($request)
    {
        $from_date = $request['from_date'];
        $to_date = $request['to_date'];
        $manual_review = $request['manual_review'];

        $sql = "SELECT COALESCE(SUM((td.amount + td.tip_amount)),0) AS amount
        FROM transaction_details td FORCE INDEX(date_key)
        INNER JOIN users u ON td.consumer_id = u.user_id
        INNER JOIN return_reason_masters rrm ON td.return_reason = rrm.id
        INNER JOIN terminal_master tm ON td.terminal_id = tm.id
        INNER JOIN merchant_stores ms ON tm.merchant_store_id = ms.id
        INNER JOIN timezone_masters tz ON td.timezone_id = tz.id";

        if ($manual_review == 'V1 Manual Review') {
            $sql .= " INNER JOIN v1_onboarding_manual_reviews mr on mr.user_id = u.user_id ";
        } else {
            $sql .= " INNER JOIN manual_review_details mr on mr.user_id = u.user_id ";
        }

        $sql .= "  WHERE td.is_v1 = 0 AND td.transaction_ref_no IS NULL AND td.isCanpay = 0 AND td.return_reason IS NOT NULL ";
        $searchArray = [];
        if ($from_date && $to_date) {
            $sql .= " AND td.local_transaction_date BETWEEN ? AND ? ";
            array_push($searchArray, $from_date, $to_date);
        }

        $totalReviewReturns = DB::connection(MYSQL_RO)->select($sql, $searchArray);
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Total returns: ", (array) $totalReviewReturns[0]->amount);
        return $totalReviewReturns[0]->amount;
    }

    /**
     * getManualReviewReturnsExport
     * This is the export function for Manual Review Returns
     * @param  mixed $request
     * @return void
     */
    public function getManualReviewReturnsExport(Request $request)
    {
        //Fetch Manual Review Returns for a particular date range
        $manualReviewReturns = $this->_getManualReviewReturnsData($request);
        $totalReturns = $this->_getTotalReturns($request);
        $reviewReturns = $this->_getReviewReturns($request);
        $percentage = number_format(($reviewReturns / $totalReturns) * 100, 2);

        $returnResponse = [
            'manualReviewReturns' => $manualReviewReturns,
            'percentage' => $percentage,
        ];

        return Excel::download(new ManualReviewReturnsExport($returnResponse), 'manual_review_returns_' . date('m-d-Y H:i:s') . '.xlsx');
    }

    /* generateReturnReportDashboard
     * This function is used to generate a return dashboard report based on a sales date or a range of sales dates
     * @param  mixed $request
     * @return void
     */
    public function generateReturnReportDashboard(Request $request)
    {
        // Validating the input requests
        $this->validate($request, [
            'from_date' => VALIDATION_REQUIRED,
            'to_date' => VALIDATION_REQUIRED,
        ]);

        $from_date = $request->get('from_date');
        $to_date = $request->get('to_date');
        $searchArray = [$from_date, $to_date]; // Used for temp table data insertion

        $result = $this->_getreturndataForDashboard($searchArray);

        // Get the last element of the result array for footer portion
        $last_element_of_array = end($result);
        $footer_object = new \stdClass(); // Declaring footer object
        if ($last_element_of_array->transaction_count == 0) {
            $footer_object->avg_return_value = 0;
        } else {
            $footer_object->avg_return_value = round($last_element_of_array->total_transaction / $last_element_of_array->transaction_count, 2);
        }
        $footer_object->unpaid_transaction_amount = round($last_element_of_array->total_transaction - $last_element_of_array->paid_amount - $last_element_of_array->pending_amount, 2);
        if ($result[0]->total_transaction == 0) {
            $footer_object->unpaid_transaction_percent = 0;
        } else {
            $footer_object->unpaid_transaction_percent = round(($footer_object->unpaid_transaction_amount / $result[0]->total_transaction) * 100, 2);
        }
        $footer_object->pending_transaction_amount = round($last_element_of_array->pending_amount, 2);
        if ($result[0]->total_transaction == 0) {
            $footer_object->pending_transaction_percent = 0;
        } else {
            $footer_object->pending_transaction_percent = round(($footer_object->pending_transaction_amount / $result[0]->total_transaction) * 100, 2);
        }
        $footer_object->paid_transaction_amount = round($last_element_of_array->paid_amount, 2);
        if ($result[0]->total_transaction == 0) {
            $footer_object->paid_transaction_percent = 0;
        } else {
            $footer_object->paid_transaction_percent = round(($footer_object->paid_transaction_amount / $result[0]->total_transaction) * 100, 2);
        }

        $final_result['report_body'] = $result;
        $final_result['report_footer'] = $footer_object;

        $message = trans('message.return_report_dashboard_fetch_success');
        return renderResponse(SUCCESS, $message, $final_result); // Returning response
    }

    private function _getreturndataForDashboard($searchArray)
    {
        // Create a temporary table
        DB::statement("CREATE TEMPORARY TABLE temp_transaction_data(id INT, total_transaction DECIMAL(10,2), transaction_percent DECIMAL(10,2), transaction_count INT, avg_transaction_value DECIMAL(10,2))");

        // Insert data in the temporary table
        $temp_table_data_insert_sql = "INSERT INTO temp_transaction_data
        SELECT 1, SUM(td.amount + td.tip_amount) AS total_transaction, '100' AS transaction_percent, COUNT(*) AS transaction_count, ROUND((SUM(td.amount + td.tip_amount)/COUNT(*)),2) AS avg_transaction_value
        FROM transaction_details td FORCE INDEX(date_key)
        INNER JOIN status_master sm ON td.status_id = sm.id
        WHERE td.is_v1 = 0 AND td.transaction_ref_no IS NULL AND sm.code IN ('200','202','511') AND td.isCanpay = 0 AND td.local_transaction_date BETWEEN ? AND ?";
        DB::statement($temp_table_data_insert_sql, $searchArray);

        // Main select query for the report
        $sql = "SELECT 'Total Transactions' AS heading, total_transaction, transaction_percent, transaction_count, avg_transaction_value, NULL as paid_amount, NULL as paid_percent, NULL AS paid_count, NULL AS paid_count_percent, NULL as pending_amount, NULL as pending_percent, NULL AS pending_count, NULL AS pending_count_percent, NULL as represented_amount, NULL as represented_percent, NULL AS represented_count, NULL AS represented_count_percent, NULL as not_represented_amount, NULL as not_represented_percent, NULL AS not_represented_count, NULL AS not_represented_count_percent
        FROM temp_transaction_data
        WHERE id = 1

        UNION

        SELECT 'Returns' AS heading, NULL as total_transaction, NULL as transaction_percent, NULL as transaction_count, NULL as avg_transaction_value, NULL as paid_amount, NULL as paid_percent, NULL AS paid_count, NULL AS paid_count_percent, NULL as pending_amount, NULL as pending_percent, NULL AS pending_count, NULL AS pending_count_percent, NULL as represented_amount, NULL as represented_percent, NULL AS represented_count, NULL AS represented_count_percent, NULL as not_represented_amount, NULL as not_represented_percent, NULL AS not_represented_count, NULL AS not_represented_count_percent

        UNION

        SELECT rrm.reason_code AS heading, ROUND(SUM(td.amount + td.tip_amount),2) AS returned_amount, ROUND(((SUM(td.amount + td.tip_amount)/ttd.total_transaction)*100),2) AS percent, COUNT(*) AS return_count, ROUND((SUM(td.amount + td.tip_amount)/COUNT(*)),2) AS avg_transaction_value,
        ROUND(SUM(IF(sm.code = '200', (td.amount + td.tip_amount), 0)),2) AS paid_amount, ROUND(((SUM(IF(sm.code = '200', (td.amount + td.tip_amount), 0))/SUM(td.amount + td.tip_amount))*100),2) AS paid_percent, SUM(IF(sm.code = '200', 1, 0)) AS paid_count, ROUND(((SUM(IF(sm.code = '200', 1, 0))/ttd.transaction_count)*100),2) AS paid_count_percent,
        ROUND(SUM(IF(sm.code = '202', (td.amount + td.tip_amount), 0)),2) AS pending_amount, ROUND(((SUM(IF(sm.code = '202', (td.amount + td.tip_amount), 0))/SUM(td.amount + td.tip_amount))*100),2) AS pending_percent, SUM(IF(sm.code = '202', 1, 0)) AS pending_count, ROUND(((SUM(IF(sm.code = '202', 1, 0))/ttd.transaction_count)*100),2) AS pending_count_percent,
        ROUND(SUM(IF(sm.code = '511' && td.represent_count > 0, (td.amount + td.tip_amount), 0)),2) AS represented_amount, ROUND(((SUM(IF(sm.code = '511' && td.represent_count > 0, (td.amount + td.tip_amount), 0))/SUM(td.amount + td.tip_amount))*100),2) AS represented_percent, SUM(IF(sm.code = '511' && td.represent_count > 0, 1, 0)) AS represented_count, ROUND(((SUM(IF(sm.code = '511' && td.represent_count > 0, 1, 0))/ttd.transaction_count)*100),2) AS represented_count_percent,
        ROUND(SUM(IF(sm.code = '511' && td.represent_count = 0, (td.amount + td.tip_amount), 0)),2) AS not_represented_amount, ROUND(((SUM(IF(sm.code = '511' && td.represent_count = 0, (td.amount + td.tip_amount), 0))/SUM(td.amount + td.tip_amount))*100),2) AS not_represented_percent, SUM(IF(sm.code = '511' && td.represent_count = 0, 1, 0)) AS not_represented_count, ROUND(((SUM(IF(sm.code = '511' && td.represent_count = 0, 1, 0))/ttd.transaction_count)*100),2) AS not_represented_count_percent
        FROM transaction_details td FORCE INDEX(date_key)
        INNER JOIN return_reason_masters rrm ON td.return_reason = rrm.id
        INNER JOIN status_master sm ON td.status_id = sm.id
        INNER JOIN temp_transaction_data ttd ON ttd.id = 1
        WHERE td.is_v1 = 0 AND td.transaction_ref_no IS NULL AND td.return_reason IS NOT NULL AND td.isCanpay = 0
        AND td.local_transaction_date BETWEEN ? AND ?
        GROUP BY rrm.reason_code
        WITH ROLLUP;";
        $result = DB::select($sql, $searchArray);

        // Create the temporary table
        DB::statement("DROP TABLE temp_transaction_data");

        return $result;
    }

    /**
     * generateReturnReportDashboardExport
     * This is the export function for Return Dashboard Report
     * @param  mixed $request
     * @return void
     */
    public function generateReturnReportDashboardExport(Request $request)
    {
        $from_date = $request->get('from_date');
        $to_date = $request->get('to_date');
        $searchArray = [$from_date, $to_date]; // Used for temp table data insertion

        $result = $this->_getreturndataForDashboard($searchArray);

        // Get the last element of the result array for footer portion
        $last_element_of_array = end($result);
        $footer_object = new \stdClass(); // Declaring footer object
        if ($last_element_of_array->transaction_count == 0) {
            $footer_object->avg_return_value = 0;
        } else {
            $footer_object->avg_return_value = round($last_element_of_array->total_transaction / $last_element_of_array->transaction_count, 2);
        }
        $footer_object->unpaid_transaction_amount = round($last_element_of_array->total_transaction - $last_element_of_array->paid_amount - $last_element_of_array->pending_amount, 2);
        if ($result[0]->total_transaction == 0) {
            $footer_object->unpaid_transaction_percent = 0;
        } else {
            $footer_object->unpaid_transaction_percent = round(($footer_object->unpaid_transaction_amount / $result[0]->total_transaction) * 100, 2);
        }
        $footer_object->pending_transaction_amount = round($last_element_of_array->pending_amount, 2);
        if ($result[0]->total_transaction == 0) {
            $footer_object->pending_transaction_percent = 0;
        } else {
            $footer_object->pending_transaction_percent = round(($footer_object->pending_transaction_amount / $result[0]->total_transaction) * 100, 2);
        }
        $footer_object->paid_transaction_amount = round($last_element_of_array->paid_amount, 2);
        if ($result[0]->total_transaction == 0) {
            $footer_object->paid_transaction_percent = 0;
        } else {
            $footer_object->paid_transaction_percent = round(($footer_object->paid_transaction_amount / $result[0]->total_transaction) * 100, 2);
        }

        $final_result['from_date'] = $from_date;
        $final_result['to_date'] = $to_date;
        $final_result['report_body'] = $result;
        $final_result['report_footer'] = $footer_object;

        return Excel::download(new ReturnReportDashboardExport($final_result), 'return_dashboard_report' . date('m-d-Y H:i:s') . '.xlsx');
    }

    /**
     * getStorewiseMonthlySales
     * This function is used to get storewise monthly sales growth
     * @param  mixed $request
     * @return void
     */
    public function getStorewiseMonthlySales(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Monthly Sales Growth report fetch started...");

        // Validating input request
        $this->validate($request, [
            'from_date' => VALIDATION_REQUIRED,
            'to_date' => VALIDATION_REQUIRED,
        ]);

        //Get Status ID
        $status_ids = getPendingSuccessReturnedStatus();

        $from_date = $request->get('from_date');
        $to_date = $request->get('to_date') . ' 23:59:59';

        $array_status_ids = explode(',', str_replace("'", '', $status_ids));

        // If count($array_status_ids) is 3 then output should come like ?,?,?
        $status_in = str_repeat('?,', count($array_status_ids) - 1) . '?';

        $sql = "SELECT ms.store_id,
        ms.retailer, ubai.routing_no, SUM(td.amount + td.tip_amount) AS sales, COUNT(*) AS transaction_count
        FROM transaction_details td FORCE INDEX(transaction_time_index)
        STRAIGHT_JOIN terminal_master tm ON td.terminal_id = tm.id
        STRAIGHT_JOIN merchant_stores ms ON tm.merchant_store_id = ms.id
        STRAIGHT_JOIN registered_merchant_master rmm ON ms.merchant_id = rmm.id
        STRAIGHT_JOIN user_bank_account_info ubai ON rmm.id = ubai.merchant_id
        WHERE td.is_v1 = 0 AND td.transaction_ref_no IS NULL AND td.scheduled_posting_date BETWEEN ? AND ? AND isCanpay = 0 AND td.status_id IN (" . $status_in . ")
        AND ubai.type = 'ach'
        GROUP BY ms.retailer ASC";

        $searchArray = [$from_date, $to_date];
        $searchArray = array_merge($searchArray, $array_status_ids);

        $results = DB::connection(MYSQL_RO)->select($sql, $searchArray);

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Storewise Monthly Sales report fetched successfully.");
        if (count($results) > 0) {
            $message = trans('message.monthly_sales_growth_fetch_success');
        } else {
            $message = trans('message.record_not_found');
        }
        return renderResponse(SUCCESS, $message, $results); // Returning response
    }

    /**
     * getStorewiseMonthlySalesExport
     * This is the export function for Storewise Monthly Sales Growth
     * @param  mixed $request
     * @return void
     */
    public function getStorewiseMonthlySalesExport(Request $request)
    {
        return Excel::download(new StorewiseMonthlySalesExport($request), 'storewise_monthly_sales' . date('m-d-Y H:i:s') . '.xlsx');
    }

    /**
     * getRewardSpinReport
     * This function is used to get the Reward
     * @param  mixed $request
     * @return void
     */
    public function getRewardSpinReport(Request $request)
    {
        Log::channel('reward-wheel-admin')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Reward Spin Report Fetch started... ");
        // Validating input request
        $this->validate($request, [
            'from_date' => VALIDATION_REQUIRED,
        ]);
        $time_array = []; // Declaring an array for logging TIme in different areas of the API
        $returnResponse = $this->_rewardSpinReport($request);

        $data = [];
        if (!empty($returnResponse[0])) {
            // Creating array to show the values in frontend
            foreach ($returnResponse[0] as $spin) {
                $nestedData['consumer_name'] = $spin->consumer_name;
                $nestedData['email'] = $spin->email;
                $nestedData['phone'] = $spin->phone;
                $nestedData['used_at'] = $spin->used_at;
                $nestedData['winning_details'] = $spin->winning_details;
                $nestedData['reward_point'] = $spin->reward_point;
                $nestedData['reward_amount'] = $spin->reward_amount;
                $nestedData['transaction_amount'] = $spin->transaction_amount;
                $nestedData['retailer'] = $spin->retailer != '' ? $spin->retailer : 'N/A';
                $nestedData['reward_wheel'] = $spin->reward_wheel;
                $nestedData['created_at'] = $spin->created_at;
                $nestedData['is_used'] = $spin->is_used;
                $nestedData['is_earned_spin'] = $spin->is_earned_spin;
                $data[] = $nestedData;
            }
        }
        $time_array['send_data_to_frontend'] = getTimeElapsed($request);
        Log::channel('reward-wheel-admin')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . json_encode($time_array));
        $json_data = array(
            "draw" => intval($request->input('draw')),
            "recordsTotal" => intval($returnResponse['totalcount']),
            "recordsFiltered" => intval($returnResponse['totalcount']),
            "data" => $data,
            "total_spins_earned" => $returnResponse['total_spins_earned'],
            "total_spins_spent" => $returnResponse['total_spins_spent'],
            "total_amount_earned_via_points" => $returnResponse['total_amount_earned_via_points'],
            "total_amount_spent_with_points" => $returnResponse['total_amount_spent_with_points'],
        );

        echo json_encode($json_data); // Rerurning the data
    }

    private function _rewardSpinReport($request, $export = false)
    {
        $getFromToDate = $this->_getFromToDate($request);
        $from_date = $getFromToDate['from_date'];
        $to_date = $getFromToDate['to_date'];
        $limit = intval($request->input('length'));
        $start = intval($request->input('start'));
        $voided = getStatus(VOIDED);

        $time_array['start_of_main_sql'] = getTimeElapsed($request);
        Log::channel('reward-wheel-admin')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . json_encode($time_array) . " - Reward Spin Report fetch started for Date Range: " . $from_date . " and " . $to_date);

        // Initialize userIdsArray
        $userIdsArray = [];

        // Check if any user search criteria are provided
        $searchingByUser = !empty(trim($request['email'])) || !empty(trim($request['phone_no'])) || !empty(trim($request['consumer']));

        if ($searchingByUser) {
            $userIdQuery = "SELECT user_id FROM users WHERE 1=1 ";
            $userIdSearchArray = [];

            if (!empty(trim($request['email']))) {
                $userIdQuery .= " AND email = ? ";
                $userIdSearchArray[] = $request['email'];
            }
            if (!empty(trim($request['phone_no']))) {
                $userIdQuery .= " AND phone = ? ";
                $userIdSearchArray[] = $request['phone_no'];
            }
            if (!empty(trim($request['consumer']))) {
                $userIdQuery .= " AND LOWER(REPLACE(CONCAT_WS(' ', REPLACE(first_name, ' ', ''), REPLACE(middle_name, ' ', ''), REPLACE(last_name, ' ', '')), '  ', ' ')) LIKE ? ";
                $userIdSearchArray[] = '%' . strtolower($request['consumer']) . '%';
            }

            // Fetch user_ids
            $userIds = DB::select($userIdQuery, $userIdSearchArray);
            $userIdsArray = array_column($userIds, 'user_id');

            // If searching by user and no matching users are found, log and return empty results
            if (empty($userIdsArray)) {
                $final_return_array = [
                    'reward_wheel_spins' => [],
                    'total_spins_earned' => 0,
                    'total_spins_spent' => 0,
                    'total_amount_earned_via_points' => 0,
                    'total_amount_spent_with_points' => 0,
                    'totalcount' => 0
                ];

                // Log the scenario
                $time_array['no_user_found_exception'] = getTimeElapsed($request);
                Log::channel('reward-wheel-admin')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . json_encode($time_array) . " - No users found matching the criteria for Reward Spin Report in Date Range: " . $from_date . " to " . $to_date);

                return $final_return_array;
            }
        }

        // Proceed with the main query (whether or not a user filter exists)
        $sql = "SELECT
        CONCAT_WS(' ', u.first_name, u.middle_name, u.last_name) AS consumer_name,
        u.email,
        u.phone,
        IFNULL(CONVERT_TZ(re.used_at, 'UTC', 'America/New_York'), 'N/A') AS used_at,
        rw.reward_wheel,
        ms.retailer,
        IFNULL(rm.segment_name, uruh.reason) AS winning_details,
        IFNULL(ROUND(uruh.reward_point, 0), 0) AS reward_point,
        IFNULL(ROUND(uruh.reward_amount, 2), 0) AS reward_amount,
        IFNULL(td.amount + td.tip_amount, 0) AS transaction_amount,
        re.created_at,
        re.is_used,
        CASE WHEN re.is_earned_spin = 1 THEN 'Yes' ELSE 'No' END AS is_earned_spin
        FROM " . config('app.reward_wheel_db') . ".rewards re
        LEFT JOIN " . config('app.reward_wheel_db') . ".reward_wheel_segment_masters rm ON re.segment_id = rm.id
        LEFT JOIN " . config('app.reward_wheel_db') . ".user_reward_usage_history uruh ON uruh.reward_id = re.id
        STRAIGHT_JOIN " . config('app.reward_wheel_db') . ".reward_wheels rw ON rw.id = re.reward_wheel_id
        JOIN users u ON u.user_id = re.user_id
        LEFT JOIN transaction_details td ON td.id = re.transaction_id AND td.status_id != ?
        LEFT JOIN terminal_master tm ON tm.id = td.terminal_id
        LEFT JOIN merchant_stores ms ON ms.id = tm.merchant_store_id
        WHERE re.created_at BETWEEN ? AND ?";

        // Add user filter only if userIdsArray is populated
        $searchArray = [$voided, $from_date, $to_date];

        if (!empty($userIdsArray)) {
            $sql .= " AND re.user_id IN (" . implode(',', array_fill(0, count($userIdsArray), '?')) . ")";
            $searchArray = array_merge($searchArray, $userIdsArray);
        }

        // Optional filters for store and reward wheel
        if (trim($request['store'])) {
            $sql .= " AND ms.id = ? ";
            array_push($searchArray, $request['store']);
        }
        if (trim($request['reward_wheel'])) {
            $sql .= " AND rw.id = ? ";
            array_push($searchArray, $request['reward_wheel']);
        }

        $totalcount = DB::select($sql, $searchArray);
        $time_array['totalcount_sql_completed'] = getTimeElapsed($request);
        Log::channel('reward-wheel-admin')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . json_encode($time_array));
        $final_return_array['totalcount'] = count($totalcount);

        if (!$export) {
            $sql .= " LIMIT " . $start . ", " . $limit;
        }

        $reward_wheel_spins = DB::select($sql, $searchArray);
        $time_array['with_limit_data_sql_before_sorting'] = getTimeElapsed($request);
        Log::channel('reward-wheel-admin')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . json_encode($time_array));

        // Sorting the array by created_at in descending order
        usort($reward_wheel_spins, function ($a, $b) {
            return strtotime($b->created_at) - strtotime($a->created_at);
        });

        $final_return_array['reward_wheel_spins'] = $reward_wheel_spins;

        $total_spins_earned = array_filter($reward_wheel_spins, function ($row) {
            return $row->winning_details == 'Free Spin';
        });

        $final_return_array['total_spins_earned'] = count($total_spins_earned);

        // Get the used spin count
        $total_spins_spent = array_filter($totalcount, function ($row) {
            return $row->is_used == 1 && $row->winning_details == 'Free Spin';
        });

        $final_return_array['total_spins_spent'] = count($total_spins_spent);

        $final_return_array['total_amount_earned_via_points'] = !empty($totalcount) ? number_format(array_sum(array_column($totalcount, 'reward_amount')), 2) : 0;

        $time_array['second_query_start'] = getTimeElapsed($request);
        Log::channel('reward-wheel-admin')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . json_encode($time_array));
        // Get the total amount spent on the transactions
        $success = getStatus(SUCCESS);
        $pending = getStatus(PENDING);
        $returned = getStatus(RETURNED);
        $transactions_sql = "SELECT COALESCE(ROUND(SUM(trd.reward_amount), 2), 0) as total_amount_spent_with_points
        FROM transaction_details td
        JOIN transaction_reward_details trd ON td.id = trd.transaction_id
        JOIN users u ON u.user_id = td.consumer_id
        JOIN terminal_master tm ON tm.id = td.terminal_id
        JOIN merchant_stores ms ON ms.id = tm.merchant_store_id
        WHERE td.transaction_ref_no IS NULL AND td.isCanpay = 0 AND td.transaction_time BETWEEN ? AND ?
        AND td.reward_point_used > 0 AND td.status_id IN ('" . $success . "', '" . $pending . "', '" . $returned . "') ";

        $searchTransactionArray = [$from_date, $to_date];
        if (trim($request['email'])) {
            $transactions_sql .= " AND u.email = ? ";
            array_push($searchTransactionArray, $request['email']);
        }
        if (trim($request['phone_no'])) {
            $transactions_sql .= " AND u.phone = ? ";
            array_push($searchTransactionArray, $request['phone_no']);
        }
        if (trim($request['consumer'])) {
            $transactions_sql .= ' AND LOWER(
                REPLACE(CONCAT(COALESCE(
                REPLACE(u.first_name, " ",""), "")," ", COALESCE(
                REPLACE(u.middle_name, " ",""), "")," ", COALESCE(
                REPLACE(u.last_name, " ",""), "")),"  "," ")) LIKE ? ';
            array_push($searchTransactionArray, '%' . $request['consumer'] . '%');
        }
        if (trim($request['store'])) {
            $transactions_sql .= " AND ms.id = ? ";
            array_push($searchTransactionArray, $request['store']);
        }
        if (trim($request['reward_wheel'])) {
            $transactions_sql .= " AND trd.reward_wheel_id = ? ";
            array_push($searchTransactionArray, $request['reward_wheel']);
        }
        $reward_amount_spent = DB::select($transactions_sql, $searchTransactionArray);
        $final_return_array['total_amount_spent_with_points'] = $reward_amount_spent[0]->total_amount_spent_with_points;

        array_push($final_return_array, $reward_wheel_spins);

        $time_array['final_return_array'] = getTimeElapsed($request);
        Log::channel('reward-wheel-admin')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . json_encode($time_array) . " - Reward Spin Report fetched successfully for Date Range: " . $from_date . " and " . $to_date);
        return $final_return_array;
    }

    /**
     * getRewardSpinReportExport
     * This is the export function for Reward Spin Report Export
     * @param  mixed $request
     * @return void
     */
    public function getRewardSpinReportExport(Request $request)
    {
        Log::channel('reward-wheel-admin')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Reward Spin Report export started for Date Range: " . $request->get('from_date') . " and " . $request->get('to_date'));
        // Validating input request
        $this->validate($request, [
            'from_date' => VALIDATION_REQUIRED,
        ]);

        $getFromToDate = $this->_getFromToDate($request);
        $from_date = $getFromToDate['from_date'];
        $to_date = $getFromToDate['to_date'];

        $returnResponse = $this->_rewardSpinReport($request, 1);
        $request->merge(['report' => $returnResponse]);
        $request->merge(['from_date' => $from_date]);
        $request->merge(['to_date' => $to_date]);
        Log::channel('reward-wheel-admin')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Reward Spin Report exported successfully for Date Range: " . $from_date . " and " . $to_date);
        return Excel::download(new RewardSpinReportExport($request), '_canpay_points_report_export' . date('m-d-Y H:i:s') . '.xlsx');
    }

    private function _getFromToDate($request)
    {
        $report_from_date = $request['from_date'] . ' 07:00:00';
        $from_date = Carbon::createFromFormat('Y-m-d H:i:s', $report_from_date, 'America/New_york');
        $arr['from_date'] = $from_date->setTimezone('UTC');

        $report_to_date = Carbon::parse($from_date)->addDays(1)->toDateString() . " 06:59:59";
        $to_date = Carbon::createFromFormat('Y-m-d H:i:s', $report_to_date, 'America/New_york');
        $arr['to_date'] = $to_date->setTimezone('UTC');

        return $arr;
    }

    /**
     * getDeliveryFeeReport
     * This function is used to get the delivery fee report
     * @param  mixed $request
     * @return void
     */
    public function getDeliveryFeeReport(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Delivery Fee Report Fetch Started...");

        $this->validate($request, [
            'from_date' => VALIDATION_REQUIRED,
            'to_date' => VALIDATION_REQUIRED,
        ]);

        $sql = "SELECT transaction_details.transaction_number,
                concat_ws(' ', users.first_name, users.middle_name, users.last_name) as consumer_name,
                transaction_details.id, rmm.merchant_name as delivery_partner, registered_merchant_master.merchant_name,
                merchant_stores.retailer,
                CASE
                    WHEN transaction_details.pos_web_identifier != '' THEN transaction_details.pos_web_identifier
                    ELSE terminal_master.terminal_name
                END as terminal_name,
                transaction_details.transaction_time, transaction_details.local_transaction_time,
                transaction_details.is_v1, timezone_masters.timezone_name,
                transaction_details.scheduled_posting_date,
                modified_transaction.scheduled_posting_date as m_scheduled_posting_date,
                transaction_details.scheduled_posting_time,
                modified_transaction.scheduled_posting_time as m_scheduled_posting_time,
                transaction_details.consumer_approval_for_change_request,
                transaction_details.expiration_datetime,
                modified_status_master.code as m_code,
                status_master.code,
                transaction_details.change_request,
                transaction_details.delivery_fee,
                transaction_details.updated_amount,
                transaction_details.attempt_count,
                transaction_details.local_transaction_date,
                transaction_details.is_ecommerce,
                transaction_details.admin_driven,
                CASE
                    WHEN transaction_details.change_request = 1 AND (modified_status_master.code='" . VOIDED . "' OR modified_status_master.status IS NULL) THEN casm.`status`
                    WHEN modified_status_master.status IS NOT NULL THEN modified_status_master.status
                    WHEN status_master.status = '" . GENERAL_EXCEPTION . "' THEN '" . TRANSACTION_DECLINED . "'
                    ELSE status_master.status
                END AS status,
                CASE
                    WHEN (modified_transaction.id != '') THEN modified_transaction.tip_amount
                    ELSE transaction_details.tip_amount
                END as last_approve_tip_amount,
                CASE
                    WHEN (modified_transaction.consumer_bank_posting_amount != '') THEN modified_transaction.consumer_bank_posting_amount
                    ELSE transaction_details.consumer_bank_posting_amount
                END as consumer_bank_posting_amount,
                CASE
                    WHEN (modified_transaction.amount != '') THEN modified_transaction.amount
                    ELSE transaction_details.amount
                END as amount,
                CASE
                    WHEN (modified_transaction.reward_amount_used != '') THEN modified_transaction.reward_amount_used
                    ELSE transaction_details.reward_amount_used
                END as reward_amount_used
                FROM transaction_details
                LEFT JOIN `transaction_details` as `modified_transaction` ON `modified_transaction`.`id` = `transaction_details`.`change_request_transaction_ref_no`
                LEFT JOIN `status_master` as `modified_status_master` ON `modified_status_master`.`id` = `modified_transaction`.`status_id`
                LEFT JOIN `status_master` as `casm` ON `casm`.`id` = `transaction_details`.`consumer_approval_for_change_request`
                JOIN users ON users.user_id = transaction_details.consumer_id
                JOIN terminal_master ON terminal_master.id = transaction_details.terminal_id
                JOIN merchant_stores ON merchant_stores.id = terminal_master.merchant_store_id
                JOIN timezone_masters ON timezone_masters.id = transaction_details.timezone_id
                JOIN status_master ON status_master.id = transaction_details.status_id
                JOIN registered_merchant_master ON registered_merchant_master.id = merchant_stores.merchant_id
                LEFT JOIN registered_merchant_master rmm ON rmm.id = transaction_details.delivery_partner_id
                WHERE transaction_details.transaction_ref_no is null
                AND ((`transaction_details`.`attempt_count` = 0 AND transaction_details.change_request_transaction_ref_no IS NULL) OR (`transaction_details`.`attempt_count` != 0))
                AND transaction_details.isCanpay = 0
                AND transaction_details.delivery_fee > 0";

        if ($request->get('from_date')) {
            $sql .= " AND transaction_details.local_transaction_date >= ?";
        }
        if ($request->get('to_date')) {
            $sql .= " AND transaction_details.local_transaction_date <= ?";
        }
        if ($request->get('email')) {
            $sql .= " AND users.email = ?";
        }
        if ($request->get('phone_no')) {
            $sql .= " AND users.phone = ?";
        }
        if ($request->get('consumer')) {
            $sql .= " AND users.user_id = ?";
        }
        if ($request->get('merchant')) {
            $sql .= " AND (registered_merchant_master.id = ? OR rmm.id = ?)";
        }
        $sql .= " GROUP BY transaction_details.id ORDER BY transaction_details.transaction_time DESC";

        $searchArray = [];

        if ($request->get('from_date')) {
            $searchArray[] = $request->get('from_date');
        }
        if ($request->get('to_date')) {
            $searchArray[] = $request->get('to_date');
        }
        if ($request->get('email')) {
            $searchArray[] = $request->get('email');
        }
        if ($request->get('phone_no')) {
            $searchArray[] = $request->get('phone_no');
        }
        if ($request->get('consumer')) {
            $searchArray[] = $request->get('consumer');
        }
        if ($request->get('merchant')) {
            $merchant = $request->get('merchant');
            $searchArray[] = $merchant;
            $searchArray[] = $merchant;
        }

        $transactions = DB::connection(MYSQL_RO)->select($sql, $searchArray);

        $data = array();
        if (!empty($transactions)) {
            // Creating array to show the values in frontend
            foreach ($transactions as $transaction) {
                $nestedData['transaction_number'] = $transaction->transaction_number;
                $nestedData['consumer_name'] = $transaction->consumer_name;
                $nestedData['merchant_name'] = $transaction->merchant_name;
                $nestedData['store_name'] = $transaction->retailer;
                $nestedData['terminal_name'] = $transaction->terminal_name;
                $nestedData['delivery_partner'] = $transaction->delivery_partner;
                $nestedData['edit'] = $transaction->id;
                $nestedData['amount'] = number_format($transaction->amount, 2);
                $nestedData['delivery_fee'] = number_format($transaction->delivery_fee, 2);
                $nestedData['merchant_funding_amount'] = number_format($transaction->amount - $transaction->delivery_fee, 2);
                $nestedData['transaction_time'] = date('m-d-Y H:i:s', strtotime($transaction->local_transaction_time)) . ' <br/> (' . $transaction->timezone_name . ')';
                $nestedData['consumer_bank_posting_amount'] = $transaction->consumer_bank_posting_amount;
                $nestedData['reward_amount_used'] = $transaction->reward_amount_used;

                $nestedData['status'] = $transaction->status;

                $scheduled_posting_date = $transaction->m_scheduled_posting_date ? $transaction->m_scheduled_posting_date : $transaction->scheduled_posting_date;
                // when transaction need merchant admin approval then show Pending Store's Approval message
                if (!$scheduled_posting_date && $transaction->status == PENDING_STATUS_NAME) {
                    $nestedData['status'] = PENDING_STORE_APPROVAL;
                }
                $nestedData['is_v1'] = $transaction->is_v1;
                $data[] = $nestedData;
            }
        }
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Delivery Fee Report fetched successfully.");
        $message = trans('message.delivery_fee_report_fetch_success');
        return renderResponse(SUCCESS, $message, $data); // Returning response
    }

    /**
     * getDeliveryFeeTransactionExport
     * This is the export function for Delivery Fee Transaction
     * @param  mixed $request
     * @return void
     */
    public function getDeliveryFeeTransactionExport(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Starting Delivery Fee Transaction Export ");
        return Excel::download(new DeliveryFeeTransactionExport($request), 'delivery_fee_transaction_' . date('m-d-Y H:i:s') . '.xlsx');
    }

    /**
     * Retrieves a list of all active merchants and delivery partners.
     *
     * Fetches the merchants and delivery partners from the database where their
     * status is active and their type is either MERCHANT or DELIVERY_PARTNER.
     * Logs the start and successful completion of the fetch operation.
     *
     * @return \Illuminate\Http\JsonResponse A JSON response containing the list of
     *         active merchants and delivery partners.
     */

    public function getAllMerchantsAndDeliveryPartners(Request $request)
    {
        $this->validate($request, [
            'merchant_name' => VALIDATION_REQUIRED,
        ]);
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Merchants and Delivery Partners fetch started...");
        if (!$request->has('delivery_partner')) {
            $merchants = RegisteredMerchantMaster::select('id', 'merchant_name')->whereIn('type', [MERCHANT, DELIVERY_PARTNER])->orderBy('created_at', 'DESC')->get();
        } else {
            $merchants = RegisteredMerchantMaster::select('id', 'merchant_name')->whereIn('type', [DELIVERY_PARTNER])->orderBy('created_at', 'DESC')->get();
        }
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Merchants and Delivery Partners fetched successfully.");
        $message = trans('message.merchants_and_delivery_partners_fetch_success');
        return renderResponse(SUCCESS, $message, $merchants);
    }

    /**
     * Delivery Fee Settlement Report
     * This function is used to get the Delivery Fee Settlement Report
     * @param  mixed $request
     * @return void
     */
    public function deliveryFeeSettlementReport(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Delivery Fee Settlement Report Fetch Started...");
        // Validating the input requests
        $rule = array(
            'from_date' => VALIDATION_REQUIRED,
            'to_date' => VALIDATION_REQUIRED,
            'delivery_partner_id' => VALIDATION_REQUIRED,
        );
        $this->__validate($request->all(), $rule);

        $result = $this->_getDeliverySettlementData($request);

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Delivery Fee Settlement Report fetched successfully.");
        $message = trans('message.delivery_settlement_report_fetch_success');
        return renderResponse(SUCCESS, $message, $result); // Returning response
    }

    /**
     * Retrieves delivery settlement data for a specified date range and delivery partner.
     *
     * This function constructs and executes a SQL query to gather aggregated delivery fee
     * data for transactions involving a specific delivery partner within a given date range.
     * It calculates various metrics such as retail volume, transaction count, and subtotal fees.
     *
     * @param  \Illuminate\Http\Request $request The request object containing the date range
     *                                           and delivery partner ID.
     * @return array An array of objects, each containing details of the delivery settlement
     *               data such as sales date, retail volume, volume rate, number of transactions,
     *               and final fees.
     */
    private function _getDeliverySettlementData($request, $export = 0)
    {
        // Get Status IDs for Pending, Success, and Returned
        $status_ids = getStatuses([PENDING, SUCCESS, RETURNED]);

        // Access individual status IDs
        $pending_status_id = $status_ids[PENDING];
        $success_status_id = $status_ids[SUCCESS];
        $return_status_id = $status_ids[RETURNED];

        $status_str = "'" . $pending_status_id . "','" . $success_status_id . "','" . $return_status_id . "'";

        $v2_retail_sql = "WITH recursive all_dates(dt) AS (
            SELECT ? dt
              UNION ALL
            SELECT dt + interval 1 day FROM all_dates WHERE dt + interval 1 DAY <= ?)";

        $v2_retail_sql .= ", combined_data AS (
                SELECT dt, td.id AS transaction_id,
                    td.delivery_fee,
                    0 AS reward_amount,
                    sdpm.canpay_commission,
                    ms.retailer,
                    ms.id AS store_id
                FROM all_dates AS d
                LEFT JOIN transaction_details AS td ON td.scheduled_posting_date = d.dt AND td.status_id IN ($status_str) AND td.is_v1 = 0 AND td.transaction_ref_no IS NULL AND td.isCanpay = 0 AND td.delivery_partner_id = ? AND td.delivery_fee > 0
                LEFT JOIN terminal_master tm ON tm.id = td.terminal_id
                LEFT JOIN merchant_stores ms ON tm.merchant_store_id = ms.id
                LEFT JOIN store_delivery_partner_maps sdpm ON ms.id = sdpm.store_id AND sdpm.delivery_partner_id = ?
                WHERE td.id IS NOT NULL
                GROUP BY td.id
            )
            SELECT
                d.dt AS sales_date,
                IFNULL(SUM(delivery_fee), 0) AS v2_retail_volume,
                canpay_commission AS v2_retail_vol_rate,
                COUNT(transaction_id) AS v2_retail_no_of_trans,
                IFNULL(((SUM(delivery_fee) / 100) * canpay_commission), 0) AS v2_retail_sub_total_fees,
                retailer
            FROM all_dates AS d
            LEFT JOIN combined_data AS cd ON cd.dt = d.dt";
        $v2_retail_sql .= " GROUP BY d.dt DESC
            WITH ROLLUP";
        $v2_retail = DB::connection(MYSQL_RO)->select($v2_retail_sql, [$request->get('from_date'), $request->get('to_date'), $request->get('delivery_partner_id'), $request->get('delivery_partner_id')]);
        array_unshift($v2_retail, end($v2_retail)); // Prepending the total to the first object of the array

        $result = [];
        $indexesToUpdate = [];
        $totalV2RetailVolume = 0;
        $totalV2RetailFees = 0;

        foreach ($v2_retail as $index => $value) {
            $activity = [];

            if ($value->sales_date === null) {
                $indexesToUpdate[] = $index;
            } else {
                $totalV2RetailVolume += round_up($value->v2_retail_volume, 2);
                $totalV2RetailFees += round_up($value->v2_retail_sub_total_fees, 2);
            }

            $activity['sales_date'] = $value->sales_date === null ? "Totals" : date('m/d/Y', strtotime($value->sales_date));
            $activity['v2_retail_volume'] = formatCurrency($value->v2_retail_volume);
            $activity['v2_retail_vol_rate'] = $value->sales_date === null ? "" : formatPercentage($value->v2_retail_vol_rate);
            $activity['v2_retail_vol_fee'] = formatCurrency(round_up($value->v2_retail_sub_total_fees, 2));
            $activity['v2_retail_no_of_trans'] = $value->v2_retail_no_of_trans;
            $activity['v2_final_fees'] = $activity['v2_retail_vol_fee'];

            $result[] = $activity;
        }

        // Update specified indices with totals
        foreach ($indexesToUpdate as $index) {
            if (isset($result[$index])) {
                $result[$index]['v2_retail_volume'] = formatCurrency($totalV2RetailVolume);
                $result[$index]['v2_retail_vol_fee'] = formatCurrency($totalV2RetailFees);
                $result[$index]['v2_final_fees'] = formatCurrency($totalV2RetailFees);
            }
        }

        return $result;
    }

    /**
     * Delivery Fee Settlement Report Export
     * This function is used to export the report of delivery fee settlement for a given date range and delivery partner
     * @param  Request $request
     * @return void
     */
    public function deliveryFeeSettlementReportExport(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Starting Delivery Fee Settlement Report Export...");

        // Validating the input requests
        $this->validate($request, [
            'from_date' => VALIDATION_REQUIRED,
            'to_date' => VALIDATION_REQUIRED,
            'delivery_partner_id' => VALIDATION_REQUIRED,
        ]);

        $delivery_partner = RegisteredMerchantMaster::find($request->get('delivery_partner_id'));

        // Prepare report headers
        $result = [
            [
                'sales_date' => $delivery_partner->merchant_name,
                'v2_retail_volume' => '',
                'v2_retail_vol_rate' => '',
                'v2_retail_vol_fee' => '',
                'v2_retail_fee_rate' => '',
                'v2_final_fees' => '',
            ],
            [
                'sales_date' => 'Sales Date',
                'v2_retail_volume' => 'Delivery Fee',
                'v2_retail_vol_rate' => 'Rate',
                'v2_retail_vol_fee' => 'Vol Fee',
                'v2_retail_fee_rate' => 'Rate',
                'v2_final_fees' => 'Final Total Fees',
            ],
        ];

        // Add settlement data
        $result[] = $this->_getDeliverySettlementData($request, 1);

        // Calculate date difference
        $fromDate = new \DateTime($request->get('from_date'));
        $toDate = new \DateTime($request->get('to_date'));
        $dateDiff = $fromDate->diff($toDate)->days;

        // Prepare response
        $returnResponse = [
            'from_date' => $request->get('from_date'),
            'to_date' => $request->get('to_date'),
            'date_diff' => $dateDiff,
            'report' => $result,
        ];

        // Export to Excel
        $fileName = 'delivery_settlement_report_' . date('Y-m-d_H-i') . '.xlsx';
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Delivery Fee Settlement Report Exported Successfully.");
        return Excel::download(new DeliverySettlementReportExport($returnResponse), $fileName);
    }

    /**
     * generateMerchantPointReport
     * This function is used to get Merchant Point Report data
     * @param  mixed $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function generateMerchantPointReport(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Merchant Point Report Fetch Started");
        // Validating input request
        $this->validate($request, [
            'from_date' => VALIDATION_REQUIRED,
            'to_date' => VALIDATION_REQUIRED,
        ]);

        $voided = getStatus(VOIDED);

        try {
            // Get date range with time
            $from_date = $request->from_date . ' 00:00:00';
            $to_date = $request->to_date . ' 23:59:59';

            $data = UserRewardUsageHistory::selectRaw("
                        DATE(user_reward_usage_history.created_at) as transaction_date,
                        CASE
                            WHEN sponsor_link_id IS NOT NULL THEN '" .SPONSOR_POINTS."'
                            WHEN campaign_id IS NOT NULL THEN '" .BRAND_POINT."'
                            WHEN cashback_program_id IS NOT NULL THEN '" .CASHBACK_POINTS."'
                            WHEN petition_id IS NOT NULL THEN '" .PETITION_POINTS."'
                            ELSE 'Unknown'
                        END as reward_type,
                        user_reward_usage_history.entry_type,
                        SUM(user_reward_usage_history.reward_point) as total_points,
                        SUM(user_reward_usage_history.reward_amount) as reward_amounts
                    ")
                ->leftJoin(config('app.main_db') . '.transaction_details', function($join) use ($voided) {
                    $join->on('transaction_details.id', '=', 'user_reward_usage_history.transaction_id')
                        ->where('transaction_details.status_id', $voided);
                })
                ->whereNull('transaction_details.id')
                ->whereBetween('user_reward_usage_history.created_at', [$from_date, $to_date])
                ->where(function ($query) {
                    $query->whereNotNull('user_reward_usage_history.sponsor_link_id')
                        ->orWhereNotNull('user_reward_usage_history.cashback_program_id')
                        ->orWhereNotNull('user_reward_usage_history.campaign_id')
                        ->orWhereNotNull('user_reward_usage_history.petition_id');
                })
                ->whereIn('user_reward_usage_history.reason', [
                    SPONSOR_POINTS, 
                    BRAND_POINT, 
                    CASHBACK_POINTS, 
                    PETITION_POINTS, 
                    PETITION_REASON_REFERRAL_SIGNUP_REWARD,
                    PETITION_REASON_PROMOTED_TO_MAYOR,
                    PETITION_REASON_PROMOTED_TO_CREW_LEADER,
                    PETITION_TIP,
                    CONSUMER_TIP_INVALID_THROUGH_PETITION,
                    CONSUMER_TIP_INVALID_THROUGH_PETITION_WITHDRAWN,
                    LAUNCHED_THE_PETITION_AND_BECOME_A_MAYOR,
                    SIGNED_THE_PETITION,
                    TRANSACTION
                ])
                ->groupBy('transaction_date', 'reward_type', 'user_reward_usage_history.entry_type')
                ->orderBy('transaction_date', 'desc')
                ->get();

            // Step 1: Generate all dates in the range
            $startDate = Carbon::parse($request->from_date);
            $endDate = Carbon::parse($request->to_date);

            $allDates = [];
            while ($startDate->lte($endDate)) {
                $allDates[] = $startDate->toDateString();
                $startDate->addDay();
            } 
            // Format the output
            $formatted = [];
            // Initialize reward type arrays
            $rewardTypes = [SPONSOR_POINTS, BRAND_POINT, CASHBACK_POINTS, PETITION_POINTS];
            foreach ($rewardTypes as $type) {
                foreach ($allDates as $date) {
                    $formatted[$type][$date] = [
                        'date' => $date,
                        'cr' => [
                            'points' => 0,
                            'amounts' => 0
                        ],
                        'dr' => [
                            'points' => 0,
                            'amounts' => 0
                        ]
                    ];
                }
            }
            // Process data
            foreach ($data as $row) {
                $date = $row->transaction_date;
                $rewardType = $row->reward_type;
                $entryType = $row->entry_type;
                $totalPoints = $row->total_points;
                $totalAmounts = $row->reward_amounts;

                if ($entryType === 'Cr') {
                    $formatted[$rewardType][$date]['cr']['points'] += $totalPoints;
                    $formatted[$rewardType][$date]['cr']['amounts'] += $totalAmounts;
                } else if ($entryType === 'Dr') {
                    $formatted[$rewardType][$date]['dr']['points'] += $totalPoints;
                    $formatted[$rewardType][$date]['dr']['amounts'] += $totalAmounts;
                }
            }
            
            // Calculate totals for each reward type
            $totals = [];
            foreach ($rewardTypes as $type) {
                $totals[$type] = [
                    'total_cr' => [
                            'points' => 0,
                            'amounts' => 0
                        ],
                    'total_dr' => [
                            'points' => 0,
                            'amounts' => 0
                        ],
                ];
                
                foreach ($formatted[$type] as $dateData) {
                    $totals[$type]['total_cr']['points'] += $dateData['cr']['points'];
                    $totals[$type]['total_cr']['amounts'] += $dateData['cr']['amounts'];
                    $totals[$type]['total_dr']['points'] += $dateData['dr']['points'];
                    $totals[$type]['total_dr']['amounts'] += $dateData['dr']['amounts'];
                }
            }
            
            $result = [
                'rewardTypes' => $rewardTypes,
                'data' => $formatted,
                'totals' => $totals
            ];
            
            $message = trans('message.merchant_point_report_fetch_success');
            return renderResponse(SUCCESS, $message, $result); // Returning response
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . $e->getMessage());
            $message = trans('message.release_conusmer_failed');
            return renderResponse(FAIL, $message, null); // Returning response
        }
    }

    /**
     * generateMerchantPointReportExport
     * This function exports the Merchant Point Report to Excel
     * @param  mixed $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function generateMerchantPointReportExport(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Merchant Point Report Export Started");
        
        try {
            return Excel::download(new MerchantPointReportExport($request), 'merchant_point_report_' . date('m-d-Y H:i:s') . '.xlsx');
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . $e->getMessage());
            $message = trans('message.merchant_point_report_export_error');
            return renderResponse(FAIL, $message, null); // Returning response
        }
    }
}
