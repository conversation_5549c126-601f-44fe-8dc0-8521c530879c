<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ConsumerStatusUpdateHistory extends Model
{

    protected $table = 'consumer_status_update_history';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'consumer_id',
        'status',
        'source',
        'reason',
        'updated_by',
    ];
    public $timestamps = true;
    public $incrementing = false;
    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
}
