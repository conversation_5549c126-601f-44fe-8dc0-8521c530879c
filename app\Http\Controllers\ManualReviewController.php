<?php

// PostController.php

namespace App\Http\Controllers;

use App\Http\Clients\ApiHttpClient;
use App\Http\Factories\EmailExecutor\EmailExecutorFactory;
use App\Http\Factories\Firebase\FirebaseFactory;
use App\Http\Factories\IdValidator\IdValidatorFactory;
use App\Models\BankAccountInfo;
use App\Models\CognitoRulesLog;
use App\Models\DuplicateUser;
use App\Models\ManualReviewDetail;
use App\Models\OnboardManualReview;
use App\Models\RegistrationSession;
use App\Models\StatusMaster;
use App\Models\User;
use App\Models\ValidAccountNumber;
use App\Models\ValidationLog;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class ManualReviewController extends Controller
{

    private $request;
    private $emailexecutor;
    private $api;
    private $idvalidator;
    private $firebase;

    public function __construct(Request $request)
    {
        $this->request = $request;
        $this->emailexecutor = new EmailExecutorFactory();
        $this->api = new ApiHttpClient();
        $this->idvalidator = new IdValidatorFactory();
        $this->firebase = new FirebaseFactory();
    }
    /**
     * Fetch all manual identity verification details
     */
    public function getAllManualReviewDetails(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Manual Review Details search started...");
        // Validating input request
        $this->validate($request, [
            'type' => VALIDATION_REQUIRED,
        ]);

        // Search with in Manual Identity Review
        $manualReviews = $this->_getManualReviewSearch($request);

        $message = trans('message.manual_review_search_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Manual Review Details search complete.");
        return renderResponse(SUCCESS, $message, $manualReviews);
    }

    /**
     * _getManualReviewSearch
     * Fetch Manual Identity Review
     * @param  mixed $searchArray
     * @return void
     */
    private function _getManualReviewSearch($request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Manual Review Details Search Started.");

        $type = $request['type'];
        $status = StatusMaster::where('code', $type)->first();

        $from_date = $request['from_date'];
        $to_date = $request['to_date'] . ' 23:59:59';

        // Fetch Manual Identity Review
        $sql = 'SELECT registration_session_details.first_name,registration_session_details.middle_name,registration_session_details.last_name,registration_session_details.email,manual_review_details.* FROM manual_review_details JOIN registration_session_details ON manual_review_details.session_id = registration_session_details.id WHERE review_status = "' . $status->id . '" AND registration_session_details.created_at >= DATE(NOW()) - INTERVAL ' . config('app.session_validity') . ' DAY ';

        $searchStr = [];
        if ($from_date && $to_date) {
            $sql .= " AND manual_review_details.created_at BETWEEN ? AND ? ";
            array_push($searchStr, $from_date, $to_date);
        }
        if (trim($request['email'])) {
            $sql .= " AND registration_session_details.email = ? ";
            array_push($searchStr, $request['email']);
        }
        if (trim($request['phone_no'])) {
            $sql .= " AND registration_session_details.phone = ? ";
            array_push($searchStr, $request['phone_no']);
        }
        if (strlen(trim($request['consumer'])) >= 3) {
            $sql .= ' AND LOWER(
                REPLACE(CONCAT(COALESCE(
                REPLACE(registration_session_details.first_name, " ",""), "")," ", COALESCE(
                REPLACE(registration_session_details.middle_name, " ",""), "")," ", COALESCE(
                REPLACE(registration_session_details.last_name, " ",""), "")),"  "," ")) LIKE ? ';
            array_push($searchStr, '%' . $request['consumer'] . '%');
        }
        $sql .= " ORDER BY manual_review_details.created_at DESC LIMIT 100";

        $consumers = DB::connection(MYSQL_RO)->Select($sql, $searchStr);

        $consumerArr = [];
        if (!empty($consumers)) {
            foreach ($consumers as $consumer) {

                $data = [];
                $data['name'] = $consumer->first_name . ' ' . $consumer->middle_name . ' ' . $consumer->last_name;
                $data['edit'] = $consumer->id;
                $data['session_id'] = $consumer->session_id;
                $data['first_name'] = $consumer->first_name;
                $data['middle_name'] = $consumer->middle_name;
                $data['last_name'] = $consumer->last_name;
                $data['phone'] = $consumer->phone;
                $data['email'] = $consumer->email;
                $data['id_front'] = $consumer->doc_front_side;
                $data['id_back'] = $consumer->doc_back_side;
                $data['created_at'] = date('m-d-Y h:i A', strtotime($consumer->created_at));

                array_push($consumerArr, $data);
            }
        } else {
            $consumerArr = [];
        }

        return $consumerArr;
    }

    /**
     * Get the cognito log details for a perticular registration session
     */
    public function getIdentityValidationDetails()
    {
        $data = $this->request->all();
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Getting identity validation details for manual review details with id: " . $data['id']);
        $details = ManualReviewDetail::where('id', $data['id'])->first();

        $cognito_rules_log = CognitoRulesLog::where('phone', $details->phone)->orderby('created_at', 'DESC')->first();
        $risk_score = !empty(getRiskScore($details->phone)) ? getRiskScore($details->phone): 'Not Present';
        $expiryTime = intval(config('app.s3_file_expiry_time'));
        $expiryDateTime = Carbon::now()->addMinutes($expiryTime);
        $disk = Storage::disk('s3');
        $front_url = $disk->temporaryUrl($details->doc_front_side, $expiryDateTime, []);
        $back_url = $disk->temporaryUrl($details->doc_back_side, $expiryDateTime, []);
        if ($cognito_rules_log) {
            $log = json_decode($cognito_rules_log->response, true);
            if (!empty($log)) {
                try {
                    $log['source_ssn'] = isset($log['source_ssn']) ? base64_decode($log['source_ssn']) : "N/A";
                    $log['input_ssn'] = !empty($log['input_ssn']) ? base64_decode($log['input_ssn']) : "N/A";

                    // Praparing a temporary URL to fetch the image from S3. We are using temporary url so that the url gets invalid after defined minutes, so that the user data got protected.
                    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Successfully fetched identity validation details");
                    $response['log'] = $log;
                    $response['cognito_response'] = 'success';
                    $response['image_front'] = $front_url;
                    $response['image_back'] = $back_url;
                    $response['record_id'] = $details->id;
                    $response['risk_score'] = $risk_score;
                    $message = trans('message.identity_details_success');
                    return renderResponse(SUCCESS, $message, $response);
                } catch (\Exception $e) {

                    $message = trans('message.general_error');
                    // Praparing a temporary URL to fetch the image from S3. We are using temporary url so that the url gets invalid after defined minutes, so that the user data got protected.
                    $response['session_id'] = $cognito_rules_log->session_id;
                    $response['user_id'] = $cognito_rules_log->user_id;
                    $response['cognito_response'] = 'fail';
                    $response['image_front'] = $front_url;
                    $response['image_back'] = $back_url;
                    $response['record_id'] = $details->id;
                    $response['risk_score'] = $risk_score;
                    return renderResponse(SUCCESS, $message, $response);
                }
            } else {

                $message = trans('message.general_error');
                $response['session_id'] = $cognito_rules_log->session_id;
                $response['user_id'] = $cognito_rules_log->user_id;
                $response['cognito_response'] = 'fail';
                $response['image_front'] = $front_url;
                $response['image_back'] = $back_url;
                $response['record_id'] = $details->id;
                $response['risk_score'] = $risk_score;
                return renderResponse(SUCCESS, $message, $response);
            }
        } else {

            $message = trans('message.general_error');
            $response['session_id'] = $cognito_rules_log->session_id;
            $response['user_id'] = $cognito_rules_log->user_id;
            $response['cognito_response'] = 'fail';
            $response['image_front'] = $front_url;
            $response['image_back'] = $back_url;
            $response['record_id'] = $details->id;
            $response['risk_score'] = $risk_score;
            return renderResponse(SUCCESS, $message, $response);
        }
    }

    /**
     * Mark record as reviewed
     */
    public function updateReviewStatus()
    {
        $data = $this->request->all();
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Updating status of record with id: " . $data['id'] . " as reviewed");
        DB::beginTransaction();
        try {
            $details = ManualReviewDetail::where('id', $data['id'])->first();
            $status = StatusMaster::where('code', $data['code'])->first();
            $details->review_status = $status->id;
            $details->save();
            $consumer_role = getRole(CONSUMER);
            $lite_user = User::where('phone', $details->phone)->where('role_id', $consumer_role)->where('consumer_type', LITE_CONSUMER)->first();
            if ($lite_user) {
                if ($data['code'] == SUSPECTED_FRAUD) {
                    $lite_user->status = $status->id;
                    $lite_user->save();
                    // Add all the bank accounts of the consumer to the blacklist table
                    $bank_accounts = BankAccountInfo::where('user_id', $lite_user->user_id)->get();
                    foreach ($bank_accounts as $bank_details) {
                        addAccountToBlacklist($bank_details);
                    }
                    // Remove all accounts from whitelist table for this user
                    ValidAccountNumber::where('consumer_id', $lite_user->user_id)->delete();
                }
                $userStatusArr = [
                    'user_id' => $lite_user->user_id,
                    'review_status_code' => $data['code'],
                ];
                // Update Status Into Firebase
                $this->firebase->storeUserReviewStatusIntoFireStore($userStatusArr);
            }
            DB::commit();
            if ($data['code'] == APPROVED || $data['code'] == SUSPECTED_FRAUD) { //if status is approved or suspected fraud send an email to consumer email id
                //get all the details needed to send the email
                $params = RegistrationSession::where('id', $details->session_id)->first();
                $this->emailexecutor->approvedConsumerIdenity($params);
            } else {
                $params = RegistrationSession::where('id', $details->session_id)->first();
                $this->emailexecutor->suspendConsumerIdenity($params);
            }

            $message = trans('message.mark_record_reviewed') . $status->status;
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") -" . $message);
            return renderResponse(SUCCESS, $message, null);
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while updating manual review details.", [EXCEPTION => $e]);
            DB::rollback();
            $message = trans('message.db_transaction_failed');
            return renderResponse(FAIL, $message, $e);
        }
    }
    /**
     * Mark record as reviewed
     */

    /**
     * Fetch all v1 manual identity verification details
     */
    public function getAllManualReviewDetailsForV1(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "V1 Manual Review Details search started...");

        // Search with in V1 Manual Identity Review
        $manualReviews = $this->_getV1ManualReviewSearch($request);

        $message = trans('message.manual_review_search_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "V1 Manual Review Details search complete.");
        return renderResponse(SUCCESS, $message, $manualReviews);
    }

    /**
     * _getV1ManualReviewSearch
     * Fetch Manual Identity Review
     * @param  mixed $searchArray
     * @return void
     */
    private function _getV1ManualReviewSearch($request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "V1 Manual Review Details Search Started.");

        $from_date = $request['from_date'];
        $to_date = $request['to_date'] . ' 23:59:59';

        // Fetch Manual Identity Review
        $sql = 'SELECT v1_onboarding_manual_reviews.*, status_master.status as status_name FROM v1_onboarding_manual_reviews JOIN status_master ON v1_onboarding_manual_reviews.status = status_master.id WHERE v1_onboarding_manual_reviews.doc_front_side IS NOT NULL AND v1_onboarding_manual_reviews.doc_back_side IS NOT NULL ';

        $searchStr = [];
        if ($from_date && $to_date) {
            $sql .= " AND v1_onboarding_manual_reviews.created_at BETWEEN ? AND ? ";
            array_push($searchStr, $from_date, $to_date);
        }
        if (trim($request['email'])) {
            $sql .= " AND v1_onboarding_manual_reviews.email = ? ";
            array_push($searchStr, $request['email']);
        }
        if (trim($request['phone_no'])) {
            $sql .= " AND v1_onboarding_manual_reviews.phone = ? ";
            array_push($searchStr, $request['phone_no']);
        }
        if (trim($request['consumer'])) {
            $sql .= ' AND LOWER(
                REPLACE(CONCAT(COALESCE(
                REPLACE(v1_onboarding_manual_reviews.first_name, " ",""), "")," ", COALESCE(
                REPLACE(v1_onboarding_manual_reviews.middle_name, " ",""), "")," ", COALESCE(
                REPLACE(v1_onboarding_manual_reviews.last_name, " ",""), "")),"  "," ")) LIKE ? ';
            array_push($searchStr, '%' . $request['consumer'] . '%');
        }
        $sql .= " ORDER BY v1_onboarding_manual_reviews.created_at DESC LIMIT 100";

        $consumers = DB::connection(MYSQL_RO)->Select($sql, $searchStr);

        $consumerArr = [];
        if (!empty($consumers)) {
            foreach ($consumers as $consumer) {

                $data = [];
                $data['name'] = $consumer->first_name . ' ' . $consumer->middle_name . ' ' . $consumer->last_name;
                $data['edit'] = $consumer->id;
                $data['email'] = $consumer->email;
                $data['first_name'] = $consumer->first_name;
                $data['middle_name'] = $consumer->middle_name;
                $data['last_name'] = $consumer->last_name;
                $data['phone'] = $consumer->phone;
                $data['id_front'] = $consumer->doc_front_side;
                $data['id_back'] = $consumer->doc_back_side;
                $data['created_at'] = date('m-d-Y h:i A', strtotime($consumer->created_at));
                $data['status'] = $consumer->status_name;

                array_push($consumerArr, $data);
            }
        } else {
            $consumerArr = [];
        }

        return $consumerArr;
    }

    public function getIdentityValidationDetailsForV1()
    {
        $data = $this->request->all();
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Getting identity validation details for manual review details with id: " . $data['id']);
        $details = OnboardManualReview::leftJoin('status_master', 'v1_onboarding_manual_reviews.status', '=', 'status_master.id')->leftJoin('users', 'v1_onboarding_manual_reviews.email', '=', 'users.email')->select('v1_onboarding_manual_reviews.*', 'status_master.status as status_name', 'status_master.code as status_code', 'users.street_address', 'users.city', 'users.state', 'users.zipcode', 'users.apt_number')->where('v1_onboarding_manual_reviews.id', $data['id'])->first();
        if ($details->is_duplicate == 1) {
            $details = OnboardManualReview::leftJoin('status_master', 'v1_onboarding_manual_reviews.status', '=', 'status_master.id')->leftJoin('duplicate_users', 'v1_onboarding_manual_reviews.email', '=', 'duplicate_users.email')->select('v1_onboarding_manual_reviews.*', 'status_master.status as status_name', 'status_master.code as status_code', 'duplicate_users.street_address', 'duplicate_users.city', 'duplicate_users.state', 'duplicate_users.zipcode')->where('v1_onboarding_manual_reviews.id', $data['id'])->first();
        }

        $disk = Storage::disk('s3');
        // Praparing a temporary URL to fetch the image from S3. We are using temporary url so that the url gets invalid after defined minutes, so that the user data got protected.
        $front_url = $disk->temporaryUrl($details->doc_front_side, Carbon::now()->addMinutes(intval(config('app.s3_file_expiry_time'))), []);

        $back_url = $disk->temporaryUrl($details->doc_back_side, Carbon::now()->addMinutes(intval(config('app.s3_file_expiry_time'))), []);

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Successfully fetched identity validation details v1");
        $response['details'] = $details;
        $response['image_front'] = $front_url;
        $response['image_back'] = $back_url;
        $response['record_id'] = $details->id;
        $response['address'] = $details->street_address . " " . $details->city . " " . $details->state . " " . $details->zipcode;
        $message = trans('message.identity_details_success');
        return renderResponse(SUCCESS, $message, $response);
    }

    public function updateReviewStatusForV1()
    {
        $data = $this->request->all();
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Updating status of record with id: " . $data['id'] . " as reviewed");
        DB::beginTransaction();
        try {

            $details = OnboardManualReview::where('id', $data['id'])->first();
            $status = StatusMaster::where('code', $data['code'])->first();
            $details->status = $status->id;
            $details->status_updated_by = $data['status_updated_by'];
            $details->save();

            if ($details->is_duplicate == 0) {
                $users = User::where('email', $details->email)->first();
                $users->status = $status->id;
                $users->save();
            } else {
                $duplicateUsers = DuplicateUser::where('email', $details->email)->first();

                $duplicateUsers->status = $status->id;
                $duplicateUsers->save();
            }

            DB::commit();
            $params = DB::table('v1_onboarding_manual_reviews')->where('id', $data['id'])->first();
            if ($data['code'] == ACTIVATED_BY_ADMIN) { //if status is approved send an email to consumer email id
                //get all the details needed to send the email
                $this->emailexecutor->approvedV1ConsumerIdenity((array) $params);
            } else if ($data['code'] == SUSPENDED_BY_ADMIN) {

                $this->emailexecutor->suspendConsumerIdenity((array) $params);
            }
            $message = trans('message.mark_record_reviewed') . $status->status;
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") -" . $message);
            return renderResponse(SUCCESS, $message, null);
        } catch (\Exception $e) {

            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while updating v1 manual review details.", [EXCEPTION => $e]);
            DB::rollback();
            $message = trans('message.db_transaction_failed');
            return renderResponse(FAIL, $message, $e);
        }
    }
    /**
     * This function does the assessment for previous Identity search with SSN
     */

    public function getAssessmentWithSSN()
    {
        //get the params
        $data = $this->request->all();
        $this->validate($this->request, [
            'sessionId' => VALIDATION_REQUIRED,
        ]);
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Starting assessment of earlier search for session: " . $data['sessionId']);
        //getting session details
        $session = RegistrationSession::where('id', $data['sessionId'])
            ->orderby('created_at', 'DESC')
            ->first();

        $params['phoneNo'] = $session->phone;
        $params['firstName'] = $session->first_name;
        $params['middleName'] = $session->middle_name;
        $params['lastName'] = $session->last_name;
        $params['email'] = $session->email;
        $params['dateOfBirth'] = $session->date_of_birth;
        $params['address'] = $session->street_address;
        $params['city'] = $session->city;
        $params['state'] = $session->state;
        $params['zip'] = $session->zipcode;
        $params['ssn'] = base64_decode($session->ssn_number);
        $params['session_id'] = $session->id;

        //get the last stored identity search result
        $result = ValidationLog::where('phone', $params['phoneNo'])
            ->where('api', '/identity_searches')
            ->orderby('created_at', 'DESC')
            ->first();
        //encrypt the ssn returned
        $attributes = [];
        $json_decoded = json_decode($result['response'], true);
        foreach ($json_decoded['included'] as $key => $val) {
            if ($val['type'] == 'ssn') {
                $attributes['number'] = base64_decode($val['attributes']['number']);
                $attributes['group'] = base64_decode($val['attributes']['area']);
                $attributes['area'] = base64_decode($val['attributes']['group']);
                $attributes['serial'] = base64_decode($val['attributes']['serial']);
                $json_decoded['included'][$key]['attributes'] = $attributes;
            }
        }
        //ssn needs to be decrypted
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Previous search result retrieved: ", (array) $result);
        $params['response'] = json_encode($json_decoded, true);
        //get the assessment done for the response returned after creating identity search
        $res = $this->_getAssessment($params);

        $message = trans('message.cognito_success');
        return renderResponse(SUCCESS, $message, null);
    }

    /**
     * Getting identity assessment done for the last Identity search without ssn
     *
     * @return mixed
     */
    private function _getAssessment($params)
    {
        $params['response'] = json_decode($params['response'], true);
        //handle error if any
        if (isset($params['response']['errors'])) {
            //format the error from cognito
            $message = formatCognitoResponse($params['response']['errors'][0]['detail']);
            return renderResponse(FAIL, $message, null);
        }
        //picking out the search id
        $params['search_id'] = $params['response']['data']['id'];
        $params['type'] = (!isset($params['ssn'])) ? ASSESSMENT : ASSESSMENT_WITH_SSN;
        $response = $this->idvalidator->getIdentityAssessment($params);
    }

    /**
     * get risk percentage against consumer for all unique account present for consumer.
     *
     * @return mixed
     */
    private function getRiskTable($user_id)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Getting consumer risk percentage started...");
        // get status
        $bank_active = getStatus(BANK_ACTIVE);
        $bank_inactive = getStatus(BANK_INACTIVE);

        $sql = "WITH risk_percentage_table AS (
            SELECT
                cppdt.account_id,
                cppdt.consumer_id,
                cppdt.account_no,
                cppdt.percentage,
                MAX(cppdt.created_at) OVER (PARTITION BY cppdt.account_id, cppdt.consumer_id) AS max_created_at,
                cppdt.created_at
            FROM
                consumer_purchase_power_decision_table cppdt
            WHERE
            cppdt.consumer_id = ?
        )
        SELECT
            CONCAT('x', RIGHT(rpt.account_no, 4)) AS masked_account_no,
             CASE
                WHEN rpt.percentage IS NULL THEN 'N/A'
                WHEN ROUND(rpt.percentage, 2) = ROUND(rpt.percentage) THEN CAST(ROUND(rpt.percentage) AS VARCHAR(255))
                ELSE CAST(rpt.percentage AS VARCHAR(255))
        END AS latest_percentage
        FROM
            risk_percentage_table rpt
        JOIN
            user_bank_account_info ubai ON ubai.id = rpt.account_id
        WHERE
            rpt.created_at = rpt.max_created_at
            AND ubai.`status` IN (?, ?)
        ";

        $searchArray = [$user_id, $bank_active, $bank_inactive];

        $risk_table = DB::connection(MYSQL_RO)->select($sql, $searchArray);

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Consumer risk percentage fetched successfully against id: " . $user_id);
        return  $risk_table;
    }

    /**
     * get risk score against consumer for all unique account present for consumer.
     *
     * @return mixed
     */
    private function getRiskScore($phone)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Getting consumer risk score started...");

        $sql_risk_score = "WITH combined_results AS (
            SELECT
             CASE
             WHEN ROUND(cppdt.risk_score, 2) = ROUND(cppdt.risk_score)
             THEN CAST(ROUND(cppdt.risk_score) AS VARCHAR(255)) ELSE CAST(cppdt.risk_score AS VARCHAR(255)) END AS risk_score,
             cppdt.created_at
            FROM registration_session_details rsd
            JOIN consumer_purchase_power_decision_table cppdt ON rsd.id = cppdt.consumer_id
            WHERE rsd.phone = ? AND cppdt.risk_score IS NOT NULL
            UNION ALL
            SELECT
             CASE
             WHEN ROUND(cppdt.risk_score, 2) = ROUND(cppdt.risk_score)
             THEN CAST(ROUND(cppdt.risk_score) AS VARCHAR(255)) ELSE CAST(cppdt.risk_score AS VARCHAR(255)) END AS risk_score,
             cppdt.created_at
            FROM users u
            JOIN consumer_purchase_power_decision_table cppdt ON u.user_id = cppdt.consumer_id
            WHERE u.phone = ? AND cppdt.risk_score IS NOT NULL
            )
            SELECT risk_score
            FROM combined_results
            ORDER BY created_at DESC
            LIMIT 1";
        $risk_score = DB::connection(MYSQL_RO)->select($sql_risk_score, [$phone, $phone]);

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Consumer risk score fetched successfully against id: " . $phone);
        if (empty($risk_score)) {
            return false;
        }
        return $risk_score[0]->risk_score;
    }

    /**
     * getConsumerCognitoDetail
     * Get the cognito log details against consumer phone number.
     * @param  mixed $searchArray
     * @return void
     */

    public function getConsumerCognitoDetail()
    {
        $data = $this->request->all();
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Fetching consumer cognito details started...");

        $cognito_rules_log = CognitoRulesLog::where('phone', $data['phone'])->orderby('created_at', 'DESC')->first();

        if ($cognito_rules_log) {

            $log = json_decode($cognito_rules_log->response, true);

            if (!empty($log)) {
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Successfully fetched consumer cognito rule log response having phone number: " . $data["phone"]);

                $log['source_ssn'] = isset($log['source_ssn']) ? base64_decode($log['source_ssn']) : "N/A";
                $log['input_ssn'] = !empty($log['input_ssn']) ? base64_decode($log['input_ssn']) : "N/A";
                $response['risk_table'] =  $this->getRiskTable($data['user_id']);
                $response['log'] = $log;
                $response['cognito_response'] = 'success';
                $message = trans('message.consumer_cognito_response_success');
                return renderResponse(SUCCESS, $message, $response);
            } else {
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Cognito rule log response not found for consumer having phone number: " . $data["phone"]);

                $response['cognito_response'] = 'fail';

                $message = trans('message.consumer_cognito_response_not_found');
                return renderResponse(SUCCESS, $message, $response);
            }
        }
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Cognito rule log not found for consumer having phone number: " . $data["phone"]);

        $response['cognito_response'] = 'fail';
        $message = trans('message.consumer_cognito_not_found');
        return renderResponse(SUCCESS, $message, $response);
    }

    public function getRiskScoreByPhone(Request $request)
    {
        return $this->getRiskScore($request->get('phone'));
    }
}
