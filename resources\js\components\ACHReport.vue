<template>
<div>
  <div v-if="table_loading">
    <CanPayLoader/>
  </div>
  <div v-if="loading">
     <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px;">
    <div class="hold-transition sidebar-mini">
      <div class="container-fluid">
        <section class="content">
          <div class="container-fluid">
            <div class="row">
              <div class="col-12">
                <div class="card card-success">
                    <div class="card-header card-success">
                        <h3 class="card-title">ACH Report</h3>
                    </div>
                    <div class="p-4"> 
                      <div class="row" >
                        <div  :class="achData.length == 2?'col-6':'col-4'" v-for="curr_ach_data in achData" :key="curr_ach_data.transaction_date">
                          <div>
                            <h4>Transaction Details: {{formatTheTransactionDate(curr_ach_data.transaction_date)}}</h4>
                          </div>
                          <div class="scrollable-list mt-3" style="height:150px">
                              <ul class="list-group">
                                  <li  v-for="curr_file in curr_ach_data.file" class="list-group-item cursor-auto" :key="curr_file.id" @click="selectTheTransactionList(curr_file.id)"  :style="{ backgroundColor: selectedItem === curr_file.id ? 'lightblue' : 'white' }">
                                      <code>{{ curr_file.file_name }}</code>
                                  </li>
                              </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                <div>
                    <button type="button" class="btn btn-success ml-4 mb-2" @click="generateTransactionFile()">Generate</button>
                </div>
                <div class="card-body" v-if="this.show_table">
                    <div class="row">
                        <div class="col-12">
                            <b-table
                              show-empty
                              head-variant="light"
                              responsive
                              bordered
                              sticky-header="600px"
                              :fields="fields"
                              :items="report"
                              :style="{ backgroundColor: this.color  }"
                            >
                            </b-table>
                            <b-table-simple
                              show-empty
                              head-variant="light"
                              responsive
                              bordered
                              sticky-header="600px"
                            >
                            <b-tbody>
                              <b-tr>
                                <b-td width="55%">
                                  Represented Transaction
                                </b-td>
                                <b-td>
                                  {{total_represented}}
                                </b-td>
                              </b-tr>
                              <b-tr>
                                <b-td width="55%">
                                  New Transactions
                                </b-td>
                                <b-td>
                                  {{new_transaction_amount}}
                                </b-td>
                              </b-tr>
                              <b-tr>
                                <b-td width="55%">
                                  Total Canpay Offset
                                </b-td>
                                <b-td>
                                  {{canpay_offset}}
                                  <span class="export-api-btn" v-if="canpay_offset!=0"><a href="javascript:void(0);" class="custom-edit-btn" title="Download Return List" @click="downloadAchReturnList();"><i class="nav-icon fa fa-download"></i></a></span>
                                </b-td>
                              </b-tr>
                            </b-tbody>
                            </b-table-simple>
                            <br>
                            <b-table-simple
                              show-empty
                              head-variant="light"
                              responsive
                              bordered
                              sticky-header="600px"
                            >
                            <b-tbody>
                              <b-tr>
                                <b-td width="55%">
                                  Merchant Points Program Debit
                                </b-td>
                                <b-td>
                                  {{total_cashback_debit}}
                                </b-td>
                              </b-tr>
                              <b-tr>
                                <b-td width="55%">
                                  CanPay Points Program Credit
                                </b-td>
                                <b-td>
                                  {{total_cashback_credit}}
                                </b-td>
                              </b-tr>
                            </b-tbody>
                            </b-table-simple>
                            <br>
                            <b-table-simple
                              show-empty
                              head-variant="light"
                              responsive
                              bordered
                              sticky-header="600px"
                            >
                            <b-tbody>
                              <b-tr>
                                <b-td width="55%">
                                  Merchant Wheel Debit
                                </b-td>
                                <b-td>
                                  {{total_merchant_reward_wheel_debit}}
                                </b-td>
                              </b-tr>
                              <b-tr>
                                <b-td width="55%">
                                  CanPay Merchant Wheel Credit
                                </b-td>
                                <b-td>
                                  {{total_merchant_reward_wheel_credit}}
                                </b-td>
                              </b-tr>
                            </b-tbody>
                            </b-table-simple>
                            <br>
                            <b-table-simple
                              show-empty
                              head-variant="light"
                              responsive
                              bordered
                              sticky-header="600px"
                            >
                            <b-tbody>
                              <b-tr>
                                <b-td width="55%">
                                  CannaPlan Debit
                                </b-td>
                                <b-td>
                                  {{total_sponsor_debit}}
                                </b-td>
                              </b-tr>
                              <b-tr>
                                <b-td width="55%">
                                  CanPay CannaPlan Credit
                                </b-td>
                                <b-td>
                                  {{total_sponsor_credit}}
                                </b-td>
                              </b-tr>
                            </b-tbody>
                            </b-table-simple>
                            <br>
                            <b-table-simple
                              show-empty
                              head-variant="light"
                              responsive
                              bordered
                              sticky-header="600px"
                            >
                            <b-tbody>
                              <b-tr>
                                <b-td width="55%">
                                  Total Credit
                                </b-td>
                                <b-td>
                                  {{total_credit}}
                                </b-td>
                              </b-tr>
                              <b-tr>
                                <b-td width="55%">
                                Total Credit Count
                                </b-td>
                                <b-td>
                                  {{total_credit_count}}
                                </b-td>
                              </b-tr>
                              <b-tr>
                                <b-td width="55%">
                                Total Debit
                                </b-td>
                                <b-td>
                                  {{total_debit}}
                                </b-td>
                              </b-tr>
                              <b-tr>
                                <b-td width="55%">
                                Total Debit Count
                                </b-td>
                                <b-td>
                                  {{total_debit_count}}
                                </b-td>
                              </b-tr>
                            </b-tbody>
                            </b-table-simple>

                        </div>
                    </div>
                </div>
                </div>
              </div>
            </div>

          </div>
        </section>
      </div>
    </div>
  </div>
</div>
</template>
<script>
import api from "@/api/ACHTransaction.js";
import { HourGlass } from "vue-loading-spinner";
import moment from "moment";
import CanPayLoader from "./CustomLoader/CanPayLoader.vue"
export default {
    components:{
      HourGlass,
      CanPayLoader
    },
    data(){
        return {
            selectedItem:null,
            achData:[],
            fields: [
                {"key":"head" ,"class":"text-left"},
                {"key":"credit" ,"class":"text-center"},
                {"key":"debit" ,"class":"text-center"},
            ],
            report:[],
            ach_report:[],
            total_represented: "",
            new_transaction_amount:"",
            represented_transaction_amount:"",
            canpay_offset:"",
            total_debit_count:"",
            total_debit:"",
            total_credit_count:"",
            total_credit:"",
            total_cashback_credit:"",
            total_cashback_debit:"",
            total_merchant_reward_wheel_credit:"",
            total_merchant_reward_wheel_debit:"",
            total_sponsor_credit:"",
            total_sponsor_debit:"",
            show_table:false,
            loading:true,
            table_loading:false,
            color:'white'
        }
    },
    mounted(){
      var self = this;
      self.fetchTheAchFile();
      self.color = 'white';
    },
    methods:{
        selectTheTransactionList(index){
            var self = this;
            if(self.selectedItem == index){
                self.selectedItem = null;
                return;
            }
            self.selectedItem = index;

            return;
        },
        downloadAchReturnList(){
          const request = {
            "ach_file_id":this.selectedItem
          }
          api.
          exportAchTransactionReturnList(request)
          .then((response)=>{
              var FileSaver = require("file-saver");
              var blob = new Blob([response], {
                type: "application/xlsx",
              });
              FileSaver.saveAs(
                blob,
                moment().format("MM/DD/YYYY") + "_canpay_ach_transaction_export.xlsx"
              );
          })
          .catch((error)=>{
            error(error.response.data.message)
          })
        },
        formatTheTransactionDate(date){
          const date_list = date.split('-');
          const required_date =  date_list[1]+'-'+date_list[2]+'-'+date_list[0];
          return required_date;
        },
        fetchTheAchFile(){
          api
          .getACHTransactionData({})
          .then((response) => {
            if(response.code == 200){
              this.achData = response.data;
            }else{
              error(response.message)
            }
            this.loading = false;
          })
          .catch((err)=>{
            this.loading =false;
            error(err.response.data.message)
          })
        },
        selectTitleForHeading(head,consumer_count = 0){
          if(head === "total_consumer_transaction_value"){
            return `Total Consumer Transaction (${consumer_count})`;
          }
          if(head === "total_reward_amount"){
            return "Total Reward Amount Used";
          }
          if(head === "total_canpay_fee_merchant_debit"){
            return "CanPay Fee(Merchant Debit)"
          }
          if(head === "total_merchant_transaction"){
            return "Total Merchant Transaction";
          }
          if(head === "total_canpay_fee"){
            return "CanPay Fee";
          }
        },
        setTheTransactionTable(transaction_array){
          var self = this;
          for(const curr_val of transaction_array){
            const keys = Object.keys(curr_val);
            for(const curr_key of keys){
            if(curr_key === "total_consumer_transaction_count"){
              continue;
            }
            if(curr_key === "total_consumer_transaction_value" || curr_key == "total_reward_amount" || curr_key == "total_canpay_fee_merchant_debit"){                   
              self.report.push({"head":self.selectTitleForHeading(curr_key,curr_val["total_consumer_transaction_count"]),"debit":curr_val[curr_key],"credit":"0.00"});
            }else{
              self.report.push({"head":self.selectTitleForHeading(curr_key,curr_val["total_consumer_transaction_count"]),"credit":curr_val[curr_key],"debit":"0.00"});
            }
            }
          }
        },
        setTheDebitCreditData(debit_credit_count_array){
          var self = this;
          self.total_credit = debit_credit_count_array["total_credit"];
          self.total_credit_count = debit_credit_count_array["credit_count"];
          self.total_debit = debit_credit_count_array["total_debit"];
          self.total_debit_count = debit_credit_count_array["debit_count"];
          self.color = self.total_credit === self.total_debit?'#e9f5db':'#fff0f3';
          if(self.total_credit === null && self.total_debit === null){
            self.color = 'white';
          }
        },
        setTransactionDetails(amountDetails){
          var self = this;
          self.new_transaction_amount = amountDetails["new_transaction_value"];
          self.total_represented = amountDetails["total_represented_value"];
          self.canpay_offset = amountDetails["canpay_offset"];
        },
        setTransactionOtherDetails(amountDetails){
          var self = this;
          self.total_cashback_credit = amountDetails["total_cashback_credit"];
          self.total_cashback_debit = amountDetails["total_cashback_debit"];
          self.total_merchant_reward_wheel_credit = amountDetails["total_merchant_reward_wheel_credit"];
          self.total_merchant_reward_wheel_debit = amountDetails["total_merchant_reward_wheel_debit"];
          self.total_sponsor_credit = amountDetails["total_sponsor_credit"];
          self.total_sponsor_debit = amountDetails["total_sponsor_debit"];
        },
        generateTransactionFile(){
            var self = this;
            if(self.report.length>0){
              self.report = [];
            }
            if(self.selectedItem != null){
                self.show_table = true;
            }
            if(self.selectedItem == null || self.selectedItem === ''){
              return;
            }
            self.color = 'white';
            self.table_loading = true;
            self.report = [];
            api.getACHReportForId({ach_file_id:self.selectedItem})
            .then((response)=>{
              if(response.code == 200){
                self.setTheTransactionTable(response.data.transaction_report_array);
                self.setTheDebitCreditData(response.data.debit_credit_count_array[0]);
                self.setTransactionDetails(response.data.return_transaction_array[0]);
                self.setTransactionOtherDetails(response.data.other_report_array[0]);
                self.table_loading = false;
              }else{
                error(response.message)
              }
            }).catch((err)=>{
              error(err.response.data.message);
              self.loading = false;
            })
            return;
        }
    }
}
</script>
<style scoped>
.scrollable-list {
  max-height: 150px;
  overflow-y: scroll;
  border: 1px solid #ccc;
}
</style>