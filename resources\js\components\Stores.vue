<template>
<div>
  <div v-if="loading">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Stores</h3>
                </div>
                <div class="loading" v-if="loading">
                  <hour-glass></hour-glass>
                </div>
                <!-- /.card-header -->
                <div class="card-body">
                  <div class="row">
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="form-control"
                        placeholder="Merchant ID (Exact)"
                        id="merchant_id"
                        v-model="merchant_id"
                      />
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="form-control"
                        placeholder="Store ID (Exact)"
                        id="store_id"
                        v-model="store_id"
                      />
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                     <input
                        class="form-control"
                        placeholder="Retailer Name"
                        id="retailer"
                        v-model="retailer"
                      />
                    </div>
                  </div>
                </div>
                </div>
                  <div class="card-footer">
                    <button
                      type="button"
                      class="btn btn-success"
                      @click="searchStores()"
                    >
                      Search
                    </button>
                    <button
                      type="button"
                      @click="reset()"
                      class="btn btn-success margin-left-5"
                    >
                      Reset
                    </button>
                  </div>
                  <div class="card-body">
                  <b-table-simple
                      responsive
                      show-empty
                      bordered
                      sticky-header="800px"
                      v-if="allStoreModel.length > 0"
                    >
                      <b-thead head-variant="light">
                        <tr>
                            <th width="9%">Merchant ID</th>
                            <th width="9%">Store ID</th>
                            <th width="9%">Retailer</th>
                            <th width="9%">Transaction Type</th>
                            <th width="9%">Timezone</th>
                            <th width="9%">Corporate Parent</th>
                            <th width="9%">Created On</th>
                            <th width="10%">Enable Ecommerce Admin Driven</th>
                            <th width="10%">Enable As Sponsor</th>
                            <th width="9%">Action</th>
                            <th width="9%">Hide From Map</th>
                        </tr>
                      </b-thead>
                      <b-tbody v-for="(row, index) in allStoreModel" :key="index">
                        <b-tr>
                          <b-td class="text-left text-gray">{{
                            row.merchantID
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.store_id
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.retailer
                          }}</b-td>
                          <b-td class="text-center text-gray">
                            {{row.store_type}} <a :data-user-id="row.id" class="editTransactionType custom-edit-btn" title="Edit Transaction type" variant="outline-success"><i class="nav-icon fas fa-edit"></i></a>
                          </b-td>
                          <b-td class="text-center text-gray">{{
                            row.timezone_name
                          }}</b-td>
                          <b-td class="text-center text-gray">{{
                            row.cp_name
                          }}</b-td>
                          <b-td class="text-center text-gray">{{
                            row.created_at
                          }}</b-td>
                          <b-td class="text-center text-gray">
                              <label class="switch"><input class="enable-ecommerce-admin-driven" :disabled="row.is_ecommerce == 0" :checked="row.check_ecommerce_admin_driven" type="checkbox" :data-id="row.id"><span :class="row.is_ecommerce == 1 ? 'slider round' : 'slider round disabled'"></span></label>
                          </b-td>
                          <b-td class="text-center text-gray">
                              <label class="switch"><input class="enable-sponsor" :disabled="row.has_cp == 1" :checked="row.is_sponsor == 1" type="checkbox" :data-id="row.id"><span :class="row.is_sponsor == 1 ? 'slider round' : 'slider round disabled'"></span></label>
                          </b-td>
                          <b-td class="text-center text-gray">
                            <a :data-user-id="row.id" class="editEmailDailyTransaction custom-edit-btn" title="Add Email Address for Daily Transaction Email" variant="outline-success" style="border:none"><i class="nav-icon fas fa-edit"></i></a>
                            <a :data-user-id="row.id" class="storepage-viewStoreDetail custom-edit-btn" title="View Assigned Stores" variant="outline-success" style="border:none"><i class="nav-icon fas fa-eye"></i></a>
                          </b-td>
                          <b-td class="text-center text-gray">
                              <div class="custom-control custom-checkbox mr-sm-2"><input  class="custom-control-input hide-from-map" :id="'customControlAutosizing' + row.id" type="checkbox" :checked="row.map_viewable"  :data-id="row.id"><label class="custom-control-label" :for="'customControlAutosizing' + row.id"></label></div>
                          </b-td>
                        </b-tr>
                      </b-tbody>
                    </b-table-simple>
                    <p v-else>No data displayed. Please refine your search criteria.</p>
                    </div>
              </div>
            </div>
          </div>
      </div>
    </section>


    <!-- Store Daily transaction Email Modal Start -->
    <b-modal
      id="sm-modal"
      ref="modal"
      :header-text-variant="headerTextVariant"
      :title="modalTitle"
      @hidden="resetModal"
      ok-title="Save"
      ok-variant="success"
      cancel-variant="outline-secondary"
      @ok="handleOk"
      :no-close-on-esc="true"
      :no-close-on-backdrop="true"
    >
      <form ref="form" @submit.stop.prevent="save" class="needs-validation">
        <div class="row">
          <div class="col-md-12">
            <div class="form-group">
              <label for="exampleInputEmail1">
                Email addresses for Daily Transaction Activity (Emails separated
                by Comma)
                <span class="red">*</span>
              </label>
              <input
                type="email"
                v-model="storeDetails.cp_emails"
                placeholder="Enter email addresses for Daily Transaction Activity (Emails separated by Comma)"
                class="form-control"
                autocomplete="off"
              />
            </div>
          </div>
        </div>
      </form>
    </b-modal>
    <!-- Store Daily transaction Email Modal End -->

    <!-- View Store Modal Start -->
    <b-modal
      id="view-store-modal"
      ref="view-store-modal"
      :header-text-variant="headerTextVariant"
      title="Store Details"
      hide-footer
    >
      <div class="row odd-row">
        <div class="col-md-4 row-value">
          <label for="name">Store ID</label>
        </div>
        <div class="col-md-1 row-value">:</div>
        <div class="col-md-7 row-value">
          <span for="name">{{ store.store_id }}</span>
        </div>
      </div>
      <div class="row even-row">
        <div class="col-md-4 row-value">
          <label for="access_rights">Retailer</label>
        </div>
        <div class="col-md-1 row-value">:</div>
        <div class="col-md-7 row-value">
          <span for="name">{{ store.retailer }}</span>
        </div>
      </div>
      <div class="row odd-row">
        <div class="col-md-4 row-value">
          <label for="access_other_org_info">Address</label>
        </div>
        <div class="col-md-1 row-value">:</div>
        <div class="col-md-7 row-value">
          <span for="name">{{
            store.address +
              ", " +
              store.city +
              ", " +
              store.state +
              ", " +
              store.county +
              ", " +
              store.zip
          }}</span>
        </div>
      </div>
      <div class="row even-row">
        <div class="col-md-4 row-value">
          <label for="access_rights">Contact No.</label>
        </div>
        <div class="col-md-1 row-value">:</div>
        <div class="col-md-7 row-value">
          <span for="name">{{store.contact_no}}</span>
        </div>
      </div>
      <div class="row odd-row">
        <div class="col-md-4 row-value">
          <label for="access_rights">Email</label>
        </div>
        <div class="col-md-1 row-value">:</div>
        <div class="col-md-7 row-value">
          <span for="name">{{store.email}}</span>
        </div>
      </div>

       <div class="row even-row">
        <div class="col-md-4 row-value">
          <label for="access_rights">Website</label>
        </div>
        <div class="col-md-1 row-value">:</div>
        <div class="col-md-7 row-value">
          <span for="name">{{store.website_address}}</span>
        </div>
      </div>
    </b-modal>
    <!-- View Store Modal End -->
    <!-- Edit Store transaction type Modal Start -->
     <b-modal
      id="edit-store-transaction-type"
      ref="modal"
      :header-text-variant="headerTextVariant"
      title="Edit Store Transaction Type"
      @hidden="resetTransactionTypeModal"
      ok-title="Save"
      ok-variant="success"
      cancel-variant="outline-secondary"
      @ok="handleTransactionTypeOk"
      :no-close-on-esc="true"
      :no-close-on-backdrop="true"
    >
   <div class="row odd-row">
        <div class="col-md-4 row-value">
          <label for="name">Store ID</label>
        </div>
        <div class="col-md-1 row-value">:</div>
        <div class="col-md-7 row-value">
          <span for="name">{{ store.store_id }}</span>
        </div>
      </div>
      <div class="row even-row">
        <div class="col-md-4 row-value">
          <label for="access_rights">Retailer</label>
        </div>
        <div class="col-md-1 row-value">:</div>
        <div class="col-md-7 row-value">
          <span for="name">{{ store.retailer }}</span>
        </div>
      </div>

      <form ref="form" @submit.stop.prevent="saveTransactionType" class="needs-validation">


        <div class="row">
          <div class="col-md-12">
            <label for="store_id">
              Transaction Type
            </label>
            <multiselect
              id="transaction_type_id"
              name="transaction_type_id"
              v-model="transaction_type_ids"
              :options="transaction_type"
              :multiple="true"
              :custom-label="customLabel"
              track-by="type"
              placeholder="Select Transaction type"
              selectLabel
              deselectLabel
              :close-on-select="false"
              :clear-on-select="false"
            ></multiselect>
          </div>
        </div>
      </form>
    </b-modal>
    <!-- Edit Store Modal End -->
   </div>
</div>
</template>
<script>
import CanPayLoader from "./CustomLoader/CanPayLoader.vue"
import api from "@/api/stores.js";
import commonConstants from "@/common/constant.js";
export default {
    components:{
      CanPayLoader
    },
  data() {
    return {
      modalTitle: "",
      storeDetails: {},
      allStoreModel: {},
      headerTextVariant: "light",
      currentUser: localStorage.getItem("user")
        ? JSON.parse(localStorage.getItem("user"))
        : null,
      showReloadBtn: false,
      constants: commonConstants,
      store: {},
      transaction_type_ids: null,
      transaction_type : [],
      loading:false,
      merchant_id:"",
      store_id:"",
      retailer:"",
      loading: false
    };
  },
  created() {
    this.editEmailDailyTransaction();
    this.toggleEcommerceAdminDriven();
    this.hideFromMap();
    this.viewStoreDetail();
    this.editMerchentStoreTransactionType();
    this.toggleMerchantAsSponsor();
  },
  methods: {
    toggleEcommerceAdminDriven() {
      var self = this;
      $(document).on("click", ".enable-ecommerce-admin-driven", function(e) {
        var request = {
          id: $(e.currentTarget).attr("data-id")
        };
        self.loading = true;
        api
          .toggleEcommerceAdminDriven(request)
          .then(response => {
            if ((response.code = 200)) {
              success(response.message);
              self.searchStores();
            } else {
              error(response.message);
            }
            self.loading = false;
          })
          .catch(err => {
            error(err.response.data.message);
            self.loading = false;
          });
      });
    },
    toggleMerchantAsSponsor() {
      var self = this;
      $(document).on("click", ".enable-sponsor", function(e) {
        var request = {
          id: $(e.currentTarget).attr("data-id")
        };
        self.loading = true;
        api
          .toggleStoreAsSponsor(request)
          .then(response => {
            if ((response.code = 200)) {
              success(response.message);
              self.searchStores();
            } else {
              error(response.message);
            }
            self.loading = false;
          })
          .catch(err => {
            error(err.response.data.message);
            self.loading = false;
          });
      });
    },

    hideFromMap() {
      var self = this;

      $(document).on("click", ".hide-from-map", function(e) {
        var request = {
          id: $(e.currentTarget).attr("data-id")
        };
        self.loading = true;
        api
          .storeHideFromMap(request)
          .then(response => {
            if ((response.code = 200)) {
              success(response.message);
              self.searchStores();
            } else {
              error(response.message);
            }
            self.loading = false;
          })
          .catch(err => {
            error(err);
            self.loading = false;
          });
      });
    },
    searchStores(){
      var self = this;
      if($("#retailer").val().trim() === '' && $("#merchant_id").val().trim() === '' &&  $("#store_id").val().trim() === ''){
        error("Please provide Retailer Name or Merchant ID(exact) or Store ID(exact)");
        return false;
      }
      var request = {
        retailer: self.retailer,
        merchant_id:self.merchant_id,
        store_id:self.store_id,
      };
      self.loading = true;
      api
      .searchStores(request)
      .then(function (response) {
        if (response.code == 200) {
          self.allStoreModel = response.data;
          self.loading = false;
        } else {
          error(response.message);
          self.loading = false;
        }
      })
      .catch(function (error) {
        // error(error);
        self.loading = false;
      });
    },
    checkValidation() {
      let self = this;
      var error_flag = 0;
      if (
        self.storeDetails.cp_emails != "" &&
        self.storeDetails.cp_emails != null
      ) {
        var cp_emails = self.storeDetails.cp_emails.split(",");
        $.each(cp_emails, function(index, value) {
          if (/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value.trim()) == false) {
            error_flag = 1;
            error("Please enter a valid Email Address.");
            return false;
          }
        });
      } else {
        error("The Email Address Field Is Required.");
        return false;
      }

      if (error_flag == 0) {
        return true;
      }
    },
    resetModal() {
      var self = this;
      self.storeDetails = {};
    },
    resetTransactionTypeModal() {
      var self = this;
      self.store= {};
      self.transaction_type_ids= null;
      self.transaction_type = [];
    },
    handleOk(bvModalEvt) {
      var self = this;
      // Prevent modal from closing
      bvModalEvt.preventDefault();
      // Trigger submit handler
      self.save();
    },
      handleTransactionTypeOk(bvModalEvt) {
      var self = this;
      // Prevent modal from closing
      bvModalEvt.preventDefault();
      // Trigger submit handler
      self.saveTransactionType();
    },
    save() {
      var self = this;
      if (self.checkValidation()) {
        self.loading = true;
        api
          .addStoreDailyTransactionEmail(self.storeDetails)
          .then(response => {
            if (response.code == 200) {
              success(response.message);
              $("#storesTable")
                .DataTable()
                .ajax.reload(null, false);
              self.$bvModal.hide("sm-modal");
              self.resetModal();
            } else {
              error(response.message);
            }
            self.loading = false;
          })
          .catch(err => {
            error(err.response.data.message);
            self.loading = false;
          });
      }
    },
    editEmailDailyTransaction() {
      var self = this;
      $(document).on("click", ".editEmailDailyTransaction", function(e) {
        self.storeDetails = self.allStoreModel.find(
          p => p.id == $(e.currentTarget).attr("data-user-id")
        );
        self.modalTitle = "Add Email Address for Daily Transaction Email";
        self.$bvModal.show("sm-modal");
      });
    },
    // view store details
    viewStoreDetail() {
      var self = this;
      $(document).on("click", ".storepage-viewStoreDetail", function(e) {
        var id = $(e.currentTarget).attr("data-user-id");
        var request = {
          id: id
        };
        self.loading = true;
        api
          .getStoreDetails(request)
          .then(response => {
            if (response.code == 200) {
              self.store = response.data;
              self.$bvModal.show("view-store-modal");
            } else {
              error(response.message);
            }
            self.loading = false;
          })
          .catch(err => {
            error(err.response.data.message);
            self.loading = false;
          });
      });
    },
    //Edit store details

    editMerchentStoreTransactionType() {
      var self = this;
      $(document).on("click", ".editTransactionType", function(e) {
           var marchentStoreId = $(e.currentTarget).attr("data-user-id");
        var request = {
          marchentStoreId: marchentStoreId
        };
        self.loading = true;
         api
          .getMerchentStoreTransactionType(request)
          .then(response => {
            if (response.code == 200) {
              self.transaction_type = response.data.transactionType;
              self.transaction_type_ids = response.data.merchantStoreTrasactionTypes;
              self.store = response.data.merchantDetails;
              self.$bvModal.show("edit-store-transaction-type");
              self.searchStores();
            } else {
              error(response.message);
            }
            self.loading = false;
          })
          .catch(err => {
            error(err.response.data.message);
          });
      });
    },

    customLabel(transaction_type) {
      return `${transaction_type.type}`;
    },

    saveTransactionType()
    {
    var self = this;
        if(self.transaction_type_ids.length==0)
        {
            error("Please enter Transaction Type");
            return false;
        }
    var request = {
        store_id: self.store.id,
        transaction_type_ids: self.transaction_type_ids,
    };
    self.loading = false;
        api
        .updateMerchantStoreTransactionType(request)
        .then(response => {
            if (response.code == 200) {
            success(response.message);
            $("#storesTable")
            .DataTable()
            .ajax.reload(null, false);
            self.$bvModal.hide("edit-store-transaction-type");
            self.resetTransactionTypeModal();
            self.loading = false;
        } else {
            error(response.message);
            self.loading = true;
        }
        })
        .catch(err => {
        error(err.response.data.message);
        self.loading = false;
        });
    },
    reset(){
      var self = this;
      self.retailer = "";
      self.merchant_id = "";
      self.store_id = "";
    }
  },
  mounted() {
    document.title = "CanPay - Stores";
  },
  beforeDestroy(){
    $(document).off('click', '.enable-ecommerce-admin-driven');
    $(document).off('click', '.hide-from-map');
    $(document).off('click', '.editEmailDailyTransaction');
    $(document).off('click', '.storepage-viewStoreDetail');
    $(document).off('click', '.editTransactionType');
  },
};
</script>

<style>
.disabled {
    background-color: #e9ecef;
}
</style>
