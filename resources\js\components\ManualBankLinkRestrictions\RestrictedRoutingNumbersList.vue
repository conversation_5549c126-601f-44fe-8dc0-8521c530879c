<template>
  <div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <b-button
                variant="outline-success"
                style="margin-top: -48px"
                @click="openModal('add')"
              >
                <i class="fas fa-user-plus"></i> Add New
              </b-button>
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Manual Bank Link Restricted Routing Numbers</h3>
                  <b-button
                    class="btn-danger export-api-btn"
                    @click="reloadDatatable"
                    v-if="showReloadBtn"
                  >
                    <i class="fas fa-redo"></i> Reload
                  </b-button>
                </div>
                <!-- /.card-header -->
                <div class="card-body">
                  <table
                    id="manualBankLinkRestrictionsTable"
                    class="table"
                    style="width: 100%; white-space: normal"
                  >
                    <thead>
                      <tr>
                        <th>Routing Number</th>
                        <th>State</th>
                        <th>Restriction Year</th>
                        <th>Added On</th>
                        <th>Enable</th>
                        <th>Action</th>
                      </tr>
                    </thead>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
    
    <!-- Consumer Modal Edit Start -->
    <b-modal
        id="routing-number-modal"
        ref="routing-number-modal"
        :header-text-variant="headerTextVariant"
        :title="routingNoTitle"
        @hidden="resetModal"
        ok-title="Save"
        ok-variant="success"
        cancel-variant="outline-secondary"
        @ok="save"
        :no-close-on-esc="true"
        :no-close-on-backdrop="true"
      >
      <form ref="form" @submit.stop.prevent="save" class="needs-validation">
        <div class="row">
          <div class="col-md-6">
            <label for="user_type">
              Routing Number
              <span class="red">*</span>
            </label>
            <input
              class="form-control"
              placeholder="Routing Number"
              id="routing_number"
              name="routing_number"
              autocomplete="off"
              v-model="routing_number"
              v-validate="'required|numeric|max:9'"
            />
            <span v-show="errors.has('routing_number')" class="text-danger">{{
              errors.first("routing_number")
            }}</span>
          </div>

          <div class="col-md-6">
            <label for="user_type">
              Restriction Year
              <span class="red">*</span>
            </label>
            <input
              class="form-control"
              placeholder="Restriction Year"
              id="year"
              name="year"
              autocomplete="off"
              v-model="restriction_year"
              v-validate="'required|numeric|max:4'"
            />
            <span v-show="errors.has('year')" class="text-danger">{{
              errors.first("year")
            }}</span>
          </div>
          <div class="col-md-12">
            <label for="user_type">
              States
              <span class="red">*</span>
            </label>
            <multiselect
                id="routing_state"
                name="routing_state"
                v-model="selectedState"
                :options="stateList"
                :multiple="true"
                track-by="id"
                :custom-label="customLabel"
                placeholder="Select States"
                selectLabel
                deselectLabel
                :close-on-select="false"
                :clear-on-select="false"
                v-validate="'required'"
                @remove="removeState"
              ></multiselect>
            <span v-show="errors.has('routing_state')" class="text-danger">The states field is required</span>
          </div>
          <div class="col-md-12">
            <input type="checkbox" id="checkbox1" @click='selectAllState()' v-model="isAllSelected" />
            <label for="checkbox1">Select All States</label>
          </div>
          
        </div>
        <div class="row" v-if="showMsg">
          <div class="col-12">
            <label for="comment" class="red">Please fill in the required fields.</label>
          </div>
        </div>
      </form>
    </b-modal>
    <!-- Consumer Modal Edit End -->
    <!-- View All Bank Matched Users modal start -->
    <b-modal
      id="all-states-modal"
      ref="all-states-modal"
      :header-text-variant="headerTextVariant"
      title="States"
      :no-close-on-esc="true"
      :no-close-on-backdrop="true"
      ok-variant="success"
      hide-footer ok-only
    >
    <div class="card-body">
      <ul>
          <li v-for="(state, index) in selectedState" :key="index">{{ state.name }} - {{ state.code }}</li>
      </ul>
    </div>
    </b-modal>
    <!-- View All Bank Matched Users modal end -->
  </div>
</template>
<script>
import api from "@/api/manualbank.js";
import commonConstants from "@/common/constant.js";
export default {
  data() {
    return {
      showMsg: false,
      headerTextVariant: "light",
      allRoutingNumbers: {},
      routing_number: "",
      showReloadBtn: false,
      constants: commonConstants,
      selectedState: [],
      stateList: [],
      ViewroutingNumberID: "",
      routingNoTitle: "Add Manual Bank Link Restricted Routing Numbers",
      restriction_year: "",
      isAllSelected: false,
    };
  },
  created() {
    this.trLeaveComment();
  },
  methods: {
    selectAllState() {
      if (this.isAllSelected) {
        this.selectedState = [];
      }else{
        this.selectedState = this.stateList;
      }
    },
    removeState() {
      this.isAllSelected = false;
    },
    customLabel(state) {
      return `${state.name} - ${state.code}`;
    },
    reloadDatatable() {
      var self = this;
      self.loadDT();
    },
    openModal(type) {
      var self = this;
      self.resetModal();
      self.routingNoTitle = "Add Manual Bank Link Restricted Routing Numbers";
      self.$bvModal.show("routing-number-modal");
    },
    resetModal() {
      var self = this;
      self.showMsg = false;
      self.ViewroutingNumberID = "";
      self.routing_number = "";
      self.restriction_year = "";
      self.selectedState = [];
      self.isAllSelected = false;
    },
    save(bvModalEvt) {
      var self = this;
      const restrictionYearString = self.restriction_year.toString(); 
      var numberRegex  = /^[0-9\s]+$/;
      if (self.routing_number == "" || self.restriction_year == "" || self.selectedState.length == 0) {
        self.showMsg = true;
        bvModalEvt.preventDefault();
      }else if (isNaN(self.routing_number)|| self.routing_number.length != 9 || (!numberRegex.test(self.routing_number))) {
        error("Invalid routing number (must be 9 digits numeric )");
        bvModalEvt.preventDefault();
      }else if (isNaN(self.restriction_year)|| restrictionYearString.length != 4 || (!numberRegex.test(self.restriction_year))) {
        error("Invalid restriction year (must be 4 digits numeric )");
        bvModalEvt.preventDefault();
      } else {

        var request = {
          edit: self.ViewroutingNumberID,
          routing_number: self.routing_number,
          restriction_year: self.restriction_year,
          selectedState: self.selectedState.map(item => item.id),
        };
        self.showMsg = false;
        bvModalEvt.preventDefault();
        api
          .addEditRoutingNumber(request)
          .then((response) => {
            if (response.code == 200) {
              success(response.message);
              self.loadDT();
              self.$bvModal.hide("routing-number-modal");
              self.resetModal();
            } else {
              error(response.message);
            }
          })
          .catch((err) => {
            error(err.response.data.message);
          });
      }
    },
    loadDT: function () {
      var self = this;
      $("#manualBankLinkRestrictionsTable").DataTable({
        pagingType: "simple_numbers",
        processing: true,
        serverSide: true,
        destroy: true,
        columnDefs: [
          { orderable: false, targets: [1,4,5] },
          { className: "dt-center", targets: [0, 1, 2, 3, 4, 5] }
        ],
        order: [[3, "desc"]],
        orderClasses: false,
        language: {
          processing:
            '<i class="fa fa-spinner fa-spin fa-3x fa-fw"></i><span class="sr-only">Loading...</span> ',
          emptyTable: "No Routing Number Available.",
          search: "_INPUT_",
          searchPlaceholder: "Search By Routing No",
          oPaginate: {
            sNext: '<i class="fas fa-angle-double-right"></i>',
            sPrevious: '<i class="fas fa-angle-double-left"></i>',
          },
          sLengthMenu:
            "<label class='label_dropdown_dt'>Per page</label> _MENU_",
        },
        ajax: {
          headers: {
            Authorization: "Bearer " + localStorage.getItem("token"),
          },
          url: "/api/getmanualbanklinkrestrictedroutingnumbers",
          type: "POST",
          data: { _token: "{{csrf_token()}}" },
          dataType: "json",
          dataSrc: function (result) {
            self.showReloadBtn = false;
            self.allRoutingNumbers = result.data;
            return self.allRoutingNumbers;
          },
          error: function (data) {
            error(commonConstants.datatable_error);
            $("#manualBankLinkRestrictionsTable_processing").hide();
            self.showReloadBtn = true;
          },
        },
        columns: [
          { data: "routing_no" },
          {
            render: function (data, type, full, meta) {
              var state_length = full.state.length;
              if (state_length > 0) {
                var state_str = full.state[0];
                if (state_length > 1) {
                  state_str =state_str + ', ' +full.state[state_length - 1];
                }
                if (state_length > 2) {
                  state_str =state_str + ', ' +full.state[state_length - 2];
                }
                if (state_length > 3) {
                  state_str =state_str + ', ...' + (
                    ' <b-button data-id="' + full.edit +'" class="viewAllState custom-edit-btn" title="View States" variant="outline-success">More</b-button>'
                  );
                }
                return state_str;
                
              } else {
                return '';
              }
            },
          },
          { data: "restriction_year" },
          { data: "created_at" },
          {
            render: function (data, type, full, meta) {
              var checked = 1;
              return (
                    '<label class="switch"><input class="statusChangeRouting allow-merchant-fees-report" checked="' + checked +'" type="checkbox" data-id="' + full.edit +'"><span class="slider round"></span></label>'
                  );
            },
          },         
          {
            render: function (data, type, full, meta) {
              return (
                    ' <b-button data-id="' + full.edit +'" class="editRouting custom-edit-btn" title="Edit" variant="outline-success"><i class="nav-icon fas fa-edit"></i></b-button>'
                  );
            },
          },         
        ],
      });

      $("#manualBankLinkRestrictionsTable").on("page.dt", function () {
        $("html, body").animate({ scrollTop: 0 }, "slow");
        $("th:first-child").focus();
      });

      //Search in the table only after 3 characters are typed
      // Call datatables, and return the API to the variable for use in our code
      // Binds datatables to all elements with a class of datatable
      var dtable = $("#manualBankLinkRestrictionsTable").dataTable().api();

      // Grab the datatables input box and alter how it is bound to events
      $(".dataTables_filter input")
        .unbind() // Unbind previous default bindings
        .bind("input", function (e) {
          // Bind our desired behavior
          // If the length is 3 or more characters, or the user pressed ENTER, search
          if (this.value.length >= 3 || e.keyCode == 13) {
            // Call the API search function
            dtable.search(this.value).draw();
          }
          // Ensure we clear the search if they backspace far enough
          if (this.value == "") {
            dtable.search("").draw();
          }
          return;
        });
    },
    getAllState() {
      var self = this;
      api
        .getAllState()
        .then((response) => {
          if ((response.code = 200)) {
            self.stateList = response.data;
          } else {
            error(response.message);
          }
        })
        .catch((err) => {
          error(err);
        });
    },
    statusChangeRoutingNumber() {
      var self = this;
      var request = {
        edit: self.ViewroutingNumberID,
      };
      api
        .statusChangeRoutingNumber(request)
        .then((response) => {
          if ((response.code = 200)) {
            success(response.message);
          } else {
            error(response.message);
          }
        })
        .catch((err) => {
          error(err);
        });
    },
    trLeaveComment() {
      var self = this;
      // All State
      $(document).on("click", ".viewAllState", function (e) {
        //open the modal
        self.ViewroutingNumberID = $(e.currentTarget).attr("data-id");
        var vieWRouting = self.allRoutingNumbers.find(
            p => p.edit == self.ViewroutingNumberID
        );
        self.selectedState = self.stateList.filter(item => vieWRouting.state_ids.includes(item.id));
        self.$bvModal.show("all-states-modal");
      });
      // Status change
      $(document).on("click", ".statusChangeRouting", function (e) {
        //open the modal
        self.ViewroutingNumberID = $(e.currentTarget).attr("data-id");
        var vieWRouting = self.allRoutingNumbers.find(
            p => p.edit == self.ViewroutingNumberID
        );
        self.selectedState = self.stateList.filter(item => vieWRouting.state_ids.includes(item.id));
        self.statusChangeRoutingNumber();
      });
      // Edit
      $(document).on("click", ".editRouting", function (e) {
        //open the modal
        self.ViewroutingNumberID = $(e.currentTarget).attr("data-id");
        var vieWRouting = self.allRoutingNumbers.find(
          p => p.edit == self.ViewroutingNumberID
        );

        self.selectedState = self.stateList.filter(item => vieWRouting.state_ids.includes(item.id));
        self.state_ids = vieWRouting.state_ids;
        self.routing_number = vieWRouting.routing_no;
        self.restriction_year = vieWRouting.restriction_year;
        self.isAllSelected = self.stateList.length == self.selectedState.length ? true : false;
        self.routingNoTitle = "Edit Manual Bank Link Restricted Routing Numbers";
        self.$bvModal.show("routing-number-modal");
      });
    },
  },
  mounted() {
    var self = this; 
    self.getAllState();
    setTimeout(function () {
      self.loadDT();
    }, 1000);
    document.title = "CanPay - Manual Bank Link Restrictions";
  },
  beforeDestroy(){
    $(document).off('click', '.viewAllState');
    $(document).off('click', '.statusChangeRouting');
    $(document).off('click', '.editRouting');
  },
};
</script>

