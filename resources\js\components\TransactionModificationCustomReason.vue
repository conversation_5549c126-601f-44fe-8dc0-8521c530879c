<template>
<div>
    <div v-if="loading">
      <CanPayLoader/>
    </div>
    <div class="content-wrapper" style="min-height: 36px">
      <section class="content-header">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Transaction Modification Custom Reason</h3>
                </div>
                <!-- /.card-header -->
                <div class="card-body">
                  <div class="row">
                  <div class="col-md-4">
                    <div class="form-group">
                     <input
                        class="form-control"
                        placeholder="Reason (min 3 chars)"
                        id="reason"
                        v-model="reason"
                      />
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                        <label class="switch"><input class="enable-employee-login" type="checkbox" @click="showAllReasons" v-model="showall"><span class="slider round"></span></label> Show All
                    </div>
                  </div>
                </div>
                </div>
                  <div class="card-footer">
                    <button
                      type="button"
                      class="btn btn-success"
                      @click="searchTransactionModCustomReason()"
                    >
                      Search
                    </button>
                    <button
                      type="button"
                      @click="reset()"
                      class="btn btn-success margin-left-5"
                    >
                      Reset
                    </button>

                  </div>
                  <div class="card-body">
                  <b-table-simple
                      responsive
                      show-empty
                      bordered
                      sticky-header="800px"
                      v-if="allReasonModel.length > 0"
                    >
                      <b-thead head-variant="light">
                        <tr>
                          <th>Reason</th>
                            <th>Added By</th>
                            <th>Created On</th>
                            <th>Updated On</th>
                            <th class="text-center">Action</th>
                        </tr>
                      </b-thead>
                      <b-tbody v-for="(row, index) in allReasonModel" :key="index">
                        <b-tr>
                          <b-td class="text-left text-gray">{{
                            row.reason
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.added_by
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.created_at
                          }}</b-td>
                          <b-td class="text-center text-gray">{{
                            row.updated_at
                          }}</b-td>
                          <b-td class="text-center text-gray">
                            <a :data-reason-id="row.edit" class="deleteTransactionModCustomReason custom-edit-btn" title="Delete Transaction Modification Custom Reason" variant="outline-success" style="border:none"><i class="nav-icon fas fa-trash"></i></a>
                          </b-td>
                        </b-tr>
                      </b-tbody>
                    </b-table-simple>
                    <p v-else>No data displayed. Please refine your search criteria.</p>
                    </div>
              </div>
            </div>
          </div>
        </div>
      </section>




    </div>
</div>
</template>
<script>
import api from "@/api/transaction.js";
import { validationMixin } from "vuelidate";
import { required, minLength } from "vuelidate/lib/validators";
import commonConstants from "@/common/constant.js";
import CanPayLoader from "./CustomLoader/CanPayLoader.vue"
export default {
  mixins: [validationMixin],
  components:{
    CanPayLoader
  },
  data() {
    return {
      showall: false,
      modalTitle: "",
      headerTextVariant: "light",
      transactionModificationReasonDetails: {},
      allReasonModel: {},
      value: [],
      showReloadBtn: false,
      constants: commonConstants,
      checkboxActive: true,
      checkboxActiveVoidMenu: false,
      loading:false,
      reason:"",
    };
  },
  created() {
    this.deleteTransactionModCustomReason();
  },
  methods: {

    showAllReasons(){
        var self = this;
        if(self.showall == true){
            self.showall = false;
        } else{
            self.showall = true;
            self.reason = '';
        }
    },
    deleteTransactionModCustomReason(){
        var self = this;
      $(document).on("click", ".deleteTransactionModCustomReason", function (e) {

        var deleteId = $(e.currentTarget).attr("data-reason-id");
        self.deleteTransactionModCustomReasonApi(deleteId);
      });
    },
    deleteTransactionModCustomReasonApi(id){
        var self = this;
        var request = {
            id: id,
        };
        var r = confirm(
            "Do you want to delete this reason?"
        );
        if (r == true) {
            api
            .deletetTransactionModCustomReason(request)
            .then((response) => {
            if (response.code == 200) {
                success(response.message);
                $("#corporateParentsTable").DataTable().ajax.reload(null, false);
                self.searchTransactionModCustomReason();
            } else {
                error(response.message);
            }
            })
            .catch((err) => {
              error(err.response.data.message);
            });
        }
    },
    searchTransactionModCustomReason(){
      var self = this;
      if((self.reason).trim().length < 3 && self.showall == false){
        error("Please provide reason (Min 3 chars)");
        return false;
      }
      var request = {
        reason: self.reason,
        showall: self.showall,
      };
      self.loading = true;
      api
      .searchTransactionModCustomReason(request)
      .then(function (response) {
        if (response.code == 200) {
          self.allReasonModel = response.data;
          self.loading = false;
        } else {
          error(response.message);
          self.loading = false;
        }
      })
      .catch(function (error) {
        // error(error);
        self.loading = false;
      });
    },
    reset(){
      var self = this;
      self.reason = "";
      self.showall = false;
    }
  },
  mounted() {
    var self = this;
    document.title = "CanPay - Transaction Modification Custom Reason";
  },
};
</script>

