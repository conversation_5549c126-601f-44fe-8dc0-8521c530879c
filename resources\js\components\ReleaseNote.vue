<template>
  <div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Release Note</h3>
                </div>
                <div class="card-body">
                  <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                            <input
                                class="form-control"
                                placeholder="Tag (Exact)"
                                id="tag"
                                v-model="search_tag"
                            />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <button
                      type="button"
                      class="btn btn-success"
                      @click="searchReleaseNotes()"
                    >
                      Search
                    </button>
                    <button
                      type="button"
                      @click="reset()"
                      class="btn btn-success margin-left-5"
                    >
                      Reset
                    </button>
                    <b-button
                        class="btn btn-success margin-left-5"
                        @click="openModal('add')"
                    >
                        <i class="fas fa-user-plus"></i> Add New
                    </b-button>
                  </div>
                <!-- /.card-header -->
                <div class="card-body">
                  <b-table-simple
                      responsive
                      show-empty
                      bordered
                      sticky-header="800px"
                      v-if="allReleaseNote.length > 0"
                    >
                      <b-thead head-variant="light">
                        <tr>
                            <th>Tag</th>
                            <th>Release Note</th>
                            <th class="text-center">Release Note Description</th>
                        </tr>
                      </b-thead>
                      <b-tbody v-for="(row, index) in allReleaseNote" :key="index">
                        <b-tr>
                          <b-td class="text-left text-gray">{{
                            row.tag
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.release_note
                          }}</b-td>
                          <b-td class="text-center text-gray">{{
                            row.release_note_description
                          }}</b-td>
                        </b-tr>
                      </b-tbody>
                    </b-table-simple>
                    <p v-else>No data displayed. Please refine your search criteria.</p>
                    </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
    <!-- Add routing numbers modal start -->
    <b-modal
      id="release-noter-modal"
      ref="release-noter-modal"
      ok-title="Add"
      cancel-title="Close"
      ok-variant="success"
      @ok="save"
      cancel-variant="outline-secondary"
      hide-header
      :no-close-on-esc="true"
      :no-close-on-backdrop="true"
    >
      <div class="row">
        <div class="col-12">
          <label for="tag">
            Enter Tag
            <span class="red">*</span>
          </label>
          <input
            name="tag"
            type="text"
            v-model="tag"
            class="form-control"
          />
        </div>
      </div>
      <div class="row">
        <div class="col-12">
          <label for="release_note">
            Enter release note
            <span class="red">*</span>
          </label>
          <input
            name="release_note"

            type="text"
            v-model="release_note"
            class="form-control"
          />
        </div>
      </div>
            <div class="row">
        <div class="col-12">
          <label for="release_note_description">
            Enter release note description
          </label>
          <input
            name="release_note_description"

            type="text"
            v-model="release_note_description"
            class="form-control"
          />
        </div>
      </div>
      <div class="row" v-if="showMsg">
        <div class="col-12">
          <label for="tag" class="red"
            >Please fill in the required fields.</label
          >
        </div>
      </div>
      <div class="row" style="margin-bottom: 40px"></div>
    </b-modal>
    <!-- Transaction cancellation comment modal end -->
  </div>
</template>
<script>
import api from "@/api/releasenote.js";
import commonConstants from "@/common/constant.js";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "./CustomLoader/CanPayLoader.vue"
export default {
  data() {
    return {
      showMsg: false,
      headerTextVariant: "light",
      allReleaseNote: {},
      tag: "",
      release_note: "",
      release_note_description: "",
      showReloadBtn: false,
      constants: commonConstants,
      loading:false,
      search_tag:""
    };
  },
  components: {
    HourGlass,
    CanPayLoader
  },
  methods: {
    searchReleaseNotes(){
      var self = this;
      if((self.search_tag).trim().length < 3){
        error("Please provide Tag (Exact)");
        return false;
      }
      var request = {
        tag: self.search_tag,
      };
      self.loading = true;
      api
      .searchReleaseNotes(request)
      .then(function (response) {
        if (response.code == 200) {
          self.allReleaseNote = response.data;
          self.loading = false;
        } else {
          error(response.message);
          self.loading = false;
        }
      })
      .catch(function (error) {
        // error(error);
        self.loading = false;
      });
    },
    openModal(type) {
      var self = this;
      self.resetModal();
      self.$bvModal.show("release-noter-modal");
    },
    resetModal() {
      var self = this;
      self.tag = "";
      self.release_note = "";
      self.release_note_description = "";
    },
    save(bvModalEvt) {
      var self = this;
      var numberRegex  = /^[0-9\s]+$/;
      var request = {
      tag : self.tag,
      release_note :self.release_note,
      release_note_description :self.release_note_description
      };
      if (self.tag == "" || self.release_note == "") {
        self.showMsg = true;
        bvModalEvt.preventDefault();
      } else {
        self.showMsg = false;
        bvModalEvt.preventDefault();
        api
          .addReleaseNote(request)
          .then((response) => {
            if (response.code == 200) {
              success(response.message);
              self.$bvModal.hide("release-noter-modal");
              self.resetModal();
            } else {
              error(response.message);
            }
          })
          .catch((err) => {
            error(err.response.data.message);
          });
      }
    },
    reset(){
      var self = this;
      self.search_tag = "";
    }
  },
  mounted() {
    document.title = "CanPay - Release Note";
  },
};
</script>

