<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use MrShan0\PHPFirestore\FirestoreClient;


class UpdateFirestoreField extends Command
{
    protected $signature = 'firestore:update {documentPath} {fieldName} {newValue}';

    protected $description = 'Update a field in Google Cloud Firestore';

    public function handle()
    {
        // Get arguments from command line
        $documentPath = $this->argument('documentPath');
        $fieldName = $this->argument('fieldName');
        $newValue = $this->argument('newValue');

        // Initialize Firestore client
        $firestoreClient = new FirestoreClient(config('app.firebase_project_id'), config('app.firebase_api_key'), [
            'database' => '(default)',
        ]);

        // Update the document
        $firestoreClient->updateDocument($documentPath, [
            $fieldName => $newValue,
        ]);

        $this->info("Field '{$fieldName}' in document '{$documentPath}' updated to '{$newValue}'");
    }
}
