<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
class GlobalRadarReview extends Model
{
    /**
     * __construct
     * This function will insert an alphanumeric 32 character unique id as primary key in the table whenever a data get inserted
     * @param  mixed $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        $this->id = generateUUID();

        parent::__construct($attributes);
    }

    protected $fillable = [
        'session_id',
        'user_id',
        'response_returned',
        'status',
        'description',
    ];
    public $timestamps = true;
    public $incrementing = false;
}
