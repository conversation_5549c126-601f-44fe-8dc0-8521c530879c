// require('./bootstrap');

window.Vue = require('vue');
import { BootstrapVue, BootstrapVueIcons } from 'bootstrap-vue';
// Install BootstrapVue
Vue.use(BootstrapVue);
Vue.use(BootstrapVueIcons);
// router setup
import routes from './router';

import VueRouter from 'vue-router';
Vue.use(VueRouter);

window.axios = require('axios');

import App from './App.vue';

// import 'bootstrap';
Vue.config.devtools = true;
import 'sweetalert2/src/sweetalert2.scss';

import { loadProgressBar } from 'axios-progress-bar';
loadProgressBar();
import 'axios-progress-bar/dist/nprogress.css'
import 'bootstrap/dist/css/bootstrap.css';
import 'bootstrap-vue/dist/bootstrap-vue.css';
import Multiselect from 'vue-multiselect';
Vue.component('multiselect', Multiselect);
import 'vue-multiselect/dist/vue-multiselect.min.css'

import vSelect from 'vue-select';
Vue.component('v-select', vSelect);
import 'vue-select/dist/vue-select.css';

import VeeValidate from "vee-validate";
Vue.use(VeeValidate, { fieldsBagName: 'veeFields' });

import VueSweetalert2 from 'vue-sweetalert2';
import 'sweetalert2/dist/sweetalert2.min.css';
Vue.use(VueSweetalert2);
import VueClipboard from 'vue-clipboard2';
VueClipboard.config.autoSetContainer = true;
Vue.use(VueClipboard);

import * as VueGoogleMaps from "vue2-google-maps";
Vue.use(VueGoogleMaps, {
    load: {
        key: process.env.MIX_GOOGLE_PLACES_API_KEY,
        libraries: 'places', //// If you need to use place input
    }
});

import VueCountdownTimer from 'vuejs-countdown-timer';
Vue.use(VueCountdownTimer);
import { Validator } from 'vee-validate';
const dictionary = {
    en: {
        attributes: {
            first_name: 'First Name',
            last_name: 'Last Name',
            email_address: 'Email',
            email: 'Email Address',
            username: 'Username',
            phone: 'Phone No.',
            contact_person: 'Conatct Person',
            store_id: 'Store',
            user_type: 'User Type',
            user_status: 'Status',
            contact_person_first_name: 'Primary Contact First Name',
            contact_person_last_name: 'Primary Contact Last Name',
            contact_person_email: 'Primary Contact Email',
            contact_person_phone: 'Primary Contact Phone',
            name: 'Name',
            return_reason: 'Return Reason',
            weekly_spending_limit: 'Weekly Spending Limit',
            purchase_power: 'Purchase Power',
            website_address: 'Website Address',
            title: 'Title',
            description: 'Description',
            description_for_consumer: 'Description for Consumer',
            time_frame: 'Time Frame',
            sandbox_webhook_url: 'Sandbox Webhook URL',
            live_webhook_url: 'Live Webhook URL',
            location_type: 'Location Type',
            ecommerce_integrator_name: 'Ecommerce Integrator Name',
            category_name: 'Category Name',
            category_code: 'CanPay Internal Version',
            nomenclature: 'Nomenclature',
            daily_spending_limit: 'Daily Spending Limit',
            consumer_weekly_spending_limit: 'Weekly Spending Limit',
            merchant_profile_name: 'Merchant Profile Name',
            reward_wheel: 'Reward Wheel',
            no_of_spins: 'No. of Spins'
        }
    }
};

Validator.localize(dictionary);
axios.defaults.headers.common["Authorization"] =
    "Bearer " + localStorage.getItem("token");
const router = new VueRouter({ mode: 'history', routes: routes });

// Global ajax error handler
$(document).ajaxError(function myErrorHandler(event, xhr, ajaxOptions, thrownError) {
    if (xhr.status === 401) { // Checking Unauth error
        clearLocalStorage() // Clearing the Local Storage
    }
});

// Global axios error handler
axios.interceptors.response.use((response) => {
    return response;
}, (error) => {
    const originalRequest = error.config;
    if (error.response.data.code == 401) { // Checking Unauth error
        clearLocalStorage() // Clearing the Local Storage
    }
    return Promise.reject(error);
});

// Function to Clear the Local Storage
function clearLocalStorage() {
    localStorage.removeItem("token");
    localStorage.removeItem("user");
    localStorage.clear();
    document.title = "CanPay Admin";
    router.push("/login");
}

router.beforeEach((to, from, next) => {
    if (localStorage.getItem("token") == null) {
        document.title = "CanPay Admin";
    }
    if (localStorage.getItem("token") == null && to.fullPath !== '/login') {
        return next({ path: "/login" });
    } else {
        return next();
    }
})

new Vue(Vue.util.extend({ router }, App)).$mount('#app');