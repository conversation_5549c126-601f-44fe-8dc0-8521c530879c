<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

//manage version

define('VERSION', 'v2.1.0');

// General Constants
define('LINE', 'Line');
define('EXCEPTION', 'Exception');
define('DELIVERY_PARTNER', 'delivery_partner');

// Date Formats
define('DATETIME_FORMAT', 'm-d-Y H:i:s');
define('DATE_FORMAT', 'm-d-Y');
define('DB_DATE_FORMAT', 'Y-m-d H:i:s');

//API status code
define('ACTIVE_CODE', 204);
define('SUCCESS', 200);
define('FAIL', 599);
define('ACCOUNT_HAS_REMAINING_BALANCE', 513);
define('UNAUTH', 401);
define('VALIDATION', 403);
define('METHOD_NOT_FOUND', 405);
define('TRANSACTION_CANCELED', 509);
define('PENDING', 202);
define('UNPAID', '208');
define('EXPIRED', '710');
define('ARCHIVED', 600);
define('APPROVED', 601);

define('INACTIVED_DUE_TO_SUSPECTED_FRAUD', 207);

//transaction modification new status
define('INITIAL', 1200);
define('AWAITINGCONSUMERAPPROVAL', 1201);
define('DECLINED', 1203);
define('AUTOAPPROVED', 1204);
define('QUEUED_FOR_PROCESSING', 1205);
define('REQUEST_TIMEOUT', 1206);
define('APPROVED_BY_CONSUMER', 1207);
define('SETTLED', 'Settled');
define('AWAITING_CONSUMER_APPROVAL', 1201);

// User/bank acoount Status
define('USER_ACTIVE', 701);
define('USER_EXPIRED', 702);
define('USER_PENDING', 703);
define('USER_REJECTED', 704);
define('USER_BLOCKED', 705);
define('ACTIVATED_BY_ADMIN', 706);
define('SUSPENDED_BY_ADMIN', 707);
define('SUSPECTED_FRAUD', 723);
define('USER_INACTIVE', 205);
define('RESTRICTED_USER', 903);
define('REJECTED', 724);

define('BANK_ACTIVE', 801);
define('BANK_INACTIVE', 802);
define('BANK_DELINK', 803);
define('BANK_DISABLE', 804);

// Transaction Place
define('POS', 'POS');
define('ACHECK21', 'Acheck21');

//Fee details
define('FEE_TYPE_RETAIL', 'Retail/POS');
define('FEE_TYPE_ECOMMERCE', 'Ecommerce/Web');
define('PERCENTAGE', 'Percentage');
define('AMOUNT', 'Amount');

// Transaction API calll type
define('CONSUMER_CREATE_TRANSACTION', 'Consumer Create Transaction');
define('MERCHANT_CREATE_FEE_TRANSACTION', 'Merchant Create Fee Transaction');
define('MERCHANT_CREATE_DEPOSIT_TRANSACTION', 'Merchant Create Deposit Transaction');
define('CANPAY_CREATE_TRANSACTION', 'Canpay Create Transaction');
define('CANCEL_TRANSACTION', 'Cancel Transaction');
define('VALIDATE_TRANSACTION', 'Validate Transaction');

// Transaction Status
define('PROCESSED_FOR_ACHECK21', '203');
define('FAILED', '500');
define('VOIDED', '510');
define('FAILED_INSUFFICIENT_BANK_BALANCE', '501');
define('FAILED_WEEKLY_LIMIT_EXHAUSTED', '502');
define('FAILED_DAILY_LIMIT_EXHAUSTED', '503');
define('FAILED_INSUFFICIENT_PURCHASE_POWER', '504');
define('FAILED_MAX_SINGLE_TRANSACTION_LIMIT_EXCEEDED', '505');
define('REGISTRATION_STOPPED', '506');
define('FAILED_DUE_TO_INVALID_PIN', '507');
define('FAILED_DUE_TO_USED_PIN', '508');
define('ACHECK21_AUTHORIZED', 'Authorized');
define('PROCESSED_TO_BANK', '206');
define('RETURNED', 511);
define('APPROVED_BY_ADMIN', 1001);
define('RETAKE', 1003);
define('VOID_REVOKED', '518');
define('FORGIVEN', '901');

define('PENDING_STORE_APPROVAL', "Pending Store's Approval");
define('VOIDED_STATUS_NAME', "Voided");
define('PENDING_STATUS_NAME', "Pending");

//User Roles
define('CORPORATE_PARENT', 'Corporate Parent');
define('SUPERADMIN', 'Super Admin');
define('ADMIN', 'Admin');
define('REPORT_ADMIN', 'Report Admin');
define('ACCOUNTANT', 'Accountant');
define('HELPDESK', 'Help Desk');
define('REGIONAL_MANAGER', 'Regional Manager');
define('STORE_MANAGER', 'Store Manager');
define('CONSUMER', 'Consumer');
define('MERCHANT', 'Merchant');
define('SPONSOR', 'sponsor');
define('BRAND_ADMIN', 'Brand Admin');

// Validation REGEX
define('AMOUNT_REGEX', '/^\d+(\.\d{1,2})?$/');
define('PHONE_NUMBER_REGEX', '/^[0-9]{10,10}$/');
define('VALIDATION_REQUIRED', 'required');
define('VALIDATION_EMAIL', 'email');
define('VALIDATION_STRING', 'string');
define('VALIDATION_REQUIRED_WITHOUT_ALL', 'required_without_all');
define('VALIDATION_REQUIRED_WITH_ALL', 'required_with');
define('SSN_REGEX', '/^[0-9]{4,4}$/');

// Email Template Status
define('TEMPLATE_ACTIVE', 'Active');
define('TEMPLATE_INACTIVE', 'Inactive');

// Email Related Constants
define('FROM_MAIL_NAME', 'CanPay');
define('FROM_MAIL_INFO', '<EMAIL>');
define('CONSUMER_FROM_MAIL_INFO', '<EMAIL>');
define('CANPAY_CEO_FROM_MAIL_INFO', '<EMAIL>');

// Email Channels
define('DEFAULT_CHANNEL', 'sendgrid');
define('SECONDARY_CHANNEL', 'aws-ses');

// Email Template Names
define('CORPORATE_PARENT_WELCOME_EMAIL', 'Corporate Parent Welcome Email');
define('ADMIN_USERS_WELCOME_EMAIL', 'Admin Users Welcome Email');
define('CONSUMER_IDENTITY_VALIDATION_SUCCESS', 'Identity Validation Success');
define('CONSECUTIVE_FAILED_LOGIN', 'Consecutive Failed Login');
define('ADMIN_PASSWORD_RESET', 'Admin Password Reset Request');
define('GLOBAL_RADAR_REVIEW_EMAIL', 'Global Radar Review Email');
define('CONSUMER_TRANSACTION_ACTIVITY', 'Consumer Transaction Activity');
define('CONSUMER_MONTHLY_TRANSACTION_ACTIVITY', 'Consumer Monthly Transaction Activity');
define('CORPORATE_PARENT_DAILY_TRANSACTION_ACTIVITY', 'Corporate Parent Daily Transaction Activity');
define('CORPORATE_PARENT_EMAIL_CHANGE', 'Corporate Parent Email Change');
define('CONSUMER_IDENTITY_VALIDATION_SUSPENDEND', 'Identity Validation Suspended/Blocked');
define('CONSUMER_RETAKE_DOCUMENT_UPLOAD_EMAIL', 'Consumer Retake Document Upload Email');
define('CONSUMER_TRANSACTION_RETURN', 'Consumer Transaction Return');
define('UNKNOWN_RETURN_REASON_RECEIVED', 'Unknown Return Reason Received');
define('FIFTH_THIRD_USER_DETECTED', 'Fifth Third User Detected');
define('SUSPECTED_DIRECT_LINK_CONSUMER_DETECTED', 'Suspected Direct Link Consumer Detected');
define('FRAUD_ALERT', 'Fraud Alert');
define('TRANSACTION_POSTING_FAILURE', 'Transaction Posting Failure');
define('CONSUMER_VOID_TRANSACTION', 'Consumer Void Transaction');
define('CONSUMER_TRANSACTION_ACTIVITY_WITH_REWARDS', 'Consumer Transaction Activity with Rewards');
define('CONSUMER_TRANSACTION_ACCEPT_PAYMENT_ACTIVITY', 'Consumer Transaction Accept Payment');
define('CONSUMER_TRANSACTION_UPDATE_ACTIVITY', 'Consumer Transaction Updated Alert');

//Access Level Label Constants
define('ONLY_VIEW_RIGHT', 'Only View Right');
define('VIEW_AND_USE_RIGHT', 'View and Use Right');
define('ONLY_USE_RIGHT', 'Only Use Right');
define('ADMIN_RIGHT', 'Admin Right');
define('ADD_RIGHT', 'Add Right - Access From Menu');
define('VIEW_AND_ADD_RIGHT', 'View and Add Right - Access From Menu');
define('VIEW_ADD_EDIT_RIGHT', 'View, Add and Edit Right - Access From Menu');
define('CRUD_RIGHT', 'Full CRUD Right - Access From Menu');

//integrator transaction type
define('TERMINAL_WEB', 'WEB');
define('TERMINAL_POS', 'POS');
define('TERMINAL_BOTH', 'BOTH');

// Default device manager email
define('DEFAULT_DEVICE_MANAGER_EMAIL', '<EMAIL>');

// Define Status Active and Inactive
define('ACTIVE', 'Active');
define('INACTIVE', 'Inactive');
define('GENERAL_EXCEPTION', 'General Exception.');
define('TRANSACTION_DECLINED', 'Transaction Declined');

// Define Debit and Credit
define('DEBIT', 'Dr');
define('CREDIT', 'Cr');
// Define Status Active and Inactive
define('TERMINAL_ACTIVE', 'Active');
define('TERMINAL_INACTIVE', 'Inactive');

// HelpDesk Email Template Names
define('HELPDESK_WELCOME_EMAIL', 'HelpDesk  Welcome Email');
define('HELPDESK_LOGIN_PASSWORD', 'HelpDesk Login Password');

// DB Connection names
define('MYSQL_RO', 'mysql_ro');
define('MYSQL_RO2', 'mysql_ro2');
define('MYSQL', 'mysql');
define('MYSQL_REWARD_WHEEL', 'mysql_reward_wheel');

// Transaction API Call Type
define('CONSUMER_CREATE_RETURN_TRANSACTION', 'Consumer Create Return Transaction');
define('CANPAY_CREATE_RETURN_TRANSACTION', 'Canpay Create Return Transaction');

// Define purchase power or spending limit
define('PURCHASE_POWER', 'Purchase Power');
define('SPENDING_LIMIT', 'Spending Limit');

define('FINICITY_AUTHENTICATION', 'authentication');
define('FINICITY_CREATE_CUSTOMER', 'customer');
define('FINICITY_CREATE_CONSUMER', 'consumer');
define('FINICITY_GENERATE_URL', 'connect url');
define('FINICITY_GENERATE_FIX_URL', 'connect fix url');
define('FINICITY_GET_ACCOUNTS', 'get accounts');

//Finicity Error code 20001
define('REAL_ACCOUNT_NUMBER_NOT_FOUND', '20001');
// Finicity Error code 20000
define('ROUTING_NO_NOT_FOUND', '20000');
// Finicity Error code 102
define('INTERNAL_SERVER_ERROR', '102');
define('FOR_HOLIDAYS_FALLING_ON_SUNDAY_FEDERAL_RESERVE_BANKS_AND_BRANCHES_WILL_BE_CLOSED_THE_FOLLOWING_MONDAY', 'For holidays falling on Sunday, Federal Reserve Banks and Branches will be closed the following Monday');

// Timezone Constant
define('PST', 'PST');

// Constants defined for different sources of balance fetch
define('SCHEDULED_BALANCE_FETCH', 'scheduled_balance_fetch');
define('CONSUMER_LOGIN', 'consumer_login');
define('OTHERS', 'others');
define('ADMIN_BALANCE_FETCH', 'admin_balance_fetch');
define('ADMIN_REFRESH_BALANCE', 'admin_refresh_balance');
define('SCHEDULED_REFRESH_BALANCE', 'scheduled_refresh_balance');
define('ONE_TIME_BALANCE_FETCH', 'one_time_balance_fetch');

//Constant defiend for cognito
define('CREATE_PROFILE', 'profiles');
define('SEARCH_PHONE', 'phone');
define('SEARCH_DOB', 'DOB');
define('SEARCH', 'search');
define('SEARCH_SSN', 'ssn');
define("SEARCH_NAME_PHONE", "name_phone");
define('ASSESSMENT', 'assessment');
define('ASSESSMENT_WITH_SSN', 'assessment_with_ssn');
define('ASSESSMENT_EXISTING_USER', 'assessment_existing_user');
define('API_PROFILE', 'post /profiles');
define('API_IDENTITY_SEARCH', 'post /identity_searches');
define('API_IDENTITY_ASSESSMENT', 'post /identity_assessments');
define('COGNITO_NAME_SCORE', 70);
define('COGNITO_PHONE_SCORE', 100);
define('COGNITO_DOB_SCORE', 100);
define('COGNITO_SSN_SCORE', 100);
//Cognito Validation Status
define('APPROVE', 'Approved');
define('MANUAL', 'Manual Review');
define('APPROVED_WITH_DOUBTS', 'Approved with doubts');

define('FI_ID_ERROR', 'Error:1002');

// Constants defined for phone and address and status update ENUM values
define('V1_ONBOARDING', 'v1 Onboarding');
define('V2_REGISTRATION', 'v2 Registration');
define('PHONE_NUMBER_UPDATE', 'Phone number Update');
define('ADDRESS_UPDATED_BY_CONSUMER', 'Address Updated By Consumer');
define('ADDRESS_UPDATED_BY_ADMIN', 'Address Updated By Admin');
define('STATUS_UPDATED_BY_ADMIN', 'Status Updated By Admin');
define('STATUS_UPDATED_BY_CONSUMER', 'Status Updated By Consumer');
define('SUSPENDED_STATUS_UPDATE_DUE_TO_MICROBILT', 'Suspended status update due to microbilt');

// Define Consuemr State
define('ILLINIOS', 'IL');
define('INDIANA', 'IN');
define('PENNSYLVANIA', 'PA');
define('DELAWARE', 'DE');

// Define Notification Type
define('EMAIL', 'email');
define('SMS', 'sms');

// Reward Point
define('TRANSACTION_VOIDED', 'transaction voided');
define('TRANSACTION_VOID_REVOKED', 'transaction void revoked');

// Transaction Type
define('NEW_RETURN', 'new_return');
define('RETURN_REPRESENTMENT', 'return_representment');
define('CONSUMER_TRANSACTION', 'consumer_transaction');
define('MERCHANT_DEBIT', 'merchant_debit');
define('MERCHANT_CREDIT', 'merchant_credit');
define('CANPAY_DEBIT', 'canpay_debit');
define('CANPAY_CREDIT', 'canpay_credit');
define('REWARD_WHEEL_DEBIT', 'reward_wheel_debit');
define('REWARD_WHEEL_CREDIT', 'reward_wheel_credit');
define('CASHBACK_DEBIT', 'cashback_debit');
define('CASHBACK_CREDIT', 'cashback_credit');
define('RETURN_REPRESENTED', 'return_represented');
define('CONSUMER_RETURN', 'consumer_return');
define('CANPAY_RETURN_OFFSET_CREDIT', 'canpay_return_offset');

// bank link type
define('DIRECT_BANK_LINK', 1);
define('MANUAL_BANK_LINK', 0);
define('DIRECT_LINK', 'Direct Link');
define('MANUAL_LINK', 'Manual Link');
define('BANK_TYPE_ACH', 'ach');
define('BANK_TYPE_MERCHANT_POINTS_PROGRAM', 'merchant_points_program');
// Microbilt outcome code
define('MICROBILT_OUTCOME_ONE', 1);
define('MICROBILT_OUTCOME_TWO', 2);
define('MICROBILT_OUTCOME_THREE', 3);
define('MICROBILT_OUTCOME_FOUR', 4);

define('MICROBILT_BANK_REVIEW', 'Microbilt bank review by admin');

// Banking Solution
define('AKOYA', 'akoya');
define('FINICITY', 'finicity');
define('MX', 'mx');

// Bank Account Type
define('CHECKING', 'checking');
define('SAVINGS', 'savings');
define('MX_CONNECTED', 'CONNECTED');

// Purchase Power Source
define('ALGO', 'algo');
define('OLD_RULE', 'old_rule');
define('NEW_RULE', 'new_rule');
define('REVERSED_TO_OLD_RULE', 'reversed_to_old_rule');
define('CUSTOM_PURCHASE_POWER', 'custom_purchase_power');

// setting name
define('ENABLE_NEW_PP_ALGO', 'enable_new_pp_algo');
define('ENABLE_PURCHASE_POWER_LOWER_LIMIT', 'enable_purchase_power_lower_limit');

//microbilt
define('ACCOUNT_STRUCTURE', 'ACCOUNT STRUCTURE');


// Reasons
define('REWARD_POINT', 'reward point');
define('TRANSACTION', 'transaction');
define('REWARD_AMOUNT_REPRESENTMENT', 'reward amount representment');
define('TRANSACTION_MODIFICATION', 'transaction modification');
define('CASHBACK_POINTS', 'cashback points');
define('PETITION_POINTS', 'petition points');

// Timezone Constant
define('ACHECK21_TIMEZONE_FOR_WEBHOOK', 'EST');
define('VENDOR', 'vendor');
define('BRAND', 'brand');

// Consumer type
define('LITE_CONSUMER', 'lite');
define('STANDARD_CONSUMER', 'standard');

//Crew Role
define('MAYOR', 'mayor');
define('CREW_LEADER', 'crew leader');
define('CREW_MEMBER', 'crew member');

// Petition Status
define('AWAITING_ADMIN_APPROVAL', 1004);
define('PROVISIONED', 217);
define('SPONSOR_POINTS', 'sponsor points');
define('BRAND_POINT', 'brand points');
define('PETITION_REASON_REFERRAL_SIGNUP_REWARD', 'referral sign-up reward');
define('PETITION_REASON_PROMOTED_TO_MAYOR', 'promoted to mayor');
define('PETITION_REASON_PROMOTED_TO_CREW_LEADER', 'promoted to crew leader');
define('PETITION_TIP', 'Petition tip');
define('CONSUMER_TIP_INVALID_THROUGH_PETITION', 'Tip invalidated due to signer withdrawal');
define('CONSUMER_TIP_INVALID_THROUGH_PETITION_WITHDRAWN', 'Tip received invalidated due to petition withdrawal');
define('LAUNCHED_THE_PETITION_AND_BECOME_A_MAYOR', 'Launched the Petition and became the Mayor');
define('SIGNED_THE_PETITION', 'Signed the Petition');
define('MERCHANT_STORE_ONBOARDING_INTRODUCTORY_EMAIL', 'Merchant Store Onboarding Introductory Email');
define('YOUR_PETITION_HAS_BEEN_REJECTED', 'Petition Rejected - Notification');
