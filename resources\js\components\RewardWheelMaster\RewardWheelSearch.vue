<template>
<div>
    <div v-if="modalLoading">
        <CanPayLoader/>
    </div>
    <div v-if="loading">
      <CanPayLoader/>
    </div>
    <div class="content-wrapper" style="min-height: 36px">
      <section class="content-header">
        <div class="container-fluid">
          <div class="row">
              <div class="col-12">
                <div class="card card-success">
                  <div class="card-header">
                    <h3 class="card-title">Stores & Reward Wheel Search</h3>
                  </div>
                  <!-- /.card-header -->
                  <div class="card-body">
                    <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <multiselect
                                id="corporateparent"
                                name="corporateparent"
                                v-model="selectedCorporateParents"
                                placeholder="Select Corporate Parent (Min 3 chars)"
                                label="corporateparent"
                                track-by="id"
                                :options="corporateParents"
                                :multiple="false"
                                :loading="isLoadingCp"
                                :internal-search="false"
                                @search-change="getAllActiveCorporateParent"
                            ></multiselect>
                        </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group">
                        <multiselect
                            id="store"
                            name="store"
                            v-model="selectedStores"
                            placeholder="Select Store (Min 3 chars)"
                            label="retailer"
                            track-by="id"
                            :options="stores"
                            :multiple="false"
                            :loading="isLoadingSt"
                            :internal-search="false"
                            @search-change="getAllActiveStores"
                        ></multiselect>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group">
                        <multiselect
                            id="wheel"
                            name="wheel"
                            v-model="selectedWheels"
                            placeholder="Select Wheel (Min 3 chars)"
                            label="reward_wheel"
                            track-by="id"
                            :options="reward_wheels"
                            :multiple="false"
                            :loading="isLoadingWh"
                            :internal-search="false"
                            @search-change="getAllwheels"
                        ></multiselect>
                      </div>
                    </div>
                  </div>
                  </div>
                    <div class="card-footer">
                      <button
                        type="button"
                        class="btn btn-success"
                        @click="searchWheels()"
                      >
                        Search
                      </button>
                      <button
                        type="button"
                        @click="reset()"
                        class="btn btn-success margin-left-5"
                      >
                        Reset
                      </button>
                      <button
                        v-if="enableAssign"
                        type="button"
                        @click="assignRewardWheel()"
                        class="btn btn-success margin-left-5"
                      >
                        Assign Reward Wheel
                      </button>
                      <button
                        v-if="enableRemoveWheel"
                        type="button"
                        @click="removeRewardWheels()"
                        class="btn btn-danger margin-left-5"
                      >
                        Remove Wheels
                      </button>
                    </div>
                    <div class="card-body">
                    <b-table-simple
                        responsive
                        show-empty
                        bordered
                        sticky-header="800px"
                        v-if="allStoreModel.length > 0"
                      >
                        <b-thead head-variant="light">
                          <tr>
                              <th width="5%" style="text-align: center;">
                                <label class="form-checkbox">
                                <input type="checkbox" v-model="selectedAllStores" @click="selectAll()">
                                <i class="form-icon"></i>
                                </label>
                              </th>
                              <th width="9%">Store ID</th>
                              <th width="9%">Retailer</th>
                              <th width="9%">Corporate Parent</th>
                              <th width="9%">Active Wheels</th>
                              <th width="9%">Inactive Wheels</th>
                          </tr>
                        </b-thead>
                        <b-tbody v-for="(row, index) in allStoreModel" :key="index">
                          <b-tr>
                            <b-td class="text-center text-gray">
                                <label class="form-checkbox"><input class="indivisual-checkbox" @change="onCheckEvent($event, row)" type="checkbox"></label>
                            </b-td>
                            <b-td class="text-left text-gray">{{
                              row.store_id
                            }}</b-td>
                            <b-td class="text-left text-gray">{{
                              row.store_name
                            }}</b-td>
                            <b-td class="text-center text-gray">{{
                              row.cp_name
                            }}</b-td>
                            <b-td class="text-center text-gray">{{
                              row.active_wheels
                            }}</b-td>
                            <b-td class="text-center text-gray" v-html="formattedInactiveWheels(row)"></b-td>
                          </b-tr>
                        </b-tbody>
                      </b-table-simple>
                      <!-- Display pagination controls -->
                    <div v-if="allStoreModel.length > 0" style="float:right">
                        <b-pagination
                            v-model="currentPage"
                            :total-rows="totalItems"
                            :per-page="perPage"
                            prev-text="Prev"
                            next-text="Next"
                            :ellipsis="true"
                            :limit="5"
                        ></b-pagination>
                    </div>
                      <p v-else>No data displayed. Please refine your search criteria.</p>
                      </div>
                </div>
              </div>
            </div>
        </div>
      </section>

      <!-- Assign Reward Wheel Modal -->
       <b-modal
        id="assign-reward-wheel-modal"
        ref="modal"
        :header-text-variant="headerTextVariant"
        title="Assign Reward Wheel"
        ok-title="Save"
        ok-variant="success"
        cancel-variant="outline-secondary"
        @ok="handleAssignRewardWheelOk"
        :no-close-on-esc="true"
        :no-close-on-backdrop="true"
      >

      <div class="card-body">
        <b-table-simple
            responsive
            show-empty
            bordered
            sticky-header="800px"
            v-if="allStoreModel.length > 0"
          >
            <b-thead head-variant="light">
              <tr>
                  <th width="5%" style="text-align: center;"><i class="fas fa-check" aria-hidden="true"></i></th>
                  <th width="9%">Wheel Name</th>
                  <th width="9%">Status</th>
              </tr>
            </b-thead>
            <b-tbody v-for="(row, index) in modal_wheels" :key="index">
              <b-tr>
                <b-td class="text-center text-gray">
                    <label class="form-checkbox"><input class="indivisual-checkbox-modal" :data-id="row.id" :checked="isChecked(row.id)" @change="onCheckModalEvent($event, row)" type="checkbox"></label>
                </b-td>
                <b-td class="text-left text-gray">{{
                  row.reward_wheel
                }}</b-td>
                <b-td class="text-left text-gray">{{
                  row.status
                }}</b-td>
              </b-tr>
            </b-tbody>
        </b-table-simple>
          <!-- Display pagination controls -->
        <div v-if="modal_wheels.length > 0" style="float:right">
            <b-pagination
                v-model="modalCurrentPage"
                :total-rows="modalTotalItems"
                :per-page="modalPerPage"
                prev-text="Prev"
                next-text="Next"
                :ellipsis="true"
                :limit="5"
            ></b-pagination>
        </div>
          <p v-else>No Wheels Found.</p>
      </div>
      </b-modal>
      <!-- Assign Reward Wheel Modal End -->
      <!-- View All Inactive Wheels Modal -->
      <b-modal
        id="all-inactive-wheels-modal"
        ref="modal"
        :header-text-variant="headerTextVariant"
        :title="modalTitle"
        :no-close-on-esc="true"
        :no-close-on-backdrop="true"
        v-model="viewMoreInactiveWheels"
        ok-variant="success"
        hide-footer ok-only
      >
      <div class="card-body">
        <ul>
            <li v-for="(value, index) in wheelList" :key="index">{{ value }}</li>
        </ul>
      </div>
      </b-modal>
     </div>
</div>
  </template>
  <script>
  import api from "@/api/rewardwheel.js";
  import reportapi from "@/api/reports.js";
  import commonConstants from "@/common/constant.js";
  import { HourGlass } from "vue-loading-spinner";
  import CanPayLoader from "../CustomLoader/CanPayLoader.vue"
  export default {
    data() {
      return {
        modalTitle: "",
        allStoreModel: {},
        headerTextVariant: "light",
        currentUser: localStorage.getItem("user")
          ? JSON.parse(localStorage.getItem("user"))
          : null,
        showReloadBtn: false,
        constants: commonConstants,
        store: {},
        stores: [],
        selectedWheels: [],
        selectedCorporateParents: [],
        selectedStores: [],
        reward_wheels : [],
        loading:false,
        store_id: '',
        corporateParents: [],
        states: [],
        reward_wheel_ids: null,
        currentPage: 1,
        perPage: 10,
        totalPage: 0,
        totalItems: 0,
        modalCurrentPage: 1,
        modalPerPage: 5,
        modalTotalPage: 0,
        modalTotalItems: 0,
        isLoadingCp: false,
        isLoadingSt: false,
        isLoadingWh: false,
        storeIdArray: [],
        selectedAllStores: false,
        enableAssign: false,
        modal_wheels: [],
        modalLoading: false,
        selectedAllWheels : false,
        wheelIdArray: [],
        is_active_selected: false,
        wheelList: [],
        viewMoreInactiveWheels: false,
        enableRemoveWheel: false,
        modalTitle: '',
      };
    },
    components: {
        HourGlass,
        CanPayLoader
    },
    created() {
        this.showAllInactiveWheels();
    },
    watch: {
        currentPage(newVal){
            this.searchWheelsApi();
        },
        modalCurrentPage(newVal){
            this.getModalWheels();
        }
    },
    methods: {
      // click search button
      searchWheels(){
        var self = this;
        // return first page when search
        self.currentPage = 1;
        self.searchWheelsApi();
      },
      // fetch results according to the selection of filtering
      searchWheelsApi(is_count = ''){
        var self = this;
        var cp_id = '';
        var store_id = '';
        var reward_wheel_id = '';
        const selectedCorporateParent = JSON.parse(JSON.stringify(self.selectedCorporateParents));
        console.log(selectedCorporateParent);
        if (selectedCorporateParent !== null && selectedCorporateParent !== undefined) {
            cp_id = selectedCorporateParent.id;
        }
        const selectedStore = JSON.parse(JSON.stringify(self.selectedStores));
        if (selectedStore !== null && selectedStore !== undefined) {
            store_id = selectedStore.id;
        }
        const selectedWheels = JSON.parse(JSON.stringify(self.selectedWheels));
        console.log(selectedWheels);
        if (selectedWheels !== null && selectedWheels !== undefined) {
            reward_wheel_id = selectedWheels.id;
        }
        var request = {
            cp_id: cp_id,
            store_id: store_id,
            reward_wheel_id: reward_wheel_id,
            currentPage: self.currentPage,
            perPage: self.perPage,
            is_count: is_count
        };
        self.loading = true;
        api
        .searchRewardWheelStores(request)
        .then(function (response) {
          if (response.code == 200) {
            if(is_count == '') {
                self.allStoreModel = response.data.result;
                self.totalPage = response.data.total_pages;
                self.totalItems = response.data.total;
                $(".indivisual-checkbox-modal").prop('checked', false);
                $(".indivisual-checkbox").prop('checked', false);
                self.loading = false;
                self.enableAssign = false;
                self.enableRemoveWheel = false;
                self.selectedAllStores = false;
            }
            else{
                self.loading = false;
                $(".indivisual-checkbox-modal").prop('checked', false);
                $(".indivisual-checkbox").prop('checked', false);
                self.selectedAllStores = false;
                // check for the available current page
                if(self.currentPage >  response.data.total_pages){
                    self.currentPage = response.data.total_pages;
                }
                else{
                    // search for the current page
                    self.searchWheelsApi();
                }
            }
          } else {
            error(response.message);
            self.loading = false;
          }
        })
        .catch(function (error) {
          // error(error);
          self.loading = false;
        });
      },
      // reset search filters
      reset(){
        var self = this;
        self.selectedCorporateParents = [];
        self.selectedStores = [];
        self.selectedWheels = [];
      },
      //get the list of All CP
      getAllActiveCorporateParent(searchtxt) {
        var self = this;
        if(searchtxt.length >= 3){
            self.isLoadingCp = true;
            self.corporateParents = [];
            var request = {
            searchtxt: searchtxt,
            };
            reportapi
            .getAllActiveCorporateParent(request)
            .then(function (response) {
                if (response.code == 200) {
                    if(response.data.length > 0){
                        self.corporateParents = response.data;
                        self.stores = [];
                    }
                    else{
                        error('No corporate parents found');
                    }

                self.isLoadingCp = false;
                } else {
                error(response.message);
                }
            })
            .catch(function (error) {
                error(error);
            });
        }
      },
      //get the list of All Stores
      getAllActiveStores(searchtxt) {
        var self = this;
        if(searchtxt.length >= 3){
            self.isLoadingSt = true;
            self.stores = [];
            var request = {
                user_id: self.selectedCorporateParents.id,
                searchtxt: searchtxt,
                is_reward_wheel_search: true,
            };
            reportapi
                .getAllActiveStores(request)
                .then(function (response) {
                if (response.code == 200) {
                    if (response.data.length > 0) {
                        self.stores = response.data;
                    }
                    else{
                        error('No stores found');
                    }
                    self.isLoadingSt = false;
                } else {
                    error(response.message);
                    self.isLoadingSt = false;
                }
                })
                .catch(function (error) {
                    alert(error.response.data.message);
                    self.isLoadingSt = false;
                });
        }
      },
      // fetch all wheels for filtering
      getAllwheels(searchtxt){
        var self = this;
        if(searchtxt.length >= 3){
            self.isLoadingWh = true;
            var store_id = self.selectedStores.id;
            api
            .getAllStoreWiseWheels(store_id, searchtxt)
            .then(function (response) {
                if (response.code == 200) {
                    if (response.data.wheels.length > 0) {
                        self.reward_wheels = response.data.wheels;
                    }
                    else{
                        error("No Wheel found");
                    }
                    self.isLoadingWh = false;
                } else {
                    error(response.message);
                    self.isLoadingWh = false;
                }
            })
            .catch(function (error) {
                alert(error.response.data.message);
                self.isLoadingWh = false;
            });
        }
      },
      // fetch the wheels to load in modal
      getModalWheels(){
        var self = this;
        self.modalLoading = true;
        api
        .getAllStoreWiseWheels("", "", self.modalCurrentPage, self.modalPerPage)
        .then(function (response) {
            if (response.code == 200) {
                if (response.data.wheels.length > 0) {
                    self.modal_wheels = response.data.wheels;
                    self.modalTotalItems = response.data.total;
                }
                else{
                    error("No Wheel found");
                }
                self.modalLoading = false;
            } else {
                error(response.message);
                self.modalLoading = false;
            }
        })
        .catch(function (error) {
            alert(error.response.data.message);
            self.modalLoading = false;
        });
      },
      // show all the wheels in a modal
      assignRewardWheel(){
        var self = this;
        self.selectedAllWheels = false;
        self.wheelIdArray = [];
        self.modalCurrentPage = 1;
        self.$bvModal.show("assign-reward-wheel-modal");
        self.getModalWheels();
      },
      // on click eye button to show all inactive wheels modal
      showAllInactiveWheels(){
        var self = this;
        $(document).on("click", ".viewAllInactiveWheel", function (e) {
          //open the modal
          var inactiveWheels = $(e.currentTarget).attr("data-wheel");
          self.wheelList = inactiveWheels.split(",");
          self.viewMoreInactiveWheels = true;
          self.modalTitle = "Inactive Wheels: " + $(e.currentTarget).attr("data-store");
          self.$bvModal.show("all-inactive-wheels-modal");
        });

      },
      // assign selected wheels to the selected stores
      handleAssignRewardWheelOk(){
        var self = this;
        self.wheelIdArray = self.wheelIdArray.filter((value, index, obj) => {
            return obj.indexOf(value) === index;
        });
        var request = {
            store_ids: self.storeIdArray,
            reward_wheel_ids: self.wheelIdArray,
            user_id: self.currentUser.user_id,
        };
        console.log(request);
        api
        .assignRewardWheel(request)
        .then(function (response) {
          if (response.code == 200) {
            self.storeIdArray = [];
            self.wheelIdArray = [];
            $(".indivisual-checkbox-modal").prop('checked', false);
            $(".indivisual-checkbox").prop('checked', false);
            self.loading = false;
            self.enableAssign = false;
            self.enableRemoveWheel = false;
            success(response.message);
            // count search records to determine the current page
            self.searchWheelsApi(true);
          } else {
            error(response.message);
            self.enableAssign = false;
            self.enableRemoveWheel = false;
            $(".indivisual-checkbox-modal").prop('checked', false);
            self.loading = false;
          }
        })
        .catch(function (error) {
            alert(error.response.data.message);
            $(".indivisual-checkbox-modal").prop('checked', false);
            self.loading = false;
        });
      },
      // on select all stores
      selectAll(){
        var self = this;
        if(!self.selectedAllStores){
            self.enableAssign = true;
            self.enableRemoveWheel = true;
            self.storeIdArray = [];
            $(".indivisual-checkbox").prop('checked', true);
            self.allStoreModel.forEach(store => {
                self.storeIdArray.push({ store_id: store.id, state: store.state });
            });
        }
        else{
            self.enableAssign = false;
            self.enableRemoveWheel = false;
            $(".indivisual-checkbox").prop('checked', false);
            self.storeIdArray = [];
        }
      },
      // on select stores
      onCheckEvent(event, row) {
        var self = this;
        if (event.target.checked) {
            self.enableAssign = true;
            self.enableRemoveWheel = true;
            self.storeIdArray.push({ store_id: row.id, state: row.state });
            console.log('stores count' + self.allStoreModel.length);
            console.log('check store count' + self.storeIdArray.length);
            if(self.allStoreModel.length == self.storeIdArray.length){
                self.selectedAllStores = true;
            }
        }
        else {
            self.selectedAllStores = false;
            self.storeIdArray = self.storeIdArray.filter((element) => element.store_id !== row.id);
            if(self.storeIdArray.length == 0){
                self.enableAssign = false;
                self.enableRemoveWheel = false;
            }
        }
      },
      // on select wheels in a modal
      onCheckModalEvent(event, row) {
        var self = this;
        if (event.target.checked) {
            self.wheelIdArray.push(row.id);
        }
        else {
            self.selectedAllWheels = false;
            let index = self.wheelIdArray.indexOf(row.id);
            if (index !== -1) {
                self.wheelIdArray.splice(index, 1); // remove the element at the found index
            }
            if(self.is_active_selected && row.status == 'Active'){
                self.is_active_selected = false;
            }
        }
      },
      // check if any wheels already selected in recent page
      isChecked(id){
        var self = this;
        return self.wheelIdArray.includes(id);
      },
      // format multiple inactive wheels and append html to see more wheels
      formattedInactiveWheels(row) {
        var inactiveWheels = row.inactive_wheels;
        if(inactiveWheels != null){
            var wheelsArray = inactiveWheels.split(",");
            console.log(wheelsArray);
            if (wheelsArray.length === 0) {
                return '';
            } else if (wheelsArray.length === 1) {
                return wheelsArray[0];
            } else if (wheelsArray.length === 2) {
                return wheelsArray.slice(0, 2).join(',');
            } else {
                const truncatedValue = wheelsArray.slice(0, 2).join(',');
                const linkHtml = `<a type="button" class="viewAllInactiveWheel custom-edit-btn" title="View All Inactive Wheels" variant="outline-success" data-wheel="${row.inactive_wheels}" data-store="${row.store_name}"><i class="nav-icon fas fa-eye"></i></a>`;
                return `${truncatedValue},.. ${linkHtml}`;
            }
        }
        return '';
      },
      // remove wheels from selected stores
      removeRewardWheels(){
        var self = this;
        var request = {
            store_ids: self.storeIdArray.map(item => item.store_id)
        };
        Vue.swal({
            title: "Are you sure to remove?",
            text: "You will not be able to undo this action!",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#149240",
            confirmButtonText: "Yes, remove it!",
            cancelButtonText: "No, cancel it!",
            closeOnConfirm: true,
            closeOnCancel: true,
            }).then((result) => {
                if (result.isConfirmed) {
                    api
                    .removeWheels(request)
                    .then(response => {
                        if ((response.code == 200)) {
                            // count records
                            self.searchWheelsApi(true);
                            self.enableRemoveWheel = false;
                            self.enableAssign = false;
                            self.storeIdArray = [];
                            Vue.swal("Removed!", response.message, "success");
                        } else {
                            Vue.swal(response.message, '', 'error');
                        }
                    })
                    .catch(err => {
                        Vue.swal(err.response.data.message, '', 'error');
                    });
                }
            })
      }
    },
    mounted() {
        var self = this;
        self.searchWheels();
        document.title = "CanPay - Reward Wheel Search";
    }
  };
  </script>

  <style>
  .disabled {
      background-color: #e9ecef;
  }
  </style>
