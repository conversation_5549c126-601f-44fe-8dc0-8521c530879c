<template>
<div>
  <div v-if="loading">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Release Consumers From Probable Return</h3>
                </div>
                <div class="card-body">
                  <div class="row">
                    <div class="col-md-4">
                    <div class="form-group">
                     <input
                        class="form-control"
                        placeholder="Consumer Name (min 3 chars)"
                        id="consumer"
                        v-model="consumer"
                      />
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="form-control"
                        placeholder="Phone No (Exact)"
                        id="phone_no"
                        v-model="phone_no"
                      />
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="form-control"
                        placeholder="Email (Exact)"
                        id="email"
                        v-model="email"
                      />
                    </div>
                  </div>
                </div>
                </div>
                <div class="card-footer">
                  <button
                    type="button"
                    class="btn btn-success"
                    @click="searchConsumersWithProbableRetuns()"
                  >
                    Search
                  </button>
                  <button
                    type="button"
                    @click="exportAllConsumersWithProbableRetuns()"
                    class="btn btn-danger ml-10"
                    >
                    Export Full List <i
                        class="fa fa-download ml-10"
                        aria-hidden="true"
                    ></i>
                  </button>
                  <button
                    type="button"
                    @click="reset()"
                    class="btn btn-success margin-left-5"
                  >
                    Reset
                  </button>
                </div>
                <!-- /.card-header -->
                <div class="card-body">
                  <div class="row">

                    <div class="col-12">
                      <b-table-simple
                      responsive
                      show-empty
                      bordered
                      sticky-header="800px"
                      v-if="allConsumersModel.length > 0"
                    >
                      <b-thead head-variant="light">
                        <b-tr>
                          <b-th class="text-center">Consumer Name</b-th>
                          <b-th class="text-center">Email</b-th>
                          <b-th class="text-center">Phone</b-th>
                          <b-th class="text-center">On Hold From</b-th>
                          <b-th class="text-center">Action</b-th>
                        </b-tr>
                      </b-thead>
                      <b-tbody v-for="(row, index) in allConsumersModel" :key="index">
                        <b-tr>
                          <b-td class="text-center text-gray">{{
                            row.name
                          }}</b-td>
                          <b-td class="text-center text-gray">{{
                            row.email
                          }}</b-td>
                          <b-td class="text-center text-gray">{{
                            row.phone
                          }}</b-td>
                          <b-td class="text-center text-gray">{{
                            row.created_at
                          }}</b-td>
                          <b-td class="text-center text-gray">
                            <a style="color: white !important" class="btn btn-success leave-comment" @click="releaseConsumerFromReturn(row.edit, row.consumer_id)">Release</a>
                          </b-td>
                        </b-tr>
                      </b-tbody>
                    </b-table-simple>
                    <p v-else>No data displayed. Please refine your search criteria.</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>


  </div>
</div>
</template>
<script>
import api from "@/api/user.js";
import moment from "moment";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "./CustomLoader/CanPayLoader.vue"
export default {
  data() {
    return {
      phone_no:"",
      email:"",
      consumer:"",
      allConsumersModel:{},
      loading: false,
    };
  },
  components: {
    HourGlass,
    CanPayLoader
  },
  created() {},
  methods: {
    searchConsumersWithProbableRetuns(){
      var self = this;
      if((self.consumer).trim().length < 3 && $("#phone_no").val().trim() === '' &&  $("#email").val().trim() === ''){
        error("Please provide Consumer name (Min 3 chars) or email(exact) or phone no(exact)");
        return false;
      }
      var request = {
        consumer: self.consumer,
        email:self.email,
        phone_no:self.phone_no,
      };
      self.loading = true;
      api
      .searchConsumersWithProbableRetuns(request)
      .then(function (response) {
        if (response.code == 200) {
          self.allConsumersModel = response.data;
          self.loading = false;
        } else {
          error(response.message);
          self.loading = false;
        }
      })
      .catch(function (error) {
        // error(error);
        self.loading = false;
      });
    },
    reset(){
      var self = this;
      self.phone_no = "";
      self.email = "";
      self.consumer = "";
    },
    releaseConsumerFromReturn(id, consumer_id){
      var self = this;
        var request = {
            id: id,
            consumer_id: consumer_id
        };
        console.log(request);
        Vue.swal({
            title: "Are you sure to release this Consumer from Probable Return List?",
            text: "Once done you will not be able to undo this action!",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#149240",
            confirmButtonText: "Yes, release the consumer!",
            cancelButtonText: "No, cancel it!",
            closeOnConfirm: true,
            closeOnCancel: true,
            width: '800px'
            }).then((result) => {
                if (result.isConfirmed) {
                    self.loading = true;
                    api
                    .releaseConsumerFromReturn(request)
                    .then((response) => {
                        if ((response.code = 200)) {
                            success(response.message);
                            self.searchConsumersWithProbableRetuns();
                        }
                    })
                    .catch((err) => {
                        error(err);
                        self.loading = false;
                    });
                }
            })
    },
    exportAllConsumersWithProbableRetuns(){
        var self = this;
        self.loading = true;
        api
            .exportAllConsumersWithProbableRetuns()
            .then(function (response) {
            var FileSaver = require("file-saver");
            var blob = new Blob([response], {
                type: "application/xlsx",
            });
            FileSaver.saveAs(
                blob,
                moment().format("MM/DD/YYYY") + "_All_Consumers_with_probabale_returns.xlsx"
            );
            self.loading = false;
            })
            .catch(function (error) {
            // error(error);
            self.loading = false;
            });
    }
  },
  mounted() {
    document.title = "CanPay - Release Consumers From Return";
  },
};
</script>


