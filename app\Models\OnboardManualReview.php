<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
class OnboardManualReview extends Model
{

    /**
     * __construct
     * This function will insert an alphanumeric 32 character unique id as primary key in the table whenever a data get inserted
     * @param  mixed $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        $this->id = generateUUID();

        parent::__construct($attributes);
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'is_duplicate',
        'status',
        'phone',
        'email',
        'first_name',
        'middle_name',
        'last_name',
        'suffix',
        'doc_front_side',
        'doc_back_side'

    ];
    public $table = 'v1_onboarding_manual_reviews';
    public $incrementing = false;
}
