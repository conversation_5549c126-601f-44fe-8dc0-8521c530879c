<?php

namespace App\Http\Controllers;

use App\Models\BankingSolutionMaster;
use App\Models\FinancialInstitutionMaster;
use App\Models\FinancialInstitutionRoutingNumber;
use App\Models\MigrationLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\MxBank;

class BankController extends Controller
{
    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    /**
     * getAllBank
     * Listing page for Admin Users along with Server Side Pagination in Datatable
     * @param  mixed $request
     * @return void
     */
    public function getAllBank(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Admin banks search started...");

        // Search with in Admin Banks
        $adminBanks = $this->_getAdminBanksSearch($request);

        $message = trans('message.admin_banks_fetch_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Admin banks search started complete.");
        return renderResponse(SUCCESS, $message, $adminBanks);
    }


    /**
     * _getAdminBanksSearch
     * Fetch the Admin Banks
     * @param  mixed $searchArray
     * @return void
     */
    private function _getAdminBanksSearch($request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Admin banks Search Started.");

        $page = $request['page']; // Get the current page from the request
        $perPage = $request['per_page']; // Set the number of items per page
        $offset = ($page - 1) * $perPage; // Calculate the offset
        $activeBankStatus = getStatus(ACTIVE_CODE);
        $sql = "SELECT financial_institution_masters.* FROM financial_institution_masters join `financial_institution_routing_numbers` on `financial_institution_masters`.`id` = `financial_institution_routing_numbers`.`financial_institution_id` where financial_institution_routing_numbers.deleted_at is null";

        $searchStr = [];
        if (strlen(trim($request['bank_name'])) >= 3) {
            $sql .= " AND (financial_institution_masters.bank_name LIKE ? OR financial_institution_routing_numbers.routing_no LIKE ? )";
            array_push($searchStr, '%' . $request['bank_name'] . '%');
            array_push($searchStr, '%' . $request['bank_name'] . '%');
        }
        if (trim($request['frontend_visibility']) != "") {
            $sql .= " AND financial_institution_masters.frontend_visibility = ? ";
            array_push($searchStr, $request['frontend_visibility']);
        }
        if (trim($request['tan_from_fi']) != "") {
            $sql .= " AND financial_institution_masters.tan_from_fi = ? ";
            array_push($searchStr, $request['tan_from_fi']);
        }
        if (trim($request['banking_solution']) != "") {
            if ($request['banking_solution'] == AKOYA) {
                $sql .= " AND financial_institution_masters.is_akoya = 1 ";
            } elseif ($request['banking_solution'] == FINICITY) {
                $sql .= " AND financial_institution_masters.is_finicity = 1 ";
            } elseif ($request['banking_solution'] == MX) {
                $sql .= " AND financial_institution_masters.is_mx = 1 ";
            }
        }
        $sql .= "  Group BY financial_institution_masters.bank_name";
        $totalCount = count(DB::connection(MYSQL_RO)->select($sql, $searchStr));
        $sql .= "  ORDER BY financial_institution_masters.bank_name ASC LIMIT ? OFFSET ?";
        array_push($searchStr, $perPage, $offset);
        $adminBanks = DB::connection(MYSQL_RO)->Select($sql, $searchStr);
        $fincity_active = BankingSolutionMaster::where(['banking_solution_name'=>FINICITY,'status_id'=>$activeBankStatus])->first();
        $mx_active = BankingSolutionMaster::where(['banking_solution_name'=>MX,'status_id'=>$activeBankStatus])->first();
        $akoaya_active = BankingSolutionMaster::where(['banking_solution_name'=>AKOYA,'status_id'=>$activeBankStatus])->first();
        $data = [
            'data' => $adminBanks,
            'current_page' => $page,
            'per_page' => $perPage,
            'total' => $totalCount,
            'total_pages' => ceil($totalCount / $perPage),
            'finicity_active' => !empty($fincity_active),
            'mx_active' => !empty($mx_active),
            'akoya_active' => !empty($akoaya_active)
        ];
        return $data;
    }


    /**
     * editBank
     * This function will update Bank
     * @param  mixed $request
     * @return void
     */
    public function editBank(Request $request)
    {
        $rule = array(
            'id' => VALIDATION_REQUIRED,
        );
        $this->__validate($request->all(), $rule);

        $bank_details = FinancialInstitutionMaster::find($request->get('id'));
        if ($bank_details) {
            if ($request->get('frontend_visibility') == 1) {
                $frontend_visibility_bank = FinancialInstitutionMaster::select('financial_institution_masters.*')->join('financial_institution_routing_numbers', 'financial_institution_masters.id', '=', 'financial_institution_routing_numbers.financial_institution_id')->whereNull('financial_institution_routing_numbers.deleted_at')->where('financial_institution_masters.frontend_visibility', 1)->where('financial_institution_masters.id', '!=', $request->get('id'))->groupBy('bank_name')->get();
                $frontend_visibility_bank = count($frontend_visibility_bank);
            } else {
                $frontend_visibility_bank = 0;
            }
            if ($frontend_visibility_bank > 7) {
                $message = trans('message.bank_frontend_visibility_limit_error');
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . $message);
                return renderResponse(FAIL, $message, null);
            } else {
                $bank_details->logo_url = $request->get('logo_url');
                $bank_details->is_finicity = $request->get('is_finicity');
                $bank_details->is_akoya = $request->get('is_akoya');
                $bank_details->is_mx = $request->get('is_mx');
                $bank_details->tan_from_fi = $request->get('tan_from_fi');
                $bank_details->frontend_visibility = $request->get('frontend_visibility');
                $bank_details->small_logo_base64_image = $request->get('small_logo_base64_image');
                $bank_details->small_logo_extension = $request->get('small_logo_extension');

                if ($request->get('is_akoya') == 1) {
                    $bank_details->akoya_provider_id = $request->get('akoya_provider_id'); 
                }
                if ($request->get('is_mx') == 1) {
                    $bank_details->mx_institution_code = $request->get('mx_institution_code'); 
                }

                $bank_details->save();
                $message = trans('message.bank_updation_success');
                // API Response returned with 200 status
                return renderResponse(SUCCESS, $message, null);
            }
        } else {
            $message = trans('message.bank_not_found');
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . $message);
            return renderResponse(FAIL, $message, null);
        }
    }

    /**
     * editbanksolution
     * This function will update Bank
     * @param  mixed $request
     * @return void
     */
    public function editbanksolution(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Edit Banking solution started...");
        $rule = array(
            'id' => VALIDATION_REQUIRED,
            'sequence_no' => [VALIDATION_REQUIRED, 'unique:banking_solution_masters,sequence_no,' . $request->get('id') . ',id'],
        );
        $this->__validate($request->all(), $rule);

        $bank_details = BankingSolutionMaster::find($request->get('id'));
        if ($bank_details) {
            $bank_details->on_registration = $request->get('on_registration');
            $bank_details->on_bank_change = $request->get('on_bank_change');
            $bank_details->status_id = $request->get('status_id');
            $bank_details->sequence_no = $request->get('sequence_no');
            $bank_details->save();
            $message = trans('message.bank_solution_updation_success');
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . $message);
            // API Response returned with 200 status
            return renderResponse(SUCCESS, $message, null);
        } else {
            $message = trans('message.bank_not_found');
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . $message);
            return renderResponse(FAIL, $message, null);
        }
    }

    /**
     * getAllBankSolution
     * Listing page for Admin Users along with Server Side Pagination in Datatable
     * @param  mixed $request
     * @return void
     */
    public function getAllBankSolution(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Banking solution list fetch started...");
        // Search with in Admin Banks
        $bankSolution = BankingSolutionMaster::join('status_master', 'banking_solution_masters.status_id', '=', 'status_master.id')->select('banking_solution_masters.*', 'status_master.status as status_name')->orderBy('banking_solution_name')->get();

        $message = trans('message.admin_banks_so_fetch_success');
        return renderResponse(SUCCESS, $message, $bankSolution);
    }

    /**
     * Fetches all BankSolution status from table
     */
    public function getBankSolutionStatus()
    {
        $status = DB::select("SELECT id, code, status FROM status_master WHERE code IN (" . ACTIVE_CODE . "," . USER_INACTIVE . ")  order by status");
        $message = trans('message.bank_solution_status_success');
        return renderResponse(SUCCESS, $message, $status); // API Response returned with 200 status
    }

    /**
     * getMxInstitution
     * Listing page for Admin Users along with Server Side Pagination in Datatable
     * @param  mixed $request
     * @return void
     */

     public function getMxInstitution(Request $request){
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Fetching mx institution data started...");
        $page = $request->currentPage; // Get the current page from the request
        $perPage = $request->perPage; // Set the number of items per page
        $offset = ($page - 1) * $perPage; // Calculate the offset
        $mx_institution_sql = "SELECT code,
        medium_logo_url,
        name,
        small_logo_url,
        CASE
        WHEN supports_account_identification = 1 THEN 'Yes'
        ELSE 'No'
        END AS supports_account_identification,
        CASE
        WHEN supports_account_statement = 1 THEN 'Yes'
        ELSE 'No'
        END AS supports_account_statement,
        CASE
        WHEN supports_account_verification = 1 THEN 'Yes'
        ELSE 'No'
        END AS supports_account_verification,
        url from mx_banks ";
        $search_array = [];
        if ($request->code != '' && $request->name != '') {
            $mx_institution_sql .= " WHERE Code = ? AND Name = ? ";
            array_push($search_array, $request->code);
            array_push($search_array, $request->name);
        } else if ($request->code != '') {
            $mx_institution_sql .= " WHERE Code = ? ";
            array_push($search_array, $request->code);
        } else if ($request->name != '') {
            $mx_institution_sql .= " WHERE Name = ? ";
            array_push($search_array, $request->name);
        }

        $totalCount = count(DB::select($mx_institution_sql, $search_array));
        $mx_institution_sql .= " LIMIT ? OFFSET ?";
        array_push($search_array, $perPage, $offset);
        $result = DB::select($mx_institution_sql, $search_array);
        $data = [
            'result' => $result,
            'current_page' => $page,
            'per_page' => $perPage,
            'total' => $totalCount,
            'total_pages' => ceil($totalCount / $perPage),
        ];
        $message = trans('message.mx_institution_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Fetching mx institution data completed.");
        return renderResponse(SUCCESS, $message, $data);

     }
     
    /**
     * getBankRoutingNumbers
     * Listing page for Admin Users along with Server Side Pagination in Datatable
     * @param  mixed $request
     * @return void
     */
    public function getBankRoutingNumbers(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Admin banks routing numbers fetch started...");

        $rule = array(
            'financial_institution_id' => VALIDATION_REQUIRED,
        );
        $this->__validate($request->all(), $rule);

        // get bank routing numbers
        $allBankRoutingNumbers = FinancialInstitutionRoutingNumber::select('*',DB::raw("CASE WHEN new_routing_no='*********' THEN 'N/A' ELSE new_routing_no END as new_routing_no"))
        ->where('financial_institution_id', $request->get('financial_institution_id'))->whereNull('deleted_at')->orderBy('routing_no')->get();

        $message = trans('message.admin_bank_routing_no_fetch_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Admin banks routing numbers fetched complete.");
        return renderResponse(SUCCESS, $message, $allBankRoutingNumbers);
    }
    /**
     * getBankLinkSurveyHistory
     * Fetch bank link survey report for admin users.
     * @param  mixed $request
     * @return void
     */
    public function getBankLinkSurveyHistory(Request $request){
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . " Fetching banks link survey history started...");  
        $page = $request->currentPage; // Get the current page from the request
        $perPage = $request->perPage; // Set the number of items per page
        $offset = ($page - 1) * $perPage; // Calculate the offset
        $sql = "SELECT * FROM bank_link_survey_history
        ";

        $search_array = [];
        if($request->consumer_email != '' && $request->consumer_phone != '' && $request->routing_no != '') {
            $sql .= " WHERE LOWER(consumer_email) = ? AND consumer_phone = ? AND routing_no = ? ";
            array_push($search_array, strtolower($request->consumer_email));
            array_push($search_array, $request->consumer_phone);
            array_push($search_array, $request->routing_no);
        } else if ($request->consumer_email != '' && $request->consumer_phone != '') {
            $sql .= " WHERE LOWER(consumer_email) = ? AND consumer_phone = ? ";
            array_push($search_array, strtolower($request->consumer_email));
            array_push($search_array, $request->consumer_phone);
        } else if ($request->consumer_email != '' && $request->routing_no != '') {
            $sql .= " WHERE LOWER(consumer_email) = ? AND routing_no = ? ";
            array_push($search_array, strtolower($request->consumer_email));
            array_push($search_array, $request->routing_no);
        }else if($request->consumer_phone != '' && $request->routing_no != ''){
            $sql .= " WHERE consumer_phone = ? AND routing_no = ? ";
            array_push($search_array, $request->consumer_phone);
            array_push($search_array, $request->routing_no);
        }else if ($request->consumer_email != ''){
            $sql .= " WHERE LOWER(consumer_email) = ? ";
            array_push($search_array, strtolower($request->consumer_email));
        }else if($request->consumer_phone != ''){
            $sql .= " WHERE consumer_phone = ? ";
            array_push($search_array, $request->consumer_phone);
        }else if($request->routing_no != ''){
            $sql .= " WHERE routing_no = ? ";
            array_push($search_array, $request->routing_no);
        }
        $totalCount = count(DB::select($sql, $search_array));
        $sql .= " ORDER BY created_at DESC
        LIMIT ? OFFSET ?";
        array_push($search_array, $perPage, $offset);
        $result = DB::select($sql, $search_array);
        $data = [
            'result' => $result,
            'current_page' => $page,
            'per_page' => $perPage,
            'total' => $totalCount,
            'total_pages' => ceil($totalCount / $perPage),
        ];
        $message = trans('message.bank_link_survey_history_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . " Fetching banks link survey history completed.");
        return renderResponse(SUCCESS, $message, $data);
    }

    /**
     * addBankDetail
     * Insert the bank details provided by user.
     * @param  mixed $request
     * @return void
     */

     public function addBankDetail(Request $request){
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . " Bank details insertion started...");
        $rule = array(
            'bank_name' => VALIDATION_REQUIRED,
            'routing_no' => VALIDATION_REQUIRED
        );
        $this->__validate($request->all(), $rule);
        $bank_details_present = FinancialInstitutionMaster::where('bank_name',$request['bank_name'])->get();
        //  insert the bank details if not present
        if(count($bank_details_present) == 0){
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . " Details for bank Name: ".$request['bank_name']." started.");
            $bank_details = new FinancialInstitutionMaster();
            $bank_details->bank_name = strtoupper($request['bank_name']);
            if($request['small_logo'] != ''){
                $bank_details->small_logo_base64_image = $request['small_logo'];
            }
            if($request['small_logo_extension'] != ''){
                $bank_details->small_logo_extension = $request['small_logo_extension'];
            }
            if($request['logo_url'] != ''){
                $bank_details->logo_url = $request['logo_url'];
            }
            $bank_details->is_fed_excluded = 1;
            $bank_details->frontend_visibility = $request['frontend_visibility'];
            $bank_details->tan_from_fi = $request['tan_from_fi'];
            $bank_details->is_finicity = $request['is_finicity'];
            $bank_details->is_akoya = $request['is_akoya'];
            $bank_details->is_mx = $request['is_mx'];
            if($request['is_akoya'] == 1 ){
                $bank_details->akoya_provider_id = $request['akoya_provider_id'];
            }
            else if($request['is_akoya'] == 0){
                $bank_details->akoya_provider_id = null;
            }
            if($request['is_mx'] == 1){
                $bank_details->mx_institution_code = $request['mx_institution_code'];
            }
            else if($request['is_mx'] == 0 ){
                $bank_details->mx_institution_code = null;
            }
            $bank_details->save();
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . " Details for bank Name: ".$request['bank_name']." added successfully.");
            $bank_details_for_id = FinancialInstitutionMaster::where('bank_name',$request['bank_name'])->first();
            
            $routing_number = explode(',', $request['routing_no']);
            foreach($routing_number as $curr_routing_number){
                if($curr_routing_number == '*********' || strlen($curr_routing_number) != 9 || !ctype_digit($curr_routing_number)){
                    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . " Unusual routing number: '". $curr_routing_number."' detected against bank name: ". $request['bank_name']);
                }else{
                    $routing_number_already_present = FinancialInstitutionRoutingNumber::where(['financial_institution_id' => $bank_details_for_id->id, 'routing_no' => $curr_routing_number])->first();
                    if(!empty($routing_number_already_present)){
                        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . " Routing number: '". $curr_routing_number."' already present against bank name : ". $request['bank_name']);
                    }else{
                        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . " Adding Routing number: '". $curr_routing_number."' against bank name : ". $request['bank_name']);
                        $bank_routing_detail = new FinancialInstitutionRoutingNumber();
                        $bank_routing_detail->routing_no = $curr_routing_number;
                        if(!empty($request['state'])){
                            $bank_routing_detail->state = $request['state'];
                        }
                        $bank_routing_detail->new_routing_no =  '*********';
                        $bank_routing_detail->financial_institution_id = $bank_details_for_id->id;
                        $bank_routing_detail->save();
                        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . " Routing number: ". $curr_routing_number." added successfully agianst bank name : ". $request['bank_name']);
                    }
                }
            }
        }else{
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . " Bank details already exsist."); 
            $message = trans('message.bank_details_already_present');
            return renderResponse(FAIL, $message, '');
        }

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . " Bank details insertion completed.");
        $message = trans('message.new_bank_added_success');
        return renderResponse(SUCCESS, $message, '');

     }

    /**
     * saveRoutingNumber
     * Insert the bank details provided by user.
     * @param  mixed $request
     * @return void
     */

     public function saveRoutingNumber(Request $request){
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . " Adding Routing Number agianst Bank started...");
        $rule = array(
            'bank_id' => VALIDATION_REQUIRED,
            'routing_no' => VALIDATION_REQUIRED
        );

        $this->__validate($request->all(), $rule);
        $routing_number = explode(',', $request['routing_no']);
        foreach($routing_number as $curr_routing_number){
            if($curr_routing_number == '*********' || strlen($curr_routing_number) != 9 || !ctype_digit($curr_routing_number)){
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . " Unusual routing number: '". $curr_routing_number."' detected against bank name: ". $request['bank_name']);
            }else{
                $routing_number_already_present = FinancialInstitutionRoutingNumber::where(['financial_institution_id' => $request["bank_id"], 'routing_no' => $curr_routing_number])->first();
                if(!empty($routing_number_already_present)){
                    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . " Routing number: '". $curr_routing_number."' already present against bank name : ". $request['bank_name']);
                }else{
                    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . " Adding Routing number: '". $curr_routing_number."' against bank name : ". $request['bank_name']);
                    $bank_routing_detail = new FinancialInstitutionRoutingNumber();
                    $bank_routing_detail->routing_no = $curr_routing_number;
                    if(!empty($request['state'])){
                        $bank_routing_detail->state = $request['state'];
                    }
                    $bank_routing_detail->new_routing_no =  '*********';
                    $bank_routing_detail->financial_institution_id = $request["bank_id"];
                    $bank_routing_detail->save();
                    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . " Routing number: ". $curr_routing_number." added successfully agianst bank name : ". $request['bank_name']);
                }
            }
        }

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Routing numbers added successfully against bank id : ". $request['bank_id']);
        $message = trans('message.routing_number_added_successfully');
        return renderResponse(SUCCESS, $message, '');
     }
}
