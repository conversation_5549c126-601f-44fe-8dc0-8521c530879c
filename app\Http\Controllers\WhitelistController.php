<?php

// PostController.php

namespace App\Http\Controllers;

use App\Models\RoutingNumberMaster;
use App\Models\UnknownRoutingNumber;
use App\Models\WhitelistedRoutingNumbers;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class WhitelistController extends Controller
{

    public function __construct(Request $request)
    {
        $this->request = $request;
    }
    /**
     * Fetch all the Whitelist routing numbers
     */
    public function getWhitelistAllRoutingNumbers()
    {
        // Columns defined for Sorting
        $columns = array(
            0 => 'whitelisted_routing_numbers.routing_number',
            1 => 'whitelisted_routing_numbers.spending_limit',
            2 => 'whitelisted_routing_numbers.created_at',
        );
        // Main Query
        // Main Query
        $query = WhitelistedRoutingNumbers::on(MYSQL_RO);

        //Count Query
        $queryCount = WhitelistedRoutingNumbers::on(MYSQL_RO)
            ->selectRaw('COUNT(*) as total_count');
        $totalData = $queryCount->first()->total_count; // Getting total no of rows

        $query = DB::table('whitelisted_routing_numbers');
        $totalData = $query->get()->count(); // Getting total no of rows
        $totalFiltered = $totalData;
        $limit = intval($this->request->input('length'));
        $start = intval($this->request->input('start'));
        $order = $columns[$this->request->input('order.0.column')];
        $dir = $this->request->input('order.0.dir');
        if (empty($this->request->input('search.value')) && empty($order) && empty($dir)) {
            $details = $query->offset($start)->limit(intval($limit))->orderBy('whitelisted_routing_numbers.created_at', 'DESC')->get();
        } else if (empty($this->request->input('search.value'))) {
            $details = $query->offset($start)->limit(intval($limit))->orderBy($order, $dir)->get();
        } else {
            $search = $this->request->input('search.value');
            $search_query = $query->where(function ($q) use ($search) {
                $q->Where('whitelisted_routing_numbers.routing_number', 'LIKE', "%{$search}%");
            });

            $search_query_count = $queryCount->where(function ($q) use ($search) {
                $q->Where('whitelisted_routing_numbers.routing_number', 'LIKE', "%{$search}%");
            });

            $totalFiltered = $search_query_count->first()->total_count;

            $details = $search_query->offset($start)->limit(intval($limit))->orderBy($order, $dir)->get();
        }
        $data = array();
        if (!empty($details)) {
            // Creating array to show the values in frontend
            foreach ($details as $detail) {
                $nestedData['routing_number'] = $detail->routing_number;
                $nestedData['spending_limit'] = $detail->spending_limit;
                $nestedData['edit'] = $detail->id;
                $nestedData['created_at'] = date('m-d-Y h:i A', strtotime($detail->created_at));
                $data[] = $nestedData;
            }
        }
        // Drawing the Datatable
        $json_data = array(
            "draw" => intval($this->request->input('draw')),
            "recordsTotal" => intval($totalData),
            "recordsFiltered" => intval($totalFiltered),
            "data" => $data,
        );

        Log::info(addslashes(__METHOD__) . "(" . LINE . __LINE__ . ") : Whitelistr routing numbers fetched successfully.");
        echo json_encode($json_data); // Rerurning the data
    }
    /**
     * add new routing numbers to the blacklist
     */
    public function addWhitelistRoutingNumber(Request $request)
    {
        $rule = array(
            'routing_number' => VALIDATION_REQUIRED,
            'spending_limit' => VALIDATION_REQUIRED,
        );
        $this->__validate($request->all(), $rule);

        $data = $this->request->all();
        $routing_number = WhitelistedRoutingNumbers::where('routing_number', $data['routing_number'])->first();
        if (!empty($routing_number)) {
            $message = trans('message.whitelist_routing_number_error');
            return renderResponse(FAIL, $message, null);
        }
        $new = new WhitelistedRoutingNumbers();
        $new->routing_number = $data['routing_number'];
        $new->spending_limit = $data['spending_limit'];
        $new->save();
        $message = trans('message.whitelist_routing_number_success');
        return renderResponse(SUCCESS, $message, null);
    }

    /**
     * getUnknownRoutingNumbers
     * Fetch all unknown routing numbers for institution id
     * @return void
     */
    public function getUnknownRoutingNumbers()
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Fetch process of Unknown routing numbers started...");
        // Columns defined for Sorting
        $columns = array(
            0 => 'institution_id_masters.institution_name',
            1 => 'users.first_name',
            2 => 'users.email',
            3 => 'users.phone',
            4 => 'unknown_routing_numbers.routing_no',
            5 => 'unknown_routing_numbers.created_at',
        );

        // Main Query
        $query = UnknownRoutingNumber::on(MYSQL_RO)->join('institution_id_masters', 'institution_id_masters.institution_id', '=', 'unknown_routing_numbers.institution_id')->join('users', 'users.user_id', '=', 'unknown_routing_numbers.consumer_id')->select('institution_id_masters.institution_name', 'users.first_name', 'users.middle_name', 'users.last_name', 'users.email', 'users.phone', 'unknown_routing_numbers.*')->where('unknown_routing_numbers.is_added', 0)->where('unknown_routing_numbers.is_declined', 0);

        //Count Query
        $queryCount = UnknownRoutingNumber::on(MYSQL_RO)->join('institution_id_masters', 'institution_id_masters.institution_id', '=', 'unknown_routing_numbers.institution_id')->join('users', 'users.user_id', '=', 'unknown_routing_numbers.consumer_id')->selectRaw('COUNT(*) as total_count')->where('unknown_routing_numbers.is_declined', 0)->where('unknown_routing_numbers.is_added', 0);
        $totalData = $queryCount->first()->total_count; // Getting total no of rows

        $totalData = $query->get()->count(); // Getting total no of rows
        $totalFiltered = $totalData;
        $limit = intval($this->request->input('length'));
        $start = intval($this->request->input('start'));
        $order = $columns[$this->request->input('order.0.column')];
        $dir = $this->request->input('order.0.dir');
        if (empty($this->request->input('search.value')) && empty($order) && empty($dir)) {
            $details = $query->offset($start)->limit(intval($limit))->orderBy('unknown_routing_numbers.created_at', 'DESC')->get();
        } else if (empty($this->request->input('search.value'))) {
            $details = $query->offset($start)->limit(intval($limit))->orderBy($order, $dir)->get();
        } else {
            $search = $this->request->input('search.value');
            $search_query = $query->where(function ($q) use ($search) {
                $q->orWhereRaw("lower(concat_ws(' ', users.first_name, users.middle_name, users.last_name)) LIKE ? ", ['%' . $search . '%']);
                $q->orWhere('unknown_routing_numbers.routing_no', 'LIKE', "%{$search}%");
                $q->orWhere('institution_id_masters.institution_name', 'LIKE', "%{$search}%");
                $q->orWhere('unknown_routing_numbers.institution_id', 'LIKE', "%{$search}%");
                $q->orWhere('unknown_routing_numbers.routing_no', 'LIKE', "%{$search}%");
                $q->orWhere('users.email', 'LIKE', "%{$search}%");
                $q->orWhere('users.phone', 'LIKE', "%{$search}%");
            });

            $search_query_count = $queryCount->where(function ($q) use ($search) {
                $q->orWhereRaw("lower(concat_ws(' ', users.first_name, users.middle_name, users.last_name)) LIKE ? ", ['%' . $search . '%']);
                $q->orWhere('unknown_routing_numbers.routing_no', 'LIKE', "%{$search}%");
                $q->orWhere('institution_id_masters.institution_name', 'LIKE', "%{$search}%");
                $q->orWhere('unknown_routing_numbers.institution_id', 'LIKE', "%{$search}%");
                $q->orWhere('unknown_routing_numbers.routing_no', 'LIKE', "%{$search}%");
                $q->orWhere('users.email', 'LIKE', "%{$search}%");
                $q->orWhere('users.phone', 'LIKE', "%{$search}%");
            });

            $totalFiltered = $search_query_count->first()->total_count;

            $details = $search_query->offset($start)->limit(intval($limit))->orderBy($order, $dir)->get();
        }
        $data = array();
        if (!empty($details)) {
            // Creating array to show the values in frontend
            foreach ($details as $detail) {
                $nestedData['consumer_name'] = $detail->first_name . ' ' . $detail->middle_name . ' ' . $detail->last_name;
                $nestedData['email'] = $detail->email;
                $nestedData['phone'] = $detail->phone;
                $nestedData['consumer_id'] = $detail->consumer_id;
                $nestedData['routing_number'] = $detail->routing_no;
                $nestedData['institution_name'] = $detail->institution_name . " (" . $detail->institution_id . ")";
                $nestedData['edit'] = $detail->id;
                $nestedData['created_at'] = date('m-d-Y h:i A', strtotime($detail->created_at));
                $data[] = $nestedData;
            }
        }
        // Drawing the Datatable
        $json_data = array(
            "draw" => intval($this->request->input('draw')),
            "recordsTotal" => intval($totalData),
            "recordsFiltered" => intval($totalFiltered),
            "data" => $data,
        );

        Log::info(addslashes(__METHOD__) . "(" . LINE . __LINE__ . ") : Unknown routing numbers fetched successfully.");
        echo json_encode($json_data); // Rerurning the data

    }

    /**
     * addRoutingToMaster
     * Add this routing number to master table as admin approved the number
     * @param  mixed $request
     * @return void
     */
    public function addRoutingToMaster(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Unknown routing number moving procees to master table started...");
        $rule = array(
            'id' => VALIDATION_REQUIRED,
        );
        $this->__validate($request->all(), $rule);

        $user_details = Auth::user();

        $routing_number = UnknownRoutingNumber::find($request->get('id'));
        if (empty($routing_number)) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No data found on given ID: " . $request->get('id'));
            $message = trans('message.routing_move_to_master_error');
            return renderResponse(FAIL, $message, null);
        }

        // Add the routing number in master table
        $routing_number_master = new RoutingNumberMaster();
        $routing_number_master->institution_id = $routing_number->institution_id;
        $routing_number_master->routing_no = $routing_number->routing_no;
        $routing_number_master->save();

        // Update the unknown routing number to added state
        $routing_number->is_added = 1;
        $routing_number->added_by = $user_details->user_id;
        $routing_number->save();

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Unknown routing number : " . $routing_number->routing_no . " moved successfully to master table by User: " . $user_details->user_id);
        $message = trans('message.routing_move_to_master_success');
        return renderResponse(SUCCESS, $message, null);
    }

    /**
     * addRoutingToMaster
     * Add this routing number to master table as admin approved the number
     * @param  mixed $request
     * @return void
     */
    public function declinerouting(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Unknown routing number decline procees started...");
        $rule = array(
            'id' => VALIDATION_REQUIRED,
        );
        $this->__validate($request->all(), $rule);

        $user_details = Auth::user();

        $routing_number = UnknownRoutingNumber::find($request->get('id'));
        if (empty($routing_number)) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No data found on given ID: " . $request->get('id'));
            $message = trans('message.routing_move_to_master_error');
            return renderResponse(FAIL, $message, null);
        }

        // Update the unknown routing number to added state
        $routing_number->is_declined = 1;
        $routing_number->declined_by = $user_details->user_id;
        $routing_number->save();

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Unknown routing number : " . $routing_number->routing_no . " declined successfully by User: " . $user_details->user_id);
        $message = trans('message.routing_decline_success');
        return renderResponse(SUCCESS, $message, null);
    }

    public function viewTransactionHistory(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Fetching transaction history for consumer procees started...");
        $rule = array(
            'consumer_id' => VALIDATION_REQUIRED,
        );
        $this->__validate($request->all(), $rule);

        $user_details = Auth::user();

        $sql = "SELECT u.first_name, u.middle_name, u.last_name, u.email, u.phone, u.street_address, u.city, u.state, u.zipcode, td.transaction_number, (td.amount+td.tip_amount) total_amount, td.local_transaction_date, sm.`status`
        FROM users u
        LEFT JOIN transaction_details td ON u.user_id = td.consumer_id AND td.transaction_ref_no IS NULL
        LEFT JOIN status_master sm ON td.status_id = sm.id AND sm.code IN ('200', '202', '511')
        WHERE u.user_id = ? ORDER BY td.local_transaction_date DESC";
        $transaction_history = DB::select($sql, [$request->get('consumer_id')]);

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction history for Consumer ID: " . $request->get('consumer_id') . " fetched successfully by User: " . $user_details->user_id);
        $message = trans('message.transaction_history_fetch_success');
        return renderResponse(SUCCESS, $message, $transaction_history);
    }
}
