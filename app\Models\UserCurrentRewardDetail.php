<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class UserCurrentRewardDetail extends Model
{
    protected $connection = MYSQL_REWARD_WHEEL;

    /**
     * __construct
     * This function will insert an alphanumeric 32 character unique id as primary key in the table whenever a data get inserted
     * @param  mixed $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        $this->id = generateUUID();

        parent::__construct($attributes);
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'reward_wheel_id',
        'user_id',
        'reward_points',
        'reward_amount',
        'corporate_parent_id',
    ];
    public $timestamps = true;
    public $incrementing = false;
    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
}
