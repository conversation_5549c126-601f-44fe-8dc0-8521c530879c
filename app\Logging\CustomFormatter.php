<?php

namespace App\Logging;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Monolog\Formatter\LineFormatter;

class CustomFormatter
{
    public $request;

    public function __construct(Request $request, Response $response)
    {
        $this->request = $request;
        $this->response = $response;
    }

    /**
     * Customize the given logger instance.
     *
     * @param  \Illuminate\Log\Logger  $logger
     * @return void
     */
    public function __invoke($logger)
    {
        $user_id = Auth::user() ? Auth::user()->user_id : "";
        $version = ENV('VERSION', '???!!!');
        if (isset($_SERVER['REMOTE_ADDR']) && !empty($_SERVER['REMOTE_ADDR'])) {
            $remote_ip = $_SERVER['REMOTE_ADDR'];
        } else {
            $remote_ip = null;
        }

        foreach ($logger->getHandlers() as $handler) {
            $log_arr = [
                '@timestamp' => '%datetime%',
                'V' => $version,
                'EC2' => config('app.ec2_instance_name'),
                'IP' => $remote_ip,
                'TID' => defined('TID') ? TID : substr(generateUUID(), 0, 16),
                'USER_ID' => $user_id,
                'PU' => getHostName(),
                'LEVEL' => '%level_name%',
                'MESSAGE' => '%message% %context%',
            ];
            $json_arr = json_encode($log_arr);
            $formatter = new LineFormatter($json_arr . "\n");
            $handler->setFormatter($formatter);
        }
    }
}
