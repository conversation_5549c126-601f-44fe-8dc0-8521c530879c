<template>
<div>
  <div v-if="loading">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">All Transactions</h3>
                  <b-button
                  class="btn-danger export-api-btn"
                  @click="reloadDatatable"
                  v-if="showReloadBtn"
                  >
                    <i class="fas fa-redo"></i> Reload
                  </b-button>
                </div>
                <div class="card-body">
                  <div class="row">
                    <div class="col-md-4">
                      <div class="form-group">
                        <input
                          autocomplete="off"
                          class="start-date form-control"
                          placeholder="Start Date"
                          id="start-date"
                          onkeydown="return false"
                        />
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group">
                        <input
                          autocomplete="off"
                          class="end-date form-control"
                          placeholder="End Date"
                          id="end-date"
                          onkeydown="return false"
                        />
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group">
                        <input
                          autocomplete="off"
                          class="form-control"
                          placeholder="Consumer Name (Min 3 chars)"
                          id="consumer"
                          v-model="consumer"
                        />
                      </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                        <label for="merchant">
                          Select Store
                          <span class="red">*</span>
                        </label>
                        <multiselect
                            id="store"
                            v-model="selectedStore"
                            placeholder="Select Store (Min 3 chars)"
                            label="retailer"
                            :options="storelist"
                            :loading="isLoading"
                            :internal-search="false"
                            v-validate="'required'"
                            @search-change="getAllStores"
                        ></multiselect>
                        </div>
                    </div>
                  </div>
                  <div class="row">
                  <div class="col-md-4">
                      <button
                        type="button"
                        class="btn btn-success"
                        @click="generateReport(false)"
                        id="generateBtn"
                      >
                        Generate</button
                      ><button
                        type="button"
                        @click="exportTransaction()"
                        class="btn btn-danger margin-left-5"
                      >
                        Export
                        <i class="fa fa-download" aria-hidden="true"></i>
                      </button>
                      <button
                        type="button"
                        @click="reset()"
                        class="btn btn-success margin-left-5"
                      >
                        Reset
                      </button>
                  </div>
                </div>
                </div>
                <div class="card-footer"></div>
                <!-- /.card-header -->
                <div class="card-body">
                  <div class="row">
                    <div class="col-12">
                      <table
                        id="transactionTable"
                        class="table"
                        style="width: 100%; white-space: normal"
                      >
                        <thead>
                          <tr>
                            <th>Transaction Number</th>
                            <th>V1 Transaction Number</th>
                            <th>Consumer</th>
                            <th>Merchant</th>
                            <th>Store</th>
                            <th>Terminal</th>
                            <th width="12%">Amount ($)</th>
                            <th width="12%">Tip Amount ($)</th>
                            <th width="12%">Delivery Fee ($)</th>
                            <th>Transaction Time</th>
                            <th>Status</th>
                          </tr>
                        </thead>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
    <!-- Transaction cancellation comment modal start -->
    <b-modal
      id="comment-modal"
      ref="comment-modal"
      ok-title="Save & Cancel"
      cancel-title="Close"
      ok-variant="success"
      @ok="cancelTransaction"
      cancel-variant="outline-secondary"
      hide-header
      :no-close-on-esc="true"
      :no-close-on-backdrop="true"
    >
      <div class="row">
        <div class="col-12">
          <label for="comment">
            Tell us why you want to cancel this transaction
            <span class="red">*</span>
          </label>
          <textarea
            name="comment"
            type="text"
            v-model="comment"
            class="form-control"
          />
        </div>
        <input type="text" v-model="transaction_id" hidden />
      </div>
      <div class="row" v-if="showMsg">
        <div class="col-12">
          <label for="comment" class="red"
            >Please fill in the required fields.</label
          >
        </div>
      </div>
      <div class="row" style="margin-bottom: 40px"></div>
    </b-modal>
    <!-- Transaction cancellation comment modal end -->

    <!-- Transaction modification history modal start -->
    <b-modal
      id="modified-tr-history-modal"
      ref="modified-tr-history-modal"
      ok-only
      cancel-variant="outline-secondary"
      :header-text-variant="headerTextVariant"
      :title="historyModalTitle"
      :no-close-on-esc="true"
      :no-close-on-backdrop="true"
    >
      <div class="row">
          <div class="col-md-12">
            <b-table-simple
            class="cp-table"
            responsive
            show-empty
            bordered
            >
                <b-thead head-variant="light">
                    <tr>
                        <th width="15%">Amount ($)</th>
                        <th width="20%">Tip Amount ($)</th>
                        <th width="20%">Modify Time</th>
                        <th width="20%">Reason</th>
                        <th width="20%">Additional Reason</th>
                        <th width="15%">Status</th>
                    </tr>
                </b-thead>
                <b-tbody v-for="(row, index) in transactionHistory" :key="index">
                   <b-tr>
                          <b-td class="text-left text-gray">${{
                            row.amount
                          }}</b-td>
                          <b-td class="text-left text-gray">${{
                            row.tip_amount
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.local_transaction_time
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.reason
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.additional_reason
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.status
                          }}</b-td>
                        </b-tr>
                </b-tbody>
            </b-table-simple>
          </div>
        </div>
    </b-modal>
    <!-- Transaction modification history modal end -->
  </div>
</div>
</template>
<script>
import api from "@/api/transaction.js";
import moment from "moment";
import { validationMixin } from "vuelidate";
import { required, minLength } from "vuelidate/lib/validators";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "./CustomLoader/CanPayLoader.vue"
import commonConstants from "@/common/constant.js";
export default {
  mixins: [validationMixin],
  data() {
    return {
      allTransactionModel: {},
      showMsg: false,
      transaction_id: null,
      comment: "",
      currentUser: localStorage.getItem("user")
        ? JSON.parse(localStorage.getItem("user"))
        : null,
      loading: false,
      report: [],
      generateExport: false,
      showReloadBtn:false,
      constants: commonConstants,
      consumer:"",
      storelist: [],
      transactionHistory: [],
      selectedStore: null,
      isLoading: false,
      headerTextVariant: "light",
      historyModalTitle: "Transaction history",
    };
  },
  components: {
    HourGlass,
    CanPayLoader
  },
  created() {
    this.trLeaveComment();
  },
  methods: {
    //API call to fetch All stores
    getAllStores(searchtxt) {
      var self = this;
      if(searchtxt.length >= 3){
        self.isLoading = true;
        var request = {
          searchtxt: searchtxt,
        };
      api
        .getStores(request)
        .then(function (response) {
          if (response.code == 200) {
            self.storelist = response.data;
            self.isLoading = false;
          }else {
            error(response.message);
            self.isLoading = false;
          }
        })
        .catch(function (error) {
        });
      }
    },
    reloadDatatable(){
      var self = this;
      self.loadDT();
    },
    // API call to generate the all the Transactions Report
    generateReport() {
      var self = this;
      if ($("#start-date").val() != "") {
        var from_date = moment($("#start-date").val()).format("YYYY-MM-DD");
      } else {
        var from_date = "";
      }
      if ($("#end-date").val() != "") {
        var to_date = moment($("#end-date").val()).format("YYYY-MM-DD");
      } else {
        var to_date = "";
      }
      if(self.selectedStore === null){
        var store_id = '';
        error("Please select a store");
        return false;
      }else{
        var store_id = self.selectedStore.id;
      }
      self.report = [];
      var request = {
        from_date: from_date,
        to_date: to_date,
        store_id: store_id,
        consumer: self.consumer,
      };
      if (request.from_date > request.to_date) {
        error("To Date cannot be greater than From date");
        return false;
      }
      if (self.generateExport == false) {
        self.loading = true;
      }
      self.loadDT(request);
    },
    loadDT: function (request) {
      var self = this;
      $("#transactionTable").DataTable({
        searching:false,
        processing: true,
        serverSide: true,
        destroy: true,
        info: false,
        scrollY: "100vh",
        scrollX: "100%",
        fixedHeader: true,
        scroller: {
          displayBuffer: 100,
          boundaryScale: 0.5,
          loadingIndicator: false,
        },
        stateSave: false,
        columnDefs: [
          { orderable: false, targets: [0, 1, 2, 3, 4, 5, 6, 7, 8] },
          { className: "dt-left", targets: [0, 1, 2, 3, 4, 5, 6, 7, 8] },
        ],
        order: [[7, "desc"]],
        orderClasses: false,
        language: {
          processing:
            '<i class="fa fa-spinner fa-spin fa-3x fa-fw"></i><span class="sr-only"></span> ',
          emptyTable: "No Transactions Available.",
          search: "_INPUT_",
          searchPlaceholder: "Search records",
        },
        ajax: {
          headers: {
            Authorization: "Bearer " + localStorage.getItem("token"),
          },
          url: "/api/alltransactions",
          type: "POST",
          data: {
            _token: "{{csrf_token()}}",
            from_date: request.from_date,
            to_date: request.to_date,
            store_id: request.store_id,
            consumer: request.consumer,
          },
          dataType: "json",
          dataSrc: function (result) {
            self.showReloadBtn = false;
            self.allTransactionModel = result.data;
            if (self.generateExport == false) {
              self.loading = false;
            }
            return self.allTransactionModel;
          },
          error: function(data){
            error(commonConstants.datatable_error);
            $('#transactionTable_processing').hide();
            self.showReloadBtn = true;
          }
        },
        columns: [
          { data: "transaction_number" },
          { data: "zipline_trans_id" },
          { data: "consumer_name" },
          { data: "merchant_name" },
          { data: "store_name" },
          { data: "terminal_name" },
          {
            render: function (data, type, full, meta) {
              if (full.attempt_count > 0) {
                var amount_str = '$' + full.amount + ' ... ';
                amount_str =amount_str + '$' +full.updated_amount;
                return amount_str + (
                  ' <b-button data-tr-number="' +full.transaction_number +'" data-id="' + full.edit +'" class="viewModifiedTransaction custom-edit-btn" title="View All Modified Transaction" variant="outline-success"><i class="nav-icon fas fa-eye"></i></b-button>'
                );
              } else {
                return '$' + full.amount;
              }
            },
          },
          {
            render: function (data, type, full, meta) {
              return '$' + full.last_approve_tip_amount;
            },
          },
          { data: "delivery_fee" },
          { data: "transaction_time" },
          { data: "status" },
        ],
      });
    },

    modifiedTransactionHistory(){
      var self = this;
      self.loading = true;
      var request = {
        transaction_id: self.transaction_id,
      };
      api
        .modifiedTransactionHistory(request)
        .then((response) => {
          if ((response.code = 200)) {
            self.transactionHistory = response.data;
            self.$bvModal.show("modified-tr-history-modal");
            success(response.message);
          } else {
            error(response.message);
          }
          self.loading = false;
        })
        .catch((err) => {
          self.loading = false;
          error(err);
        });
    },
    trLeaveComment() {
      var self = this;
      $(document).on("click", ".tr-leave-comment", function (e) {
        //open the modal
        self.comment = "";
        self.$bvModal.show("comment-modal");
        //set transaction id to a hidden field for future use
        self.transaction_id = $(e.currentTarget).attr("data-id");
      });
      // modify transaction history
      $(document).on("click", ".viewModifiedTransaction", function (e) {
        //open the modal
        self.transactionHistory = [];
        self.transaction_id = $(e.currentTarget).attr("data-id");
        self.historyModalTitle = "Transaction history for " + $(e.currentTarget).attr("data-tr-number");
        self.modifiedTransactionHistory();
      });
    },
    cancelTransaction(bvModalEvt) {
      var self = this;
      if (self.comment == "") {
        self.showMsg = true;
        bvModalEvt.preventDefault();
      } else {
        self.showMsg = false;
        var request = {
          transaction_id: self.transaction_id,
          comment: self.comment,
          transaction_place: "Admin",
        };
        api
          .cancelTransaction(request)
          .then((response) => {
            if (response.code == 200) {
              success(response.message);
              self.$refs["comment-modal"].hide();
              self.comment == "";
              self.generateReport();
            } else {
              error(response.message);
              self.$refs["comment-modal"].hide();
              self.comment == "";
            }
          })
          .catch((err) => {
            console.log(err);
            error(err);
          });
      }
    },
    exportTransaction() {
      var self = this;
      self.generateExport = true;
      self.loading = true;

      if ($("#end-date").val() == "" && $("#start-date").val() == "") {
        error("Please select Start Date & End Date then Generate.");
        return false;
      }
      var from_date = moment($("#start-date").val()).format("YYYY-MM-DD");
      var to_date = moment($("#end-date").val()).format("YYYY-MM-DD");

      if(self.selectedStore === null){
        var store_id = '';
      }else{
        var store_id = self.selectedStore.id;
      }

      var request = {
        from_date: from_date,
        to_date: to_date,
        store_id: store_id,
        consumer: self.consumer,
      };

      api
        .getalltransactionexport(request)
        .then(function (response) {
          self.generateExport = false;
          self.loading = false;
          var FileSaver = require("file-saver");
          var blob = new Blob([response], {
            type: "application/xlsx",
          });
          FileSaver.saveAs(
            blob,
            moment().format("MM/DD/YYYY") + "_all_transaction_export.xlsx"
          );
        })
        .catch(function (error) {
          // error(error);
          self.generateExport = false;
          self.loading = false;
        });
    },
    dateDiff(){
      if ($("#start-date").val() != "") {
        var from_date = moment($("#start-date").val()).format("YYYY-MM-DD");
      } else {
        var from_date = "";
      }
      if ($("#end-date").val() != "") {
        var to_date = moment($("#end-date").val()).format("YYYY-MM-DD");
      } else {
        var to_date = "";
      }
      if(from_date!='' && to_date!=''){
        //calculate the date Difference
        var date1 = new Date(from_date);
        var date2 = new Date(to_date);
        // To calculate the time difference of two dates
        var Difference_In_Time = date2.getTime() - date1.getTime();

        // To calculate the no. of days between two dates
        var Difference_In_Days = Difference_In_Time / (1000 * 3600 * 24);

        if(Difference_In_Days > 180){
          $("#generateBtn").prop('disabled', true);
        }else{
          $("#generateBtn").prop('disabled', false);
        }
      }
    },
    reset(){
      var self = this;
      self.consumer = "";
      self.selectedStore = null;
    }
  },
  mounted() {
    var self = this;
    $("#start-date").datepicker({
      format: "mm/dd/yyyy",
      autoclose: true,
      todayHighlight: true,
    }).on('changeDate', function (ev) {
        self.dateDiff();
    });
    $("#end-date").datepicker({
      format: "mm/dd/yyyy",
      autoclose: true,
      todayHighlight: true,
    }).on('changeDate', function (ev) {
        self.dateDiff();
    });
    $("#start-date , #end-date").datepicker("setDate", new Date());
  },
};
</script>
