<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class RegisteredMerchantMaster extends Model
{

    protected $table = 'registered_merchant_master';

    /**
     * __construct
     * This function will insert an alphanumeric 32 character unique id as primary key in the table whenever a data get inserted
     * @param  mixed $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        $this->id = generateUUID();

        parent::__construct($attributes);
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'merchant_id',
        'merchant_name',
        'contact_no',
        'email',
        'authorized_signer',
        'title',
        'email_for_report',
        'username',
        'password',
        'volume_value',
        'per_transaction_value',
        'allow_ecommerce_transaction',
        'allow_consumer_auth',
        'merchant_profile_name',
        'ach_identifier',
        'sponsor_signup_url',
        'type',
    ];
    public $timestamps = true;
    public $incrementing = false;
    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
}
