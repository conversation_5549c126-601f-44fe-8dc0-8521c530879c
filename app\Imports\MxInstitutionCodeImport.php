<?php

namespace App\Imports;

use App\Models\UserRole;
use App\Models\User;
use App\Models\StoreUserMap;
use App\Models\AccessLevelMaster;
use App\Models\FinancialInstitutionMaster;
use App\Models\StatusMaster;
use App\Models\MerchantStores;
use App\Models\MxBank;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class MxInstitutionCodeImport implements ToModel, WithHeadingRow, WithBatchInserts, WithChunkReading
{
    use Importable;
    private $rows = 0;
    private $duplicate_rows = 0;
    private $updated_rows = 0;

    public function __construct()
    {
    }

    /**
     * @param array $row
     * This function actully imports the data as row from Excel Sheet. Here we used the WithHeadingRow to get the Data with Heading. Do Not try to get the rows with index.
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row)
    {
        Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Update mx institution code started... ");
        $provider = FinancialInstitutionMaster::where('bank_name', $row['bank_name'])->first();
        ++$this->rows;
        if ($provider) {
            $mx_bank = MxBank::where(function ($q) use ($row) {
                $q->where('name', $row['parent_name'])->orWhere('name', $row['mx_name']);
            })->first();
            if ($mx_bank) {
                $provider->is_mx = 1;
                $provider->mx_institution_code = $mx_bank->code;
                $provider->save();
                ++$this->updated_rows;
                Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "MX data updated with Routing No: " . $row['routing_no'] . " and Mx Name: " . $row['parent_name']);
            } else {
                Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Mx Bank Not found in our DB with MX Name: " . $row['parent_name']);
            }
        } else {
            Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Provider Not found in our DB with Routing No: " . $row['routing_no']);
        }
    }

    public function getRowCount()
    {
        return $this->rows . '|' . $this->duplicate_rows . '|' . $this->updated_rows;
    }

    public function batchSize(): int
    {
        return 1000;
    }

    public function chunkSize(): int
    {
        return 5000;
    }
}
