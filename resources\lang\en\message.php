<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Password Reset Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines are the default lines which match reasons
    | that are given by the password broker for a password update attempt
    | has failed, such as for an invalid token or invalid new password.
    |
     */
    'invalid_otp'                                            => 'Invalid OTP.',
    'invalid_credentials'                                    => 'Invalid credentials.',
    'access_denied'                                          => 'Access denied.',
    'token_error'                                            => 'Could not create token.',
    'token_success'                                          => 'Token generated successfully.',
    'user_not_found'                                         => 'User not found.',
    'token_expired'                                          => 'Token expired.',
    'invalid_token'                                          => 'Invalid token.',
    'token_not_found'                                        => 'No authorization token provided.',
    'login_success'                                          => 'User logged in successfully.',
    'user_success'                                           => 'User details retrieved successfully.',
    'user_update_success'                                    => 'User details updated successfully.',
    'db_transaction_failed'                                  => 'Database transaction failed.',
    'user_cp_creation_success'                               => 'Corporate parent added successfully.',
    'user_cp_creation_error'                                 => 'Failed to create corporate parent.',
    'user_cp_updation_success'                               => 'Corporate parent updated successfully.',
    'user_cp_updation_error'                                 => 'Failed to update corporate parent.',
    'store_fetch_success'                                    => 'Store list fetched successfully.',
    'excel_import_success'                                   => 'Excel imported successfully.',
    'invalid_excel'                                          => 'Invalid file format. Only xls,xlsx,csv files are allowed.',
    'invalid_logo'                                           => 'Invalid file format. Only gif,jpg,jpeg,png files are allowed.',
    'excel_data_success'                                     => 'Import excel details retrieved successfully.',
    'empty_excel'                                            => "Please select a file to import.",
    'user_creation_success'                                  => 'User added successfully.',
    'user_creation_error'                                    => 'Failed to create user.',
    'user_updation_success'                                  => 'User updated successfully.',
    'user_updation_error'                                    => 'Failed to update user.',
    'user_roles_fetch_success'                               => 'User roles fetched successfully.',
    'template_details_fetch_success'                         => 'Template details fetched successfully.',
    'template_details_update_success'                        => 'Template details updated successfully.',
    'global_notification_creation_success'                   => 'Global notification created successfully.',
    'user_status_activate'                                   => 'User activated successfully.',
    'user_status_deactivate'                                 => 'User account deactivated.',
    'passwords_does_not_match'                               => 'Passwords does not match.',
    'incorrect_password'                                     => 'Incorrect current password.',
    'password_update_success'                                => 'Password updated successfully.',
    'mark_record_reviewed'                                   => 'Status updated as ',
    'transaction_cancelled'                                  => 'Transaction voided successfully',
    'identity_details_success'                               => 'Identity details fetched successfully.',
    'identity_details_not_found'                             => 'Identity details fetched successfully.',
    'transaction_cancel_error'                               => 'Transaction could not be canceled.',
    'routing_number_error'                                   => 'Routing number already blacklisted.',
    'routing_number_success'                                 => 'Routing number blacklisted successfully.',
    'account_not_active'                                     => 'Your account has been deactivated. Please contact the system administrator.',
    'update_merchant_success'                                => 'Merchant updated successfully.',
    'employee_login_enabled'                                 => 'POS employee login enabled.',
    'employee_login_disabled'                                => 'POS employee login disabled.',
    'ecommerce_admin_driven_enabled'                         => 'Ecommerce Admin Driven enabled.',
    'ecommerce_admin_driven_disabled'                        => 'Ecommerce Admin Driven disabled.',
    'not_ecommerce_store'                                    => 'Store is not eCommerce type.',
    'role_mismatched'                                        => 'You are not authorized to make this request from here.',
    'password_reset_succsessful'                             => 'Password reset request successful. Please check your email for your temporary password.',
    'password_reset_fail'                                    => 'Failed to reset password.',
    'global_notification_sent_success'                       => 'Global notification sent successfully.',
    'test_email_success'                                     => 'Test email sent successfully.',
    'update_merchant_account_details_success'                => 'Merchant account details updated successfully.',
    'consumer_list_fetch_success'                            => 'Consumer list fetched successfully',
    'consumer_transaction_fetch_success'                     => 'Consumer transaction list fetched successfully',
    'transaction_fetch_success'                              => 'Transaction list fetched successfully',
    'disable_store_success'                                  => 'Store disabled successfully.',
    'disable_store_error'                                    => 'Failed to disable store.',
    'enable_store_success'                                   => 'Store enabled successfully.',
    'enable_store_error'                                     => 'Failed to enable store.',
    'transaction_status_update_success'                      => 'All transactions status updated successfully.',
    'no_transaction_found'                                   => 'No Transactions found.',
    'transaction_error'                                      => 'Transaction failed. Please try again.',
    'transaction_already_posted'                             => 'Couldn\'t post as the transactions are already posted.',
    'no_transaction_found_for_post'                          => 'Couldn\'t post as no transaction found.',
    'transaction_not_updated'                                => 'Couldn\'t post as the transactions status are not updated.',
    'transaction_post_success'                               => 'Transactions posted successfully.',
    'transaction_report_success'                             => 'Transactions report generated successfully.',
    'dashboard_details_fetched'                              => 'Dashboard details fetched successfully.',
    'store_email_add_success'                                => 'Emails added for the store successfully.',
    'store_email_add_error'                                  => 'Failed to add email for the store.',
    'store_hide_from_map_disabled'                           => 'Store Show in Map',
    'store_hide_from_map_enable'                             => 'Store Hide From Map',
    'return_transaction_fetch_success'                       => 'Return transaction list fetched successfully',
    'void_transaction_fetch_success'                         => 'Transactions fetched successfully',
    'email_not_available'                                    => 'This email is already associated with a CanPay account.',
    'return_reasons_success'                                 => 'Return reasons fetched successfully.',
    'return_waived_successfully'                             => 'Return waived successfully.',
    'transaction_not_found'                                  => 'Transaction not found.',
    'return_waived_error'                                    => 'Return waiving failed.',
    'merchant_details_fetch_success'                         => 'Merchant details fetched successfully',
    'consumer_details_fetch_success'                         => 'Consumer details fetched successfully',
    'return_transaction_details_success'                     => 'Return Transaction Details fetched Successfully.',
    'consumer_search_success'                                => 'Consumer search completed successfully',
    'consumer_algo_history'                                  => 'Consumer Algo history fetched successfully',
    'consumer_accounts_search_success'                       => 'Consumer accounts search completed successfully',
    'store_search_success'                                   => 'Store search completed successfully',
    'corporate_parent_success'                               => 'Corporate Parent search completed successfully',
    'token_blacklisting_success'                             => 'Token blacklisted successfully.',
    'transaction_type_updation_success'                      => 'Transaction type updated successfully.',
    'transaction_type_updation_error'                        => 'Failed to updateTransaction type ',
    'user_details_update_fail'                               => 'Failed to update Consumer Details',
    'user_status_update_fail'                                => 'Failed to update details',
    'registered_merchant_success'                            => 'Registered merchant fetched successfully.',
    'user_list_fetch_success'                                => 'Users List fetched successfully.',
    'fetch_active_stores'                                    => 'Stores fetched successfully.',
    'settlement_report_fetch_success'                        => 'Settlement report fetched successfully.',
    'fetch_cp_success'                                       => 'Corporate Parents fetched successfully.',
    'whitelist_routing_number_success'                       => 'Routing number Whitelisted successfully.',
    'whitelist_routing_number_error'                         => 'Routing number already whitelisted.',
    'return_reason_success'                                  => 'Return reason list fetched successfully.',
    'mail_to_consumer_based_on_return_transaction_success'   => 'Mail to consumer based on return transaction success',
    'mail_to_consumer_based_on_return_transaction_fail'      => 'Mail to consumer based on return transaction fail',
    'fraud_report_fetch_success'                             => 'Consumer fraud report generated successfully',
    'consumer_transaction_history_fetch_success'             => 'Consumer transaction report fetched successfully',
    'consumer_inactive_success'                              => 'Consumer inactivated successfully',
    'consumer_inactive_failed'                               => 'Consumer inactivation failed',
    'transaction_search_success'                             => 'Transaction search completed successfully',
    'consumers_release_success'                              => 'Consumer released successfully.',
    'consumers_release_error'                                => 'No consumer found to release.',
    'fincity_returns_fetch_success'                          => 'Finicity R01 returns report fetched successfully.',
    'manual_banking_returns_fetch_success'                   => 'Manual banking returns report fetched successfully.',
    'manual_review_returns_fetch_success'                    => 'Manual review returns report fetched successfully.',
    'return_report_dashboard_fetch_success'                  => 'Return dashboard fetched successfully.',
    'consumer_comments_fetch_success'                        => 'Consumer comments fetched successfully.',
    'consumer_comment_save_success'                          => 'Comment Saved Successfully.',
    'balance_refresh'                                        => 'Balance refreshed successfully.',
    'balance_refresh_error'                                  => 'Error Occured while fetching balance.',
    'invalid_access_request'                                 => 'You are not authorized to perform this action.',
    'all_accounts_delink_state'                              => 'No active account present. Please ask the consumer to add atleast one active account.',
    'setting_value_fetch_success'                            => 'Setting value fetched successfully',
    'setting_value_update_success'                           => 'Setting values updated successfully',
    'monthly_sales_growth_fetch_success'                     => 'Monthly Sales Growth Report Fetched Successfully.',
    'manual_vs_direct_details_fetched'                       => 'Manual vs Direct linked transaction data fetched successfully.',
    'release_note_error'                                     => 'Release note already exist',
    'release_note_success'                                   => 'Release note added successfully',
    'upload_logo_success'                                    => "You succesfully uploaded the Corporate Logo.",
    'upload_bank_logo_success'                               => "You succesfully uploaded the bank Logo.",
    'upload_logo_fail'                                       => "You attempt to upload Corporate Logo failed.",
    'upload_bank_logo_fail'                                  => "You attempt to upload bank Logo failed.",
    'invalid_file'                                           => 'Invalid file format. Only pdf files are allowed.',
    'empty_file'                                             => "Please select a file to import.",
    'store_api_key_fetch_success'                            => 'Store API Keys List fetched successfully',
    'manual_review_search_success'                           => 'Manual Review search completed successfully.',
    'admin_users_fetch_success'                              => 'Admin Users fetched successfully.',
    'helpdesk_users_fetch_success'                           => 'Helpdesk Users fetched successfully.',
    'global_notification_fetch_success'                      => 'Global notifications fetched successfully.',
    'consumer_balance_fetch_success'                         => "Consumer balance fetched successfully.",
    'return_reason_add_suceess'                              => 'Unknown Return Reason saved successfully.',
    'blacklist_account_number_success'                       => 'Account Number Blacklisted successfully.',
    'valid_account_number_success'                           => 'Valid Account Number for Consumer added successfully.',
    'delete_blacklisted_account_number_success'              => 'Account Number deleted from blacklist.',
    'valid_account_numbers_fetch_success'                    => 'Valid Account Number fetched successfully.',
    'blacklisted_account_numbers_fetch_success'              => 'Blacklisted Account Number fetched successfully.',
    'consumer_linked_accounts_fetch_success'                 => 'Consumer Linked Account Numbers fetched successfully.',
    'delete_valid_account_number_success'                    => 'Account Number deleted from valid list.',
    'valid_account_already_exists'                           => 'This account number is already validated for the consumer.',
    'account_manually_linked'                                => 'This account is manually linked. Are you sure you want to continue?',
    'account_directly_linked'                                => 'This account is directly linked.',
    'add_account_to_blacklist_success'                       => 'Account number added to blacklist successfully.',
    'account_number_not_blacklisted'                         => 'Account number is not blacklisted. Cannot add to valid account number.',
    'general_error'                                          => 'Please contract system administrator',
    'cognito_success'                                        => 'cognito user data successfully fetch',
    'consumer_account_number_fetch_success'                  => 'Consumer Account number list fetched successfully.',
    'fi_id_error'                                            => 'Error:1002. There is a mismatch with the FI Id. Please ask the consumer to relink their bank again.',
    'unmarked_as_suspected'                                  => 'Consumer removed from suspected list successfully.',
    'marked_as_suspected'                                    => 'Consumer marked as a suspected consumer successfully.',
    'forgot_password_email_validation'                       => "Reset password email already sent. Please wait :atr seconds to resend it.",
    'release_conusmer_failed'                                => 'Something went wrong. Please try again.',
    'release_conusmer_success'                               => 'Consumer released successfully from probable return.',
    'update_store_location_success'                          => "Store location updated Successfully.",
    'store_update_location_failed'                           => "Failed to update store location.",
    'routing_move_to_master_error'                           => 'Something went wrong. Please try again!',
    'routing_move_to_master_success'                         => 'Routing number approved successfully.',
    'routing_decline_success'                                => 'Routing number declined successfully.',
    'transaction_history_fetch_success'                      => 'Transaction history fetched successfully.',
    'transaction_void_revoked'                               => 'Void Transaction Revoked successfully',
    'transaction_void_revoked_failed'                        => 'Failed to revoke void transaction.',
    'transaction_cancelation_time_exceeded'                  => 'Time to cancel/revoke transaction exceeded.',
    'notification_resend_success'                            => 'Notification re-sent successfully.',
    'general_error'                                          => 'Please contract system administrator',
    'ecom_category_success'                                  => 'Ecommerce category search completed successfully',
    'ecom_category_creation_success'                         => 'Category added successfully.',
    'ecom_category_creation_error'                           => 'Failed to create Category.',
    'ecom_category_updation_success'                         => 'Category updated successfully.',
    'ecom_category_updation_error'                           => 'Failed to update Category.',
    'ecom_category_fetch_success'                            => 'Ecommerce category list fetched successfully.',
    'merchant_fetch_success'                                 => 'Merchant list fetched successfully.',
    'ecom_merchant_key_success'                              => 'Ecommerce Merchant key search completed successfully',
    'ecom_merchant_key_creation_success'                     => 'Merchant key added successfully.',
    'ecom_merchant_key_duplicate_creation_fail'              => 'Selected category already created for this merchant',
    'ecom_merchant_key_creation_error'                       => 'Failed to create Merchant key.',
    'ecom_merchant_key_updation_success'                     => 'Merchant key updated successfully.',
    'ecom_merchant_key_updation_error'                       => 'Failed to update Merchant key.',
    'merchant_key_fetch_success'                             => 'Merchant key list fetched successfully.',
    'merchant_key_status_updation_success'                   => 'Merchant key status changed successfully.',
    'merchant_key_status_updation_error'                     => 'Failed to changed Merchant key status.',
    'forgot_password_email_validation'                       => "Reset password email already sent. Please wait :atr seconds to resend it.",
    'release_conusmer_failed'                                => 'Something went wrong. Please try again.',
    'release_conusmer_success'                               => 'Consumer released successfully from probable return.',
    'transaction_modification_reason_success'                => 'Transaction modification reason search completed successfully.',
    'transaction_modification_reason_add_success'            => 'Transaction modification reason added successfully.',
    'transaction_modification_reason_add_fail'               => 'Transaction modification reason already exist.',
    'transaction_modification_reason_update_success'         => 'Transaction modification reason updated successfully.',
    'delete_transaction_modification_reason_success'         => 'Transaction modification reason deleted successfully.',
    'delete_transaction_modification_custom_reason_success'  => 'Transaction modification custom reason deleted successfully.',
    'transaction_modification_custom_reason_success'         => 'Transaction modification custom reason search completed successfully.',
    'fetch_mofied_transaction_history'                       => 'Modified transaction fetched successfully.',
    'fetch_mofied_transaction_history_not_found'             => 'No modified transaction found on your given input.',
    'consumer_account_owner_info_success'                    => 'Account owner information fetched and stored successfully.',
    'consumer_account_owner_info_exists'                     => 'Account owner information fetch skipped as the data already exists.',
    'account_owner_history_fetch_success'                    => 'Account owner history fetched successfully.',
    'enable_merchant_fees_report'                            => 'Merchant Fees report enabled.',
    'disable_merchant_fees_report'                           => 'Merchant Fees report disabled.',
    'consumer_bank_delinked_success'                         => 'Consumer Bank Account Delinked Successfully.',
    'transaction_details_fetch_success'                      => 'Transaction details fetched successfully.',
    'record_not_found'                                       => 'There are no records to show.',
    'user_status_success'                                    => 'User Status fetched successfully.',
    'retailer_not_found'                                     => 'No retailer found on your given input.',
    'ecom_category_not_found'                                => 'No ecommerce category found on your given input',
    'ecom_integrator_not_found'                              => 'No ecommerce integrator found on your given input',
    'store_used_for_reward_wheel'                            => 'Cannot remove store as it is used for a Reward Wheel.',
    'financial_institutions_fetch_success'                   => 'Financial Institutions fetched successfully.',
    'institution_delete_fail'                                => 'There was a problem deleting institution.',
    'institution_removed_success'                            => 'Institution deleted successfully.',
    'pending_transactions_exists'                            => 'Pending transactions found for this institution.',
    'fetch_new_enrollment_users'                             => 'New enrollment users fetched successfully.',
    'all_state_success'                                      => 'State fetched successfully.',
    'manual_bank_restricted_routing_updation_error'          => 'Failed to update manual bank link restricted routing numbers.',
    'manual_bank_restricted_routing_updation_success'        => 'Manual bank link restricted routing numbers updated successfully.',
    'manual_bank_restricted_routing_created_success'         => 'Manual bank link restricted routing numbers created successfully.',
    'manual_bank_restricted_routing_exists_error'            => 'Routing Number and Restriction Year already exists.',
    'manual_bank_restricted_routing_status_disabled_success' => 'Manual bank link restricted routing numbers disabled.',
    'manual_bank_restricted_routing_status_enabled_success'  => 'Manual bank link restricted routing numbers enabled.',
    'manual_bank_restricted_routing_check_not_found'         => 'Manual bank link restriction not found.',
    'manual_bank_restricted_routing_check_found'             => 'Manual bank link restriction found.',
    'user_review_save_success'                               => 'Review Saved Successfully.',
    'user_review_history_fetch_success'                      => 'User Review history fetched successfully.',
    'new_ach_process_is_enabled'                             => 'New ACH process is enabled.',
    'transaction_report_failure'                             => 'Transaction Report Generation Failed.',
    'microbilt_bypass_error'                                 => 'The Microbilt error has been bypassed for this user.',
    'previous_microbilt_not_found'                           => 'Previous microbilt error not found',
    'bank_not_found'                                         => 'Bank not found.',
    'bank_frontend_visibility_limit_error'                   => 'The front-end visibility limit is over.',
    'bank_updation_success'                                  => 'Bank updated successfully.',
    'admin_banks_fetch_success'                              => 'Bank fetched successfully.',
    'admin_bank_routing_no_fetch_success'                    => 'Bank Routing numbers fetched successfully.',
    'admin_banks_so_fetch_success'                           => 'Bank Solution fetched successfully.',
    'bank_solution_status_success'                           => 'Bank Solution Status fetched successfully.',
    'bank_solution_updation_success'                         => 'Banking Solution updated successfully.',
    'mx_institution_success'                                 => 'Mx Institution fetched successfully.',
    'consumer_cognito_response_success'                      => 'Cognito response fetched successfully.',
    'consumer_cognito_response_not_found'                    => 'Cognito response not found.',
    'consumer_cognito_not_found'                             => 'Cognito rule log not found.',
    'account_owner_api_failed_challenged_user'               => 'Unable to fetch account owner information due to user authentication challenge',
    'enable_sponsor_points'                                  => 'Freeze Sponsor Points enabled.',
    'disable_sponsor_points'                                 => 'Freeze Sponsor Points disabled.',
    'sponsor_feature_enabled'                                => 'Sponsor Feature enabled.',
    'sponsor_feature_disabled'                               => 'Sponsor Feature disabled.',
    'fetch_user_members_exception'                           => 'MX account fetch Failed.',
    'connected_bank_not_found'                               => 'No connected institutions found.',
    'connected_merber_guid_found'                            => 'Member ID found successfully.',
    'connected_merber_not_found'                             => 'No Member ID found for this MX User.',
    'mx_last_merber_not_connected'                           => "The latest account is still pending, and account details can't be fetched. Do you want to activate the last successfully connected account of this consumer?",
    'purchase_power_rule_change_success'                     => 'Purchase power rule changed successfully.',
    'purchase_power_rule_change_fail'                        => 'Failed to Update Purchase Power Rule.',
    'repayment_return_success'                               => 'Repayment processed successfully.',
    'repayment_return_error'                                 => 'Repayment process failed.',
    'routing_number_not_matched'                             => 'The routing number does not match with the selected bank.',
    'bank_account_update'                                    => 'Bank Account updated.',
    'selected_account_not_found'                             => 'Selected bank account not found.',
    'fetch_registration_failed_consumer_due_to_mx'           => 'Fetched Consumer detail failed registration due to MX.',
    'fail_mx_due_to_date_range'                              => 'The date range difference should not be greater than 14 days',
    'fail_mx_due_to_date_difference'                         => 'The start date cannot be greater than the end date.',
    'bank_link_survey_history_success'                       => 'Consumer reported missing bank fetched successfully.',
    'bank_name_fetched_successfully'                         => 'List of bank name fetched successfully.',
    'new_bank_added_success'                                 => 'Bank details added successfully.',
    'bank_already_added_error'                               => 'Routing number already present for the bank name.',
    'bank_update_success'                                    => 'Bank detail updated sucessfully.',
    'bank_details_already_present'                           => 'Bank details already present in the system. Please update the existing bank details for changes.',
    'routing_number_added_successfully'                      => 'Distinct Routing Number added successfully.',
    'date_range_difference_for_return_transaction'           => 'The date range difference should not be greater than ' . config('app.mix_api_return_transaction_max_difference') . ' days.',
    'reward_point_deficit_error'                             => 'Sufficient Reward Point not available.',
    'date_range_difference_for_return_transaction'           => 'The date range difference should not be greater than ' . config('app.mix_api_return_transaction_max_difference') . ' days.',
    'identify_member_error'                                  => 'Error Occured while calling Identify member API.',
    'member_identity_call_success'                           => 'Account owner information request is in progress. It may take a few minutes to retrieve the data from MX. Please check back shortly.',
    'consumer_problematic_status_detected'                   => 'Consumer problematic status detected. Please wait until the issue is resolved to fetch the account owner information.',
    'consumer_non_actionable_problematic_status_detected'    => 'Consumer Non-Actionable status detected. Please wait until the issue is resolved to fetch the account owner information.',
    'fetch_active_remotepay_stores'                          => 'Successfully fetched Active Remote Pay Store',
    'delivery_fee_report_fetch_success'                      => 'Delivery Fee Report fetched successfully.',
    'merchants_and_delivery_partners_fetch_success'          => 'Merchants and Delivery Partners fetched successfully.',
    'delivery_settlement_report_fetch_success'               => 'Delivery Settlement Report fetched successfully.',
    'petition_status_success'                                => 'Petition Status fetched successfully.',
    'upload_petition_logo_success'                           => "You succesfully uploaded the petition Logo.",
    'upload_petition_logo_fail'                              => "You attempt to upload petition Logo failed.",
    'petition_not_found'                                     => 'Petition not found.',
    'petition_updation_success'                              => 'Petition updated successfully.',
    'petition_signed_users_fetch_success'                    => 'Petition signed users fetched successfully.',
    'petition_not_found'                                     => 'Petition not found or already linked.',
    'store_not_found'                                        => 'Store not found or already linked to a petition.',
    'petition_link_success'                                  => 'Petition successfully linked with Store.',
    'merchant_point_report_fetch_success'                    => 'Merchant point report fetched successfully.',
    'merchant_point_report_export_error'                     => 'Something went wrong while exporting the report. Please try again later.',
    'petition_or_primary_contact_email_not_found'            => 'Petition or primary contact email not found.',
    'petition_email_sent_success'                            => 'Email sent to petition primary contact.',
    'petition_email_sent_success_to_both'                    => 'Petition onboarding emails sent successfully to both primary and secondary contacts.',
];
