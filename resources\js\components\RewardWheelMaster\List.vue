    <template>
    <div>
        <div v-if="loading">
            <CanPayLoader/>
        </div>   
    <div class="content-wrapper" style="min-height: 36px">
        <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
            <div class="col-sm-6"></div>
            </div>
        </div>
        </section>
        <div class="hold-transition sidebar-mini">
        <section class="content">
            <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                <b-button
                    variant="outline-success"
                    style="margin-top: -48px"
                    @click="openModal('add')"
                >
                    <i class="fas fa-plus"></i> Add New
                </b-button>
                <div class="card card-success">
                    <div class="card-header">
                    <h3 class="card-title mb-0">Reward Wheel</h3>
                    <b-button
                        class="btn-danger export-api-btn"
                        @click="reloadDatatable"
                        v-if="showReloadBtn"
                    >
                        <i class="fas fa-redo"></i> Reload
                    </b-button>
                    </div>
                    <div class="card-body">
                    <table
                        id="rewardWheelTable"
                        class="table"
                        style="width: 100%; white-space: normal"
                    >
                        <thead>
                        <tr>
                            <th>Reward Wheel</th>
                            <th>No of Segments</th>
                            <th>Status</th>
                            <th>Action</th>
                        </tr>
                        </thead>
                    </table>
                    </div>
                </div>
                </div>
            </div>
            </div>
        </section>
        </div>
        <!-- Add reward type modal start -->
        <b-modal
        :title="modal_header"
        :header-text-variant="headerTextVariant"
        id="reward-wheel-modal"
        ref="reward-wheel-modal"
        :ok-title="submitBtnText"
        cancel-title="Close"
        ok-variant="success"
        @ok="save"
        cancel-variant="outline-secondary"
        :no-close-on-esc="true"
        :no-close-on-backdrop="true"
        >
            <div v-if="!fetchingLoader">
                <div class="row mb-2">
                    <div class="col-12">
                    <label for="reward_wheel">
                        Reward Wheel
                        <span class="red">*</span>
                    </label>
                    <input
                        name="reward_wheel"
                        placeholder="Enter reward wheel type name"
                        type="text"
                        v-model="formData.reward_wheel"
                        class="form-control"
                        v-validate="'required|min:3|max:24'"
                    />
                    <span v-show="errors.has('reward_wheel')" class="text-danger">{{ getFormatedErrorMessage('reward_wheel', 'Reward Wheel') }}</span>
                    </div>
                </div>

                <div v-if="formData.is_merchant_wheel == 1" class="row">
                    <div class="col-12">
                        <label class="switch"><input type="checkbox" id="use_as_generic_points" name="use_as_generic_points" v-model="formData.use_as_generic_points" true-value="1" false-value="0" class="enable-employee-login"><span class="slider round"></span></label> <label for="use_as_generic_points">Store Specific Points
                            <sup style="margin-left: 0px;top: -7px;"><a style="color: #000;" href="javascript:void(0)" v-b-tooltip.hover title="Points can be used for store which are participating in this wheel only. If this toggle switch is off then points can be used for any store. If you toggle this switch after creating the wheel then Historical points won't get change, only new earned points will be with the new type.">
                                <svg fill="#000000" width="15px" viewBox="-1 0 19 19" xmlns="http://www.w3.org/2000/svg" class="cf-icon-svg"><path d="M16.417 9.583A7.917 7.917 0 1 1 8.5 1.666a7.917 7.917 0 0 1 7.917 7.917zM5.85 3.309a6.833 6.833 0 1 0 2.65-.534 6.787 6.787 0 0 0-2.65.534zm2.654 1.336A1.136 1.136 0 1 1 7.37 5.78a1.136 1.136 0 0 1 1.135-1.136zm.792 9.223V8.665a.792.792 0 1 0-1.583 0v5.203a.792.792 0 0 0 1.583 0z"/></svg>
                            </a></sup>
                        </label>
                    </div>
                </div>
                <div v-if="formData.is_merchant_wheel == 1" class="row">
                    <div class="col-12">
                        <label class="switch"><input type="checkbox" id="ach_billable" name="ach_billable" v-model="formData.ach_billable" true-value="1" false-value="0" class="enable-employee-login"><span class="slider round"></span></label> <label for="ach_billable">ACH billable
                            <sup style="margin-left: 0px;top: -7px;"><a style="color: #000;" href="javascript:void(0)" v-b-tooltip.hover title="When the toggle switch is enabled, the reward amount will be deducted from the merchant via an ACH file.">
                                <svg fill="#000000" width="15px" viewBox="-1 0 19 19" xmlns="http://www.w3.org/2000/svg" class="cf-icon-svg"><path d="M16.417 9.583A7.917 7.917 0 1 1 8.5 1.666a7.917 7.917 0 0 1 7.917 7.917zM5.85 3.309a6.833 6.833 0 1 0 2.65-.534 6.787 6.787 0 0 0-2.65.534zm2.654 1.336A1.136 1.136 0 1 1 7.37 5.78a1.136 1.136 0 0 1 1.135-1.136zm.792 9.223V8.665a.792.792 0 1 0-1.583 0v5.203a.792.792 0 0 0 1.583 0z"/></svg>
                            </a></sup>
                        </label>
                    </div>
                </div>
                <div v-if="formData.is_merchant_wheel == 1" class="row">
                    <div class="col-12">
                        <label class="switch"><input type="checkbox" id="is_merchant_funded" name="is_merchant_funded" v-model="formData.is_merchant_funded" true-value="1" false-value="0" class="enable-employee-login"><span class="slider round"></span></label> <label for="is_merchant_funded">Merchant Funded
                            <sup style="margin-left: 0px;top: -7px;"><a style="color: #000;" href="javascript:void(0)" v-b-tooltip.hover title="When the toggle switch is enabled, a discount is applied to the merchant fee if the consumer makes a transaction using points.">
                                <svg fill="#000000" width="15px" viewBox="-1 0 19 19" xmlns="http://www.w3.org/2000/svg" class="cf-icon-svg"><path d="M16.417 9.583A7.917 7.917 0 1 1 8.5 1.666a7.917 7.917 0 0 1 7.917 7.917zM5.85 3.309a6.833 6.833 0 1 0 2.65-.534 6.787 6.787 0 0 0-2.65.534zm2.654 1.336A1.136 1.136 0 1 1 7.37 5.78a1.136 1.136 0 0 1 1.135-1.136zm.792 9.223V8.665a.792.792 0 1 0-1.583 0v5.203a.792.792 0 0 0 1.583 0z"/></svg>
                            </a></sup>
                        </label>
                    </div>
                </div>
                <div v-if="formData.is_merchant_wheel == 1" class="row">
                    <div class="col-12">
                        <label class="switch"><input type="checkbox" id="coupon_enabled" name="coupon_enabled" v-model="formData.coupon_enabled" true-value="1" false-value="0" class="enable-employee-login"><span class="slider round"></span></label> <label for="coupon_enabled">Enable Coupon Code
                            <sup style="margin-left: 0px;top: -7px;"><a style="color: #000;" href="javascript:void(0)" v-b-tooltip.hover title="When the toggle switch is enabled, Consumer will be able to receive CanPay coupon.">
                                <svg fill="#000000" width="15px" viewBox="-1 0 19 19" xmlns="http://www.w3.org/2000/svg" class="cf-icon-svg"><path d="M16.417 9.583A7.917 7.917 0 1 1 8.5 1.666a7.917 7.917 0 0 1 7.917 7.917zM5.85 3.309a6.833 6.833 0 1 0 2.65-.534 6.787 6.787 0 0 0-2.65.534zm2.654 1.336A1.136 1.136 0 1 1 7.37 5.78a1.136 1.136 0 0 1 1.135-1.136zm.792 9.223V8.665a.792.792 0 1 0-1.583 0v5.203a.792.792 0 0 0 1.583 0z"/></svg>
                            </a></sup>
                        </label>
                    </div>
                    <div class="col-12">
                    <input
                        v-if="formData.coupon_enabled == 1"
                        name="coupon_code"
                        placeholder="Enter Coupon Code"
                        type="text"
                        v-model="formData.coupon_code"
                        class="form-control"
                        v-validate="'required|min:3|max:24'"
                    />
                    </div>
                </div>
                <div class="row" v-if="formData.is_merchant_wheel == 1">
                    <div class="col-12">
                    <label for="reward_wheel">
                        States
                        <span v-if="!isStatusInActive(formData.status_id)" class="red">*</span>
                    </label>
                    <multiselect
                    v-model="formData.state"
                    :options="states"
                    :custom-label="stateCustomName"
                    group-values="data"
                    group-label="label"
                    :group-select="true"
                    ref="state"
                    :multiple="true"
                    placeholder="Select state"
                    track-by="state">
                    </multiselect>
                    <input
                    name="state"
                    type="hidden"
                    v-model="formData.state"
                    v-validate="isStatusInActive(formData.status_id) ? '' : 'required'"
                    />
                    <span v-if="formData.is_merchant_wheel == 1" v-show="errors.has('state')" class="text-danger">{{ getFormatedErrorMessage('state', 'State') }}</span>
                    </div>
                </div>

                <div class="row" v-if="formData.is_merchant_wheel == 1">
                    <div class="col-12">
                    <label for="reward_wheel">
                        Corporate Parents
                        <span v-if="!isStatusInActive(formData.status_id)" class="red">*</span>
                    </label>
                    <multiselect
                    v-model="formData.corporate_parent"
                    :options="corporateParents"
                    :custom-label="cpCustomName"
                    group-values="data"
                    group-label="label"
                    :group-select="true"
                    ref="corporate_parent"
                    :multiple="true"
                    placeholder="Select corporate parents"
                    track-by="first_name">
                    </multiselect>
                    <input
                    name="corporate_parent"
                    type="hidden"
                    v-model="formData.corporate_parent"
                    v-validate="isStatusInActive(formData.status_id) ? '' : 'required'"
                    />
                    <span v-if="formData.is_merchant_wheel == 1" v-show="errors.has('corporate_parent')" class="text-danger">{{ getFormatedErrorMessage('corporate_parent', 'Corporate Parent') }}</span>
                    </div>
                </div>

                <div class="row mb-3" v-if="formData.is_merchant_wheel == 1">
                    <div class="col-12">
                    <label for="reward_wheel">
                        Stores
                        <span v-if="!isStatusInActive(formData.status_id)" class="red">*</span>
                    </label>
                    <multiselect
                    :disabled="disableStore"
                    v-model="formData.stores"
                    :options="stores"
                    :custom-label="storeCustomName"
                    group-values="data"
                    group-label="label"
                    :group-select="true"
                    ref="stores"
                    :multiple="true"
                    placeholder="Select stores"
                    track-by="retailer">
                    </multiselect>
                    <input
                    name="stores"
                    type="hidden"
                    v-model="formData.stores"
                    v-validate="isStatusInActive(formData.status_id) ? '' : 'required'"
                    />
                    <span v-if="formData.is_merchant_wheel == 1" v-show="errors.has('stores')" class="text-danger">{{ getFormatedErrorMessage('stores', 'Stores') }}</span>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-12">
                        <label for="status_id">
                            Status
                            <span class="red">*</span>
                        </label>
                        <select
                            class="form-control"
                            name="status_id"
                            v-model="formData.status_id"
                            v-validate="'required'"
                        >
                            <option value="" hidden>Select a status</option>
                            <option
                            v-for="status in allStatuses"
                            :selected="status.id == formData.status_id"
                            :key="status.id"
                            :value="status.id"
                            >
                            {{ status.status }}
                            </option>
                        </select>
                        <span
                        v-show="errors.has('status_id')"
                        class="text-danger"
                        >{{ getFormatedErrorMessage('status_id', 'Status') }}</span>
                    </div>
                </div>

                <div class="row"  v-if="formData.is_merchant_wheel == 0">
                    <div class="col-12">
                    <label for="spin_gap_hours">
                        Spin Refresh Hours
                        <a href="javascript:void(0)" v-b-tooltip.hover title="The free spinner will be refreshed and a new free spin will be available for all consumer.">
                            <svg width="15px" version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
                            viewBox="0 0 490 490" style="enable-background:new 0 0 490 490;" xml:space="preserve"><g><g><g><path d="M245,490C109.9,490,0,380.1,0,245S109.9,0,245,0s245,109.9,245,245S380.1,490,245,490z M245,62C144.1,62,62,144.1,62,245s82.1,183,183,183s183-82.1,183-183S345.9,62,245,62z"/></g><g><g><circle cx="241.3" cy="159.2" r="29.1"/></g><g><polygon points="285.1,359.9 270.4,359.9 219.6,359.9 204.9,359.9 204.9,321 219.6,321 219.6,254.8 205.1,254.8 205.1,215.9 219.6,215.9 263.1,215.9 270.4,215.9 270.4,321 285.1,321 				"/></g></g></g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g>
                            </svg>
                        </a>
                        <span class="red">*</span>
                    </label>
                    <input
                        name="spin_gap_hours"
                        placeholder="Enter spin refresh hours"
                        min="0"
                        value="0"
                        type="number"
                        v-model="formData.spin_gap_hours"
                        class="form-control"
                        v-validate="'required'"
                    />
                    <span  v-if="formData.is_merchant_wheel == 0" v-show="errors.has('spin_gap_hours')" class="text-danger">{{ getFormatedErrorMessage('spin_gap_hours', 'Spin Refresh Hours') }}</span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <label for="">
                            Segments
                            <span class="red">*</span>
                        </label>
                    </div>
                    <div class="col-12">
                        <div class="accordion" id="accordionDivisions">
                            <Container @drop="onDrop">
                                <Draggable v-for="(input, index) in formData.rewardSegments" :key="index">
                                    <div class="card segment-card"  style="overflow: unset;">
                                        <div class="card-header px-0 py-2 d-flex justify-content-between align-items-center" :id="'segmentHeaderError'+index">
                                            <span class="col-6">Segment {{index+1}} {{ segmentName(input) }}</span>

                                            <div class="col-6 d-flex justify-content-end align-items-center">
                                                <button v-b-tooltip.hover title="Copy segment" @click="copySegment(input)" class="copy-btn" type="button">
                                                    <svg fill="#fff" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
                                                    viewBox="0 0 442 442" style="enable-background:new 0 0 442 442;" xml:space="preserve">
                                                    <g>
                                                    <polygon points="291,0 51,0 51,332 121,332 121,80 291,80 	"/>
                                                    <polygon points="306,125 306,195 376,195 	"/>
                                                    <polygon points="276,225 276,110 151,110 151,442 391,442 391,225 	"/></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g>
                                                    </svg>
                                                </button>
                                                <button @click="collapseSegment(index)" class="btn text-black text-left" type="button" data-toggle="collapse" :data-target="'#collapseDivision'+index" :aria-expanded="index == 0 ? 'true' : 'false'" :aria-controls="'collapseDivision'+index">
                                                    <svg v-if="collapseArrow != index || collapseArrow == undefined" class="collapse-arrow" width="15" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"viewBox="0 0 330 330" style="enable-background:new 0 0 330 330;" xml:space="preserve"><path id="XMLID_225_" d="M325.607,79.393c-5.857-5.857-15.355-5.858-21.213,0.001l-139.39,139.393L25.607,79.393c-5.857-5.857-15.355-5.858-21.213,0.001c-5.858,5.858-5.858,15.355,0,21.213l150.004,150c2.813,2.813,6.628,4.393,10.606,4.393s7.794-1.581,10.606-4.394l149.996-150C331.465,94.749,331.465,85.251,325.607,79.393z"/><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g></svg>
                                                    <svg v-if="collapseArrow == index" class="collapse-arrow" width="15" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"viewBox="0 0 330 330" style="enable-background:new 0 0 330 330;" xml:space="preserve"><path id="XMLID_93_" d="M325.606,229.393l-150.004-150C172.79,76.58,168.974,75,164.996,75c-3.979,0-7.794,1.581-10.607,4.394l-149.996,150c-5.858,5.858-5.858,15.355,0,21.213c5.857,5.857,15.355,5.858,21.213,0l139.39-139.393l139.397,139.393C307.322,253.536,311.161,255,315,255c3.839,0,7.678-1.464,10.607-4.394C331.464,244.748,331.464,235.251,325.606,229.393z"/><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g></svg>
                                                </button>
                                            </div>
                                        </div>

                                        <div :id="'collapseDivision'+index" :class="index == 0 ? 'segment-card-body collapse show' : 'segment-card-body collapse hide'" aria-labelledby="headingOne" data-parent="#accordionDivisions">
                                            <div class="card-body row">
                                                <div class="col-12 d-flex justify-content-end">
                                                    <button
                                                    v-b-tooltip.hover title="Delete segment"
                                                    type="button"
                                                    @click="deleteSegmentRow(index)"
                                                    class="segment-delete-btn"
                                                    >
                                                        <i class="fa fa-trash"></i>
                                                    </button>
                                                </div>
                                                <div class="col-6">
                                                    <label for="segment_id">
                                                        Segment
                                                        <span class="red">*</span>
                                                    </label>

                                                    <select
                                                        class="form-control"
                                                        :name="'segment[' + index + '][segment_id]'"
                                                        v-model="input.segment_id"
                                                        v-validate="'required'"
                                                        style="text-transform: capitalize"
                                                    >
                                                        <option
                                                        v-for="segmentData in segments"
                                                        :selected="segmentData.id == input.segment_id"
                                                        :key="segmentData.id"
                                                        :value="segmentData.id"
                                                        >
                                                        {{ segmentData.segment_name }}
                                                        </option>
                                                    </select>
                                                    <span
                                                        v-show="errors.has('segment[' + index + '][segment_id]')"
                                                        class="text-danger"
                                                        >{{ getFormatedErrorMessage('segment[' + index + '][segment_id]', 'Segment', true, index) }}</span
                                                    >
                                                </div>
                                                <div class="col-6">
                                                    <label for="value">
                                                        Winning Text <span class="red">*</span>
                                                        <a href="javascript:void(0)" v-b-tooltip.hover title="This will be displayed on the success modal.">
                                                            <svg width="15px" version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
                                                            viewBox="0 0 490 490" style="enable-background:new 0 0 490 490;" xml:space="preserve"><g><g><g><path d="M245,490C109.9,490,0,380.1,0,245S109.9,0,245,0s245,109.9,245,245S380.1,490,245,490z M245,62C144.1,62,62,144.1,62,245s82.1,183,183,183s183-82.1,183-183S345.9,62,245,62z"/></g><g><g><circle cx="241.3" cy="159.2" r="29.1"/></g><g><polygon points="285.1,359.9 270.4,359.9 219.6,359.9 204.9,359.9 204.9,321 219.6,321 219.6,254.8 205.1,254.8 205.1,215.9 219.6,215.9 263.1,215.9 270.4,215.9 270.4,321 285.1,321 				"/></g></g></g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g>
                                                            </svg>
                                                        </a>
                                                    </label>
                                                    <input
                                                        v-model="input.text"
                                                        type="text"
                                                        :name="'segment[' + index + '][text]'"
                                                        class="form-control"
                                                        placeholder="Enter text"
                                                        v-validate="'required'"
                                                    />
                                                    <span
                                                        v-show="errors.has('segment[' + index + '][text]')"
                                                        class="text-danger"
                                                        >{{ getFormatedErrorMessage('segment[' + index + '][text]', 'Winning Text', true, index) }}</span
                                                    >
                                                </div>
                                                <div class="col-6">
                                                    <label for="value">
                                                        Value
                                                        <span class="red" v-if="!isJackpot(input.segment_id) && !isLooser(input.segment_id)">*</span>
                                                    </label>
                                                    <div v-if="isAmount(input.segment_id)">
                                                        <div class="amount-field" v-if="isAmount(input.segment_id)">
                                                            
                                                            <strong>$</strong>

                                                            <NumberInput
                                                            style="display: none;"
                                                            v-model="input.value"
                                                            />
                                                            <input
                                                            class="form-control w-100"
                                                            :name="'segment[' + index + '][value]'"
                                                            type="text"
                                                            v-model="input.value"
                                                            v-validate="'required'"
                                                            />
                                                        </div>
                                                        <span v-show="errors.has('segment[' + index + '][value]')" class="text-danger">{{ getFormatedErrorMessage('segment[' + index + '][value]', 'Value', true, index) }}</span>
                                                    </div>
                                                    <div v-if="(isJackpot(input.segment_id)) && (!isAmount(input.segment_id))">
                                                        <div class="readonlyInput padding"></div>
                                                    </div>
                                                    <div v-if="(!isJackpot(input.segment_id)) && (!isAmount(input.segment_id)) && (!isFreeSpin(input.segment_id)) && (!isLooser(input.segment_id)) && (!isMultiplier(input.segment_id))">
                                                        <NumberInput
                                                        style="display: none;"
                                                        v-model="input.value"
                                                        type="decimal"
                                                        />
                                                        <input
                                                        class="form-control w-100"
                                                        :name="'segment[' + index + '][value]'"
                                                        type="text"
                                                        v-model="input.value"
                                                        v-validate="'required'"
                                                        />
                                                        <span v-show="errors.has('segment[' + index + '][value]')" class="text-danger">{{ getFormatedErrorMessage('segment[' + index + '][value]', 'Value', true, index) }}</span>
                                                    </div>
                                                    <div v-if="isMultiplier(input.segment_id)">
                                                        <NumberInput
                                                        style="display: none;"
                                                        length-type="multiplier"
                                                        v-model="input.value"
                                                        />
                                                        <input
                                                        class="form-control w-100"
                                                        :name="'segment[' + index + '][value]'"
                                                        type="text"
                                                        v-model="input.value"
                                                        v-validate="'required'"
                                                        />
                                                        <span v-show="errors.has('segment[' + index + '][value]')" class="text-danger">{{ getFormatedErrorMessage('segment[' + index + '][value]', 'Value', true, index) }}</span>
                                                    </div>
                                                    <div v-if="isLooser(input.segment_id)">
                                                        <div class="readonlyInput">0</div>
                                                    </div>
                                                    <div v-if="isFreeSpin(input.segment_id)">
                                                        <NumberInput
                                                        class="form-control w-100"
                                                        style="display: none;"
                                                        v-model="input.value"
                                                        type="decimal"
                                                        />
                                                        <input
                                                        class="form-control w-100"
                                                        :name="'segment[' + index + '][value]'"
                                                        type="text"
                                                        v-model="input.value"
                                                        v-validate="'required'"
                                                        />
                                                        <span v-show="errors.has('segment[' + index + '][value]')" class="text-danger">{{ getFormatedErrorMessage('segment[' + index + '][value]', 'Value', true, index) }}</span>
                                                    </div>
                                                </div>
                                                <div class="col-6">
                                                    <label for="probability">
                                                        Probability
                                                        <span class="red">*</span>
                                                    </label>
                                                    <NumberInput
                                                    v-model="input.probability"
                                                    :to-fixed="8"
                                                    style="display: none;"
                                                    />
                                                    <input
                                                    class="form-control w-100"
                                                    :name="'segment[' + index + '][probability]'"
                                                    type="text"
                                                    v-model="input.probability"
                                                    v-validate="'required'"
                                                    />
                                                    <span
                                                        v-show="errors.has('segment[' + index + '][probability]')"
                                                        class="text-danger"
                                                        >{{ getFormatedErrorMessage('segment[' + index + '][probability]', 'Probability', true, index) }}</span
                                                    >
                                                    <span :id="'segmentProbabilityError'+index" class=""></span>
                                                    <ProbabilityCalculator
                                                    :segments="formData.rewardSegments"
                                                    class="mt-3" 
                                                    />
                                                </div>

                                                <div class="col-6">
                                                    <label for="image">
                                                        Image
                                                        <span class="red">*</span>
                                                    </label>
                                                    <Uploader :default-images="allDefaultImages" v-model="input.image" />
                                                    <input
                                                        v-model="input.image"
                                                        type="hidden"
                                                        :name="'segment[' + index + '][image]'"
                                                        v-validate="'required'"
                                                    />
                                                    <label v-show="typeof input.image === 'object'" class="rw-check-container">Add to Image Gallery
                                                        <input v-model="input.set_as_default_image" type="checkbox">
                                                        <span class="rw-check-checkmark"></span>
                                                    </label>
                                                    <span
                                                        v-show="errors.has('segment[' + index + '][image]') && input.image == ''"
                                                        class="text-danger"
                                                        >{{ getFormatedErrorMessage('segment[' + index + '][image]', 'Image', true, index) }}</span
                                                    >
                                                </div>


                                                <div class="col-6">
                                                    <label for="color">
                                                        Color <small>(Hex color code)</small>
                                                        <span class="red">*</span>
                                                    </label>
                                                    <br />
                                                    <v-swatches
                                                        v-model="input.color"
                                                        :name="'segment[' + index + '][color]'"
                                                        :swatches="swatches"
                                                        row-length="5"
                                                        show-fallback
                                                        fallback-input-type="color"
                                                        popover-x="left"
                                                    ></v-swatches>
                                                </div>


                                                <div class="col-12">
                                                    <label for="description">
                                                        Segment Description
                                                    </label>
                                                    <br>
                                                    <textarea
                                                    v-model="input.description"
                                                    :name="'segment[' + index + '][description]'"
                                                    id="description"
                                                    rows="3"
                                                    class="form-control w-100"
                                                    >
                                                    </textarea>
                                                </div>

                                            </div>
                                        </div>

                                        <div class="segment-form-error" :id="'segmentError'+index">
                                        </div>
                                    </div>
                                </Draggable>
                            </Container>
                        </div>
                        <div class="form-group row">
                            <div class="col-lg-6">
                                <button type="button" @click="addSegmentRow" class="btn btn-outline-secondary">Add New Segment</button>
                            </div>
                            <div class="col-lg-6 d-flex justify-content-end">
                                <button type="button" @click="showPreviewModal" class="btn btn-outline-success">Show Wheel Preview</button>
                            </div>
                        </div>

                        <div>
                            <div class="d-flex align-items-center mb-3">
                                <button class="btn btn-primary" type="button" data-toggle="collapse" data-target="#wheelPageSettings" aria-expanded="false" aria-controls="wheelPageSettings">
                                    Reward Wheel Page Settings
                                </button>
                            </div>
                            <div class="collapse" id="wheelPageSettings">
                                <div class="card card-body">
                                    <div class="card mb-3">
                                        <div class="card-header">
                                            <div class="row justify-content-between mx-0">
                                                <div>
                                                    <strong>Page Background</strong>
                                                    <a @click="showSettingInfoModal('/img/wheelsettingsinfo/pagebackground.svg')" style="color: #000;" href="javascript:void(0)" >
                                                        <svg fill="#000000" width="20px" viewBox="-1 0 19 19" xmlns="http://www.w3.org/2000/svg" class="cf-icon-svg"><path d="M16.417 9.583A7.917 7.917 0 1 1 8.5 1.666a7.917 7.917 0 0 1 7.917 7.917zM5.85 3.309a6.833 6.833 0 1 0 2.65-.534 6.787 6.787 0 0 0-2.65.534zm2.654 1.336A1.136 1.136 0 1 1 7.37 5.78a1.136 1.136 0 0 1 1.135-1.136zm.792 9.223V8.665a.792.792 0 1 0-1.583 0v5.203a.792.792 0 0 0 1.583 0z"/></svg>
                                                    </a>
                                                </div>

                                                <DesignTemplate
                                                :all-templates="allTemplates"
                                                label="Template"
                                                type="page"
                                                v-model="formData.wheel_options"
                                                />
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="row mb-3">
                                                <div class="col-12 col-md-7 col-lg-7">
                                                    <div class="form-check-inline">
                                                        <label class="form-check-label">
                                                            <input v-model="formData.wheel_options.page.is_image" type="radio" :value="false" class="form-check-input">Color
                                                        </label>
                                                    </div>
                                                    <div class="form-check-inline">
                                                        <label class="form-check-label">
                                                            <input v-model="formData.wheel_options.page.is_image" type="radio" :value="true" class="form-check-input">Image
                                                        </label>
                                                    </div>
                                                    <div class="row" v-if="formData.wheel_options.page.is_image == false">
                                                        <div class="col-4 col-md-4 col-lg-4">
                                                            <div>
                                                                <small>
                                                                Color 1
                                                                </small>
                                                            </div>
                                                            <v-swatches
                                                            v-model="formData.wheel_options.page.color_1"
                                                            show-fallback
                                                            fallback-input-type="color"
                                                            popover-x="left"
                                                            ></v-swatches>
                                                        </div>
                                                        <div class="col-4 col-md-4 col-lg-4">
                                                            <div>
                                                                <small>
                                                                Color 2
                                                                </small>
                                                            </div>
                                                            <v-swatches
                                                            v-model="formData.wheel_options.page.color_2"
                                                            show-fallback
                                                            fallback-input-type="color"
                                                            popover-x="left"
                                                            ></v-swatches>
                                                        </div>
                                                        <div class="col-4 col-md-4 col-lg-4">
                                                            <div>
                                                                <small>
                                                                Color 3
                                                                </small>
                                                            </div>
                                                            <v-swatches
                                                            v-model="formData.wheel_options.page.color_3"
                                                            show-fallback
                                                            fallback-input-type="color"
                                                            popover-x="left"
                                                            ></v-swatches>
                                                        </div>
                                                    </div>
                                                    <div class="row" v-else>
                                                        <div class="col-12 col-md-12 col-lg-12">
                                                            <div>
                                                                <small>
                                                                Background Image
                                                                </small>
                                                            </div>
                                                            <Uploader 
                                                            :clearable="true"
                                                            :show-default-image="false"
                                                            :image-size="200"
                                                            image-dimension=""
                                                            v-model="formData.wheel_options.page.image"
                                                            />
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-12 col-md-5 col-lg-5">
                                                    <div class="mb-3">
                                                        <span>
                                                        Sparkles Display
                                                        </span>
                                                    </div>
                                                    <label class="switch">
                                                        <input type="checkbox" id="sparkles_show" name="sparkles_show" v-model="formData.wheel_options.sparkles.show" true-value="1" false-value="0" class="enable-employee-login">
                                                        <span class="slider round"></span>
                                                    </label>
                                                    <label for="sparkles_show"></label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card mb-3">
                                        <div class="card-header">
                                            <div class="row justify-content-between mx-0">
                                                <div>
                                                    <strong>Page Header</strong>
                                                    <a @click="showSettingInfoModal('/img/wheelsettingsinfo/pageheader.svg')" style="color: #000;" href="javascript:void(0)" >
                                                        <svg fill="#000000" width="20px" viewBox="-1 0 19 19" xmlns="http://www.w3.org/2000/svg" class="cf-icon-svg"><path d="M16.417 9.583A7.917 7.917 0 1 1 8.5 1.666a7.917 7.917 0 0 1 7.917 7.917zM5.85 3.309a6.833 6.833 0 1 0 2.65-.534 6.787 6.787 0 0 0-2.65.534zm2.654 1.336A1.136 1.136 0 1 1 7.37 5.78a1.136 1.136 0 0 1 1.135-1.136zm.792 9.223V8.665a.792.792 0 1 0-1.583 0v5.203a.792.792 0 0 0 1.583 0z"/></svg>
                                                    </a>
                                                </div>
                                                <DesignTemplate
                                                :all-templates="allTemplates"
                                                label="Template"
                                                type="header"
                                                v-model="formData.wheel_options"
                                                />
                                            </div>
                                        </div>
                                        
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-8">
                                                    <div  class="row">
                                                    <div class="form-check-inline ml-3">
                                                        <label class="form-check-label">
                                                            <input v-model="formData.wheel_options.header.header_type" type="radio" value="text" class="form-check-input" name="wheel_bg_image">Text
                                                        </label>
                                                    </div>
                                                    <div class="form-check-inline ml-5">
                                                        <label class="form-check-label">
                                                            <input v-model="formData.wheel_options.header.header_type" type="radio" value="image" class="form-check-input" name="wheel_bg_image">Image
                                                        </label>
                                                    </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row" >
                                                <div class="col-12 col-md-3 col-lg-3">
                                                    <div>
                                                        <small>
                                                         Color
                                                        </small>
                                                    </div>
                                                    <v-swatches
                                                    v-model="formData.wheel_options.header.color"
                                                    show-fallback
                                                    fallback-input-type="color"
                                                    popover-x="left"
                                                    ></v-swatches>
                                                </div>
                                                <div class="col-12 col-md-3 col-lg-3" v-if="formData.wheel_options.header.header_type == 'text'">
                                                    <div>
                                                        <small>
                                                        Title
                                                        </small>
                                                    </div>
                                                    <input
                                                    name="page_title"
                                                    placeholder="Enter page title"
                                                    type="text"
                                                    v-model="formData.wheel_options.header.page_title"
                                                    class="form-control"
                                                    v-validate="'required|min:3|max:24'"
                                                    />
                                                    <span v-show="errors.has('page_title')" class="text-danger">{{ getFormatedErrorMessage('page_title', 'Page Title') }}</span>
                                                </div>
                                                <div class="col-12 col-md-3 col-lg-3" v-if="formData.wheel_options.header.header_type == 'text'">
                                                    <div>
                                                        <small>
                                                        Title Text Size
                                                        </small>
                                                    </div>
                                                    <NumberInput
                                                    style="display: none;"
                                                    v-model="formData.wheel_options.header.page_title_size"
                                                    type="decimal"
                                                    />
                                                    <input
                                                    name="page_title_size"
                                                    min="0"
                                                    value="0"
                                                    type="text"
                                                    v-model="formData.wheel_options.header.page_title_size"
                                                    class="form-control"
                                                    v-validate="'required|min_value:10|max_value:50'"
                                                    />
                                                    <span v-show="errors.has('page_title_size')" class="text-danger">{{ getFormatedErrorMessage('page_title_size', 'Title Text Size') }}</span>
                                                </div>
                                                <div class="col-12 col-md-3 col-lg-3" v-if="formData.wheel_options.header.header_type == 'text'">
                                                    <div>
                                                        <small>
                                                            Transparency
                                                        </small>
                                                    </div>
                                                    <label class="switch">
                                                        <input type="checkbox" id="header_transparent" name="header_transparent" v-model="formData.wheel_options.header.header_transparent" true-value="1" false-value="0" class="enable-employee-login">
                                                        <span class="slider round"></span>
                                                    </label>
                                                    <label for="header_transparent"></label>
                                                </div>
                                                <div class="col-12 col-md-6 col-lg-6" v-else>
                                                    <div>
                                                        <small>
                                                        Background Image
                                                        </small>
                                                    </div>
                                                    <Uploader 
                                                    :clearable="true"
                                                    :show-default-image="false"
                                                    :image-size="200"
                                                    image-dimension=""
                                                    v-model="formData.wheel_options.header.header_title_image"
                                                    />
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-12 col-md-3 col-lg-3">
                                                    <div>
                                                        <small>
                                                          Sub Title Text Color
                                                        </small>
                                                    </div>
                                                    <v-swatches
                                                    v-model="formData.wheel_options.header.header_sub_title_color"
                                                    show-fallback
                                                    fallback-input-type="color"
                                                    popover-x="left"
                                                    ></v-swatches>
                                                </div>
                                                <div class="col-12 col-md-3 col-lg-3">
                                                    <div>
                                                        <small>
                                                        Sub Title
                                                        </small>
                                                    </div>
                                                    <input
                                                    name="sub_titile"
                                                    placeholder="Enter Sub Title"
                                                    type="text"
                                                    v-model="formData.wheel_options.header.header_sub_title"
                                                    class="form-control"
                                                    v-validate="'max:45'"
                                                    />
                                                </div>
                                                <div class="col-12 col-md-3 col-lg-3">
                                                    <div>
                                                        <small>
                                                        Sub Title Text Size
                                                        </small>
                                                    </div>
                                                    <NumberInput
                                                    style="display: none;"
                                                    v-model="formData.wheel_options.header.header_sub_title_size"
                                                    type="decimal"
                                                    />
                                                    <input
                                                    name="header_sub_title_size"
                                                    min="0"
                                                    value="0"
                                                    type="text"
                                                    v-model="formData.wheel_options.header.header_sub_title_size"
                                                    class="form-control"
                                                    v-validate="'min_value:3|max_value:14'"
                                                    />
                                                    <span v-show="errors.has('header_sub_title_size')" class="text-danger">{{ getFormatedErrorMessage('header_sub_title_size', 'Sub Title Text Size') }}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
<!--- start --->
                                    <div class="card mb-3">
                                        <div class="card-header">
                                            <div class="row justify-content-between mx-0">
                                                <div>
                                                    <strong>Page Button</strong>
                                                    <!-- <a @click="showSettingInfoModal('/img/wheelsettingsinfo/pageheader.svg')" style="color: #000;" href="javascript:void(0)" >
                                                        <svg fill="#000000" width="20px" viewBox="-1 0 19 19" xmlns="http://www.w3.org/2000/svg" class="cf-icon-svg"><path d="M16.417 9.583A7.917 7.917 0 1 1 8.5 1.666a7.917 7.917 0 0 1 7.917 7.917zM5.85 3.309a6.833 6.833 0 1 0 2.65-.534 6.787 6.787 0 0 0-2.65.534zm2.654 1.336A1.136 1.136 0 1 1 7.37 5.78a1.136 1.136 0 0 1 1.135-1.136zm.792 9.223V8.665a.792.792 0 1 0-1.583 0v5.203a.792.792 0 0 0 1.583 0z"/></svg>
                                                    </a> -->
                                                </div>
                                                <DesignTemplate
                                                :all-templates="allTemplates"
                                                label="Template"
                                                type="button"
                                                v-model="formData.wheel_options"
                                                />
                                            </div>
                                        </div>
                                        
                                        <div class="card-body">
                                            <div class="row" >
                                                <div class="col-12 col-md-3 col-lg-3">
                                                    <div>
                                                        <small>
                                                         Color
                                                        </small>
                                                    </div>
                                                    <v-swatches
                                                    v-model="formData.wheel_options.button.wheel_bottom_button_background_color"
                                                    show-fallback
                                                    fallback-input-type="color"
                                                    popover-x="left"
                                                    ></v-swatches>
                                                </div>
                                                <div class="col-12 col-md-3 col-lg-3">
                                                    <div>
                                                        <small>
                                                         Button Text Color
                                                        </small>
                                                    </div>
                                                    <v-swatches
                                                    v-model="formData.wheel_options.button.wheel_bottom_button_text_color"
                                                    show-fallback
                                                    fallback-input-type="color"
                                                    popover-x="left"
                                                    ></v-swatches>
                                                </div>
                                                <div class="col-12 col-md-3 col-lg-3">
                                                    <div>
                                                        <small>
                                                        Button Text
                                                        </small>
                                                    </div>
                                                    <input
                                                    name="wheel_bottom_button_text"
                                                    placeholder="Enter Button Text"
                                                    type="text"
                                                    v-model="formData.wheel_options.button.wheel_bottom_button_text"
                                                    class="form-control"
                                                    v-validate="'max:50'"
                                                    />
                                                    
                                                </div>
                                                <div class="col-12 col-md-3 col-lg-3">
                                                    <div>
                                                        <small>
                                                        Button Text Size
                                                        </small>
                                                    </div>
                                                    <NumberInput
                                                    style="display: none;"
                                                    v-model="formData.wheel_options.header.page_title_size"
                                                    type="decimal"
                                                    />
                                                    <input
                                                    name="wheel_bottom_button_text_size"
                                                    min="0"
                                                    value="0"
                                                    type="text"
                                                    v-model="formData.wheel_options.button.wheel_bottom_button_text_size"
                                                    class="form-control"
                                                    v-validate="'max_value:40'"
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
<!--- end --->
                                    <div class="row">
                                        <div class="col-12 col-md-5 col-lg-5">
                                            <div class="card mb-3">
                                                <div class="card-header">
                                                    <div class="row justify-content-between mx-0">
                                                        <div>
                                                            <strong>Page Footer</strong>
                                                            <a @click="showSettingInfoModal('/img/wheelsettingsinfo/pagefooter.svg')" style="color: #000;" href="javascript:void(0)" >
                                                                <svg fill="#000000" width="20px" viewBox="-1 0 19 19" xmlns="http://www.w3.org/2000/svg" class="cf-icon-svg"><path d="M16.417 9.583A7.917 7.917 0 1 1 8.5 1.666a7.917 7.917 0 0 1 7.917 7.917zM5.85 3.309a6.833 6.833 0 1 0 2.65-.534 6.787 6.787 0 0 0-2.65.534zm2.654 1.336A1.136 1.136 0 1 1 7.37 5.78a1.136 1.136 0 0 1 1.135-1.136zm.792 9.223V8.665a.792.792 0 1 0-1.583 0v5.203a.792.792 0 0 0 1.583 0z"/></svg>
                                                            </a>
                                                        </div>
                                                        <DesignTemplate
                                                        :all-templates="allTemplates"
                                                        label="Template"
                                                        type="footer"
                                                        v-model="formData.wheel_options"
                                                        />
                                                    </div>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row">
                                                        <div class="col-12 col-md-6 col-lg-6">
                                                            <div>
                                                                <small>
                                                                Color
                                                                </small>
                                                            </div>
                                                            <v-swatches
                                                            v-model="formData.wheel_options.footer.color"
                                                            show-fallback
                                                            fallback-input-type="color"
                                                            popover-x="left"
                                                            ></v-swatches>
                                                        </div>
                                                        <div class="col-12 col-md-6 col-lg-6">
                                                            <div>
                                                                <small>
                                                                    Transparency
                                                                </small>
                                                            </div>
                                                            <label class="switch">
                                                                <input type="checkbox" id="footer_transparent" name="footer_transparent" v-model="formData.wheel_options.footer.footer_transparent" true-value="1" false-value="0" class="enable-employee-login">
                                                                <span class="slider round"></span>
                                                            </label>
                                                            <label for="footer_transparent"></label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-12 col-md-7 col-lg-7">
                                            <div class="card mb-3">
                                                <div class="card-header">
                                                    <div class="row justify-content-between mx-0">
                                                        <div>
                                                            <strong>Spin Count Badge</strong>
                                                            <a @click="showSettingInfoModal('/img/wheelsettingsinfo/spincount.svg')" style="color: #000;" href="javascript:void(0)" >
                                                                <svg fill="#000000" width="20px" viewBox="-1 0 19 19" xmlns="http://www.w3.org/2000/svg" class="cf-icon-svg"><path d="M16.417 9.583A7.917 7.917 0 1 1 8.5 1.666a7.917 7.917 0 0 1 7.917 7.917zM5.85 3.309a6.833 6.833 0 1 0 2.65-.534 6.787 6.787 0 0 0-2.65.534zm2.654 1.336A1.136 1.136 0 1 1 7.37 5.78a1.136 1.136 0 0 1 1.135-1.136zm.792 9.223V8.665a.792.792 0 1 0-1.583 0v5.203a.792.792 0 0 0 1.583 0z"/></svg>
                                                            </a>
                                                        </div>
                                                        <DesignTemplate
                                                        :all-templates="allTemplates"
                                                        label="Template"
                                                        type="countbadge"
                                                        v-model="formData.wheel_options"
                                                        />
                                                    </div>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row">
                                                        <div class="col-6 col-md-4 col-lg-4">
                                                            <div>
                                                                <small>
                                                                Color
                                                                </small>
                                                            </div>
                                                            <v-swatches
                                                            v-model="formData.wheel_options.spin_button.color"
                                                            show-fallback
                                                            fallback-input-type="color"
                                                            popover-x="left"
                                                            ></v-swatches>
                                                        </div>
                                                        <div class="col-6 col-md-4 col-lg-4">
                                                            <div>
                                                                <small>
                                                                Text Color
                                                                </small>
                                                            </div>
                                                            <v-swatches
                                                            v-model="formData.wheel_options.spin_button.text_color"
                                                            show-fallback
                                                            fallback-input-type="color"
                                                            popover-x="left"
                                                            ></v-swatches>
                                                        </div>
                                                        <div class="col-12 col-md-4 col-lg-4">
                                                            <div>
                                                                <small>
                                                                    Transparency
                                                                </small>
                                                            </div>
                                                            <label class="switch">
                                                                <input type="checkbox" id="spin_transparent" name="spin_transparent" v-model="formData.wheel_options.spin_button.spin_transparent" true-value="1" false-value="0" class="enable-employee-login">
                                                                <span class="slider round"></span>
                                                            </label>
                                                            <label for="spin_transparent"></label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card mb-3">
                                        <div class="card-header">
                                            <strong>Wheel</strong>
                                        </div>
                                        <div class="card-body">
                                            <div class="card mb-3">
                                                <div class="card-header">
                                                    <div class="row justify-content-between mx-0">
                                                        <div>
                                                            <small><strong>Wheel Outer</strong></small>
                                                            <a @click="showSettingInfoModal('/img/wheelsettingsinfo/wheelouter.svg')" style="color: #000;" href="javascript:void(0)" >
                                                                <svg fill="#000000" width="20px" viewBox="-1 0 19 19" xmlns="http://www.w3.org/2000/svg" class="cf-icon-svg"><path d="M16.417 9.583A7.917 7.917 0 1 1 8.5 1.666a7.917 7.917 0 0 1 7.917 7.917zM5.85 3.309a6.833 6.833 0 1 0 2.65-.534 6.787 6.787 0 0 0-2.65.534zm2.654 1.336A1.136 1.136 0 1 1 7.37 5.78a1.136 1.136 0 0 1 1.135-1.136zm.792 9.223V8.665a.792.792 0 1 0-1.583 0v5.203a.792.792 0 0 0 1.583 0z"/></svg>
                                                            </a>
                                                        </div>
                                                        <DesignTemplate
                                                        :all-templates="allTemplates"
                                                        label="Template"
                                                        type="wheelouter"
                                                        v-model="formData.wheel_options"
                                                        />
                                                    </div>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row">
                                                        <div class="col-12 col-md-4 col-lg-4">
                                                            <div>
                                                                <small>
                                                                Color
                                                                </small>
                                                            </div>
                                                            <v-swatches
                                                            v-model="formData.wheel_options.wheel.outer.color"
                                                            show-fallback
                                                            fallback-input-type="color"
                                                            popover-x="left"
                                                            ></v-swatches>
                                                        </div>
                                                        <div class="col-12 col-md-4 col-lg-4">
                                                            <div>
                                                                <small>
                                                                Size
                                                                </small>
                                                            </div>
                                                            <NumberInput
                                                            style="display: none;"
                                                            v-model="formData.wheel_options.wheel.outer.size"
                                                            type="decimal"
                                                            :allow_zero="true"
                                                            />
                                                            <input
                                                            name="outer_size"
                                                            type="text"
                                                            v-model="formData.wheel_options.wheel.outer.size"
                                                            class="form-control"
                                                            v-validate="'max_value:15'"
                                                            />
                                                            <span v-show="errors.has('outer_size')" class="text-danger">{{ getFormatedErrorMessage('outer_size', 'Outer Size') }}</span>
                                                        </div>
                                                        <div class="col-12 col-md-4 col-lg-4">
                                                            <div>
                                                                <small>
                                                                    Shadow
                                                                </small>
                                                            </div>
                                                            <label class="switch">
                                                                <input type="checkbox" id="outer_shadow" name="outer_shadow" v-model="formData.wheel_options.wheel.outer_shadow" true-value="1" false-value="0" class="enable-employee-login">
                                                                <span class="slider round"></span>
                                                            </label>
                                                            <label for="outer_shadow"></label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="card mb-3">
                                                <div class="card-header">
                                                    <div class="row justify-content-between mx-0">
                                                        <div>
                                                            <small><strong>Wheel Inner</strong></small>
                                                            <a @click="showSettingInfoModal('/img/wheelsettingsinfo/wheelinner.svg')" style="color: #000;" href="javascript:void(0)" >
                                                                <svg fill="#000000" width="20px" viewBox="-1 0 19 19" xmlns="http://www.w3.org/2000/svg" class="cf-icon-svg"><path d="M16.417 9.583A7.917 7.917 0 1 1 8.5 1.666a7.917 7.917 0 0 1 7.917 7.917zM5.85 3.309a6.833 6.833 0 1 0 2.65-.534 6.787 6.787 0 0 0-2.65.534zm2.654 1.336A1.136 1.136 0 1 1 7.37 5.78a1.136 1.136 0 0 1 1.135-1.136zm.792 9.223V8.665a.792.792 0 1 0-1.583 0v5.203a.792.792 0 0 0 1.583 0z"/></svg>
                                                            </a>
                                                        </div>
                                                        <DesignTemplate
                                                        :all-templates="allTemplates"
                                                        label="Template"
                                                        type="wheelinner"
                                                        v-model="formData.wheel_options"
                                                        />
                                                    </div>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row mb-2">
                                                        <div class="col-12 col-md-2 col-lg-2">
                                                            <div>
                                                                <small>
                                                                Color 1
                                                                </small>
                                                            </div>
                                                            <v-swatches
                                                            v-model="formData.wheel_options.wheel.inner.color_1"
                                                            show-fallback
                                                            fallback-input-type="color"
                                                            popover-x="left"
                                                            ></v-swatches>
                                                        </div>
                                                        <div class="col-12 col-md-2 col-lg-2">
                                                            <div>
                                                                <small>
                                                                Color 2
                                                                </small>
                                                            </div>
                                                            <v-swatches
                                                            v-model="formData.wheel_options.wheel.inner.color_2"
                                                            show-fallback
                                                            fallback-input-type="color"
                                                            popover-x="left"
                                                            ></v-swatches>
                                                        </div>
                                                        <div class="col-12 col-md-2 col-lg-2">
                                                            <div>
                                                                <small>
                                                                Color 3
                                                                </small>
                                                            </div>
                                                            <v-swatches
                                                            v-model="formData.wheel_options.wheel.inner.color_3"
                                                            show-fallback
                                                            fallback-input-type="color"
                                                            popover-x="left"
                                                            ></v-swatches>
                                                        </div>
                                                        <div class="col-12 col-md-4 col-lg-4">
                                                            <div>
                                                                <small>
                                                                    Size
                                                                </small>
                                                            </div>
                                                            <NumberInput
                                                            style="display: none;"
                                                            v-model="formData.wheel_options.wheel.inner.size"
                                                            type="decimal"
                                                            />
                                                            <input
                                                            name="inner_size"
                                                            min="0"
                                                            value="0"
                                                            type="text"
                                                            v-model="formData.wheel_options.wheel.inner.size"
                                                            class="form-control"
                                                            v-validate="'required|min_value:10|max_value:30'"
                                                            />
                                                            <span v-show="errors.has('inner_size')" class="text-danger">{{ getFormatedErrorMessage('inner_size', 'Inner Size') }}</span>
                                                        </div>
                                                        <div class="col-12 col-md-2 col-lg-2">
                                                            <div>
                                                                <small>
                                                                Shadow
                                                                </small>
                                                            </div>
                                                            <label class="switch">
                                                                <input type="checkbox" id="inner_shadow" name="inner_shadow" v-model="formData.wheel_options.wheel.inner_shadow" true-value="1" false-value="0" class="enable-employee-login">
                                                                <span class="slider round"></span>
                                                            </label>
                                                            <label for="inner_shadow"></label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="card mb-3">
                                                <div class="card-header">
                                                    <div class="row justify-content-between mx-0">
                                                        <div>
                                                            <small><strong>Wheel Pointer</strong></small>
                                                            <a @click="showSettingInfoModal('/img/wheelsettingsinfo/wheelpointer.svg')" style="color: #000;" href="javascript:void(0)" >
                                                                <svg fill="#000000" width="20px" viewBox="-1 0 19 19" xmlns="http://www.w3.org/2000/svg" class="cf-icon-svg"><path d="M16.417 9.583A7.917 7.917 0 1 1 8.5 1.666a7.917 7.917 0 0 1 7.917 7.917zM5.85 3.309a6.833 6.833 0 1 0 2.65-.534 6.787 6.787 0 0 0-2.65.534zm2.654 1.336A1.136 1.136 0 1 1 7.37 5.78a1.136 1.136 0 0 1 1.135-1.136zm.792 9.223V8.665a.792.792 0 1 0-1.583 0v5.203a.792.792 0 0 0 1.583 0z"/></svg>
                                                            </a>
                                                        </div>
                                                        <DesignTemplate
                                                        :all-templates="allTemplates"
                                                        label="Template"
                                                        type="wheelpointer"
                                                        v-model="formData.wheel_options"
                                                        />
                                                    </div>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row mb-2">
                                                        <div class="col-12 col-md-6 col-lg-6">
                                                            <div>
                                                                <small>
                                                                    Image
                                                                </small>
                                                            </div>
                                                            <Uploader 
                                                            :show-default-image="false"
                                                            :image-size="200"
                                                            image-dimension=""
                                                            v-model="formData.wheel_options.wheel.pointer_arrow"
                                                            />
                                                            <input
                                                            v-model="formData.wheel_options.wheel.pointer_arrow"
                                                            type="hidden"
                                                            :name="'pointer_arrow'"
                                                            v-validate="'required'"
                                                            />
                                                            <span v-show="errors.has('pointer_arrow')" class="text-danger">{{ getFormatedErrorMessage('pointer_arrow', 'Pointer Arrow') }}</span>
                                                        </div>
                                                        <!-- <div class="col-12 col-md-6 col-lg-6">
                                                            <div>
                                                                <small>
                                                                Shadow
                                                                </small>
                                                            </div>
                                                            <label class="switch">
                                                                <input type="checkbox" id="pointer_shadow" name="pointer_shadow" v-model="formData.wheel_options.wheel.pointer_shadow" true-value="1" false-value="0" class="enable-employee-login">
                                                                <span class="slider round"></span>
                                                            </label>
                                                            <label for="pointer_shadow"></label>
                                                        </div> -->
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="card mb-3">
                                                <div class="card-header">
                                                    <div class="row justify-content-between mx-0">
                                                        <div>
                                                            <small><strong>Wheel Lights</strong></small>
                                                            <a @click="showSettingInfoModal('/img/wheelsettingsinfo/wheellights.svg')" style="color: #000;" href="javascript:void(0)" >
                                                                <svg fill="#000000" width="20px" viewBox="-1 0 19 19" xmlns="http://www.w3.org/2000/svg" class="cf-icon-svg"><path d="M16.417 9.583A7.917 7.917 0 1 1 8.5 1.666a7.917 7.917 0 0 1 7.917 7.917zM5.85 3.309a6.833 6.833 0 1 0 2.65-.534 6.787 6.787 0 0 0-2.65.534zm2.654 1.336A1.136 1.136 0 1 1 7.37 5.78a1.136 1.136 0 0 1 1.135-1.136zm.792 9.223V8.665a.792.792 0 1 0-1.583 0v5.203a.792.792 0 0 0 1.583 0z"/></svg>
                                                            </a>
                                                        </div>
                                                        <DesignTemplate
                                                        :all-templates="allTemplates"
                                                        label="Template"
                                                        type="wheellights"
                                                        v-model="formData.wheel_options"
                                                        />
                                                    </div>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row">
                                                        <div class="col-12 col-md-3 col-lg-3">
                                                            <div>
                                                                <small>
                                                                Color
                                                                </small>
                                                            </div>
                                                            <v-swatches
                                                            v-model="formData.wheel_options.wheel.pin.color"
                                                            show-fallback
                                                            fallback-input-type="color"
                                                            popover-x="left"
                                                            ></v-swatches>
                                                        </div>
                                                        <div class="col-12 col-md-3 col-lg-3">
                                                            <div>
                                                                <small>
                                                                    Size
                                                                </small>
                                                            </div>
                                                            <NumberInput
                                                            style="display: none;"
                                                            v-model="formData.wheel_options.wheel.pin.size"
                                                            type="decimal"
                                                            />
                                                            <input
                                                            name="pin_size"
                                                            min="0"
                                                            value="0"
                                                            type="text"
                                                            v-model="formData.wheel_options.wheel.pin.size"
                                                            class="form-control"
                                                            v-validate="'required|min_value:3|max_value:8'"
                                                            />
                                                            <span v-show="errors.has('pin_size')" class="text-danger">{{ getFormatedErrorMessage('pin_size', 'Light Size') }}</span>
                                                        </div>
                                                        <div class="col-12 col-md-3 col-lg-3">
                                                            <div>
                                                                <small>
                                                                    Glow
                                                                </small>
                                                            </div>
                                                        <label class="switch">
                                                                <input type="checkbox" id="pin_glow" name="pin_glow" v-model="formData.wheel_options.wheel.pin.glow" true-value="1" false-value="0" class="enable-employee-login">
                                                                <span class="slider round"></span>
                                                            </label>
                                                            <label for="pin_glow"></label>
                                                        </div>
                                                        <div class="col-12 col-md-3 col-lg-3">
                                                            <div>
                                                                <small>
                                                                    Count
                                                                    <sup style="margin-left: 0px;top: -7px;"><a style="color: #000;" href="javascript:void(0)" v-b-tooltip.hover title="It will multiply the total number of segments by the selected value.">
                                                                        <svg fill="#000000" width="15px" viewBox="-1 0 19 19" xmlns="http://www.w3.org/2000/svg" class="cf-icon-svg"><path d="M16.417 9.583A7.917 7.917 0 1 1 8.5 1.666a7.917 7.917 0 0 1 7.917 7.917zM5.85 3.309a6.833 6.833 0 1 0 2.65-.534 6.787 6.787 0 0 0-2.65.534zm2.654 1.336A1.136 1.136 0 1 1 7.37 5.78a1.136 1.136 0 0 1 1.135-1.136zm.792 9.223V8.665a.792.792 0 1 0-1.583 0v5.203a.792.792 0 0 0 1.583 0z"/></svg>
                                                                    </a></sup>
                                                                </small>
                                                            </div>
                                                            <select 
                                                            name="pin_count"
                                                            v-model="formData.wheel_options.wheel.pin.count"
                                                            class="form-control"
                                                            >
                                                                <option hidden value="">Select</option>
                                                                <option value="1">1x</option>
                                                                <option value="2">2x</option>
                                                            </select>
                                                        </div>
                                                </div>
                                                </div>
                                            </div>
                                            <div class="card">
                                                <div class="card-header">
                                                    <div class="row justify-content-between mx-0">
                                                        <div>
                                                            <small><strong>CanPay Logo</strong></small>
                                                            <a @click="showSettingInfoModal('/img/wheelsettingsinfo/canpaylogo.svg')" style="color: #000;" href="javascript:void(0)" >
                                                                <svg fill="#000000" width="20px" viewBox="-1 0 19 19" xmlns="http://www.w3.org/2000/svg" class="cf-icon-svg"><path d="M16.417 9.583A7.917 7.917 0 1 1 8.5 1.666a7.917 7.917 0 0 1 7.917 7.917zM5.85 3.309a6.833 6.833 0 1 0 2.65-.534 6.787 6.787 0 0 0-2.65.534zm2.654 1.336A1.136 1.136 0 1 1 7.37 5.78a1.136 1.136 0 0 1 1.135-1.136zm.792 9.223V8.665a.792.792 0 1 0-1.583 0v5.203a.792.792 0 0 0 1.583 0z"/></svg>
                                                            </a>
                                                        </div>
                                                        <DesignTemplate
                                                        :all-templates="allTemplates"
                                                        label="Template"
                                                        type="canpaylogo"
                                                        v-model="formData.wheel_options"
                                                        />
                                                    </div>
                                                </div>
                                                <div class="card-body">
                                                    <div class="row">
                                                        <div class="col-12 col-md-6 col-lg-6">
                                                            <div>
                                                                <small>
                                                                    Logo
                                                                </small>
                                                            </div>
                                                            <Uploader 
                                                            :show-default-image="false"
                                                            :image-size="200"
                                                            image-dimension=""
                                                            v-model="formData.wheel_options.wheel.cp_logo"
                                                            />
                                                            <input
                                                            v-model="formData.wheel_options.wheel.cp_logo"
                                                            type="hidden"
                                                            :name="'cp_Logo'"
                                                            v-validate="'required'"
                                                            />
                                                            <span v-show="errors.has('cp_Logo')" class="text-danger">{{ getFormatedErrorMessage('cp_Logo', 'CP Logo') }}</span>
                                                        </div>
                                                        <div class="col-12 col-md-6 col-lg-6">
                                                            <div>
                                                                <small>
                                                                    Shadow
                                                                </small>
                                                            </div>
                                                            <label class="switch">
                                                                <input type="checkbox" id="cp_shadow" name="cp_shadow" v-model="formData.wheel_options.wheel.cp_shadow" true-value="1" false-value="0" class="enable-employee-login">
                                                                <span class="slider round"></span>
                                                            </label>
                                                            <label for="cp_shadow"></label>
                                                        </div>
                                                </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row" style="margin-bottom: 40px"></div>
            </div>
            <div v-else class="d-flex flex-column justify-content-center align-items-center">
                <i class="fa fa-spinner fa-spin fa-3x fa-fw spinner-default"></i>
                <p>{{fetchingLoaderText}}</p>
            </div>
        </b-modal>
        <!-- Add reward type modal end -->

        <!-- Wheel preview modal start  -->
        <b-modal
        title="Wheel Preview"
        header-text-variant="light"
        id="wheel-preview-modal"
        ref="wheel-preview-modal"
        cancel-title="Close"
        hide-header
        hide-footer
        cancel-variant="outline-secondary"
        :no-close-on-esc="true"
        :no-close-on-backdrop="true"
        >
            <WheelPreview :wheel-data="formData">
                <template v-slot:closeModal="">
                    <a class="close-modal" @click="hideModal('wheel-preview-modal')" href="javascript:void(0)">
                        <svg fill="#fff" width="20px" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M4.293,18.293,10.586,12,4.293,5.707A1,1,0,0,1,5.707,4.293L12,10.586l6.293-6.293a1,1,0,1,1,1.414,1.414L13.414,12l6.293,6.293a1,1,0,1,1-1.414,1.414L12,13.414,5.707,19.707a1,1,0,0,1-1.414-1.414Z"/></svg>
                    </a>
                </template>
            </WheelPreview>
        </b-modal>
        <!-- Wheel preview modal end  -->

        <!-- Wheel setting info modal start  -->
        <b-modal
        title="Wheel Setting Information"
        header-text-variant="light"
        id="wheel-setting-info-modal"
        ref="wheel-setting-info-modal"
        cancel-title="Close"
        hide-footer
        cancel-variant="outline-secondary"
        :no-close-on-esc="true"
        :no-close-on-backdrop="true"
        >
            <img :src="wheel_setting_info_img" class="w-100" />
        </b-modal>
        <!-- Wheel setting info modal end  -->

        <!-- Confirmation modal start -->
        <b-modal ref="confirmation-modal" hide-footer hide-header id="confirmation-modal">
        <div class="color">
          <div class="col-12 text-center">
            <svg width="100" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
            viewBox="0 0 473 473" style="enable-background:new 0 0 473 473;" xml:space="preserve">
            <g>
            <path fill="#149240" d="M317.667,214.42l5.667-86.42h20.951V38h-98.384V0H132.669v38H34.285v90h20.951l20,305h140.571
            c23.578,24.635,56.766,40,93.478,40c71.368,0,129.43-58.062,129.43-129.43C438.715,275.019,385.143,218.755,317.667,214.42z
            M162.669,30h53.232v8h-53.232V30z M64.285,68h250v30h-250V68z M103.334,403L85.301,128H293.27l-5.77,87.985
            c-61.031,10.388-107.645,63.642-107.645,127.586c0,21.411,5.231,41.622,14.475,59.43H103.334z M309.285,443
            c-54.826,0-99.43-44.604-99.43-99.43s44.604-99.429,99.43-99.429s99.43,44.604,99.43,99.429S364.111,443,309.285,443z"/>
            <polygon fill="#149240" points="342.248,289.395 309.285,322.358 276.322,289.395 255.109,310.608 288.072,343.571 255.109,376.533
            276.322,397.746 309.285,364.783 342.248,397.746 363.461,376.533 330.498,343.571 363.461,310.608 	"/></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g></svg>
          </div>
          <div class="purchaserpower-modal-text">
            <div class="d-block text-center">
              <label class="text-justify text-secondary h4">
                Are you sure ?
              </label>
              <br />
              <label class="text-justify text-secondary text-dark">
                {{confirmationText}}
              </label>
            </div>
            <div class="row">
              <div class="col-12 text-center">
                <button
                  @click="cancelConfrimModal()"
                  class="btn btn-secondary btn-md center-block mr-2"
                >
                  <label class="forgetpassword-ok-label my-0">Cancel</label>
                </button>
                <button
                  @click="confirmRemoveSegment()"
                  class="btn btn-success btn-md center-block ml-2"
                >
                  <label class="forgetpassword-ok-label my-0">Confirm</label>
                </button>
              </div>
            </div>
          </div>
        </div>
      </b-modal>
        <!-- Confirmation modal end -->

        <!-- Confirmation modal start -->
        <b-modal ref="confirmation-copy-modal" hide-footer hide-header id="confirmation-copy-modal">
        <div class="color">
          <div class="col-12 text-center">
            <svg fill="#149240" width="100px" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
            viewBox="0 0 442 442" style="enable-background:new 0 0 442 442;" xml:space="preserve">
            <g>
            <polygon points="291,0 51,0 51,332 121,332 121,80 291,80 	"/>
            <polygon points="306,125 306,195 376,195 	"/>
            <polygon points="276,225 276,110 151,110 151,442 391,442 391,225 	"/></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g>
            </svg>
          </div>
          <div class="purchaserpower-modal-text">
            <div class="d-block text-center">
              <label class="text-justify text-secondary h4">
                Are you sure ?
              </label>
              <br />
              <label class="text-justify text-secondary text-dark">
                {{confirmationText}}
              </label>
            </div>
            <div class="row">
              <div class="col-12 text-center">
                <button
                  @click="cancelConfrimCopyModal()"
                  class="btn btn-secondary btn-md center-block mr-2"
                >
                  <label class="forgetpassword-ok-label my-0">Cancel</label>
                </button>
                <button
                  @click="confirmCopySegment()"
                  class="btn btn-success btn-md center-block ml-2"
                >
                  <label class="forgetpassword-ok-label my-0">Confirm</label>
                </button>
              </div>
            </div>
          </div>
        </div>
      </b-modal>
        <!-- Confirmation modal end -->
        <!-- Consumer Modal Edit Start -->
        <b-modal
          id="reward-wheel-clone-modal"
          ref="reward-wheel-clone-modal"
          :header-text-variant="headerTextVariant"
          title="Clone Reward Wheel"
          @hidden="resetModal"
          ok-title="Save"
          ok-variant="success"
          cancel-variant="outline-secondary"
          @ok="handleOkClone"
          :no-close-on-esc="true"
          :no-close-on-backdrop="true"
        >
        <form ref="form" @submit.stop.prevent="saveCloneRewardWheel" class="needs-validation">
            <div class="row">
              <div class="col-md-6">
                <label for="user_type">
                  Reward Wheel Name
                  <span class="red">*</span>
                </label>
                <input
                  class="form-control"
                  placeholder="Enter Reward Wheel Name"
                  id="reward_wheel_name"
                  name="reward_wheel_name"
                  autocomplete="off"
                  v-model="reward_wheel_name"
                  v-validate="'required|min:3|max:24'"
                />
                <span v-show="show_reward_wheel_name_error" class="text-danger">{{reward_wheel_name_error}}</span>
              </div>
                <div class="col-md-12" v-if="wheelType == 1">
                    <label class="switch"><input type="checkbox" id="copy_participating_merchants" name="copy_participating_merchants" v-model="copy_participating_merchants" true-value="1" false-value="0" class="enable-employee-login"><span class="slider round"></span></label> <label for="copy_participating_merchants"> Copy Participating Merchants</label>
                </div>
            </div>
          </form>
        </b-modal>
    </div>
    </div>
</template>
<script>
import api from "@/api/rewardwheel.js";
import commonConstants from "@/common/constant.js";
import { validationMixin } from "vuelidate";
import { required, minLength } from "vuelidate/lib/validators";
import WheelPreview from "./WheelPreview.vue";
import Uploader from "./Uploader.vue";
import NumberInput from "./NumberInput.vue";
import ProbabilityCalculator from "./ProbabilityCalculator.vue";
import VSwatches from 'vue-swatches'
import 'vue-swatches/dist/vue-swatches.css'
import { rsort } from "semver";
import { validate } from "json-schema";
import { Container, Draggable } from "vue-smooth-dnd";
import DesignTemplate from "./DesignTemplate.vue"
import CanPayLoader from "../CustomLoader/CanPayLoader.vue"

export default {
    mixins: [validationMixin],
    data() {
        const wheelOldOptions = {}
        return {
            collapseArrow: 0,
            headerTextVariant: "light",
            submitBtnText: "Add",
            modal_header: "",
            allRewardTypes: [],
            segments: [],
            corporateParents: [],
            states: [],
            stores: [],
            disableStore: true,
            rewardWheelID: null,
            wheelOldOptions,
            formData: {
                coupon_enabled:0,
                coupon_code:null,
                reward_wheel: "",
                spin_gap_hours: "",
                state: [],
                corporate_parent: [],
                stores: [],
                status_id: '',
                is_merchant_wheel: 1,
                ach_billable: 0,
                is_merchant_funded: 0,
                use_as_generic_points: 0,
                rewardSegments: [{
                    segment_id: "",
                    text: "",
                    value: "",
                    probability: 0,
                    image: "",
                    image_url: "",
                    set_as_default_image: 0,
                    color: "#C1FCC7",
                    use_for_same_store: 0,
                    description: "",
                    sequence: 0
                }],
                wheel_options: { 
                    "header": {
                        "color": "#006925",
                        "page_title": "Spin To Win",
                        "page_title_size": 34,
                        "header_transparent": 1,
                        "header_type":"text",
                        "header_sub_title":"",
                        "header_sub_title_size":14,
                        "header_sub_title_color":"#FFFAFA",
                        "header_title_image":""
                    },
                    "spin_button": {
                        "color": "#e7e7e7",
                        "text_color": "#007EE5",
                        "spin_transparent": 0
                    },
                    "button": {
                         "wheel_bottom_button_text":"",
                         "wheel_bottom_button_background_color":"#087930",
                         "wheel_bottom_button_text_color":"#FFFAFA",
                         "wheel_bottom_button_text_size":7,
                    },
                    "footer": {
                        "color": "#006925",
                        "footer_transparent": 1
                    },
                    "page": {
                        "color_1": "#148f3f",
                        "color_2": "#148f3f",
                        "color_3": "#148f3f",
                        "image": null,
                        "is_image": 0
                    },
                    "sparkles": {
                        "show": 0
                    },
                    "wheel": {
                        "outer": {
                            "color": "#c5c5c5",
                            "size": 10
                        },
                        "inner": {
                            "color_1": "#3c3c3c",
                            "color_2": "#3c3c3c",
                            "color_3": "#3c3c3c",
                            "size": 20
                        },
                        "pin": {
                            "color": "#ffc100",
                            "size": 6,
                            "glow": 0,
                            "count": 1
                        },
                        "inner_shadow": 0,
                        "outer_shadow": 0,
                        "pointer_arrow": "https://s3.us-east-2.amazonaws.com/com.canpaydebit.dev.public-uploaded-files/reward_wheel/arrow2.svg",
                        "pointer_shadow": 0,
                        "cp_logo": "https://s3.us-east-2.amazonaws.com/com.canpaydebit.dev.public-uploaded-files/reward_wheel/cp.svg",
                        "cp_shadow": 0
                    }
                }
            },
            showReloadBtn: false,
            constants: commonConstants,
            fetchingLoader: false,
            fetchingLoaderText: "",
            confirmationText: "",
            segmentId: undefined,
            swatches: [
                ['#00D760', '#C1FCC7', '#F493A7', '#F891A6', '#FFCCD5' ],
                ['#8b5aff', '#a27bff', '#b99cff', '#d0bdff', '#e8deff' ],
                ['#51e5db', '#74ebe3', '#96f0ea', '#b9f5f1', '#dcfaf8' ],
                ['#ffa51a', '#ffb748', '#ffc976', '#ffdba3', '#ffedd1', '#000', '#ffcb23', '#fff', '#007EE5' ]
            ],
            defaultSegment: '',
            totalProbability: 100,
            probabilityLeft: null,
            copiedSegment: {},
            allStatuses: [],
            allDefaultImages: [],
            storeErrors: [],
            rewardWheelAPIUrl: process.env.MIX_REWARD_WHEEL_APP_URL,
            wheel_setting_info_img: "",
            allTemplates: [],
            loading: false,
            copy_participating_merchants: false,
            reward_wheel_name: "",
            show_reward_wheel_name_error: false,
            wheelType : 1,
            reward_wheel_name_error: '',
        };
    },
    components:{
        WheelPreview,
        Uploader,
        VSwatches,
        NumberInput,
        ProbabilityCalculator,
        Container,
        Draggable,
        DesignTemplate,
        CanPayLoader
    },
    methods: {
        isJackpot(id){
            var self = this;
            let segment = self.segments.find(segement => segement.id === id);
            if(segment && segment.segment_name == 'Jackpot'){
                return true;
            }else{
                return false;
            }
        },
        isMultiplier(id){
            var self = this;
            let segment = self.segments.find(segement => segement.id === id);
            if(segment && segment.segment_name == 'Multiplier'){
                return true;
            }else{
                return false;
            }
        },
        isAmount(id){
            var self = this;
            let segment = self.segments.find(segement => segement.id === id);
            if(segment && segment.segment_name == 'Amount'){
                return true;
            }else{
                return false;
            }
        },
        isLooser(id){
            var self = this;
            let segment = self.segments.find(segement => segement.id === id);
            if(segment && segment.segment_name == 'Loser'){
                return true;
            }else{
                return false;
            }
        },
        isFreeSpin(id){
            var self = this;
            let segment = self.segments.find(segement => segement.id === id);
            if(segment && segment.segment_name == 'Free Spin'){
                return true;
            }else{
                return false;
            }
        },
        isStatusActive(id){
            var self = this;
            let status = self.allStatuses.find(status => status.id == id);
            if(status && status.code == 204){
                return true;
            }else{
                return false;
            }
        },
        isStatusInActive(id){
            var self = this;
            let status = self.allStatuses.find(status => status.id == id);
            if(status && status.code == 212){
                return true;
            }else{
                return false;
            }
        },
        onChangeMerchantWheel(val){
            if(this.formData.is_merchant_wheel == 0){
                this.getSegments({is_merchant_wheel: 1})
            }else{
                this.getSegments({is_merchant_wheel: 0})
            }
        },
        collapseSegment(index){
            var self = this;
            if(self.collapseArrow == index){
                self.collapseArrow = undefined
            }else{
                self.collapseArrow = index
            }
        },
        reloadDatatable() {
            var self = this;
            self.loadDT();
        },
        openModal(type) {
            var self = this;
            if(type == 'add'){
                self.storeErrors = []
                self.resetModal();
                self.getSegments({is_merchant_wheel: 1});
                self.getAllCorporateParents();
                self.getAllStates();
                self.getAllStatuses();
                self.getAllDefaultImages();
                self.fetchAllTemplates();
                self.fetchingLoaderText = "Please wait! we're fetching all segment details";
                self.submitBtnText = "Add";
                self.modal_header = "Add Wheel";
                self.$bvModal.show("reward-wheel-modal");
            }else{
                self.submitBtnText = "Update";
                self.modal_header = "Edit Wheel";
                self.$bvModal.show("reward-wheel-modal");
            }
        },
        showPreviewModal(){
            var self = this;
            self.$bvModal.show("wheel-preview-modal");
        },
        showSettingInfoModal(image){
            var self = this;
            self.wheel_setting_info_img = image
            self.$bvModal.show("wheel-setting-info-modal");
        },
        resetModal() {
            var self = this;
            self.formData = {
                reward_wheel: "",
                spin_gap_hours: "",
                state: [],
                corporate_parent: [],
                stores: [],
                status_id: "",
                is_merchant_wheel: 1,
                ach_billable: 0,
                is_merchant_funded: 0,
                use_as_generic_points: 0,
                rewardSegments: [{
                    segment_id: self.defaultSegment,
                    text: "",
                    value: "",
                    probability: 0,
                    image: "",
                    image_url: "",
                    set_as_default_image: 0,
                    color: "#C1FCC7",
                    use_for_same_store: 0,
                    description: "",
                    sequence: 0
                }],
                wheel_options: { 
                    "header": {
                        "color": "#006925",
                        "page_title": "Spin To Win",
                        "page_title_size": 34,
                        "header_transparent": 1,
                        "header_type":"text",
                        "header_sub_title":"",
                        "header_sub_title_size":14,
                        "header_sub_title_color":"#ffffff",
                        "header_title_image":""
                    },
                    "spin_button": {
                        "color": "#e7e7e7",
                        "text_color": "#007EE5",
                        "spin_transparent": 0
                    },
                    "button": {
                         "wheel_bottom_button_text":"",
                         "wheel_bottom_button_background_color":"#ffffff",
                         "wheel_bottom_button_text_color":"#ffffff",
                         "wheel_bottom_button_text_size":7,
                    },
                    "footer": {
                        "color": "#006925",
                        "footer_transparent": 1
                    },
                    "page": {
                        "color_1": "#148f3f",
                        "color_2": "#148f3f",
                        "color_3": "#148f3f",
                        "image": null,
                        "is_image": 0
                    },
                    "sparkles": {
                        "show": 0
                    },
                    "wheel": {
                        "outer": {
                            "color": "#c5c5c5",
                            "size": 10
                        },
                        "inner": {
                            "color_1": "#3c3c3c",
                            "color_2": "#3c3c3c",
                            "color_3": "#3c3c3c",
                            "size": 20
                        },
                        "pin": {
                            "color": "#ffc100",
                            "size": 6,
                            "glow": 0,
                            "count": 1
                        },
                        "inner_shadow": 0,
                        "outer_shadow": 0,
                        "pointer_arrow": "https://s3.us-east-2.amazonaws.com/com.canpaydebit.dev.public-uploaded-files/reward_wheel/arrow2.svg",
                        "pointer_shadow": 0,
                        "cp_logo": "https://s3.us-east-2.amazonaws.com/com.canpaydebit.dev.public-uploaded-files/reward_wheel/cp.svg",
                        "cp_shadow": 0
                    }
                }
            }
            self.rewardWheelID = null
            self.fetchingLoader = false
        },
        save(bvModalEvt) {

            var self = this;

            this.storeErrors = []

            if(self.validateSegments(self.formData.rewardSegments)){
                bvModalEvt.preventDefault();
                return false
            }

            bvModalEvt.preventDefault();

            self.$validator.validateAll().then(result => {


                if (result) {

                    let requestData = new FormData()

                    if(self.formData.id){
                        self.fetchingLoaderText = "Please wait! we're updating details";
                        requestData.append('id', self.formData.id)
                    }
                    else{
                        self.fetchingLoaderText = "Please wait! we're adding details";
                    }
                    self.fetchingLoader = true;
                    requestData.append('reward_wheel', self.formData.reward_wheel)
                    requestData.append('spin_gap_hours', self.formData.spin_gap_hours)
                    requestData.append('is_merchant_wheel', self.formData.is_merchant_wheel)
                    requestData.append('status_id', self.formData.status_id)

                    if(self.formData.is_merchant_wheel == 1){


                        requestData.append('use_as_generic_points', self.formData.use_as_generic_points == 1 ? 0 : 1)
                        requestData.append('ach_billable', self.formData.ach_billable)
                        requestData.append('is_merchant_funded', self.formData.is_merchant_funded)
                        requestData.append("coupon_enabled", self.formData.coupon_enabled);
                        requestData.append("coupon_code",self.formData.coupon_code);
                        if(self.formData.state.length > 0){
                            self.formData.state.forEach((state, index) => {
                                requestData.append('state['+index+']', state.state)
                            });
                        }

                        if(self.formData.corporate_parent.length > 0){

                            if(self.formData.stores && self.formData.stores.length > 0){
                                self.formData.stores.forEach((store, index) => {
                                    requestData.append('user_id['+index+']', store.corporate_parent_id)
                                    requestData.append('store_ids['+index+']', store.id)
                                });
                            }

                        }
                    }

                    let formProbablity = 0;

                    self.formData.rewardSegments.forEach((segment, index) => {

                        formProbablity += parseFloat(segment.probability)
                        if(segment.id){
                        requestData.append('definition_id['+index+']', segment.id)
                        }else{
                        requestData.append('definition_id['+index+']', '')
                        }
                        requestData.append('segment_id['+index+']', segment.segment_id)
                        requestData.append('text['+index+']', segment.text)
                        requestData.append('fontfamily['+index+']', "")
                        requestData.append('fontsize['+index+']', "")
                        requestData.append('textcolor['+index+']', "")
                        requestData.append('textmargin['+index+']', "")

                        if(self.isJackpot(segment.segment_id)){
                            requestData.append('value['+index+']', 0)
                        }else{
                            requestData.append('value['+index+']', segment.value ? segment.value : 0)
                        }

                        if(self.isLooser(segment.segment_id)){
                            requestData.append('value['+index+']', 0)
                        }else{
                            requestData.append('value['+index+']', segment.value ? segment.value : 0)
                        }

                        requestData.append('probability['+index+']', segment.probability)
                        if(segment.image){
                        requestData.append('image['+index+']', segment.image)
                        requestData.append('attachment['+index+']', segment.image)
                        }else{
                        requestData.append('image['+index+']', '')
                        requestData.append('attachment['+index+']', '')
                        }

                        if(segment.set_as_default_image && segment.set_as_default_image == true){
                            requestData.append('set_as_default_image['+index+']', 1)
                        }else{
                            requestData.append('set_as_default_image['+index+']', 0)
                        }

                        requestData.append('color['+index+']', segment.color)
                        requestData.append('text_orientation['+index+']', "")
                        requestData.append('use_for_same_store['+index+']', segment.use_for_same_store)
                        requestData.append('description['+index+']', segment.description)
                        if(segment.sequence){
                            requestData.append('sequence['+index+']', segment.sequence)
                        }else{
                            requestData.append('sequence['+index+']', index)
                        }
                    });
                    // not sending the value for header sub title size as ""
                    // but sending it as 0
                    if(self.formData.wheel_options.header.header_sub_title_size == null){
                        self.formData.wheel_options.header.header_sub_title_size = 14;
                    }
                    if(self.formData.wheel_options.header.header_sub_title_size == ''){
                        self.formData.wheel_options.header.header_sub_title_size = 14;
                    }
                    if(self.formData.wheel_options.header.header_sub_title_size == NaN){
                        self.formData.wheel_options.header.header_sub_title_size = 14;
                    }
                    if(self.formData.wheel_options.button.wheel_bottom_button_text_size == null){
                        self.formData.wheel_options.button.wheel_bottom_button_text_size = 7;
                    }
                    if(self.formData.wheel_options.button.wheel_bottom_button_text_size == ''){
                        self.formData.wheel_options.button.wheel_bottom_button_text_size = 7;
                    }
                    if(self.formData.wheel_options.header.header_sub_title == null){
                        self.formData.wheel_options.header.header_sub_title = '';
                    }
                    if(self.formData.wheel_options.button.wheel_bottom_button_text == null){
                        self.formData.wheel_options.button.wheel_bottom_button_text = '';
                    }
                    if(self.formData.wheel_options.button.wheel_bottom_button_text_size == null){
                        self.formData.wheel_options.button.wheel_bottom_button_text_size = '';
                    }
                    // Wheel Design Fields
                    requestData.append("header_type",self.formData.wheel_options.header.header_type);
                    requestData.append("header_sub_title",self.formData.wheel_options.header.header_sub_title);
                    requestData.append("header_sub_title_size",self.formData.wheel_options.header.header_sub_title_size);
                    requestData.append("header_sub_title_color",self.formData.wheel_options.header.header_sub_title_color);
                    requestData.append("header_title_image",self.formData.wheel_options.header.header_title_image);
                    requestData.append("wheel_bottom_button_text",self.formData.wheel_options.button.wheel_bottom_button_text);
                    requestData.append("wheel_bottom_button_background_color",self.formData.wheel_options.button.wheel_bottom_button_background_color);
                    requestData.append("wheel_bottom_button_text_color",self.formData.wheel_options.button.wheel_bottom_button_text_color);
                    requestData.append("wheel_bottom_button_text_size",self.formData.wheel_options.button.wheel_bottom_button_text_size);
                    requestData.append('header_color', self.formData.wheel_options.header.color)
                    requestData.append('header_transparent', self.formData.wheel_options.header.header_transparent == 1 ? true : false)
                    requestData.append('page_title', self.formData.wheel_options.header.page_title)
                    requestData.append('page_title_size', self.formData.wheel_options.header.page_title_size)
                    requestData.append('spin_button_color', self.formData.wheel_options.spin_button.color)
                    requestData.append('spin_button_text_color', self.formData.wheel_options.spin_button.text_color)
                    requestData.append('spin_transparent', self.formData.wheel_options.spin_button.spin_transparent == 1 ? true : false)
                    requestData.append('footer_color', self.formData.wheel_options.footer.color)
                    requestData.append('footer_transparent', self.formData.wheel_options.footer.footer_transparent == 1 ? true : false)
                    requestData.append('page_color1', self.formData.wheel_options.page.color_1)
                    requestData.append('page_color2', self.formData.wheel_options.page.color_2)
                    requestData.append('page_color3', self.formData.wheel_options.page.color_3)
                    requestData.append('page_image', self.formData.wheel_options.page.image)
                    requestData.append('is_image', self.formData.wheel_options.page.is_image)
                    requestData.append('sparkle_show', self.formData.wheel_options.sparkles.show == 1 ? true : false)
                    requestData.append('wheel_outer_color', self.formData.wheel_options.wheel.outer.color)
                    requestData.append('wheel_outer_text_size', self.formData.wheel_options.wheel.outer.size)
                    requestData.append('wheel_inner_color1', self.formData.wheel_options.wheel.inner.color_1)
                    requestData.append('wheel_inner_color2', self.formData.wheel_options.wheel.inner.color_2)
                    requestData.append('wheel_inner_color3', self.formData.wheel_options.wheel.inner.color_3)
                    requestData.append('wheel_inner_text_size', self.formData.wheel_options.wheel.inner.size)
                    requestData.append('wheel_pin_color', self.formData.wheel_options.wheel.pin.color)
                    requestData.append('wheel_pin_size', self.formData.wheel_options.wheel.pin.size)
                    requestData.append('wheel_pin_glow', self.formData.wheel_options.wheel.pin.glow == 1 ? true : false)
                    requestData.append('wheel_pin_count', self.formData.wheel_options.wheel.pin.count)
                    requestData.append('wheel_inner_shadow', self.formData.wheel_options.wheel.inner_shadow == 1 ? true : false)
                    requestData.append('wheel_outer_shadow', self.formData.wheel_options.wheel.outer_shadow == 1 ? true : false)
                    requestData.append('wheel_pointer_arrow', self.formData.wheel_options.wheel.pointer_arrow)
                    requestData.append('wheel_pointer_shadow', self.formData.wheel_options.wheel.pointer_shadow == 1 ? true : false)
                    requestData.append('wheel_cp_logo', self.formData.wheel_options.wheel.cp_logo)
                    requestData.append('wheel_cp_shadow', self.formData.wheel_options.wheel.cp_shadow == 1 ? true : false)

                   
                    if(this.isDesginObjectChanged(self.formData.wheel_options, self.wheelOldOptions)){
                        requestData.append('design_changes', false)
                    }else{
                        requestData.append('design_changes', true)
                    }

                    if(!self.formData.id){
                        this.loading = true;
                        api
                        .createRewardWheel(requestData)
                        .then(response => {
                            if (response.code == 200) {
                                self.fetchingLoader = false;
                                $("#rewardWheelTable").DataTable().ajax.reload(null, false);
                                self.$bvModal.hide("reward-wheel-modal");
                                self.resetModal();
                                success(response.message);
                            } else {
                                self.fetchingLoader = false;
                                error(response.message);
                                
                            }
                            this.loading = false;
                        })
                        .catch(err => {
                            this.loading = false;
                            self.fetchingLoader = false;
                            if(err.response.data.data){
                                err.response.data.data.forEach(error => {
                                    this.storeErrors += '<p>'+error.reward_wheel_store+'</p>'
                                });
                                self.$swal({
                                    title: '<p>'+err.response.data.message+'</p>',
                                    html: this.storeErrors,
                                    width: 600,
                                    icon: 'error'
                                })
                            }else{
                                error(err.response.data.message);
                            }
                        });
                    }else{
                        this.loading = true;
                        api
                        .updateRewardWheel(requestData)
                        .then(response => {
                            if (response.code == 200) {
                                self.fetchingLoader = false;
                                $("#rewardWheelTable").DataTable().ajax.reload(null, false);
                                self.$bvModal.hide("reward-wheel-modal");
                                self.resetModal();
                                success(response.message);
                            } else {
                                self.fetchingLoader = false;
                                error(response.message);
                            }
                            this.loading = false;
                        })
                        .catch(err => {
                            self.fetchingLoader = false;
                            this.loading = false;
                            if(err.response.data.data){
                                err.response.data.data.forEach(error => {
                                    this.storeErrors += '<p>'+error.reward_wheel_store+'</p>'
                                });
                                self.$swal({
                                    title: '<p>'+err.response.data.message+'</p>',
                                    html: this.storeErrors,
                                    width: 600,
                                    icon: 'error'
                                })
                            }else{
                                error(err.response.data.message);
                            }
                        });
                    }
                }else{
                    // Log the validation errors
                    console.error(self.$validator.errors.items);

                    // Optionally, iterate through errors to display or handle them
                    self.$validator.errors.items.forEach(error => {
                        console.log(`Field: ${error.field}, Message: ${error.msg}`);
                    });
                    self.fetchingLoader = false;
                    error("Form is not valid! Please check the fields.");
                }
            })
            .catch(err => {
                self.fetchingLoader = false;
                error(err);
            });

        },
        isDesginObjectChanged(formObject, oldObject){

            let str1 = formObject
            str1.wheel.outer.size = parseInt(formObject.wheel.outer.size)
            str1.wheel.inner.size = parseInt(formObject.wheel.inner.size)
            str1.wheel.pin.size = parseInt(formObject.wheel.pin.size)
            str1.wheel.pin.count = parseInt(formObject.wheel.pin.count)

            str1 = JSON.stringify(str1);
            let str2 = JSON.stringify(oldObject);

            if(str1 === str2){
                return true;
            }else{
                return false;
            }
        },
        validateSegments(segments){

            var errors = []

            segments.forEach((segment, index) => {

                $("#segmentHeaderError"+index).removeClass('segment-header-error')
                $("#segmentError"+index).text('')

                $("#segmentProbabilityError"+index).removeClass('segment-probability-error')
                $("#segmentProbabilityError"+index).text('')

                if(this.isJackpot(segment.segment_id) || this.isLooser(segment.segment_id)){
                    if(
                        segment.text &&
                        segment.probability &&
                        segment.image
                    ){
                        
                        if(parseFloat(segment.probability) == 0){
                            $("#segmentHeaderError"+index).addClass('segment-header-error')
                            var errorMsg = `There is a validation error in the segment ${index+1}!`
                            $("#segmentError"+index).text(errorMsg)
                            $("#segmentProbabilityError"+index).addClass('segment-probability-error')
                            $("#segmentProbabilityError"+index).text('Probability should not be 0')
                            errors.push(true)

                        }
                        errors.push(false)
                    }else{
                        $("#segmentHeaderError"+index).addClass('segment-header-error')
                        var errorMsg = `There is a validation error in the segment ${index+1}!`
                        $("#segmentError"+index).text(errorMsg)
                    }
                }else{
                    if(
                        segment.text &&
                        segment.value &&
                        segment.probability &&
                        segment.image
                    ){
                        
                        if(parseFloat(segment.probability) == 0){
                            $("#segmentHeaderError"+index).addClass('segment-header-error')
                            var errorMsg = `There is a validation error in the segment ${index+1}!`
                            $("#segmentError"+index).text(errorMsg)
                            $("#segmentProbabilityError"+index).addClass('segment-probability-error')
                            $("#segmentProbabilityError"+index).text('Probability should not be 0')
                            errors.push(true)
                        }
                        errors.push(false)
                    }else{
                        $("#segmentHeaderError"+index).addClass('segment-header-error')
                        var errorMsg = `There is a validation error in the segment ${index+1}!`
                        $("#segmentError"+index).text(errorMsg)
                    }
                }

            });

            if(errors.every(error => error === false)){
                return false
            }else{
                return true
            }

        },
        addSegmentRow(){
            var self = this;

            if(self.formData.rewardSegments.slice(-1)[0].color == '#00D760'){
                var color = '#C1FCC7';
            }else{
                var color = '#00D760';
            }

            self.formData.rewardSegments.push({
                segment_id: self.defaultSegment,
                text: "",
                value: "",
                probability: 0,
                image: "",
                image_url: "",
                set_as_default_image: 0,
                color: color,
                use_for_same_store: 0,
                description: "",
                sequence: self.formData.rewardSegments.length+1
            });
        },
        copySegment(segment){
            var self = this;
            self.copiedSegment = segment
            self.confirmationText = "Are you sure want to copy this segment?"
            self.$bvModal.show("confirmation-copy-modal");
        },
        confirmCopySegment(){
            var self = this;

            if(self.formData.rewardSegments.slice(-1)[0].color == '#00D760'){
                var color = '#C1FCC7';
            }else{
                var color = '#00D760';
            }

            self.formData.rewardSegments.push({
                segment_id: self.copiedSegment.segment_id,
                text: self.copiedSegment.text,
                value: self.copiedSegment.value,
                probability: self.copiedSegment.probability,
                image: self.copiedSegment.image,
                image_url: self.copiedSegment.image_url,
                set_as_default_image: self.copiedSegment.set_as_default_image,
                color: color,
                use_for_same_store: self.copiedSegment.use_for_same_store,
                description: self.copiedSegment.description,
                sequence: self.formData.rewardSegments.length+1
            });
            self.copiedSegment = {}
            self.$bvModal.hide("confirmation-copy-modal");
        },
        cancelConfrimCopyModal(){
            var self = this;
            self.copiedSegment = {}
            self.$bvModal.hide("confirmation-copy-modal");
        },
        showModal(modal){
            this.$bvModal.show(modal);
        },
        hideModal(modal){
            this.$bvModal.hide(modal);
        },
        deleteSegmentRow(id) {
            var self = this;
            self.segmentId = id
            self.confirmationText = "Are you sure want to remove this segment?"
            self.$bvModal.show("confirmation-modal");
        },
        confirmRemoveSegment(){
            var self = this;
            self.formData.rewardSegments.splice(self.segmentId,1);
            self.segmentId = undefined;
            self.$bvModal.hide("confirmation-modal");
        },
        cancelConfrimModal(){
            var self = this;
            self.segmentId = undefined;
            self.$bvModal.hide("confirmation-modal");
        },
        loadDT: function () {
          var self = this;
          $("#rewardWheelTable").DataTable({
            pagingType: "simple_numbers",
            processing: true,
            serverSide: true,
            destroy: true,
            columnDefs: [
                { orderable: false, targets: [3] },
                { className: "dt-center", targets: [ 1, 2, 3] },
                { className: 'dt-left', targets: [0] },
            ],
            order: [[2, "desc"]],
            orderClasses: false,
            language: {
              processing:
                '<i class="fa fa-spinner fa-spin fa-3x fa-fw"></i><span class="sr-only">Loading...</span> ',
              emptyTable: "No Reward Wheel Available.",
              search: "_INPUT_",
              searchPlaceholder: "Search records",
              oPaginate: {
                sNext: '<i class="fas fa-angle-double-right"></i>',
                sPrevious: '<i class="fas fa-angle-double-left"></i>',
              },
              sLengthMenu:
                "<label class='label_dropdown_dt'>Per page</label> _MENU_",
            },
            ajax: {
              headers: {
                Authorization: "Bearer " + localStorage.getItem("token"),
              },
              url: self.rewardWheelAPIUrl + "/api/admin/getrewardwheels",
              type: "POST",
              data: { _token: "{{csrf_token()}}" },
              dataType: "json",
              dataSrc: function (result) {
                self.showReloadBtn = false;
                self.allRewardTypes = result.data;
                return self.allRewardTypes;
              },
              error: function (data) {
                error(commonConstants.datatable_error);
                $("#rewardWheelTable_processing").hide();
                self.showReloadBtn = true;
              },
            },
            columns: [
                { data: "reward_wheel" },
                { data: "no_of_segments" },
                { data: "status" },
                {
                    render: function(data, type, full, meta) {
                        // edit button
                        var buttons =
                            '<b-button data-reward-type-id="' +
                            full.editID +
                            '" class="editRewardWheelDetails custom-edit-btn" title="Edit Reward Wheel Details" variant="outline-success"><i class="nav-icon fas fa-edit"></i></b-button>';

                        // Conditionally add the clone button based on the value of full.is_merchant_wheel
                        if (full.is_merchant_wheel == 1) {
                            buttons += '&nbsp;&nbsp;<b-button data-template-id="' + full.editID + '" data-wheel-type="' + full.is_merchant_wheel + '" class="cloneRewardWheel custom-edit-btn" title="Clone Reward Wheel" variant="outline-success"><i class="nav-icon fas fa-clone"></i></b-button>';
                        }

                        return buttons;
                    }
                }
            ],
          });

          $("#rewardWheelTable").on("page.dt", function () {
            $("html, body").animate({ scrollTop: 0 }, "slow");
            $("th:first-child").focus();
          });

          //Search in the table only after 3 characters are typed
          // Call datatables, and return the API to the variable for use in our code
          // Binds datatables to all elements with a class of datatable
          var dtable = $("#rewardWheelTable").dataTable().api();

          // Grab the datatables input box and alter how it is bound to events
          $(".dataTables_filter input")
            .unbind() // Unbind previous default bindings
            .bind("input", function (e) {
              // Bind our desired behavior
              // If the length is 3 or more characters, or the user pressed ENTER, search
              if (this.value.length >= 3 || e.keyCode == 13) {
                // Call the API search function
                dtable.search(this.value).draw();
              }
              // Ensure we clear the search if they backspace far enough
              if (this.value == "") {
                dtable.search("").draw();
              }
              return;
            });
        },
        editRewardWheel(){
            var self = this;
            $(document).on("click", ".editRewardWheelDetails", function(e) {

                self.rewardWheelID = $(e.currentTarget).attr("data-reward-type-id");

                self.formData.corporate_parent = []
                self.formData.stores = []
                self.formData.rewardSegments = []
                self.storeErrors = []

                Promise.all([
                    self.getAllCorporateParents(),
                    self.getAllStates(),
                    self.getAllStatuses(),
                    self.getAllDefaultImages(),
                    self.getSingleRewardWheel(self.rewardWheelID),
                    self.fetchAllTemplates(self.rewardWheelID)
                ])
                .then(values => {
                    self.getSegments({is_merchant_wheel: self.formData.is_merchant_wheel}),
                    self.fetchingLoaderText = "Please wait! we're fetching reward wheel details";
                    self.submitBtnText = "Update";
                    self.modal_header = "Edit Wheel";
                    self.$bvModal.show("reward-wheel-modal");
                })
                .catch(errors => {
                });
            });
        },
        cloneRewardWheel(){
            var self = this;
            $(document).on("click", ".cloneRewardWheel", function(e) {
                self.rewardWheelID = $(e.currentTarget).attr("data-template-id");
                self.wheelType = $(e.currentTarget).attr("data-wheel-type");
                self.copy_participating_merchants = 0;
                self.reward_wheel_name = '';
                self.show_reward_wheel_name_error = false;
                self.reward_wheel_name_error = '';
                self.$bvModal.show("reward-wheel-clone-modal");
            });
        },
        handleOkClone(bvModalEvt) {
            var self = this;
            // Prevent modal from closing
            bvModalEvt.preventDefault();
            const minLength = 3;
            const maxLength = 24;

            // Check the length of the input value
            if (self.reward_wheel_name == "") {
                self.show_reward_wheel_name_error = true;
                self.reward_wheel_name_error = `Please enter reward wheel name`;
            } else if (self.reward_wheel_name.length < minLength) {
                self.show_reward_wheel_name_error = true;
                self.reward_wheel_name_error = `Must be at least ${minLength} characters.`;
            } else if (self.reward_wheel_name.length > maxLength) {
                self.show_reward_wheel_name_error = true;
                self.reward_wheel_name_error = `Must not exceed ${maxLength} characters.`;
            } else {
                self.reward_wheel_name_error = ''; // Clear error if valid
                self.show_reward_wheel_name_error = false;
                self.saveCloneRewardWheel();
            }
        },
        saveCloneRewardWheel(){
            this.$validator.validateAll().then((result) => {
                var self = this;
                var requestData = {
                    reward_wheel: self.reward_wheel_name,
                    id: self.rewardWheelID,
                    copy_participating_merchants: self.copy_participating_merchants
                }

                this.loading = true;
                api
                .cloneRewardWheel(requestData)
                .then(response => {
                    if (response.code == 200) {
                        self.loading = false;
                        self.$bvModal.hide("reward-wheel-clone-modal");
                        self.loadDT();
                        success(response.message);
                    } else {
                        self.loading = false;
                        error(response.message);

                    }
                    this.loading = false;
                })
                .catch(err => {
                    this.loading = false;
                    if(err.response.data.data){
                        err.response.data.data.forEach(error => {
                            this.storeErrors += '<p>'+error.reward_wheel_store+'</p>'
                        });
                        self.$swal({
                            title: '<p>'+err.response.data.message+'</p>',
                            html: this.storeErrors,
                            width: 600,
                            icon: 'error'
                        })
                    }else{
                        error(err.response.data.message);
                    }
                });
            });
        },
        getSingleRewardWheel(id){
            return new Promise((resolve, reject) => {
                var self = this;
                self.fetchingLoader = true

                var request = {}
                request.id = id
                this.loading = true;
                api
                .getSingleRewardWheel(request)
                .then(response => {
                    if (response.code == 200) {
                        self.formData.id = response.data.rewardWheel.id
                        self.formData.reward_wheel = response.data.rewardWheel.reward_wheel
                        self.formData.spin_gap_hours = response.data.rewardWheel.spin_gap_hours
                        self.formData.status = response.data.rewardWheel.status
                        self.formData.status_id = response.data.rewardWheel.status_id
                        self.formData.no_of_divisions = response.data.rewardWheel.no_of_divisions
                        self.formData.is_merchant_wheel = response.data.rewardWheel.is_merchant_wheel
                        self.formData.ach_billable = response.data.rewardWheel.ach_billable
                        self.formData.is_merchant_funded = response.data.rewardWheel.is_merchant_funded
                        self.formData.rewardSegments = response.data.rewardWheelDivisions
                        self.formData.use_as_generic_points = response.data.rewardWheel.use_as_generic_points == 0 ? 1 : 0
                        self.formData.coupon_enabled = response.data.rewardWheel.coupon_enabled;
                        self.formData.coupon_code = response.data.rewardWheel.coupon_code;
                        // Ensure header_sub_title_size has a valid number
                        if (!response.data.reward_wheel_design.header.header_sub_title_size || isNaN(response.data.reward_wheel_design.header.header_sub_title_size)) {
                            response.data.reward_wheel_design.header.header_sub_title_size = 14;
                        }
                        // Ensure wheel_bottom_button_text_size has a valid number
                        if (!response.data.reward_wheel_design.button.wheel_bottom_button_text_size || isNaN(response.data.reward_wheel_design.button.wheel_bottom_button_text_size)) {
                            response.data.reward_wheel_design.button.wheel_bottom_button_text_size = 7;
                        }
                        const designObject1 = JSON.parse(JSON.stringify(response.data.reward_wheel_design))
                        const designObject2 = JSON.parse(JSON.stringify(response.data.reward_wheel_design))
                        // Set Wheel Options To OLD Object 
                        self.wheelOldOptions = JSON.parse(JSON.stringify(designObject1));
                        self.wheelOldOptions.sparkles.show = self.wheelOldOptions.sparkles.show == true ? 1 : 0
                        self.wheelOldOptions.wheel.cp_shadow = self.wheelOldOptions.wheel.cp_shadow == true ? 1 : 0
                        self.wheelOldOptions.wheel.inner_shadow = self.wheelOldOptions.wheel.inner_shadow == true ? 1 : 0
                        self.wheelOldOptions.wheel.outer_shadow = self.wheelOldOptions.wheel.outer_shadow == true ? 1 : 0
                        self.wheelOldOptions.wheel.pointer_shadow = self.wheelOldOptions.wheel.pointer_shadow == true ? 1 : 0
                        self.wheelOldOptions.wheel.pin.glow = self.wheelOldOptions.wheel.pin.glow == true ? 1 : 0
                        self.wheelOldOptions.header.header_transparent = self.wheelOldOptions.header.header_transparent == true ? 1 : 0
                        self.wheelOldOptions.footer.footer_transparent = self.wheelOldOptions.footer.footer_transparent == true ? 1 : 0
                        self.wheelOldOptions.spin_button.spin_transparent = self.wheelOldOptions.spin_button.spin_transparent == true ? 1 : 0

                        // Set Wheel Options To FORM Object 
                        self.formData.wheel_options = JSON.parse(JSON.stringify(designObject2));
                        self.formData.wheel_options.sparkles.show = self.formData.wheel_options.sparkles.show == true ? 1 : 0
                        self.formData.wheel_options.wheel.cp_shadow = self.formData.wheel_options.wheel.cp_shadow == true ? 1 : 0
                        self.formData.wheel_options.wheel.inner_shadow = self.formData.wheel_options.wheel.inner_shadow == true ? 1 : 0
                        self.formData.wheel_options.wheel.outer_shadow = self.formData.wheel_options.wheel.outer_shadow == true ? 1 : 0
                        self.formData.wheel_options.wheel.pointer_shadow = self.formData.wheel_options.wheel.pointer_shadow == true ? 1 : 0
                        self.formData.wheel_options.wheel.pin.glow = self.formData.wheel_options.wheel.pin.glow == true ? 1 : 0
                        self.formData.wheel_options.header.header_transparent = self.formData.wheel_options.header.header_transparent == true ? 1 : 0
                        self.formData.wheel_options.footer.footer_transparent = self.formData.wheel_options.footer.footer_transparent == true ? 1 : 0
                        self.formData.wheel_options.spin_button.spin_transparent = self.formData.wheel_options.spin_button.spin_transparent == true ? 1 : 0

                        if(self.formData.wheel_options.page.image){
                            self.wheel_bg_image = 0
                        }else{
                            self.wheel_bg_image = 1
                        }

                        if(response.data.rewardWheel.is_merchant_wheel == 1){
                            if(response.data.rewardStores && response.data.rewardStores[0]){

                                // find & set old stores
                                // old cps
                                var cp_ids = []
                                var state_codes = []

                                response.data.rewardStores.forEach(rewardStore => {
                                    var cpUser = this.corporateParents[0].data.find(cp => cp.user_id == rewardStore.user_id);
                                    var stateInfo = this.states[0].data.find(state => state.state == rewardStore.state);

                                    if (cpUser && cpUser.user_id) {
                                        cp_ids.push({ id: cpUser.user_id });
                                    }

                                    if (stateInfo && stateInfo.state) {
                                        state_codes.push({ state: stateInfo.state });
                                    }
                                });

                                // remove duplicates from cp array
                                cp_ids = cp_ids.filter((value, index1, cp) =>
                                    index1 === cp.findIndex((t) => (
                                        t.id === value.id
                                    ))
                                )
                                // remove duplicates from state array
                                state_codes = state_codes.filter((value, index2, state) =>
                                    index2 === state.findIndex((t) => (
                                        t.state === value.state
                                    ))
                                )

                                Promise.all([
                                    // OLD STATE
                                    this.getAllStores(state_codes, cp_ids, response.data.rewardStores)
                                ])
                                .then(values => {
                                    // find & set old corporate parents
                                    this.setCorporateParents(response.data.rewardStores)
                                    // find & set old states
                                    this.setStates(response.data.rewardStores)
                                })
                                .catch(errors => {
                                });

                            }
                        }


                        self.formData.rewardSegments.forEach((division, i) => {
                        self.formData.rewardSegments[i]['image_url'] = division.image
                        });
                        resolve(true)
                    } else {
                        error(response.message);
                        reject(false)
                    }
                    self.fetchingLoader = false
                    this.loading = false;
                })
                .catch(err => {
                    self.fetchingLoader = false
                    this.loading = false;
                    error(err);
                    reject(false)
                });
            })
        },
        setStates(rewardStores){
            this.formData.state = []
            rewardStores.forEach(rewardStore => {
                this.formData.state.push(this.states[0].data.find(state => state.state == rewardStore.state))
            });

            // remove duplicates from state array
            this.formData.state = this.formData.state.filter((value, index, self) =>
                index === self.findIndex((t) => (
                    t.state === value.state
                ))
            )
        },
        setCorporateParents(rewardStores){
            rewardStores.forEach(rewardStore => {
                this.formData.corporate_parent.push(this.corporateParents[0].data.find(cp => cp.user_id == rewardStore.user_id))
            });

            // remove duplicates from cp array
            this.formData.corporate_parent = this.formData.corporate_parent.filter((value, index, self) =>
                index === self.findIndex((t) => (
                    t.user_id === value.user_id
                ))
            )
        },
        getSegments(payload){
            return new Promise((resolve, reject) => {
                var self = this;
                self.fetchingLoader = false
                self.loading = true;
                api
                .getSegments(payload)
                .then(response => {
                    if (response.code == 200) {
                        self.segments = response.data
                        resolve(true)
                    } else {
                        error(response.message);
                        reject(false)
                    }
                    self.fetchingLoader = false
                    self.loading = false;
                })
                .catch(err => {
                    self.fetchingLoader = false
                    self.loading = false;
                    error(err);
                    reject(false)
                });
            })
        },
        getAllCorporateParents(){
            return new Promise((resolve, reject) => {
                var self = this;
                self.fetchingLoader = true
                self.loading = true;
                api
                .getAllCorporateParents()
                .then(response => {
                    if (response.code == 200) {
                        self.corporateParents = [{
                            label: 'Select All Corporate Parents',
                            data: response.data
                        }]
                        resolve(true)
                    } else {
                        error(response.message);
                        reject(false)
                    }
                    self.fetchingLoader = false
                    self.loading = false;
                })
                .catch(err => {
                    self.fetchingLoader = false
                    self.loading = false;
                    error(err);
                    reject(false)
                });
            })
        },
        getAllStates(){
            return new Promise((resolve, reject) => {
                var self = this;
                self.fetchingLoader = true
                self.loading = true;
                api
                .getAllStates()
                .then(response => {
                    if (response.code == 200) {
                        self.states = [{
                            label: 'Select All States',
                            data: response.data
                        }]
                        resolve(true)
                    } else {
                        error(response.message);
                        reject(false)
                    }
                    self.loading = false;
                    self.fetchingLoader = false
                })
                .catch(err => {
                    self.fetchingLoader = false
                    self.loading = false;
                    error(err);
                    reject(false)
                });
            })
        },
        getAllStatuses(){
            return new Promise((resolve, reject) => {
                var self = this;
                self.loading = true;
                api
                .getAllStatuses()
                .then(response => {
                    if (response.code == 200) {
                        self.allStatuses = response.data
                        resolve(true)
                    } else {
                        error(response.message);
                        reject(false)
                    }
                    self.loading = false;
                })
                .catch(err => {
                    self.loading = false;
                    error(err);
                    reject(false)
                });
            })
        },
        getAllDefaultImages(){
            return new Promise((resolve, reject) => {
                var self = this;
                self.loading  = true;
                api
                .getAllDefaultImages()
                .then(response => {
                    if (response.code == 200) {
                        self.allDefaultImages = response.data
                        resolve(true)
                    } else {
                        error(response.message);
                        reject(false)
                    }
                    self.loading = false;
                })
                .catch(err => {
                    self.loading = false;
                    error(err);
                    reject(false)
                });
            })
        },
        stateCustomName (data) {
        return data.state
        },
        cpCustomName (data) {
        return data.first_name
        },
        storeCustomName (data) {
        return data && data.retailer ? data.retailer + ' - ' + data.state : ''
        },
        getAllStores(states, cp_ids, selectedStores = null){
            return new Promise((resolve, reject) => {
                var self = this;
                self.disableStore = true
                self.loading = true;
                api
                .getAllStores(states, cp_ids)
                .then(response => {
                    if (response.code == 200) {

                        if(response.data.length > 0){
                            self.disableStore = false
                            self.stores = [{
                                label: 'Select All Stores',
                                data: response.data
                            }]

                            // console.log(selectedStores);

                            // self.formData.stores.push({retailer: 'Select All Stores', id: '1'})
                            if(selectedStores != null && selectedStores.length > 0){
                                self.formData.stores = []
                                var oldStores = []
                                selectedStores.forEach((store, i) => {
                                    self.formData.stores.push(response.data.filter(st => st.id == store.store_id)[0])
                                });
                            }
                            resolve(true)
                        }else{
                            error('No stores found!')
                            reject(false)
                        }
                        

                    } else {
                        self.disableStore = false
                        error(response.message);
                        reject(false)
                    }
                    self.fetchingLoader = false
                    self.loading = false;
                })
                .catch(err => {
                    self.fetchingLoader = false
                    self.loading = false;
                    error(err);
                    reject(false)
                });
            });
        },
        onSelectSegment(val){
            var self = this;
            self.formData.rewardSegments.forEach((rewardSegment, i) => {
                if(this.isLooser(rewardSegment.segment_id)){
                    self.formData.rewardSegments[i].value = 0
                }
            });
        },
        segmentName(input){
            var self = this;
            let segment = self.segments.find(segement => segement.id === input.segment_id);
            if(segment){
                return ' - ' + segment.segment_name
            }else{
                return ''
            }
        },
        getFormatedErrorMessage(field, fieldTitle, segment = false, segmentNo){
            let message = ''
            this.errors.items.forEach((error, i) => {
               if(error.field == field){
                    if(error.rule == 'required'){
                        if(segment === true){
                            message = fieldTitle + ' is a mandatory field. Please fill it up in Wheel Segment ' + parseInt(segmentNo+1) + '.'
                        }else{
                            message = fieldTitle + ' is a mandatory field. Please fill it up.'
                        }
                    }
                    if(error.rule == 'max'){
                        message = 'The '+ fieldTitle +' field may not be greater than 24 characters'
                    }
                    if(error.rule == 'min'){
                        message = 'The '+ fieldTitle +' field must be at least 3 characters'
                    }
                    if(error.rule == 'min_value'){
                        message = error.msg.replace(error.field, fieldTitle)
                    }
                    if(error.rule == 'max_value'){
                        message = error.msg.replace(error.field, fieldTitle)
                    }
                    if(field == 'header_sub_title_size' && (this.formData.wheel_options.header.header_sub_title_size < 3 || this.formData.wheel_options.header.header_sub_title_size >14)){
                         message = fieldTitle + ' The Sub Title Text Size should be Between 3 to 14.'
                    }
               }
            });
            return message;
        },
        onDrop(dropResult) {
            var self = this;
            let tempItems = Object.assign(self.formData.rewardSegments)
            const movedItem = tempItems[dropResult.removedIndex]
            tempItems.splice(dropResult.removedIndex, 1)
            tempItems.splice(dropResult.addedIndex, 0, movedItem)
            tempItems = self.updateItemsOrder(tempItems)
        },
        updateItemsOrder (items) {
            return items.map((item, index) => {
                return Object.assign(item, { sequence: index })
            })
        },
        fetchAllTemplates(id){
            return new Promise((resolve, reject) => {
                var request = {}
                request.reward_wheel_id = id
                self.loading = true;
                api
                .fetchAllTemplates(request)
                .then(response => {
                    if (response.code == 200) {
                        this.allTemplates = response.data
                        resolve(true)
                    }else{
                        reject(false)
                    }
                    self.loading = false;
                })
                .catch(err => {
                    self.loading = false;
                    reject(false)
                })
            })
        }
    },
    created() {
        this.editRewardWheel();
        this.onChangeMerchantWheel();
        this.cloneRewardWheel();
    },
    mounted() {
        var self = this;
        setTimeout(function () {
        self.loadDT();
        }, 1000);
        document.title = "CanPay - Reward Types";
    },
    watch: {
        'formData.corporate_parent': function (val, oldVal){
            var self = this;
            var states = [];
            var cp_ids = [];

            if(((self.formData.corporate_parent.length > 0) && (self.formData.state.length > 0))){

                var selectedStore = [];

                self.formData.corporate_parent.forEach(cp => {
                    cp_ids.push({
                        id: cp.user_id
                    })
                    self.formData.stores.forEach(store => {
                        if(store.corporate_parent_id == cp.user_id){
                            selectedStore.push({store_id: store.id})
                        }
                    });
                });

                self.formData.state.forEach(state => {
                    states.push({
                        state: state.state
                    })
                });

                self.getAllStores(states, cp_ids, selectedStore)
            }else{
                self.formData.stores = []
                self.stores = []
                self.disableStore = true
            }
        },
        'formData.state': function (val, oldVal){
            var self = this;

            self.stores = []

            var states = [];
            var cp_ids = [];
            if(((self.formData.state.length > 0) && (self.formData.corporate_parent.length > 0))){

                self.formData.corporate_parent.forEach(cp => {
                    cp_ids.push({
                        id: cp.user_id
                    })
                });

                var selectedStore = [];

                self.formData.state.forEach(state => {
                    states.push({
                        state: state.state
                    })

                    self.formData.stores.forEach(store => {
                        if(store.state == state.state){
                            selectedStore.push({store_id: store.id})
                        }
                    });
                });

                self.getAllStores(states, cp_ids, selectedStore)
            }else{
                self.formData.stores = []
                self.stores = []
                self.disableStore = true
            }
        }
    }
};
</script>

<style scoped>
.segment-card-container{
    border: 1px solid #000;
    border-radius: 3px;
    border: 1px solid #d3d3d3;
    margin: 0;
    padding: 10px;
    background: whitesmoke;
    position: relative;
}
.btn:focus, .btn.focus {
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgb(0 123 255 / 0%);
}
.vue-swatches__trigger__wrapper {
    border: 1px solid #e5e5e5!important;
    border-radius: 12px!important;
}
.vue-swatches__trigger{
    border: 1px solid #e5e5e5!important;
    border-radius: 12px!important;
}
#confirmation-modal___BV_modal_content_{
    width: 500px!important;
}
.modal-okay-btn{
    background: #149240;
    width: 100px;
    height: auto!important;
    border: 0;
    padding: 10px 10px;
    border-radius: 6px;
  }
  .modal-okay-btn label{
    margin-bottom: 0!important;
    margin-top: 0!important;
  }
  .amount-field{
    display: flex;
    justify-content: flex-start;
    align-items: center;
    border: 1px solid #dfd3d3;
    border-radius: 3px;
    padding-left: 10px;
  }
  .amount-field input{
    border: 0!important;
  }
  .amount-field input:focus{
    border: 0!important;
    border-color: unset!important;
    box-shadow: none!important;
  }

  .segment-card{
    border: 0;
    box-shadow: unset;
  }
  .segment-card .card-header{
    border: 1px solid #dbdbdb;
  }
  .segment-card-body{
    border: 1px solid #dbdbdb;
  }

  .segment-header-error{
    border: 1px solid #fd9b9b!important;
    box-shadow: 0px 0px 5px 0px #ffbcbc!important;
  }
  .segment-form-error{
    padding: 7px 0;
    color: #d96969;
  }
  .spinner-default{
    color: #149240;
  }
  .copy-btn{
    background: #149240;
    width: 30px;
    height: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 100%;
    border: 0;
  }
  .segment-delete-btn{
    background: #f03535;
    color: #fff;
    width: 30px;
    height: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 100%;
    border: 0;
  }
  .readonlyInput{
    padding: 7px 10px;
    background: #eaeaea;
    border-radius: 3px;
    border: 1px solid #cdcdcd;
  }
  .readonlyInput.padding{
    padding: 18px 10px;
    background: #eaeaea;
    border-radius: 3px;
    border: 1px solid #cdcdcd;
  }
</style>

<style>
#wheel-preview-modal___BV_modal_content_{
    background: transparent;
    box-shadow: none;
    border: 0;
}
#wheel-preview-modal___BV_modal_body_{
    padding: 0;
    display: flex;
    justify-content: center;
    background: transparent;
}
#wheel-preview-modal___BV_modal_header_{
border-bottom: 1px solid rgba(0, 0, 0, 0)!important;
}
.vue-swatches__swatch{
    border: 1px solid #dbdbdb!important;
}
.vue-swatches__trigger{
    border: 1px solid #dbdbdb!important;
}
.amount-field .number-input input{
  border: 0!important;
}
.amount-field .number-input input:focus{
  border: 0!important;
  border-color: unset!important;
  box-shadow: none!important;
}
.segment-probability-error{
    color: #d96969;
}
.store-error{
    color: #dc3545 !important;
}

/* The container */
.rw-check-container {
  display: block;
  position: relative;
  padding-left: 35px;
  margin-bottom: 12px;
  cursor: pointer;
  font-size: 14px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Hide the browser's default checkbox */
.rw-check-container input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

/* Create a custom checkbox */
.rw-check-checkmark {
    position: absolute;
    top: 50%;
    left: 0;
    height: 20px;
    width: 20px;
    background-color: #eee;
    transform: translateY(-50%);
}

/* On mouse-over, add a grey background color */
.rw-check-container:hover input ~ .rw-check-checkmark {
  background-color: #ccc;
}

/* When the checkbox is checked, add a blue background */
.rw-check-container input:checked ~ .rw-check-checkmark {
  background-color: #149240;
}

/* Create the checkmark/indicator (hidden when not checked) */
.rw-check-checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

/* Show the checkmark when checked */
.rw-check-container input:checked ~ .rw-check-checkmark:after {
  display: block;
}

/* Style the checkmark/indicator */
.rw-check-container .rw-check-checkmark:after {
  left: 9px;
  top: 5px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 3px 3px 0;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}
</style>
