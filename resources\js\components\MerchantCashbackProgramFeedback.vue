<template>
<div>
  <div v-if="loading">
    <CanPayLoader/>
  </div>
    <div class="content-wrapper" style="min-height: 36px">
      <section class="content-header">
        <div class="container-fluid">
          <div class="row mb-2">
            <div class="col-sm-6"></div>
          </div>
        </div>
      </section>
      <div class="hold-transition sidebar-mini">
        <section class="content">
          <div class="container-fluid">
            <div class="row">
              <div class="col-12">
                <div class="card card-success">
                  <div class="card-header">
                    <h3 class="card-title">Merchant Points Program Feedback</h3>
                  </div>
                  <div class="card-body">
                    <div class="row">
                      <div class="col-md-4">
                        <div class="form-group">
                          <multiselect
                            id="cashback_program"
                            v-model="selectedCashbackProgram"
                            placeholder="Select Merchant Points Program"
                            label="cashback_program_name"
                            :options="cashbackProgramList"
                            :internal-search="true"
                          ></multiselect>
                        </div>
                      </div>
                    </div>
                    
                    <div class="row">
                      <div class="col-md-4">
                          <button
                            type="button"
                            class="btn btn-success"
                            @click="getCashbackFeedback()"
                          >
                            Search
                          </button>
                      </div>
                    </div>
                  </div>
                  <div class="card-footer">
                    
                  </div>
                  <div class="card-body">
                    <b-table-simple
                      responsive
                      show-empty
                      bordered
                      sticky-header="800px"
                      v-if="cashbackFeedback.length > 0"
                    >
                      <b-thead head-variant="light">
                        <b-tr>
                          <b-th class="text-center" width="10%">Program Name</b-th>
                          <b-th class="text-center" width="10%">Name</b-th>
                          <b-th class="text-center" width="10%">Email</b-th>
                          <b-th class="text-center" width="10%">Phone</b-th>
                          <b-th class="text-center" width="50%">Feedback</b-th>
                          <b-th class="text-center" width="10%">Created On</b-th>
                        </b-tr>
                      </b-thead>
                      <b-tbody v-for="(row, index) in cashbackFeedback" :key="index">
                        <b-tr>
                          <b-td class="text-center text-gray cashback-fix-with">{{
                            row.program_name
                          }}</b-td>
                          <b-td class="text-center text-gray cashback-fix-with">{{
                            row.name
                          }}</b-td>
                          <b-td class="text-center text-gray cashback-fix-with">{{
                            row.email
                          }}</b-td>
                          <b-td class="text-center text-gray cashback-fix-with">{{
                            row.phone
                          }}</b-td>
                          <b-td class="text-center text-gray">{{
                            truncatedText(row.feedback)
                          }}<span v-if="row.feedback.length > 50">
                              ... <a  href="javascript:void(0)"  @click="viewMoreFeedBack(row.feedback)">More</a>
                            </span>
                          </b-td>
                          <b-td class="text-center text-gray cashback-fix-with">{{
                            row.created_at
                          }}</b-td>
                        </b-tr>
                      </b-tbody>
                    </b-table-simple>
                    <div v-if="cashbackFeedback.length > 0" style="float:right">
                        <b-pagination
                            v-model="currentPage"
                            :total-rows="totalItems"
                            :per-page="perPage"
                            prev-text="Prev"
                            next-text="Next"
                            :ellipsis="true"
                            :limit="5"
                        ></b-pagination>
                    </div>
                  </div>    
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
      <b-modal
        id="show-more-feedback"
        ref="show-more-feedback"
        hide-header
        hide-footer
        >
        <!-- Custom header with close button -->
        <template v-slot:modal-title>
          <b-button class="btn-close" variant="link" @click="$refs['show-more-feedback'].hide()"></b-button>
        </template>
        <div class="card-body">
          <p>{{more_feedback}}</p>
        </div>
        
        <div class="row">
          <div class="col-12 text-center">
            <button
              type="button"
              class="btn btn-success btn-md center-block ml-2"
              style="height: 40px"
              @click="hideModal('show-more-feedback')"
            >
              <span class="purchasepower-modal-ok-label"
                >OK</span
              >
            </button>
          </div>
        </div>
      </b-modal>
    </div>
</div>
  </template>
  <script>
  import reportapi from "@/api/reports.js";
  import api from "@/api/transaction.js";
  import rewardwheelapi from "@/api/rewardwheel.js";
  import moment from "moment";
  import { validationMixin } from "vuelidate";
  import { required, minLength } from "vuelidate/lib/validators";
  import { HourGlass } from "vue-loading-spinner";
  import commonConstants from "@/common/constant.js";
  import CanPayLoader from "./CustomLoader/CanPayLoader.vue"
  export default {
    mixins: [validationMixin],
    data() {
      return {
        headerTextVariant: "light",
        selectedCashbackProgram: null,
        cashbackProgramList: [],
        showReloadBtn: false,
        cashbackFeedback: [],
        isLoading: false,
        loading: false,
        currentPage: 1,
        perPage: 10,
        totalPage: 0,
        totalItems: 0,
        more_feedback: '',
        rewardWheelAPIUrl: process.env.MIX_REWARD_WHEEL_APP_URL,
      };
    },
    components: {
      HourGlass,
      CanPayLoader
    },
    methods: {
      hideModal(modalID){
        var self = this;
        self.$bvModal.hide(modalID);
      },
      truncatedText(fullText) {
        return fullText.length > 50
          ? fullText.substring(0, 50)
          : fullText;
      },
      viewMoreFeedBack(fullText) {
        var self = this;
        self.more_feedback = fullText;
        self.$bvModal.show("show-more-feedback");
      },
      getAllCashbackPrograms() {
        var self = this;
        rewardwheelapi
          .getAllCashbackPrograms()
          .then(function (response) {
            if (response.code == 200) {
              self.cashbackProgramList = response.data.concat([{'cashback_program_name': 'Other', 'id' : 'other'}]);
            }else {
              error(response.message);
            }
          })
          .catch(function (error) {
          });
      },
      getCashbackFeedback(){
        var self = this;
        if(self.selectedCashbackProgram === null){
          var cashback_program = '';
        }else{
          var cashback_program = self.selectedCashbackProgram.id;
        }
        var request = {
          id: cashback_program,
          page: self.currentPage,
          per_page: self.perPage,
        };
        self.loading = true;
        rewardwheelapi
        .getCashbackFeedback(request)
        .then(function (response) {
          if (response.code == 200) {
            self.cashbackFeedback = response.data.data;
            self.totalPage = response.data.total_pages;
            self.totalItems = response.data.total;
            self.currentPage = response.data.current_page;
            self.$bvModal.show("show-cashback-feedback");
            self.loading = false;
          } else {
            error(response.message);
            self.loading = false;
          }
        })
        .catch(function (error) {
          // error(error);
          self.loading = false;
        });
      },
    },
    mounted() {
      var self = this;
      setTimeout(function () {
        self.getCashbackFeedback();
      }, 1000);
      self.getAllCashbackPrograms();
    },
  };
  </script>
 <style>
.cashback-fix-with {
  word-wrap: break-word !important;
  max-width: 200px !important;
}
</style>
