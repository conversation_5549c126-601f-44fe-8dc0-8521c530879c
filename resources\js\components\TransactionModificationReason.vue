<template>
<div>
    <div v-if="loading">
      <CanPayLoader/>
    </div>
    <div class="content-wrapper" style="min-height: 36px">
      <section class="content-header">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Transaction Modification Reason</h3>
                </div>
                <div class="loading" v-if="loading">
                  <hour-glass></hour-glass>
                </div>
                <!-- /.card-header -->
                <div class="card-body">
                  <div class="row">
                  <div class="col-md-4">
                    <div class="form-group">
                     <input
                        class="form-control"
                        placeholder="Reason (min 3 chars)"
                        id="reason"
                        v-model="reason"
                      />
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                        <label class="switch"><input class="enable-employee-login" type="checkbox" @click="showAllReasons" v-model="showall"><span class="slider round"></span></label> Show All
                    </div>
                  </div>
                </div>
                </div>
                  <div class="card-footer">
                    <button
                      type="button"
                      class="btn btn-success"
                      @click="searchTransactionModReason()"
                    >
                      Search
                    </button>
                    <button
                      type="button"
                      @click="reset()"
                      class="btn btn-success margin-left-5"
                    >
                      Reset
                    </button>
                    <b-button
                  @click="openModal('add')"
                  class="btn btn-success margin-left-5"
                >
                  <i class="fas fa-plus"></i> Add New Reason
                </b-button>
                  </div>
                  <div class="card-body">
                  <b-table-simple
                      responsive
                      show-empty
                      bordered
                      sticky-header="800px"
                      v-if="allReasonModel.length > 0"
                    >
                      <b-thead head-variant="light">
                        <tr>
                          <th>Reason</th>
                            <th>Reason Type</th>
                            <th>Need Approval</th>
                            <th>Created On</th>
                            <th>Updated On</th>
                            <th class="text-center">Action</th>
                        </tr>
                      </b-thead>
                      <b-tbody v-for="(row, index) in allReasonModel" :key="index">
                        <b-tr>
                          <b-td class="text-left text-gray">{{
                            row.reason
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            camelize(row.reason_type)
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.need_approval
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.created_at
                          }}</b-td>
                          <b-td class="text-center text-gray">{{
                            row.updated_at
                          }}</b-td>
                          <b-td class="text-center text-gray">
                            <a :data-reason-id="row.edit" class="editTransactionModReason custom-edit-btn" title="Edit Transaction Modification Reason" variant="outline-success" style="border:none"><i class="nav-icon fas fa-edit"></i></a>
                            <a v-if="row.is_deletable" :data-reason-id="row.edit" class="deleteTransactionModReason custom-edit-btn" title="Delete Transaction Modification Reason" variant="outline-success" style="border:none"><i class="nav-icon fas fa-trash"></i></a>
                          </b-td>
                        </b-tr>
                      </b-tbody>
                    </b-table-simple>
                    <p v-else>No data displayed. Please refine your search criteria.</p>
                    </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- CP Modal Start -->
      <b-modal
        id="cp-modal"
        ref="modal"
        :header-text-variant="headerTextVariant"
        :title="modalTitle"
        @show="resetModal"
        @hidden="resetModal"
        ok-title="Save"
        ok-variant="success"
        cancel-variant="outline-secondary"
        @ok="handleOk"
        :no-close-on-esc="true"
        :no-close-on-backdrop="true"
      >
        <form ref="form" @submit.stop.prevent="save" class="needs-validation">
          <div class="row">
            <div class="col-md-12">
            <label for="reason">
                Transaction Modification Reason
                <span class="red">*</span>
            </label>
            <textarea name="reason" type="text" v-model="modreason" v-validate="'required'" class="form-control" />
            <span v-show="errors.has('reason')" class="text-danger">{{
              errors.first("reason")
            }}</span>
          </div>
          </div>
          <div class="row">
          <div class="col-md-12">
            <label for="user_type">
              Need Approval
              <span class="red">*</span>
            </label>
            <select
              class="form-control"
              id="reason_type"
              name="reason_type"
              v-model="reason_type"
              v-validate="'required'"
            >
              <option
                v-for="(reasons, index) in reasonTypeArray"
                :key="reasons.id"
                :value="reasons.value"
              >
                {{ reasons.name }}
              </option>
            </select>
            <span v-show="errors.has('approval')" class="text-danger">{{
              errors.first("approval")
            }}</span>
          </div>
        </div>
          <div class="row">
          <div class="col-md-12">
            <label for="user_type">
              Need Approval
              <span class="red">*</span>
            </label>
            <select
              class="form-control"
              id="approval"
              name="approval"
              v-model="need_approval"
              v-validate="'required'"
            >
              <option
                v-for="(reasons, index) in approvalTypeArray"
                :key="reasons.id"
                :value="reasons.value"
              >
                {{ reasons.name }}
              </option>
            </select>
            <span v-show="errors.has('approval')" class="text-danger">{{
              errors.first("approval")
            }}</span>
          </div>
        </div>
        </form>
      </b-modal>
      <!-- CP Modal End -->


    </div>
</div>
</template>
<script>
import api from "@/api/transaction.js";
import { validationMixin } from "vuelidate";
import { required, minLength } from "vuelidate/lib/validators";
import commonConstants from "@/common/constant.js";
import CanPayLoader from "./CustomLoader/CanPayLoader.vue";
export default {
  mixins: [validationMixin],
  components:{
    CanPayLoader
  },
  data() {
    return {
      showall: false,
      modalTitle: "",
      headerTextVariant: "light",
      transactionModificationReasonDetails: {},
      allReasonModel: {},
      value: [],
      isEdit: false,
      showReloadBtn: false,
      constants: commonConstants,
      checkboxActive: true,
      checkboxActiveVoidMenu: false,
      loading:false,
      reason:"",
      modreason:"",
      editId: '',
      need_approval: 0,
      reason_type: 'decrease',
      reasonTypeArray: [
        {
            name: 'Increase',
            value: 'increase'
        },
        {
            name: 'Decrease',
            value: 'decrease'
        }
      ],
      approvalTypeArray: [
        {
            name: 'Yes',
            value: 1
        },
        {
            name: 'No',
            value: 0
        }
      ]
    };
  },
  created() {
    this.editTransactionModReason();
    this.deleteTransactionModReason();
  },
  methods: {
    camelize(str){
      return str.replace(/(?:^|\s)\w/g, function(match) {
        return match.toUpperCase();
      });
    },
    showAllReasons(){
        var self = this;
        if(self.showall == true){
            self.showall = false;
        } else{
            self.showall = true;
            self.reason = '';
        }
    },
    editTransactionModReason() {
      var self = this;
      $(document).on("click", ".editTransactionModReason", function (e) {
        self.editId = $(e.currentTarget).attr("data-reason-id");
        var editReason = self.allReasonModel.find(
                (p) => p.edit == self.editId
            );
        self.modreason = editReason.reason;
        self.reason_type = editReason.reason_type;
        self.need_approval = (editReason.need_approval == 'Yes') ? 1 : 0;
        self.modalTitle = "Edit Transaction Modification Reason";
        self.$bvModal.show("cp-modal");
        self.isEdit = true;
      });
    },
    deleteTransactionModReason(){
        var self = this;
      $(document).on("click", ".deleteTransactionModReason", function (e) {

        var deleteId = $(e.currentTarget).attr("data-reason-id");
        self.deleteTransactionModReasonApi(deleteId);
      });
    },
    openModal(type) {
      var self = this;
      if (type == "edit") {
        self.modalTitle = "Edit Transaction Modification Reason";
        //call to api to get perticular corporate details
        //this.transactionModificationReasonDetails = {};
        self.$bvModal.show("cp-modal");
      } else {
        self.modalTitle = "Add Transaction Modification Reason";
        self.isEdit = false;
        self.modreason = '';
        self.need_approval = 0;
        self.reason_type = 'decrease';
        self.$bvModal.show("cp-modal");
      }
    },
    resetModal() {
      var self = this;
      self.isEdit = false;
    },
    handleOk(bvModalEvt) {
      var self = this;
      // Prevent modal from closing
      bvModalEvt.preventDefault();
      // Trigger submit handler
      self.save();
    },
    save() {
      var self = this;
      // Exit when the form isn't valid
      this.$validator.validateAll().then((result) => {
        if (result) {
          //call to api to save or edit the details
          if(self.isEdit == true){
            self.editTransactionModReasonApi();
          } else {
            self.addTransactionModificationReasonApi();
          }


        }

      });
    },
    editTransactionModReasonApi() {
        var self = this;
        var data = {
            editId: self.editId,
            reason: self.modreason,
            need_approval: self.need_approval,
            reason_type: self.reason_type
        };
      api
        .editTransactionModReason(data)
        .then((response) => {
          if (response.code == 200) {
            success(response.message);
            $("#corporateParentsTable").DataTable().ajax.reload(null, false);
            self.$bvModal.hide("cp-modal");
            self.resetModal();
            self.searchTransactionModReason();
          } else {
            error(response.message);
          }
        })
        .catch((err) => {
          error(err.response.data.message);
        });
    },
    deleteTransactionModReasonApi(id){
        var self = this;
        var request = {
            id: id,
        };
        var r = confirm(
            "Do you want to delete this reason?"
        );
        if (r == true) {
            api
            .deletetTransactionModReason(request)
            .then((response) => {
            if (response.code == 200) {
                success(response.message);
                $("#corporateParentsTable").DataTable().ajax.reload(null, false);
                self.searchTransactionModReason();
            } else {
                error(response.message);
            }
            })
            .catch((err) => {
              error(err.response.data.message);
            });
        }
    },
    addTransactionModificationReasonApi() {
        var self = this;
        var request = {
            reason: self.modreason,
            need_approval: self.need_approval,
            reason_type: self.reason_type
        }
      api
        .addTransactionModificationReason(request)
        .then((response) => {
          if (response.code == 200) {
            success(response.message);
            $("#corporateParentsTable").DataTable().ajax.reload(null, false);
            self.$bvModal.hide("cp-modal");
            self.searchTransactionModReason();
            self.resetModal();
          } else {
            error(response.message);
          }
        })
        .catch((err) => {
          error(err.response.data.message);
        });
    },
    searchTransactionModReason(){
      var self = this;
      if((self.reason).trim().length < 3 && self.showall == false){
        error("Please provide reason (Min 3 chars)");
        return false;
      }
      var request = {
        reason: self.reason,
        showall: self.showall,
      };
      self.loading = true;
      api
      .searchTransactionModReason(request)
      .then(function (response) {
        if (response.code == 200) {
          self.allReasonModel = response.data;
          self.loading = false;
        } else {
          error(response.message);
          self.loading = false;
        }
      })
      .catch(function (error) {
        // error(error);
        self.loading = false;
      });
    },
    reset(){
      var self = this;
      self.reason = "";
      self.showall = false;
    }
  },
  mounted() {
    var self = this;
    document.title = "CanPay - Transaction Modification Reason";
  },
};
</script>

