<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Petition extends Model
{

    /**
     * __construct
     * This function will insert an alphanumeric 32 character unique id as primary key in the table whenever a data get inserted
     * @param  mixed $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        $this->id = generateUUID();

        parent::__construct($attributes);
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'petition_number',
        'consumer_id',
        'store_name',
        'store_short_name',
        'street_address',
        'apt_number',
        'city',
        'state',
        'zipcode',
        'logo_url',
        'primary_contact_person_phone',
        'primary_contact_person_name',
        'primary_contact_person_email',
        'secondary_contact_person_phone',
        'secondary_contact_person_name',
        'secondary_contact_person_email',
        'status_id',
        'onboarded_date',
        'merchant_store_id',
        'rejection_reason',
        'rejection_comments',
    ];
    public $timestamps = true;
    public $incrementing = false;
    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
}
