<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
class ManualReviewDetail extends Model
{

    /**
     * __construct
     * This function will insert an alphanumeric 32 character unique id as primary key in the table whenever a data get inserted
     * @param  mixed $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        $this->id = generateUUID();

        parent::__construct($attributes);
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'session_id',
        'user_id',
        'phone',
        'doc_front_side',
        'doc_back_side',
        'review_status'

    ];
    public $table = 'manual_review_details';
    public $incrementing = false;
}
