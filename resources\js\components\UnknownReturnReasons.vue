<template>
<div>
<div v-if="loading">
  <CanPayLoader/>
</div>
  <div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
     <section class="content-header">
        <div class="container-fluid">
          <div class="row mb-2">
            <div class="col-sm-6"></div>
          </div>
        </div>
      </section>
      <div class="hold-transition sidebar-mini">
        <section class="content">
          <div class="container-fluid">
            <div class="row">
              <div class="col-12">
                <div class="card card-success">
                  <div class="card-header">
                    <h3 class="card-title">Manage Unknown Return Codes</h3>
                    <b-button
                      class="btn-danger export-api-btn"
                      @click="reloadDatatable"
                      v-if="showReloadBtn"
                    >
                      <i class="fas fa-redo"></i> Reload
                    </b-button>
                  </div>
                  <!-- /.card-header -->
                  <div class="card-body">
                    <table
                      id="unknownReturnReasonsTable"
                      class="table"
                      style="width: 100%; white-space: normal"
                    >
                      <thead>
                        <tr>
                          <th style="width:60%">Return Code</th>
                          <th class="text-center">Created At</th>
                          <th class="text-center">Action</th>
                        </tr>
                      </thead>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
      <b-modal
      id="return-reason-modal"
      ref="modal"
      :header-text-variant="headerTextVariant"
      :title="modalTitle"
      @show="resetModal"
      @hidden="resetModal"
      ok-title="Save"
      ok-variant="success"
      cancel-variant="outline-secondary"
      @ok="handleOk"
      :no-close-on-esc="true"
      :no-close-on-backdrop="true"
    >
      <form ref="form" @submit.stop.prevent="save" class="needs-validation">
        <div class="row">
          <div class="col-md-6">
            <label for="return_code">
              Return Code
              <span class="red">*</span>
            </label>
            <input
              id="return_code"
              name="return_code"
              v-validate="'required'"
              type="text"
              v-model="reasonModel.return_code"
              readonly
              class="form-control"
            />
            <span v-show="errors.has('return_code')" class="text-danger">{{
              errors.first("return_code")
            }}</span>
          </div>

          <div class="col-md-6">
            <label for="title">
              Title
              <span class="red">*</span>
            </label>
            <input
              id="title"
              name="title"
              v-validate="'required'"
              type="text"
              v-model="reasonModel.title"
              class="form-control"
            />
            <span v-show="errors.has('title')" class="text-danger">{{
              errors.first("title")
            }}</span>
          </div>
        </div>

        <div class="row">
          <div class="col-md-12">
            <label for="description">
              Description
              <span class="red">*</span>
            </label>
            <input
              id="description"
              name="description"
              v-validate="'required'"
              type="text"
              v-model="reasonModel.description"
              class="form-control"
            />
            <span v-show="errors.has('description')" class="text-danger">{{
              errors.first("description")
            }}</span>
          </div>
        </div>

        <div class="row">
          <div class="col-md-12">
            <label for="description_for_consumer">
              Description for Consumer
              <span class="red">*</span>
            </label>
            <input
              id="description_for_consumer"
              name="description_for_consumer"
              v-validate="'required'"
              type="text"
              v-model="reasonModel.new_title"
              class="form-control"
            />
            <span v-show="errors.has('description_for_consumer')" class="text-danger">{{
              errors.first("description_for_consumer")
            }}</span>
          </div>
        </div>

        <div class="row">
          <div class="col-md-12">
            <label for="time_frame">
              Time Frame
              <span class="red">*</span>
            </label>
            <input
              id="time_frame"
              name="time_frame"
              v-validate="'required'"
              type="text"
              v-model="reasonModel.time_frame"
              class="form-control"
            />
            <span v-show="errors.has('time_frame')" class="text-danger">{{
              errors.first("time_frame")
            }}</span>
          </div>
        </div>

        <div class="row">
          <div class="col-md-7">
            <label for="canpay_represent">
              Can CanPay Represent
              <span class="red">*</span>
            </label>
            <input type="radio" name="canpay_represent" id="canpay_represent1" value="1" v-model="reasonModel.canpay_represent"> <label for="canpay_represent1">Yes</label>
            <input type="radio" name="canpay_represent" id="canpay_represent2" value="0" v-model="reasonModel.canpay_represent"> <label for="canpay_represent2">No</label>
          </div>

          <div class="col-md-5">
            <label for="new_banking">
              New Banking Required
              <span class="red">*</span>
            </label>
            <input type="radio" name="new_banking" id="new_banking1" value="1" v-model="reasonModel.new_banking"> <label for="new_banking1">Yes</label>
            <input type="radio" name="new_banking" id="new_banking2" value="0" v-model="reasonModel.new_banking"> <label for="new_banking2">No</label>
          </div>
        </div>

        <div class="row">
          <div class="col-md-7">
            <label for="new_banking_after_second_attempt">
              New Banking Required After 2nd Attempt
              <span class="red">*</span>
            </label>
            <input type="radio" name="new_banking_after_second_attempt" id="new_banking_after_second_attempt1" value="1" v-model="reasonModel.new_banking_after_second_attempt"> <label for="new_banking_after_second_attempt1">Yes</label>
            <input type="radio" name="new_banking_after_second_attempt" id="new_banking_after_second_attempt2" value="0" v-model="reasonModel.new_banking_after_second_attempt"> <label for="new_banking_after_second_attempt2">No</label>
          </div>

          <div class="col-md-5">
            <label for="bank_login">
              Bank Login Required
              <span class="red">*</span>
            </label>
            <input type="radio" name="bank_login" id="bank_login1" value="1" v-model="reasonModel.bank_login"> <label for="bank_login1">Yes</label>
            <input type="radio" name="bank_login" id="bank_login2" value="0" v-model="reasonModel.bank_login"> <label for="bank_login2">No</label>
          </div>
        </div>

        <div class="row">
          <div class="col-md-12">
            <label for="bank_login_after_second_attempt">
              Bank Login Required After 2nd Attempt
              <span class="red">*</span>
            </label>
            <input type="radio" name="bank_login_after_second_attempt" id="bank_login_after_second_attempt1" value="1" v-model="reasonModel.bank_login_after_second_attempt"> <label for="bank_login_after_second_attempt1">Yes</label>
            <input type="radio" name="bank_login_after_second_attempt" id="bank_login_after_second_attempt2" value="0" v-model="reasonModel.bank_login_after_second_attempt"> <label for="bank_login_after_second_attempt2">No</label>
          </div>
        </div>

      </form>
    </b-modal>
  </div>
</div>
</template>
<script>
import api from "@/api/return.js";
import { HourGlass } from "vue-loading-spinner";
import commonConstants from "@/common/constant.js";
import CanPayLoader from "./CustomLoader/CanPayLoader.vue"
export default {
  components: {
    HourGlass,
    CanPayLoader
  },
  data() {
    return {
      allReasonModel: {},
      reasonModel: {},
      success_message: null,
      is_visible: 0,
      showReloadBtn: false,
      constants: commonConstants,
      reasonID: null,
      modalTitle: "",
      loading:false
    };
  },
  created(){
      this.addReturnReason();
  },
  methods: {
   addReturnReason() {
      var self = this;
      $(document).on("click", ".addReturnReason", function (e) {
        self.reasonID = $(e.currentTarget).attr("data-reason-id");
        var reasondetails = self.allReasonModel.find((p) => p.edit == self.reasonID);
        self.modalTitle = "Add to Return Reason Master";
        self.$bvModal.show("return-reason-modal");
        self.reasonModel = reasondetails;
        self.reasonModel.reason_code = reasondetails.return_code;
      });
    },
    handleOk(bvModalEvt) {
      var self = this;
      // Prevent modal from closing
      bvModalEvt.preventDefault();
      // Trigger submit handler
      self.save();
    },
    save() {
      var self = this;
      // Exit when the form isn't valid
      this.$validator.validateAll().then((result) => {
        if (result) {
          self.loading = true;
            api
              .addReturnReason(self.reasonModel)
              .then((response) => {
                if (response.code == 200) {
                  success(response.message);
                  $("#unknownReturnReasonsTable").DataTable().ajax.reload(null, false);
                  self.$bvModal.hide("return-reason-modal");
                  self.resetModal();
                } else {
                  error(response.message);
                }
                self.loading = false;
              })
              .catch((err) => {
                self.loading = false;
                error(err.response.data.message);
              });
          }
      });
    },
    resetModal() {
      var self = this;
      self.reasonModel = {};
      self.reasonID = null;
    },
    reloadDatatable() {
      var self = this;
      self.loadDT();
    },
    loadDT: function () {
      var self = this;
    $("#unknownReturnReasonsTable").DataTable({
        pagingType: "simple_numbers",
        processing: true,
        serverSide: true,
        destroy: true,
        columnDefs: [
          { orderable: false, targets: [2] },
          { className: "dt-left", targets: [0] },
          { className: "dt-center", targets: [1, 2] },
        ],
        order: [[0, "asc"]],
        orderClasses: false,
        language: {
          processing:
            '<i class="fa fa-spinner fa-spin fa-3x fa-fw"></i><span class="sr-only">Loading...</span> ',
          emptyTable: "No Unknown Return Codes Available.",
          search: "_INPUT_",
          searchPlaceholder: "Search records",
          oPaginate: {
            sNext: '<i class="fas fa-angle-double-right"></i>',
            sPrevious: '<i class="fas fa-angle-double-left"></i>',
          },
          sLengthMenu:
            "<label class='label_dropdown_dt'>Per page</label> _MENU_",
        },
        ajax: {
          headers: {
            Authorization: "Bearer " + localStorage.getItem("token"),
          },
          url: "/api/unknownreturnreasonlist",
          type: "POST",
          data: { _token: "{{csrf_token()}}" },
          dataType: "json",
          dataSrc: function (result) {
            self.showReloadBtn = false;
            self.allReasonModel = result.data;
            return result.data;
          },
          error: function (data) {
            error(commonConstants.datatable_error);
            $("#unknownReturnReasonsTable_processing").hide();
            self.showReloadBtn = true;
          },
        },
        columns: [
          { data: "return_code" },
          { data: "created_at" },
          {
            render: function(data, type, full, meta) {
              return (
                '<b-button data-reason-id="' +
                full.edit +
                '" class="addReturnReason custom-edit-btn" title="Add to Return Code Master" variant="outline-success"><i class="nav-icon fas fa-plus"></i></b-button>'
              );
            }
          }
        ],
      });


      $("#unknownReturnReasonsTable").on("page.dt", function () {
        $("html, body").animate({ scrollTop: 0 }, "slow");
        $("th:first-child").focus();
      });

      //Search in the table only after 3 characters are typed
      // Call datatables, and return the API to the variable for use in our code
      // Binds datatables to all elements with a class of datatable
      var dtable = $("#unknownReturnReasonsTable").dataTable().api();

      // Grab the datatables input box and alter how it is bound to events
      $(".dataTables_filter input")
        .unbind() // Unbind previous default bindings
        .bind("input", function (e) {
          // Bind our desired behavior
          // If the length is 3 or more characters, or the user pressed ENTER, search
          if (this.value.length >= 3 || e.keyCode == 13) {
            // Call the API search function
            dtable.search(this.value).draw();
          }
          // Ensure we clear the search if they backspace far enough
          if (this.value == "") {
            dtable.search("").draw();
          }
          return;
        });
    },
  },
  mounted() {
    var self = this;
    self.loading = true;
    setTimeout(function () {
      self.loadDT();
      self.loading = false;
    }, 1000);
    document.title = "CanPay - Manage Unknown Return Codes";
  },
};
</script>
