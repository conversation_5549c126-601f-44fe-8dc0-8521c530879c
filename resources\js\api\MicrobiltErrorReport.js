const getReportOfConsumerDeclinedForBankValidation = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/getReportOfConsumerDeclinedForBankValidation', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
}

const getAutomaticRestrctedConsumerDetails = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/restrictedConsumer', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
}

const editConsumerStatus = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/updateReviewStatus', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
}
const getConsumerDetails = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/consumerDetailMicrobilt', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
}

const getOtherConsumerDetails = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/searchDifferentUser', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
}

export default {
    getReportOfConsumerDeclinedForBankValidation,
    getAutomaticRestrctedConsumerDetails,
    editConsumerStatus,
    getConsumerDetails,
    getOtherConsumerDetails
}
