import DashboardLayout from '@/components/Layout/DashboardLayout.vue';
import AuthLayout from "@/components/Layout/AuthLayout.vue";
import Login from "@/components/Login.vue";
import UserProfile from "@/components/UserProfile.vue";
import Dashboard from "@/components/Dashboard.vue";
import AlertDashboard from "@/components/AlertDashboard.vue";
import ReturnDashboard from "@/components/ReturnDashboard.vue";
import ImportManager from "@/components/ImportManager.vue";
import ImportCorporateParent from "@/components/ImportCorporateParent.vue";
import ImportAkoyaProviderID from "@/components/Akoya/ImportProviderID.vue";
import ImportMxInstitutionCode from "@/components/MX/ImportMxInstitutionCode.vue";
import MxInstitution from "@/components/MX/MxInstituition.vue";
import CorporateParent from "@/components/users/CorporateParent.vue";
import AdminUsers from "@/components/users/AdminUsers.vue";
import Consumers from "@/components/users/Consumers.vue";
import AlgoHistory from "@/components/users/AlgoHistory.vue";
import AccountOwnerInfo from "@/components/users/AccountOwnerInfo.vue";
import NotificationLogs from "@/components/users/NotificationLogs.vue";
import ConsumerViewBalance from "@/components/users/ConsumerViewBalance.vue";
import ConsumerSearch from "@/components/users/ConsumerSearch.vue";
import V1Edit from "@/components/users/V1Edit.vue";
import Stores from "@/components/Stores.vue";
import Petitions from "@/components/Petitions.vue";
import EnableDisableStores from "@/components/EnableDisableStores.vue";
import Emails from "@/components/Emails.vue";
import Notifications from "@/components/Notifications.vue";
import Audits from "@/components/Audits.vue";
import ChangePassword from "@/components/ChangePassword.vue";
import Pending from "@/components/ManualReview/Pending.vue";
import Approved from "@/components/ManualReview/Approved.vue";
import Archived from "@/components/ManualReview/Archived.vue";
import SuspectedFraud from "@/components/ManualReview/SuspectedFraud.vue";
import Transactions from "@/components/Transactions.vue";
import ConsumerTransactions from "@/components/ConsumerTransactions.vue";
import VoidTransactions from "@/components/VoidTransactions.vue";
import TransactionReport from "@/components/TransactionReport.vue";
import ACHReport from "@/components/ACHReport.vue";
import ReturnTransactions from "@/components/ReturnTransactions.vue";
import ConsumerReturns from "@/components/ConsumerReturns.vue";
import GroupReturnTransactions from "@/components/ReturnTransactionsByReturnCode.vue";
import UpdateTransactions from "@/components/UpdateTransactions.vue";
import RoutingNumbers from "@/components/BlackList/RoutingNumbers.vue";
import ImportConsumers from "@/components/DataMigration/ImportConsumers.vue";
import ImportMerchants from "@/components/DataMigration/ImportMerchants.vue";
import ImportTransactions from "@/components/DataMigration/ImportTransactions.vue";
import ImportTransactionTraceNumber from "@/components/DataMigration/ImportTransactionTraceNumber.vue";
import ImportVoidedTransactions from "@/components/DataMigration/ImportVoidedTransactions.vue";
import RegisteredMerchants from "@/components/RegisteredMerchants.vue";
import ImportIntegrator from "@/components/ImportIntegrator.vue";
import GlobalRadarPending from "@/components/GlobalRadarReview/GlobalRadarPending.vue";
import GlobalRadarApproved from "@/components/GlobalRadarReview/GlobalRadarApproved.vue";
import GlobalRadarArchived from "@/components/GlobalRadarReview/GlobalRadarArchived.vue";
import HelpdeskUsers from "@/components/users/HelpdeskUser.vue";
import V1ManualReview from "@/components/V1ManualReview/V1ManualReview.vue";
import WhiteListRoutingNumbers from "@/components/WhiteList/WhiteListRoutingNumbers.vue";
import ManualBankLinkRestrictedRoutingNumbersList from "@/components/ManualBankLinkRestrictions/RestrictedRoutingNumbersList.vue";
import BankList from "@/components/Akoya/BankList.vue";
import BankSolutionList from "@/components/Akoya/BankSolutionList.vue";
import ApiKeys from "@/components/ApiKeys.vue";
import SettlementReport from "@/components/SettlementReport.vue";
import DiscountedFeeReport from "@/components/DiscountedFeeReport.vue";
import MerchantTransactionReport from "@/components/MerchantTransactionReport.vue";
import ReturnTransactionsEmail from "@/components/ReturnTransactionsEmail.vue";
import PostConsumerReturn from "@/components/PostConsumerReturn.vue";
import ReleaseConsumers from "@/components/ReleaseConsumers.vue";
import FinicityReturns from "@/components/Reports/FinicityReturns.vue";
import ManualBankingReturns from "@/components/Reports/ManualBankingReturns.vue";
import ManualReviewReturns from "@/components/Reports/ManualReviewReturns.vue";
import ImportHolidayLists from "@/components/DataMigration/ImportHolidayLists.vue";
import ConsumerDocumentVerifyPending from "@/components/ConsumerDocument/ConsumerDocumentVerifyPending.vue";
import ConsumerDocumentVerifyRetake from "@/components/ConsumerDocument/ConsumerDocumentVerifyRetake.vue";
import ConsumerDocumentVerifyArchive from "@/components/ConsumerDocument/ConsumerDocumentVerifyArchive.vue";
import Settings from "@/components/Settings.vue";
import MonthlySalesGrowth from "@/components/MonthlySalesGrowth.vue";
import StorewiseMonthlySales from "@/components/StorewiseMonthlySales.vue";
import MerchantPointReport from "@/components/MerchantPointReport.vue";
import ReleaseNote from "@/components/ReleaseNote.vue";
import Integrator from "@/components/RemotePayment/Integrator.vue";
import EcommerceCategory from "@/components/RemotePayment/EcommerceCategory.vue";
import MerchantKeyMaster from "@/components/RemotePayment/MerchantKeyMaster.vue";
import ImportReturnTransactions from "@/components/ImportReturnTransactions.vue";
import UnknownReturnReasons from "@/components/UnknownReturnReasons.vue";
import BlacklistedAccountNumber from "@/components/BlacklistedAccountNumber.vue";
import ValidAccountNumber from "@/components/ValidAccountNumber.vue";
import SuspectedConsumers from "@/components/users/SuspectedConsumers.vue";
import ReleaseConusmersFromReturn from "@/components/ReleaseConusmersFromReturn.vue";
import StoreLocator from "@/components/StoreLocator.vue";
import UnknownRoutingnumbers from "@/components/UnknownRoutingnumbers.vue";
import TransactionModifyReason from "@/components/TransactionModificationReason.vue";
import EcommerceVoidTransactions from "@/components/EcommerceVoidTransactions.vue";
import TransactionModifyCustomReason from "@/components/TransactionModificationCustomReason.vue";
import RewardWheel from "@/components/RewardWheelMaster/List.vue";
import RewardWheelSendInvitation from "@/components/RewardWheelInvitation/List.vue";
import InvitationStatus from "@/components/RewardWheelInvitation/InvitationStatusList.vue";
import RewardWheelReport from "@/components/RewardWheelReport/List.vue";
import RewardProbabilityTest from "@/components/RewardWheelMaster/ProbabilityTest.vue";
import JackpotReset from "@/components/RewardWheelMaster/JackpotReset.vue";
import RewardWheelSearch from "@/components/RewardWheelMaster/RewardWheelSearch.vue";
import RemotePayTransactionReport from "@/components/RemotePayTransactionReport.vue";
import AchFileList from "@/components/users/AchFileList.vue";
import MicrobiltConsumerDeclinedForBankValidation from "@/components/MicrobiltErrorMenu/MicrobiltConsumerDeclinedForBankValidation.vue"
import MicrobiltReview from "@/components/MicrobiltReview/MicrobiltReview"
import AchReturn from "@/components/AchReturn.vue"
import RegistrationFailure from "@/components/users/RegistrationFailure.vue"
import BankReportSurvey from "@/components/BankReportSurvey"
import CashbackProgramReport from "@/components/MerchantCashbackProgramReport.vue";
import PointsbackReport from "@/components/PointsbackReport.vue";
import CashbackProgramFeedback from "@/components/MerchantCashbackProgramFeedback.vue";
import CashbackProgramMaster from "@/components/MerchantCashbackProgramMaster.vue";
import MxIdentificationHistory from "@/components/MxIdentificationHistory.vue";
import HealthChecks from "@/components/HealthChecks.vue";
import DeliveryFeeReport from "@/components/DeliveryReport/DeliveryFeeReport.vue";
import DeliverySettlementReport from "@/components/DeliveryReport/DeliverySettlementReport.vue"
let authPages = {
    path: "/",
    component: AuthLayout,
    name: "Authentication",
    children: [{
        path: "/login",
        name: "Login",
        component: Login
    }]
};

const routes = [{
        path: "/",
        redirect: "/login",
        name: "Home"
    },
    authPages,
    {
        path: "/",
        component: DashboardLayout,
        children: [{
                path: "userProfile",
                name: "UserProfile",
                components: { default: UserProfile }
            },
            {
                path: "dashboard",
                name: "Dashboard",
                components: { default: Dashboard }
            },
            {
                path: "alertDashboard",
                name: "AlertDashboard",
                components: { default: AlertDashboard }
            },
            {
                path: "returnDashboard",
                name: "ReturnDashboard",
                components: { default: ReturnDashboard }
            },
            {
                path: "importManager",
                name: "ImportManager",
                components: { default: ImportManager }
            },
            {
                path: "importCorporateParent",
                name: "ImportCorporateParent",
                components: { default: ImportCorporateParent }
            },
            {
                path: "corporateParent",
                name: "CorporateParent",
                components: { default: CorporateParent }
            },
            {
                path: "canpayUsers",
                name: "canpayUsers",
                components: { default: AdminUsers }
            },
            {
                path: "consumers",
                query: { email: '' },
                name: "consumers",
                components: { default: Consumers }
            },
            {
                path: "algohistory",
                query: { email: '' },
                name: "algohistory",
                components: { default: AlgoHistory }
            },
            {
                path: "accountownerinfo",
                name: "accountownerinfo",
                components: { default: AccountOwnerInfo }
            },
            {
                path: "consumerviewbalance",
                name: "consumerviewbalance",
                components: { default: ConsumerViewBalance }
            },
            {
                path: "consumersearch",
                name: "consumersearch",
                components: { default: ConsumerSearch }
            },
            {
                path: "v1edit",
                name: "v1edit",
                components: { default: V1Edit }
            },
            {
                path: "petitions",
                name: "petitions",
                components: { default: Petitions }
            },
            {
                path: "stores",
                name: "stores",
                components: { default: Stores }
            },
            {
                path: "StoreLocator",
                name: "StoreLocator",
                components: { default: StoreLocator }
            },
            {
                path: "enabledisablestores",
                name: "enabledisablestores",
                components: { default: EnableDisableStores }
            },
            {
                path: "emails",
                name: "emails",
                components: { default: Emails }
            },
            {
                path: "notifications",
                name: "notifications",
                components: { default: Notifications }
            },
            {
                path: "changePassword",
                name: "changePassword",
                components: { default: ChangePassword }
            },
            {
                path: "pending",
                query: { email: '' },
                name: "pending",
                components: { default: Pending }
            },
            {
                path: "approved",
                query: { email: '' },
                name: "approved",
                components: { default: Approved }
            },
            {
                path: "archived",
                query: { email: '' },
                name: "archived",
                components: { default: Archived }
            },
            {
                path: "suspectedfraud",
                query: { email: '' },
                name: "suspectedfraud",
                components: { default: SuspectedFraud }
            },
            {
                path: "transactions",
                name: "transactions",
                components: { default: Transactions }
            },
            {
                path: "consumertransactions",
                name: "consumertransactions",
                components: { default: ConsumerTransactions }
            },
            {
                path: "voidtransactions",
                name: "voidtransactions",
                components: { default: VoidTransactions }
            },
            {
                path: "transactionreport",
                name: "transactionreport",
                components: { default: TransactionReport }
            },
            {
                path: "achTransactionReport",
                name: "achTransactionReport",
                components: { default: ACHReport }
            },
            {
                path: "returntransactions",
                name: "returntransactions",
                components: { default: ReturnTransactions }
            },
            {
                path: "consumerreturns",
                name: "consumerreturns",
                components: { default: ConsumerReturns }
            },
            {
                path: "groupreturntransactions",
                name: "groupreturntransactions",
                components: { default: GroupReturnTransactions }
            },
            {
                path: "updatetransactions",
                name: "updatetransactions",
                components: { default: UpdateTransactions }
            },
            {
                path: "routingNumbers",
                name: "routingNumbers",
                components: { default: RoutingNumbers }
            },
            {
                path: "audits",
                name: "audits",
                components: { default: Audits }
            },
            {
                path: "importConsumers",
                name: "importconsumers",
                components: { default: ImportConsumers }
            },
            {
                path: "importMerchants",
                name: "importMerchants",
                components: { default: ImportMerchants }
            },
            {
                path: "importTransactions",
                name: "importTransactions",
                components: { default: ImportTransactions }
            },
            {
                path: "importTransactionTraceNumber",
                name: "importTransactionTraceNumber",
                components: { default: ImportTransactionTraceNumber }
            },
            {
                path: "postConsumerReturn",
                name: "postConsumerReturn",
                components: { default: PostConsumerReturn }
            },
            {
                path: "importVoidedTransactions",
                name: "importVoidedTransactions",
                components: { default: ImportVoidedTransactions }
            },
            {
                path: "merchants",
                name: "merchants",
                components: { default: RegisteredMerchants }
            },
            {
                path: "importIntegrators",
                name: "importIntegrators",
                components: { default: ImportIntegrator }
            },
            {
                path: "globalRadarPending",
                query: { email: '' },
                name: "globalRadarPending",
                components: { default: GlobalRadarPending }
            },
            {
                path: "globalRadarApproved",
                query: { email: '' },
                name: "globalRadarApproved",
                components: { default: GlobalRadarApproved }
            },
            {
                path: "globalRadarArchived",
                query: { email: '' },
                name: "globalRadarArchived",
                components: { default: GlobalRadarArchived }
            },
            {
                path: "apikeys",
                name: "apikeys",
                components: { default: ApiKeys }
            },
            {
                path: "helpdeskUsers",
                name: "helpdeskUsers",
                components: { default: HelpdeskUsers }
            },
            {
                path: "v1manualreview",
                query: { email: '' },
                name: "v1manualreview",
                components: { default: V1ManualReview }
            },
            {
                path: "settlementreport",
                name: "settlementReport",
                components: { default: SettlementReport }
            },
            {
                path: "discountedfeereport",
                name: "discountedfeereport",
                components: { default: DiscountedFeeReport }
            },
            {
                path: "whitelistRoutingNumbers",
                name: "whitelistRoutingNumbers",
                components: { default: WhiteListRoutingNumbers }
            },
            {
                path: "merchanttransactionreport",
                name: "merchantTransactionReport",
                components: { default: MerchantTransactionReport }
            },
            {
                path: "returntransactionsemail",
                name: "returntransactionsemail",
                components: { default: ReturnTransactionsEmail }
            },
            {
                path: "releaseconsumers",
                name: "releaseconsumers",
                components: { default: ReleaseConsumers }
            },
            {
                path: "finicityreturns",
                name: "finicityreturns",
                components: { default: FinicityReturns }
            },
            {
                path: "manualbankingreturns",
                name: "manualbankingreturns",
                components: { default: ManualBankingReturns }
            },
            {
                path: "manualreviewreturns",
                name: "manualreviewreturns",
                components: { default: ManualReviewReturns }
            },
            {
                path: "importholidaylists",
                name: "importholidaylists",
                components: { default: ImportHolidayLists }
            },
            {
                path: "consumerdocumentverifyarchive",
                name: "ConsumerDocumentVerifyArchive",
                components: { default: ConsumerDocumentVerifyArchive }
            },
            {
                path: "consumerdocumentverifyretake",
                name: "ConsumerDocumentVerifyRetake",
                components: { default: ConsumerDocumentVerifyRetake }
            },
            {
                path: "consumerdocumentverifypending",
                name: "ConsumerDocumentVerifyPending",
                components: { default: ConsumerDocumentVerifyPending }
            },
            {
                path: "settingsfd01952d34f7",
                name: "settingsfd01952d34f7",
                components: { default: Settings }
            },
            {
                path: "monthlysalesgrowth",
                name: "monthlySalesGrowth",
                components: { default: MonthlySalesGrowth }
            },
            {
                path: "storewisemonthlysales",
                name: "storewisemonthlysales",
                components: { default: StorewiseMonthlySales }
            },
            {
                path: "merchantpointreport",
                name: "merchantpointreport",
                components: { default: MerchantPointReport }
            },
            {
                path: "releasenote",
                name: "ReleaseNote",
                components: { default: ReleaseNote }
            },
            {
                path: "importreturntransactions",
                name: "importreturntransactions",
                components: { default: ImportReturnTransactions }
            },
            {
                path: "unknownreturncodes",
                name: "unknownreturncodes",
                components: { default: UnknownReturnReasons }
            },
            {
                path: "blacklistedaccountnumber",
                name: "blacklistedaccountnumber",
                components: { default: BlacklistedAccountNumber }
            },
            {
                path: "manualbanklinkrestrictedroutingnumbers",
                name: "manualbanklinkrestrictedroutingnumbers",
                components: { default: ManualBankLinkRestrictedRoutingNumbersList }
            },
            {
                path: "banklist",
                name: "banklist",
                components: { default: BankList }
            },
            {
                path: "banksolutionlist",
                name: "banksolutionlist",
                components: { default: BankSolutionList }
            },
            {
                path: "validaccountnumber",
                name: "validaccountnumber",
                components: { default: ValidAccountNumber }
            },
            {
                path: "suspectedconsumers",
                name: "suspectedconsumers",
                components: { default: SuspectedConsumers }
            },
            {
                path: "releaseconusmersfromreturn",
                name: "releaseconusmersfromreturn",
                components: { default: ReleaseConusmersFromReturn }
            },
            {
                path: "unknownroutingnumbers",
                name: "unknownroutingnumbers",
                components: { default: UnknownRoutingnumbers }
            },
            {
                path: "notificationlogs",
                name: "notificationlogs",
                components: { default: NotificationLogs }
            },
            {
                path: "integrator",
                name: "Integrator",
                components: { default: Integrator }
            },
            {
                path: "ecommerceCategory",
                name: "ecommerceCategory",
                components: { default: EcommerceCategory }
            },
            {
                path: "merchantKeyMaster",
                name: "merchantKeyMaster",
                components: { default: MerchantKeyMaster }
            },
            {
                path: "ecommerceVoidTransactions",
                name: "ecommerceVoidTransactions",
                components: { default: EcommerceVoidTransactions }
            },
            {
                path: "transactionmodificationreason",
                name: "transactionmodificationreason",
                components: { default: TransactionModifyReason }
            },
            {
                path: "transactionmodificationcustomreason",
                name: "transactionmodificationcustomreason",
                components: { default: TransactionModifyCustomReason }
            },
            {
                path: "rewardWheel",
                name: "RewardWheel",
                components: { default: RewardWheel }
            },
            {
                path: "rewardwheelsendinvitation",
                name: "RewardWheelSendInvitation",
                components: { default: RewardWheelSendInvitation }
            },
            {
                path: "invitationstatus",
                name: "InvitationStatus",
                components: { default: InvitationStatus }
            },
            {
                path: "rewardwheelreport",
                name: "rewardwheelreport",
                components: { default: RewardWheelReport }
            },
            {
                path: "probabilitytest",
                name: "probabilitytest",
                components: { default: RewardProbabilityTest }
            },
            {

                path: "jackpotreset",
                name: "jackpotreset",
                components: { default: JackpotReset }
            },
            {
                path: "rewardwheelsearch",
                name: "rewardwheelsearch",
                components: { default: RewardWheelSearch }
            },
            {
                path: "remotepaytransactionreport",
                name: "remotepaytransactionreport",
                components: { default: RemotePayTransactionReport }
            },
            {
                path: "achfilelists",
                name: "achfilelists",
                components: { default: AchFileList }
            },
            {
                path: "microbiltConsumerDeclinedForBankValidation",
                name: "microbiltConsumerDeclinedForBankValidation",
                components: { default: MicrobiltConsumerDeclinedForBankValidation }
            },
            {
                path: "microbiltReview",
                name: "microbiltReview",
                components: { default: MicrobiltReview }
            },
            {
                path: "achreturns",
                name: "achreturns",
                components: { default: AchReturn }
            },
            {
                path: "importAkoyaProviderID",
                name: "importAkoyaProviderID",
                components: { default: ImportAkoyaProviderID }
            },
            {
                path: "importmxinstitutioncode",
                name: "importmxinstitutioncode",
                components: { default: ImportMxInstitutionCode }
            },
            {
                path: "mxinstitution",
                name: "mxinstitution",
                components: { default: MxInstitution }
            },
            {
                path: "banklinkfailure",
                name: "banklinkfailure",
                components: { default: RegistrationFailure }
            },
            {
                path: "bankreportsurvey",
                name: "bankreportsurvey",
                components: { default: BankReportSurvey }
            },
            {
                path: "pointsbackmaster",
                name: "pointsbackmaster",
                components: { default: CashbackProgramMaster }
            },
            {
                path: "pointsbackprogramreportold",
                name: "cashbackprogramreport",
                components: { default: CashbackProgramReport }
            },
            {
                path: "pointsbackreport",
                name: "Pointsbackreport",
                components: { default: PointsbackReport }
            },
            {
                path: "pointsbackprogramfeedback",
                name: "cashbackprogramfeedback",
                components: { default: CashbackProgramFeedback }
            },
            {
                path: "mxidentificationhistory",
                name: "mxidentificationhistory",
                components: { default: MxIdentificationHistory }
            },
            {
                path: "healthchecks",
                name: "healthchecks",
                components: { default: HealthChecks }
            },
            {
                path: 'deliveryfeereport',
                name: 'deliveryfeereport',
                components: { default: DeliveryFeeReport }
            },
            {
                path: 'deliverysettlementreport',
                name: 'deliverysettlementreport',
                components: { default: DeliverySettlementReport }
            }
        ]
    },
];

export default routes;