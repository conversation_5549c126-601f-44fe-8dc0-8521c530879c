<?php

namespace App\Imports;

use App\Models\ConsumerMigration;
use App\Models\UserRole;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;

class UsersImport implements ToModel, WithHeadingRow, WithBatchInserts, WithValidation, WithChunkReading
{
    use Importable;
    /**
     * @param array $row
     * This function actully imports the data as row from Excel Sheet. Here we used the WithHeadingRow to get the Data with Heading. Do Not try to get the rows with index.
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row)
    {
        $role_details = UserRole::where('role_name', CONSUMER)->first(); // Fetching Role ID with respect to User Type
        $email = $row['email'];
        if (!empty($email)) {
            try {
                // If mobile phone number exists then we are tking the mobile as phone number else we are taking the landline as phone number
                $phone_number = $row['mobile_phone'] != '' ? str_replace("-", "", $row['mobile_phone']) : str_replace("-", " ", $row['phone']);

                // Check if consumer already exists in Database
                $checkConsumerExists = ConsumerMigration::where('email', $email)->first();
                if (empty($checkConsumerExists)) { // If not exists insert it into database

                    // Importing all the data in the Consumer Migration Table
                    $user = new ConsumerMigration();
                    $user->first_name = $row['first_name'];
                    $user->last_name = $row['last_name'];
                    $user->email = $email;
                    $user->phone = $phone_number;
                    $user->street_address = $row['address'];
                    $user->city = $row['city'];
                    $user->state = $row['state'];
                    $user->zipcode = $row['zip_code'];
                    $user->role_id = $role_details->role_id;
                    $user->existing_user = 1;
                    $user->save();

                    $action = 'added'; // For loggingg purpose

                } else {

                    // Checking if any of the data got changed
                    if ($checkConsumerExists->first_name != $row['first_name'] || $checkConsumerExists->last_name != $row['last_name'] || $checkConsumerExists->phone != $phone_number || $checkConsumerExists->street_address != $row['address'] || $checkConsumerExists->city != $row['city'] || $checkConsumerExists->state != $row['state'] || $checkConsumerExists->zipcode != $row['zip_code']) {

                        // Updating the data in the Consumer Migration Table
                        $user = ConsumerMigration::find($checkConsumerExists->id);
                        $user->first_name = $row['first_name'];
                        $user->last_name = $row['last_name'];
                        $user->email = $email;
                        $user->phone = $phone_number;
                        $user->street_address = $row['address'];
                        $user->city = $row['city'];
                        $user->state = $row['state'];
                        $user->zipcode = $row['zip_code'];
                        $user->updated = 1;
                        $user->save();

                        $action = 'updated'; // For loggingg purpose
                    } else {
                        $action = 'insertion/updation skipped due to duplicate entry'; // For logging purpose
                        $user = new \stdClass();
                        $user->id = $checkConsumerExists->id;
                        insertSkippedDataLog('Consumer', 'email', $row['email'], "Consumer insertion/updation skipped due to duplicate entry in Consumer Migration Table for User Email : " . $email . " with User ID : " . $user->id, json_encode($row), 'Mailing List');
                    }
                }
                Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer " . $action . " in Consumer Migration Table for User Email : " . $email . " with User ID : " . $user->id);
            } catch (\Exception $e) {
                Log::channel('datamigration')->error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured during insertion in Consumer Migration Table for User Email : " . $email . ".", [EXCEPTION => $e]);
                insertSkippedDataLog('Consumer', 'email', $row['email'], $e, json_encode($row), 'Mailing List');
            }
        }
    }

    public function batchSize(): int
    {
        return 1000;
    }

    public function chunkSize(): int
    {
        return 5000;
    }

    public function rules(): array
    {
        return [
            '*.email' => 'required',
        ];
    }
}
