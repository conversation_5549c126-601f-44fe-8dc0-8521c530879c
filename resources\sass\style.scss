//
body {
    font-size: 0.93rem !important;
}

// Datatables CSS Start
th.dt-center,
td.dt-center {
    text-align: center;
}

.dataTables_empty {
    text-align: center !important;
}

div.dataTables_wrapper div.dataTables_processing {
    background: none !important;
    border: none !important;
    color: $cp-primary !important;
    box-shadow: none !important;
}

.page-item.active .page-link {
    background-color: $cp-primary !important;
    border-color: $cp-primary !important;
}

// Datatables CSS End
// Top Progress Bar CSS Start
#nprogress .bar {
    background: $cp-primary !important;
    z-index: 999999 !important;
}

#nprogress .peg {
    box-shadow: 0 0 10px $cp-primary, 0 0 5px $cp-primary !important;
    z-index: 999999 !important;
}

#nprogress .spinner-icon {
    border-top-color: $cp-primary !important;
    border-left-color: $cp-primary !important;
    z-index: 999999 !important;
}

// Top Progress Bar CSS End
// Scroll Style Start
.scroll-style-3::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    background-color: $cp-white;
}

.scroll-style-3::-webkit-scrollbar {
    width: 6px;
    background-color: $cp-white;
}

.scroll-style-3::-webkit-scrollbar-thumb {
    background-color: $cp-primary !important;
}

// Scroll Style End
.modal-content {
    width: 750px !important;
    left: -20% !important;
}

.red {
    color: $cp-red !important;
}

.custom-edit-btn {
    cursor: pointer !important;
    color: $cp-primary !important;
}

.router-link-active {
    background: $cp-primary !important;
    color: $cp-white !important;
}

.brand-link.router-link-active {
    background: none !important;
}

.multiselect__tag {
    background: $cp-primary !important;
}

.multiselect__option--highlight {
    background: $cp-primary !important;
}

.brand-link .brand-image {
    margin-left: 0 !important;
}

.card-success:not(.card-outline)>.card-header {
    background-color: $cp-primary !important;
}

.badge-success {
    background-color: $cp-primary !important;
}

.btn-outline-success:hover {
    color: $cp-white !important;
    background-color: $cp-primary !important;
}

.btn-outline-success {
    color: $cp-primary !important;
    border-color: $cp-primary !important;
}

.btn-success {
    background-color: $cp-primary !important;
    border-color: $cp-primary !important;
}

.modal-header {
    background-color: $cp-primary !important;
    color: $cp-white !important;
}

.success {
    background-color: $cp-primary !important;
}

.nav-sidebar {
    overflow: visible !important;
}

.helper-text {
    color: $cp-red;
    font-size: 11px;
}


/**
* For toggle switch
*/

.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 25px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    -webkit-transition: .4s;
    transition: .4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 21px;
    width: 21px;
    left: 10px;
    bottom: 2px;
    background-color: white;
    -webkit-transition: .4s;
    transition: .4s;
}

input:checked+.slider {
    background-color: $cp-primary;
}

input:focus+.slider {
    box-shadow: 0 0 1px $cp-primary;
}

input:checked+.slider:before {
    -webkit-transform: translateX(26px);
    -ms-transform: translateX(26px);
    transform: translateX(26px);
}


/* Rounded sliders */

.slider.round {
    border-radius: 34px;
}

.slider.round:before {
    border-radius: 50%;
}

.datepicker table tr td.active:active,
.datepicker table tr td.active.highlighted:active,
.datepicker table tr td.active.active,
.datepicker table tr td.active.highlighted.active {
    background-color: $cp-primary !important;
    background-image: none !important;
}

.datepicker table tr td.today,
.datepicker table tr td.today:hover,
.datepicker table tr td.today.disabled,
.datepicker table tr td.today.disabled:hover {
    background-color: $cp-primary !important;
    background-image: none !important;
    color: $cp-white;
}

.image-text {
    cursor: pointer !important;
}

.image-text:hover {
    color: $cp-primary !important;
}

.id-preview {
    margin: auto;
    width: 301px;
    height: 213px;
    border-radius: 7px;
}

.id-preview-integrator {
    margin: auto;
    max-width: 301px;
    max-height: 213px;
    border-radius: 7px;
}

.vs__clear {
    display: block !important;
    visibility: hidden !important;
}

.vs__dropdown-menu li:hover {
    background: $cp-primary;
    color: $cp-white;
}

th:focus {
    outline: none !important;
}

.odd-row {
    background-color: #f2f2f2 !important;
    margin-left: 20px !important;
    margin-right: 20px !important;
}

.even-row {
    margin-left: 20px !important;
    margin-right: 20px !important;
}

.row-value {
    margin-top: 10px;
    margin-bottom: 10px;
}

.loading {
    position: fixed;
    z-index: 999;
    overflow: show;
    margin: auto;
    top: 0;
    left: 150px;
    bottom: 0;
    right: 0;
    width: 50px;
    height: 50px;
}


/* Transparent Overlay */

.loading:before {
    content: '';
    display: block;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.5);
}

.f-13 {
    font-size: 13px;
}

.cursor-pointer {
    cursor: pointer;
    color: #337ab7 !important;
}

.break-all {
    word-break: break-all !important;
}

.modal-body label {
    margin-top: 0.5rem !important;
}

.export-api-btn {
    float: right !important;
}

h4 {
    border-bottom: 1px solid #ccc;
}

.start-date,
.end-date {
    caret-color: transparent !important;
}

div.dts div.dataTables_scrollBody {
    background: none !important;
}

.div.dts tbody th,
div.dts tbody td {
    white-space: pre-wrap !important;
}

.margin-left-5 {
    margin-left: 5px !important;
}

.consumer-details-label {
    display: inline-block;
    margin-top: 0.5 rem !important;
    font-weight: normal !important;
}

.text-green {
    color: $cp-green;
    font-weight: bold;
}

.text-red {
    color: $cp-red;
    font-weight: bold;
}

.calendar-link {
    color: $cp-blue;
}

.select-date-cls {
    margin-top: 5px;
    margin-right: 10px;
}

.control-label::before {
    background-color: #149240 !important;
}

.custom-control-input:checked~.custom-control-label::before {
    background-color: #149240 !important;
}

.control-label::after {
    background-color: #149240 !important;
}

.custom-control-input:checked~.custom-control-label::after {
    background-color: #149240 !important;
}

.nav-sidebar .menu-open>.nav-treeview {
    display: inline-block !important;
    width: -webkit-fill-available !important;
}

.text-bold {
    font-weight: bold;
}

.users-pp-table-cls {
    display: inline-block;
    overflow-y: scroll;
    max-height: 500px;
}

.th-white {
    background-color: #ffffff !important;
}

.table-bordered td,
.table-bordered th {
    border: 1px solid #c1c0c0 !important;
}

.helper-text-right {
    float: right;
    color: #F00;
}

.mb-10 {
    margin-bottom: 10px !important;
}

.ml-10 {
    margin-left: 10px;
}

.mt-7 {
    margin-top: 7px;
}

.text-gray-italic {
    color: #949191;
    font-style: italic;
    text-align: center;
}

#consumer-transaction-details-modal___BV_modal_content_ {
    width: 900px !important;
    left: -40% !important;
}

#account-owner-history-modal___BV_modal_content_ {
    width: 900px !important;
}

.background-gray {
    background-color: #EFEFEF !important;
}

.background-red {
    background-color: #F4CCCC !important;
}

.background-green {
    background-color: #D9EAD3 !important;
}

.background-blue {
    background-color: #CFE2F3 !important;
}

.background-yellow {
    background-color: #FFF2CC !important;
}

.background-purple {
    background-color: #D9D2E9 !important;
}

textarea {
    resize: none !important;
}

.card-comment-box {
    float: right !important;
    margin-top: -17px !important;
    font-size: 10px !important;
}

.auto-overflow {
    overflow: auto;
    height: 350px;
    max-height: 350px;
}

.account-link {
    color: #007bff !important;
    cursor: pointer;
}

.allow_consumer_auth {
    margin-right: 165px;
}

.bg-color-class{
     background-color:#e2eafc;
}

.custom-default-algo{

    font-size:11px;

    white-space:nowrap;

    font-weight:bold;

    color:#000000;

  }
