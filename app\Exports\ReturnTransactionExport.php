<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Events\AfterSheet;

class ReturnTransactionExport implements FromCollection, WithHeadings, ShouldAutoSize, WithEvents
{
    protected $request;

    public function __construct($request)
    {
        $this->request = $request; // Declaring the request variable
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        $collection_array = $this->request->get('returnTransaction'); // Storing the array received from request

        $transactions = [];
        foreach ($collection_array as $transaction) {
            $nestedData['transaction_number'] = $transaction['transaction_number'];
            $nestedData['consumer_name'] = $transaction['consumer_name'];
            $nestedData['phone'] = $transaction['phone'];
            $nestedData['user_type'] = $transaction['user_type'];
            $nestedData['bank_link_type'] = $transaction['bank_link_type'];
            $nestedData['current_purchase_power'] = $transaction['current_purchase_power'];
            $nestedData['transaction_time'] = str_replace("<br/>", " ", $transaction['transaction_time']);
            $nestedData['total_amount'] = $transaction['total_amount'];
            $nestedData['return_reason'] = $transaction['reason_code'];
            $nestedData['represent_count'] = $transaction['represent_count'] == 0 ? '0' : $transaction['represent_count'];
            $nestedData['status'] = $transaction['status'];
            $nestedData['represented_on'] = $transaction['status'] == 'Pending' ? str_replace("<br/>", " ", $transaction['represented_on']) : '';
            $nestedData['expected_clearance'] = $transaction['status'] == 'Pending' ? $transaction['expected_clearance'] : '';

            array_push($transactions, $nestedData);
        }

        return collect([
            $transactions,
        ]);
    }

    public function headings(): array
    {
        //Return Details Array For the Top table
        $i = 0;
        foreach ($this->request->get('returnDetails') as $valReturn) {
            $returnArr[$i] = [];
            array_push($returnArr[$i], $valReturn['title'], "$" . $valReturn['amount']);
            $i++;
        }

        $returnArray = array(
            [
                'CanPay Return Transaction ',
            ],
            // 2nd header
            [
                ' Return Transaction Transaction Date Range:',
                date('m/d/Y', strtotime($this->request->get('from_date'))) . ' - ' . date('m/d/Y', strtotime($this->request->get('to_date'))),
            ],
            [],
            $returnArr[0],
            $returnArr[1],
            $returnArr[2],
            $returnArr[3],
            [],
            [
                'Transaction Number',
                'Consumer Name',
                'Phone',
                'User Type',
                'Bank Link Type',
                'Current Purchase Power',
                'Local Transaction Date',
                'Amount ($)',
                'Return Reason Code',
                'Number of time settlement',
                'Representment Status',
                'Represented On',
                'Expected Clearance',

            ],
        );

        return $returnArray;
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $event->sheet->getStyle('A1:E1')->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);

                //Returns total Value
                $event->sheet->getStyle('A7:B7')->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);

                //Apply Center Alignment
                $event->sheet->getStyle('A:E')->getAlignment()->applyFromArray(
                    array('horizontal' => 'center')
                );
            },
        ];
    }
}
