<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class StoreDeliveryPartnerMap extends Model
{
    protected $table = 'store_delivery_partner_maps';
    /**
     * __construct
     * This function will insert an alphanumeric 32 character unique id as primary key in the table whenever a data get inserted
     * @param  mixed $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
    }
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'store_id',
        'delivery_partner_id',
        'canpay_commission'
    ];
    public $timestamps = true;
}
