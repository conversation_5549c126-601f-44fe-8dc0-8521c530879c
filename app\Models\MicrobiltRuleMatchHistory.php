<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class MicrobiltRuleMatchHistory extends Model
{

    protected $table = 'microbilt_rule_match_history';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'id',
        'batch_id',
        'registration_session_id',
        'consumer_id',
        'rule_id',
        'bank_link_type',
        'canpay_positive_transaction',
        'phone',
        'account_no',
        'is_admin_driven',
        'parent_batch_id',
        'routing_no'
    ];
    public $timestamps = true;
    public $incrementing = false;
    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
}
