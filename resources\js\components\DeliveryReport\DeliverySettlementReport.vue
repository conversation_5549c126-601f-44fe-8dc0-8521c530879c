<template>
<div>
  <div v-if="loading">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Delivery Settlement Report</h3>
                </div>

                <div class="card-body">
                <div class="row">
                  <!---- Merchant Select Start ------>
                      <div class="col-md-4">
                          <div class="form-group">
                            <multiselect
                                id="merchant"
                                v-model="selectedMerchant"
                                placeholder="Select Delivery Partner (Min 3 chars)"
                                label="merchant_name"
                                :options="merchantList"
                                :loading="isLoadingMerchant"
                                :internal-search="false"
                                @search-change="getAllMerchant"
                                @input="dateDiff"
                            ></multiselect>
                          </div>
                      </div>
                  <!---- Merchant Select End -------->
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="start-date form-control"
                        placeholder="Start Date"
                        id="start-date"
                        onkeydown="return false"
                        autocomplete="off"
                      />
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="end-date form-control"
                        placeholder="End Date"
                        id="end-date"
                        onkeydown="return false"
                        autocomplete="off"
                      />
                    </div>
                  </div>
                </div>

              </div>
              <div class="card-footer">
                <button
                  type="button"
                  class="btn btn-success"
                  @click="generateReport()"
                  id="generateBtn"
                >
                  Generate
                </button>
                <button
                  type="button"
                  @click="exportReport()"
                  class="btn btn-danger ml-10"
                >
                  Export <i
                    class="fa fa-download ml-10"
                    aria-hidden="true"
                  ></i>
                </button>
                <button
                  type="button"
                  @click="reset()"
                  class="btn btn-success margin-left-5"
                >
                  Reset
                </button>
              </div>

              <div class="card-body">
                <div class="row">
                  <div class="col-12">
                    <!-- Only V2 Transaction report -->
                    <b-table-simple
                      responsive
                      show-empty
                      bordered
                      sticky-header="800px"
                    >
                      <b-thead head-variant="light" style="position: sticky; top:0;">
                        <b-tr>
                          <b-th colspan="1" class="text-center th-white">
                            <span v-if="selectedMerchant.id"
                              >Delivery Partner:
                              {{ selectedMerchant['merchant_name'] }}
                              </span
                            >
                          </b-th>
                          <b-th colspan="6" class="text-center b-top b-right"
                            >Delivery Partner Activity</b-th
                          >
                        </b-tr>
                        <b-tr>
                          <b-th class="text-center">Sales Date</b-th>
                          <b-th class="text-center">Delivery Fee</b-th>
                          <b-th class="text-center">Rate</b-th>
                          <b-th class="text-center">Vol Fee</b-th>
                          <b-th class="text-center"># of Trans</b-th>
                          <b-th class="text-center">Total Fees</b-th>
                        </b-tr>
                      </b-thead>
                      <b-tbody v-for="(row, index) in report" :key="index">
                        <b-tr :class="row.sales_date === 'Totals' ? 'report-total-cls' : ''">
                          <b-td class="text-left" :class="{'bold-text': row.sales_date === 'Totals'}">
                            {{ row.sales_date }}
                          </b-td>
                          <b-td class="text-right bold-text">
                            {{ row.v2_retail_volume }}
                          </b-td>
                          <b-td class="text-right" :class="{'bold-text': row.sales_date === 'Totals'}">
                            {{ row.v2_retail_vol_rate }}
                          </b-td>
                          <b-td class="text-right" :class="{'bold-text': row.sales_date === 'Totals'}">
                            {{ row.v2_retail_vol_fee }}
                          </b-td>
                          <b-td class="text-center" :class="{'bold-text': row.sales_date === 'Totals'}">
                            {{ row.v2_retail_no_of_trans }}
                          </b-td>
                          <b-td class="text-right bold-text">
                            {{ row.v2_final_fees }}
                          </b-td>
                        </b-tr>
                      </b-tbody>
                    </b-table-simple>
                  </div>
                </div>
              </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</div>
</template>
<script>
import merchantApi from "@/api/delivery.js";
import moment from "moment";
import { saveAs } from "file-saver";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "../CustomLoader/CanPayLoader.vue"
export default {
  data() {
    return {
      headerTextVariant: 'light',
      report: [],
      isLoadingMerchant:false,
      merchantList:[],
      selectedMerchant:[],
      fields: [],
      loading: false,
      storeList: [],
      selectedStore: [],
      reportGeneratedStore: [],
      cpList: [],
      rewardDetails: [],
      selectedCp: "",
      isLoadingCp: false,
      showOldSalesReport: false,
      showOldSalesMaxDate: process.env.MIX_V1_TRANSACTION_CUTOFF_DATE,
      isLoadingSt: false,
      singular_debit: false
    };
  },
  components: {
    HourGlass,
    CanPayLoader
  },
  watch: {
  },
  mounted() {
    var self = this;
    $("#start-date").datepicker({
      format: "mm/dd/yyyy",
      autoclose: true,
      todayHighlight: true,
    }).on('changeDate', function (ev) {
        self.dateDiff();
    });
    $("#end-date").datepicker({
      format: "mm/dd/yyyy",
      autoclose: true,
      todayHighlight: true,
    }).on('changeDate', function (ev) {
        self.dateDiff();
    });
    $("#start-date , #end-date").datepicker("setDate", "-1d");
  },
  methods: {
    dateDiff() {
        var self = this;

        // Get start and end dates from inputs
        var from_date = $("#start-date").val() ? moment($("#start-date").val()).format("YYYY-MM-DD") : "";
        var to_date = $("#end-date").val() ? moment($("#end-date").val()).format("YYYY-MM-DD") : "";

        if (from_date && to_date) {
            // Parse dates
            var date1 = new Date(from_date);
            var date2 = new Date(to_date);
            var compDate = new Date(self.showOldSalesMaxDate);

            // Determine old sales report visibility
            self.showOldSalesReport = compDate >= date1;

            // Calculate difference in days
            var Difference_In_Days = (date2 - date1) / (1000 * 3600 * 24);
            // Enable/disable the generate button
            if (Difference_In_Days > 60 || !self.selectedMerchant || !self.selectedMerchant.id) {
                $("#generateBtn").prop('disabled', true);
            } else {
                $("#generateBtn").prop('disabled', false);
            }
        }
    },
    getAllMerchant(searchtxt){
        var self = this;
        if(searchtxt.length >= 3){
            self.isLoadingMerchant = true;
            var request = {
            merchant_name: searchtxt,
            delivery_partner: 1
        };
        merchantApi
            .getAllMerchantsAndDeliveryPartners(request)
            .then(function (response) {
            if (response.code == 200) {
                self.merchantList = response.data;
            }else {
                error(response.message);
            }
            self.isLoadingMerchant = false;
            })
            .catch(function (error) {
            });
            }
    },
    // API call to generate the merchant location transaction report
    generateReport() {
      var self = this;
      if(self.selectedMerchant == null || moment($("#start-date").val()).format("YYYY-MM-DD") == 'Invalid date' || moment($("#end-date").val()).format("YYYY-MM-DD") == 'Invalid date'){
        error("Delivery Partner field, start date and end date is required.");
        return false;
      }
      if (
        moment($("#start-date").val()).format("YYYY-MM-DD") >
        moment().format("YYYY-MM-DD")
      ) {
        error("Start date cannot be from future.");
        return false;
      }
      if (
        moment($("#end-date").val()).format("YYYY-MM-DD") >
        moment().format("YYYY-MM-DD")
      ) {
        error("End date cannot be from future.");
        return false;
      }
      self.reportGeneratedStore = self.selectedStore[0];
      self.report = [];
      var request = {
        from_date: moment($("#start-date").val()).format("YYYY-MM-DD"),
        to_date: moment($("#end-date").val()).format("YYYY-MM-DD"),
        delivery_partner_id: self.selectedMerchant.id,
      };
      if(request.from_date > request.to_date){
        error("To Date cannot be greater than From date");
        return false;
      }
      self.loading = true;
      merchantApi
        .getDeliveryFeeSettlementReport(request)
        .then(function (response){
            self.report = response.data;
            self.loading = false;
        })
        .catch(function(err){
            self.loading = false;
            error(err)
        })
    },
    // exports the report
    exportReport() {
      var self = this;
      if(self.selectedMerchant == null || moment($("#start-date").val()).format("YYYY-MM-DD") == 'Invalid date' || moment($("#end-date").val()).format("YYYY-MM-DD") == 'Invalid date'){
        error("Delivery Partner field, start date and end date is required.");
        return false;
      }
      if (
        moment($("#start-date").val()).format("YYYY-MM-DD") >
        moment().format("YYYY-MM-DD")
      ) {
        error("Start date cannot be from future.");
        return false;
      }
      if (
        moment($("#end-date").val()).format("YYYY-MM-DD") >
        moment().format("YYYY-MM-DD")
      ) {
        error("End date cannot be from future.");
        return false;
      }
      self.report = [];
      var request = {
        from_date: moment($("#start-date").val()).format("YYYY-MM-DD"),
        to_date: moment($("#end-date").val()).format("YYYY-MM-DD"),
        delivery_partner_id: self.selectedMerchant.id,
      };
      if(request.from_date > request.to_date){
        error("To Date cannot be greater than From date");
        return false;
      }
      merchantApi
        .getDeliveryFeeSettlementTransactionExport(request)
        .then(function (response) {
          var FileSaver = require("file-saver");
          var blob = new Blob([response], {
            type: "application/xlsx",
          });
          FileSaver.saveAs(
            blob,
            moment().format("MM/DD/YYYY") + "_CanPay_Delivery_Settlement_Fees_Report.xlsx"
          );
          self.loading = false;
        })
        .catch(function (error) {
          // error(error);
          self.loading = false;
        });
    },
    reset(){
      var self = this;
      self.selectedMerchant = [];
    },
  },
};
</script>
<style>
.bold-text {
  font-weight: bold;
}
#view-merchant-rewards-modal___BV_modal_content_{
  width: 900px !important;
  position: absolute;
  top: 50%;
  left: 50%;
}
</style>
