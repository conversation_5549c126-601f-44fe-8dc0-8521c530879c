<template>
<div>
  <div v-if="loading">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Settlement/Fees Report</h3>
                </div>
 
                <div class="card-body">
                <div class="row">
                  <div class="col-md-4">
                    <div class="form-group">
                      <multiselect
                        id="corporateparent"
                        name="corporateparent"
                        v-model="selectedCp"
                        placeholder="Select Corporate Parent (Min 3 chars)"
                        label="corporateparent"
                        track-by="id"
                        :options="cpList"
                        :multiple="false"
                        :loading="isLoadingCp"
                        :internal-search="false"
                        @search-change="getAllActiveCorporateParent"
                        @input="dateDiff"
                        @select="refreshStores"
                      ></multiselect>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <multiselect
                        id="store"
                        name="store"
                        v-model="selectedStore"
                        placeholder="Select Store (Min 3 chars)"
                        label="retailer"
                        track-by="id"
                        :options="storeList"
                        :multiple="true"
                        :loading="isLoadingSt"
                        :internal-search="true"
                        @input="dateDiff"
                      ></multiselect>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="start-date form-control"
                        placeholder="Start Date"
                        id="start-date"
                        onkeydown="return false"
                        autocomplete="off"
                      />
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="end-date form-control"
                        placeholder="End Date"
                        id="end-date"
                        onkeydown="return false"
                        autocomplete="off"
                      />
                    </div>
                  </div>
                </div>
                
                <div class="row" v-if="!showOldSalesReport">
                  <div class="col-md-12">
                    <div class="form-group">
                      <label class="switch"
                        ><input
                          class="enable-employee-login"
                          type="checkbox"
                          v-model="singular_debit" /><span
                          class="slider round"
                        ></span
                      ></label>
                      Show Singular Debit Column
                    </div>
                  </div>
                </div>
              </div>
              <div class="card-footer">
                <button
                  type="button"
                  class="btn btn-success"
                  @click="generateReport()"
                  id="generateBtn"
                >
                  Generate
                </button>
                <button
                  type="button"
                  @click="exportReport()"
                  class="btn btn-danger ml-10"
                >
                  Export <i
                    class="fa fa-download ml-10"
                    aria-hidden="true"
                  ></i>
                </button>
                <button
                  type="button"
                  @click="exportAllStores()"
                  class="btn btn-danger ml-10 generateBtn"
                >
                  Export All Stores<i
                    class="fa fa-download ml-10"
                    aria-hidden="true"
                  ></i>
                </button>
                <button
                  type="button"
                  @click="reset()"
                  class="btn btn-success margin-left-5"
                >
                  Reset
                </button>
              </div>

              <div class="card-body">
                <div class="row">
                  <div class="col-12">
                    <!--Only V1, Older of the 2022 Transaction Report-->
                    <b-table-simple
                      responsive
                      show-empty
                      bordered
                      sticky-header="800px"
                      v-if="showOldSalesReport"
                    >
                      <b-thead head-variant="light" style="position: sticky; top:0;">
                        <b-tr>
                          <b-th colspan="7" class="text-center th-white">
                            <span v-if="selectedStore.length > 0"
                              >Store Name:
                              {{ selectedStore[0]['retailer'] }}
                              </span
                            >
                          </b-th>
                          <b-th
                            colspan="7"
                            class="text-center b-left b-top b-right"
                            >Old CanPay Sales Activity</b-th
                          >
                          <b-th colspan="7" class="text-center b-top b-right"
                            >New CanPay Sales Activity</b-th
                          >
                        </b-tr>
                        <b-tr>
                          <b-th class="text-center">Sales Date</b-th>
                          <b-th class="text-center b-left b-top"
                            >Total Volume</b-th
                          >
                          <b-th
                            class="text-center report-table-border b-top b-right"
                            >Total Fees</b-th
                          >
                          <b-th class="text-center">Old CanPay Deposit</b-th>
                          <b-th class="text-center">Fees 1</b-th>
                          <b-th class="text-center">New CanPay Deposit</b-th>
                          <b-th class="text-center">Fees 2</b-th>
                          <b-th class="text-center">Volume</b-th>
                          <b-th class="text-center">Rate</b-th>
                          <b-th class="text-center">Vol Fee</b-th>
                          <b-th class="text-center"># of Trans</b-th>
                          <b-th class="text-center">Rate</b-th>
                          <b-th class="text-center">Trans Fees</b-th>
                          <b-th class="text-center">Sub Total Fees</b-th>
                          <b-th class="text-center">Volume</b-th>
                          <b-th class="text-center">Rate</b-th>
                          <b-th class="text-center">Vol Fee</b-th>
                          <b-th class="text-center"># of Trans</b-th>
                          <b-th class="text-center">Rate</b-th>
                          <b-th class="text-center">Trans Fees</b-th>
                          <b-th class="text-center">Sub Total Fees</b-th>
                        </b-tr>
                      </b-thead>
                      <b-tbody v-for="(row, index) in report" :key="index">
                        <b-tr v-if="row.sales_date === 'Totals'" class="report-total-cls">
                          <b-td class="text-left"
                            ><b>{{ row.sales_date }}</b></b-td
                          >
                          <b-td class="text-right"
                            ><b>{{ row.total_volume }}</b></b-td
                          >
                          <b-td class="text-right report-table-border"
                            ><b>{{ row.total_fees }}</b></b-td
                          >
                          <b-td class="text-right"
                            ><b>{{ row.v1_deposit }}</b></b-td
                          >
                          <b-td class="text-right"
                            ><b>{{ row.v1_fees }}</b></b-td
                          >
                          <b-td class="text-right"
                            ><b>{{ row.v2_deposit }}</b></b-td
                          >
                          <b-td class="text-right"
                            ><b>{{ row.v2_fees }}</b></b-td
                          >
                          <b-td class="text-right"
                            ><b>{{ row.v1_volume }}</b></b-td
                          >
                          <b-td class="text-right"
                            ><b>{{ row.v1_vol_rate }}</b></b-td
                          >
                          <b-td class="text-right"
                            ><b>{{ row.v1_vol_fee }}</b></b-td
                          >
                          <b-td class="text-center"
                            ><b>{{ row.v1_no_of_trans }}</b></b-td
                          >
                          <b-td class="text-right"
                            ><b>{{ row.v1_fee_rate }}</b></b-td
                          >
                          <b-td class="text-right"
                            ><b>{{ row.v1_trans_fee }}</b></b-td
                          >
                          <b-td class="text-right"
                            ><b>{{ row.v1_sub_total_fees }}</b></b-td
                          >
                          <b-td class="text-right"
                            ><b>{{ row.v2_retail_volume }}</b></b-td
                          >
                          <b-td class="text-right"
                            ><b>{{ row.v2_retail_vol_rate }}</b></b-td
                          >
                          <b-td class="text-right"
                            ><b>{{ row.v2_retail_vol_fee }}</b></b-td
                          >
                          <b-td class="text-center"
                            ><b>{{ row.v2_retail_no_of_trans }}</b></b-td
                          >
                          <b-td class="text-right"
                            ><b>{{ row.v2_retail_fee_rate }}</b></b-td
                          >
                          <b-td class="text-right"
                            ><b>{{ row.v2_retail_trans_fee }}</b></b-td
                          >
                          <b-td class="text-right"
                            ><b>{{ row.v2_retail_sub_total_fees }}</b></b-td
                          >
                        </b-tr>
                        <b-tr v-else>
                          <b-td class="text-left">{{ row.sales_date }}</b-td>
                          <b-td class="text-right"><b>{{ row.total_volume }}</b></b-td>
                          <b-td class="text-right report-table-border"><b>{{
                            row.total_fees
                          }}</b></b-td>
                          <b-td class="text-right">{{ row.v1_deposit }}</b-td>
                          <b-td class="text-right">{{ row.v1_fees }}</b-td>
                          <b-td class="text-right">{{ row.v2_deposit }}</b-td>
                          <b-td class="text-right">{{ row.v2_fees }}</b-td>

                          <b-td class="text-right old-sales-act-cls"><b>{{ row.v1_volume }}</b></b-td>
                          <b-td class="text-right old-sales-act-cls">{{ row.v1_vol_rate }}</b-td>
                          <b-td class="text-right old-sales-act-cls">{{ row.v1_vol_fee }}</b-td>
                          <b-td class="text-center old-sales-act-cls">{{
                            row.v1_no_of_trans
                          }}</b-td>
                          <b-td class="text-right old-sales-act-cls">{{ row.v1_fee_rate }}</b-td>
                          <b-td class="text-right old-sales-act-cls">{{ row.v1_trans_fee }}</b-td>
                          <b-td class="text-right old-sales-act-cls"><b>{{
                            row.v1_sub_total_fees
                          }}</b></b-td>
                          <b-td class="text-right new-sales-act-cls"><b>{{
                            row.v2_retail_volume
                          }}</b></b-td>
                          <b-td class="text-right new-sales-act-cls">{{
                            row.v2_retail_vol_rate
                          }}</b-td>
                          <b-td class="text-right new-sales-act-cls">{{
                            row.v2_retail_vol_fee
                          }}</b-td>
                          <b-td class="text-center new-sales-act-cls">{{
                            row.v2_retail_no_of_trans
                          }}</b-td>
                          <b-td class="text-right new-sales-act-cls">{{
                            row.v2_retail_fee_rate
                          }}</b-td>
                          <b-td class="text-right new-sales-act-cls">{{
                            row.v2_retail_trans_fee
                          }}</b-td>
                          <b-td class="text-right new-sales-act-cls"><b>{{
                            row.v2_retail_sub_total_fees
                          }}</b></b-td>
                        </b-tr>
                      </b-tbody>
                    </b-table-simple>
                    <!-- Only V2 Transaction report -->
                    <b-table-simple
                      responsive
                      show-empty
                      bordered
                      sticky-header="800px"
                      v-else
                    >
                      <b-thead head-variant="light" style="position: sticky; top:0;">
                        <b-tr>
                          <b-th colspan="1" class="text-center th-white">
                            <span v-if="selectedStore.length > 0"
                              >Store Name:
                              {{ selectedStore[0]['retailer'] }}
                              </span
                            >
                          </b-th>
                          <b-th colspan="9" class="text-center b-top b-right"
                            >Canpay Sales Activity</b-th
                          >
                          <b-th colspan="2" class="text-center b-top b-right"
                            >Merchant Rewards</b-th
                          >
                        </b-tr>
                        <b-tr>
                          <b-th class="text-center">Sales Date</b-th>

                          <b-th class="text-center">Sales Volume</b-th>
                          <b-th class="text-center">Rate</b-th>
                          <b-th class="text-center">Vol Fee</b-th>
                          <b-th class="text-center"># of Trans</b-th>
                          <b-th class="text-center">Rate</b-th>
                          <b-th class="text-center">Trans Fees</b-th>
                          <b-th class="text-center">Sub Total Fees</b-th>
                          <b-th class="text-center">Reduced Fee Amount</b-th>
                          <b-th class="text-center">Final Total Fees</b-th>

                          <b-th class="text-center">Points Program Contribution</b-th>
                          <b-th class="text-center">Reward Points Contribution</b-th>
                          <b-th class="text-center" v-if="singular_debit">Total Debit</b-th>
                        </b-tr>
                      </b-thead>
                      <b-tbody v-for="(row, index) in report" :key="index">
                        <b-tr :class="row.sales_date === 'Totals' ? 'report-total-cls' : ''">
                          <b-td class="text-left" :class="{'bold-text': row.sales_date === 'Totals'}">
                            {{ row.sales_date }}
                          </b-td>
                          <b-td class="text-right bold-text">
                            {{ row.v2_retail_volume }}
                          </b-td>
                          <b-td class="text-right" :class="{'bold-text': row.sales_date === 'Totals'}">
                            {{ row.v2_retail_vol_rate }}
                          </b-td>
                          <b-td class="text-right" :class="{'bold-text': row.sales_date === 'Totals'}">
                            {{ row.v2_retail_vol_fee }}
                          </b-td>
                          <b-td class="text-center" :class="{'bold-text': row.sales_date === 'Totals'}">
                            {{ row.v2_retail_no_of_trans }}
                          </b-td>
                          <b-td class="text-right" :class="{'bold-text': row.sales_date === 'Totals'}">
                            {{ row.v2_retail_fee_rate }}
                          </b-td>
                          <b-td class="text-right" :class="{'bold-text': row.sales_date === 'Totals'}">
                            {{ row.v2_retail_trans_fee }}
                          </b-td>
                          <b-td class="text-right" :class="{'bold-text': row.sales_date === 'Totals'}">
                            {{ row.v2_retail_sub_total_fees }}
                          </b-td>
                          <b-td class="text-right text-danger" :class="{'bold-text': row.sales_date === 'Totals'}">
                            {{ row.v2_retail_sub_total_fees_waived }}
                          </b-td>
                          <b-td class="text-right bold-text">
                            {{ row.v2_final_fees }}
                          </b-td>
                          <b-td class="text-right bold-text">
                            {{ row.v2_retail_cashback_volume }}
                          </b-td>
                          <b-td class="text-right bold-text" v-if="row.sales_date === 'Totals' || row.v2_merchant_reward_volume === '$0.00'">
                            {{ row.v2_merchant_reward_volume }}
                          </b-td>
                          <b-td class="text-right bold-text" v-else>
                            <a class="cursor-pointer" @click="viewMerchantRewarsList(row.sales_date)" title="View Reward Points Details">{{ row.v2_merchant_reward_volume }}</a>
                          </b-td>
                          <b-td class="text-right bold-text" v-if="singular_debit">
                            {{ row.v2_retail_total_debit }}
                          </b-td>
                        </b-tr>
                      </b-tbody>
                    </b-table-simple>
                  </div>
                </div>
              </div>
              </div>
            </div>
          </div>
          <b-modal
            id="view-merchant-rewards-modal"
            ref="modal"
            :header-text-variant="headerTextVariant"
            title="Reward Points Details"
            ok-variant="success"
            :no-close-on-esc="true"
            :no-close-on-backdrop="true"
            :hide-footer="true"
            >
            <div class="row">
              
              <div class="col-md-12">
                <table class="table table-striped table-valign-middle td-pad-15">
                  <thead>
                    <tr>
                      <th class="text-center">Transaction Number</th>
                      <th class="text-center">Transaction Date</th>
                      <th class="text-center">Reward Amount</th>
                      <th class="text-center">Date Of Wheel Spin</th>
                    </tr>
                  </thead>
                  <tbody v-for="(row, index) in rewardDetails" :key="index">
                    <tr>
                      <td class="text-center">{{row.transaction_number}}</td>
                      <td class="text-center">{{row.local_transaction_date}}</td>
                      <td class="text-center">{{row.reward_amount}}</td>
                      <td class="text-center">{{row.wheel_spin_date}}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </b-modal>
        </div>
      </section>
    </div>
  </div>
</div>
</template>
<script>
import api from "@/api/reports.js";
import moment from "moment";
import { saveAs } from "file-saver";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "./CustomLoader/CanPayLoader.vue"
export default {
  data() {
    return {
      headerTextVariant: 'light',
      report: [],
      fields: [],
      loading: false,
      storeList: [],
      selectedStore: [],
      reportGeneratedStore: [],
      cpList: [],
      rewardDetails: [],
      selectedCp: "",
      isLoadingCp: false,
      showOldSalesReport: false,
      showOldSalesMaxDate: process.env.MIX_V1_TRANSACTION_CUTOFF_DATE,
      isLoadingSt: false,
      singular_debit: false
    };
  },
  components: {
    HourGlass,
    CanPayLoader
  },
  watch: {
  },
  mounted() {
    var self = this;
    $("#start-date").datepicker({
      format: "mm/dd/yyyy",
      autoclose: true,
      todayHighlight: true,
    }).on('changeDate', function (ev) {
        self.dateDiff();
    });
    $("#end-date").datepicker({
      format: "mm/dd/yyyy",
      autoclose: true,
      todayHighlight: true,
    }).on('changeDate', function (ev) {
        self.dateDiff();
    });
    $("#start-date , #end-date").datepicker("setDate", "-1d");
  },
  methods: {
    dateDiff(){
      var self = this;
      if ($("#start-date").val() != "") {
        var from_date = moment($("#start-date").val()).format("YYYY-MM-DD");
      } else {
        var from_date = "";
      }
      if ($("#end-date").val() != "") {
        var to_date = moment($("#end-date").val()).format("YYYY-MM-DD");
      } else {
        var to_date = "";
      }
      if(from_date!='' && to_date!=''){
        //calculate the date Difference 
        var date1 = new Date(from_date);
        var date2 = new Date(to_date);
        var compDate = new Date(self.showOldSalesMaxDate);
        if (compDate >= date1) {
          self.showOldSalesReport = true;
        } else {
          self.showOldSalesReport = false;
        }

        // To calculate the time difference of two dates
        var Difference_In_Time = date2.getTime() - date1.getTime();
          
        // To calculate the no. of days between two dates
        var Difference_In_Days = Difference_In_Time / (1000 * 3600 * 24);
        
        if(Difference_In_Days > 60 || self.selectedStore.length!=1){
          $("#generateBtn").prop('disabled', true);
        }else{
          $("#generateBtn").prop('disabled', false);
        }
      }
    },
    // This is wrapper API function , it is called for blob type response as for blob type response, the token is not refreshed automatically
    exportAllStores(){
      var self = this;
      console.log(self.selectedCp)
      if(self.selectedCp == ""){
        error("Select a Corporate Parent");
        return false;
      }
      self.loading = true;
        api
        .generateRefreshToken()
        .then(function (response) {
          if (response.code == 200) {
            self.exportReportAllStores();
          } else {
            self.loading = false;
            error(response.message);
          }
        })
        .catch(function (error) {
          // error(error);
        });
    },
    //Export Report for all the Stores
    exportReportAllStores(){
      var self = this;
      var start_date = moment($("#start-date").val()).format("YYYY-MM-DD");
      var end_date = moment($("#end-date").val()).format("YYYY-MM-DD");
      var current_date = moment().format("YYYY-MM-DD");
      
      if (
        moment($("#start-date").val()).format("YYYY-MM-DD") >
        moment().format("YYYY-MM-DD")
      ) {
        error("Start date cannot be from future.");
        return false;
      }
      if (
        moment($("#end-date").val()).format("YYYY-MM-DD") >
        moment().format("YYYY-MM-DD")
      ) {
        error("End date cannot be from future.");
        return false;
      }

      var request = {
        from_date: moment($("#start-date").val()).format("YYYY-MM-DD"),
        to_date: moment($("#end-date").val()).format("YYYY-MM-DD"),
        singular_debit: self.singular_debit,
        corporate_parent_id:self.selectedCp.id
      };
      api
        .exportv1SettlementReportAllStores(request)
        .then(function (response) {
          var FileSaver = require("file-saver");
          var blob = new Blob([response], {
            type: "application/xlsx",
          });
          FileSaver.saveAs(
            blob,
            moment().format("MM/DD/YYYY") + "_CanPay_Settlement_Fees_Report_All_Stores.xlsx"
          );
          self.loading = false;
        })
        .catch(function (error) {
          // error(error);
          self.loading = false;
        });
    },
    //get the list of All CP
    getAllActiveCorporateParent(searchtxt) {
      var self = this;
      if(searchtxt.length >= 3){
        self.isLoadingCp = true;
        self.cpList = [];
        var request = {
          searchtxt: searchtxt,
        };
        api
          .getAllActiveCorporateParent(request)
          .then(function (response) {
            if (response.code == 200) {
              self.cpList = response.data;
              self.storeList = [];
              self.isLoadingCp = false;
            } else {
              error(response.message);
            }
          })
          .catch(function (error) {
            error(error);
          });
      }
    },
    //get the list of All Stores
    getAllActiveStores() {
      var self = this;
      self.isLoadingSt = true;
      self.storeList = [];
      var request = {
        user_id: self.selectedCp.id,
      };
      api
        .getAllActiveStores(request)
        .then(function (response) {
          if (response.code == 200) {
            self.storeList = response.data;
            self.isLoadingSt = false;
          } else {
            error(response.message);
          }
        })
        .catch(function (error) {
          error(error);
        });
    },
    // API call to generate the merchant location transaction report
    generateReport() {
      var self = this;
      if(self.selectedStore.length == 0 && self.selectedCp == ""){
        error("Either Corporate Parent or Store field is required.");
        return false;
      }
      if (
        moment($("#start-date").val()).format("YYYY-MM-DD") >
        moment().format("YYYY-MM-DD")
      ) {
        error("Start date cannot be from future.");
        return false;
      }
      if (
        moment($("#end-date").val()).format("YYYY-MM-DD") >
        moment().format("YYYY-MM-DD")
      ) {
        error("End date cannot be from future.");
        return false;
      }

      var stores = [];
      if (self.selectedStore != []) {
        $.each(self.selectedStore, function (key, value) {
          stores.push(value.id);
        });
      }
      self.reportGeneratedStore = self.selectedStore[0];
      self.report = [];
      var request = {
        from_date: moment($("#start-date").val()).format("YYYY-MM-DD"),
        to_date: moment($("#end-date").val()).format("YYYY-MM-DD"),
        store_id: stores,
        singular_debit: self.singular_debit,
        initiated_by: 'Admin'
      };
      if(request.from_date > request.to_date){
        error("To Date cannot be greater than From date");
        return false;
      }
      self.loading = true;
      api
        .generateSettlementReport(request)
        .then(function (response) {
          if (response.code == 200) {
            self.report = response.data;
            self.loading = false;
          } else {
            error(response.message);
            self.loading = false;
          }
        })
        .catch(function (error) {
          // error(error);
          self.loading = false;
        });
    },
    // exports the report
    exportReport() {
      var self = this;
      if(self.selectedStore.length == 0 && self.selectedCp == ""){
        error("Either Corporate Parent or Store field is required.");
        return false;
      }
      if (
        moment($("#start-date").val()).format("YYYY-MM-DD") >
        moment().format("YYYY-MM-DD")
      ) {
        error("Start date cannot be from future.");
        return false;
      }
      if (
        moment($("#end-date").val()).format("YYYY-MM-DD") >
        moment().format("YYYY-MM-DD")
      ) {
        error("End date cannot be from future.");
        return false;
      }

      var stores = [];
      if (self.selectedStore != []) {
        $.each(self.selectedStore, function (key, value) {
          stores.push(value.id);
        });
      }

      self.report = [];
      var request = {
        from_date: moment($("#start-date").val()).format("YYYY-MM-DD"),
        to_date: moment($("#end-date").val()).format("YYYY-MM-DD"),
        store_id: stores,
        user_id: self.selectedCp.id,
        singular_debit: self.singular_debit,
        initiated_by:'Admin'
      };
      if(request.from_date > request.to_date){
        error("To Date cannot be greater than From date");
        return false;
      }
      self.loading = true;
      api
        .exportSettlementReport(request)
        .then(function (response) {
          var FileSaver = require("file-saver");
          var blob = new Blob([response], {
            type: "application/xlsx",
          });
          FileSaver.saveAs(
            blob,
            moment().format("MM/DD/YYYY") + "_CanPay_Settlement_Fees_Report.xlsx"
          );
          self.loading = false;
        })
        .catch(function (error) {
          // error(error);
          self.loading = false;
        });
    },
    reset(){
      var self = this;
      self.selectedCp = "";
      self.selectedStore = [];
    },
    refreshStores(selectedOption){
      var self = this;
      self.storeList = [];
      self.selectedStore = [];
      self.selectedCp = selectedOption;
      self.getAllActiveStores();
    },
    //View all merchant rewards
    viewMerchantRewarsList(sales_date){
      var self = this;
      self.loading = true;
      var request = {
        store_id: self.reportGeneratedStore.id,
        sales_date: moment(sales_date).format("YYYY-MM-DD"),
      };
      api
        .viewMerchantRewarsList(request)
        .then(function (response) {
          if (response.code == 200) {
            self.rewardDetails = response.data;
            self.$bvModal.show('view-merchant-rewards-modal');
            self.loading = false;
          } else {
            error(response.message);
            self.loading = false;
          }
        })
        .catch(function (error) {
          // error(error);
          self.loading = false;
        });
    }
  },
};
</script>
<style>
.bold-text {
  font-weight: bold;
}
#view-merchant-rewards-modal___BV_modal_content_{
  width: 900px !important;
  position: absolute;
  top: 50%;
  left: 50%;
}
</style>