<?php
namespace App\Http\Clients;

use App\Models\ValidationLog;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 *
 * @package App\Http\Clients
 */
class CognitoHttpClient
{
    /**
     * @var Client
     */
    private $client;
    /**
     * CognitoHttpClient constructor.
     */
    public function __construct()
    {
        $this->client = new Client(['base_uri' => config('app.cognito_base_url')]);
    }
    /**
     * Cognito profile creation to proceed further with creating the identity search
     *
     * @return mixed
     */
    public function createCognitoProfile($params)
    {
        $params['api'] = '/profiles';
        try {
            $response = $this->client->post($params['api'], [
                'headers' => $params['headers'],
                'body' => $params['body'],
            ]);
            $params['response'] = $response->getBody();
            if (!isset($params['flag_type'])) {
                //storing the response in the database
                $this->storeResponseInDB($params);
            }
            return $response->getBody();
        } catch (\GuzzleHttp\Exception\ClientException $ex) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while sending request to cognito.", [EXCEPTION => $ex]);
            return $ex->getResponse()->getBody()->getContents();
        }
    }
    /**
     * Creating the identity search
     *
     * @return mixed
     */
    public function searchIdentity($params)
    {
        $params['api'] = '/identity_searches';
        try {
            $response = $this->client->post($params['api'], [
                'headers' => $params['headers'],
                'body' => $params['body'],
            ]);
            $params['response'] = $response->getBody();
            //encrypt the ssn returned
            $attributes = [];
            $json_decoded = json_decode($params['response'], true);
            foreach ($json_decoded['included'] as $key => $val) {
                if ($val['type'] == 'ssn') {
                    $attributes['number'] = base64_encode($val['attributes']['number']);
                    $attributes['group'] = base64_encode($val['attributes']['area']);
                    $attributes['area'] = base64_encode($val['attributes']['group']);
                    $attributes['serial'] = base64_encode($val['attributes']['serial']);
                    $json_decoded['included'][$key]['attributes'] = $attributes;
                }
            }
            $params['response'] = json_encode($json_decoded);
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Cognito search api with phone returned response : " . $params['response']);
            if (!isset($params['flag_type'])) {
                //storing the response in the database
                $this->storeResponseInDB($params);
            }
            return $response->getBody();
        } catch (\GuzzleHttp\Exception\ClientException $ex) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while sending request to cognito.", [EXCEPTION => $ex]);
            return $ex->getResponse()->getBody()->getContents();
        }
    }
    /**
     * Getting identity assessment done for the mentioned Identity search
     *
     * @return mixed
     */
    public function identityAssessment($params)
    {
        $params['api'] = '/identity_assessments';
        try {
            $response = $this->client->post($params['api'], [
                'headers' => $params['headers'],
                'body' => $params['body'],
            ]);
            $params['response'] = $response->getBody();
            if (isset($params['ssn'])) {
                //encrypt the ssn returned
                $json_decoded = json_decode($params['response'], true);
                $json_decoded['data']['attributes']['ssn']['serial'] = base64_encode($json_decoded['data']['attributes']['ssn']['serial']);
                foreach ($json_decoded['included'] as $key => $val) {
                    if ($val['type'] == 'ssn_comparison') {
                        $components = $val['attributes']['components'];
                        $components['area']['source'] = base64_encode($components['area']['source']);
                        $components['group']['source'] = base64_encode($components['group']['source']);
                        $components['serial']['source'] = base64_encode($components['serial']['source']);
                        $components['serial']['input'] = base64_encode($components['serial']['input']);
                        $json_decoded['included'][$key]['attributes']['components'] = $components;
                    }
                }
                $params['response'] = json_encode($json_decoded);
            }
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Cognito assessment api returned response : " . $params['response']);
            //storing the response in the database
            if (!isset($params['flag_type'])) {
                $this->storeResponseInDB($params);
            }
            return $response->getBody();
        } catch (\GuzzleHttp\Exception\ClientException $ex) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while sending request to cognito.", [EXCEPTION => $ex]);
            return $ex->getResponse()->getBody()->getContents();
        }
    }
    /**
     * Storing the api call details in DB to keep logs
     *
     * @return mixed
     */
    public function storeResponseInDB($params)
    {
        $log_data = array(
            'api' => $params['api'],
            'phone' => $params['phoneNo'],
            'first_name' => isset($params['firstName']) ? $params['firstName'] : null,
            'middle_name' => isset($params['middleName']) ? $params['middleName'] : null,
            'last_name' => isset($params['lastName']) ? $params['lastName'] : null,
            'ssn' => isset($params['ssn']) ? $params['ssn'] : null,
            'response' => $params['response'],
            'type' => 'cognito',
            'session_id' => $params['session_id'],
        );
        DB::beginTransaction();
        try {
            ValidationLog::create($log_data);
            DB::commit();
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while storing data.", [EXCEPTION => $e]);
            DB::rollback();
        }
    }
}
