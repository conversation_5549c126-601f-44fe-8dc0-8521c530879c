<?php

namespace App\Exports;

use DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Events\AfterSheet;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;

class MonthlySalesGrowthExport implements FromCollection, WithHeadings, ShouldAutoSize, WithEvents, WithColumnFormatting
{
    protected $request;

    public function __construct($request)
    {
        $this->request = $request; // Declaring the request variable
    }

    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        $collection_array = $this->request->get('report'); // Storing the array received from request
        
        $results = array();
        foreach($collection_array as $result){
            $nestedData['month'] = $result['month'];
            $nestedData['transaction_count'] = $result['transaction_count'];
            
            array_push($results,$nestedData);
        }

        return collect([
            $results,
        ]);
    }

    public function headings(): array
    {
        $returnArray = array(
            // 1st header
            [
                'Monthly Sales Growth',
            ],
            [
                'Month',
                'Transaction Count'
            ],
        );

        return $returnArray;
    }

    public function columnFormats(): array
    {
        return [
            'B' => '0.00',
        ];
    }


    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event){
                $event->sheet->getStyle('A1:B1')->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);

                //Apply Center Alignment
                $event->sheet->getStyle('A:B')->getAlignment()->applyFromArray(
                    array('horizontal' => 'center')
                );
            },
        ];
    }
}
