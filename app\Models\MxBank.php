<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class MxBank extends Model
{

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'id',
        'code',
        'forgot_password_url',
        'forgot_username_url',
        'instructional_text',
        'medium_logo_url',
        'name',
        'small_logo_url',
        'supports_account_identification',
        'supports_account_statement',
        'supports_tax_document',
        'supports_account_verification',
        'supports_oauth',
        'supports_transaction_history',
        'trouble_signing_in_url',
        'url',
    ];
    public $timestamps = true;
    public $incrementing = true;
    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
}
