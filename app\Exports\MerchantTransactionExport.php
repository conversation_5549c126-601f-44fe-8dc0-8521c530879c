<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Events\AfterSheet;
use Illuminate\Support\Facades\Log;

class MerchantTransactionExport implements FromCollection, WithHeadings, ShouldAutoSize, WithEvents
{
    protected $request;

    public function __construct($request)
    {
        $this->request = $request; // Declaring the request variable
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        $collection_array = $this->request->get('report'); // Storing the array received from request
        return collect([
            $collection_array,
        ]);
    }

    public function headings(): array
    {
        $returnArray = array(
            // 1st header
            [
                'Report Date:',
                date('m/d/Y', strtotime($this->request->get('from_date'))) . ' - ' . date('m/d/Y', strtotime($this->request->get('to_date'))),
            ],
            // 2nd header
            [
                '',
                '',
                'Store Category',
                'Terminal ID',
                'Transaction Date',
                'Transaction Time',
                'Transaction No.',
                'Total Payment',
                'Base Amount',
                'Tip',
                'Trans Count',
                'Transaction Status',
                'Consumer Identifier',
            ],
        );

        return $returnArray;
    }

    public function registerEvents(): array
    {
        $voided_index = $this->request->get('voided_index');
        $total_amount_index = $this->request->get('total_amount_index');
        return [
            AfterSheet::class => function (AfterSheet $event) use($voided_index, $total_amount_index) {
                $event->sheet->getStyle('A2:M2')->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);
                $event->sheet->getStyle('H3:K3')->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);
                $event->sheet->getStyle('H4:K4')->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);
                // Merging cells
                $event->sheet->mergeCells('B1:D1');
                //Apply Right Alignment
                $event->sheet->getStyle('H3:H100000')->getAlignment()->applyFromArray(
                    array('horizontal' => 'right')
                );
                $event->sheet->getStyle('I3:I100000')->getAlignment()->applyFromArray(
                    array('horizontal' => 'right')
                );
                $event->sheet->getStyle('J3:J100000')->getAlignment()->applyFromArray(
                    array('horizontal' => 'right')
                );

                // Gerying out the voided transaction
                if(!empty($voided_index)){
                    foreach($voided_index as $voided){
                        $count = ($voided + 3);
                        $event->sheet->getStyle('E'.$count.':M'.$count)->applyFromArray([
                            'font' => [
                                'italic' => true,
                                'color' => ['argb' => '808080'],
                            ],
                        ]);
                    }
                }

                // Bold the total amounts
                if(!empty($voided_index)){
                    foreach($total_amount_index as $total_amount){
                        $count = ($total_amount + 3);
                        $event->sheet->getStyle('H'.$count.':K'.$count)->applyFromArray([
                            'font' => [
                                'bold' => true
                            ],
                        ]);
                    }
                }
            },
        ];
    }
}
