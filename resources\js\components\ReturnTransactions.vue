<template>
<div>
  <div v-if="loading">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px;">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Return Transactions</h3>
                </div>
                <div class="card-body">
                <div class="row">
                  <div class="col-md-6">
                    <div class="form-group">
                      <multiselect
                        v-model="selectedConsumer"
                        placeholder="Select Consumer (Minimum 3 characters required to search)"
                        id="consumer"
                        label="consumer_name"
                        :options="consumerList"
                        :loading="isLoading"
                        :internal-search="false"
                        @search-change="getConsumers"
                          >
                    </multiselect>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-group">
                      <input
                        class="form-control"
                        placeholder="Phone No."
                        id="phone_no"
                        v-model="phone_no"
                        autocomplete="off"
                      />
                    </div>
                  </div>
                </div>

                <div class="row">
                  <div class="col-md-6">
                    <div class="form-group">
                      <input
                        class="start-date form-control"
                        placeholder="Start Date"
                        id="start-date"
                        onkeydown="return false"
                        autocomplete="off"
                      />
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-group">
                      <input
                        class="end-date form-control"
                        placeholder="End Date"
                        id="end-date"
                        onkeydown="return false"
                        autocomplete="off"
                      />
                    </div>
                  </div>
                </div>
                <div class="row">
                  <div class="col-md-3">
                    <div class="form-group">
                      <label class="switch"><input class="enable-employee-login" type="checkbox" v-model="toggle_local_transaction_time_search"><span class="slider round"></span></label> Local Transaction Date
                    </div>
                  </div>
                  <div class="col-md-9">
                    <div class="form-group">
                      <label>Return Type</label>&nbsp;
                      <input type="radio" name="new_representable" id="radio0" v-model="new_representable">&nbsp;<label for="radio0">All Returns</label> &nbsp;&nbsp;
                      <input type="radio" name="new_representable" id="radio1" value="0" v-model="new_representable">&nbsp;<label for="radio1">New Returns</label> &nbsp;&nbsp;
                      <input type="radio" name="new_representable" id="radio2" value="1" v-model="new_representable">&nbsp;<label for="radio2">Represented Returns</label> &nbsp;&nbsp;
                      <input type="radio" name="new_representable" id="radio2" value="2" v-model="new_representable">&nbsp;<label for="radio2">Forgiven Returns</label>
                    </div>
                  </div>
                </div>
              </div>
              <div class="card-footer">
                <button
                  type="button"
                  class="btn btn-success"
                  @click="generateReport(false)"
                >
                  Generate
                </button>
                <button
                  type="button"
                  @click="generateReport(true)"
                  class="btn btn-danger ml-10"
                >
                  Generate & Export <i
                    class="fa fa-download ml-10"
                    aria-hidden="true"
                  ></i>
                </button>
              </div>
                <!-- /.card-header -->
                <div class="card-body">
                  <div class="row">
                    <div class="col-8" v-if="returnDetails.length > 0">
                        <span class="text-red">Total Returns Only within selected date range. Does not represent customer specific values.</span>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-4">
                      <b-table-simple bordered>
                        <b-tbody>
                          <b-tr v-for="(returnRow, index) in returnDetails" :key="index">
                            <b-td :class="returnDetails.length== index+1?'text-bold text-left':'text-left'">{{
                            returnRow.title
                          }}</b-td>
                            <b-td :class="returnDetails.length== index+1?'text-bold text-center':'text-center'">${{
                            returnRow.amount
                          }}</b-td>
                          </b-tr>
                        </b-tbody>
                      </b-table-simple>
                    </div>
                  </div>
                  <div class="row">
                  <div class="col-12">
                    <b-table-simple
                      responsive
                      show-empty
                      bordered
                      sticky-header="800px"
                    >
                      <b-thead head-variant="light">
                        <b-tr>
                          <b-th class="text-center">Transaction Number</b-th>
                          <b-th class="text-center">Consumer</b-th>
                          <b-th class="text-center">Phone Number</b-th>
                          <b-th class="text-center">User Type</b-th>
                          <b-th class="text-center">Bank Link Type</b-th>
                          <b-th class="text-center">Current Purchase Power</b-th>
                          <b-th width="10%" class="text-center">Local Transaction Date</b-th>
                          <b-th class="text-center">Amount</b-th>
                          <b-th class="text-center">Return Reason Code</b-th>
                          <b-th class="text-center">Attempted Settlement </b-th>
                          <b-th class="text-center">Representment Status</b-th>
                          <b-th class="text-center">Represented On</b-th>
                          <b-th class="text-center">Expected Clearance</b-th>
                          <b-th class="text-center">Action</b-th>
                        </b-tr>
                      </b-thead>
                      <b-tbody v-for="(row, index) in report" :key="index">
                        <b-tr>
                          <b-td class="text-left text-gray">
                            <a style="color: white !important" class="btn btn-success" @click="viewTransactionDetails(row)">{{row.transaction_number}}</a></b-td>
                          <b-td class="text-left text-gray">{{
                            row.consumer_name
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.phone
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.user_type
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.bank_link_type
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.current_purchase_power
                          }}</b-td>
                           <b-td v-html="row.transaction_time" class="text-left text-gray"></b-td>
                           <b-td class="text-left text-gray">{{
                            row.total_amount
                          }}</b-td>
                          <b-td class="text-left text-gray">
                                <a style="color: white !important" class="btn btn-success " @click="detailsReason(row)">{{
                            row.reason_code
                          }} </a>

                          </b-td>
                          <b-td class="text-left text-gray">{{
                            row.represent_count
                          }}</b-td>
                          <b-td class="text-left text-gray">{{row.status}}</b-td>
                          <b-td class="text-left text-gray"><span v-if="row.status == 'Pending'" v-html="row.represented_on"></span></b-td>
                          <b-td class="text-left text-gray"><span v-if="row.status == 'Pending'" >{{row.expected_clearance}}</span></b-td>
                          <b-td class="text-left text-gray"><a v-if="row.status != 'Pending' && row.status != 'Success'" style="color: white !important" class="btn btn-danger leave-comment" @click="waiveReturn(row.edit)"><i class="fa fa-times fa-lg"></i> Cancel</a><a v-if="row.consumer_represented != 1 && row.status == 'Returned'" style="color: white !important; margin-top: 3px; width: 93px;" class="btn btn-success " @click="clickRepayment(row)"> Re-Pay</a></b-td>
                        </b-tr>
                      </b-tbody>
                    </b-table-simple>
                  </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
    <!-- Transaction cancellation comment modal start -->
    <b-modal
      id="comment-modal"
      ref="comment-modal"
      ok-title="Save & Cancel"
      cancel-title="Close"
      ok-variant="success"
      @ok="waiveReturnTransaction"
      cancel-variant="outline-secondary"
      hide-header
      :no-close-on-esc="true"
      :no-close-on-backdrop="true"
    >
      <form ref="form" @submit.stop.prevent="save" class="needs-validation">
        <div class="row">
          <div class="col-md-12">
            <label for="user_type">
              Status
              <span class="red">*</span>
            </label>
            <select
              class="form-control"
              id="user_status"
              name="user_status"
              v-model="status"
              v-validate="'required'"
              placeholder="Select return Status"
            >
              <option
                v-for="(status, index) in statusList"
                :key="index"
                :value="status.id"
              >
                {{ status.status }}
              </option>
            </select>
            <span v-show="errors.has('user_status')" class="text-danger">{{
              errors.first("user_status")
            }}</span>
          </div>
        </div>

        <div class="row">
          <div class="col-md-12">
            <label for="return_reason">
              Return Reason
              <span class="red">*</span>
            </label>
            <select
              class="form-control"
              id="return_reason"
              name="return_reason"
              v-model="reason"
              v-validate="'required'"
              placeholder="Select return Status"
            >
              <option
                v-for="(reason, index) in reasonList"
                :key="index"
                :value="reason.id"
              >
                {{ reason.reason }}
              </option>
            </select>
            <span v-show="errors.has('return_reason')" class="text-danger">{{
              errors.first("return_reason")
            }}</span>
          </div>
        </div>
        <div class="row" v-if="showMsg">
        <div class="col-12">
          <label for="comment" class="red"
            >Please fill in the required fields.</label
          >
        </div>
      </div>
      </form>
      <div class="row" style="margin-bottom: 40px"></div>
    </b-modal>
    <!-- Transaction cancellation comment modal end -->

    <!-- Transaction Return Reason Modal Start -->
    <b-modal
      id="modal-approved-review"
      ref="modal-approved-review"
      hide-footer
      title="Return Reason"
      :no-close-on-esc="false"
      :no-close-on-backdrop="false"
    >
    <div class="row mb-2">
        <div class="col-12">
            <b> Return Code </b> : {{return_code}}
        </div>
    </div>
    <div class="row mb-2">
        <div class="col-12">
            <b> Customer Name </b> : {{customer_name}}
        </div>
    </div>
    <div class="row mb-2">
        <div class="col-12">
            <b> Transaction ID </b> : {{transaction_id}}
        </div>
    </div>
    <div class="row mb-2">
        <div class="col-12">
            <b> Title </b> : {{reasonCodeTitle}}
        </div>
    </div>
    <div class="row mb-2">
        <div class="col-12">
            <b> Description </b> : {{reasonCodeDiscription}}
        </div>
    </div>
    </b-modal>
    <!-- Transaction Return Reason Modal end -->

    <!-- Transaction Details modal start -->
    <b-modal
      id="transaction-details-modal"
      ref="transaction-details-modal"
      :header-text-variant="headerTextVariant"
      :title="modal_header"
      hide-footer
    >
    <div class="row mb-2">
        <div class="col-12">
            <b> Customer Name </b> : {{customer_name}}
        </div>
    </div>
    <div class="row mb-2">
        <div class="col-12">
            <b> Transaction ID </b> : {{transaction_id}}
        </div>
    </div>
    <div class="row mb-2">
        <div class="col-12">
            <b> Transaction History </b> :
            <p v-for="(row, index) in returnTransactionDetails" :key="index">
                <span>{{row.local_transaction_date}} - {{row.status}}</span>
            </p>
        </div>
    </div>
    </b-modal>
    <!-- Transaction Details modal end -->
    <!-- Return Representment modal start -->
    <b-modal
      id="modal-represent-return"
      ref="modal-represent-return"
      :header-text-variant="headerTextVariant"
      :title="modal_header"
      @ok="handleReturnRepresent"
      ok-title="Re-Pay"
      ok-variant="success"
      cancel-title="Close"
      cancel-variant="outline-secondary"
      title="Return Transaction Repayment"
      :no-close-on-esc="true"
      :no-close-on-backdrop="true"
    >

        <div class="row">
            <div class="col-md-12"><label>Consumer Name : </label> <span v-html="consumer_name"></span></div>
            <div class="col-md-12"><label>Consumer Phone : </label> <span v-html="consumer_phone"></span></div>
            <div class="col-md-12"><label>Transaction Number : </label> <span v-html="transaction_number"></span></div>
            <div class="col-md-12"><label>Total Transaction Amount : </label> <span v-html="total_transaction_amount"></span></div>
          </div>
          <div class="row">
                <div class="col-md-12">
                <br />
                    <div class="form-group">
                        <label>Select Bank Account</label>
                        <br />
                        <div style="max-height: 120px; overflow-y: auto;" v-if="change_bank == 1">
                            <div v-for="(row, index) in consumerBankList" :key="index">
                                <input type="radio" v-if="transaction_returned_account != row.account_id" @change="onSelectBank" v-validate="'required'" name="selected_account" :id="'selected_account-'+row.account_id" :value="row.account_id" v-model="selectedBank">&nbsp;<label v-if="row.blacklisted == 0" :for="'selected_account-'+row.account_id">{{row.account_no}} - {{row.bank_link_type}}</label><label v-else :for="'selected_account-'+row.account_id">{{row.account_no}} - {{row.bank_link_type}} (blacklisted)</label> &nbsp;&nbsp;
                            </div>
                        </div>
                        <div style="max-height: 120px; overflow-y: auto;" v-if="change_bank == 0">
                            <div v-for="(row, index) in consumerBankList" :key="index">
                                <input type="radio" @change="onSelectBank" v-validate="'required'" name="selected_account" :id="'selected_account-'+row.account_id" :value="row.account_id" v-model="selectedBank">&nbsp;<label v-if="row.blacklisted == 0" :for="'selected_account-'+row.account_id">{{row.account_no}} - {{row.bank_link_type}}</label><label v-else :for="'selected_account-'+row.account_id">{{row.account_no}} - {{row.bank_link_type}} (blacklisted)</label> &nbsp;&nbsp;
                            </div>
                        </div>
                        <span v-if="selectedBankError" class="text-danger">Please select a bank</span>
                    </div>
                </div>
          </div>
    </b-modal>
    <!-- Return Representment modal end -->
  </div>
</div>
</template>
<script>
import api from "@/api/transaction.js";
import user_api from "@/api/user.js";
import moment from "moment";
import { validationMixin } from "vuelidate";
import { required, minLength } from "vuelidate/lib/validators";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "./CustomLoader/CanPayLoader.vue";
export default {
  mixins: [validationMixin],
  data() {
    return {
      allTransactionModel: {},
      showMsg: false,
      transaction_id: null,
      statusList: [],
      reasonList:[],
      comment: "",
      currentUser: localStorage.getItem("user")
        ? JSON.parse(localStorage.getItem("user"))
        : null,
      loading: false,
      report: [],
      consumerList: [],
      selectedConsumer: null,
      email:"",
      phone_no:"",
      reasonCodeTitle: "",
      reasonCodeDiscription: "",
      isLoading: false,
      transaction_id: "",
      status:null,
      reason:null,
      toggle_local_transaction_time_search: false,
      new_representable: null,
      returnDetails:{},
      headerTextVariant: "light",
      returnTransactionDetails:{},
      modal_header: null,
      customer_name: null,
      transaction_id: null,
      return_code: null,
      updatedDetails:{},
      consumerBankList: [],
      selectedBank: null,
      selectedChangeBank: '',
      selectedBankError: false,
      consumer_name: null,
      consumer_phone: null,
      total_transaction_amount: null,
      transaction_number: null,
      change_bank: null,
      transaction_returned_account: null,
      consumer_bank_link_type: null,
    };
  },
  components: {
    HourGlass,
    CanPayLoader
  },
  created() {

  },
  methods: {
    waiveReturn(id){
        var self = this;
        self.$bvModal.show("comment-modal");
        //set transaction id to a hidden field for future use
        self.transaction_id = id;
    },
    viewTransactionDetails(data){
      var self = this;
      var request = {
          transaction_number: data.transaction_number
      };
      api
        .getReturnTransactionDetails(request)
        .then((response) => {
          if ((response.code = 200)) {
            self.returnTransactionDetails = response.data;
            self.transaction_id = data.transaction_number;
            self.customer_name = data.consumer_name;
            self.modal_header = 'Transaction Details';
            self.$bvModal.show("transaction-details-modal");
          } else {
            error(response.message);
          }
        })
        .catch((err) => {
          error(err);
        });
    },
    getReturnStatus() {
      var self = this;
      api
        .getReturnStatus()
        .then((response) => {
          if ((response.code = 200)) {
            self.statusList = response.data;
          } else {
            error(response.message);
          }
        })
        .catch((err) => {
          error(err);
        });
    },
    getReturnReasons() {
      var self = this;
      api
        .getReturnReasons()
        .then((response) => {
          if ((response.code = 200)) {
            self.reasonList = response.data;
          } else {
            error(response.message);
          }
        })
        .catch((err) => {
          error(err);
        });
    },
    waiveReturnTransaction(bvModalEvt){
        var self = this;

        self.updatedDetails = self.report.find(
            p => p.edit == self.transaction_id
        );
        if (self.reason == null || self.status == null) {
            console.log("test");
            self.showMsg = true;
            bvModalEvt.preventDefault();
        } else {
            console.log("new");
            self.showMsg = false;
            var request = {
                transaction_id: self.transaction_id,
                reason: self.reason,
                status: self.status,
            };
            api
            .waiveTransaction(request)
            .then((response) => {
                if (response.code == 200) {
                    success(response.message);
                    self.$refs["comment-modal"].hide();
                    self.reason = null;
                    self.status = null;
                    self.updatedDetails.status = 'Success';
                } else {
                    error(response.message);
                    self.$refs["comment-modal"].hide();
                    self.comment == "";
                }
            })
            .catch((err) => {
                console.log(err);
                error(err);
            });
        }
    },
    //get the consumer list
    getConsumers(searchtxt) {
      var self = this;
      if(searchtxt.length >= 3){
        self.isLoading = true;
        var request = {
          searchtxt: searchtxt,
        };
        api
          .getConsumers(request)
          .then(function (response) {
            if (response.code == 200) {
              self.consumerList = response.data;
              self.isLoading = false;
            } else {
              error(response.message);
            }
          })
          .catch(function (error) {
            error(error);
          });
      }
    },
    // API call to generate the merchant location transaction report
    generateReport(reportExport) {
      var self = this;
      if($("#end-date").val() == '' &&  $("#start-date").val() == ''){
        error("Please select Start Date & End Date then Generate.");
        return false;
      }
      if (
        moment($("#start-date").val()).format("YYYY-MM-DD") >
        moment().format("YYYY-MM-DD") && $("#start-date").val()!= ''
      ) {
        error("Start date cannot be from future.");
        return false;
      }
      if (
        moment($("#end-date").val()).format("YYYY-MM-DD") >
        moment().format("YYYY-MM-DD") && $("#end-date").val()!= ''
      ) {
        error("End date cannot be from future.");
        return false;
      }
      if($("#start-date").val()!=''){
        var from_date = moment($("#start-date").val()).format("YYYY-MM-DD");
      }else{
        var from_date = '';
      }
      if($("#end-date").val()!=''){
        var to_date = moment($("#end-date").val()).format("YYYY-MM-DD");
      }else{
        var to_date = '';
      }

      if(self.selectedConsumer === null){
        var consumer = '';
      }else{
        var consumer = self.selectedConsumer.user_id;
      }
      const fromDate = new Date(from_date);
      const toDate = new Date(to_date);
      // Calculate the difference in days
      const timeDiff = toDate.getTime() - fromDate.getTime();
      const dayDiff = timeDiff / (1000 * 3600 * 24);
      console.log(dayDiff+1,process.env.MIX_API_RETURN_TRASNACTION_MAX_DIFFERENCE);
      if(dayDiff+1>process.env.MIX_API_RETURN_TRASNACTION_MAX_DIFFERENCE){
        error("The date range difference should not be greater than "+process.env.MIX_API_RETURN_TRASNACTION_MAX_DIFFERENCE+" days.")
        return;
      }

      self.report = [];
      var request = {
        from_date: from_date,
        to_date: to_date,
        consumer: consumer,
        phone_no:self.phone_no,
        toggle_local_transaction_time_search: self.toggle_local_transaction_time_search,
        new_representable: self.new_representable
      };
      if(request.from_date > request.to_date){
        error("To Date cannot be greater than From date");
        return false;
      }
      self.loading = true;
      api
        .generateReturnTransactionReport(request)
        .then(function (response) {
          if (response.code == 200) {
            self.report = response.data.report;
            self.returnDetails = response.data.returnDetails;
            if(self.report.length > 0){
              if (reportExport) {
                self.exportReport();
              } else {
                self.loading = false;
              }
            }else {
              error("No records found!");
              self.loading = false;
            }
          } else {
            error(response.message);
            self.loading = false;
          }
        })
        .catch(function (error) {
          // error(error);
          self.loading = false;
        });
    },

    // exports the report
    exportReport() {
      var self = this;
      self.loading = true;
      if($("#end-date").val() == '' &&  $("#start-date").val() == ''){
        error("Please select Start Date & End Date then Generate.");
        return false;
      }
      if($("#start-date").val()!=''){
        var from_date = moment($("#start-date").val()).format("YYYY-MM-DD");
      }else{
        var from_date = '';
      }
      if($("#end-date").val()!=''){
        var to_date = moment($("#end-date").val()).format("YYYY-MM-DD");
      }else{
        var to_date = '';
      }

      var request = {
        returnTransaction: self.report,
        returnDetails: self.returnDetails,
        from_date : from_date,
        to_date   : to_date,
      };
      api
        .getreturntransactionexport(request)
        .then(function (response) {
          var FileSaver = require("file-saver");
          var blob = new Blob([response], {
            type: "application/xlsx",
          });
          FileSaver.saveAs(
            blob,
            moment().format("MM/DD/YYYY") + "_return_transaction_report.xlsx"
          );
          self.loading = false;
        })
        .catch(function (error) {
          // error(error);
          self.loading = false;
        });
    },
    detailsReason(data)
    {
        var self = this;
        self.customer_name = data.consumer_name;
        self.transaction_id = data.transaction_number;
        self.return_code = data.reason_code;
        self.reasonCodeTitle = data.title;
        self.reasonCodeDiscription = data.description;
        self.$refs["modal-approved-review"].show();
    },
    clickRepayment(data) {
        var self = this;
        self.consumerBankList = [];
        self.isLoading = true;
        self.selectedBankError = false;
        self.selectedBank = null;
        self.transaction_id = data.edit;
        self.total_transaction_amount = '$' + data.total_amount;
        self.consumer_name = data.consumer_name;
        self.consumer_phone = data.phone;
        self.transaction_number = data.transaction_number;
        self.change_bank = data.new_banking;
        console.log("change bank: " + self.change_bank);
        self.transaction_returned_account = data.transaction_returned;
        var request = {
          id: data.user_id,
        };
        user_api
          .consumerBankAccountList(request)
          .then(function (response) {
            self.consumerBankList = response.accounts;
            self.consumer_bank_link_type = response.bank_link_type;
            self.isLoading = false;
            self.$refs["modal-represent-return"].show();
          })
          .catch(function (error) {
            console.log(error);
          });
    },
    onSelectBank(){
        var self = this;
        if(self.selectedBank == '' || self.selectedBank == null){
            self.selectedBankError = true;
        }
        else{
            self.selectedBankError = false;
        }
    },
    handleReturnRepresent(bvModalEvt){
        var self = this;
        if(self.selectedBank == '' || self.selectedBank == null){
            self.selectedBankError = true;
            // Prevent modal from closing
            bvModalEvt.preventDefault();
        }
        else {
            self.swalText = 'Once you repayment for this return you will not be able to undo this action.';
            var swal_title = 'Are you sure to repayment for the transaction number ' + self.transaction_number + '?';
            Vue.swal({
                title: swal_title,
                text: self.swalText,
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#149240",
                confirmButtonText: "Yes",
                cancelButtonText: "No, cancel it!",
                closeOnConfirm: true,
                closeOnCancel: true,
                width: '800px'
                }).then((result) => {
                    if (result.isConfirmed) {
                        self.makeReturnRepayment();
                    }
                });
        }
    },
    makeReturnRepayment() {
        var self = this;
        self.isLoading = true;
        var request = {
        id: self.transaction_id,
        bank_account_id: self.selectedBank,
        balance_checking: self.consumer_bank_link_type
        };
        api
        .returnRepayment(request)
        .then(function (response) {
            if (response.code == 200) {
                success(response.message);
                self.generateReport(false);
            } else {
                error(response.message);
            }
            self.isLoading = false;
            self.$refs["modal-represent-return"].hide();
        })
        .catch(function (error) {
            console.log(error);
            self.isLoading = false;
            self.$refs["modal-represent-return"].hide();
        });
    }

  },
  mounted() {
    var self = this;
    self.getReturnStatus();
    self.getReturnReasons();
    $("#start-date").datepicker({
      format: "mm/dd/yyyy",
      autoclose: true,
      todayHighlight: true,
    });
    $("#end-date").datepicker({
      format: "mm/dd/yyyy",
      autoclose: true,
      todayHighlight: true,
    });
    $("#start-date , #end-date").datepicker("setDate", new Date());
  },
};
</script>


