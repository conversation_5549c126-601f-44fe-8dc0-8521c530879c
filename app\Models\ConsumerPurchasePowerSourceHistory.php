<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;


class ConsumerPurchasePowerSourceHistory extends Model
{

    protected $table = 'consumer_purchase_power_source_history';

    /**
     * __construct
     * This function will insert an alphanumeric 32 character unique id as primary key in the table whenever a data get inserted
     * @param  mixed $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        $this->id = generateUUID();

        parent::__construct($attributes);
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'id',
        'user_id',
        'purchase_power',
        'purchase_power_source',
        'created_at',
        'updated_at'
    ];
    public $timestamps = true;
    public $incrementing = false;
    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
}
