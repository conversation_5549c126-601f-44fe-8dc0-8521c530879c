import { loadProgressBar } from 'axios-progress-bar'

const searchBankList = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/getbanklist', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const importLogo = (formData) => {
    return new Promise((res, rej) => {
        axios.defaults.headers.common["Content-Type"] = 'multipart/form-data';
        axios.post('api/import/banklogo', formData)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const editBank = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/editbank', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const bankSolutionList = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/getbanksolutionlist', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const editBankSolution = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/editbanksolution', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getBankSolutionStatus = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/getbanksolutionstatus', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const getBankRoutingNumbers = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/getbankroutingnumbers', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getBankLinkSurveyHistory = (payload) => {
    return new Promise((res, rej) => {
        axios.post('api/getbanklinksurveyhistory', payload)
        .then((response) => {
            res(response.data);
        })
        .catch((err) => {
            rej(err);
        })
})
};
const addBankDetail = (request) => {
    axios.defaults.headers.common["Content-Type"] = 'multipart/form-data';
    return new Promise((res, rej) => {
        axios.post('api/addbankdetail', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const searchBankName = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/searchbankname', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const saveRoutingNumber = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/saveroutingnumber', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

export default {
    searchBankList,
    importLogo,
    editBank,
    bankSolutionList,
    editBankSolution,
    getBankSolutionStatus,
    getBankRoutingNumbers,
    getBankLinkSurveyHistory,
    addBankDetail,
    searchBankName,
    saveRoutingNumber
};