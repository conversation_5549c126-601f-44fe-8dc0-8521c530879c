<?php

namespace App\Http\Clients;

use App\Models\AkoyaRefreshToken;
use App\Models\BankingSolutionMaster;
use App\Models\StatusMaster;
use App\Models\User;
use App\Models\UserBankAccountInfo;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;

/**
 *
 * @package App\Http\Clients
 */
class AkoyaHttpClient
{
    /**
     * @var Client
     */
    private $productClient;
    private $clientIdp;
    private $clientNoError;
    private $akoya_id;
    /**
     * AkoyaHttpClient constructor.
     */
    public function __construct()
    {
        $this->productClient = new Client(['base_uri' => config('app.akoya_product_url')]);
        $this->clientIdp = new Client(['base_uri' => config('app.akoya_idp_url')]);
        $this->clientNoError = new Client(['base_uri' => config('app.akoya_product_url'), 'http_errors' => false]);
        $this->akoya_id = BankingSolutionMaster::where('banking_solution_name', AKOYA)->first()->id;
    }

    /**
     * getAccessToken
     * This function will create the access token for akoya to retrive funther consumer details
     * @param  mixed $params
     * @return void
     */
    public function getAccessToken($params)
    {
        try {
            $params['api'] = '/token';
            $client_id = config('app.akoya_client_id');
            $client_secret = config('app.akoya_client_secret');
            $authString = base64_encode("$client_id:$client_secret");

            $params['data'] = [
                'form_params' => [
                    'grant_type' => 'authorization_code',
                    'redirect_uri' => config('app.akoya_redirect_url'),
                    'code' => $params['code'],
                ],
                'headers' => [
                    'Content-Type' => 'application/x-www-form-urlencoded',
                    'x-akoya-interaction-type' => 'user',
                    'Authorization' => 'Basic ' . $authString,
                ],
            ];
            $response = $this->clientIdp->post($params['api'], $params['data']);
            $response_headers = $response->getHeaders();
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Akoya get access token returned headers: ", $response_headers);
            $params['akoya_interaction_id'] = isset($response_headers['x-akoya-interaction-id'][0]) ? $response_headers['x-akoya-interaction-id'][0] : (isset($response_headers['akoyaid'][0]) ? $response_headers['akoyaid'][0] : null);
            saveBankingSolutionResponseData($params, $response->getBody(), $this->akoya_id);
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Akoya get access token returned response: " . $response->getBody());
            return $response->getBody();
        } catch (\GuzzleHttp\Exception\ClientException $ex) {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while get access token from Akoya.", [EXCEPTION => $ex]);
            return $ex->getResponse()->getBody()->getContents();
        }
    }

    /**
     * refreshToken
     * This function will return a refreshed id_token along with a refresh token for a specific consumer
     * @param  mixed $token
     * @param  mixed $consumer_id
     * @return void
     */
    public function getRefreshedToken($token, $consumer_id = null, $institution_id)
    {
        try {
            $params['consumer_id'] = $consumer_id;
            $params['api'] = '/token';
            $client_id = config('app.akoya_client_id');
            $client_secret = config('app.akoya_client_secret');

            $params['data'] = [
                'form_params' => [
                    'grant_type' => 'refresh_token',
                    'refresh_token' => $token,
                    'client_id' => $client_id,
                    'client_secret' => $client_secret,
                ],
                'headers' => [
                    'Content-Type' => 'application/x-www-form-urlencoded',
                    'x-akoya-interaction-type' => 'user',
                ],
            ];
            $response = $this->clientIdp->post($params['api'], $params['data']);
            $response_headers = $response->getHeaders();
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Akoya get access token returned headers: ", $response_headers);
            $params['akoya_interaction_id'] = isset($response_headers['x-akoya-interaction-id'][0]) ? $response_headers['x-akoya-interaction-id'][0] : (isset($response_headers['akoyaid'][0]) ? $response_headers['akoyaid'][0] : null);
            saveBankingSolutionResponseData($params, $response->getBody(), $this->akoya_id);
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Akoya get refresh token returned response: " . $response->getBody());
            return $response->getBody();
        } catch (\GuzzleHttp\Exception\ClientException $ex) {
            $responseBody = $ex->getResponse()->getBody()->getContents();
            if ($ex->getCode() == 401 || $ex->getCode() == 400 ) {
                $responseData = json_decode($responseBody, true);
                if (isset($responseData['error']) && ($responseData['error'] === 'token_inactive' || $responseData['error'] === 'invalid_grant' || $responseData['error'] === 'invalid_request')) {
                    // Log the error or perform any necessary action
                    // Get the bank delink status_id
                    $bank_delink = getStatus(BANK_DELINK);
                    $bank_status = StatusMaster::whereIn('code', [BANK_ACTIVE, BANK_INACTIVE])->pluck('id')->toArray();
                    // Handle token_inactive/invalid_grant/invalid_request error specifically
                    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "The Bank account was delinked due to Akoya refresh token is invalid for consumer ID: " . $consumer_id);
                    // Update active and inactive accounts to Delinked state
                    UserBankAccountInfo::where(['user_id' => $consumer_id])->whereIn('status', $bank_status)
                        ->update(['status' => $bank_delink]);
                    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Force delink updated to 1for consumer ID: " . $consumer_id);
                    User::where(['user_id' => $consumer_id])->update(['force_delink' => 1]);
                    AkoyaRefreshToken::where('consumer_id', $consumer_id)->where('institution_id', $institution_id)->update(['error' => 1]);
                    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Token is inactive because it is expired or otherwise invalid for consumer ID: " . $consumer_id, ['EXCEPTION' => $ex]);
                } else {
                    // Handle other 401 errors
                    // Log the error or perform any necessary action
                    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while getting refresh token from Akoya for consumer ID: " . $consumer_id, ['EXCEPTION' => $ex]);
                }
            } else {
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while getting refresh token from Akoya for consumer ID: " . $consumer_id, ['EXCEPTION' => $ex]);
            }
        }
    }

    /**
     * getAccountInfo
     * This function will return the accounts information like balances etc. of the consumer except account_no and routing_no
     * @param  mixed $id_token
     * @return void
     */
    public function getAccountInfo($params, $accountId = false)
    {
        try {
            $akoya_provider_id = rawurlencode($params['akoya_provider_id']);
            $params['api'] = $accountId != '' ? '/accounts/v2/' . $akoya_provider_id . '?accountIds=' . $accountId : '/accounts/v2/' . $akoya_provider_id;

            $params['data'] = [
                'headers' => [
                    'accept' => 'application/json',
                    'x-akoya-interaction-type' => 'user',
                    'authorization' => 'Bearer ' . $params['id_token'],
                ],
            ];
            $response = $this->productClient->get($params['api'], $params['data']);
            $response_headers = $response->getHeaders();
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Akoya get access token returned headers: ", $response_headers);
            $params['akoya_interaction_id'] = isset($response_headers['x-akoya-interaction-id'][0]) ? $response_headers['x-akoya-interaction-id'][0] : (isset($response_headers['akoyaid'][0]) ? $response_headers['akoyaid'][0] : null);
            saveBankingSolutionResponseData($params, $response->getBody(), $this->akoya_id);
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Akoya get accounts returned response: " . $response->getBody());
            return $response->getBody();
        } catch (\GuzzleHttp\Exception\ClientException $ex) {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while get account info from Akoya.", [EXCEPTION => $ex]);
            return $ex->getResponse()->getBody()->getContents();
        }
    }

    /**
     * getPaymentNetworks
     * This function will return the accounts information like account_no and routing_no
     * @param  mixed $id_token
     * @param  mixed $accountId
     * @return void
     */
    public function getPaymentNetworks($id_token, $accountId)
    {
        try {
            $params['api'] = '/payments/v2/mikomo/' . $accountId . '/payment-networks';

            $params['data'] = [
                'headers' => [
                    'accept' => 'application/json',
                    'x-akoya-interaction-type' => 'user',
                    'authorization' => 'Bearer ' . $id_token,
                ],
            ];
            $response = $this->productClient->get($params['api'], $params['data']);
            $response_headers = $response->getHeaders();
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Akoya get access token returned headers: ", $response_headers);
            $params['akoya_interaction_id'] = isset($response_headers['x-akoya-interaction-id'][0]) ? $response_headers['x-akoya-interaction-id'][0] : (isset($response_headers['akoyaid'][0]) ? $response_headers['akoyaid'][0] : null);
            saveBankingSolutionResponseData($params, $response->getBody(), $this->akoya_id);
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Akoya get payment-networks returned response: " . $response->getBody());
            return $response->getBody();
        } catch (\GuzzleHttp\Exception\ClientException $ex) {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while get payment-networks from Akoya.", [EXCEPTION => $ex]);
            return $ex->getResponse()->getBody()->getContents();
        }
    }

    /**
     * getAccountOwnerInfo
     * This function will return the account owner information from Akoya
     * @param  mixed $id_token
     * @return void
     */
    public function getAccountOwnerInfo($params)
    {
        try {
            $akoya_provider_id = rawurlencode($params['akoya_provider_id']);
            $params['api'] = '/customers/v2/' . $akoya_provider_id . '/current';
            $params['data'] = [
                'headers' => [
                    'accept' => 'application/json',
                    'x-akoya-interaction-type' => 'user',
                    'authorization' => 'Bearer ' . $params['id_token'],
                ],
            ];
            $response = $this->productClient->get($params['api'], $params['data']);
            $response_headers = $response->getHeaders();
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Akoya get access token returned headers: ", $response_headers);
            $params['akoya_interaction_id'] = isset($response_headers['x-akoya-interaction-id'][0]) ? $response_headers['x-akoya-interaction-id'][0] : (isset($response_headers['akoyaid'][0]) ? $response_headers['akoyaid'][0] : null);
            saveBankingSolutionResponseData($params, $response->getBody(), $this->akoya_id);
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Akoya get account owner info returned response: " . $response->getBody());
            return $response->getBody();
        } catch (\GuzzleHttp\Exception\ClientException $ex) {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while get account owner info from Akoya.", [EXCEPTION => $ex]);
            return $ex->getResponse()->getBody()->getContents();
        }
    }

    /**
     * getAccountOwnerInfoCO
     * This function will return the account owner information from Akoya for Captial One only
     * @param  mixed $id_token
     * @return void
     */
    public function getAccountOwnerInfoCO($params)
    {
        try {
            $akoya_provider_id = rawurlencode($params['akoya_provider_id']);
            $params['api'] = '/contacts/v2/' . $akoya_provider_id . '/' . $params['account_id'] . '';
            $params['data'] = [
                'headers' => [
                    'accept' => 'application/json',
                    'x-akoya-interaction-type' => 'user',
                    'authorization' => 'Bearer ' . $params['id_token'],
                ],
            ];
            $response = $this->productClient->get($params['api'], $params['data']);
            $response_headers = $response->getHeaders();
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Akoya get access token returned headers: ", $response_headers);
            $params['akoya_interaction_id'] = isset($response_headers['x-akoya-interaction-id'][0]) ? $response_headers['x-akoya-interaction-id'][0] : (isset($response_headers['akoyaid'][0]) ? $response_headers['akoyaid'][0] : null);
            saveBankingSolutionResponseData($params, $response->getBody(), $this->akoya_id);
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Akoya get account owner (Capital One) info for Account ID: " . $params['account_id'] . " returned response: " . $response->getBody());
            return $response->getBody();
        } catch (\GuzzleHttp\Exception\ClientException $ex) {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while get account owner info from Akoya.", [EXCEPTION => $ex]);
            return $ex->getResponse()->getBody()->getContents();
        }
    }
}
