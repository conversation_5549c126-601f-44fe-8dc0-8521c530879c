const addNotification = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/addnotification', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const sendNotification = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/sendnotification', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const searchNotifications = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/getallnotifications', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};


export default {
    addNotification,
    sendNotification,
    searchNotifications
};