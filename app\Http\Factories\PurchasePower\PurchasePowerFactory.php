<?php

namespace App\Http\Factories\PurchasePower;

use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 *
 * @package App\Http\Factories\PurchasePower
 */
class PurchasePowerFactory implements PurchasePowerInterface
{
    /**
     * calculatePurchasePower
     * This function will Calculate the Purchase Power based on the Consumer's bank balanace. This is only for the Users whol have linked their bank account via Finicity
     * @param  mixed $params
     * @return void
     */
    public function calculatePurchasePower($balance, $user)
    {
        if ($balance > 0) {
            //get the required bank balance status from query
            if ($user->purchase_power_source == NEW_RULE) {
                $result = DB::table('purchase_power_lower_limit')
                    ->select(DB::raw("FLOOR((percentage/100)*?) as purchase_power"))
                    ->whereRaw(" ? BETWEEN low_amount AND high_amount", [$balance, $balance])
                    ->first();
            } else {
                $result = DB::table('transaction_decision_table')
                    ->select(DB::raw("CASE WHEN fixed_amount IS NULL THEN FLOOR((percentage/100)*?) ELSE fixed_amount END as purchase_power"))
                    ->whereRaw(" ? BETWEEN low_amount AND high_amount", [$balance, $balance])
                    ->first();
            }

            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Calculated purchase power on balance: " . $balance . " is: " . $result->purchase_power);
            return $result->purchase_power > config('app.max_transaction_limit_for_single_transaction') ? config('app.max_transaction_limit_for_single_transaction') : $result->purchase_power;
        }
        return 0;
    }

}
