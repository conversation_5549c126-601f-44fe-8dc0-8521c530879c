<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
class BankAccountInfo extends Model
{
    protected $table = 'user_bank_account_info';

    /**
     * __construct
     * This function will insert an alphanumeric 32 character unique id as primary key in the table whenever a data get inserted
     * @param  mixed $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        $this->id = generateUUID();

        parent::__construct($attributes);
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'merchant_id',
        'routing_no',
        'bank_name',
        'banker',
        'fees_account_number',
        'account_no',
        'account_type',
        'username',
        'password',
        'secret_key_url',
        'account_type',
        'institution_id',
        'account_id',
        'finicity_id',
        'status',
        'token',
        'qr_url',
        'one_time_transaction_limit',
        'external_validation_type',
        'account_verified',
        'mobile_verified',
        'email_verified',
    ];

    public $incrementing = false;
    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
}
