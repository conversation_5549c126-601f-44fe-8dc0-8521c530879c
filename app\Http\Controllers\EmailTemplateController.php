<?php

namespace App\Http\Controllers;

use App\Http\Factories\EmailExecutor\EmailExecutorFactory;
use App\Models\EmailTemplate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class EmailTemplateController extends Controller
{
    public function __construct(Request $request)
    {
        $this->emailexecutor = new EmailExecutorFactory();
    }

    /**
     * getAllStores
     * Listing page for Email Templates along with Server Side Pagination in Datatable
     * @param  mixed $request
     * @return void
     */
    public function getAllEmailTemplates(Request $request)
    {
        // Columns defined for Sorting
        $columns = array(
            0 => 'template_name',
            1 => 'template_subject',
            2 => 'updated_at',
        );
        //Count Query 
        $queryCount = EmailTemplate::on(MYSQL_RO)
        ->selectRaw('COUNT(*) as total_count');
        $totalData = $queryCount->first()->total_count; // Getting total no of rows

        $totalFiltered = $totalData;
        $limit = intval($request->input('length'));
        $start = intval($request->input('start'));
        $order = $columns[$request->input('order.0.column')];
        $dir = $request->input('order.0.dir');
        if (empty($request->input('search.value')) && empty($order) && empty($dir)) {
            $templates = EmailTemplate::on(MYSQL_RO)->offset($start)->limit(intval($limit))->orderBy('merchant_stores.created_at', 'DESC')->get();
        } else if (empty($request->input('search.value'))) {
            $templates = EmailTemplate::on(MYSQL_RO)->offset($start)->limit(intval($limit))->orderBy($order, $dir)->get();
        } else {
            $search = $request->input('search.value');
            $search_query = EmailTemplate::on(MYSQL_RO)->where('id', 'LIKE', "%{$search}%")->orWhere('template_name', 'LIKE', "%{$search}%")->orWhere('template_subject', 'LIKE', "%{$search}%");

            $search_query_count = $queryCount->where('id', 'LIKE', "%{$search}%")->orWhere('template_name', 'LIKE', "%{$search}%")->orWhere('template_subject', 'LIKE', "%{$search}%");

            $totalFiltered = $search_query_count->first()->total_count;

            $templates = $search_query->offset($start)->limit(intval($limit))->orderBy($order, $dir)->get();
        }

        $data = array();
        if (!empty($templates)) {
            // Creating array to show the values in frontend
            foreach ($templates as $template) {
                $nestedData['template_name'] = $template->template_name;
                $nestedData['template_subject'] = $template->template_subject;
                $nestedData['status'] = $template->status;
                $nestedData['edit'] = $template->id;
                $nestedData['created_at'] = date('m-d-Y h:i A', strtotime($template->created_at));
                $nestedData['updated_at'] = date('m-d-Y h:i A', strtotime($template->updated_at));
                $data[] = $nestedData;
            }
        }
        // Drawing the Datatable
        $json_data = array(
            "draw" => intval($request->input('draw')),
            "recordsTotal" => intval($totalData),
            "recordsFiltered" => intval($totalFiltered),
            "data" => $data,
        );

        Log::info(addslashes(__METHOD__) . "(" . LINE . __LINE__ . ") : Email Template List fetched successfully");
        echo json_encode($json_data); // Rerurning the data
    }

    /**
     * getTemplateDetails
     * This function is used to fetch a particular Template Details for Update and View Purpose
     * @param  mixed $request
     * @return void
     */
    public function getTemplateDetails(Request $request)
    {
        $rule = array(
            'template_id' => VALIDATION_REQUIRED,
        );
        $this->__validate($request->all(), $rule);

        $user_details = Auth::user(); // Fetching Logged In User Details

        $template_details = EmailTemplate::find($request->get('template_id')); // Fetching Tempate Details

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Email Template Details Fetched Syccessfully by User ID : " . $user_details->user_id);
        $message = trans('message.template_details_fetch_success');
        // API Response returned with 200 status
        return renderResponse(SUCCESS, $message, $template_details);
    }

    /**
     * updateEmailTemplate
     * This Function is used to Update the Email Template
     * @param  mixed $request
     * @return void
     */
    public function updateEmailTemplate(Request $request)
    {
        $rule = array(
            'id' => VALIDATION_REQUIRED,
            'template_subject' => VALIDATION_REQUIRED,
            'template_body' => VALIDATION_REQUIRED,
        );
        $this->__validate($request->all(), $rule);

        $user_details = Auth::user(); // Fetching Logged In User Details

        // Updating the Email Template
        $template_details = EmailTemplate::find($request->get('id'));
        $template_details->template_subject = $request->get('template_subject');
        $template_details->template_body = $request->get('template_body');
        $template_details->save();

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Email Template Details Updated Syccessfully for Template ID : " . $request->get('id') . " by User ID : " . $user_details->user_id);
        $message = trans('message.template_details_update_success');
        // API Response returned with 200 status
        return renderResponse(SUCCESS, $message, $template_details);
    }

    /**
     * sendMail
     * This function is sued to send test mail to the given email address so that admin can check the template in HTML view
     * @param  mixed $request
     * @return void
     */
    public function sendMail(Request $request)
    {
        $rule = array(
            'email_address' => VALIDATION_REQUIRED . '|' . VALIDATION_EMAIL,
            'template_name' => VALIDATION_REQUIRED,
        );
        $this->__validate($request->all(), $rule);

        // Sending Test Email
        $params = [
            'email' => $request->get('email_address'),
            'template_name' => $request->get('template_name'),
        ];
        $this->emailexecutor->testEmail($params);

        $message = trans('message.test_email_success');
        // API Response returned with 200 status
        return renderResponse(SUCCESS, $message, null);

    }
}
