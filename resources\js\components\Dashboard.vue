<template>
<div>
   <div v-if="loading">
      <CanPayLoader/>
   </div>
   <div class="content-wrapper" style="min-height: 36px;">
      <section class="content-header">
         <div class="container-fluid">
            <div class="row mb-2">
               <div class="col-sm-6">
                  <div class="input-group">
                    <h1 class="m-0">Dashboard</h1>
                 </div>
               </div>
               <div class="col-sm-6">
                  <ol class="breadcrumb float-sm-right">
                     <li class="select-date-cls">
                        <b
                           >Selected Date :
                        <span v-html="selected_date"></span
                           ></b>
                     </li>
                     <li>
                        <h1 class="calendar-link">
                           <i
                              class="fas fa-calendar-alt"
                              id="select-date"
                              ></i>
                        </h1>
                     </li>
                  </ol>
               </div>
            </div>
         </div>
      </section>
      <section class="content">

         <div class="container-fluid">
            <div class="row">
               <div class="col-12 col-md-5 info-box">
                  <div class="row">
                     <div class="col-12">
                        <h3><strong> Sales V2</strong></h3>
                     </div>
                     <div class="col-12">
                        <div class="info-box">
                           <span class="info-box-icon bg-indigo elevation-1"
                              ><i class="fas fa-dollar-sign"></i
                              ></span>
                           <div class="info-box-content">
                              <span class="info-box-text">Total Sales</span>
                              <span
                                 class="info-box-number"
                                 v-html="total_sales_v2"
                                 >
                              </span>
                           </div>
                        </div>
                     </div>
                     <div class="col-12 ">
                        <div class="info-box mb-4">
                           <span class="info-box-icon bg-pink elevation-1"
                              ><i class="far fa-calendar-times"></i
                              ></span>
                           <div class="info-box-content">
                              <span class="info-box-text"
                                 >Total Monthly Sales</span
                                 >
                              <span
                                 class="info-box-number"
                                 v-html="total_monthly_sales_v2"
                                 ></span>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
               <div class="col-1"></div>
               <div class="clearfix hidden-md-up"></div>
               <div class="col-12 col-md-5 info-box">
                  <div class="row">
                     <div class="col-12">
                        <h3><strong> Transactions V2</strong></h3>
                     </div>
                     <div class="col-12">
                        <div class="info-box ">
                           <span class="info-box-icon bg-indigo elevation-1"
                              ><i class="fas fa-sync-alt"></i
                              ></span>
                           <div class="info-box-content">
                              <span class="info-box-text"
                                 >Total Transactions</span
                                 >
                              <span
                                 class="info-box-number"
                                 v-html="total_transactions_v2"
                                 >
                              </span>
                           </div>
                        </div>
                     </div>
                     <div class="col-12 ">
                        <div class="info-box mb-4">
                           <span class="info-box-icon bg-yellow elevation-1"
                              ><i class="fas fa-calendar-minus"></i
                              ></span>
                           <div class="info-box-content">
                              <span class="info-box-text"
                                 >Monthly Transactions</span
                                 >
                              <span
                                 class="info-box-number"
                                 v-html="total_monthly_transactions_v2"
                                 ></span>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
            <div class="row">
               <div class="col-12 col-md-5 info-box">
                  <div class="row">
                     <div class="col-12">
                        <h3><strong> Admin Driven RemotePay Transactions</strong></h3>
                     </div>
                     <div class="col-12">
                        <div class="info-box ">
                           <span class="info-box-icon bg-indigo elevation-1"
                              ><i class="fas fa-dollar-sign"></i
                              ></span>
                           <div class="info-box-content">
                              <span class="info-box-text text-wrap"
                                 >Daily Admin Driven Transactions <span class="text-danger">(Awaiting Approval)</span></span
                                 >
                              <span
                                 class="info-box-number"
                                 v-html="total_daily_admin_driven_not_posted_amount"
                                 >
                              </span>
                           </div>
                        </div>
                     </div>
                     <div class="col-12 ">
                        <div class="info-box mb-4">
                           <span class="info-box-icon bg-pink elevation-1"
                              ><i class="far fa-calendar-times"></i
                              ></span>
                           <div class="info-box-content">
                              <span class="info-box-text text-wrap"
                                 >Total Monthly Admin Driven Transactions <span class="text-danger">(Awaiting Approval)</span></span
                                 >
                              <span
                                 class="info-box-number"
                                 v-html="total_monthly_admin_driven_not_posted_amount"
                                 ></span>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
               <div class="col-1"></div>
               <div class="clearfix hidden-md-up"></div>
               <div class="col-12 col-md-5 info-box">
                  <div class="row">
                     <div class="col-12">
                        <h3><strong> Remotepay Enrollments</strong></h3>
                     </div>
                     <div class="col-12">
                        <div class="info-box">
                           <span class="info-box-icon bg-success elevation-1"
                              ><i class="fas fa-user-plus"></i
                              ></span>
                           <div class="info-box-content">
                              <span class="info-box-text">Daily Enrollments</span>
                              <span
                                 class="info-box-number"
                                 v-html="remotepay_daily_registration"
                                 >
                              </span>
                           </div>
                        </div>
                     </div>
                     <div class="col-12 ">
                        <div class="info-box mb-4">
                           <span class="info-box-icon bg-primary elevation-1"
                              ><i class="fas fa-user-plus"></i
                              ></span>
                           <div class="info-box-content">
                              <span class="info-box-text"
                                 >Monthly Enrollments</span
                                 >
                              <span
                                 class="info-box-number"
                                 v-html="remotepay_monthly_registration"
                                 ></span>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
            <div class="row">
               <div class="col-12 col-md-5 info-box">
                  <div class="row">
                     <div class="col-12">
                        <h3><strong> Daily Enrollments</strong></h3>
                     </div>
                     <div class="col-12">
                        <div class="info-box">
                           <span class="info-box-icon bg-success elevation-1"
                              ><i class="fas fa-user-plus"></i
                              ></span>
                           <div class="info-box-content">
                              <span class="info-box-text">Migrated Consumer(V1)</span>
                              <span
                                 class="info-box-number"
                                 v-html="new_enrollments_v1"
                                 >
                              </span>
                           </div>
                        </div>
                     </div>
                     <div class="col-12 ">
                        <div class="info-box mb-4">
                           <span class="info-box-icon bg-primary elevation-1"
                              ><i class="fas fa-user-plus"></i
                              ></span>
                           <div class="info-box-content">
                              <span class="info-box-text"
                                 >New Enrollments (v2)</span
                                 >
                              <span
                                 class="info-box-number"
                                 v-html="new_enrollments_v2"
                                 ></span>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
               <div class="col-1"></div>
               <div class="clearfix hidden-md-up"></div>
               <div class="col-12 col-md-5 info-box">
                  <div class="row">
                     <div class="col-12">
                        <h3><strong> Total Enrollments</strong></h3>
                     </div>
                     <div class="col-12">
                        <div class="info-box">
                           <span class="info-box-icon bg-secondary elevation-1"
                              ><i class="fas fa-users"></i
                              ></span>
                           <div class="info-box-content">
                              <span class="info-box-text">Total Migrated Consumer(V1)</span>
                              <span
                                 class="info-box-number"
                                 v-html="total_enrollments_v1"
                                 >
                              </span>
                           </div>
                        </div>
                     </div>
                     <div class="col-12 ">
                        <div class="info-box mb-4">
                           <span class="info-box-icon bg-cyan elevation-1"
                              ><i class="fas fa-users"></i
                              ></span>
                           <div class="info-box-content">
                              <span class="info-box-text"
                                 >Total Enrollments (v2)</span
                                 >
                              <span
                                 class="info-box-number"
                                 v-html="total_enrollments_v2"
                                 ></span>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
            <div class="row">
               <div class="col-12 col-md-5 info-box">
                  <div class="row">
                     <div class="col-12">
                        <h3><strong> Lite User Enrollments</strong></h3>
                     </div>
                     <div class="col-12">
                        <div class="info-box">
                           <span class="info-box-icon bg-success elevation-1"
                              ><i class="fas fa-user-plus"></i
                              ></span>
                           <div class="info-box-content">
                              <span class="info-box-text">Lite Users Enrolled This Month</span>
                              <span
                                 class="info-box-number"
                                 v-html="lite_users_monthly"
                                 >
                              </span>
                           </div>
                        </div>
                     </div>
                     <div class="col-12 ">
                        <div class="info-box mb-4">
                           <span class="info-box-icon bg-primary elevation-1"
                              ><i class="fas fa-user-plus"></i
                              ></span>
                           <div class="info-box-content">
                              <span class="info-box-text"
                                 >Lite Users Enrolled Today</span
                                 >
                              <span
                                 class="info-box-number"
                                 v-html="lite_users_today"
                                 ></span>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
               <div class="col-1"></div>
               <div class="clearfix hidden-md-up"></div>
               <div class="col-12 col-md-5 info-box">
                  <div class="row">
                     <div class="col-12">
                        <h3><strong>Lite Users Upgraded to Standard</strong></h3>
                     </div>
                     <div class="col-12">
                        <div class="info-box">
                           <span class="info-box-icon bg-secondary elevation-1"
                              ><i class="fas fa-users"></i
                              ></span>
                           <div class="info-box-content">
                              <span class="info-box-text">Lite Users Converted to Standard This Month</span>
                              <span
                                 class="info-box-number"
                                 v-html="lite_to_standard_conversions_monthly"
                                 >
                              </span>
                           </div>
                        </div>
                     </div>
                     <div class="col-12 ">
                        <div class="info-box mb-4">
                           <span class="info-box-icon bg-cyan elevation-1"
                              ><i class="fas fa-users"></i
                              ></span>
                           <div class="info-box-content">
                              <span class="info-box-text"
                                 >Lite Users Converted to Standard Today</span
                                 >
                              <span
                                 class="info-box-number"
                                 v-html="lite_to_standard_conversions_today"
                                 ></span>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
            <div class="row ">
               <div class="col-12 col-md-5 info-box">
                  <div class="row">
                     <div class="col-12">
                        <h3><strong>  Last 7 days Sales and Transaction </strong></h3>
                        <div class="row">
                           <div class="col-6">
                              <label> <strong>Start Date : </strong> {{end_date}}</label>
                           </div>
                           <div class="col-6">
                              <label> <strong>End Date : </strong> {{start_date}}</label>
                           </div>
                        </div>
                     </div>
                     <div class="col-12">
                        <div class="info-box mb-4">
                           <span class="info-box-icon bg-warning elevation-1"
                              ><i class="fas fa-dollar-sign"></i
                              ></span>
                           <div class="info-box-content">
                              <span class="info-box-text text-justify"
                                 >Average  sales over </br>last 7 days</span
                                 >
                              <span
                                 class="info-box-number"
                                 v-html="avg_weekly_sales"
                                 ></span>
                           </div>
                        </div>
                     </div>
                     <div class="clearfix hidden-md-up"></div>
                     <div class="col-12">
                        <div class="info-box mb-4">
                           <span class="info-box-icon bg-gradient-light elevation-1"
                              ><i class="fas fa-hashtag"></i></span>
                           <div class="info-box-content">
                              <span class="info-box-text text-justify"
                                 >Average Transactions </br> over the last 7 days</span
                                 >
                              <span
                                 class="info-box-number"
                                 v-html="avg_weekly_transactions"
                                 ></span>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
               <div class="col-1"></div>
               <div class="clearfix hidden-md-up"></div>
               <div class="col-12 col-md-5 info-box">
                  <div class="row">
                     <div class="col-12">
                         <div class="input-group">
                            <h3><strong>  Manual vs Direct Linked </strong></h3>
                            <span class="input-group-addon ml-3">
                                <button type="button" class="btn btn-success" @click="getManualvsDirectData()">Generate</button>
                            </span>
                        </div>
                        <div class="row" v-if="generateReport == 1">
                           <div class="col-12">
                              <label> <strong>Total Consumers : </strong> {{total_active_conusmers}}</label>
                           </div>
                        </div>
                     </div>
                    <div class="col-12" v-if="generateReport == 1">
                        <div class="info-box mb-4">
                            <table class="table">
                                <thead>
                                <tr style="border-top: hidden;">
                                    <th>Head</th>
                                    <th>Manual Linked</th>
                                    <th>Direct Linked</th>
                                </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>V2 Transaction Count</td>
                                        <td>{{manual_link_consumer_count}}</td>
                                        <td>{{direct_link_consumer_count}}</td>
                                    </tr>
                                    <tr>
                                        <td>v2 Transaction %</td>
                                        <td>{{manual_link_transaction_percent}}</td>
                                        <td>{{direct_link_transaction_percent}}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                     </div>
                    <div class="col-12 text-center" v-if="generateReport == 0">
                        <label>Please click the Generate button to view this report.</label>
                    </div>
                  </div>
               </div>
            </div>
         </div>
      </section>
   </div>
</div>
</template>
<script>
import api from "@/api/transaction.js";
import moment from "moment";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "./CustomLoader/CanPayLoader.vue";
export default {
  data() {
    return {
      total_sales_v1: 0,
      total_transactions_v1: 0,
      total_monthly_sales_v1: 0,
      total_monthly_transactions_v1: 0,
      total_sales_v2: 0,
      total_transactions_v2: 0,
      total_monthly_sales_v2: 0,
      total_monthly_transactions_v2: 0,
      new_enrollments_v1: 0,
      total_enrollments_v1: 0,
      new_enrollments_v2: 0,
      total_enrollments_v2: 0,
      avg_weekly_sales: 0,
      avg_weekly_transactions: 0,
      total_active_conusmers: 0,
      manual_link_consumer_count: 0,
      direct_link_consumer_count: 0,
      manual_link_transaction_percent: 0,
      direct_link_transaction_percent: 0,
      remotepay_daily_registration: 0,
      remotepay_monthly_registration: 0,
      total_daily_admin_driven_not_posted_amount: 0,
      total_monthly_admin_driven_not_posted_amount: 0,
      generateReport: 0,
      lite_users_monthly: 0,
      lite_users_today: 0,
      lite_to_standard_conversions_monthly: 0,
      lite_to_standard_conversions_today: 0,
      loading: false,
      start_date: "",
      end_date: "",
      selected_date: moment($().val()).format("MM/DD/YYYY"),
      searchDate: moment($().val()).format("YYYY-MM-DD"),
    };
  },
  components: {
    HourGlass,
    CanPayLoader
  },
  created() {
    //Fetch the Transaction details
    this.getTransactionDetails();
  },
  mounted() {
    var self = this;
    $("#select-date")
      .datepicker({
        format: "mm/dd/yyyy",
        autoclose: true,
        todayHighlight: true,
        endDate: new Date(),
      })
      .on("changeDate", function (e) {
        self.selected_date = e.format();
        self.searchDate = e.format("yyyy-mm-dd");
        //Fetch the Transaction Details
        self.getTransactionDetails();
      });
  },
  methods: {
    // API call to generate the storewise Transaction Details
    getTransactionDetails() {
      var self = this;
      var request = {
        searchDate: self.searchDate,
      };
      self.loading = true;
      api
        .getTransactionDetails(request)
        .then(function (response) {
          if (response.code == 200) {
            //Populate the Boxes
            self.total_sales_v1 = "$" + response.data.total_sales_v1;
            self.total_sales_v2 = "$" + response.data.total_sales_v2;
            self.total_transactions_v1 = response.data.total_transactions_v1;
            self.total_transactions_v2 = response.data.total_transactions_v2;
            self.total_monthly_sales_v1 =
              "$" + response.data.total_monthly_sales_v1;
            self.total_monthly_sales_v2 =
              "$" + response.data.total_monthly_sales_v2;
            self.total_monthly_transactions_v1 =
              response.data.total_monthly_transactions_v1;
            self.total_monthly_transactions_v2 =
              response.data.total_monthly_transactions_v2;
            self.total_daily_admin_driven_not_posted_amount =
              response.data.total_daily_admin_driven_not_posted_amount;
            self.total_monthly_admin_driven_not_posted_amount =
              response.data.total_monthly_admin_driven_not_posted_amount;
            self.new_enrollments_v1 = response.data.new_enrollments_v1;
            self.total_enrollments_v1 = response.data.total_enrollments_v1;
            self.new_enrollments_v2 = response.data.new_enrollments_v2;
            self.total_enrollments_v2 = response.data.total_enrollments_v2;
            self.avg_weekly_sales = "$" + response.data.avg_weekly_sales;
            self.avg_weekly_transactions =
              response.data.avg_weekly_transactions;
            self.start_date = response.data.start_date;
            self.end_date = response.data.end_date;
            self.remotepay_daily_registration = response.data.remotepay_daily_registration;
            self.remotepay_monthly_registration = response.data.remotepay_monthly_registration;
            self.lite_users_monthly = response.data.lite_users_monthly;
            self.lite_users_today = response.data.lite_users_today;
            self.lite_to_standard_conversions_monthly = response.data.lite_to_standard_conversions_monthly;
            self.lite_to_standard_conversions_today = response.data.lite_to_standard_conversions_today;
            self.loading = false;
          } else {
            error(response.message);
            self.loading = false;
          }
        })
        .catch(function (error) {
          // error(error);
          self.loading = false;
        });
    },
    getManualvsDirectData() {
      var self = this;
      self.loading = true;
      api
        .getManualvsDirectData()
        .then(function (response) {
          if (response.code == 200) {
            //Populate the Boxes
            self.total_active_conusmers = response.data.total_active_conusmers
            self.manual_link_consumer_count = response.data.manual_link_consumer_count
            self.direct_link_consumer_count = response.data.direct_link_consumer_count
            self.manual_link_transaction_percent = response.data.manual_link_transaction_percent
            self.direct_link_transaction_percent = response.data.direct_link_transaction_percent
            self.loading = false;
            self.generateReport = 1;
          } else {
            error(response.message);
            self.loading = false;
          }
        })
        .catch(function (error) {
          // error(error);
          self.loading = false;
        });
    },
  },
};
</script>
