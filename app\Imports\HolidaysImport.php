<?php

namespace App\Imports;

use App\Models\RegisteredMerchantMaster;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use App\Models\HolidayList;
use Illuminate\Support\Facades\Auth;
use Datetime;
use Illuminate\Support\Facades\DB;
class HolidaysImport implements ToModel
{
    use Importable;
    protected $first_year;
    protected $second_year;
    protected $third_year;
    protected $fourth_year;
    protected $fifth_year;
    protected $inc = 1;
    /**
     * @param array $row
     * This function actully imports the data as row from Excel Sheet. Here we used the WithHeadingRow to get the Data with Heading. Do Not try to get the rows with index.
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row)
    {

       // first row header store as year
        if ($this->inc == 1) {
            $this->first_year = $row[1];
            $this->second_year = $row[2];
            $this->third_year = $row[3];
            $this->fourth_year = $row[4];
            $this->fifth_year = $row[5];
        } else {
            DB::beginTransaction();
            try {
               if(!empty($row[1]))
               {
                $checkFirstYearDateExist = HolidayList::select('holiday_lists.id')->where('holiday_year', $this->first_year)->where('holiday_name', ucfirst($row[0]))->get()->toArray();;
                if (empty($checkFirstYearDateExist)) {
                    $holidayList = new HolidayList();
                    $holidayList->holiday_name = ucfirst($row[0]);
                    $holidayList->holiday_year = $this->first_year;
                    $holidayList->holiday_date = date('Y-m-d', strtotime(str_replace("*", "", $row[1] . " " . $this->first_year)));
                    $holidayList->added_by =  Auth::user()->user_id;
                    $holidayList->save();
                    Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - ".ucfirst($row[0]) ." is inserted in year ".$this->first_year);
                    if ((date('l', strtotime(str_replace("*", "", $row[1] . " " . $this->first_year)))) == "Sunday") {
                        $date = new DateTime(date('Y-m-d', strtotime(str_replace("*", "", $row[1] . " " . $this->first_year))));
                        $date->modify('+1 day');
                        $nextDay = $date->format('Y-m-d');
                        $holidayList = new HolidayList();
                        $holidayList->holiday_name = ucfirst($row[0]);
                        $holidayList->holiday_year = $this->first_year;
                        $holidayList->holiday_date = $nextDay;
                        $holidayList->added_by =  Auth::user()->user_id;
                        $holidayList->reason = FOR_HOLIDAYS_FALLING_ON_SUNDAY_FEDERAL_RESERVE_BANKS_AND_BRANCHES_WILL_BE_CLOSED_THE_FOLLOWING_MONDAY;
                        $holidayList->save();
                        Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - ".ucfirst($row[0]) ." is inserted in year ".$this->first_year . " due to ".FOR_HOLIDAYS_FALLING_ON_SUNDAY_FEDERAL_RESERVE_BANKS_AND_BRANCHES_WILL_BE_CLOSED_THE_FOLLOWING_MONDAY);
                    }
                } else {
                    $checkFirstYearDateExist = array_column($checkFirstYearDateExist,'id');

                    HolidayList::whereIn('id', $checkFirstYearDateExist)->delete();
                    $holidayList = new HolidayList();
                    $holidayList->holiday_name = ucfirst($row[0]);
                    $holidayList->holiday_year = $this->first_year;
                    $holidayList->holiday_date = date('Y-m-d', strtotime(str_replace("*", "", $row[1] . " " . $this->first_year)));
                    $holidayList->added_by =  Auth::user()->user_id;
                    $holidayList->save();
                    Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - ".ucfirst($row[0]) ." is update in year ".$this->first_year);

                    if ((date('l', strtotime(str_replace("*", "", $row[1] . " " . $this->first_year)))) == "Sunday") {
                        $date = new DateTime(date('Y-m-d', strtotime(str_replace("*", "", $row[1] . " " . $this->second_year))));
                        $date->modify('+1 day');
                        $nextDay = $date->format('Y-m-d');
                        $holidayList = new HolidayList();
                        $holidayList->holiday_name = ucfirst($row[0]);
                        $holidayList->holiday_year = $this->first_year;
                        $holidayList->holiday_date = $nextDay;
                        $holidayList->added_by =  Auth::user()->user_id;
                        $holidayList->reason = FOR_HOLIDAYS_FALLING_ON_SUNDAY_FEDERAL_RESERVE_BANKS_AND_BRANCHES_WILL_BE_CLOSED_THE_FOLLOWING_MONDAY;
                        $holidayList->save();
                        Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - ".ucfirst($row[0]) ." is inserted in year ".$this->first_year . " due to ".FOR_HOLIDAYS_FALLING_ON_SUNDAY_FEDERAL_RESERVE_BANKS_AND_BRANCHES_WILL_BE_CLOSED_THE_FOLLOWING_MONDAY);
                    }
                }
               }
               if(!empty($row[2]))
               {
                $checkSecondYearDateExist = HolidayList::select('holiday_lists.id')->where('holiday_year', $this->second_year)->where('holiday_name', ucfirst($row[0]))->get()->toArray();;
                if (empty($checkSecondYearDateExist)) {
                    $holidayList = new HolidayList();
                    $holidayList->holiday_name = ucfirst($row[0]);
                    $holidayList->holiday_year = $this->second_year;
                    $holidayList->holiday_date = date('Y-m-d', strtotime(str_replace("*", "", $row[2] . " " . $this->second_year)));
                    $holidayList->added_by =  Auth::user()->user_id;
                    $holidayList->save();
                    Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - ".ucfirst($row[0]) ." is inserted in year ".$this->second_year);
                    if ((date('l', strtotime(str_replace("*", "", $row[2] . " " . $this->second_year)))) == "Sunday") {
                        $date = new DateTime(date('Y-m-d', strtotime(str_replace("*", "", $row[2] . " " . $this->second_year))));
                        $date->modify('+1 day');
                        $nextDay = $date->format('Y-m-d');
                        $holidayList = new HolidayList();
                        $holidayList->holiday_name = ucfirst($row[0]);
                        $holidayList->holiday_year = $this->second_year;
                        $holidayList->holiday_date = $nextDay;
                        $holidayList->added_by =  Auth::user()->user_id;
                        $holidayList->reason = FOR_HOLIDAYS_FALLING_ON_SUNDAY_FEDERAL_RESERVE_BANKS_AND_BRANCHES_WILL_BE_CLOSED_THE_FOLLOWING_MONDAY;
                        $holidayList->save();

                        Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - ".ucfirst($row[0]) ." is inserted in year ".$this->second_year . " due to ".FOR_HOLIDAYS_FALLING_ON_SUNDAY_FEDERAL_RESERVE_BANKS_AND_BRANCHES_WILL_BE_CLOSED_THE_FOLLOWING_MONDAY);

                    }
                } else {
                    $checkSecondYearDateExist = array_column($checkSecondYearDateExist,'id');
                    HolidayList::whereIn('id', $checkSecondYearDateExist)->delete();
                    $holidayList = new HolidayList();
                    $holidayList->holiday_name = ucfirst($row[0]);
                    $holidayList->holiday_year = $this->second_year;
                    $holidayList->holiday_date = date('Y-m-d', strtotime(str_replace("*", "", $row[2] . " " . $this->second_year)));
                    $holidayList->added_by =  Auth::user()->user_id;
                    $holidayList->save();
                    Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - ".ucfirst($row[0]) ." is update in year ".$this->second_year);

                    if ((date('l', strtotime(str_replace("*", "", $row[2] . " " . $this->second_year)))) == "Sunday") {
                        $date = new DateTime(date('Y-m-d', strtotime(str_replace("*", "", $row[2] . " " . $this->second_year))));
                        $date->modify('+1 day');
                        $nextDay = $date->format('Y-m-d');
                        $holidayList = new HolidayList();
                        $holidayList->holiday_name = ucfirst($row[0]);
                        $holidayList->holiday_year = $this->second_year;
                        $holidayList->holiday_date = $nextDay;
                        $holidayList->added_by =  Auth::user()->user_id;
                        $holidayList->reason = FOR_HOLIDAYS_FALLING_ON_SUNDAY_FEDERAL_RESERVE_BANKS_AND_BRANCHES_WILL_BE_CLOSED_THE_FOLLOWING_MONDAY;
                        $holidayList->save();

                        Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - ".ucfirst($row[0]) ." is inserted in year ".$this->second_year . " due to ".FOR_HOLIDAYS_FALLING_ON_SUNDAY_FEDERAL_RESERVE_BANKS_AND_BRANCHES_WILL_BE_CLOSED_THE_FOLLOWING_MONDAY);

                    }
                }
               }
           if(!empty($row[3]))
           {
            $checkThirdYearDateExist = HolidayList::select('holiday_lists.id')->where('holiday_year', $this->third_year)->where('holiday_name', ucfirst($row[0]))->get()->toArray();;
            if (empty($checkThirdYearDateExist)) {
                $holidayList = new HolidayList();
                $holidayList->holiday_name = ucfirst($row[0]);
                $holidayList->holiday_year = $this->third_year;
                $holidayList->holiday_date = date('Y-m-d', strtotime(str_replace("*", "", $row[3] . " " . $this->third_year)));
                $holidayList->added_by =  Auth::user()->user_id;
                $holidayList->save();
                Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - ".ucfirst($row[0]) ." is inserted in year ".$this->third_year);
                if ((date('l', strtotime(str_replace("*", "", $row[3] . " " . $this->third_year)))) == "Sunday") {
                    $date = new DateTime(date('Y-m-d', strtotime(str_replace("*", "", $row[3] . " " . $this->third_year))));
                    $date->modify('+1 day');
                    $nextDay = $date->format('Y-m-d');
                    $holidayList = new HolidayList();
                    $holidayList->holiday_name = ucfirst($row[0]);
                    $holidayList->holiday_year = $this->third_year;
                    $holidayList->holiday_date = $nextDay;
                    $holidayList->added_by =  Auth::user()->user_id;
                    $holidayList->reason = FOR_HOLIDAYS_FALLING_ON_SUNDAY_FEDERAL_RESERVE_BANKS_AND_BRANCHES_WILL_BE_CLOSED_THE_FOLLOWING_MONDAY;
                    $holidayList->save();
                    Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - ".ucfirst($row[0]) ." is inserted in year ".$this->third_year . " due to ".FOR_HOLIDAYS_FALLING_ON_SUNDAY_FEDERAL_RESERVE_BANKS_AND_BRANCHES_WILL_BE_CLOSED_THE_FOLLOWING_MONDAY);
                }
            } else {
                $checkThirdYearDateExist = array_column($checkThirdYearDateExist,'id');
                HolidayList::whereIn('id', $checkThirdYearDateExist)->delete();

                $holidayList = new HolidayList();
                $holidayList->holiday_name = ucfirst($row[0]);
                $holidayList->holiday_year = $this->third_year;
                $holidayList->holiday_date = date('Y-m-d', strtotime(str_replace("*", "", $row[3] . " " . $this->third_year)));
                $holidayList->added_by =  Auth::user()->user_id;
                $holidayList->save();
                Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - ".ucfirst($row[0]) ." is update in year ".$this->third_year);

                if ((date('l', strtotime(str_replace("*", "", $row[3] . " " . $this->third_year)))) == "Sunday") {

                    $date = new DateTime(date('Y-m-d', strtotime(str_replace("*", "", $row[3] . " " . $this->third_year))));
                    $date->modify('+1 day');
                    $nextDay = $date->format('Y-m-d');
                    $holidayList = new HolidayList();
                    $holidayList->holiday_name = ucfirst($row[0]);
                    $holidayList->holiday_year = $this->third_year;
                    $holidayList->holiday_date = $nextDay;
                    $holidayList->added_by =  Auth::user()->user_id;
                    $holidayList->reason = FOR_HOLIDAYS_FALLING_ON_SUNDAY_FEDERAL_RESERVE_BANKS_AND_BRANCHES_WILL_BE_CLOSED_THE_FOLLOWING_MONDAY;
                    $holidayList->save();
                    Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - ".ucfirst($row[0]) ." is inserted in year ".$this->third_year . " due to ".FOR_HOLIDAYS_FALLING_ON_SUNDAY_FEDERAL_RESERVE_BANKS_AND_BRANCHES_WILL_BE_CLOSED_THE_FOLLOWING_MONDAY);
                }
            }
           }
                if(!empty($row[4]))
                {

                    $checkFourthYearDateExist = HolidayList::select('holiday_lists.id')->where('holiday_year', $this->fourth_year)->where('holiday_name', ucfirst($row[0]))->get()->toArray();;
                    if (empty($checkFourthYearDateExist)) {
                        $holidayList = new HolidayList();
                        $holidayList->holiday_name = ucfirst($row[0]);
                        $holidayList->holiday_year = $this->fourth_year;
                        $holidayList->holiday_date = date('Y-m-d', strtotime(str_replace("*", "", $row[4] . " " . $this->fourth_year)));
                        $holidayList->added_by =  Auth::user()->user_id;
                        $holidayList->save();
                        Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - ".ucfirst($row[0]) ." is inserted in year ".$this->fourth_year);
    
                        if ((date('l', strtotime(str_replace("*", "", $row[4] . " " . $this->fourth_year)))) == "Sunday") {
                            $date = new DateTime(date('Y-m-d', strtotime(str_replace("*", "", $row[4] . " " . $this->fourth_year))));
                            $date->modify('+1 day');
                            $nextDay = $date->format('Y-m-d');
                            $holidayList = new HolidayList();
                            $holidayList->holiday_name = ucfirst($row[0]);
                            $holidayList->holiday_year = $this->fourth_year;
                            $holidayList->holiday_date = $nextDay;
                            $holidayList->added_by =  Auth::user()->user_id;
                            $holidayList->reason = FOR_HOLIDAYS_FALLING_ON_SUNDAY_FEDERAL_RESERVE_BANKS_AND_BRANCHES_WILL_BE_CLOSED_THE_FOLLOWING_MONDAY;
                            $holidayList->save();
                            Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - ".ucfirst($row[0]) ." is inserted in year ".$this->fourth_year . " due to ".FOR_HOLIDAYS_FALLING_ON_SUNDAY_FEDERAL_RESERVE_BANKS_AND_BRANCHES_WILL_BE_CLOSED_THE_FOLLOWING_MONDAY);
                        }
                    }
                    else
                    {
                        $checkFourthYearDateExist = array_column($checkFourthYearDateExist,'id');
                        HolidayList::whereIn('id', $checkFourthYearDateExist)->delete();
                        $holidayList = new HolidayList();
                        $holidayList->holiday_name = ucfirst($row[0]);
                        $holidayList->holiday_year = $this->fourth_year;
                        $holidayList->holiday_date = date('Y-m-d', strtotime(str_replace("*", "", $row[4] . " " . $this->fourth_year)));
                        $holidayList->added_by =  Auth::user()->user_id;
                        $holidayList->save();
                        Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - ".ucfirst($row[0]) ." is update in year ".$this->fourth_year);
    
                        if ((date('l', strtotime(str_replace("*", "", $row[4] . " " . $this->fourth_year)))) == "Sunday") {
                            $date = new DateTime(date('Y-m-d', strtotime(str_replace("*", "", $row[4] . " " . $this->fourth_year))));
                            $date->modify('+1 day');
                            $nextDay = $date->format('Y-m-d');
                            $holidayList = new HolidayList();
                            $holidayList->holiday_name = ucfirst($row[0]);
                            $holidayList->holiday_year = $this->fourth_year;
                            $holidayList->holiday_date = $nextDay;
                            $holidayList->added_by =  Auth::user()->user_id;
                            $holidayList->reason = FOR_HOLIDAYS_FALLING_ON_SUNDAY_FEDERAL_RESERVE_BANKS_AND_BRANCHES_WILL_BE_CLOSED_THE_FOLLOWING_MONDAY;
                            $holidayList->save();
                            Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - ".ucfirst($row[0]) ." is inserted in year ".$this->fourth_year . " due to ".FOR_HOLIDAYS_FALLING_ON_SUNDAY_FEDERAL_RESERVE_BANKS_AND_BRANCHES_WILL_BE_CLOSED_THE_FOLLOWING_MONDAY);
                        }
                    }
                }
                if(!empty($row[5]))
                {

                    $checkFifthYearDateExist = HolidayList::select('holiday_lists.id')->where('holiday_year', $this->fifth_year)->where('holiday_name', ucfirst($row[0]))->get()->toArray();;
                    if (empty($checkFifthYearDateExist)) {
                        $holidayList = new HolidayList();
                        $holidayList->holiday_name = ucfirst($row[0]);
                        $holidayList->holiday_year = $this->fifth_year;
                        $holidayList->holiday_date = date('Y-m-d', strtotime(str_replace("*", "", $row[5] . " " . $this->fifth_year)));
                        $holidayList->added_by =  Auth::user()->user_id;
                        $holidayList->save();
                        Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - ".ucfirst($row[0]) ." is inserted in year ".$this->fifth_year);
                        if ((date('l', strtotime(str_replace("*", "", $row[5] . " " . $this->fifth_year)))) == "Sunday") {
                            $date = new DateTime(date('Y-m-d', strtotime(str_replace("*", "", $row[5] . " " . $this->fifth_year))));
                            $date->modify('+1 day');
                            $nextDay = $date->format('Y-m-d');
                            $holidayList = new HolidayList();
                            $holidayList->holiday_name = ucfirst($row[0]);
                            $holidayList->holiday_year = $this->fifth_year;
                            $holidayList->holiday_date = $nextDay;
                            $holidayList->added_by =  Auth::user()->user_id;
                            $holidayList->reason = FOR_HOLIDAYS_FALLING_ON_SUNDAY_FEDERAL_RESERVE_BANKS_AND_BRANCHES_WILL_BE_CLOSED_THE_FOLLOWING_MONDAY;
                            $holidayList->save();
                            Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - ".ucfirst($row[0]) ." is inserted in year ".$this->fifth_year . " due to ".FOR_HOLIDAYS_FALLING_ON_SUNDAY_FEDERAL_RESERVE_BANKS_AND_BRANCHES_WILL_BE_CLOSED_THE_FOLLOWING_MONDAY);
                        }
                    } else {
                        $checkFifthYearDateExist = array_column($checkFifthYearDateExist,'id');
                        HolidayList::whereIn('id', $checkFifthYearDateExist)->delete();
                        $holidayList = new HolidayList();
                        $holidayList->holiday_name = ucfirst($row[0]);
                        $holidayList->holiday_year = $this->fifth_year;
                        $holidayList->holiday_date = date('Y-m-d', strtotime(str_replace("*", "", $row[5] . " " . $this->fifth_year)));
                        $holidayList->added_by =  Auth::user()->user_id;
                        $holidayList->save();
                        Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - ".ucfirst($row[0]) ." is update in year ".$this->fifth_year);
    
                        if ((date('l', strtotime(str_replace("*", "", $row[5] . " " . $this->fifth_year)))) == "Sunday") {
                            $date = new DateTime(date('Y-m-d', strtotime(str_replace("*", "", $row[5] . " " . $this->fifth_year))));
                            $date->modify('+1 day');
                            $nextDay = $date->format('Y-m-d');
                            $holidayList = new HolidayList();
                            $holidayList->holiday_name = ucfirst($row[0]);
                            $holidayList->holiday_year = $this->fifth_year;
                            $holidayList->holiday_date = $nextDay;
                            $holidayList->added_by =  Auth::user()->user_id;
                            $holidayList->reason = FOR_HOLIDAYS_FALLING_ON_SUNDAY_FEDERAL_RESERVE_BANKS_AND_BRANCHES_WILL_BE_CLOSED_THE_FOLLOWING_MONDAY;
                            $holidayList->save();
                            Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - ".ucfirst($row[0]) ." is inserted in year ".$this->fifth_year . " due to ".FOR_HOLIDAYS_FALLING_ON_SUNDAY_FEDERAL_RESERVE_BANKS_AND_BRANCHES_WILL_BE_CLOSED_THE_FOLLOWING_MONDAY);
                        }
                    }
                }
               

                DB::commit();
            } catch (\Exception $e) {

                Log::channel('datamigration')->error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occurred during insertion for  holiday: " . ucfirst($row[0]) . ".", [EXCEPTION => $e]);
                DB::rollback();
            }
        }


        $this->inc++;
    }
}
