<template>
    <div>
        <div class="row">
            <div class="col-12 mb-2 d-flex">
                <div class="input-group">
                    <div class="custom-file">
                    <input
                        type="file"
                        ref="image"
                        name="image"
                        id="image"
                        v-on:change="handleFileUpload()"
                        class="custom-file-input"
                        accept="image/*"
                    />
                    <label for="image" class="custom-file-label">{{fileLabel ? fileLabel : value}}</label>
                    </div>
                </div>
                <button @click="clearFile" type="button" v-if="fileData && clearable" class="clear-file-btn">x</button>
            </div>
            <div class="col-12" v-if="showDefaultImage">
                <button class="defaultImageSelector w-100" type="button" @click="openModal('defaultImageSelectorModal')">Select from Gallery</button>
            </div>
        </div>
        <small v-if="imageDimension">
            <strong>Image size should {{imageDimension}}</strong>
        </small>
        <b-modal ref="defaultImageSelectorModal" hide-footer hide-header id="defaultImageSelectorModal">
            <DefaultImageSelector
            :modal-obj="modal"
            v-model="defaultImage"
            :default-images="defaultImages"
            />
        </b-modal>
    </div>
</template>

<script>
import DefaultImageSelector from "./DefaultImageSelector.vue";

export default {
    props: {
        value: "",
        label:{
            type: String,
            default: "Upload image"
        },
        defaultImages: [],
        showDefaultImage:{
            type: Boolean,
            default: true
        },
        imageSize:{
            type: Number,
            default: 20
        },
        imageDimension:{
            type: String,
            default: "80x200"
        },
        clearable:{
            type: Boolean,
            default: false
        },
    },
    components:{
        DefaultImageSelector
    },
    data(){
        return{
            fileLabel: "",
            fileData: "",
            modal: "",
            defaultImage: ''
        }
    },
    mounted(){
        var self = this;
        if(self.value && self.value.name){
            self.fileLabel = self.value.name
        }else{
            self.fileLabel = self.value
            self.defaultImage = self.value
        }
        self.fileData = self.value

        self.modal = this.$refs["defaultImageSelectorModal"]
    },
    methods:{
        handleFileUpload() {
            let self = this;

            //Get reference of FileUpload.
            var fileUpload = self.$refs.image;
            
            //Check whether the file is valid Image.
            var regex = new RegExp("([a-zA-Z0-9\s_\\.\-:])+(.jpg|.jpeg|.png|.svg)$");
            if (regex.test(fileUpload.value.toLowerCase())) {

                //Check whether HTML5 is supported.
                if (typeof (fileUpload.files) != "undefined") {
                    //Initiate the FileReader object.
                    var reader = new FileReader();
                    //Read the contents of Image File.
                    reader.readAsDataURL(fileUpload.files[0]);
                    reader.onload = function (e) {
                        //Initiate the JavaScript Image object.
                        var image = new Image();

                        //Set the Base64 string return from FileReader as source.
                        image.src = e.target.result;

                        var maxSizeKB = this.imageSize; // 20kb default
                        var maxSize = maxSizeKB * 1024; //File size is returned in Bytes
                                
                        //Validate the File Height and Width.
                        image.onload = function () {
                            if (self.$refs.image.files[0].size > maxSize) {
                                alert("Maximum 20 kb image can be uploaded.");
                                return false;
                            }

                            self.fileData = self.$refs.image.files[0];
                            self.fileLabel = self.$refs.image.files[0].name;
                            self.$emit("input", self.fileData);
                            return true;
                        };

                    }
                } else {
                    alert("This browser does not support HTML5.");
                    return false;
                }
            } else {
                alert("Please select a valid (jpg, jpeg, png, svg) Image file.");
                return false;
            }
            
        },
        clearFile(e) {
            var self = this;
            self.fileData = ""
            self.fileLabel = self.label
            self.$refs.image.value = "";
            this.$emit("input", "");
            e.preventDefault();
        },
        openModal(modal){
            this.$refs[modal].show()
        },
        hideModal(modal){
            this.$refs[modal].hide()
        }
    },
    watch: {
        defaultImage: function(){
            this.$emit("input", this.defaultImage);
            this.fileLabel = this.defaultImage;
        }
    }
}
</script>

<style scoped>
.preview-image-card{
    position: relative;
}
.preview-image-clear-btn{
    position: absolute;
    right: 10px;
    top: 10px;
    border-radius: 100%;
    border: 1px solid #b7b7b7;
    color: #3a3a3a;
}
.custom-file-label{
    margin: 0!important;
}
.defaultImageSelector{
    border: 0;
    font-size: 14px;
    background: #149240;
    color: white;
    padding: 10px 15px;
    border-radius: 3px;
}
.clear-file-btn{
    width: 40px;
    height: 100%;
    border: 1px solid #e3e3e3;
    border-radius: 3px;
}
</style>