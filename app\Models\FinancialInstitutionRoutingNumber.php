<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class FinancialInstitutionRoutingNumber extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'financial_institution_id',
        'routing_no',
        'new_routing_no',
        'deleted_at'
    ];
    public $timestamps = true;
    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
}
