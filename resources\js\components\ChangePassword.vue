<template>
  <div class="content-wrapper" style="min-height: 36px;">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <section class="content">
      <div class="container-fluid">
        <div class="row">
          <div class="col-md-12">
            <div class="card card-success">
              <div class="card-header">
                <h3 class="card-title">Change Password</h3>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-md-12">
                    <div class="form-group">
                      <label>
                        Enter Current Password
                        <span class="red">*</span>
                      </label>
                      <input
                        type="password"
                        v-model="passwords.old_password"
                        placeholder="Enter Your Current Password"
                        class="form-control"
                      />
                    </div>
                  </div>
                </div>
                <div class="row">
                  <div class="col-md-6">
                    <div class="form-group">
                      <label>
                        New Password
                        <span class="red">*</span>
                      </label>
                      <input
                        type="password"
                        v-model="passwords.new_password"
                        placeholder="Enter New Password"
                        class="form-control"
                        autocomplete="off"
                      />
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-group">
                      <label>
                        Confirm New Password
                        <span class="red">*</span>
                      </label>
                      <input
                        type="password"
                        placeholder="Re Enter New Password"
                        v-model="passwords.confirm_password"
                        class="form-control"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div class="card-footer">
                <button type="button" @click="changePassword" class="btn btn-success">Save</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>
<script>
import api from "@/api/user.js";
export default {
  data() {
    return {
      passwords: {
        old_password: "",
        new_password: "",
        confirm_password: ""
      }
    };
  },
  methods: {
    checkValidation() {
      let self = this;
      if (
        self.passwords.old_password == "" ||
        self.passwords.new_password == "" ||
        self.passwords.confirm_password == ""
      ) {
        error("Please fill in the required fields.");
        return false;
      }
      if (self.passwords.new_password != self.passwords.confirm_password) {
        error("Passwords does not match.");
        return false;
      }
      return true;
    },
    changePassword() {
      let self = this;
      //update profile
      if (self.checkValidation()) {
        api
          .changePassword(self.passwords)
          .then(response => {
            if (response.code == 200) {
              success(response.message);
              self.passwords = {
                old_password: "",
                new_password: "",
                confirm_password: ""
              };
            } else {
              error(response.message);
            }
          })
          .catch(err => {
            error(err);
          });
      }
    }
  }
};
</script>
