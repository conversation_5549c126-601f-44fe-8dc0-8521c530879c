<?php

namespace App\Models;

use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON>mon\JWTAuth\Contracts\JWTSubject;


class User extends Authenticatable implements JWTSubject
{

    use Notifiable;

    /**
     * __construct
     * This function will insert an alphanumeric 32 character unique id as primary key in the table whenever a data get inserted
     * @param  mixed $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        $this->user_id = generateUUID();

        parent::__construct($attributes);
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'first_name',
        'middle_name',
        'last_name',
        'email',
        'username',
        'password',
        'phone',
        'date_of_birth',
        'street_address',
        'apt_number',
        'city',
        'state',
        'zipcode',
        'contact_person',
        'driving_license',
        'state_issued',
        'ssn_number',
        'status',
        'pin',
        'added_by',
        'excess_purchase_amount_privilage',
        'excess_purchase_amount',
        'purchase_power',
        'role_id',
        'active_allow_transaction',
        'active_allow_transaction_time',
        'required_upload_document',
        'consumer_type',
        'default_cp',
    ];
    public $timestamps = true;
    protected $primaryKey = 'user_id';
    public $incrementing = false;
    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
    protected $hidden = [
        'password',
    ];
    // Rest omitted for brevity

    /**
     * Get the identifier that will be stored in the subject claim of the JWT.
     *
     * @return mixed
     */
    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    /**
     * Return a key value array, containing any custom claims to be added to the JWT.
     *
     * @return array
     */
    public function getJWTCustomClaims()
    {
        return [];
    }
}
