<template>
  <div class="content-wrapper" style="min-height: 36px;">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <section class="content">
      <div class="container-fluid">
        <div class="row">
          <div class="col-md-12">
            <div class="card card-success">
              <div class="card-header">
                <h3 class="card-title">Scheduled Jobs (Beta)</h3>
              </div>
              <div class="card-body">
                <div class="row">
                  <div class="col-md-10">
                    <div class="form-group">
                      <label for="first_name" data-toggle="tooltip" data-placement="top" title="Enable/Disable Acheck21 Consumer Debit Process that posts consumer transaction">
                        Acheck21 Consumer Debit Process
                      </label>
                    </div>
                  </div>
                  <div class="col-md-2">
                    <div class="form-group">
                    <label class="switch"><input class="enable-employee-login" type="checkbox" v-model="cronSettings.enable_acheck21_consumer_debit_process" true-value="1" false-value="0"><span class="slider round"></span></label>
                    </div>
                  </div>
                </div>
                <div class="row">
                  <div class="col-md-10">
                    <div class="form-group">
                      <label for="first_name" data-toggle="tooltip" data-placement="top" title="Enable/Disable Acheck21 return representmets">
                        Acheck21 Return Representment Process
                      </label>
                    </div>
                  </div>
                  <div class="col-md-2">
                    <div class="form-group">
                    <label class="switch"><input class="enable-employee-login" type="checkbox" v-model="cronSettings.enable_acheck21_return_representment_process" true-value="1" false-value="0"><span class="slider round"></span></label>
                  </div>
                  </div>
                  </div>
                  <div class="row">
                  <div class="col-md-10">
                    <div class="form-group">
                      <label for="first_name" data-toggle="tooltip" data-placement="top" title="Enable/Disable transaction cooldown process. By enabling this a consumer cannot make second transaction in last 10 (configurable) minutes.">
                        Transaction Cooldown
                      </label>
                    </div>
                  </div>
                  <div class="col-md-2">
                    <div class="form-group">
                    <label class="switch"><input class="enable-employee-login" type="checkbox" v-model="cronSettings.transaction_cooldown" true-value="1" false-value="0"><span class="slider round"></span></label>
                    </div>
                  </div>
                </div>
                  <div class="row">
                  <div class="col-md-10">
                    <div class="form-group">
                      <label for="first_name" data-toggle="tooltip" data-placement="top" title="Enable/Disable Schedule Balance Fetch that updates each direct linked consumers balance">
                        Schedule Balance Fetch
                      </label>
                    </div>
                  </div>
                  <div class="col-md-2">
                    <div class="form-group">
                    <label class="switch"><input class="enable-employee-login" type="checkbox" v-model="cronSettings.enable_finicity_bank_balance_update" true-value="1" false-value="0"><span class="slider round"></span></label>
                    </div>
                    </div>
                  </div>
                <div class="row">
                  <div class="col-md-10">
                    <div class="form-group">
                      <label for="first_name" data-toggle="tooltip" data-placement="top" title="Enable/Disable Complete Reward Wheel Module from Consumer Panel">
                        Reward Wheel - Complete Shutdown
                      </label>
                    </div>
                  </div>
                  <div class="col-md-2">
                    <div class="form-group">
                    <label class="switch"><input class="enable-employee-login" type="checkbox" v-model="cronSettings.reward_wheel_complete_shutdown" true-value="1" false-value="0"><span class="slider round"></span></label>
                    </div>
                  </div>
                </div>
                <div class="row">
                  <div class="col-md-10">
                    <div class="form-group">
                      <label for="first_name" data-toggle="tooltip" data-placement="top" title="Enable/Disable Reward Wheel Spin in Consumer Panel without effecting Transaction with/without Reward points">
                        Reward Wheel - Spin Shutdown
                      </label>
                    </div>
                  </div>
                  <div class="col-md-2">
                    <div class="form-group">
                    <label class="switch"><input class="enable-employee-login" type="checkbox" v-model="cronSettings.reward_wheel_spin_shutdown" true-value="1" false-value="0"><span class="slider round"></span></label>
                    </div>
                  </div>
                </div>
                <div class="row">
                  <div class="col-md-10">
                    <div class="form-group">
                      <label for="first_name" data-toggle="tooltip" data-placement="top" title="Enable/Disable Reward Wheel Invitation for Consumers">
                        Reward Wheel - Invitation Allowed
                      </label>
                    </div>
                  </div>
                  <div class="col-md-2">
                    <div class="form-group">
                    <label class="switch"><input class="enable-employee-login" type="checkbox" v-model="cronSettings.reward_wheel_invitation_allowed" true-value="1" false-value="0"><span class="slider round"></span></label>
                    </div>
                  </div>
                </div>
                <div class="row">
                  <div class="col-md-10">
                    <div class="form-group">
                      <label for="first_name" data-toggle="tooltip" data-placement="top" title="Enable/Disable New ACH Process For All Merchants">
                        New ACH Process For All Merchants
                      </label>
                    </div>
                  </div>
                  <div class="col-md-2">
                    <div class="form-group">
                    <label class="switch"><input class="enable-employee-login" type="checkbox" v-model="cronSettings.enable_new_ach_process_for_all_merchant" true-value="1" false-value="0"><span class="slider round"></span></label>
                    </div>
                  </div>
                </div>
                <div class="row">
                  <div class="col-md-10">
                    <div class="form-group">
                      <label for="first_name" data-toggle="tooltip" data-placement="top" title="Enable/Disable Algo Based Purchase Power">
                        Enable Algo Based Purchase Power
                      </label>
                    </div>
                  </div>
                  <div class="col-md-2">
                    <div class="form-group">
                    <label class="switch"><input class="enable-employee-login" type="checkbox" v-model="cronSettings.enable_purchase_power_algo" true-value="1" false-value="0"><span class="slider round"></span></label>
                    </div>
                  </div>
                </div>
                <!-- <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                      <label for="first_name">
                        Enable Schedule Check Returns
                      </label>
                    </div>
                  </div>
                  <div class="col-md-2">
                    <div class="form-group">
                    <label class="switch"><input class="enable-employee-login" type="checkbox" v-model="schedule_check_returns"><span class="slider round"></span></label>
                    </div>
                  </div>
                  </div>
                <div class="row">
                  <div class="col-md-4">
                    <div class="form-group">
                      <label for="first_name">
                        Enable Represent Transaction
                      </label>
                    </div>
                  </div>
                  <div class="col-md-2">
                    <div class="form-group">
                    <label class="switch"><input class="enable-employee-login" type="checkbox" v-model="represent_transaction"><span class="slider round"></span></label>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <label for="first_name">
                        Enable Represent Manual Bank Transaction
                      </label>
                    </div>
                  </div>
                  <div class="col-md-2">
                    <div class="form-group">
                    <label class="switch"><input class="enable-employee-login" type="checkbox" v-model="represent_manual_bank_transaction"><span class="slider round"></span></label>
                    </div>
                  </div>
                </div>
                <div class="row">
                  <div class="col-md-4">
                    <div class="form-group">
                      <label for="first_name">
                        Enable Update Transaction
                      </label>
                    </div>
                  </div>
                  <div class="col-md-2">
                    <div class="form-group">
                    <label class="switch"><input class="enable-employee-login" type="checkbox" v-model="update_transaction"><span class="slider round"></span></label>
                    </div>
                  </div><div class="col-md-4">
                    <div class="form-group">
                      <label for="first_name">
                        Enable Post Transaction
                      </label>
                    </div>
                  </div>
                  <div class="col-md-2">
                    <div class="form-group">
                    <label class="switch"><input class="enable-employee-login" type="checkbox" v-model="post_transaction"><span class="slider round"></span></label>
                    </div>
                  </div>
                </div>
                <div class="row">
                  <div class="col-md-4">
                    <div class="form-group">
                      <label for="first_name">
                        Enable Post Void Transaction
                      </label>
                    </div>
                  </div>
                  <div class="col-md-2">
                    <div class="form-group">
                    <label class="switch"><input class="enable-employee-login" type="checkbox" v-model="post_void_transaction"><span class="slider round"></span></label>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <label for="first_name">
                        Enable Update Posting Block Flag
                      </label>
                    </div>
                  </div>
                  <div class="col-md-2">
                    <div class="form-group">
                    <label class="switch"><input class="enable-employee-login" type="checkbox" v-model="update_posting_block_flag"><span class="slider round"></span></label>
                    </div>
                  </div>
                </div>
                <div class="row">
                  <div class="col-md-4">
                    <div class="form-group">
                      <label for="first_name">
                        Enable Update Purchase Power
                      </label>
                    </div>
                  </div>
                  <div class="col-md-2">
                    <div class="form-group">
                    <label class="switch"><input class="enable-employee-login" type="checkbox" v-model="update_purchase_power"><span class="slider round"></span></label>
                    </div>
                  </div>
                </div> -->
              </div>
              <div class="card-footer">
                <button type="button" @click="updateGlobalSettings" class="btn btn-success">Save</button>
              </div>

            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>
<script>
import api from "@/api/user.js";
import { validationMixin } from "vuelidate";
import { required, minLength } from "vuelidate/lib/validators";
export default {
  mixins: [validationMixin],
  data() {
    return {
      cronSettings:{
        enable_acheck21_consumer_debit_process:0,
        enable_acheck21_return_representment_process:0,
        enable_finicity_bank_balance_update:0,
        schedule_check_returns:0,
        represent_transaction:0,
        represent_manual_bank_transaction:0,
        update_transaction:0,
        post_transaction:0,
        post_void_transaction:0,
        update_posting_block_flag:0,
        update_purchase_power:0,
        reward_wheel_complete_shutdown:0,
        reward_wheel_spin_shutdown:0,
        reward_wheel_invitation_allowed:0,
        transaction_cooldown:0,
        enable_new_ach_process_for_all_merchant:0,
        enable_purchase_power_lower_limit:0,
        enable_purchase_power_algo:0
      },
      userDetails: JSON.parse(localStorage.getItem("user"))
    };
  },
  methods: {
    updateGlobalSettings() {
      let self = this;
      var r = confirm(
        "Do you want to save the changes?"
      );
      if (r == true) {
          //update cron jobs
          api
            .updateGlobalSettings(self.cronSettings)
            .then(response => {
              if (response.code == 200) {
                success(response.message);
              } else {
                error(response.message);
              }
            })
            .catch(err => {
              error(err.response.data.message);
            });
      }
    },
    getAllSettings() {
      let self = this;
      api
        .getAllSettings()
        .then(response => {
          if (response.code == 200) {
            self.cronSettings.enable_acheck21_consumer_debit_process = parseInt(response.data.find(item => item.name === "enable_acheck21_consumer_debit_process").val);
            self.cronSettings.enable_finicity_bank_balance_update = parseInt(response.data.find(item => item.name === "enable_finicity_bank_balance_update").val);
            self.cronSettings.reward_wheel_complete_shutdown = parseInt(response.data.find(item => item.name === "reward_wheel_complete_shutdown").val);
            self.cronSettings.reward_wheel_spin_shutdown = parseInt(response.data.find(item => item.name === "reward_wheel_spin_shutdown").val);
            self.cronSettings.reward_wheel_invitation_allowed = parseInt(response.data.find(item => item.name === "reward_wheel_invitation_allowed").val);
            self.cronSettings.enable_acheck21_return_representment_process = parseInt(response.data.find(item => item.name === "enable_acheck21_return_representment_process").val);
            self.cronSettings.transaction_cooldown = parseInt(response.data.find(item => item.name === "transaction_cooldown").val);
            self.cronSettings.enable_new_ach_process_for_all_merchant = parseInt(response.data.find(item => item.name === "enable_new_ach_process_for_all_merchant").val);
            self.cronSettings.enable_purchase_power_lower_limit = parseInt(response.data.find(item => item.name === "enable_purchase_power_lower_limit").val);
            self.cronSettings.enable_purchase_power_algo = parseInt(response.data.find(item => item.name === "enable_purchase_power_algo").val);
          }else{
            error(response.message);
          }
        })
        .catch(err => {
          error(err.response.data.message);
        });
    }
  },
  mounted() {
    var self = this;
    self.getAllSettings();
    document.title = "CanPay - Global Settings";
     $('[data-toggle="tooltip"]').tooltip();
  }
};
</script>
