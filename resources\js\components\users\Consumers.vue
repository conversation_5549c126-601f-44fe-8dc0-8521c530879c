<template>
  <div>
  <div v-if="loading">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Consumers</h3>
                </div>
                <!-- /.card-header -->
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <input
                                    class="form-control"
                                    placeholder="First Name (min 3 chars)"
                                    id="consumer"
                                    v-model="consumer_first_name"
                                />
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <input
                                    class="form-control"
                                    placeholder="Middle Name (min 3 chars)"
                                    id="consumer"
                                    v-model="consumer_middle_name"
                                />
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <input
                                    class="form-control"
                                    placeholder="Last Name (min 3 chars)"
                                    id="consumer"
                                    v-model="consumer_last_name"
                                />
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                            <input
                                class="form-control"
                                placeholder="Phone No (Exact)"
                                id="phone_no"
                                v-model="phone_no"
                            />
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                            <input
                                class="form-control"
                                placeholder="Email (Exact)"
                                id="email"
                                v-model="email"
                            />
                            </div>
                        </div>
                    </div>
                </div>
                  <div class="card-footer">
                    <button
                      type="button"
                      class="btn btn-success"
                      @click="searchRegisteredConsumer()"
                    >
                      Search
                    </button>
                    <button
                      type="button"
                      @click="reset()"
                      class="btn btn-success margin-left-5"
                    >
                      Reset
                    </button>
                  </div>
                  <div class="card-body">
                  <b-table-simple
                      responsive
                      show-empty
                      bordered
                      sticky-header="800px"
                      v-if="allUserModel.length > 0"
                    >
                      <b-thead head-variant="light">
                        <b-tr>
                          <b-th class="text-left">Name</b-th>
                          <b-th class="text-left">Email</b-th>
                          <b-th class="text-left">Phone</b-th>
                          <b-th class="text-center">Date Of Birth</b-th>
                          <b-th class="text-center">Bank Link Type</b-th>
                          <b-th class="text-center">Banking Solution</b-th>
                          <b-th class="text-center">Active Returns(Unique Count)</b-th>
                          <b-th class="text-center">Total Return</b-th>
                          <b-th class="text-center">Consumer Type</b-th>
                          <b-th class="text-center">Status</b-th>
                          <b-th class="text-center">Last Balance Fetch Date</b-th>
                          <b-th class="text-center">Lockout</b-th>
                          <b-th class="text-center">Action</b-th>
                        </b-tr>
                      </b-thead>
                      <b-tbody v-for="(row, index) in allUserModel" :key="index">
                        <b-tr>
                          <b-td class="text-left text-gray">{{
                            row.name
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.email
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.phone
                          }}</b-td>
                          <b-td class="text-center text-gray">{{
                            row.date_of_birth
                          }}</b-td>
                          <b-td class="text-center text-gray">{{
                            row.bank_link_type
                          }}</b-td>
                          <b-td class="text-center text-gray text-capitalize">{{
                            row.banking_solution_name
                          }}</b-td>
                          <b-td class="text-center text-gray">{{
                            row.active_return_count
                          }}</b-td>
                          <b-td v-if="row.all_time_return_count != 0" class="text-center text-gray">
                             <a :data-user-id="row.edit" style="color: white !important" class="viewReturnTransaction btn btn-success" title="View return transaction" variant="outline-success"> {{row.all_time_return_count}}</a>
                          </b-td>
                            <b-td v-else class="text-center text-gray">
                          {{
                            row.all_time_return_count
                          }}
                          </b-td>
                          <b-td class="text-center text-gray">{{
                            capitalizeFirstLetter(row.consumer_type)
                          }}</b-td>
                          <b-td class="text-center text-gray">{{
                            row.status_name
                          }}</b-td>
                          <b-td class="text-center text-gray">{{
                            row.last_balance_fetch_date
                          }}</b-td>
                          <b-td class="text-center text-gray">
                            <!-- {{row.lockout}} -->
                            <button @click="loginUnlock(row)" v-if="row.lockout == 1" :data-user-id="row.edit" class="lockoutBtn custom-edit-btn bg-transparent" title="Unlock" variant="outline-success" style="border:none">Unlock</button>
                            <p v-else>-</p>
                          </b-td>
                          <b-td class="text-center text-gray">
                            <a :data-user-id="row.edit" :data-mx-consumer-id="row.mx_consumer_id" class="fetchMXBankData custom-edit-btn" title="Fetch the latest account from MX" variant="outline-success" style="border:none" v-if="row.mx_consumer_id && row.consumer_type != constants.lite_consumer"><i class="nav-icon fa fa-cog"></i></a>
                            <a :data-user-id="row.edit" class="viewConsumerDetails custom-edit-btn" title="View Consumer Details" variant="outline-success" style="border:none"><i class="nav-icon fas fa-eye"></i></a>
                            <a :data-user-id="row.edit" class="editconsumer custom-edit-btn" title="Edit Consumer Details" variant="outline-success" style="border:none"><i class="nav-icon fas fa-edit"></i></a>
                            <a :data-user-id="row.edit" class="delinkBanks custom-edit-btn" title="Delink Consumer Bank Accounts" variant="outline-success" style="border:none" v-if="row.consumer_type != constants.lite_consumer"><i class="nav-icon fas fa-university"></i></a>
                            <a :data-user-id="row.edit" class="custom-edit-btn" v-bind:class = "row.active_account_exists ? 'delinkInstitution': 'iconDisabled'" title="Delete Financial Institution" variant="outline-success" style="border:none" v-if="row.bank_link_type == 'Direct Link' && row.banking_solution_name == 'Finicity' && row.consumer_type != constants.lite_consumer"><i class="nav-icon fas fa-unlink"></i></a>
                            <a :data-user-id="row.edit" class="commentconsumer custom-edit-btn" title="Comment About Customer" variant="outline-success" style="border:none"><i class="nav-icon fas fa-clipboard"></i></a>
                            <a :data-user-id="row.edit" class="custom-edit-btn" v-bind:class = "row.active_account_exists ? 'fetchConsumerAccountBalance': 'iconDisabled'" title="Fetch Consumer Account Balance" variant="outline-success" style="border:none" v-if="row.bank_link_type == 'Direct Link' && row.banking_solution_name == 'Finicity' && row.consumer_type != constants.lite_consumer"><i class="nav-icon fas fa-redo"></i></a>
                            <a :data-user-id="row.edit" :banking-solution="row.banking_solution_name" :data-user-name="row.name" class="custom-edit-btn" :class="row.mx_user_action_needed == 1 ? '' : (row.active_account_exists ? 'refreshConsumerAccountBalance' : 'iconDisabled')"  title="Refresh Consumer Account Balance" variant="outline-success" style="border:none" v-if="row.bank_link_type == 'Direct Link' && row.refresh_count == 0 && row.consumer_type != constants.lite_consumer" @click="handleClick(row)"><i class="nav-icon fas fa-retweet"></i></a>
                            <a @click="bypassMicrobiltError(row.edit)" class="bypassMicrobiltError custom-edit-btn" title="Bypass Microbilt Error" variant="outline-success" style="border:none" v-if="row.consumer_type != constants.lite_consumer"><i class="nav-icon fas fa-unlock"></i></a>
                            <a v-if="row.show_purchase_power_change_option && row.consumer_type != constants.lite_consumer" :data-user-id="row.edit" :data-user-name="row.name" :data-user-rule="row.purchase_power_source" class="changerule custom-edit-btn" title="Change Purchase Power Rule" variant="outline-success" style="border:none"><i class="nav-icon fas fa-sync-alt"></i></a>
                          </b-td>
                        </b-tr>
                      </b-tbody>
                    </b-table-simple>
                    <p v-else>No data displayed. Please refine your search criteria.</p>
                    </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>

    <!-- Consumer Modal Details Start -->
    <b-modal
      id="consumer-modal"
      ref="modal"
      :header-text-variant="headerTextVariant"
      title="Change Consumer Details"
      @hidden="resetModal"
      ok-title="Save"
      ok-variant="success"
      cancel-variant="outline-secondary"
      @ok="handleOk"
      :no-close-on-esc="true"
      :no-close-on-backdrop="true"
    >
      <form ref="form" @submit.stop.prevent="save" class="needs-validation">
        <div class="row">
          <div class="col-md-4">
            <label for="user_type">
              First Name
              <span class="red">*</span>
            </label>
            <input
              class="form-control"
              placeholder="First Name"
              id="first_name"
              name="first_name"
              autocomplete="off"
              v-model="first_name"
              v-validate="'required|alpha_spaces'"
            />
            <span v-show="errors.has('first_name')" class="text-danger">{{
              errors.first("first_name")
            }}</span>
          </div>
          <div class="col-md-4">
            <label for="user_type">
              Middle Name
            </label>
            <input
              class="form-control"
              placeholder="Middle Name"
              id="middle_name"
              name="middle_name"
              autocomplete="off"
              v-model="middle_name"
              v-validate="'alpha_spaces'"
            />
            <span v-show="errors.has('middle_name')" class="text-danger">{{
              errors.first("middle_name")
            }}</span>
          </div>
          <div class="col-md-4">
            <label for="user_type">
              Last Name
              <span class="red">*</span>
            </label>
            <input
              class="form-control"
              placeholder="Last Name"
              id="last_name"
              name="last_name"
              autocomplete="off"
              v-model="last_name"
              v-validate="'required|alpha_spaces'"
            />
            <span v-show="errors.has('last_name')" class="text-danger">{{
              errors.first("last_name")
            }}</span>
          </div>
          <div class="col-md-8">
            <label for="user_type">
              Street Address
              <span class="red">*</span>
            </label>
            <input
              class="form-control"
              placeholder="Street Address"
              id="street_address"
              name="street_address"
              autocomplete="off"
              v-model="street_address"
              v-validate="'required'"
            />
            <span v-show="errors.has('street_address')" class="text-danger">{{
              errors.first("street_address")
            }}</span>
          </div>
          <div class="col-md-4">
            <label for="user_type">
              Apartment Number
            </label>
            <input
              class="form-control"
              placeholder="Apartment Number"
              id="apt_number"
              name="apt_number"
              autocomplete="off"
              v-model="apt_number"
            />
          </div>
          <div class="col-md-4">
            <label for="user_type">
              City
            </label>
            <input
              class="form-control"
              placeholder="City"
              id="city"
              name="city"
              autocomplete="off"
              v-model="city"
            />
          </div>
          <div class="col-md-4">
            <label for="user_type">
              State
            </label>
            <input
              class="form-control"
              placeholder="State"
              id="state"
              name="state"
              autocomplete="off"
              v-model="state"
            />
          </div>
          <div class="col-md-4">
            <label for="user_type">
              Zipcode
            </label>
            <input
              class="form-control"
              placeholder="Zipcode"
              id="zipcode"
              name="zipcode"
              autocomplete="off"
              v-model="zipcode"
              maxlength="5"
            />
          </div>
          <div class="col-md-12">
            <label for="user_type">
              Status
              <span class="red">*</span>
            </label>
            <select
              class="form-control"
              id="user_status"
              name="user_status"
              v-model="status"
              v-validate="'required'"
            >
              <option
                v-for="(status, index) in statusList"
                :key="index"
                v-bind:value="{ id: status.id, text: status.status }"
              >
                {{ status.status }}
              </option>
            </select>
            <span v-show="errors.has('user_status')" class="text-danger">{{
              errors.first("user_status")
            }}</span>
          </div>
          <div class="col-md-12">
            <label for="user_type"> Date of Birth </label>
            <input

              class="form-control"
              placeholder="Date of Birth (Format:- mm-dd-yyyy)"
              id="dob"
              autocomplete="off"
              v-model="date_of_birth"
              :maxlength="10"
              @keypress="isNumber($event)"
            />
          </div>
            <div class="col-md-12">
            <input type="checkbox" id="automatic_pp" name="automatic_pp" :disabled="is_automatic_purchase_power_enable == false" v-model="automatic_purchase_power" true-value="1" false-value="0" @click="getPurchasePower"> <label for="automatic_pp"> Automatic Purchase Power </label>
            </div>
            <div class="col-md-12" v-if="show_purchase_power">
            <label for="user_type">
              Daily Spending Limit
            </label>
            <input
              name="purchase_power"
              class="form-control"
              placeholder="Purchase Power"
              id="purchase_power"
              ref="purchase_power"
              autocomplete="off"
              v-model="purchase_power"
              v-validate="'decimal'"
            />
            <span v-show="errors.has('purchase_power')" class="text-danger">{{
              errors.first("purchase_power")
            }}</span>
            </div>
            <div class="col-md-12">
            <input type="checkbox" id="automatic_weekly_spending_limit" name="automatic_weekly_spending_limit" v-model="automatic_weekly_spending_limit" true-value="1" false-value="0" :disabled="current_user_consumer_type == constants.lite_consumer"> <label for="automatic_weekly_spending_limit">Disable Weekly Spending Limit  </label><i class="fa fa-info-circle" aria-hidden="true" :title="constants.disable_weekly_spending_limit_help_text"></i>
            </div>
            <div class="col-md-12" v-if="automatic_weekly_spending_limit == 0">
            <label for="user_type">
              Weekly Spending Limit
            </label>
            <input
              name="weekly_spending_limit"
              class="form-control"
              placeholder="Weekly Spending Limit"
              id="weekly_spending_limit"
              autocomplete="off"
              v-model="weekly_spending_limit"
              v-validate="'required|decimal|min_value:'+purchase_power"
              v-if="purchase_power!=''"
            />
            <input
              name="weekly_spending_limit"
              class="form-control"
              placeholder="Weekly Spending Limit"
              id="weekly_spending_limit"
              autocomplete="off"
              v-model="weekly_spending_limit"
              v-validate="'decimal|min_value:'+purchase_power"
              v-else
            />
            <span v-show="errors.has('weekly_spending_limit')" class="text-danger">{{
              errors.first("weekly_spending_limit")
            }}</span>
            </div>
            <div class="col-md-12">
            <input type="checkbox" id="active_allow_transaction" name="active_allow_transaction" v-model="active_allow_transaction" true-value="1" false-value="0" :disabled="is_active_allow_transaction == false"> <label for="active_allow_transaction">Active Allow Transaction</label>
            </div>
            <div class="col-md-12" v-if="enable_microbuilt_override_check && current_status != 'Active'">
              <input type="checkbox" id="override_microbuilt_check" name="override_microbuilt_check" v-model="override_microbuilt_check" true-value="1" false-value="0"> <label for="override_microbuilt_check">Override Microbilt Check</label>
            </div>
            <div class="col-md-12">

            <label class="switch"><input type="checkbox" id="required_upload_document" name="required_upload_document" v-model="required_upload_document" true-value="1" false-value="0" class="enable-employee-login"><span class="slider round"></span></label> <label for="required_upload_document"> Bank Statement Upload Required</label>
            </div>
        </div>
      </form>
    </b-modal>
    <!-- Consumer Modal Details End -->

    <!-- Consumer Bank Details Modal Details Start -->
    <b-modal
      id="consumer-disable-bank-modal"
      ref="consumer-disable-bank-modal"
      :header-text-variant="headerTextVariant"
      title="Delink Consumer Bank Accounts"
      @hidden="resetModal"
      ok-title="Save"
      ok-variant="success"
      cancel-variant="outline-secondary"
      @ok="handleOkBankDelink"
      :no-close-on-esc="true"
      :no-close-on-backdrop="true"
    >
      <form ref="form_bank_disable" @submit.stop.prevent="save" class="needs-validation">
        <div class="row">
            <div class="col-md-12"><label>Consumer Name : </label> <span v-html="consumer_name"></span></div>
            <div class="col-md-12"><label>Total Transactions : </label> <span v-html="total_transactions"></span></div>
            <div class="col-md-12"><label>Successful Transactions : </label> <span v-html="successful_transactions"></span></div>
            <div class="col-md-12"><label>Average Ticket ($) : </label> <span v-html="average_ticket"></span></div>
            <div class="col-md-12" v-if="active_account_exists == 0"><span class="red"><b>All accounts are in delinked state.</b></span></div>
            <div class="col-md-12">
              <div v-if="consumer_bank_link_type == 1">
                <input type="radio" v-validate="'required'" name="change_bank_link_type_name" id="keep_direct_link_account" value="1" v-model="change_bank_link_type"> <label for="keep_direct_link_account">{{consumer_bank_link_message[1]}}.</label>&nbsp;&nbsp;<br />
              </div>
              <div>
                <input type="radio" v-validate="'required'" name="change_bank_link_type_name" id="make_manual_link_account" value="2" v-model="change_bank_link_type"> <label for="make_manual_link_account">{{consumer_bank_link_message[2]}}.</label>&nbsp;&nbsp;<br />
              </div>
              <div v-if="consumer_manual_bank_accounts.length > 0">
                <input type="radio" v-validate="'required'" name="change_bank_link_type_name" id="activate_manual_link_account" value="3" v-model="change_bank_link_type"> <label for="activate_manual_link_account">{{consumer_bank_link_message[3]}}.</label>&nbsp;&nbsp;<br />
              </div>
              <div v-if="consumer_bank_link_type == 1 && consumer_direct_bank_accounts.length > 0">
                <input type="radio" v-validate="'required'" name="change_bank_link_type_name" id="convert_direct_to_manual_with_previously_linked" value="4" v-model="change_bank_link_type"> <label for="convert_direct_to_manual_with_previously_linked">{{consumer_bank_link_message[4]}}.</label>&nbsp;&nbsp;<br />
              </div>
              <span v-show="errors.has('change_bank_link_type_name')" class="text-danger">Please Choose one</span>
            </div>
            <div class="col-md-12" v-if="change_bank_link_type == 3 && consumer_manual_bank_accounts.length > 0">
              <br />
              <div class="form-group">
                <label>Select Bank Account</label>
                <br /><span v-show="errors.has('selected_account')" class="text-danger">Please Select Bank Account</span>
                <div style="max-height: 120px; overflow-y: auto;">
                  <div v-for="(row, index) in consumer_manual_bank_accounts" :key="index">
                    <input type="radio" v-validate="'required'" name="selected_account" :id="'selected_account-'+row.id" :value="row.id" v-model="selected_account_id">&nbsp;<label :for="'selected_account-'+row.id">x{{row.account_no}}<spna v-if="row.ref_no"> (Hybrid Account)</spna></label> &nbsp;&nbsp;
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-12" v-if="consumer_bank_link_type == 1 && change_bank_link_type == 4">
              <br />
              <div class="form-group">
                <label>Select Bank Account</label>
                <br /><span v-show="errors.has('selected_account')" class="text-danger">Please Select Bank Account</span>
                <div style="max-height: 120px; overflow-y: auto;">
                  <div v-for="(row, index) in all_bank_accounts" :key="index">
                    <input type="radio" v-validate="'required'" name="selected_account" :id="'selected_account-'+row.id" :value="row.id" v-model="selected_account_id">&nbsp;<label :for="'selected_account-'+row.id">x{{row.account_no}}<spna v-if="row.ref_no"> (Hybrid Account)</spna></label> &nbsp;&nbsp;
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="row" v-if="change_bank_link_type == 2 || change_bank_link_type == 3 || change_bank_link_type == 4">
            <div class="col-md-12">
                <input type="checkbox" id="automatic_pp" name="automatic_pp" v-model="use_default_spending_limit" true-value="1" false-value="0" @click="getDefaultSpendingLimit"> <label for="automatic_pp"> Use Default Spending Limit</label>
            </div>
            <div class="col-md-12">
              <label for="user_type">
                Daily Spending Limit
              </label>
              <input
                name="daily_spending_limit"
                class="form-control"
                placeholder="Daily Spending Limit"
                id="daily_spending_limit"
                ref="daily_spending_limit"
                autocomplete="off"
                v-model="daily_spending_limit"
                v-validate="'required|decimal'"
              />
              <span v-show="errors.has('daily_spending_limit')" class="text-danger">{{
                errors.first("daily_spending_limit")
              }}</span>
            </div>
            <div class="col-md-12">
              <label for="user_type">
                Weekly Spending Limit
              </label>
              <input
                name="consumer_weekly_spending_limit"
                class="form-control"
                placeholder="Weekly Spending Limit"
                id="consumer_weekly_spending_limit"
                autocomplete="off"
                v-validate="'required|decimal|min_value:'+daily_spending_limit"
                v-model="consumer_weekly_spending_limit"
                v-if="daily_spending_limit!=''"
              />
              <input
                name="consumer_weekly_spending_limit"
                class="form-control"
                placeholder="Weekly Spending Limit"
                id="consumer_weekly_spending_limit"
                autocomplete="off"
                v-validate="'decimal|min_value:'+daily_spending_limit"
                v-else
              />
              <span v-show="errors.has('consumer_weekly_spending_limit')" class="text-danger">{{
                errors.first("consumer_weekly_spending_limit")
              }}</span>
            </div>
          </div>
      </form>
    </b-modal>
    <!-- Consumer Bank Details Modal Details End -->

    <!-- View Consumer Details Modal Start -->
    <b-modal
      id="consumer-details-modal"
      ref="consumer-details-modal"
      :header-text-variant="headerTextVariant"
      title="Consumer Details"
      ok-title="Refresh"
      ok-variant="success"
      @ok="refresh"
      :cancel-disabled = "true"
      cancel-variant="outline-lite"
      cancel-title=" "
    >
      <div class="row odd-row">
        <div class="col-md-4 row-value">
          <label for="name">Address</label>
        </div>
        <div class="col-md-1 row-value"><label class="consumer-details-label">:</label></div>
        <div class="col-md-7 row-value">
          <label class="consumer-details-label">{{ consumerDetails.address }}</label>
        </div>
      </div>
      <div class="row even-row">
        <div class="col-md-4 row-value">
          <label for="access_rights">Member Since</label>
        </div>
        <div class="col-md-1 row-value"><label class="consumer-details-label">:</label></div>
        <div class="col-md-7 row-value">
          <label class="consumer-details-label">{{ consumerDetails.created_at }}</label>
        </div>
      </div>
      <div class="row odd-row" v-if="consumerDetails.account_no!=''">
        <div class="col-md-4 row-value">
          <label for="access_other_org_info">Account No.</label>
        </div>
        <div class="col-md-1 row-value"><label class="consumer-details-label">:</label></div>
        <div class="col-md-7 row-value">
          <label class="consumer-details-label">{{ consumerDetails.account_no }}</label>
        </div>
      </div>
      <div :class="consumerDetails.account_no!='' ? 'row even-row' : 'row odd-row'">
        <div class="col-md-4 row-value">
          <label for="access_other_org_info">Bank Link Type</label>
        </div>
        <div class="col-md-1 row-value"><label class="consumer-details-label">:</label></div>
        <div class="col-md-7 row-value">
          <label class="consumer-details-label" v-if="consumerDetails.consumer_type == constants.lite_consumer">N/A</label>
          <label class="consumer-details-label" v-else-if="consumerDetails.bank_link_type == 1">Direct Link</label>
          <label class="consumer-details-label" v-else>Manual Link</label>
        </div>
      </div>
      <div :class="consumerDetails.account_no!='' ? 'row odd-row' : 'row even-row'" v-if="consumerDetails.bank_name!=null">
        <div class="col-md-4 row-value">
          <label for="access_other_org_info">Bank Name</label>
        </div>
        <div class="col-md-1 row-value"><label class="consumer-details-label">:</label></div>
        <div class="col-md-7 row-value">
          <label class="consumer-details-label">{{ consumerDetails.bank_name }}</label>
        </div>
      </div>
      <div :class="consumerDetails.account_no!='' && consumerDetails.bank_name!=null ? 'row even-row' : 'row odd-row'" v-if="consumerDetails.routing_no!=null">
        <div class="col-md-4 row-value">
          <label for="access_other_org_info">Routing No.</label>
        </div>
        <div class="col-md-1 row-value"><label class="consumer-details-label">:</label></div>
        <div class="col-md-7 row-value">
          <label class="consumer-details-label">{{ consumerDetails.routing_no }}</label>
        </div>
      </div>
      <div :class="consumerDetails.account_no!='' && consumerDetails.bank_name!=null && consumerDetails.routing_no!=null ? 'row odd-row' : 'row even-row'">
        <div class="col-md-4 row-value">
          <label for="access_other_org_info">Risk Score</label>
        </div>
        <div class="col-md-1 row-value"><label class="consumer-details-label">:</label></div>
        <div class="col-md-7 row-value">
          <label class="consumer-details-label">{{ risk_score }}</label>
        </div>
      </div>
      <div :class="consumerDetails.account_no!='' && consumerDetails.bank_name!=null && consumerDetails.routing_no!=null ? 'row even-row' : 'row odd-row'">
        <div class="col-md-4 row-value">
          <label for="access_other_org_info">Calculated Purchase Power <i class="fa fa-info-circle" aria-hidden="true" :title="constants.purchase_power_help_text"></i></label>
        </div>
        <div class="col-md-1 row-value"><label class="consumer-details-label">:</label></div>
        <div class="col-md-7 row-value">
          <label class="consumer-details-label">{{ calculatePerchasePower }}
            <p v-if="consumerDetails.consumer_type == constants.lite_consumer">(Brand Points)</p>
            <p v-else-if="consumerDetails.pp_reason_msg != ''">({{ consumerDetails.pp_reason_msg }})</p>
            <p v-else-if="consumerDetails.bank_link_type == 1 && consumerDetails.pp_reason_msg == '' && consumerDetails.pp_calculated_from !=''">(PP Calculated based on {{ consumerDetails.pp_calculated_from  }})</p>
            <p v-else></p>
            <p v-if="consumerDetails.consumer_type != constants.lite_consumer">Calculation Rule = {{consumerDetails.calculated_purchase_power_source}}</p>
        </label>
        </div>
      </div>
      <div :class="consumerDetails.account_no!='' && consumerDetails.bank_name!=null && consumerDetails.routing_no!=null &&consumerDetails.is_algo_based == 1 ? 'row odd-row' : 'row even-row'">
        <div class="col-md-4 row-value">
          <label for="access_other_org_info">Effective Purchase Power <i class="fa fa-info-circle" aria-hidden="true" :title="constants.effective_purchase_power_help_text"></i></label>
        </div>
        <div class="col-md-1 row-value"><label class="consumer-details-label">:</label></div>
        <div class="col-md-7 row-value">
          <label class="consumer-details-label">{{ effectivePerchasePower }}</label>
        </div>
      </div>
      <div :class="consumerDetails.account_no!='' && consumerDetails.bank_name!=null && consumerDetails.routing_no!=null &&consumerDetails.is_algo_based == 1 ? 'row even-row' : 'row odd-row'">
        <div class="col-md-4 row-value">
          <label for="access_other_org_info">Purchase Power Rule</label>
        </div>
        <div class="col-md-1 row-value"><label class="consumer-details-label">:</label></div>
        <div class="col-md-7 row-value">
          <label class="consumer-details-label" v-if="consumerDetails.consumer_type == constants.lite_consumer">N/A</label>
          <label class="consumer-details-label" v-else>{{purchasePowerRule}}</label>
        </div>
      </div>
      <div :class="consumerDetails.account_no!='' && consumerDetails.bank_name!=null && consumerDetails.routing_no!=null && consumerDetails.is_algo_based == 1 ? 'row odd-row' : 'row even-row'" v-if="consumerDetails.is_algo_based == 1">
        <div class="col-md-4 row-value">
          <label for="access_other_org_info">Failover Purchase Power Rule</label>
        </div>
        <div class="col-md-1 row-value"><label class="consumer-details-label">:</label></div>
        <div class="col-md-7 row-value">
          <label class="consumer-details-label">{{ consumerDetails.purchase_power_source }}</label>
        </div>
      </div>
      <div :class="consumerDetails.account_no!='' && consumerDetails.bank_name!=null && consumerDetails.routing_no!=null &&consumerDetails.is_algo_based == 1 ? 'row even-row' : 'row odd-row'">
        <div class="col-md-4 row-value">
          <label for="access_other_org_info">Pending Transaction Amount Of Consumer</label>
        </div>
        <div class="col-md-1 row-value"><label class="consumer-details-label">:</label></div>
        <div class="col-md-7 row-value">
          <label class="consumer-details-label">{{totalPendingTransaction}}</label>
        </div>
      </div>
      <div :class="consumerDetails.account_no!='' && consumerDetails.bank_name!=null && consumerDetails.routing_no!=null &&consumerDetails.is_algo_based == 1 ? 'row odd-row' : 'row even-row'">
        <div class="col-md-4 row-value">
          <label for="access_other_org_info">Pending Transaction Amount Against Account</label>
        </div>
        <div class="col-md-1 row-value"><label class="consumer-details-label">:</label></div>
        <div class="col-md-7 row-value">
          <label class="consumer-details-label">{{totalPendingTransactionAgainstAccount}}</label>
        </div>
      </div>
      <div :class="consumerDetails.account_no!='' && consumerDetails.bank_name!=null && consumerDetails.routing_no!=null &&consumerDetails.is_algo_based == 1 ? 'row even-row' : 'row odd-row'"  v-if="consumerDetails.existing_user == 1 && consumerDetails.disable_automatic_purchase_power == 1">
        <div class="col-md-4 row-value">
          <label for="access_other_org_info">Standard Daily Limit  </label>
        </div>
        <div class="col-md-1 row-value"><label class="consumer-details-label">:</label></div>
        <div class="col-md-7 row-value">
          <label class="consumer-details-label">{{ consumerDetails.standard_daily_limit }}</label>
        </div>
      </div>
      <div class="card-body">
        <label v-if="latestBalanceDetails.length > 0" for="balance-fetch-details">Balance Fetch Details</label>
        <b-table-simple
          responsive
          show-empty
          bordered
          sticky-header="800px"
          v-if="latestBalanceDetails.length > 0"
        >

          <b-thead head-variant="light">
            <b-tr>
              <b-th class="text-left">Title</b-th>
              <b-th class="text-left">Balance ($)</b-th>
              <b-th class="text-left">Source</b-th>
              <b-th class="text-left">Balance Check Datetime (PST)</b-th>
            </b-tr>
          </b-thead>
          <b-tbody v-for="(row, index) in latestBalanceDetails" :key="index">
            <b-tr>
                <b-td class="text-left text-gray">{{row.title}}</b-td>
                <b-td class="text-left text-gray">{{row.balance}}</b-td>
                <b-td class="text-left text-gray">{{row.reason}}</b-td>
                <b-td class="text-left text-gray">{{row.balance_check_date}}</b-td>
            </b-tr>
          </b-tbody>
        </b-table-simple>
      </div>
</b-modal>
    <!-- View Consumer Details Modal End -->

    <!-- Consumer Comments Modal Start -->
    <b-modal
      id="consumer-comments-modal"
      ref="consumer-comments-modal"
      :header-text-variant="headerTextVariant"
      title="Consumer Comments"
      ok-title="Save"
      ok-variant="success"
      @ok="handleOkComment"
      cancel-title="Close"
      cancel-variant="outline-secondary"
      :cancel-disabled = "false"
    >
    <div class="auto-overflow" v-if="userComments.length > 0">
        <div class="col-md-12" v-for="(comments, index) in userComments" :key="index">
            <div class="card">
                <div class="card-body">
                    <label for="name">{{comments.comment}}</label>
                </div>
            </div>
            <span class="card-comment-box">{{comments.added_by_name}}&nbsp;{{comments.post_time}}</span>
        </div>
    </div>

    <form ref="form" @submit.stop.prevent="save" class="needs-validation">
        <div class="row">
            <div class="col-md-12 row-value">
                <label for="email">
                Comment
                <span class="red">*</span>
                </label>
                <textarea name="comment" id="comment" class="form-control" v-model="consumer_comment" v-validate="'required'"></textarea>
                <span v-show="errors.has('comment')" class="text-danger">{{
                    errors.first("comment")
                }}</span>
            </div>
        </div>
    </form>
    </b-modal>
    <!-- Consumer Comments Modal End -->

   <!-- return Transaction Details Modal Start -->

    <b-modal
      id="view-return-transaction-modal"
      ref="modal"
      :header-text-variant="headerTextVariant"
      title="Return Transaction Details"
      :no-close-on-esc="true"
      :no-close-on-backdrop="true"
      hide-footer
    >
      <div v-if="returnTransaction.length > 0">
          <div class="row odd-row"  >
            <div class="col-md-4 row-value">
              <label for="name">Consumer Name</label>
            </div>
            <div class="col-md-1 row-value">
              <label class="consumer-details-label">:</label>
            </div>
            <div class="col-md-7 row-value">
              <label class="consumer-details-label">{{
             userDetails.first_name + " " + userDetails.last_name
              }}</label>
            </div>
          </div>
          <div class="row even-row"  >
            <div class="col-md-4 row-value">
              <label for="name">Consumer Email</label>
            </div>
            <div class="col-md-1 row-value">
              <label class="consumer-details-label">:</label>
            </div>
            <div class="col-md-7 row-value">
              <label class="consumer-details-label">{{
             userDetails.email
              }}</label>
            </div>
          </div>
          <div class="row odd-row"  >
            <div class="col-md-4 row-value">
              <label for="name">Consumer Phone</label>
            </div>
            <div class="col-md-1 row-value">
              <label class="consumer-details-label">:</label>
            </div>
            <div class="col-md-7 row-value">
              <label class="consumer-details-label">{{
             userDetails.phone
              }}</label>
            </div>
          </div>
      <div class="row even-row">
        <div class="col-md-4 row-value">
          <label for="access_other_org_info">Bank Link Type</label>
        </div>
        <div class="col-md-1 row-value"><label class="consumer-details-label">:</label></div>
        <div class="col-md-7 row-value">
          <label class="consumer-details-label" v-if="userDetails.bank_link_type == 1">Direct Link</label>
          <label class="consumer-details-label" v-else>Manual Link</label>
        </div>
      </div>
          </div>
      <div class="card-body">
        <b-table-simple
          responsive
          show-empty
          bordered
          sticky-header="800px"
          v-if="returnTransaction.length > 0"
        >

          <b-thead head-variant="light">
            <b-tr>
              <b-th class="text-left">Transaction Number</b-th>
              <b-th class="text-left">Amount</b-th>
              <b-th class="text-left">Transaction Date</b-th>
              <b-th class="text-center">Return Date</b-th>
              <b-th class="text-center">Return Code</b-th>
              <b-th class="text-center">Representment count</b-th>
              <b-th class="text-center">Present Status </b-th>
            </b-tr>
          </b-thead>
          <b-tbody v-for="(row, index) in returnTransaction" :key="index">
            <b-tr>
              <b-td class="text-left text-gray">{{row.transaction_number}}</b-td>
              <b-td class="text-left text-gray">{{ row.amount }}</b-td>
              <b-td class="text-left text-gray">{{row.transaction_date}}</b-td>
              <b-td class="text-center text-gray">{{ row.returned_on }}</b-td>
              <b-td class="text-center text-gray">{{ row.reason_code }}</b-td>
              <b-td class="text-center text-gray">{{ row.represent_count }}</b-td>
              <b-td class="text-center text-gray">{{ row.status_name }}</b-td>
            </b-tr>
          </b-tbody>
        </b-table-simple>
        <p v-else>No data displayed. Please refine your search criteria.</p>
      </div>
    </b-modal>
  <!-- return Transaction Details Modal End -->


  <!-- Lockout Unlock Confirmation Modal  -->
    <b-modal ref="login-unlock-modal" hide-footer hide-header id="login-unlock-modal">
      <div class="color">
        <div class="col-12 text-center">
          <svg width="100" fill="#149240" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
          viewBox="796 729.37 200 200" enable-background="new 796 729.37 200 200" xml:space="preserve">
          <path d="M947.232,737.06c-26.891,0-48.769,21.876-48.769,48.769v21.623h-83.855c-10.277,0-18.609,8.332-18.609,18.609v57.775
          c0,20.9,16.943,37.844,37.843,37.844h66.979c20.9,0,37.843-16.943,37.843-37.844V826.06c0-10.277-8.331-18.609-18.608-18.609h-3.955
          v-21.623c0-17.166,13.965-31.131,31.131-31.131c17.165,0,31.131,13.965,31.131,31.131v31.413c0,4.871,3.947,8.819,8.817,8.819
          s8.819-3.949,8.819-8.819v-31.413C996,758.937,974.123,737.06,947.232,737.06z M873.933,865.427v23.546
          c0,3.836-3.109,6.942-6.942,6.942c-3.833,0-6.942-3.107-6.942-6.942v-23.546c-5.288-2.578-8.94-7.99-8.94-14.269
          c0-8.772,7.111-15.883,15.882-15.883c8.772,0,15.884,7.11,15.884,15.883C882.875,857.437,879.22,862.85,873.933,865.427z"/>
          </svg>
        </div>
        <div class="purchaserpower-modal-text">
          <div class="d-block text-center">
            <label class="text-justify text-secondary h4">
              Are you sure ?
            </label>
            <br />
            <label class="text-justify text-secondary text-dark">
              {{errorMessage}}
            </label>
          </div>
          <div class="row">
            <div class="col-12 text-center">
              <button
                @click="hideModal('login-unlock-modal')"
                class="btn btn-secondary btn-md center-block mr-2"
              >
                <label class="forgetpassword-ok-label mb-0">Cancel</label>
              </button>
              <button
                @click="unlockConsumerAndMicrobilt()"
                class="btn btn-success btn-md center-block ml-2"
              >
                <label class="forgetpassword-ok-label mb-0">Confirm</label>
              </button>
            </div>
          </div>
        </div>
      </div>
    </b-modal>

    <!-- Consumer Bank Details Modal Details Start -->
    <b-modal
    id="consumer-financial-institution"
    ref="consumer-financial-institution"
    :header-text-variant="headerTextVariant"
    title="Financial Institutions"
    :no-close-on-esc="true"
    :no-close-on-backdrop="true"
    hide-footer
  >
    <form ref="form_bank_disable" @submit.stop.prevent="save" class="needs-validation">
      <div class="row">
          <div class="col-md-12">
            <div v-if="financial_institutions.length!=0">
              <div  class="col-12" v-for="(fin, index) in financial_institutions" :key="index">
                <div class="row bank-name-list m-2">
                  <div class="col-8 align-self-center bl-f-weight text-left">
                    <span>{{ fin.bank_name }}</span>
                  </div>
                  <div class="col-4 align-self-center">
                    <div class="text-center float-right">
                      <span @click="confirmDeleteFI(fin)" title="Delete Financial Institution">
                        <i class="nav-icon fas fa-times-circle"></i>
                      </span>
                    </div>
                  </div>
                </div>
                <div class="col-12">
                  <div v-if="typeof fin.checking != 'undefined' && fin.checking.length != 0">
                    <div class="row m-2 pl-2">
                      <div class="col-4 remove-padding">
                        <span class="float-left" >Checking</span>
                      </div>
                      <div class="col-8 bl-f-weight">
                        <span class="float-left pl-2" v-for="(account, i) in fin.checking" :key="i">x{{ account.account_no }} {{ (fin.checking.length > 1 && i != (fin.checking.length - 1)) ? ',': '' }}</span>
                      </div>
                    </div>
                  </div>
                  <div v-if="(typeof fin.savings != 'undefined' && fin.savings.length != 0)">
                    <div class="row m-2 pl-2">
                      <div class="col-4 remove-padding">
                        <span class="float-left">Savings</span>
                      </div>
                      <div class="col-8 bl-f-weight">
                        <span class="float-left pl-2" v-for="(account, i) in fin.savings" :key="i">x{{ account.account_no }} {{ (fin.savings.length > 1 && i != (fin.savings.length - 1)) ? ',': '' }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div v-else>
              <span>No Financial Institutions Found.</span>
            </div>
          </div>
        </div>
    </form>
  </b-modal>
  <b-modal
      id="fin-ins-delete-confirm-modal"
      ref="fin-ins-delete-confirm-modal"
      ok-title="Confirm"
      cancel-title="Close"
      ok-variant="success"
      @ok="deleteFI"
      cancel-variant="outline-secondary"
      hide-header
      :no-close-on-esc="true"
      :no-close-on-backdrop="true"
    >
      <div class="row">
        <div class="col-12">
          <label for="comment">
            Tell us why you want to delete this Financial Institution
          </label>
          <textarea name="comment" type="text" v-model="institution_delete_comment" class="form-control" />
        </div>
      </div>
    <div class="row" style="margin-bottom: 40px;"></div>
  </b-modal>

  <b-modal
      ref="fin-ins-delete-alert-msg-modal"
      hide-footer
      hide-header
      id="fin-ins-delete-alert-msg-modal"
    >
      <div class="color">
        <div class="col-12 text-center">
          <svg
            version="1.1"
            id="Layer_1"
            xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink"
            x="0px"
            y="0px"
            width="120"
            height="120"
            viewBox="0 0 100 125"
            style="enable-background: new 0 0 100 125"
            xml:space="preserve"
            fill="#e14343"
          >
            <path
              d="M96.2,47.5l-22-38c-0.4-0.6-1-1-1.7-1h-44c-0.7,0-1.4,0.4-1.7,1l-22,37.9c-0.4,0.6-0.4,1.4,0,2l22,38.1c0.4,0.6,1,1,1.7,1
  h44c0.7,0,1.4-0.4,1.7-1l22-38C96.6,48.9,96.6,48.1,96.2,47.5z M71.3,84.5H29.7L8.8,48.4l20.8-35.9h41.7l20.8,36L71.3,84.5z
  M50.5,60.5c1.1,0,2-0.9,2-2v-30c0-1.1-0.9-2-2-2c-1.1,0-2,0.9-2,2v30C48.5,59.6,49.4,60.5,50.5,60.5z M48.4,66.4
  c-0.6,0.6-0.9,1.3-0.9,2.1c0,0.8,0.3,1.6,0.9,2.1s1.3,0.9,2.1,0.9c0.8,0,1.6-0.3,2.1-0.9c0.6-0.6,0.9-1.3,0.9-2.1
  c0-0.8-0.3-1.6-0.9-2.1C51.5,65.3,49.5,65.3,48.4,66.4z"
            />
          </svg>
        </div>
        <div class="purchaserpower-modal-text">
          <div class="d-block text-center">
            <label class="purchasepower-def-label">
              The financial institution cannot be deleted while the consumer has pending transactions.
            </label>
          </div>
          <br />
          <br />
          <div class="row">
            <div class="col-12 text-center">
              <button
                @click="hideModal('fin-ins-delete-alert-msg-modal')"
                class="btn btn-danger btn-md center-block ml-2"
              >
                <label class="forgetpassword-ok-label">OK</label>
              </button>
            </div>
          </div>
        </div>
      </div>
  </b-modal>
  <b-modal
    ref="fin-ins-delete-success-modal"
    hide-footer
    hide-header
    id="fin-ins-delete-success-modal"
  >
    <div class="color">
      <div class="col-12 text-center">

      </div>
      <div class="purchaserpower-modal-text">
        <div class="d-block text-center">
          <label class="purchasepower-def-label">
            Financial Institution deleted successfully
          </label>
        </div>
        <br />
        <br />
        <div class="row">
          <div class="col-12 text-center">
            <button
              type="button"
              class="btn btn-success btn-md center-block ml-2"
              style="height: 40px"
              @click="hideModal('fin-ins-delete-success-modal')"
            >
              <span class="purchasepower-modal-ok-label"
                >OK</span
              >
            </button>
          </div>
        </div>
      </div>
    </div>
  </b-modal>
  </div>
  </div>
</template>
<script>
import { db } from "../../firebaseConfig.js";
import moment from "moment";
import api from "@/api/user.js";
import { validationMixin } from "vuelidate";
import { required, minLength,decimal } from "vuelidate/lib/validators";
import commonConstants from "@/common/constant.js";
import { HourGlass } from "vue-loading-spinner"
import CanPayLoader from "../CustomLoader/CanPayLoader.vue"
export default {
  mixins: [validationMixin],
  data() {
    return {
      allUserModel: {},
      userId: "",
      statusList: [],
      first_name: "",
      middle_name: "",
      last_name: "",
      street_address: "",
      apt_number: "",
      city: "",
      state: "",
      zipcode: "",
      status: {},
      date_of_birth:"",
      purchase_power:"",
      weekly_spending_limit:"",
      automatic_purchase_power:"",
      existing_user:"",
      headerTextVariant: "light",
      currentUser: localStorage.getItem("user")
        ? JSON.parse(localStorage.getItem("user"))
        : null,
      consumerDetails:{},
      latestBalanceDetails:[],
      show_purchase_power:true,
      searchvalue:"",
      showReloadBtn:false,
      constants: commonConstants,
      consumer_first_name:"",
      consumer_middle_name:"",
      consumer_last_name:"",
      phone_no:"",
      email:"",
      calculatePerchasePower:null,
      effectivePerchasePower:null,
      totalPendingTransaction:null,
      purchasePowerRule:null,
      totalPendingTransactionAgainstAccount: null,
      returnTransaction: {},
      userDetails: null,
      userComments: {},
      consumer_comment: null,
      consumer_id: null,
      loading:false,
      required_upload_document:"",
      active_allow_transaction:"",
      is_active_allow_transaction: "",
      automatic_weekly_spending_limit:"",
      is_automatic_purchase_power_enable: "",
      updatedDetails:{},
      consumer_weekly_spending_limit:"",
      daily_spending_limit:"",
      change_bank_link_type:"",
      consumer_bank_link_type:"",
      selected_account_id:"",
      consumer_bank_link_message:{
        1: 'Delink all accounts and keep this consumer as direct linked',
        2: 'Delink all accounts and make this consumer as manual linked',
        3: 'Delink all accounts and activate a previous manual link account',
        4: 'Convert Direct Link to Manual Link with previously linked accounts'
      },
      consumer_manual_bank_accounts: [],
      consumer_direct_bank_accounts: [],
      all_bank_accounts: [],
      total_transactions:"",
      successful_transactions:"",
      average_ticket:"",
      consumer_name:"",
      active_account_exists:0,
      financial_institutions:{},
      institution_id:"",
      institution_login_id:"",
      institution_delete_comment :"",
      swalText: "",
      errorMessage: "",
      microbiltErrorBypass: false,
      mxConsumerID: "",
      override_microbuilt_check:0,
      current_status:"",
      enable_microbuilt_override_check:false,
      risk_score: "",
      use_default_spending_limit: false,
      default_spending_limit: '',
      default_weekly_spending_limit: '',
      users:{},
      active_qr_code_alert:false,
      current_user_consumer_type:''
    };
  },
  components: {
    HourGlass,
    CanPayLoader
  },
  created() {
    this.editconsumer();
    this.delinkBanks();
    this.viewConsumerDetails();
    this.fetchMXBankData();
    this.viewReturnTransaction();
    this.commentconsumer();
    this.fetchConsumerAccountBalance();
    this.refreshConsumerAccountBalance();
    this.delinkInstitution();
    this.changerule();
    this.email = this.$route.query.email;
  },
  methods: {
    handleClick(row) {
      var self = this;
      if (row.mx_user_action_needed == 1) {
        Vue.swal({
        text: 'Problematic status detected for this consumer. Please wait until the consumer resolves this problem before proceeding.',
        icon: 'warning',
        confirmButtonText: 'Okay'
      });
      }
    },
    loginUnlock(row){
      var self = this;
      self.consumerDetails = row
      self.microbiltErrorBypass = false;
      self.errorMessage = 'Do you want to unlock this consumer?';
      self.$bvModal.show("login-unlock-modal");
    },
    unlockConsumerAndMicrobilt(){
      var self = this;
      if (self.microbiltErrorBypass) {
        self.bypassMicrobiltErrorApiCall();
      } else {
        self.unlockConsumer();
      }
    },
    unlockConsumer(){
      var self = this;

      var request = {}
      request.user_id = self.consumerDetails.edit
      api
      .unlockConsumer(request)
      .then((response) => {
        if ((response.code = 200)) {
          success(response.message);
          self.searchRegisteredConsumer();
          self.$bvModal.hide("login-unlock-modal");
        } else {
          error(response.message);
        }
      })
      .catch((err) => {
        error(err);
      });

    },
    bypassMicrobiltErrorApiCall(){
      var self = this;
      var request = {
          'user_id':self.userId,
      };
      api.bypassMicrobiltError(request)
      .then(response => {
          if (response.code == 200) {
            success(response.message);
            self.searchRegisteredConsumer();
            self.$bvModal.hide("login-unlock-modal");
          } else {
            error(response.message);
            self.loading = false;
          }
      })
      .catch(err => {
          error(err.response.data.message);
          self.loading = false;
      });

    },
    hideModal(modalID){
      var self = this;
      self.$bvModal.hide(modalID);
    },
    resetModal() {
      var self = this;
      self.change_bank_link_type = '';
      self.status = {};
    },
    capitalizeFirstLetter(str) {
      return str.charAt(0).toUpperCase() + str.slice(1);
    },
    reset(){
      var self = this;
      self.consumer = "";
      self.phone_no = "";
      self.email = "";
      self.consumer_first_name = "";
      self.consumer_middle_name = "";
      self.consumer_last_name = "";
    },
    handleOk(bvModalEvt) {
      var self = this;
      // Prevent modal from closing
      bvModalEvt.preventDefault();
      // Trigger submit handler
      self.changeStatus();
    },
    handleOkBankDelink(bvModalEvt) {
      var self = this;
      // Prevent modal from closing
      bvModalEvt.preventDefault();
      // Trigger submit handler
      this.$validator.validateAll().then((result) => {
        if (result) {
          self.swalText = 'Once done you will not be able to undo this action!';
          if (self.change_bank_link_type == 2) {
            self.swalText = 'Direct Linking is mandatory for this Oauth enabled Routing Number in some States. This action is going to overwrite that rule and for this time consumer can manual link. Future Non-admin driven Manual links are going to be restricted again.';
            self.saveBankDelinked();
          } else if (self.change_bank_link_type == 3) {
            self.checkRestrictedRoutingNumber();
          } else {
            self.saveBankDelinked();
          }
        }
      });
    },
    checkRestrictedRoutingNumber() {
      var self = this;
      var request = {
        userID: self.userId,
        selecte_bank_account: self.selected_account_id
      };
      api
        .checkRestrictedRoutingNumber(request)
        .then((response) => {
          if ((response.code = 200) && response.data) {
            self.swalText = 'Direct Linking is mandatory for this Routing Number '+response.data['routing_no']+'. This action is going to overwrite that rule and for this time consumer can manual link. Future Non-admin driven Manual links are going to be restricted again.';
          }
          self.saveBankDelinked();
        })
        .catch((err) => {
          error(err);
        });
    },
    handleOkComment(bvModalEvt) {
      var self = this;
      // Prevent modal from closing
      bvModalEvt.preventDefault();
      // Trigger submit handler
      self.saveComment();
    },
    getPurchasePower(){
      var self = this;
      if($("#automatic_pp").prop("checked") == true){
        self.show_purchase_power = false;
        self.purchase_power = "";
      }else{
        self.show_purchase_power = true;
      }
    },
    getUserStatus() {
      var self = this;
      api
        .getUserStatus()
        .then((response) => {
          if ((response.code = 200)) {
            self.statusList = response.data;
          } else {
            error(response.message);
          }
        })
        .catch((err) => {
          error(err);
        });
    },
    editconsumer() {
      var self = this;
      $(document).on("click", ".editconsumer", function (e) {
        var userID = $(e.currentTarget).attr("data-user-id");
        self.userId = userID;
        var request = {
            'userID':userID,
        };
        api.getconsumerdetails(request)
        .then(response => {
            if (response.code == 200) {
                self.first_name = response.data.consumerDetails.first_name;
                self.middle_name = response.data.consumerDetails.middle_name;
                self.last_name = response.data.consumerDetails.last_name;
                self.street_address = response.data.consumerDetails.street_address;
                self.apt_number = response.data.consumerDetails.apt_number;
                self.city = response.data.consumerDetails.city;
                self.state = response.data.consumerDetails.state;
                self.zipcode = response.data.consumerDetails.zipcode;
                self.status.id = response.data.consumerDetails.status;
                self.enable_microbuilt_override_check = response.data.enable_microbuilt_override_check;
                self.override_microbuilt_check = response.data.checked_by_admin;
                self.status.text = (response.data.consumerDetails.status_name).replace("User ", "");
                self.current_status = (response.data.consumerDetails.status_name).replace("User ", "");
                if(response.data.consumerDetails.date_of_birth!=null && response.data.consumerDetails.date_of_birth!='0000-00-00'){
                  self.date_of_birth = moment(response.data.consumerDetails.date_of_birth).format("MM-DD-YYYY");
                }else{
                  self.date_of_birth = "";
                }
                self.purchase_power = response.data.consumerDetails.existing_user==1 && response.data.consumerDetails.standard_daily_limit!=null ? response.data.consumerDetails.standard_daily_limit : response.data.consumerDetails.purchase_power;
                self.weekly_spending_limit = response.data.consumerDetails.weekly_spending_limit;
                self.automatic_purchase_power = response.data.consumerDetails.disable_automatic_purchase_power == 0 ? 1 : 0;
                self.automatic_weekly_spending_limit = response.data.consumerDetails.disable_automatic_weekly_spending_limit == 0 ? 1 : 0;
                self.existing_user = response.data.consumerDetails.existing_user;
                self.required_upload_document = response.data.consumerDetails.required_upload_document;
                self.active_allow_transaction = response.data.consumerDetails.active_allow_transaction;
                self.current_user_consumer_type = response.data.consumerDetails.consumer_type;
                if(self.automatic_purchase_power == 1){
                  self.show_purchase_power = false;
                  self.purchase_power = "";
                }else{
                  self.show_purchase_power = true;
                }
                self.$bvModal.show("consumer-modal");
                self.is_active_allow_transaction = response.data.is_active_allow_transaction;
                self.is_automatic_purchase_power_enable = response.data.is_automatic_purchase_power_enable;
            } else {
                error(response.message);
            }
        })
        .catch(err => {
            error(err.response.data.message);
        });
      });
    },
    delinkBanks() {
      var self = this;
      $(document).on("click", ".delinkBanks", function (e) {
        var userID = $(e.currentTarget).attr("data-user-id");
        self.userId = userID;
        self.selected_account_id = '';
        self.userDetails = self.allUserModel.find(
        p => p.edit == userID
        );
        var request = {
            'userID':userID,
        };
        self.use_default_spending_limit = false;
        api.getTransactionDetailsForConsumer(request)
        .then(response => {
            if (response.code == 200) {
                self.consumer_name = self.userDetails.name;
                self.active_account_exists = self.userDetails.active_account_exists;
                self.consumer_bank_link_type = response.data['bank_link_type'];
                self.consumer_manual_bank_accounts = response.data['manual_bank_accounts'];
                self.consumer_direct_bank_accounts = response.data['direct_bank_accounts'];
                self.all_bank_accounts = response.data['all_banks'];
                self.default_daily_spending_limit = response.data['default_daily_spending_limit'];
                self.default_weekly_spending_limit = response.data['default_weekly_spending_limit'];
                self.daily_spending_limit = self.userDetails.standard_daily_limit;
                self.consumer_weekly_spending_limit = self.userDetails.weekly_spending_limit;
                self.total_transactions = response.data[0].total_transactions;
                self.successful_transactions = response.data[0].successful_transactions;
                self.average_ticket = response.data[0].average_ticket;
                // show modal
                setTimeout(function() {
                    self.$bvModal.show("consumer-disable-bank-modal");
                }, 500);
            } else {
                error(response.message);
            }
        })
        .catch(err => {
            error(err.response.data.message);
        });
        });
    },
    fetchConsumerAccountBalance(){
      var self = this;
      $(document).on("click", ".fetchConsumerAccountBalance", function (e) {
        self.loading = true;
        var userID = $(e.currentTarget).attr("data-user-id");
        self.userId = userID;
        var request = {
            'user_id':userID,
        };
        if(self.loading){
          api.fetchConsumerAccountBalance(request)
          .then(response => {
              if (response.code == 200) {
                success(response.message);
                self.searchRegisteredConsumer();
                self.loading = false;
              } else {
                error(response.message);
                self.loading = false;
              }
          })
          .catch(err => {
              error(err.response.data.message);
              self.loading = false;
          });
        }
      });
    },
    refreshConsumerAccountBalance(){
      var self = this;
      $(document).on("click", ".refreshConsumerAccountBalance", function (e) {
        var userID = $(e.currentTarget).attr("data-user-id");
        var name = $(e.currentTarget).attr("data-user-name");
        var bank_solution = $(e.currentTarget).attr("banking-solution")
        self.userId = userID;
        var request = {
            'user_id':userID,
        };
        var text_message = "Do you want to proceed with balance refresh for "+name+"?";
        Vue.swal({
            title: "Alert!!!",
            text: text_message,
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#149240",
            confirmButtonText: "Confirm, proceed with refresh balance",
            cancelButtonText: "No, don't proceed with refresh balance",
            closeOnConfirm: true,
            closeOnCancel: true,
            width: '800px'
            }).then((result) => {
                if (result.isConfirmed) {
                    let request = {
                        user_id: userID
                    }
                    self.loading = true;
                      api.refreshConsumerAccountBalance(request)
                      .then(response => {
                          if (response.code == 200) {
                              self.searchRegisteredConsumer();
                              self.loading = false;
                              Vue.swal("Done!", name+"'s Purchase Power has been calculated with the latest data.", "success");
                          } else {
                            Vue.swal("There was an issue refreshing "+name+"'s balance. Please contact the System Administrator.",'', "error");
                            self.loading = false;
                          }
                      })
                      .catch(err => {
                          Vue.swal(err.response.data.message,'', "error");
                          self.loading = false;
                      });

                }
            })
      });
    },
    bypassMicrobiltError(userID){
      var self = this;
      self.userId = userID;
      self.microbiltErrorBypass = true;
      self.errorMessage = 'Do you want to bypass microbilt error for this consumer?';
      self.$bvModal.show("login-unlock-modal");
    },
    viewConsumerDetails() {
      var self = this;
      $(document).on("click", ".viewConsumerDetails", function (e) {
        var moment = require('moment-timezone');
        var userID = $(e.currentTarget).attr("data-user-id");
        self.userID = userID;
          var request = {
              'userID':userID,
          };
          api.getconsumerdetails(request)
          .then(response => {
              if (response.code == 200) {
                  self.consumerDetails = response.data.consumerDetails;
                  self.risk_score = response.data.risk_score;
                  var latestBalanceDetails = response.data.latestBalanceDetails;
                  if(response.data.consumerDetails.account_no!=null){
                    self.consumerDetails.account_no = 'XXXX'+response.data.consumerDetails.account_no;
                  }else{
                    self.consumerDetails.account_no = '';
                  }


                  //Convert Created At date to PST time
                  var dCreated = new Date(response.data.consumerDetails.created_at);
                  var ds = moment.tz(moment(dCreated).format("YYYY-MM-DD"), 'America/Adak').isDST();
                  if(ds){
                    var targetTimeOffset  = -7*60; //desired time zone, taken as GMT-7 (Pacific Time) Daylight Saving On
                  }else{
                    var targetTimeOffset  = -8*60; //desired time zone, taken as GMT-8 (Pacific Time) Daylight Saving Off
                  }
                  dCreated.setMinutes(dCreated.getMinutes() + dCreated.getTimezoneOffset() + targetTimeOffset );
                  self.consumerDetails.created_at = moment(dCreated).format("MM/DD/YYYY HH:mm:ss");
                  var BalDetails = [];
                  latestBalanceDetails.forEach(function(details, key) {
                    //Convert Last balance checked date to PST time
                    if(details.balance_check_date!=null){
                        var dBalChk = new Date(details.balance_check_date);
                        var ds1 = moment.tz(moment(dBalChk).format("YYYY-MM-DD"), 'America/Adak').isDST();
                        if(ds1){
                        var targetTimeOffset1  = -7*60; //desired time zone, taken as GMT-7 (Pacific Time) Daylight Saving On
                        }else{
                        var targetTimeOffset1  = -8*60; //desired time zone, taken as GMT-8 (Pacific Time) Daylight Saving Off
                        }
                        dBalChk.setMinutes(dBalChk.getMinutes() + dBalChk.getTimezoneOffset() + targetTimeOffset1 );
                        details.balance_check_date = moment(dBalChk).format("MM/DD/YYYY HH:mm:ss");
                        if(key == 0){
                            self.consumerDetails.pp_calculated_from = details.title;
                        }
                        BalDetails.push(details);
                    }
                  });
                  self.latestBalanceDetails = BalDetails;



                   if (Math.floor(response.data.calculate_purchase_power) <= 0) {
                    self.calculatePerchasePower = 0.0;
                   }
                   else{
                    self.calculatePerchasePower = response.data.calculate_purchase_power;
                   }
                   if (Math.floor(response.data.effective_purchase_power) <= 0) {
                    self.effectivePerchasePower = 0.0;
                   }
                   else{
                    self.effectivePerchasePower = response.data.effective_purchase_power;
                   }
                   self.totalPendingTransaction = response.data.total_pending_transaction;
                   self.totalPendingTransactionAgainstAccount = response.data.pending_amount_against_account;
                   self.purchasePowerRule = response.data.consumerDetails.purchase_power_source;
                   if(response.data.consumerDetails.is_algo_based == 1){
                    self.purchasePowerRule = 'Algo';
                   }else{
                    if(self.purchasePowerRule == 'old_rule'){
                      self.purchasePowerRule = 'Old Rule';
                   }
                   else if(self.purchasePowerRule == 'reversed_to_old_rule'){
                    self.purchasePowerRule = 'Reversed to Old Rule';
                    }
                    else if(self.purchasePowerRule == 'new_rule'){
                      self.purchasePowerRule = 'New Rule';
                    }
                   }

                  self.$bvModal.show("consumer-details-modal");
              } else {
                  error(response.message);
              }
          })
          .catch(err => {
              error(err.response.data.message);
          });
      });
    },
    mxBankDataFetchAlert(swal_title, swalText, skipNotConnectedMember) {
      var self = this;
      Vue.swal({
        title: swal_title,
        text: swalText,
        type: "warning",
        showCancelButton: true,
        confirmButtonColor: "#149240",
        confirmButtonText: "Yes",
        cancelButtonText: "No, cancel it!",
        closeOnConfirm: true,
        closeOnCancel: true,
        width: '800px'
        }).then((result) => {
            if (result.isConfirmed) {
              self.callMXBankData(skipNotConnectedMember);
            }
        });
    },
    fetchMXBankData() {
      var self = this;
      $(document).on("click", ".fetchMXBankData", function (e) {
        var userID = $(e.currentTarget).attr("data-user-id");
        var mxConsumerID = $(e.currentTarget).attr("data-mx-consumer-id");
        self.userID = userID;
        self.mxConsumerID = mxConsumerID;
        var swal_title = 'Are you sure?';
        var swalText = 'Do you want to fetch the latest account from MX for this consumer?';
        self.mxBankDataFetchAlert(swal_title, swalText, false);
      });
    },
    callMXBankData(skipNotConnectedMember) {
      var self = this;
      var request = {
        'userID':self.userID,
        'mxConsumerID':self.mxConsumerID,
        'skipNotConnectedMember':skipNotConnectedMember,
      };
      api.fetchMXBankData(request)
      .then(response => {
          if (response.code == 200) {
              self.callConsumerUpdateBankAPI(response.data);
          } else {
              error(response.message);
          }
      })
      .catch(err => {
          if(err.response.data.code ==597){
            var swal_title = 'Are you sure?';
            var swalText = err.response.data.message;
            self.mxBankDataFetchAlert(swal_title, swalText, true);
          } else {
            Vue.swal(err.response.data.message, '', 'error')
          }
      });
    },
    callConsumerUpdateBankAPI(request) {
      var self = this;
      api.callConsumerUpdateBankAPI(request)
      .then(response => {
          if (response.code == 200) {
            Vue.swal("Done!", 'Bank updated successfully', "success");
            self.searchRegisteredConsumer();
          } else {
              error(response.message);
          }
      })
      .catch(err => {
          Vue.swal(err.response.data.message, '', 'error')
      });
    },

    commentconsumer() {
      var self = this;
      $(document).on("click", ".commentconsumer", function (e) {
        var userID = $(e.currentTarget).attr("data-user-id");
        self.consumer_id = userID;
          var request = {
              'userID':userID,
          };
          api.getconsumercomments(request)
        .then(response => {
            if (response.code == 200) {
                self.userComments = response.data;
                self.$bvModal.show("consumer-comments-modal");
            } else {
                error(response.message);
            }
        })
        .catch(err => {
            error(err.response.data.message);
        });
      });
    },
    changerule() {
      var self = this;
      $(document).on("click", ".changerule", function (e) {
        var userID = $(e.currentTarget).attr("data-user-id");
        var user_name = $(e.currentTarget).attr("data-user-name");
        var user_rule = $(e.currentTarget).attr("data-user-rule")
        var text_message = "Do you want to change the purchase power rule for "+user_name;
        if(user_rule == 'old_rule' || user_rule == 'reversed_to_old_rule'){
          text_message +=  " from old rule to new rule?";
        }else if(user_rule == 'new_rule'){
          text_message +=  " from new rule to old rule?";
        }
        Vue.swal({
            title: "Warning!!!",
            text: text_message,
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#149240",
            confirmButtonText: "Yes, Change the rule",
            cancelButtonText: "No, Don't change the rule",
            closeOnConfirm: true,
            closeOnCancel: true,
            width: '800px'
            }).then((result) => {
                if (result.isConfirmed) {
                    let request = {
                        user_id: userID
                    }
                    api
                    .updatePurchasePowerRule(request)
                    .then((response) => {
                        Vue.swal("Done!", "The purchase power rule has been updated successfully for "+user_name, "success");
                    })
                    .catch((error) => {
                        Vue.swal(error.response.data.message, '', 'error');
                    })

                }
            })
      });
    },
    saveComment(){
        this.$validator.validateAll().then((result) => {
        if (result) {
          var self = this;
          var request = {
            userID: self.consumer_id,
            comment: self.consumer_comment
          };
          api
          .saveconsumercomment(request)
          .then((response) => {
            if ((response.code = 200)) {
              success(response.message);
              self.userComments = response.data;
              self.consumer_comment = null;
            } else {
              error(response.message);
            }
          })
          .catch((err) => {
            error(err);
          });
          }
        });
    },
    changeStatus() {
      var self = this;

      self.updatedDetails = self.allUserModel.find(
        p => p.edit == self.userId
      );
      if(self.yearsDiff(moment($("#dob").val())) < process.env.MIX_REGISTRATION_AGE_LIMIT){
        error("Minimum age should be "+process.env.MIX_REGISTRATION_AGE_LIMIT);
        return false;
      }else{
        this.$validator.validateAll().then((result) => {
        if (result) {
        if($("#dob").val()!=''){
          var date_of_birth = moment($("#dob").val()).format("YYYY-MM-DD")
        }else{
          var date_of_birth = '';
        }

        if(date_of_birth=="Invalid date")
        {
          error("please enter in valid dob format mm-dd-yyyy");
          return false;
        }
          if(self.enable_microbuilt_override_check == 0){
            self.override_microbuilt_check = 0;
          }
          var request = {
            id: self.userId,
            status: self.status.id,
            first_name: self.first_name,
            middle_name: self.middle_name,
            last_name: self.last_name,
            street_address: self.street_address,
            apt_number: self.apt_number,
            city: self.city,
            state: self.state,
            zipcode: self.zipcode,
            date_of_birth: date_of_birth,
            purchase_power: self.purchase_power,
            weekly_spending_limit: self.weekly_spending_limit,
            automatic_purchase_power: self.automatic_purchase_power,
            required_upload_document: self.required_upload_document,
            active_allow_transaction: self.active_allow_transaction,
            automatic_weekly_spending_limit : self.automatic_weekly_spending_limit,
            override_microbuilt_check: self.override_microbuilt_check

          };
          api
          .changeUserStatus(request)
          .then((response) => {
            if ((response.code = 200)) {
              success(response.message);
              self.$bvModal.hide("consumer-modal");
              self.dob_year = '';
              self.dob_day = '';
              self.month_number = '';
              self.dob_month = "Month";
              self.automatic_purchase_power = '';
              // update the table
              self.updatedDetails.name = [self.first_name, self.middle_name, self.last_name].filter(Boolean).join(' ');
              self.updatedDetails.date_of_birth = date_of_birth!='' ? moment(date_of_birth).format("MM-DD-YYYY") : '';
              self.updatedDetails.status_name = self.status.text;
            } else {
              error(response.message);
            }
          })
          .catch((err) => {
            error(err.response.data.message || err);
          });
          }
        });
      }
    },
    saveBankDelinked(){
      var self = this;
        this.$validator.validateAll().then((result) => {
        if (result) {
          var request = {
            userID: self.userId,
            daily_spending_limit: self.daily_spending_limit,
            consumer_weekly_spending_limit: self.consumer_weekly_spending_limit,
            change_bank_link_type: self.change_bank_link_type,
            selecte_bank_account: self.selected_account_id,
          };
          var swal_title = 'Are you sure to ' + self.consumer_bank_link_message[self.change_bank_link_type].toLowerCase() + '?';
           Vue.swal({
            title: swal_title,
            text: self.swalText,
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#149240",
            confirmButtonText: "Yes",
            cancelButtonText: "No, cancel it!",
            closeOnConfirm: true,
            closeOnCancel: true,
            width: '800px'
            }).then((result) => {
                if (result.isConfirmed) {
                    api
                .saveBankDelinked(request)
                .then((response) => {
                    if ((response.code = 200)) {
                        Vue.swal("Done!", response.message, "success");
                        self.$bvModal.hide("consumer-disable-bank-modal");
                        self.searchRegisteredConsumer();
                    } else {
                        Vue.swal(response.message, '', 'error')
                    }
                })
                .catch((err) => {
                    Vue.swal(err.response.data.message, '', 'error')
                    // error(err.response.data.message || err);
                });
                }
            });
        }
        });
    },
    searchRegisteredConsumer(){
      var self = this;
      if(((self.consumer_first_name).trim().length < 3 && (self.consumer_middle_name).trim().length < 3 && (self.consumer_last_name).trim().length < 3) && $("#phone_no").val().trim() === '' &&  $("#email").val().trim() === ''){
        error("Please provide Consumer name (Min 3 chars) or email(exact) or phone no(exact)");
        return false;
      }
      var request = {
        consumer_first_name: self.consumer_first_name,
        consumer_middle_name: self.consumer_middle_name,
        consumer_last_name: self.consumer_last_name,
        email:self.email,
        phone_no:self.phone_no,
      };
      self.loading = true;
      api
      .searchRegisteredConsumer(request)
      .then(function (response) {
        if (response.code == 200) {
          self.allUserModel = response.data;
          self.loading = false;
        } else {
          error(response.message);
          self.loading = false;
        }
      })
      .catch(function (error) {
        // error(error);
        self.loading = false;
      });
    },
    yearsDiff(d1) {
        let date1 = new Date(d1);
        let date2 = new Date();
        let yearsDiff =  date2.getFullYear() - date1.getFullYear();
        return yearsDiff;
    },
    refresh(bvModalEvt)
    {
     var self = this;
     bvModalEvt.preventDefault();

     var request = {
              'userID':self.userID,
          };
          api.getconsumerdetails(request)
          .then(response => {
              if (response.code == 200) {
                  self.consumerDetails = response.data.consumerDetails;
                  var latestBalanceDetails = response.data.latestBalanceDetails;
                  if(response.data.consumerDetails.account_no!=null){
                    self.consumerDetails.account_no = 'XXXX'+response.data.consumerDetails.account_no;
                  }else{
                    self.consumerDetails.account_no = '';
                  }


                  //Convert Created At date to PST time
                  var dCreated = new Date(response.data.consumerDetails.created_at);
                  var ds = moment.tz(moment(dCreated).format("YYYY-MM-DD"), 'America/Adak').isDST();
                  if(ds){
                    var targetTimeOffset  = -7*60; //desired time zone, taken as GMT-7 (Pacific Time) Daylight Saving On
                  }else{
                    var targetTimeOffset  = -8*60; //desired time zone, taken as GMT-8 (Pacific Time) Daylight Saving Off
                  }
                  dCreated.setMinutes(dCreated.getMinutes() + dCreated.getTimezoneOffset() + targetTimeOffset );
                  self.consumerDetails.created_at = moment(dCreated).format("MM/DD/YYYY HH:mm:ss");
                  var BalDetails = [];
                  latestBalanceDetails.forEach(function(details, key) {
                    //Convert Last balance checked date to PST time
                    if(details.balance_check_date!=null){
                        var dBalChk = new Date(details.balance_check_date);
                        var ds1 = moment.tz(moment(dBalChk).format("YYYY-MM-DD"), 'America/Adak').isDST();
                        if(ds1){
                        var targetTimeOffset1  = -7*60; //desired time zone, taken as GMT-7 (Pacific Time) Daylight Saving On
                        }else{
                        var targetTimeOffset1  = -8*60; //desired time zone, taken as GMT-8 (Pacific Time) Daylight Saving Off
                        }
                        dBalChk.setMinutes(dBalChk.getMinutes() + dBalChk.getTimezoneOffset() + targetTimeOffset1 );
                        details.balance_check_date = moment(dBalChk).format("MM/DD/YYYY HH:mm:ss");
                        if(key == 0){
                            self.consumerDetails.pp_calculated_from = details.title;
                        }
                        BalDetails.push(details);
                    }
                  });
                  self.latestBalanceDetails = BalDetails;



                   if (Math.floor(response.data.calculate_purchase_power) <= 0) {
                    self.calculatePerchasePower = 0.0;
                   }
                   else{
                    self.calculatePerchasePower = response.data.calculate_purchase_power;
                   }
                   if (Math.floor(response.data.effective_purchase_power) <= 0) {
                    self.effectivePerchasePower = 0.0;
                   }
                   else{
                    self.effectivePerchasePower = response.data.effective_purchase_power;
                   }
                   self.totalPendingTransaction = response.data.total_pending_transaction;
                   self.totalPendingTransactionAgainstAccount = response.data.pending_amount_against_account;
                  self.$bvModal.show("consumer-details-modal");
              } else {
                  error(response.message);
              }
          })
          .catch(err => {
              error(err.response.data.message);
          });
    },
    viewReturnTransaction() {
      var self = this;
      $(document).on("click", ".viewReturnTransaction", function (e) {
        var userID = $(e.currentTarget).attr("data-user-id");
        self.userId = userID;
        var request = {
            'userID':userID,
        };

        api.getConsumerReturnTransaction(request)
        .then(response => {
            if (response.code == 200) {
               self.returnTransaction = response.data;
               self.userDetails = self.returnTransaction[0];

                self.$bvModal.show("view-return-transaction-modal");
            } else {
                error(response.message);
            }
        })
        .catch(err => {
            error(err.response.data.message);
        });
      });
    },
    isNumber: function (evt) {
      evt = evt ? evt : window.event;
      var charCode = evt.which ? evt.which : evt.keyCode;
      if (
        charCode != 46 &&
        charCode != 45 &&
        charCode > 31 &&
        (charCode < 48 || charCode > 57)
      ) {
      evt.preventDefault();
      } else {
        return true;
      }
    },
    delinkInstitution() {
      var self = this;
      $(document).on("click", ".delinkInstitution", function (e) {
        var userID = $(e.currentTarget).attr("data-user-id");
        self.userId = userID;
        self.selected_account_id = '';
        self.userDetails = self.allUserModel.find(
        p => p.edit == userID
        );
        self.getFinancialInstitutions();
      });
    },
    getFinancialInstitutions(){
      var self = this;
      var request = {
          'userID':self.userId,
      };
      api.getFinancialInstitutions(request)
      .then(response => {
          if (response.code == 200) {
            self.consumer_name = self.userDetails.name;
            self.financial_institutions = response.data;
            self.$bvModal.show("consumer-financial-institution");
          } else {
              error(response.message);
          }
      })
      .catch(err => {
          error(err.response.data.message);
      });
    },
    confirmDeleteFI(fin){
      console.log(fin);
      console.log(fin.institution_id);
      var self = this;
      self.institution_delete_comment  = '';
      self.institution_id = fin.institution_id;
      self.institution_login_id = fin.institution_login_id;
      self.$refs["fin-ins-delete-confirm-modal"].show();
    },
    deleteFI(){
      var self = this;
      self.$refs["fin-ins-delete-confirm-modal"].hide();
      var request = {
        'institution_id': self.institution_id,
        'institution_login_id': self.institution_login_id,
        'userID': self.userId,
        'reason': self.institution_delete_comment,
      };
      console.log(request);
      api
        .deleteFinancialInstitution(request)
        .then((response) => {
          if(response.code == 200){
            self.$refs["fin-ins-delete-success-modal"].show();
            self.getFinancialInstitutions();
            self.searchRegisteredConsumer();
          }
        })
        .catch(function (error) {
          if(error.response.data.code == 513){
            self.$refs["fin-ins-delete-alert-msg-modal"].show();
          }
        });
    },
    getDefaultSpendingLimit() {
        var self = this;
        if($("#automatic_pp").prop("checked") == true){
            self.daily_spending_limit = self.default_daily_spending_limit;
            self.consumer_weekly_spending_limit = self.default_weekly_spending_limit;
        } else {
            self.daily_spending_limit = self.userDetails.standard_daily_limit;
            self.consumer_weekly_spending_limit = self.userDetails.weekly_spending_limit;
        }
    },
  },
  mounted() {
    var self = this;
    self.getUserStatus();
    if(self.email){
      self.searchRegisteredConsumer();
    }
    document.title = "CanPay - Consumers";
  },
  beforeDestroy(){
    $(document).off('click', '.viewConsumerDetails');
    $(document).off('click', '.fetchMXBankData');
    $(document).off('click', '.editconsumer');
    $(document).off('click', '.delinkBanks');
    $(document).off('click', '.fetchConsumerAccountBalance');
    $(document).off('click', '.refreshConsumerAccountBalance');
    $(document).off('click', '.commentconsumer');
    $(document).off('click', '.viewReturnTransaction');
    $(document).off('click', '.delinkInstitution');
    $(document).off('click', '.changerule');
  },
};
</script>
<style>
.bl-f-weight {
  font-weight: 900;
}
.fa-times-circle {
  font-size:23px;
  color:red;
}
.bank-name-list {
  height: 3rem;
  background-color: #EEEEEE;
  border-radius: 0.5rem;
}
.bank-name-list-disabled {
  height: 3rem;
  background-color: #EEEEEE;
  border-radius: 0.5rem;
}
.iconDisabled {
  cursor: not-allowed !important;
  color: gray !important;
}
.fa-info-circle {
  margin-left: 6px !important;
}
</style>
