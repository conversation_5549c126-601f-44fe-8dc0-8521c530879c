<template>
<div>
  <div v-if="loading">
    <CanPayLoader/>
  </div>
    <div class="content-wrapper" style="min-height: 36px">
      <section class="content-header">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Corporate Parents</h3>
                </div>

                <!-- /.card-header -->
                <div class="card-body">
                  <div class="row">
                  <div class="col-md-4">
                    <div class="form-group">
                     <input
                        class="form-control"
                        placeholder="Corporate Parent Name (min 3 chars)"
                        id="corporate_parent"
                        v-model="corporate_parent"
                      />
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="form-control"
                        placeholder="Phone No (Exact)"
                        id="phone_no"
                        v-model="phone_no"
                      />
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="form-control"
                        placeholder="Email (Exact)"
                        id="email"
                        v-model="email"
                      />
                    </div>
                  </div>
                </div>
                </div>
                  <div class="card-footer">
                    <button
                      type="button"
                      class="btn btn-success"
                      @click="searchCorporateParents()"
                    >
                      Search
                    </button>
                    <button
                      type="button"
                      @click="reset()"
                      class="btn btn-success margin-left-5"
                    >
                      Reset
                    </button>
                    <b-button
                  @click="openModal('add')"
                  class="btn btn-success margin-left-5"
                >
                  <i class="fas fa-user-plus"></i> Add Corporate Parent
                </b-button>
                  </div>
                  <div class="card-body">
                  <b-table-simple
                      responsive
                      show-empty
                      bordered
                      sticky-header="800px"
                      v-if="allCorporatePersonModel.length > 0"
                    >
                      <b-thead head-variant="light">
                        <tr>
                          <th>Name</th>
                          <th>Primary Contact Name</th>
                          <th>Primary Contact Email</th>
                          <th>Primary Contact Phone</th>
                          <th>Created On</th>
                          <th class="text-center">Status</th>
                          <th class="text-center">Allow Merchant Fees Report</th>
                          <th class="text-center">Freeze Sponsor Points </th>
                          <th class="text-center">Action(s)</th>
                        </tr>
                      </b-thead>
                      <b-tbody v-for="(row, index) in allCorporatePersonModel" :key="index">
                        <b-tr>
                          <b-td class="text-left text-gray">{{
                            row.name
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.contact_person_first_name+' '+row.contact_person_last_name
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.contact_person_email
                          }}</b-td>
                          <b-td class="text-center text-gray">{{
                            row.contact_person_phone
                          }}</b-td>
                          <b-td class="text-center text-gray">{{
                            row.created_at
                          }}</b-td>
                          <b-td class="text-center text-gray">{{
                            row.status_name
                          }}</b-td>
                          <b-td class="text-center text-gray">
                              <label class="switch"><input class="allow-merchant-fees-report" :checked="row.allow_merchant_fees_report" type="checkbox" :data-id="row.edit"><span class="slider round"></span></label>
                          </b-td>
                          <b-td class="text-center text-gray">
                              <label class="switch"><input class="enable-freeze-sponsor-points" :disabled="!row.is_sponsor_cp" :checked="row.freeze_sponsor_points" type="checkbox" :data-id="row.edit"  @change="toggleFreezeSponsorPoints(row)"><span :class="row.is_sponsor_cp ? 'slider round' : 'slider round disabled'"></span></label>
                          </b-td>
                          <b-td class="text-center text-gray">
                            <a :data-user-id="row.edit" class="viewStoreDetails custom-edit-btn" title="View Assigned Stores" variant="outline-success" style="border:none"><i class="nav-icon fas fa-eye"></i></a>
                            <a :data-user-id="row.edit" class="editcorporateparent custom-edit-btn" title="Edit Corporate Parent" variant="outline-success" style="border:none"><i class="nav-icon fas fa-edit"></i></a>
                          </b-td>
                        </b-tr>
                      </b-tbody>
                    </b-table-simple>
                    <p v-else>No data displayed. Please refine your search criteria.</p>
                    </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- CP Modal Start -->
      <b-modal
        id="cp-modal"
        ref="modal"
        :header-text-variant="headerTextVariant"
        :title="modalTitle"
        @show="resetModal"
        @hidden="resetModal"
        ok-title="Save"
        ok-variant="success"
        cancel-variant="outline-secondary"
        @ok="handleOk"
        :no-close-on-esc="true"
        :no-close-on-backdrop="true"
      >
        <form ref="form" @submit.stop.prevent="save" class="needs-validation">
          <div class="row">
            <div class="col-md-12">
              <label for="name">
                Name
                <span class="red">*</span>
              </label>
              <input
                id="name"
                name="name"
                v-validate="'required|alpha_spaces'"
                type="text"
                v-model="corporateParentDetails.first_name"
                class="form-control"
              />
              <span v-show="errors.has('name')" class="text-danger">{{
                errors.first("name")
              }}</span>
            </div>
          </div>
          <div class="row">
            <div class="col-md-12">
              <label for="display_name">
                Display Name
              </label>
              <input
                id="display_name"
                name="display_name"
                v-validate="'alpha_spaces'"
                type="text"
                v-model="corporateParentDetails.display_name"
                class="form-control"
              />
              <span v-show="errors.has('display_name')" class="text-danger">{{
                errors.first("display_name")
              }}</span>
            </div>
          </div>
          <div class="row">
            <div class="col-md-12">
              <label for="corporate_url">
                Corporate URL
              </label>
              <input
                id="corporate_url"
                name="corporate_url"
                v-validate="'url'"
                type="text"
                v-model="corporateParentDetails.corporate_url"
                class="form-control"
              />
              <span v-show="errors.has('corporate_url')" class="text-danger">{{
                errors.first("corporate_url")
              }}</span>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <label for="first_name">
                Primary Contact First Name
                <span class="red">*</span>
              </label>
              <input
                id="contact_person_first_name"
                name="contact_person_first_name"
                v-validate="'required|alpha'"
                type="text"
                v-model="corporateParentDetails.contact_person_first_name"
                class="form-control"
              />
              <span
                v-show="errors.has('contact_person_first_name')"
                class="text-danger"
                >{{ errors.first("contact_person_first_name") }}</span
              >
            </div>

            <div class="col-md-6">
              <label for="email">
                Primary Contact Last Name
                <span class="red">*</span>
              </label>
              <input
                id="contact_person_last_name"
                name="contact_person_last_name"
                v-validate="'required|alpha'"
                type="text"
                v-model="corporateParentDetails.contact_person_last_name"
                class="form-control"
              />
              <span
                v-show="errors.has('contact_person_last_name')"
                class="text-danger"
                >{{ errors.first("contact_person_last_name") }}</span
              >
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <label for="first_name">
                Primary Contact Email
                <span class="red">*</span>
              </label>
              <input
                id="contact_person_email"
                name="contact_person_email"
                v-validate="'required|email'"
                type="text"
                v-model="corporateParentDetails.contact_person_email"
                class="form-control"
              />
              <span
                v-show="errors.has('contact_person_email')"
                class="text-danger"
                >{{ errors.first("contact_person_email") }}</span
              >
            </div>

            <div class="col-md-6">
              <label for="email">
                Primary Contact Phone
                <span class="red">*</span>
              </label>
              <input
                id="contact_person_phone"
                name="contact_person_phone"
                v-validate="'required|numeric'"
                type="text"
                v-model="corporateParentDetails.contact_person_phone"
                class="form-control"
              />
              <span
                v-show="errors.has('contact_person_phone')"
                class="text-danger"
                >{{ errors.first("contact_person_phone") }}</span
              >
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <label for="corporate_color">
                Primary Corporate Color (Hex value)
              </label>
              <input
                id="corporate_color"
                name="corporate_color"
                type="text"
                v-model="corporateParentDetails.corporate_color"
                class="form-control"
              />
            </div>

            <div class="col-md-5" style="padding-right: 0">
              <label for="corporate_vanity"> Landing Page Vanity URL </label>
              <input
                id="corporate_vanity"
                name="corporate_vanity"
                type="text"
                v-model="corporateParentDetails.corporate_vanity_url"
                v-on:change="updateVanityUrl()"
                class="form-control"
              />
            </div>
            <div
              class="col-md-1"
              v-clipboard:copy="vanity_url_complete"
              v-clipboard:success="onCopy"
              v-clipboard:error="onError"
            >
              <b-icon-clipboard-plus
                style="width: 40px; height: 40px; vertical-align: -4.15em"
              ></b-icon-clipboard-plus>
            </div>
          </div>
          <div class="row">
            <div class="col-md-12">
              <label for="corporateLogo"> Corporate Logo </label>
              <div class="input-group">
                <div class="custom-file">
                  <input
                    type="file"
                    ref="logo_file"
                    name="corporateLogo"
                    id="corporateLogo"
                    v-on:change="handleFileUpload()"
                    class="custom-file-input"
                    accept="image/*"
                  />
                  <label for="corporateLogo" class="custom-file-label">{{
                    logo_fileName
                  }}</label>
                </div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6">
              <input type="checkbox" id="checkbox" v-model="checkboxActive" />
              <label for="checkbox">Landing Page Active</label>
            </div>
            <div class="col-md-6">
              <b-button
                style="float:right; margin-top: 7px"
                @click="clear"
              >
                Clear File
              </b-button>
            </div>
          </div>
          <div class="row">
            <div class="col-md-12">
              <input type="checkbox" id="checkbox1" v-model="checkboxActiveVoidMenu" />
              <label for="checkbox1">Show Void Transaction Menu in Merchant Admin Panel</label>
            </div>
          </div>
          <div class="row">
            <div class="col-md-12">
              <input type="checkbox" id="checkboxSponsorCP"
                    :disabled="disableBrandCheckbox || disableSponsorCheckbox"
                    v-model="checkboxSponsorCP"
                    @change="handleCheckboxChange('sponsor')"/>
              <label for="checkboxSponsorCP">Sponsor Corporate Parent</label>
            </div>
            <div class="col-md-12">
              <input type="checkbox" id="checkboxBrandCP"
                    :disabled="disableSponsorCheckbox || disableBrandCheckbox"
                    v-model="checkboxBrandCP"
                    @change="handleCheckboxChange('brand')"/>
              <label for="checkboxBrandCP">Brand Corporate Parent</label>
            </div>
            <div class="col-md-12">
              <label for="store_id"> Store </label>
              <multiselect
                id="store_id"
                name="store_id"
                v-model="store_ids"
                :options="stores"
                :multiple="true"
                track-by="retailer"
                :custom-label="customLabel"
                placeholder="Select Stores"
                selectLabel
                deselectLabel
                :close-on-select="false"
                :clear-on-select="false"
                @select="selectStore"
                @remove="removeStore"
              ></multiselect>
            </div>
          </div>

          <div class="row" v-if="isEdit == true">
            <div class="col-md-12">
              <label for="user_type">
                Status
                <span class="red">*</span>
              </label>
              <select
                class="form-control"
                id="user_status"
                name="user_status"
                v-model="corporateParentDetails.status"
                v-validate="'required'"
              >
                <option
                  v-for="(status, index) in statusList"
                  :key="index"
                  :value="status.id"
                >
                  {{ status.status }}
                </option>
              </select>
              <span v-show="errors.has('user_status')" class="text-danger">{{
                errors.first("user_status")
              }}</span>
            </div>
          </div>
        </form>
      </b-modal>
      <!-- CP Modal End -->

      <!-- View Store Modal Start -->
      <b-modal
        id="view-store-modal"
        ref="view-store-modal"
        :header-text-variant="headerTextVariant"
        title="Corporate Parent"
        hide-footer
      >
        <div class="row odd-row">
          <div class="col-md-4 row-value">
            <label for="name">Corporate Parent Name</label>
          </div>
          <div class="col-md-1 row-value">:</div>
          <div class="col-md-7 row-value">
            <span for="name">{{ corporateParent.name }}</span>
          </div>
        </div>
        <div class="row even-row">
          <div class="col-md-4 row-value">
            <label for="access_rights">Primary Contact Name</label>
          </div>
          <div class="col-md-1 row-value">:</div>
          <div class="col-md-7 row-value">
            <span for="name"
              >{{ corporateParent.contact_person_first_name }}
              {{ corporateParent.contact_person_last_name }}</span
            >
          </div>
        </div>
        <div class="row odd-row">
          <div class="col-md-4 row-value">
            <label for="access_other_org_info">Primary Contact Email</label>
          </div>
          <div class="col-md-1 row-value">:</div>
          <div class="col-md-7 row-value">
            <span for="name">{{ corporateParent.contact_person_email }}</span>
          </div>
        </div>
        <div class="row even-row">
          <div class="col-md-4 row-value">
            <label for="access_rights">Primary Contact Phone</label>
          </div>
          <div class="col-md-1 row-value">:</div>
          <div class="col-md-7 row-value">
            <span for="name">{{ corporateParent.contact_person_phone }}</span>
          </div>
        </div>
        <div class="row odd-row">
          <div class="col-md-4 row-value">
            <label for="access_rights">Created On</label>
          </div>
          <div class="col-md-1 row-value">:</div>
          <div class="col-md-7 row-value">
            <span for="name">{{ corporateParent.created_at }}</span>
          </div>
        </div>
        <div class="row even-row">
          <div class="col-md-4 row-value">
            <label for="access_rights">Store Access</label>
          </div>
          <div class="col-md-1 row-value">:</div>
          <div class="col-md-7 row-value">
            <span v-for="item in corporateParent.retailer" :key="item.id">
              <li style="list-style: none">
                <span class="mb-2" :for="item.id">{{ item.retailer }}</span>
              </li>
            </span>
          </div>
        </div>
      </b-modal>
      <!-- View Store Modal Start -->
    </div>
</div>
</template>
<script>
import api from "@/api/user.js";
import { validationMixin } from "vuelidate";
import { required, minLength } from "vuelidate/lib/validators";
import commonConstants from "@/common/constant.js";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "../CustomLoader/CanPayLoader.vue"
export default {
  mixins: [validationMixin],
  data() {
    return {
      modalTitle: "",
      statusList: [],
      headerTextVariant: "light",
      corporateParentDetails: {},
      allCorporatePersonModel: {},
      userId: null,
      stores: {},
      allStores: {},
      store_ids: null,
      disableSponsorCheckbox: false,
      disableBrandCheckbox: false,
      value: [],
      fields: [{ key: "retailer", label: "Store Name", class: "text-center" }],
      corporateParent: {},
      isEdit: false,
      showReloadBtn: false,
      constants: commonConstants,
      logo_file: null,
      logo_fileName: "Choose File",
      checkboxActive: true,
      checkboxActiveVoidMenu: false,
      checkboxSponsorCP: false,
      checkboxBrandCP: false,
      upload_changed: false,
      vanity_url_complete: null,
      loading:false,
      corporate_parent:"",
      phone_no:"",
      email:"",
      cp_type:"merchant",
      deletedStoreIds: [],
      storeErrors: [],
    };
  },
  components: {
    HourGlass,
    CanPayLoader
  },
  created() {
    this.editcorporateparent();
    this.viewStoreDetails();
    this.updateVanityUrl();
    this.toggleAllowMerchantFeesReport();
    //TODO: this.toggleFreezeSponsorPoints();
  },
  methods: {
    handleCheckboxChange(type) {
      var self = this;
      if (type === 'sponsor') {
        if (self.checkboxSponsorCP) {
          self.checkboxBrandCP = false;
        }
      } else if (type === 'brand') {
        if (self.checkboxBrandCP) {
          self.checkboxSponsorCP = false;
        }
      }
      if (!self.checkboxBrandCP && !self.checkboxSponsorCP) {
        type = 'merchant';
      }
      self.cp_type = type;
      self.stores = self.allStores.filter(item => {
        return item.type === type;
      });
    },
    updateVanityUrl() {
      var self = this;
      self.vanity_url_complete =
        process.env.MIX_LANDING_PAGE_URL +
        self.corporateParentDetails.corporate_vanity_url;
    },
    onCopy: function (e) {
      var self = this;
      alert("You just copied the following text to the clipboard: " + e.text);
    },
    onError: function (e) {
      alert("Failed to copy the text to the clipboard");
      console.log(e);
    },
    customLabel(store) {
      return `${store.retailer}`;
    },
    viewStoreDetails() {
      var self = this;
      $(document).on("click", ".viewStoreDetails", function (e) {
        self.corporateParent = self.allCorporatePersonModel.find(
          (p) => p.edit == $(e.currentTarget).attr("data-user-id")
        );
        self.$bvModal.show("view-store-modal");
      });
    },
    clone(obj) {
      if (obj == null || typeof obj != "object") return obj;

      var temp = new obj.constructor();
      for (var key in obj) temp[key] = this.clone(obj[key]);

      return temp;
    },

    editcorporateparent() {
      var self = this;
      $(document).on("click", ".editcorporateparent", function (e) {
        self.userId = $(e.currentTarget).attr("data-user-id");
        var userdetails = self.allCorporatePersonModel.find(
          (p) => p.edit == self.userId
        );
        self.modalTitle = "Edit Corporate Parent";
        self.$bvModal.show("cp-modal");
        self.isEdit = true;
        if (userdetails) {
          self.store_ids = userdetails.retailer;
          self.checkboxSponsorCP = userdetails.is_sponsor_cp;
          self.checkboxBrandCP = userdetails.is_brand_cp;
          self.disableSponsorCheckbox = userdetails.retailer.length > 0 ? true : false;
          self.disableBrandCheckbox = userdetails.retailer.length > 0 ? true : false;
          self.corporateParentDetails = self.clone(userdetails);
          self.corporateParentDetails.user_id = userdetails.edit;
          self.logo_fileName = userdetails.logo_original_name;
          self.checkboxActive = userdetails.active;
          self.checkboxActiveVoidMenu = userdetails.show_void_transaction_menu;

          if (self.logo_fileName == null) {
            self.logo_fileName = "Choose File";
          }

          self.vanity_url_complete =
            process.env.MIX_LANDING_PAGE_URL +
            self.corporateParentDetails.corporate_vanity_url;
          self.getMerchantStores();
        }
        //Fetch the status List
        self.getUserStatus();
      });
    },
    openModal(type) {
      var self = this;
      self.checkboxSponsorCP = false;
      if (type == "edit") {
        self.modalTitle = "Edit Corporate Parent";
        //call to api to get perticular corporate details
        //this.corporateParentDetails = {};
        self.$bvModal.show("cp-modal");
      } else {
        self.disableSponsorCheckbox = false;
        self.disableBrandCheckbox = false;
        self.checkboxBrandCP = false;
        self.modalTitle = "Add Corporate Parent";
        self.$bvModal.show("cp-modal");
        self.getMerchantStores();
      }
    },
    resetModal() {
      var self = this;
      self.corporateParentDetails = {};
      self.store_ids = null;
      self.isEdit = false;
      self.logo_fileName = "Choose File";
      self.logo_file = null;
      self.upload_changed = false;
      self.vanity_url_complete = null;
    },
    handleOk(bvModalEvt) {
      var self = this;
      // Prevent modal from closing
      bvModalEvt.preventDefault();
      // Trigger submit handler
      self.save();
    },
    save() {
      var self = this;
      let formData = new FormData();
      formData.append("logo", self.logo_file);
      formData.append("companyName", this.corporateParentDetails.first_name);
      // Exit when the form isn't valid
      this.$validator.validateAll().then((result) => {
        if (result) {
          //call to api to save the details
          self.corporateParentDetails.active = self.checkboxActive;
          self.corporateParentDetails.show_void_transaction_menu = self.checkboxActiveVoidMenu;
          self.corporateParentDetails.cp_type = self.cp_type;
          if (!self.corporateParentDetails.user_id) {
            // Add section
            self.corporateParentDetails.store_ids = self.store_ids;
            self.corporateParentDetails.logo_url = self.logo_fileName.valueOf();
            self.corporateParentDetails.logo_original_name = self.logo_fileName.valueOf();
            if (
              self.corporateParentDetails.logo_url.valueOf() ==
              "Choose File".valueOf()
            ) {
              self.corporateParentDetails.logo_url = null;
            }

            if (self.logo_file) {
              api.importLogo(formData).then((response) => {
                self.corporateParentDetails.logo_url = process.env.MIX_AWS_CLOUDFRONT_URL + response.data.replace(/^.*[\\\/]/, '');
                self.addCorporateParentApi(self);
              });
            } else {
              self.addCorporateParentApi(self);
            }

          } else {
            // Edit Section
            self.corporateParentDetails.store_ids = self.store_ids;


            if (self.corporateParentDetails.logo_url === "Choose File") {
              self.corporateParentDetails.logo_url = null;
            }
            if (this.upload_changed) {
               self.corporateParentDetails.logo_url = self.logo_fileName.valueOf();
               self.corporateParentDetails.logo_original_name = self.logo_fileName.valueOf();
              api.importLogo(formData).then((response) => {
                self.corporateParentDetails.logo_url = process.env.MIX_AWS_CLOUDFRONT_URL +  response.data.replace(/^.*[\\\/]/, '');
                self.editCorporateParentApi(self);
              });
            } else {
              self.editCorporateParentApi(self);
            }
          }
        }
      });
    },
    editCorporateParentApi(self) {
      self.corporateParentDetails.deleted_store_ids = self.deletedStoreIds
      self.storeErrors = []
      api
        .editCorporateParent(self.corporateParentDetails)
        .then((response) => {
          if (response.code == 200) {
            success(response.message);
            $("#corporateParentsTable").DataTable().ajax.reload(null, false);
            self.$bvModal.hide("cp-modal");
            self.getMerchantStores();
            self.store_ids = null;
            self.resetModal();
            self.searchCorporateParents();
          } else {
            error(response.message);
          }
        })
        .catch((err) => {
          if(err.response.data.data){
              err.response.data.data.forEach(error => {
                  self.storeErrors += '<p><strong>'+error.reward_wheel+'</strong>: '+error.retailer+'</p>'
              });
              self.$swal({
                  title: '<p>'+err.response.data.message+'</p>',
                  html: self.storeErrors,
                  width: 600,
                  icon: 'error'
              })
          }else{
              error(err.response.data.message);
          }
        });
    },
    addCorporateParentApi(self) {
      api
        .addCorporateParent(self.corporateParentDetails)
        .then((response) => {
          if (response.code == 200) {
            success(response.message);
            $("#corporateParentsTable").DataTable().ajax.reload(null, false);
            self.$bvModal.hide("cp-modal");
            self.getMerchantStores();
            self.store_ids = null;
            self.resetModal();
          } else {
            error(response.message);
          }
        })
        .catch((err) => {
          error(err.response.data.message);
        });
    },
    getMerchantStores() {
      var self = this;
      let request = {
        user_id: self.corporateParentDetails.user_id,
      };
      api
        .getStores(request)
        .then((response) => {
          if (response.code == 200) {
            self.allStores = response.data;
            if (self.checkboxBrandCP) {
              self.handleCheckboxChange('brand');
            } else {
              self.handleCheckboxChange('merchant');
            }
          } else {
            error(response.message);
          }
        })
        .catch((err) => {
          error(err.response.data.message);
        });
    },
    getUserStatus() {
      var self = this;
      api
        .getUserStatus()
        .then((response) => {
          if ((response.code = 200)) {
            self.statusList = response.data;
          } else {
            error(response.message);
          }
        })
        .catch((err) => {
          error(err);
        });
    },
    searchCorporateParents(){
      var self = this;
      if((self.corporate_parent).trim().length < 3 && $("#phone_no").val().trim() === '' &&  $("#email").val().trim() === ''){
        error("Please provide Corporate Name (Min 3 chars) or email(exact) or phone no(exact)");
        return false;
      }
      var request = {
        corporate_parent: self.corporate_parent,
        email:self.email,
        phone_no:self.phone_no,
      };
      self.loading = true;
      api
      .searchCorporateParents(request)
      .then(function (response) {
        if (response.code == 200) {
          self.allCorporatePersonModel = response.data;
          self.loading = false;
        } else {
          error(response.message);
          self.loading = false;
        }
      })
      .catch(function (error) {
        // error(error);
        self.loading = false;
      });
    },
    handleFileUpload() {
      let self = this;
      self.logo_file = self.$refs.logo_file.files[0];
      self.logo_fileName = self.$refs.logo_file.files[0].name;
      self.upload_changed = true;
    },
    clear(e) {
      var self = this;
      self.logo_fileName = "Choose File";
      self.$refs.logo_file.value = null;
      self.corporateParentDetails.logo_file = false;
      self.corporateParentDetails.logo_url = "";
      self.corporateParentDetails.logo_original_name = "";

      e.preventDefault();
    },
    reset(){
      var self = this;
      self.corporate_parent = "";
      self.phone_no = "";
      self.email = "";
    },
    toggleAllowMerchantFeesReport() {
      var self = this;
      $(document).on("click", ".allow-merchant-fees-report", function(e) {
        var request = {
          id: $(e.currentTarget).attr("data-id")
        };
        api
          .toggleAllowMerchantFeesReport(request)
          .then(response => {
            if ((response.code = 200)) {
              success(response.message);
              self.searchStores();
            } else {
              error(response.message);
            }
          })
          .catch(err => {
            error(err.response.data.message);
          });
      });
    },
    toggleFreezeSponsorPoints(row) {
      var self = this;
        var freeze_sponsor_points = row.freeze_sponsor_points;
        var request = {
          id: row.edit,
          freeze_sponsor_points: freeze_sponsor_points ? 1 : 0,
        };
        var updated_freeze_sponsor_points = freeze_sponsor_points ? false : true;
        var arrayIndex = self.allCorporatePersonModel.findIndex(item => item.edit === row.edit);
        if (arrayIndex !== -1) {
          self.allCorporatePersonModel[arrayIndex].freeze_sponsor_points = updated_freeze_sponsor_points;
        }
        console.log(self.allCorporatePersonModel);
        var swal_test = freeze_sponsor_points ? 'disable' : 'enable'
        Vue.swal({
          title: "Are you sure?",
          text: "You want to "+ swal_test +" freeze sponsor points!",
          type: "warning",
          showCancelButton: true,
          confirmButtonColor: "#149240",
          confirmButtonText: "Yes, "+ swal_test +" it!",
          cancelButtonText: "No, cancel it!",
          closeOnConfirm: true,
          closeOnCancel: true,
          }).then((result) => {
            if (result.isConfirmed) {
              api
              .toggleFreezeSponsorPoints(request)
              .then(response => {
                if ((response.code = 200)) {
                  success(response.message);
                } else {
                  error(response.message);
                  self.allCorporatePersonModel[arrayIndex].freeze_sponsor_points = freeze_sponsor_points;
                }
              })
              .catch(err => {
                error(err.response.data.message);
                  self.allCorporatePersonModel[arrayIndex].freeze_sponsor_points = freeze_sponsor_points;
              });
            } else {
              self.allCorporatePersonModel[arrayIndex].freeze_sponsor_points = freeze_sponsor_points;
            }
        });
    },
    selectStore(val){
      if(this.store_ids.length > 0){
        const index = this.store_ids.findIndex(item => item.id === val.id);
        if (index !== -1) {
          this.deletedStoreIds = this.deletedStoreIds.splice(index, 1);
        }
      }

    },
    removeStore(val){
      this.deletedStoreIds.push(val.id)
    }
  },
  mounted() {
    var self = this;
    self.getMerchantStores();
    document.title = "CanPay - Corporate Parents";
  },
};
</script>
<style>
.disabled {
    background-color: #e9ecef;
}
</style>


