<template>
<div>
    <div  v-if="loading">
        <CanPayLoader/>
    </div>
    <div class="content-wrapper" style="min-height: 36px">
        <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
            <div class="col-sm-6"></div>
            </div>
        </div>
        </section>
        <div class="hold-transition sidebar-mini">
            <section class="content">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-12">
                            <!-- starting the card -->
                            <div class="card card-success">
                                <!-- starting the card header for title -->
                                <div class="card-header card-success">
                                    <h3 class="card-title">ACH Return Report</h3>
                                </div>
                                <!-- starting the car-body for content -->
                                <div class="card-body" >
                                    <!-- grid property starting -->
                                    <!-- initiating a row -->
                                    <div>
                                    <div class="row" >
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <!-- input to start date -->
                                                        <input
                                                        autocomplete="off"
                                                        class="start-date form-control"
                                                        placeholder="Date"
                                                        id="start-date"
                                                        onkeydown="return false"
                                                        />
                                                    </div>
                                                </div>
                                        </div> 
                                    </div>

                                </div>
                                    <div class="card-footer">
                                    <button
                                        type="button"
                                        class="btn btn-success"
                                        id="generateBtn"
                                        @click="fetchAchReturnReport"
                                    >
                                        Generate</button>
                                    <button
                                        type="button"
                                        @click="exportAchReturn()"
                                        class="btn btn-danger margin-left-5"
                                    >
                                        Export
                                        <i class="fa fa-download" aria-hidden="true"></i>
                                    </button>
                                    <button
                                        type="button"
                                        @click="reset()"
                                        class="btn btn-success margin-left-5"
                                    >
                                        Reset
                                    </button>
                                    </div>
                                    <div v-if="achReturnReport.length>0">
                                        <div class="mt-4 p-3" >
                                        <div class="row">
                                            <div class="col-lg-3 col-md-3 col-sm-6 col-xs-6">
                                            <div class="info-box bg-color-class">
                                                    <span class="info-box-icon bg-indigo elevation-1">
                                                        <i class="fas fa-dollar-sign"></i></span> 
                                                            <div class="info-box-content">
                                                            <span class="info-box-text">New R01</span>
                                                            <span class="info-box-number">{{new_ro1}}</span
                                                            ></div>
                                                </div>
                                            </div>
                                            <div class="col-lg-3 col-md-3 col-sm-6 col-xs-6">
                                            <div class="info-box bg-color-class">
                                                    <span class="info-box-icon bg-indigo elevation-1">
                                                        <i class="fas fa-dollar-sign"></i></span>
                                                        <div class="info-box-content"><span class="info-box-text">Old R01</span> 
                                                        <span class="info-box-number">{{old_ro1}}</span></div>
                                                </div>
                                            </div>

                                            <div class="col-lg-3 col-md-3 col-sm-6 col-xs-6">
                                            <div class="info-box bg-color-class">
                                                    <span class="info-box-icon bg-indigo elevation-1">
                                                        <i class="fas fa-dollar-sign"></i></span> 
                                                        <div class="info-box-content"><span class="info-box-text">Total R02</span> <span class="info-box-number">{{total_ro2}}</span></div>
                                                </div>
                                            </div>
                                            <div class="col-lg-3 col-md-3 col-sm-6 col-xs-6">
                                            <div class="info-box bg-color-class">
                                                    <span class="info-box-icon bg-indigo elevation-1">
                                                        <i class="fas fa-dollar-sign"></i></span> 
                                                        <div class="info-box-content"><span class="info-box-text">Total R03 and R04</span> <span class="info-box-number">{{total_ro3_and_ro4}}</span></div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row mt-3">
                                            <div class="col-lg-3 col-md-3 col-sm-6 col-xs-6">
                                            <div class="info-box bg-color-class">
                                                    <span class="info-box-icon bg-indigo elevation-1">
                                                        <i class="fas fa-dollar-sign"></i></span> 
                                                        <div class="info-box-content"><span class="info-box-text">Total R05 and All</span> <span class="info-box-number">{{total_ro5_and_all}}</span></div>
                                                </div>
                                            </div>
                                            <div class="col-lg-3 col-md-3 col-sm-6 col-xs-6">
                                            <div class="info-box bg-color-class">
                                                    <span class="info-box-icon bg-indigo elevation-1">
                                                        <i class="fas fa-dollar-sign"></i></span>
                                                        <div class="info-box-content"><span class="info-box-text">Total New Returns</span> 
                                                        <span class="info-box-number">{{total_new_return}}</span></div>
                                                </div>
                                            </div>
                                            <div class="col-lg-3 col-md-3 col-sm-6 col-xs-6">
                                            <div class="info-box bg-color-class">
                                                    <span class="info-box-icon bg-indigo elevation-1">
                                                        <i class="fas fa-dollar-sign"></i></span> 
                                                        <div class="info-box-content"><span class="info-box-text">Total All Returns</span> <span class="info-box-number">{{total_all_return}}</span></div>
                                                </div>
                                            </div>
                                            <div class="col-lg-3 col-md-3 col-sm-6 col-xs-6">
                                            <div class="info-box information-box bg-color-class">
                                                    <span class="info-box-icon bg-indigo elevation-1">
                                                        <i class="fas fa-file"></i></span> 
                                                        <div class="info-box-content"><span class="info-box-text">Total Return Count</span> <span class="info-box-number">{{total_return_count}}</span></div>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                    <div class="mt-2">
                                        <div class="p-3">
                                            <b-table 
                                                responsive
                                                show-empty
                                                bordered
                                                sticky-header="800px"
                                                :items="achReturnReport" 
                                                :fields="fields"
                                                head-variant="light"></b-table>
                                        </div>
                                    </div>
                                    </div>
                                    <div v-else class="p-4">
                                        <p>No data displayed. Please refine your search criteria.</p>
                                    </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </div>
</div>
</template>
<script>
import moment from "moment";
import api from "@/api/ACHTransaction.js";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "./CustomLoader/CanPayLoader.vue"
export default {
  components: {
    HourGlass,
    CanPayLoader
  },
    data(){
        return {
            fields:[
                {
                    key: 'retailer',
                    label: 'Store Name'                    
                },
                {
                    key: 'consumer_name',
                    label: 'Name'
                },
                {
                    key: 'transaction_number',
                    label: 'Transaction Number'
                },
                {
                    key: 'returned_on',
                    label: 'Returned Date'
                },
                {
                    key: 'return_reason',
                    label: 'Return Reason'
                },
                {
                    key: 'amount',
                    label: 'Amount ($)'
                },
                {
                    key:  'account_no',
                    label: 'Account Number'
                },
                {
                    key: 'routing_no',
                    label: 'Routing Number'
                }
            ],
            achReturnReport:[],
            new_ro1:null,
            old_ro1:null,
            total_ro2:null,
            total_ro3_and_ro4:null,
            total_ro5_and_all:null,
            total_all_return:null,
            total_new_return:null,
            total_return_count:null,
            loading:false,

        }
    },
    mounted() {
        var self = this;
        $("#start-date").datepicker({
        format: "mm/dd/yyyy",
        autoclose: true,
        startDate:process.env.MIX_MIN_ACCEPTED_DATE,
        endDate: moment(new Date()).format("MM/DD/YYYY"),
        todayHighlight: true,
        });
        $("#start-date").val(moment(new Date()).format("MM/DD/YYYY"));
    },
    methods:{

        fetchAchReturnReport(){
            var self = this;
            // Parse dates using moment
            const startDate = moment($("#start-date").val(), "MM/DD/YYYY");
            const todayDate = moment(new Date());

            // Compare the dates
            if (todayDate.isBefore(startDate)) {
                return error("ACH return report is not available for future date " + startDate.format("MM/DD/YYYY") + ".");
            }

            if($("#start-date").val() == '' || $("#start-date").val() == null )
            {
                return error('Select a valid start date');
            }
            const request = {
                from_date: moment($("#start-date").val()).format("YYYY-MM-DD"),
            }
            self.loading = true;
            api
                .getAchReturnReport(request)
                .then((response)=>{

                    if(response.code == 200){

                        self.achReturnReport = response.data;

                        if(self.achReturnReport.length>0){

                            self.new_ro1 = self.achReturnReport[0].TotalNewR01;
                            self.old_ro1 = self.achReturnReport[0].TotalOldR01;
                            self.total_ro2 = self.achReturnReport[0].TotalR02;
                            self.total_ro3_and_ro4 = self.achReturnReport[0].TotalR03R04;
                            self.total_ro5_and_all = self.achReturnReport[0].TotalR05AndAll;
                            self.total_all_return = self.achReturnReport[0].TotalAllReturns; 
                            self.total_new_return =  self.achReturnReport[0].TotalNewReturns;
                            self.total_return_count = self.achReturnReport[0].TotalReturnCount;
                        }

                    }else{
                        self.achReturnReport = []
                    }
                    if(self.achReturnReport.length == 0){
                        error("ACH return report is not available for date " + moment($("#start-date").val()).format("MM/DD/YYYY")+".");
                    }

                    self.loading = false;

                })
                .catch((err)=>{
                    error(err);
                    self.loading = false;
                })
        },

        exportAchReturn(){
            let self = this;
            const request = {
                from_date: moment($("#start-date").val()).format("YYYY-MM-DD"),
            }
            api
                .exportAchReturnReport(request)
                .then((response) => {
                    var FileSaver = require("file-saver");
                    var blob = new Blob([response], {
                        type: "application/xlsx",
                    });
                    FileSaver.saveAs(
                        blob,
                        moment().format("MM/DD/YYYY") + "_ach_return_report.xlsx"
                    );
                })
                .catch((err)=>{
                    error(err);
                })

        },
 
        reset(){
            $("#start-date").val('');
        },

    }
}
</script>