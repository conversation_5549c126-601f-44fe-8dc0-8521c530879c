import VueFirestore from 'vue-firestore';
import Firebase from 'firebase'
import Vue from 'vue'

require('firebase/firestore')

Vue.use(VueFirestore)


var config = {
    apiKey: process.env.MIX_API_FIREBASE_API,
    authDomain: process.env.MIX_API_AUTO_DOMAIN,
    databaseURL: process.env.MIX_API_DATABASE_URL,
    projectId: process.env.MIX_API_PROJECT_ID,
    storageBucket: process.env.MIX_API_STORAGE_BUCKET,
    messagingSenderId: process.env.MIX_API_MESSAGING_SENDERID,
    appId: process.env.MIX_API_APPID,
}
var firebaseApp = Firebase.initializeApp(config);

export const db = firebaseApp.firestore();

// date issue fix according to firebase
const settings = {
    //timestampsInSnapshots: true
};


db.settings(settings);
export default {
    name: "db",
};