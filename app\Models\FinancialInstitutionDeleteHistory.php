<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
class FinancialInstitutionDeleteHistory extends Model
{

    protected $table = 'financial_institution_delete_history';

    /**
     * __construct
     * This function will insert an alphanumeric 32 character unique id as primary key in the table whenever a data get inserted
     * @param  mixed $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        $this->id = generateUUID();

        parent::__construct($attributes);
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'consumer_id',
        'institution_id',
        'institution_login_id',
        'deleted_by',
    ];
    public $timestamps = true;
    public $incrementing = false;
}
