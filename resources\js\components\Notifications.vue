<template>
<div>
  <div v-if="loading">
    <CanPayLaoder/>
  </div>
  <div class="content-wrapper" style="min-height: 36px;">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Global Notifications</h3>
                </div>
                <div class="card-body">
                  <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                            <input
                                class="form-control"
                                placeholder="Title (min 3 chars)"
                                id="title"
                                v-model="title"
                            />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <button
                      type="button"
                      class="btn btn-success"
                      @click="searchNotifications()"
                    >
                      Search
                    </button>
                    <button
                      type="button"
                      @click="reset()"
                      class="btn btn-success margin-left-5"
                    >
                      Reset
                    </button>
                    <b-button
                        class="btn btn-success margin-left-5"
                        @click="openModal('add')"
                    >
                        <i class="fas fa-exclamation-triangle"></i> Add Global Notification
                    </b-button>
                  </div>
                <!-- /.card-header -->
                <div class="card-body">
                  <b-table-simple
                      responsive
                      show-empty
                      bordered
                      sticky-header="800px"
                      v-if="notificationModel.length > 0"
                    >
                      <b-thead head-variant="light">
                        <tr>
                            <th width="30%">Title</th>
                            <th width="50%">Notification Body</th>
                            <th class="text-center">Created On</th>
                            <th class="text-center">Action</th>
                        </tr>
                      </b-thead>
                      <b-tbody v-for="(row, index) in notificationModel" :key="index">
                        <b-tr>
                          <b-td class="text-left text-gray">{{
                            row.title
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.notification
                          }}</b-td>
                          <b-td class="text-center text-gray">{{
                            row.created_at
                          }}</b-td>
                          <b-td class="text-center text-gray">
                            <a :data-notification-id="row.edit" class="sendNotification custom-edit-btn" title="Send Notification" variant="outline-success" style="border:none"><i class="nav-icon fas fa-paper-plane"></i></a>
                          </b-td>
                        </b-tr>
                      </b-tbody>
                    </b-table-simple>
                    <p v-else>No data displayed. Please refine your search criteria.</p>
                    </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
    <!-- Notification Modal Start -->
    <b-modal
      id="notification-modal"
      ref="modal"
      :header-text-variant="headerTextVariant"
      :title="modalTitle"
      @show="resetModal"
      @hidden="resetModal"
      ok-title="Save"
      ok-variant="success"
      cancel-variant="outline-secondary"
      @ok="handleOk"
      :no-close-on-esc="true"
      :no-close-on-backdrop="true"
    >
      <form ref="form" @submit.stop.prevent="save" class="needs-validation">
        <div class="row">
          <div class="col-md-12">
            <label for="title">Title <span class="red">*</span></label>
            <input id="title" name="title" v-validate="'required'" type="text" v-model="notificationModel.title" class="form-control">
            <span v-show="errors.has('title')" class="text-danger">{{ errors.first('title') }}</span>
          </div>
        </div>
        <div class="row">
          <div class="col-md-12">
            <label for="notification">Notification <span class="red">*</span></label>
            <textarea id="notification" style="height: 100px;resize: none;" name="notification" v-validate="'required'" type="text" v-model="notificationModel.notification" class="form-control"></textarea>
            <span v-show="errors.has('notification')" class="text-danger">{{ errors.first('notification') }}</span>
          </div>
        </div>
      </form>
    </b-modal>
    <!-- Notification Modal End -->
  </div>
</div>
</template>
<script>
import api from "@/api/notification.js";
import { validationMixin } from "vuelidate";
import { required, minLength } from "vuelidate/lib/validators";
import commonConstants from "@/common/constant.js";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "./CustomLoader/CanPayLoader.vue";
export default {
  data() {
    return {
      notificationModel: {},
      modalTitle: "",
      headerTextVariant: "light",
      currentUser: localStorage.getItem("user")? JSON.parse(localStorage.getItem("user")): null,
      notificationId: "",
      showReloadBtn:false,
      loading:false,
      title:""
    };
  },
  components: {
    HourGlass,
    CanPayLoader
  },
  created() {
      this.sendNotification();
  },
  methods: {
    searchNotifications(){
      var self = this;
      if((self.title).trim().length < 3){
        error("Please provide Title (Min 3 chars)");
        return false;
      }
      var request = {
        title: self.title,
      };
      self.loading = true;
      api
      .searchNotifications(request)
      .then(function (response) {
        if (response.code == 200) {
          self.notificationModel = response.data;
          self.loading = false;
        } else {
          error(response.message);
          self.loading = false;
        }
      })
      .catch(function (error) {
        // error(error);
        self.loading = false;
      });
    },
    sendNotification() {
      var self = this;
      $(document).on("click", ".sendNotification", function(e) {
        self.notificationId = $(e.currentTarget).attr("data-notification-id");
        let request = {
            notification_id: self.notificationId,
        };
        Vue.swal({
            title: "Are you sure to send?",
            text: "You will not be able to undo this action!",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#149240",
            confirmButtonText: "Yes, send it!",
            cancelButtonText: "No, cancel it!",
            closeOnConfirm: true,
            closeOnCancel: true,
            }).then((result) => {
                if (result.isConfirmed) {
                    api
                    .sendNotification(request)
                    .then(response => {
                        if ((response.code == 200)) {
                            $("#notificationsTable")
                            .DataTable()
                            .ajax.reload(null, false);
                            Vue.swal("Sent!", response.message, "success");
                        } else {
                            Vue.swal(response.message, '', 'error')
                        }
                    })
                    .catch(err => {
                        Vue.swal(err.response.data.message, '', 'error')
                    });
                }
            })
      });
    },
    openModal(type) {
        var self = this;
        self.modalTitle = "Add Global Notification";
        self.$bvModal.show("notification-modal");
    },
    resetModal() {
      var self = this;
      self.notificationModel = {};
    },
    handleOk(bvModalEvt) {
      var self = this;
      // Prevent modal from closing
      bvModalEvt.preventDefault();
      // Trigger submit handler
      self.save();
    },
    save() {
      var self = this;
      // Exit when the form isn't valid
      this.$validator.validateAll().then((result) => {
        if (result) {
        //call to api to save the details
            api
                .addNotification(self.notificationModel)
                .then(response => {
                    if ((response.code == 200)) {
                        success(response.message);
                        self.$bvModal.hide("notification-modal");
                        self.resetModal();
                    } else {
                        error(response.message);
                    }
                })
                .catch(err => {
                    error(err.response.data.message);
                });
        }
    });
    },
    reset(){
      var self = this;
      self.title = "";
    }
  },
  mounted() {
    var self = this;
    document.title = "CanPay - Global Notifications";
  }
};
</script>

