<template>
<div>
  <div v-if="loading">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Update Transactions</h3>
                </div>

                <!-- /.card-header -->
                <div class="card-body">
                  <b-table-simple
                      responsive
                      show-empty
                      bordered
                      sticky-header="800px"
                    >
                      <b-thead head-variant="light">
                        <b-tr>
                          <b-th class="text-center">Transaction Date</b-th>
                          <b-th class="text-center">Expected Posting to Bank</b-th>
                          <b-th class="text-center">Actual Posting to Bank</b-th>
                          <b-th class="text-center">Status</b-th>
                          <b-th class="text-center">Action</b-th>
                        </b-tr>
                      </b-thead>
                      <b-tbody v-for="(row, index) in report" :key="index" v-if="report.length > 0">
                        <b-tr>
                          <b-td class="text-center text-gray">{{
                            row.transaction_date
                          }}</b-td>
                          <b-td class="text-center text-gray">{{
                            row.expected_posting_date
                          }}</b-td>
                          <b-td class="text-center text-gray">{{
                            row.actual_posting_date
                          }}</b-td>
                          <b-td class="text-center text-gray">
                              <span v-if="row.processed_to_bank == 0">Processed to Bank</span>
                              <span v-else>Pending</span>
                          </b-td>
                          <b-td class="text-left text-gray">
                              <span v-if="index == 1 && row.processed_to_bank == 1 && row.marchant_scheduler_required == 0">
                                  <a style="color: white !important" class="btn btn-success" @click="updateTransactionStatus(row.transaction_date)"
                                  ><i class="fa fa-check fa-lg"></i> Update</a>
                            </span>
                              <span v-if="index == 1 && row.marchant_scheduler_required == 1">
                                  <a style="color: white !important" class="btn btn-success" @click="runMerchantScheduler(row.transaction_date)"
                                  ><i class="fa fa-check fa-lg"></i> Post CanPay Fee</a>
                              </span>
                          </b-td>
                        </b-tr>
                      </b-tbody>
                      <b-tbody v-else>
                        <b-tr>
                          <b-td colspan="5" class="text-center">No transaction found for updation.</b-td>
                        </b-tr>
                      </b-tbody>
                    </b-table-simple>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</div>
</template>
<script>
import api from "@/api/transaction.js";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "./CustomLoader/CanPayLoader.vue"
export default {
  data() {
    return {
      allStoreModel: {},
      currentUser: localStorage.getItem("user")
        ? JSON.parse(localStorage.getItem("user"))
        : null,
      report: {},
      loading: false,
    };
  },
  components: {
    HourGlass,
    CanPayLoader
  },
  created() {

  },
  methods: {
    updateTransactionStatus(transaction_date){
        var self = this;
        var request = {
          transaction_date: transaction_date,
        };
        api
          .updateTransactionStatus(request)
          .then((response) => {
            if ((response.code = 200)) {
              success(response.message);
              self.getPendingTransactions();
            } else {
              error(response.message);
            }
          })
          .catch((err) => {
            error(err);
          });
    },
    runMerchantScheduler(transaction_date){
        var self = this;
        var request = {
          transaction_date: transaction_date,
        };
        api
          .runMerchantScheduler(request)
          .then((response) => {
            if ((response.code = 200)) {
              success(response.message);
              self.getPendingTransactions();
            } else {
              error(response.message);
            }
          })
          .catch((err) => {
            error(err);
          });
    },
    getPendingTransactions() {
      var self = this;
      self.loading = true;
      api
          .getPendingTransactions()
          .then((response) => {
            if ((response.code = 200)) {
              self.report = response.data;
              self.loading = false;
            } else {
              error(response.message);
              self.loading = false;
            }
          })
          .catch((err) => {
            error(err);
            self.loading = false;
          });
    },
  },
  mounted() {
    var self = this;
    setTimeout(function () {
      self.getPendingTransactions();
    }, 1000);
    document.title = "CanPay - Update Transactions";
  },
};
</script>

