<?php
namespace App\Http\Clients;

use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;

/**
 *
 * @package App\Http\Clients
 */
class BLLHttpClient
{
    /**
     * @var Client
     */
    private $client;
    /**
     * BLLHttpClient constructor.
     */
    public function __construct()
    {
        $this->client = new Client(['base_uri' => config('app.bll_api_base_url')]);
    }

    public function payReversalWithToken($params)
    {
        try {
            $params['api'] = '/production/payment/payreversalwithtoken';
            $request['headers'] = $params['headers'];
            $request['body'] = $params['body'];
            $response = $this->client->post($params['api'], $request);
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "BLL payment reversal with body: " . $request['body'] . " returned response : " . $response->getBody());
            Log::channel('void-transaction-failure')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "BLL payment with body: " . $request['body'] . " returned response: " . $response->getBody());
            return $response->getBody();
        } catch (\GuzzleHttp\Exception\ClientException $ex) {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while sending request to BLL payment reversal with token API.", [EXCEPTION => $ex]);
            return $ex->getResponse()->getBody()->getContents();
        }
    }
}
