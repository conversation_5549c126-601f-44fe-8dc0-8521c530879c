<?php

namespace App\Http\Controllers;

use App\Http\Factories\EmailExecutor\EmailExecutorFactory;
use App\Http\Factories\SmsExecutor\SmsExecutorFactory;
use App\Models\NotificationLog;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class NotificationLogController extends Controller
{
    public function __construct(Request $request)
    {
        $this->request = $request;
        $this->emailexecutor = new EmailExecutorFactory();
        $this->sms = new SmsExecutorFactory();
    }

    public function searchNotifications(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Notification Logs search started.");
        // Validating input request
        $this->validate($request, [
            'consumer' => VALIDATION_REQUIRED_WITHOUT_ALL . ':phone_no,email',
            'phone_no' => VALIDATION_REQUIRED_WITHOUT_ALL . ':email,consumer',
            'email' => VALIDATION_REQUIRED_WITHOUT_ALL . ':phone_no,consumer',
        ]);

        //Search with in Notification Logs
        $notification_logs = $this->_searchNotificationLogs($request);

        $message = trans('message.notification_log_search_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Notification Logs Search Complete");
        return renderResponse(SUCCESS, $message, $notification_logs);
    }

    /**
     * _searchNotificationLogs
     * Search the notification logs with the search criteria
     * @param  mixed $searchArray
     * @return void
     */
    private function _searchNotificationLogs($request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Notification log search started with the criteria.");

        // Search the notification logs
        $sql = 'SELECT COUNT(nl1.id) resend_count, nl.*, s.val, s.sms_no, CASE WHEN u.first_name IS NOT NULL THEN REPLACE(CONCAT(COALESCE(
            REPLACE(u.first_name, " ",""), "")," ", COALESCE(
            REPLACE(u.middle_name, " ",""), "")," ", COALESCE(
            REPLACE(u.last_name, " ",""), "")),"  "," ")
            WHEN u.first_name IS NULL AND u.contact_person_first_name IS NOT NULL THEN REPLACE(CONCAT(COALESCE(
            REPLACE(u.contact_person_first_name, " ",""), "")," ", COALESCE(
            REPLACE(u.contact_person_last_name, " ",""), "")," "),"  "," ")
            ELSE REPLACE(CONCAT(COALESCE(
            REPLACE(rsd.first_name, " ",""), "")," ", COALESCE(
            REPLACE(rsd.middle_name, " ",""), "")," ", COALESCE(
            REPLACE(rsd.last_name, " ",""), "")),"  "," ") END AS consumer_name
            FROM notification_logs nl
            LEFT JOIN notification_logs nl1 ON nl.id = nl1.parent_id
            JOIN settings s ON nl.gateway_used = s.id
            LEFT JOIN users u ON u.user_id = nl.user_id';
        if ($request['type'] == EMAIL) {
            $sql .= ' LEFT JOIN (SELECT * FROM registration_session_details WHERE email = ? ORDER BY created_at DESC LIMIT 1) rsd ON rsd.email = nl.to_email ';
            $searchStr = [$request['email']];
        } else {
            $sql .= ' LEFT JOIN (SELECT * FROM registration_session_details WHERE phone = ? ORDER BY created_at DESC LIMIT 1) rsd ON rsd.phone = nl.to_phone ';
            $searchStr = [$request['phone_no']];
        }
        $sql .= ' WHERE nl.parent_id IS NULL AND nl.type = ? AND nl.notification_time BETWEEN ? AND ? ';
        array_push($searchStr, $request['type'], $request['date'], $request['date'] . ' 23:59:59');
        if (trim($request['phone_no'])) {
            $sql .= ' AND  nl.to_phone = ? ';
            array_push($searchStr, $request['phone_no']);
        }
        if (trim($request['email'])) {
            $sql .= ' and nl.to_email = ? ';
            array_push($searchStr, $request['email']);
        }
        $sql .= " GROUP BY nl.id ORDER BY nl.created_at desc LIMIT 100";
        $notification_logs = DB::connection(MYSQL_RO)->Select($sql, $searchStr);
        $consumerArr = [];
        if (!empty($notification_logs)) {
            foreach ($notification_logs as $log) {

                $data = [];
                $data['consumer_name'] = $log->consumer_name;
                $data['notification_event'] = $log->notification_event;
                $data['type'] = $log->type;
                $data['to_email'] = $log->to_email;
                $data['to_phone'] = $log->to_phone;
                $data['email_subject'] = $log->email_subject;
                $data['email_body'] = htmlspecialchars_decode($log->email_body);
                $data['sms_body'] = $log->sms_body;
                $data['notification_time'] = $log->notification_time;
                $data['sent_from'] = $log->type == SMS ? $log->sms_no : strtoupper($log->val);
                $data['resend'] = $log->resend_expiration_time == null || ($log->resend_expiration_time != null && Carbon::parse($log->resend_expiration_time)->gte(Carbon::now())) ? 1 : 0;
                $data['resend_count'] = $log->resend_count;
                $data['edit'] = $log->id;
                $is_cooldown = 0;
                if ($log->resend_count > 0) {
                    $resend_row = NotificationLog::where('parent_id', $log->id)->orderBy('created_at', 'DESC')->first();
                    if ($log->resend_count == 1) {
                        $cooldown_time = config('app.resend_notification_after_first_attempt');
                    } else if ($log->resend_count == 2) {
                        $cooldown_time = config('app.resend_notification_after_second_attempt');
                    } else {
                        $cooldown_time = config('app.resend_notification_from_third_attempt');
                    }
                    $end_time = Carbon::parse($resend_row->created_at)->addMinutes(intval($cooldown_time));
                    $data['timer_start_time'] = strtotime($resend_row->created_at) * 1000;
                    $data['timer_end_time'] = strtotime($end_time) * 1000;

                    if ($end_time->gte(Carbon::now())) {
                        $is_cooldown = 1;
                    }
                }
                $data['is_cooldown'] = $is_cooldown;

                array_push($consumerArr, $data);
            }
        } else {
            $consumerArr = [];
        }

        return $consumerArr;
    }

    public function resendNotification(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Resend Notification process started...");
        // Validating input request
        $this->validate($request, [
            'notification_id' => VALIDATION_REQUIRED,
            'type' => VALIDATION_REQUIRED,
        ]);

        $request->get('type') == EMAIL ? $this->emailexecutor->resendMail($request) : $this->sms->resendSms($request);

        $message = trans('message.notification_resend_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Notification re-sent successfully for Notification ID: " . $request->get('notification_id'));
        return renderResponse(SUCCESS, $message, null);
    }
}
