<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
class LegalEntityInfo extends Model
{

    protected $table = 'legal_entity_info';

    /**
     * __construct
     * This function will insert an alphanumeric 32 character unique id as primary key in the table whenever a data get inserted
     * @param  mixed $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        $this->id = generateUUID();

        parent::__construct($attributes);
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'merchant_id',
        'legal_entity_name',
        'street_address',
        'city',
        'state',
        'zip',
        'federal_tax_id',
        'business_type_id',
        'coorporate_contact_name',
        'title',
        'phone',
        'email',
        'recepient_address',
    ];
    public $timestamps = true;
    public $incrementing = false;
    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
}
