<template>
<div>
  <div v-if="loading">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px;">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Return Transactions By Return Code</h3>
                </div>
                <div class="card-body">
                <div class="row">
                  <div class="col-md-6">
                    <div class="form-group">
                      <input
                        class="start-date form-control"
                        placeholder="Start Date"
                        id="start-date"
                        onkeydown="return false"
                        autocomplete="off"
                      />
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-group">
                      <input
                        class="end-date form-control"
                        placeholder="End Date"
                        id="end-date"
                        onkeydown="return false"
                        autocomplete="off"
                      />
                    </div>
                  </div>
                </div>
                <div class="row">
                  <div class="col-md-3">
                    <div class="form-group">
                      <label class="switch"><input class="enable-employee-login" type="checkbox" v-model="toggle_local_transaction_time_search"><span class="slider round"></span></label> Local Transaction Date
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-group">
                      <label>Return Type</label>&nbsp;
                      <input type="radio" name="new_representable" id="radio0" v-model="new_representable">&nbsp;<label for="radio0">All Returns</label> &nbsp;&nbsp;
                      <input type="radio" name="new_representable" id="radio1" value="0" v-model="new_representable">&nbsp;<label for="radio1">New Returns</label> &nbsp;&nbsp;
                      <input type="radio" name="new_representable" id="radio2" value="1" v-model="new_representable">&nbsp;<label for="radio2">Represented Returns</label>
                    </div>
                  </div>
                </div>
              </div>
              <div class="card-footer">
                <button
                  type="button"
                  class="btn btn-success"
                  @click="generateReport(false)"
                >
                  Generate
                </button>
                <button
                  type="button"
                  @click="generateReport(true)"
                  class="btn btn-danger ml-10"
                >
                  Generate & Export <i
                    class="fa fa-download ml-10"
                    aria-hidden="true"
                  ></i>
                </button>
              </div>
                <!-- /.card-header -->
                <div class="card-body">
                  <div class="row">
                    <div class="col-4">
                      <b-table-simple bordered>
                        <b-tbody>
                          <b-tr v-for="(returnRow, index) in returnDetails" :key="index">
                            <b-td :class="returnDetails.length== index+1?'text-bold text-left':'text-left'">{{
                            returnRow.title
                          }}</b-td>
                            <b-td :class="returnDetails.length== index+1?'text-bold text-center':'text-center'">${{
                            returnRow.amount
                          }}</b-td>
                          </b-tr>
                        </b-tbody>
                      </b-table-simple>
                    </div>
                  </div>

                <div class="row" v-if="report.length > 0">
                    <div class="col-4">
                      <b-table-simple bordered>
                        <b-th>Reason Code</b-th>
                        <b-th>Count</b-th>
                        <b-th>Amount</b-th>
                        <b-th>Status</b-th>
                        <b-tbody>
                          <b-tr v-for="(data, index) in report" :key="index">
                            <b-td v-if="data.status != null" class="text-center">{{
                            data.reason_code
                          }}</b-td>
                          <b-td v-if="data.status == null && data.reason_code != null" class="text-bold text-center">{{data.reason_code}} Total</b-td>
                          <b-td v-if="data.status == null && data.reason_code == null" class="text-bold text-center">Grand Total</b-td>
                          <b-td :class="data.status == null ? 'text-bold text-right' : 'text-right'">{{
                            data.total_count
                          }}</b-td>
                          <b-td :class="data.status == null ? 'text-bold text-right' : 'text-right'">${{
                            data.total_return_amount
                          }}</b-td>
                          <b-td class="text-center">{{
                            data.status
                          }}</b-td>
                          </b-tr>
                        </b-tbody>
                      </b-table-simple>
                    </div>
                  </div>

                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</div>
</template>
<script>
import api from "@/api/transaction.js";
import moment from "moment";
import { validationMixin } from "vuelidate";
import { required, minLength } from "vuelidate/lib/validators";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "./CustomLoader/CanPayLoader.vue"
export default {
  mixins: [validationMixin],
  data() {
    return {
      allTransactionModel: {},
      showMsg: false,
      transaction_id: null,
      statusList: [],
      reasonList:[],
      comment: "",
      currentUser: localStorage.getItem("user")
        ? JSON.parse(localStorage.getItem("user"))
        : null,
      loading: false,
      report: [],
      consumerList: [],
      selectedConsumer: null,
      email:"",
      phone_no:"",
      reasonCodeTitle: "",
      reasonCodeDiscription: "",
      isLoading: false,
      transaction_id: "",
      status:null,
      reason:null,
      toggle_local_transaction_time_search: false,
      new_representable: null,
      returnDetails:{},
      headerTextVariant: "light",
      returnTransactionDetails:{},
      modal_header: null,
      customer_name: null,
      transaction_id: null,
      return_code: null
    };
  },
  components: {
    HourGlass,
    CanPayLoader
  },
  created() {

  },
  methods: {
    // API call to generate the merchant location transaction report
    generateReport(reportExport) {
      var self = this;
      if($("#end-date").val() == '' &&  $("#start-date").val() == ''){
        error("Please select Start Date & End Date then Generate.");
        return false;
      }
      if (
        moment($("#start-date").val()).format("YYYY-MM-DD") >
        moment().format("YYYY-MM-DD") && $("#start-date").val()!= ''
      ) {
        error("Start date cannot be from future.");
        return false;
      }
      if (
        moment($("#end-date").val()).format("YYYY-MM-DD") >
        moment().format("YYYY-MM-DD") && $("#end-date").val()!= ''
      ) {
        error("End date cannot be from future.");
        return false;
      }
      if($("#start-date").val()!=''){
        var from_date = moment($("#start-date").val()).format("YYYY-MM-DD");
      }else{
        var from_date = '';
      }
      if($("#end-date").val()!=''){
        var to_date = moment($("#end-date").val()).format("YYYY-MM-DD");
      }else{
        var to_date = '';
      }
      self.report = [];
      var request = {
        from_date: from_date,
        to_date: to_date,
        toggle_local_transaction_time_search: self.toggle_local_transaction_time_search,
        new_representable: self.new_representable
      };
      if(request.from_date > request.to_date){
        error("To Date cannot be greater than From date");
        return false;
      }
      self.loading = true;
      api
        .generateReturnTransactionGroupReport(request)
        .then(function (response) {
          if (response.code == 200) {
            self.report = response.data.report;
            self.returnDetails = response.data.returnDetails;
            if(self.report.length > 0){
              if (reportExport) {
                self.exportReport();
              } else {
                self.loading = false;
              }
            }else {
              error("No records found!");
              self.loading = false;
            }
          } else {
            error(response.message);
            self.loading = false;
          }
        })
        .catch(function (error) {
          // error(error);
          self.loading = false;
        });
    },

    // exports the report
    exportReport() {
      var self = this;
      self.loading = true;
      if($("#end-date").val() == '' &&  $("#start-date").val() == ''){
        error("Please select Start Date & End Date then Generate.");
        return false;
      }
      if($("#start-date").val()!=''){
        var from_date = moment($("#start-date").val()).format("YYYY-MM-DD");
      }else{
        var from_date = '';
      }
      if($("#end-date").val()!=''){
        var to_date = moment($("#end-date").val()).format("YYYY-MM-DD");
      }else{
        var to_date = '';
      }

      var request = {
        returnTransaction: self.report,
        returnDetails: self.returnDetails,
        from_date : from_date,
        to_date   : to_date,
      };
      api
        .getreturntransactiongroupexport(request)
        .then(function (response) {
          var FileSaver = require("file-saver");
          var blob = new Blob([response], {
            type: "application/xlsx",
          });
          FileSaver.saveAs(
            blob,
            moment().format("MM/DD/YYYY") + "_return_transaction_report.xlsx"
          );
          self.loading = false;
        })
        .catch(function (error) {
          // error(error);
          self.loading = false;
        });
    },

  },
  mounted() {
    var self = this;
    $("#start-date").datepicker({
      format: "mm/dd/yyyy",
      autoclose: true,
      todayHighlight: true,
    });
    $("#end-date").datepicker({
      format: "mm/dd/yyyy",
      autoclose: true,
      todayHighlight: true,
    });
    $("#start-date , #end-date").datepicker("setDate", new Date());
  },
};
</script>


