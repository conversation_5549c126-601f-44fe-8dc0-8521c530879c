<template>
<div>
  <div v-if="loading">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px;">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Manual Banking Returns</h3>
                </div>

                <div class="card-body">
                  <div class="row">
                  <div class="col-md-6">
                    <div class="form-group">
                      <input
                        class="start-date form-control"
                        placeholder="Start Date"
                        id="start-date"
                        onkeydown="return false"
                        autocomplete="off"
                      />
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-group">
                      <input
                        class="end-date form-control"
                        placeholder="End Date"
                        id="end-date"
                        onkeydown="return false"
                        autocomplete="off"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div class="card-footer">
                <button
                  type="button"
                  class="btn btn-success"
                  id="generateBtn"
                  @click="generateReport()"
                >
                  Generate
                </button>
                <button
                  type="button"
                  @click="exportReport()"
                  class="btn btn-danger ml-10"
                >
                  Export <i
                    class="fa fa-download ml-10"
                    aria-hidden="true"
                  ></i>
                </button>
              </div>
                <!-- /.card-header -->
                <div class="card-body">
                  <div class="row">
                  <div class="col-12">
                    <b-table-simple
                      responsive
                      show-empty
                      bordered
                      sticky-header="800px"
                    >
                      <b-thead head-variant="light">
                        <b-tr>
                          <b-th class="text-center">Consumer Name</b-th>
                          <b-th class="text-center">Phone</b-th>
                          <b-th class="text-center">Email</b-th>
                          <b-th class="text-center">User Type</b-th>
                          <b-th class="text-center">Local Transaction Date</b-th>
                          <b-th class="text-center">Spending Limit (At the time of Purchase)</b-th>
                          <b-th class="text-center">Microbilt code</b-th>
                          <b-th class="text-center">Amount</b-th>
                          <b-th class="text-center">Store</b-th>
                          <b-th class="text-center">Return Code</b-th>
                        </b-tr>
                      </b-thead>
                      <b-tbody v-for="(row, index) in report" :key="index">
                        <b-tr>
                          <b-td class="text-left text-gray">{{row.consumer_name}}</b-td>
                          <b-td class="text-left text-gray">{{row.phone}}</b-td>
                          <b-td class="text-left text-gray">{{row.email}}</b-td>
                          <b-td class="text-left text-gray">{{row.user_type}}</b-td>
                          <b-td class="text-center text-gray">{{row.local_transaction_time}}<br/>({{row.timezone_name}})</b-td>
                          <b-td class="text-right text-gray">{{row.spending_limit}}</b-td>
                          <b-td class="text-center text-gray">{{row.response_code}}</b-td>
                          <b-td class="text-right text-gray">{{row.amount}}</b-td>
                          <b-td class="text-left text-gray">{{row.retailer}}</b-td>
                          <b-td class="text-left text-gray">{{row.reason_code}}</b-td>
                        </b-tr>
                      </b-tbody>
                    </b-table-simple>
                  </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
    
  </div>
</div>
</template>
<script>
import api from "@/api/reports.js";
import moment from "moment";
import { validationMixin } from "vuelidate";
import { required, minLength } from "vuelidate/lib/validators";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "../CustomLoader/CanPayLoader.vue"
export default {
  mixins: [validationMixin],
  data() {
    return {
      loading: false,
      report: [],
    };
  },
  components: {
    HourGlass,
    CanPayLoader
  },
  created() {
  },
  methods: {
    // API call to generate the report
    generateReport() {
      var self = this;
      if($("#end-date").val() == '' &&  $("#start-date").val() == ''){
        error("Please select Start Date & End Date then Generate.");
        return false;
      }
      if (
        moment($("#start-date").val()).format("YYYY-MM-DD") >
        moment().format("YYYY-MM-DD") && $("#start-date").val()!= ''
      ) {
        error("Start date cannot be from future.");
        return false;
      }
      if (
        moment($("#end-date").val()).format("YYYY-MM-DD") >
        moment().format("YYYY-MM-DD") && $("#end-date").val()!= ''
      ) {
        error("End date cannot be from future.");
        return false;
      }
      if($("#start-date").val()!=''){
        var from_date = moment($("#start-date").val()).format("YYYY-MM-DD");
      }else{
        var from_date = '';
      }
      if($("#end-date").val()!=''){
        var to_date = moment($("#end-date").val()).format("YYYY-MM-DD");
      }else{
        var to_date = '';
      }
      self.report = [];
      var request = {
        from_date: from_date,
        to_date: to_date
      };
      if(request.from_date > request.to_date){
        error("To Date cannot be greater than From date");
        return false;
      }
      self.loading = true;
      api
        .generateManualBankingReturns(request)
        .then(function (response) {
          if (response.code == 200) {
            self.report = response.data;
            if(self.report.length > 0){
              self.loading = false;
            }else {
              error("No records found!");
              self.loading = false;
            }
          } else {
            error(response.message);
            self.loading = false;
          }
        })
        .catch(function (error) {
          // error(error);
          self.loading = false;
        });
    },

    // exports the report
    exportReport() {
      var self = this;
      self.loading = true;
      if($("#end-date").val() == '' &&  $("#start-date").val() == ''){
        error("Please select Start Date & End Date then Generate.");
        return false;
      }
      if($("#start-date").val()!=''){
        var from_date = moment($("#start-date").val()).format("YYYY-MM-DD");
      }else{
        var from_date = '';
      }
      if($("#end-date").val()!=''){
        var to_date = moment($("#end-date").val()).format("YYYY-MM-DD");
      }else{
        var to_date = '';
      }

      var request = {
        from_date : from_date,
        to_date   : to_date,
      };
      api
        .getManualBankingReturnsExport(request)
        .then(function (response) {
          var FileSaver = require("file-saver");
          var blob = new Blob([response], {
            type: "application/xlsx",
          });
          FileSaver.saveAs(
            blob,
            moment().format("MM/DD/YYYY") + "_manual_banking_returns_report.xlsx"
          );
          self.loading = false;
        })
        .catch(function (error) {
          // error(error);
          self.loading = false;
        });
    },
    dateDiff(){
      if ($("#start-date").val() != "") {
        var from_date = moment($("#start-date").val()).format("YYYY-MM-DD");
      } else {
        var from_date = "";
      }
      if ($("#end-date").val() != "") {
        var to_date = moment($("#end-date").val()).format("YYYY-MM-DD");
      } else {
        var to_date = "";
      }
      if(from_date!='' && to_date!=''){
        //calculate the date Difference 
        var date1 = new Date(from_date);
        var date2 = new Date(to_date);
        // To calculate the time difference of two dates
        var Difference_In_Time = date2.getTime() - date1.getTime();
          
        // To calculate the no. of days between two dates
        var Difference_In_Days = Difference_In_Time / (1000 * 3600 * 24);
        
        if(Difference_In_Days > 90){
          $("#generateBtn").prop('disabled', true);
        }else{
          $("#generateBtn").prop('disabled', false);
        }
      }
    },
  },
  mounted() {
    var self = this;
    $("#start-date").datepicker({
      format: "mm/dd/yyyy",
      autoclose: true,
      todayHighlight: true,
    }).on('changeDate', function (ev) {
        self.dateDiff();
    });
    $("#end-date").datepicker({
      format: "mm/dd/yyyy",
      autoclose: true,
      todayHighlight: true,
    }).on('changeDate', function (ev) {
        self.dateDiff();
    });
    $("#start-date , #end-date").datepicker("setDate", new Date());
  },
};
</script>


