APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost
MIX_API_URL=https://dev1-api.canpay.rivethammer.com
MIX_REWARD_WHEEL_APP_URL=http://127.0.0.1:8082

# AWS CloudFront URL:
MIX_AWS_CLOUDFRONT_URL=https://dlfp8s73xlek0.cloudfront.net/logos/

LOG_CHANNEL=daily

# Database Configuration
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=canpay
DB_USERNAME=root
DB_PASSWORD=root

# Database Configuration for Replica
DB_CONNECTION_RO=mysql_ro
DB_HOST_RO=127.0.0.1
DB_PORT_RO=3307
DB_DATABASE_RO=canpay_production_replica
DB_USERNAME_RO=root
DB_PASSWORD_RO=root

# Database Configuration for Reward Wheel
DB_CONNECTION_REWARD_WHEEL=mysql_reward_wheel
DB_HOST_REWARD_WHEEL=127.0.0.1
DB_PORT_REWARD_WHEEL=3306
DB_DATABASE_REWARD_WHEEL=canpay_reward_wheel
DB_USERNAME_REWARD_WHEEL=root
DB_PASSWORD_REWARD_WHEEL=root

BROADCAST_DRIVER=log
CACHE_DRIVER=file
QUEUE_CONNECTION=sync
SESSION_DRIVER=cookie
SESSION_LIFETIME=120
SESSION_SECURE_COOKIE=true

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# Mail Configuration
DEFAULT_MAILER=sendgrid

DEFAULT_MAIL_HOST=smtp.sendgrid.net
DEFAULT_MAIL_PORT=587
DEFAULT_MAIL_USERNAME=xxxxxxxxxxxx
DEFAULT_MAIL_PASSWORD=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
DEFAULT_MAIL_ENCRYPTION=tls
DEFAULT_MAILER_DSN=sendgrid+smtp://${DEFAULT_MAIL_PASSWORD}@default

SECONDARY_MAIL_HOST=smtp.gmail.com
SECONDARY_MAIL_PORT=587
SECONDARY_MAIL_USERNAME=xxxxxxxxxxxxxxxxxx
SECONDARY_MAIL_PASSWORD=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
SECONDARY_MAIL_ENCRYPTION=tls
SECONDARY_MAILER_DSN=ses+smtp://${SECONDARY_MAIL_USERNAME}:${SECONDARY_MAIL_PASSWORD}@default

SEND_MAIL=TRUE

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

# JWT Secret Key
JWT_SECRET=TDljwyUqd9fYP4OX1Iic7GPiX2mzIyGkXuygubL6ro0fu6lB4vkYnXJij8VOwpWn

# Firebase Credentials for Push Notifications and Cloud Firestore
FIREBASE_PUSH_NOTIFICATION_KEY=AAAAuQdSIRk:APA91bFs89UItvjgZOmGyozZojYNo7xoEhRgwIjWX9ZOFUOR9J5PMKPRMlcAB56JK68_GSzjGYn4irgfZV3t14BuRt8483yNNHKmBLdbOzHWgXR_OaV-BaXLdY5OJb7kkhxNRO8ythA6
#FIREBASE_LOGIN_INFORMATION=canpay-2851b-firebase-adminsdk-r697w-619704c7b3.json
# Login info for Development server
FIREBASE_LOGIN_INFORMATION=canpay-dev-b9e31-firebase-adminsdk-y8vp8-eba619441d.json

# Acheck21 Configuration
ACHECK_BASE_URL=https://gateway.acheck21.com
ACHECK_USERNAME=<EMAIL>
ACHECK_PASSWORD=9B6BkvSvgnf6L8T@

#Transaction settlement variables
TRASNACTION_SETTLEMENT_START_TIME=7
TRASNACTION_SETTLEMENT_END_TIME=8
TRASNACTION_SETTLEMENT_TIMEZONE=US/Eastern

#Consumer end URL for mail template purpose
CONSUMER_APP_URL=https://consumer.canpaydebit.com/

# AWS bucket related settings
AWS_ACCESS_KEY_ID=XXXXXXXXX
AWS_SECRET_ACCESS_KEY=XXXXXXXXXXXXXX
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=xxxxxxxxxxxxxxxxxxxx
AWS_BUCKET_MERCHANT_LOGO=xxxxxxxxxxxxxxxxxxx
AWS_SECRET_ID=XXXXXXXXXXXXXXXXXXXXX

# Expiry time must be in minutes
S3_FILE_EXPIRY_TIME=2

#3rd party API environment (possible values "sandbox" or "live")
API_ENVIORNMENT=sandbox

#Brute force attack related variables
MAX_CONSECUTIVE_LOGIN_COUNT=3
#Must be in minutes
LOGIN_OTP_VALIDITY=5

# Validity for temporary password. Must be in Minutes
TEMP_PASSWORD_VALIDITY=30

# Google Places API Key
GOOGLE_PLACES_API_KEY=XXXXXXXXXXXXXXXXXXXXXXXXXXXXX

# Mix Google Places API Key
MIX_GOOGLE_PLACES_API_KEY=XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX

# EC2 Instance Name (Used to differentiate servers from which the logs are coming)
EC2_INSTANEC_NAME=Local-Server

# Transaction not Posted Before Time (Eastern Time)
TRANSACTION_POSTING_BLOCK_END_TIME=8

# Canpay ACHECK21 Submerchant ID
CANPAY_SETTLEMENT_ID=XXXXXXXXXXXXX
CANPAY_FEES_ID=XXXXXXXXXXX
CANPAY_DEPOSIT=XXXXXXXXXXXX

# Canpay Bank Account Details
CANPAY_ACCOUNT_NUMBER=XXXXXXXXXXX
CANPAY_ROUTING_NUMBER=XXXXXXXXXXX
CANPAY_ACCOUNT_TYPE=checking
CANPAY_ACCOUNT_HOLDER_NAME=CanPay

# Finicity Configuration
FINICITY_BASE_URL=https://api.finicity.com
FINICITY_APP_KEY=XXXXXXXXXXXXXXXXXXXXXX
FINICITY_PARTNER_SECRET=XXXXXXXXXXXXXXXXXXXXXX
FINICITY_PARTNER_ID=XXXXXXXXXXXXXXXXXXXXXX
#Possible values can be 'active' and 'testing'
FINICITY_CUSTOMER_TYPE=testing

# BLL API BASE URL and Key
BLL_API_BASE_URL=https://yzub5pnh34.execute-api.us-east-1.amazonaws.com
BLL_API_BASE_KEY=XXXXXXXXXXXXXXXXXXXXXX

#Age limit for Customer DOB
MIX_REGISTRATION_AGE_LIMIT=18

# Public and Private Key Pair for JWT
JWT_PRIVATE_KEY="XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
JWT_PUBLIC_KEY="XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
JWT_ALGO="RS256"

# Active Allow Transaction Time Validity

ACTIVE_ALLOW_TRANSACTION_TIME_VALIDITY = 30

# Purchase Power for Manual Bank Linked Consumer

PURCHASE_POWER_FOR_MANUAL_BANK_LINKED_CONSUMER = 150

# Email address for Unknown return reason email
DAILY_TRANSACTION_DETAILS_EMAIL = "<EMAIL>"

# Cognito Configuration
COGNITO_THRESHOLD=XX
COGNITO_BASE_URL=https://sandbox.cognitohq.com
COGNITO_API_KEY="XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
COGNITO_API_SECRET="XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"

#Forgot password email send validation
#please set value in seconds
SEND_EMAIL_AFTER_1st_ATTEMPT=10
SEND_EMAIL_AFTER_2nd_ATTEMPT=20
SEND_EMAIL_AFTER_3rd_ATTEMPT=30
#total email send count within a duration
SEND_EMAIL_COUNT=5
#email send block duration (Seconds)
SEND_EMAIL_BLOCK_DURATION=120

# ACHECK POSTING
ACHECK_POSTING=false

# Email for Fifth Third User Notification
EMAILS_FOR_FIFTH_THIRD_USER_DETECTED=<EMAIL>,<EMAIL>

# Twilio Configuration
TWILIO_SID=XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
TWILIO_TOKEN=XXXXXXXXXXXXXXXXXXXXXXXXXXXXX
TWILIO_PHONE_NO=+XXXXXXXXXXXXXXXXXXXXX

# Twilio Configuration (Secondary Channel)
SECONDARY_TWILIO_SID=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
SECONDARY_TWILIO_TOKEN=xxxxxxxxxxxxxxxxxxxxxxxxxxxx
SECONDARY_TWILIO_PHONE_NO=+xxxxxxxxxxxxxxxxxxx

COUNTRY_CODE=+91

# Cooldown Time for Resend Notification. Must be in minutes.
RESEND_NOTIFICATION_COOLDOWN_AFTER_FIRST_ATTEMPT=1
RESEND_NOTIFICATION_COOLDOWN_AFTER_SECOND_ATTEMPT=1
RESEND_NOTIFICATION_COOLDOWN_FROM_THIRD_ATTEMPT=3

#3rd party API environment (possible values "sandbox" or "live")
API_ENVIRONMENT=sandbox
#Admin driven transaction Expiry days
ADMIN_DRIVEN_TRANSACTION_EXPIRY_DAYS=7

TIMEONE_PST="America/Los_Angeles"

# Exchange Rate
EXCHANGE_RATE=.0001
MINIMUM_REDEEM_POINTS=100

# Jackpot minimum base value
MIX_PROBABILITY_TEST_MINIMUM_VALUE=1000000

# Jackpot minimum base value
MIX_JACKPOT_MINIMUM_VALUE=100

MIX_ACH_APP_URL=XXXXXXXXXXXXXXXXXXXXXXXXXXXX

# Minimum date for ACH return report
MIX_MIN_ACCEPTED_DATE="10/17/2023"

# Akoya Credentials
AKOYA_PRODUCT_URL=XXXXXXXXXXXXXXXXXXXXXXXXXXXX
AKOYA_IDP_URL=XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
AKOYA_CLIENT_ID=XXXXXXXXXXXXXXXXXXXXXXXXXXX
AKOYA_CLIENT_SECRET=XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
AKOYA_REDIRECT_URL=XXXXXXXXXXXXXXXXXXXXX
AKOYA_STATE=XXXXXXXXXXXXXXXXXX
AKOYA_VERSION=v2

# MX Credentials
MX_BASE_URL=XXXXXXXXXXXXX
MX_CLIENT_ID=XXXXXXXXXXXXXXXXXXXXXXX
MX_API_KEY=XXXXXXXXXXXXXXXXXXXXXXXXXXXXXX

TESTING_MODE=false
CUSTOM_BANK_BALANCE=XXXX

# Max Transaction limit for a Week
MAX_TRANSACTION_LIMIT_WEEKLY_FOR_BANK_LINKED=1050
MAX_TRANSACTION_LIMIT_WEEKLY_FOR_NON_BANK_LINKED=300

# Max Limit per Transaction
MAX_AMOUNT_LIMIT_FOR_SINGLE_TRANSACTION=5000
MIX_MAX_AMOUNT_LIMIT_FOR_SINGLE_TRANSACTION=5000

# Purchase power lower limit max value
PP_LOWER_LIMIT_MAX_VALUE=250
MIX_PP_LOWER_LIMIT_MAX_VALUE=250
FIREBASE_PROJECT_ID=XXXXXXXXXX
FIREBASE_API_KEY=XXXXXXXXXXXXXXXXXX

# mx date difference
MX_FAILURE_DATE_DIFFERENCE=14
# Maximum date range for return transaction
MIX_API_RETURN_TRASNACTION_MAX_DIFFERENCE=30

MIX_API_FIREBASE_API="XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
MIX_API_AUTO_DOMAIN="XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
MIX_API_DATABASE_URL="XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
MIX_API_PROJECT_ID="XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
MIX_API_STORAGE_BUCKET="XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
MIX_API_MESSAGING_SENDERID="XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
MIX_API_APPID="XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
# Time is in Milli seconds
MIX_API_CONSUMER_REFRESH_BALANCE_TIMEOUT=10000
# Registration session validity in days
SESSION_VALIDITY=7

# V1 transaction cutoff date
MIX_V1_TRANSACTION_CUTOFF_DATE="2021-06-30"

# Custom purchase power update time for v1 consumers (Eastern Time)
CUSTOM_PURCHASE_POWER_UPDATE_TIME=5

VUE_APP_CONSUMER_APP_URL=XXXXX
