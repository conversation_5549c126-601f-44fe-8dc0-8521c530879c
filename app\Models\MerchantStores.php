<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
class MerchantStores extends Model
{

    protected $table = 'merchant_stores';


    /**
     * __construct
     * This function will insert an alphanumeric 32 character unique id as primary key in the table whenever a data get inserted
     * @param  mixed $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        $this->id = generateUUID();

        parent::__construct($attributes);
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'merchant_id',
        'store_id',
        'retailer',
        'lat',
        'long',
        'address',
        'zip',
        'contact_no',
        'email',
        'transaction_type_id',
        'location_type',
        'city',
        'state',
        'country',
        'website_address',
        'average_monthly_sales',
        'average_ticket_per_sale',
        'pos_provider',
        'terminals_needed',
        'multi_tablet_charging_stand',
        'hardware_shipped',
        'merchant_daily_velocity_limit',
        'is_ecommerce',
        'ecommerce_admin_driven',
        'pos_employee_login'
    ];
    public $timestamps = true;
    public $incrementing = false;
    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
}
