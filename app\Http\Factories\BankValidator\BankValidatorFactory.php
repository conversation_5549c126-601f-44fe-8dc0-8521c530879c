<?php

namespace App\Http\Factories\BankValidator;

use App\Http\Clients\FinicityHttpClient;
use App\Models\BankAccountInfo;
use App\Models\OtpAccessToken;
use App\Models\UserValidationCredentials;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 *
 * @package App\Http\Factories\IdValidator
 */
class BankValidatorFactory implements BankValidatorInterface
{

    private $finicity = null;

    public function __construct()
    {
        $this->finicity = new FinicityHttpClient();
    }

    /**
     * Getting finicity request headers based on type of API
     * returns header array
     */
    public function getFinicityHeaders($params)
    {
        if ($params['type'] == FINICITY_AUTHENTICATION) {
            $headers = array(
                'Finicity-App-Key' => config('app.finicity_app_key'),
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            );
        } else {
            $access_token = $this->getAccessToken($params);
            $headers = array(
                'Finicity-App-Key' => config('app.finicity_app_key'),
                'Finicity-App-Token' => $access_token,
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            );
        }
        return $headers;

    }

    /**
     * Validates bank
     *
     * @return mixed
     */
    public function getAccessToken($params)
    {
        //fetch the existing token from the table and check whether its valid or not
        DB::beginTransaction();
        try {
            $result = OtpAccessToken::where('type', 'finicity')
                ->orderBy('created_at', 'DESC')
                ->first();
            //checking if it is valid and getting the token from DB
            $current_datetime = Carbon::now();
            if (!empty($result) && ($result['value'] != null) && $current_datetime->lte(Carbon::parse($result['expiration_datetime']))) {
                $token = $result['value'];
            } else {
                //calling authentication api then getting the token and storing it into DB for future use
                $params['type'] = FINICITY_AUTHENTICATION;
                $request['headers'] = $this->getFinicityHeaders($params);
                $request['body'] = $this->getFinicityRequestBody($params);
                $request['phoneNo'] = $params['phoneNo']; // phone number required to store in database for api log
                $request['session_id'] = isset($params['session_id']) ? $params['session_id'] : null;
                $response = $this->finicity->getAccessToken($request);
                $json_decoded = json_decode($response, true);
                $token = $json_decoded['token'];
                $otp_data = array(
                    'phone_no' => $params['phoneNo'],
                    'value' => $token,
                    'type' => "finicity",
                    'expiration_datetime' => $current_datetime->addHours(2),
                    'verified' => 1,
                );
                OtpAccessToken::create($otp_data);
                DB::commit();
            }
            return $token;
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while fetching otp access token data.", [EXCEPTION => $e]);
            DB::rollback();
            $message = trans('message.db_transaction_fail');
            return renderResponse(FAIL, $message, null);
        }
    }

    /**
     * Getting finicity request body based on type of API
     * returns json body
     */
    public function getFinicityRequestBody($params)
    {
        switch ($params['type']) {
            case FINICITY_AUTHENTICATION:
                $body = array(
                    'partnerId' => config('app.finicity_partner_id'),
                    'partnerSecret' => config('app.finicity_partner_secret'),
                );
                break;
            case FINICITY_CREATE_CUSTOMER:
                $arr = explode("@", $params['email'], 2);
                $username = $arr[0] . strtotime(Carbon::now());
                $body = array(
                    'username' => $username,
                    'firstName' => $params['firstName'],
                    'lastName' => $params['lastName'],
                );
                break;
            case FINICITY_CREATE_CONSUMER:
                $dob = explode('-', $params['dateOfBirth']);
                $birthday = array(
                    'year' => $dob[2],
                    'month' => $dob[0],
                    'dayOfMonth' => $dob[1],
                );
                $body = array(
                    'firstName' => $params['firstName'],
                    'lastName' => $params['lastName'],
                    'address' => $params['address'],
                    'city' => $params['city'],
                    'state' => $params['state'],
                    'zip' => $params['zip'],
                    'phone' => $params['phoneNo'],
                    'ssn' => $params['ssn'],
                    'birthday' => $birthday,
                    'email' => $params['email'],
                );
                break;
            case FINICITY_GENERATE_URL:
                $body = array(
                    'partnerId' => config('app.finicity_partner_id'),
                    'customerId' => $params['id']['customer_id'],
                    'experience' => 'default',
                );
                break;
            case FINICITY_GENERATE_FIX_URL:
                $body = array(
                    'partnerId' => config('app.finicity_partner_id'),
                    'customerId' => $params['id']['customer_id'],
                    'institutionLoginId' => $params['institutionLoginId'],
                );
                break;
        }
        return json_encode($body, true);
    }

    public function getConsumerAccountDetails($user)
    {
        // check if user already linked with finicity then changed their bank manually due to return then generate the link using existing cutomer id
        $result = UserValidationCredentials::where('user_id', $user->user_id)->first();
        $params['id']['customer_id'] = $result->finicity_customer_id;
        $params['phoneNo'] = $user->phone;
        $params['email'] = $user->email;
        if (empty($params['id']['customer_id'])) {
            $result = BankAccountInfo::join('status_master', 'user_bank_account_info.status', '=', 'status_master.id')
                ->where('user_id', $user->user_id)
                ->where('status_master.code', BANK_ACTIVE)
                ->first();
            $params['id']['customer_id'] = $result->finicity_id;
        }
        $params['customer_id'] = $params['id']['customer_id'];
        $params['type'] = FINICITY_GET_ACCOUNTS;
        $params['headers'] = $this->getFinicityHeaders($params);
        // get customer accounts with finicity id and update the institution login ids
        $response = $this->finicity->getCustomerAccounts($params);
        $customer_accounts = json_decode($response, true);
        return $customer_accounts['accounts'];
    }

    /**
     * Get accounts by consumer finicity id and Institution Login Id for a consumer
     *
     */
    public function getConsumerRefreshBalance($user)
    {
        $bank_active = getStatus(BANK_ACTIVE);
        $result = BankAccountInfo::join('status_master', 'user_bank_account_info.status', '=', 'status_master.id')
            ->whereRaw('finicity_id IS NOT NULL')
            ->where('user_id', $user->user_id)
            ->where('user_bank_account_info.status', $bank_active)
            ->first();
        if (empty($result)) {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer with ID: " . $user->user_id . " doesn't have any active bank account. Hence returning error.");
            return null;
        }
        $params['customer_id'] = $result['finicity_id'];
        $params['institution_id'] = $result['institution_id'];
        $params['institution_login_id'] = $result['institution_login_id'];
        if (empty($result['finicity_id'])) {
            $result = UserValidationCredentials::where('phone', $user->phone)
                ->orderBy('created_at', 'DESC')
                ->first();
            $params['customer_id'] = $result['finicity_customer_id'];
        }
        $params['type'] = FINICITY_GET_ACCOUNTS;
        $params['phoneNo'] = $user->phone;
        if (empty($result['institution_login_id'])) {
            $params['institution_login_id'] = $this->getFinicityInstituionLoginId($params);
        }
        try {
            if ($params['institution_login_id'] != '') {
                $params['headers'] = $this->getFinicityHeaders($params);
                //get account details by calling finicity api
                $response = $this->finicity->getConsumerRefreshBalance($params);
                $customer_accounts = json_decode($response, true);
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Account Details for Consumer ID : " . $params['customer_id'] . " and Institution Login ID : " . $params['institution_login_id'] . " is :" . json_encode($customer_accounts, true));
                return $customer_accounts;
            } else {
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Error:1002. There is a mismatch with the FI Id. Please ask the consumer to relink their bank again for Consumer ID : " . $params['customer_id']);
                return FI_ID_ERROR;
            }
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception caused while trying to form the refined bank account listing.", [EXCEPTION => $e]);
            return 0;
        }

    }

    public function getFinicityInstituionLoginId($params)
    {
        $params['type'] = FINICITY_GET_ACCOUNTS;
        $params['headers'] = $this->getFinicityHeaders($params);
        // get customer accounts with finicity id and update the institution login ids
        $response = $this->finicity->getCustomerAccounts($params);
        $customer_accounts = json_decode($response, true);
        $params['institutionLoginId'] = null;
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Getting the institution login id based on the institution id: " . $params['institution_id']);
        if (array_key_exists("accounts", $customer_accounts)) {
            foreach ($customer_accounts['accounts'] as $customer_account) {
                if (strcmp($customer_account['institutionId'], $params['institution_id']) == 0) {
                    $params['institutionLoginId'] = $customer_account['institutionLoginId'];
                    break;
                }
            }
        }
        return $params['institutionLoginId'];
    }

    /**
     *
     * Gets consumer's active account owner information from finicity
     */
    public function getAccountOwnerInfo($bank_details)
    {
        try {
            $params['customer_id'] = $bank_details->finicity_id;
            $params['account_id'] = $bank_details->account_id;
            $params['phoneNo'] = $bank_details->phone;
            $params['consumer_id'] = $bank_details->user_id;
            $params['type'] = FINICITY_GET_ACCOUNTS;
            $params['headers'] = $this->getFinicityHeaders($params);
            //get account owner details calling finicity api
            $response = $this->finicity->getAccountOwnerInfo($params);
            $res = json_decode($response, true);
            return $res;
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured while trying to fetch bank account owner information for user:  " . $bank_details->user_id . ".", [EXCEPTION => $e]);
        }
    }

    /**
     * Refresh Customer Accounts
     *
     */
    public function getConsumerRefreshAccountsBalance($user)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Refresh Customer Accounts User ID : " . $user->user_id);

        $result = BankAccountInfo::join('status_master', 'user_bank_account_info.status', '=', 'status_master.id')
            ->where('status_master.code', BANK_ACTIVE)
            ->whereRaw('finicity_id IS NOT NULL')
            ->where('user_id', $user->user_id)
            ->first();
        $params['customer_id'] = $result['finicity_id'];
        if (empty($result['finicity_id'])) {
            $result = UserValidationCredentials::where('phone', $user->phone)
                ->orderBy('created_at', 'DESC')
                ->first();
            $params['customer_id'] = $result['finicity_customer_id'];
        }
        $params['type'] = FINICITY_GET_ACCOUNTS;
        $params['phoneNo'] = $user->phone;

        try {
            $params['headers'] = $this->getFinicityHeaders($params);
            //get account details by calling finicity api
            $response = $this->finicity->getConsumerRefreshAccountsBalance($params);
            $customer_accounts = json_decode($response, true);
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Account Details for Consumer ID : " . $params['customer_id'] . " is :" . json_encode($customer_accounts, true));
            return $customer_accounts;
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception caused while trying to Refresh Customer Accounts.", [EXCEPTION => $e]);
            return 0;
        }

    }
    
    /**
     * This function returns consumer linked bank account name
     */
    public function getConsumerBankName($params)
    {
        $params['type'] = FINICITY_GET_ACCOUNTS;
        $params['headers'] = $this->getFinicityHeaders($params);
        //get the associated institution id and call the api to get the details
        $response = $this->finicity->getInstitution($params);
        $response = json_decode($response, true);
        return $response['institution']['name'];
    }
    
    public function deleteConsumerAccounts($account, $institution_login_id)
    {
        $result = BankAccountInfo::join('status_master', 'user_bank_account_info.status', '=', 'status_master.id')
            ->where('user_bank_account_info.id', $account->id)
            ->first();
        $params['customer_id'] = $result['finicity_id'];
        $params['institution_id'] = $result['institution_id'];
        $params['institution_login_id'] = $institution_login_id;
        $params['type'] = FINICITY_GET_ACCOUNTS;
        $params['phoneNo'] = $account->phone;
        try {
            $params['headers'] = $this->getFinicityHeaders($params);
            $this->finicity->deleteConsumerAccounts($params);
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Bank account removed with finicity customer id: " . $params['customer_id']);
            return true;
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception caused while trying to remove bank acocunt.", [EXCEPTION => $e]);
            return 0;
        }
    }
}
