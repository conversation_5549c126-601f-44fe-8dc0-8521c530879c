<?php

namespace App\Http\Controllers;

use App\Imports\MerchantMasterImport;
use App\Imports\MerchantStoresImport;
use App\Imports\TransactionImport;
use App\Imports\UserCardImport;
use App\Imports\UsersImport;
use App\Imports\UserStatusImport;
use App\Imports\VoidedTransactionImport;
use App\Models\ConsumerCardMap;
use App\Models\ConsumerMigration;
use App\Models\DuplicateUser;
use App\Models\MigrationLog;
use App\Models\User;
use App\Models\UserRole;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;

class DataMigrationController extends Controller
{
    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    /**
     * importConsumersFromExcel
     * This function is used to import consumers with their card number in V2 system from V1
     * @return void
     */
    public function importConsumersFromExcel()
    {
        DB::beginTransaction();
        try {
            ini_set('max_execution_time', 20000);
            $mailing_list = $this->request->file('mailing_list');
            $card_list = $this->request->file('card_list');
            $enrollemnt_list = $this->request->file('enrollemnt_list');

            //check if the sheets are being uploaded for the first time or not
            if (ConsumerMigration::all()->isEmpty() && (empty($mailing_list) || empty($card_list) || empty($enrollemnt_list))) {
                $message = "Please upload all the data sheets in order to migrate to v2.";
                return renderResponse(FAIL, $message, null);
            }
            //====================Code Segment For Parsing Mailing List===============================
            if (!empty($mailing_list)) {
                Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Mailing list excel import started with excel name : " . $mailing_list);
                Excel::import(new UsersImport(), $mailing_list);
                Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Mailing list excel import completed.");
            }
            //====================Code Segment For Parsing Card List By Email==========================
            if (!empty($card_list)) {
                Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Card list by email excel import started with excel name : " . $card_list);
                Excel::import(new UserCardImport(), $card_list);
                Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Card list by email excel import completed.");
            }
            //====================Code Segment For Parsing Enrollement Data Sheet======================
            if (!empty($enrollemnt_list)) {
                Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Enrollment excel import started with excel name : " . $enrollemnt_list);
                Excel::import(new UserStatusImport(), $enrollemnt_list);
                Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Enrollment excel import completed.");
            }

            // Data migration from consumer migration table to User table started
            Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Data Migration started at: " . date('m-d-Y H:i:s'));

            $migrated_data = $this->_migrateData();

            // Insertion in Migraion Log table Started
            $imported_data_count = explode('|', $migrated_data);
            $log = new MigrationLog();
            $log->type = CONSUMER;
            $log->summary = 'Success';
            $log->actual_data_imported = $imported_data_count[0];
            $log->duplicate_date_imported = $imported_data_count[1];
            $log->migrated_data_updated = $imported_data_count[2];
            $log->uploaded_by = Auth::user()->user_id;
            $log->save();

            DB::commit();

            $message = "Total " . $imported_data_count[0] . " records imported without duplication. Total " . $imported_data_count[2] . " migrated records updated. Total " . $imported_data_count[1] . " duplicate records found.";
            return renderResponse(SUCCESS, $message, null);
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured while importing consumer data.", [EXCEPTION => $e]);
            DB::rollback();
            // Insertion in Migraion Log table Started
            $log = new MigrationLog();
            $log->type = CONSUMER;
            $log->summary = 'Failed';
            $log->actual_data_imported = 0;
            $log->duplicate_date_imported = 0;
            $log->migrated_data_updated = 0;
            $log->uploaded_by = Auth::user()->user_id;
            $log->save();
            $message = "There was some problem while trying to import consumer data. Processed data for current period has been rolled back.";
            return renderResponse(FAIL, $message, null);
        }
    }

    /**
     * importMerchantsFromExcel
     * This function is used to import merchants with their stores in V2 system from V1
     * @return void
     */
    public function importMerchantsFromExcel()
    {
        DB::beginTransaction();
        try {
            ini_set('max_execution_time', 20000);
            $store_list_by_merchant_group = $this->request->file('store_list_by_merchant_group');
            $canpay_transaction_fees = $this->request->file('canpay_transaction_fees');

            //check if both the sheets are being uploaded or not
            if (empty($store_list_by_merchant_group) && empty($canpay_transaction_fees)) {
                $message = "Please upload all the data sheets in order to migrate to v2.";
                return renderResponse(FAIL, $message, null);
            }
            //====================Code Segment For Parsing Canpay Transaction Fees==========================
            if (!empty($canpay_transaction_fees)) {
                Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Canpay Transaction Fees excel upload done and import started");
                Excel::import(new MerchantMasterImport(), $canpay_transaction_fees);
                Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Canpay Transaction Fees excel import completed");
            }

            //====================Code Segment For Parsing Store List by Merchant Group===============================
            if (!empty($store_list_by_merchant_group)) {
                Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Store List by Merchant Group excel upload done and import started");
                $import = new MerchantStoresImport();
                Excel::import($import, $store_list_by_merchant_group);
                Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Store List by Merchant Group excel import completed");
            }

            // Insertion in Migraion Log table Started
            $row_count = explode('|', $import->getRowCount());
            $log = new MigrationLog();
            $log->type = MERCHANT;
            $log->summary = 'Success';
            $log->actual_data_imported = $row_count[0];
            $log->migrated_data_updated = $row_count[1];
            $log->uploaded_by = Auth::user()->user_id;
            $log->save();

            DB::commit();

            $message = "Total " . $row_count[0] . " stores imported and " . $row_count[1] . " stores updated successfully.";
            return renderResponse(SUCCESS, $message, null);
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured while importing merchant store data.", [EXCEPTION => $e]);
            DB::rollback();
            $message = "There was some problem while trying to import merchant store data. Processed data for current period has been rolled back.";
            return renderResponse(FAIL, $message, null);
        }
    }

    /**
     * importTransactionsFromExcel
     * This function is used to import transactions for consumers in V2 system from V1
     * @return void
     */
    public function importTransactionsFromExcel()
    {
        DB::beginTransaction();
        try {
            ini_set('max_execution_time', 6000);
            $canpay_transaction_summary = $this->request->file('canpay_transaction_summary');

            //check if both the sheets are being uploaded or not
            if (empty($canpay_transaction_summary)) {
                $message = "Please upload the data sheets in order to migrate to v2.";
                return renderResponse(FAIL, $message, null);
            }
            //====================Code Segment For Parsing Canpay Transaction Fees==========================
            if (!empty($canpay_transaction_summary)) {
                Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Canpay Transaction Summary excel upload done and import started");
                $import = new TransactionImport();
                Excel::import($import, $canpay_transaction_summary);
                Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Canpay Transaction Summary excel import completed");
            }

            // Insertion in Migraion Log table Started
            $count = explode('|', $import->getRowCount());
            $log = new MigrationLog();
            $log->type = 'Transaction';
            $log->summary = 'Success';
            $log->actual_data_imported = $count[0];
            $log->duplicate_date_imported = $count[1];
            $log->migrated_data_updated = $count[2];
            $log->uploaded_by = Auth::user()->user_id;
            $log->save();

            DB::commit();

            $message = "Total " . $count[0] . " transactions imported successfully and " . $count[1] . " duplicate records found and " . $count[2] . " updated records found.";
            return renderResponse(SUCCESS, $message, null);
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured while importing transaction data.", [EXCEPTION => $e]);
            DB::rollback();
            $message = "There was some problem while trying to import transaction data. Processed data for current period has been rolled back.";
            return renderResponse(FAIL, $message, null);
        }
    }

    /**
     * importVoidedTransactionsFromExcel
     * This function is used to import voided transactions for consumers in V2 system from V1
     * @return void
     */
    public function importVoidedTransactionsFromExcel()
    {
        DB::beginTransaction();
        try {
            ini_set('max_execution_time', 6000);
            $voided_transaction = $this->request->file('voided_transaction');

            //check if both the sheets are being uploaded or not
            if (empty($voided_transaction)) {
                $message = "Please upload the data sheets in order to migrate to v2.";
                return renderResponse(FAIL, $message, null);
            }
            $sheet_names = '';
            //====================Code Segment For Parsing Canpay Transaction Fees==========================
            if (!empty($voided_transaction)) {
                $params = array(
                    'csv' => $voided_transaction,
                    'name' => 'VoidedTransaction_' . Carbon::today()->toDateString() . '.' . $voided_transaction->getClientOriginalExtension(),
                );
                $sheet_names .= $params['name'];
                Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Voided Transaction excel upload done and import started");
                $import = new VoidedTransactionImport();
                Excel::import($import, $voided_transaction);
                Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Voided Transaction excel import completed");
            }

            // Insertion in Migraion Log table Started
            $log = new MigrationLog();
            $log->type = 'Voided Transaction';
            $log->summary = 'Success';
            $log->actual_data_imported = $import->getRowCount();
            $log->uploaded_by = Auth::user()->user_id;
            $log->save();

            DB::commit();

            $message = "Total " . $import->getRowCount() . " voided transactions imported successfully.";
            return renderResponse(SUCCESS, $message, null);
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured while importing transaction data.", [EXCEPTION => $e]);
            DB::rollback();
            $message = "There was some problem while trying to import voided transaction data. Processed data for current period has been rolled back.";
            return renderResponse(FAIL, $message, null);
        }
    }

    /**
     * _migrateData
     * This function is used to migrate the consumer data to User table or in Duplicate User table(if found duplicate). It will also update the migrated users data in both the tables.
     * @return void
     */
    private function _migrateData()
    {
        // Get all the consumers data that are ready for migration
        $applicableForMigrationData = ConsumerMigration::where('migrated', 0)->where('card_number', '!=', null)->where('status', '!=', null)->get();

        if (!empty($applicableForMigrationData)) {
            $role_details = UserRole::where('role_name', CONSUMER)->first(); // Fetching Role ID with respect to User Type
            $actual_data_imported = 0;
            $duplicate_data_imported = 0;
            foreach ($applicableForMigrationData as $data) {
                // Check if the user has duplicate email
                $checkEmailExists = User::where('email', $data->email)->first();
                if (empty($checkEmailExists)) {
                    // Check if the phone number already exists in User table
                    $checkPhoneEXists = User::where('phone', $data->phone)->first();

                    if (empty($checkPhoneEXists)) { // If not exists then insert it in User Table
                        // Insertion in user table begins
                        $user = new User();
                        $user->first_name = $data->first_name;
                        $user->last_name = $data->last_name;
                        $user->email = $data->email;
                        $user->phone = $data->phone;
                        $user->street_address = $data->street_address;
                        $user->city = $data->city;
                        $user->state = $data->state;
                        $user->zipcode = $data->zipcode;
                        $user->role_id = $role_details->role_id;
                        $user->status = $data->status;
                        $user->existing_user = 1;
                        $user->save();

                        // Card number mapping started for consumer
                        $card_map = new ConsumerCardMap();
                        $card_map->user_id = $user->user_id;
                        $card_map->card_number = $data->card_number;
                        $card_map->save();

                        Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Data Migrated in user table for User Email: " . $data->email);

                        $actual_data_imported++;
                    } else { //If exists then move the data to duplicate user table
                        $user = new DuplicateUser();
                        $user->first_name = $data->first_name;
                        $user->last_name = $data->last_name;
                        $user->email = $data->email;
                        $user->phone = $data->phone;
                        $user->street_address = $data->street_address;
                        $user->city = $data->city;
                        $user->state = $data->state;
                        $user->zipcode = $data->zipcode;
                        $user->role_id = $role_details->role_id;
                        $user->status = $data->status;
                        $user->card_number = $data->card_number;
                        $user->existing_user = 1;
                        $user->save();

                        Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Data Migrated in duplicate user tabel due to duplicate phone number table for User Email: " . $data->email);

                        $duplicate_data_imported++;
                    }

                    // Updating the consumer migration table with migrated flag to 1
                    $consumer_migration = ConsumerMigration::find($data->id);
                    $consumer_migration->updated = 0;
                    $consumer_migration->migrated = 1;
                    $consumer_migration->save();
                }
            }
        }

        // Now check for any update for the consumers data those have been migrated
        $updated_data = ConsumerMigration::where(['migrated' => 1, 'updated' => 1])->get();
        if (!empty($updated_data)) {
            $migrated_data_updated = 0;
            foreach ($updated_data as $updated) {
                $user = User::where('email', $updated->email)->first();
                if (!empty($user)) { // If the user got found in User table then proceed for the update
                    // Check if the user is already registered in V2 system
                    if ($user->password == '') { // If the password is empty then the user is not registered in V2. So proceed with the updation process
                        $user->first_name = $updated->first_name;
                        $user->last_name = $updated->last_name;
                        $user->phone = $updated->phone;
                        $user->street_address = $updated->street_address;
                        $user->city = $updated->city;
                        $user->state = $updated->state;
                        $user->zipcode = $updated->zipcode;
                        $user->status = $updated->status;
                        $user->save();

                        // Updating the Card Map table
                        $card_map = ConsumerCardMap::where('user_id', $user->user_id)->first();
                        $card_map->card_number = $updated->card_number;
                        $card_map->save();

                        $migrated_data_updated++;

                        Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Migrated data updated in user table for User Email: " . $updated->email);
                    } else { // As the password is not empty that means the user is already registered in V2. So, simply skipping the row and writing a log for record.
                        Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Migrated data updation skipped in user table as the user is already registered in V2 system for User Email: " . $updated->email);
                    }
                } else { //search in duplicate user table for the user as it is missing from user table but has the migrated flag 1 in the consumer migration table
                    $user = DuplicateUser::where('email', $updated->email)->first();
                    $user->first_name = $updated->first_name;
                    $user->last_name = $updated->last_name;
                    $user->phone = $updated->phone;
                    $user->street_address = $updated->street_address;
                    $user->city = $updated->city;
                    $user->state = $updated->state;
                    $user->zipcode = $updated->zipcode;
                    $user->status = $updated->status;
                    $user->card_number = $updated->card_number;
                    $user->save();

                    $migrated_data_updated++;

                    Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Migrated data updated in duplicate user table for User Email: " . $updated->email);
                }

                // Updating the consumer migration table with updated flag to 0 as the update has been done
                $consumer_migration = ConsumerMigration::find($updated->id);
                $consumer_migration->updated = 0;
                $consumer_migration->save();
            }
        }
        Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Data Migration completed at: " . date('m-d-Y H:i:s'));

        return $actual_data_imported . "|" . $duplicate_data_imported . "|" . $migrated_data_updated;
    }

    /**
     * getMigrationLog
     * Listing for Migration Log for all types of data migration in Datatable
     * @param  mixed $request
     * @return void
     */
    public function getMigrationLog(Request $request)
    {
        // Columns defined for Sorting
        $columns = array(
            0 => 'migration_log.summary',
            1 => 'migration_log.actual_data_imported',
            2 => 'migration_log.duplicate_date_imported',
            3 => 'migration_log.migrated_data_updated',
            4 => 'users.first_name',
            5 => 'migration_log.created_at',
        );
        // Main Query
        $query = MigrationLog::on(MYSQL_RO)->join('users', 'users.user_id', '=', 'migration_log.uploaded_by')->select('migration_log.*', 'users.first_name', 'users.middle_name', 'users.last_name')->where('migration_log.type', $request->get('type'));

        //Count Query
        $queryCount = MigrationLog::on(MYSQL_RO)->join('users', 'users.user_id', '=', 'migration_log.uploaded_by')
            ->selectRaw('COUNT(*) as total_count')
            ->where('migration_log.type', $request->get('type'));

        $totalData = $queryCount->first()->total_count; // Getting total no of rows

        $totalFiltered = $totalData;
        $limit = intval($request->input('length'));
        $start = intval($request->input('start'));
        $order = $columns[$request->input('order.0.column')];
        $query_limit = $query->offset(0)->limit(3);
        $dir = $request->input('order.0.dir');
        if (empty($request->input('search.value')) && empty($order) && empty($dir)) {
            $migration_logs = $query_limit->orderBy('migration_log.created_at', 'DESC')->get();
        } else {
            $migration_logs = $query->offset($start)->limit(intval($limit))->orderBy($order, $dir)->get();
        }

        $data = array();
        if (!empty($migration_logs)) {
            // Creating array to show the values in frontend
            foreach ($migration_logs as $migration_log) {
                $nestedData['summary'] = $migration_log->summary;
                $nestedData['actual_data_imported'] = $migration_log->actual_data_imported;
                $nestedData['duplicate_date_imported'] = $migration_log->duplicate_date_imported;
                $nestedData['migrated_data_updated'] = $migration_log->migrated_data_updated;
                $nestedData['uploaded_by'] = $migration_log->first_name . ' ' . $migration_log->middle_name . ' ' . $migration_log->last_name;
                $nestedData['edit'] = $migration_log->id;
                $nestedData['created_at'] = date('m-d-Y h:i A', strtotime($migration_log->created_at));
                $data[] = $nestedData;
            }
        }
        // Drawing the Datatable
        $json_data = array(
            "draw" => intval($request->input('draw')),
            "recordsTotal" => intval($totalData),
            "recordsFiltered" => intval($totalFiltered),
            "data" => $data,
        );

        Log::info(addslashes(__METHOD__) . "(" . LINE . __LINE__ . ") : Migration Log List fetched successfully");
        echo json_encode($json_data); // Rerurning the data
    }

}
