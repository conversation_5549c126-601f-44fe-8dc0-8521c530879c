<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Events\AfterSheet;
use Illuminate\Support\Facades\Log;

class ReturnTransactionDailyExport implements FromCollection, WithHeadings, ShouldAutoSize, WithEvents
{
    protected $request;

    public function __construct($request)
    {
        $this->request = $request; // Declaring the request variable
    }

    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        $collection_array = $this->request['return_transactions']; // Storing the array received from request

        $returns = array();
        foreach($collection_array as $return){
            $nestedData['name'] = $return->head;
            $nestedData['credit'] = $return->credit;
            $nestedData['debit'] = $return->debit;
            $nestedData['transaction_time'] = $return->transaction_time;
            $nestedData['reason_code'] = $return->reason_code;
            $nestedData['type'] = $return->type;
            $nestedData['represent_count'] = $return->represent_count;
            
            array_push($returns,$nestedData);
        }

        return collect([
            $returns,
        ]);
    }

    public function headings(): array
    {
        $returnArray = array(
            [
                'Name',
                'Credit($)',
                'Debit($)',
                'Transaction Time',
                'Reason Code',
                'Type',
                'Represent Count',
            ],
        );

        return $returnArray;
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event){
                $event->sheet->getStyle('A1:G1')->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);

                //Apply Center Alignment
                $event->sheet->getStyle('A:G')->getAlignment()->applyFromArray(
                    array('horizontal' => 'center')
                );
            },
        ];
    }
}
