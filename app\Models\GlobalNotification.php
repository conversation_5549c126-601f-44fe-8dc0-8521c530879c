<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
class GlobalNotification extends Model
{
    /**
     * __construct
     * This function will insert an alphanumeric 32 character unique id as primary key in the table whenever a data get inserted
     * @param  mixed $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        $this->id = generateUUID();

        parent::__construct($attributes);
    }

    protected $fillable = [
        'notification'
    ];
    public $timestamps = true;
    public $incrementing = false;
}
