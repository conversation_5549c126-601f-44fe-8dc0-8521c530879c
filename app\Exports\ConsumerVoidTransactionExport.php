<?php

namespace App\Exports;

use DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Events\AfterSheet;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;

class ConsumerVoidTransactionExport implements FromCollection, WithHeadings, ShouldAutoSize, WithEvents, WithColumnFormatting
{
    protected $request;

    public function __construct($request)
    {
        $this->request = $request; // Declaring the request variable
    }

    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        $collection_array = $this->request->get('report'); // Storing the array received from request

        $transactions = array();
        foreach($collection_array as $transaction){
            $nestedData['transaction_number'] = $transaction['transaction_number'];
            $nestedData['consumer_name'] = $transaction['consumer_name'];
            $nestedData['merchant_name'] = $transaction['merchant_name'];
            $nestedData['store_name'] = $transaction['store_name'];
            $nestedData['terminal_name'] = $transaction['terminal_name'];
            $nestedData['amount'] = $transaction['amount'];
            $nestedData['tip_amount'] = $transaction['tip_amount'];
            $nestedData['delivery_fee'] = $transaction['delivery_fee'];
            $nestedData['transaction_time'] = str_replace("<br/>"," ",$transaction['transaction_time']);
            $nestedData['status'] = $transaction['status'];

            array_push($transactions,$nestedData);
        }

        return collect([
            $transactions,
        ]);
    }

    public function headings(): array
    {
        $returnArray = array(
            [
                'Transaction Number',
                'Consumer',
                'Merchant',
                'Store',
                'Terminal',
                'Amount ($)',
                'Tip Amount ($)',
                'Delivery Fee ($)',
                'Transaction Time',
                'Status'
            ],
        );

        return $returnArray;
    }

    public function columnFormats(): array
    {
        return [
            'F' => '0.00',
        ];
    }


    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event){
                $event->sheet->getStyle('A1:H1')->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);

                //Apply Center Alignment
                $event->sheet->getStyle('A:H')->getAlignment()->applyFromArray(
                    array('horizontal' => 'center')
                );
            },
        ];
    }
}
