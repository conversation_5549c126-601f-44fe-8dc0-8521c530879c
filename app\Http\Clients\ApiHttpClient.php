<?php
namespace App\Http\Clients;

use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;

/**
 *
 * @package App\Http\Clients
 */
class ApiHttpClient
{
    /**
     * @var Client
     */
    private $client;
    /**
     * Acheck21HttpClient constructor.
     */
    public function __construct()
    {
        $this->client = new Client(['base_uri' => config('app.canpay_api_url')]);
    }
    /**
     * Api gets consumer account balance from CanPay API
     */
    public function getConsumerAccountBalance($consumer_id)
    {
        $params['api'] = '/getConsumerAccountBalance';
        $params['headers'] = [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ];
        $params['body'] = [
            'consumer_id' => $consumer_id
        ];
        try {
            $response = $this->client->post($params['api'], [
                'headers' => $params['headers'],
                'body' => json_encode($params['body']),
            ]);
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Account balance from CanPay API returned response : " . $response->getBody());
            return $response->getBody();
        } catch (\GuzzleHttp\Exception\GuzzleException $ex) {
            Log::error(__METHOD__ . "(Line: " . __LINE__ . ") - " . "Exception while sending request to CanPay API.", ['Exception' => $ex]);
            return $ex;
        }
    }
    /**
     * Ge consumer id image into base64 encoded form
     */
    public function getConsumerIDImage($id)
    {
        $params['api'] = '/getConsumerIDImage';
        $params['headers'] = [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ];
        $params['body'] = [
            'id' => $id
        ];
        try {
            $response = $this->client->post($params['api'], [
                'headers' => $params['headers'],
                'body' => json_encode($params['body']),
            ]);
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Get consumer ID CanPay API returned response");
            return $response->getBody();
        } catch (\GuzzleHttp\Exception\GuzzleException $ex) {
            Log::error(__METHOD__ . "(Line: " . __LINE__ . ") - " . "Exception while sending request to CanPay API.", ['Exception' => $ex]);
            return $ex;
        }
    }

    public function updateConsumerLiteToStandard($consumer_id)
    {
        $token = generateToken();
        $params['api'] = '/admin/update-consumer-lite-to-standard';
        $params['headers'] = [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
            'Authorization' => extractTokenFromHttpResponse($token),
        ];
        $params['body'] = [
            'consumer_id' => $consumer_id
        ];
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Admin has sent a request to update the consumer from Lite to Standard for Consumer ID: ". $consumer_id);
        try {
            $response = $this->client->post($params['api'], [
                'headers' => $params['headers'],
                'body' => json_encode($params['body']),
            ]);
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Lite to standard response : " . $response->getBody());
            return $response->getBody();
        } catch (\GuzzleHttp\Exception\GuzzleException $ex) {
            Log::error(__METHOD__ . "(Line: " . __LINE__ . ") - " . "Exception while sending request to CanPay API.", ['Exception' => $ex]);

        }
    }

}
