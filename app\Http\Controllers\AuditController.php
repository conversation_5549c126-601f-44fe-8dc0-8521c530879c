<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Log;
use OwenIt\Auditing\Models\Audit;

class AuditController extends Controller
{
    /**
     * getAllAudits
     * Listing page for Audit Trails with User Mapping along with Server Side Pagination in Datatable
     * @param  mixed $request
     * @return void
     */
    public function getAllAudits(Request $request)
    {
        // Columns defined for Sorting
        $columns = array(
            0 => 'audits.auditable_type',
            1 => 'audits.event',
            2 => 'users.first_name',
            3 => 'audits.created_at',
        );
        // Main Query
        $query = Audit::on(MYSQL_RO)->leftjoin('users', 'users.user_id', '=', 'audits.user_id')->select('audits.*', 'users.first_name', 'users.middle_name', 'users.last_name');

        //Count Query
        $queryCount = Audit::on(MYSQL_RO)->leftjoin('users', 'users.user_id', '=', 'audits.user_id')
            ->selectRaw('COUNT(*) as total_count');
        $totalData = $queryCount->first()->total_count; // Getting total no of rows

        $totalFiltered = $totalData;
        $limit = intval($request->input('length'));
        $start = intval($request->input('start'));
        $order = $columns[$request->input('order.0.column')];
        $dir = $request->input('order.0.dir');
        if (empty($request->input('search.value')) && empty($order) && empty($dir)) {
            $audits = $query->offset($start)->limit(intval($limit))->orderBy('audits.created_at', 'DESC')->get();
        } else if (empty($request->input('search.value'))) {
            $audits = $query->offset($start)->limit(intval($limit))->orderBy($order, $dir)->get();
        } else {
            $search = $request->input('search.value');
            $search_query = $query->where('audits.id', 'LIKE', "%{$search}%")->orWhere('audits.auditable_type', 'LIKE', "%{$search}%")->orWhere('audits.event', 'LIKE', "%{$search}%")->orWhere('audits.auditable_id', 'LIKE', "%{$search}%")->orWhere('audits.event', 'LIKE', "%{$search}%")->orWhere('audits.old_values', 'LIKE', "%{$search}%")->orWhere('audits.new_values', 'LIKE', "%{$search}%")->orWhereRaw("lower(concat_ws(' ', users.first_name, users.middle_name, users.last_name)) LIKE ? ", ['%' . $search . '%'])->offset($start)->limit($limit)->orderBy($order, $dir);

            $audits = $search_query->get();
            $totalFiltered = Audit::leftjoin('users', 'users.user_id', '=', 'audits.user_id')->select('audits.*', 'users.first_name', 'users.middle_name', 'users.last_name')->where('audits.id', 'LIKE', "%{$search}%")->orWhere('audits.auditable_type', 'LIKE', "%{$search}%")->orWhere('audits.event', 'LIKE', "%{$search}%")->orWhere('audits.auditable_id', 'LIKE', "%{$search}%")->orWhere('audits.event', 'LIKE', "%{$search}%")->orWhere('audits.old_values', 'LIKE', "%{$search}%")->orWhere('audits.new_values', 'LIKE', "%{$search}%")->orWhereRaw("lower(concat_ws(' ', users.first_name, users.middle_name, users.last_name)) LIKE ? ", ['%' . $search . '%'])->count();
        }

        $data = array();
        if (!empty($audits)) {
            // Creating array to show the values in frontend
            foreach ($audits as $audit) {
                $nestedData['name'] = $audit->first_name . ' ' . $audit->middle_name . ' ' . $audit->last_name;
                $nestedData['first_name'] = $audit->first_name;
                $nestedData['middle_name'] = $audit->middle_name;
                $nestedData['last_name'] = $audit->last_name;
                $nestedData['model'] = substr($audit->auditable_type, 11); //$audit->auditable_type.'(ID: '.$audit->auditable_id.')';
                $nestedData['auditable_type'] = $audit->auditable_type;
                $nestedData['auditable_id'] = $audit->auditable_id;
                $nestedData['event'] = ucfirst($audit->event);
                if (!empty($audit->old_values)) {
                    $old_values = '<table class="table">';
                    foreach ($audit->old_values as $attribute => $value) {
                        $old_values .= '<tr>
                        <td><b>' . $attribute . '</b></td>
                        <td>' . $value . '</td>';
                    }
                    $old_values .= '</table>';
                } else {
                    $old_values = '';
                }
                $nestedData['old_values'] = $old_values;
                $new_values = '<table class="table">';
                foreach ($audit->new_values as $attribute => $value) {
                    if (!is_array($value)) {
                        $new_values .= '<tr>
                        <td><b>' . $attribute . '</b></td>
                        <td>' . $value . '</td>';
                    }
                }
                $new_values .= '</table>';
                $nestedData['new_values'] = $new_values;
                $nestedData['edit'] = $audit->id;
                $nestedData['url'] = $audit->url;
                $nestedData['ip_address'] = $audit->ip_address;
                $nestedData['user_agent'] = $audit->user_agent;
                $nestedData['created_at'] = date('m-d-Y h:i A', strtotime($audit->created_at));
                $data[] = $nestedData;
            }
        }
        // Drawing the Datatable
        $json_data = array(
            "draw" => intval($request->input('draw')),
            "recordsTotal" => intval($totalData),
            "recordsFiltered" => intval($totalFiltered),
            "data" => $data,
        );

        Log::info(addslashes(__METHOD__) . "(" . LINE . __LINE__ . ") : Audit Trail List fetched successfully");
        echo json_encode($json_data); // Rerurning the data
    }
}
