<template>
<div>
  <div v-if="loading">
    <CanPayLoader/>
  </div>
    <div class="content-wrapper" style="min-height: 36px">
      <section class="content-header">
        <div class="container-fluid">
          <div class="row mb-2">
            <div class="col-sm-6"></div>
          </div>
        </div>
      </section>
      <div class="hold-transition sidebar-mini">
        <section class="content">
          <div class="container-fluid">
            <div class="row">
              <div class="col-12">
                <div class="card card-success">
                  <div class="card-header">
                    <h3 class="card-title">Merchant Points Program Master</h3>
                  </div>
                  <div class="card-body">
                    <div class="row">
                      <div class="col-md-4">
                        <div class="form-group">
                          <multiselect
                            id="corporateparent"
                            name="corporateparent"
                            v-model="selectedCp"
                            placeholder="Select Corporate Parent (Min 3 chars)"
                            label="corporateparent"
                            track-by="id"
                            :options="cpList"
                            :multiple="false"
                            :loading="isLoadingCp"
                            :internal-search="false"
                            @search-change="getAllActiveCorporateParent"
                          ></multiselect>
                        </div>
                      </div>
                    </div>
                    
                    <div class="row">
                      <div class="col-md-4">
                          <button
                            type="button"
                            class="btn btn-success"
                            @click="loadDT()"
                          >
                            Search
                          </button>
                      </div>
                    </div>
                  </div>
                  <div class="card-footer">
                    
                  </div>
                  <div class="card-body">
                    <table
                        id="cashbackMasterTable"
                        class="table"
                        style="width: 100%; white-space: normal"
                      >
                        <thead>
                          <tr>
                            <th width="25%">Merchant Points Program Name</th>
                            <th width="13%">Status</th>
                            <th width="25%">Created On</th>
                            <th width="25%">Corporate Parents Name</th>
                            <th width="25%">Merchant Funded</th>
                            <th width="13%">Feedback</th>
                          </tr>
                        </thead>
                      </table>
                  </div>    
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
      <b-modal
        id="show-cashback-feedback"
        ref="show-cashback-feedback"
        :header-text-variant="headerTextVariant"
        title="Merchant Points Program Feedback"
        hide-footer
        >
        <div class="row odd-row">
          <div class="col-md-5 row-value">
            <label for="name">Merchant Points Program Name</label>
          </div>
          <div class="col-md-1 row-value"><label class="consumer-details-label">:</label></div>
          <div class="col-md-6 row-value">
            <label class="consumer-details-label">{{ program_name }}</label>
          </div>
        </div>
        <div class="card-body">
          <label v-if="cashbackFeedback.length > 0" for="balance-fetch-details">Feedback:</label>
          <b-table-simple
            responsive
            show-empty
            bordered
            sticky-header="800px"
            v-if="cashbackFeedback.length > 0"
          >
            <b-thead head-variant="light">
              <b-tr>
                <b-th class="text-center" width="10%">Name</b-th>
                <b-th class="text-center" width="10%">Email</b-th>
                <b-th class="text-center" width="10%">Phone</b-th>
                <b-th class="text-center" width="60%">Feedback</b-th>
                <b-th class="text-center" width="10%">Created On</b-th>
              </b-tr>
            </b-thead>
            <b-tbody v-for="(row, index) in cashbackFeedback" :key="index">
              <b-tr>
                <b-td class="text-center text-gray cashback-fix-with">{{
                  row.name
                }}</b-td>
                <b-td class="text-center text-gray cashback-fix-with">{{
                  row.email
                }}</b-td>
                <b-td class="text-center text-gray cashback-fix-with">{{
                  row.phone
                }}</b-td>
                <b-td class="text-center text-gray">{{
                  truncatedText(row.feedback)
                }}<span v-if="row.feedback.length > 50">
                    ... <a  href="javascript:void(0)"  @click="viewMoreFeedBack(row.feedback)">More</a>
                  </span>
                </b-td>
                <b-td class="text-center text-gray cashback-fix-with">{{
                  row.created_at
                }}</b-td>
              </b-tr>
            </b-tbody>
          </b-table-simple>
          <div v-if="cashbackFeedback.length > 0" style="float:right">
              <b-pagination
                  v-model="currentPage"
                  :total-rows="totalItems"
                  :per-page="perPage"
                  prev-text="Prev"
                  next-text="Next"
                  :ellipsis="true"
                  :limit="5"
              ></b-pagination>
          </div>
          <p v-else>No data displayed.</p>
        </div>
      </b-modal>
      <b-modal
        id="show-more-feedback"
        ref="show-more-feedback"
        hide-header
        hide-footer
        >
        <!-- Custom header with close button -->
        <template v-slot:modal-title>
          <b-button class="btn-close" variant="link" @click="$refs['show-more-feedback'].hide()"></b-button>
        </template>
        <div class="card-body">
          <p>{{more_feedback}}</p>
        </div>
        
        <div class="row">
          <div class="col-12 text-center">
            <button
              type="button"
              class="btn btn-success btn-md center-block ml-2"
              style="height: 40px"
              @click="hideModal('show-more-feedback')"
            >
              <span class="purchasepower-modal-ok-label"
                >OK</span
              >
            </button>
          </div>
        </div>
      </b-modal>
    </div>
</div>
  </template>
  <script>
  import reportapi from "@/api/reports.js";
  import api from "@/api/transaction.js";
  import rewardwheelapi from "@/api/rewardwheel.js";
  import moment from "moment";
  import { validationMixin } from "vuelidate";
  import { required, minLength } from "vuelidate/lib/validators";
  import { HourGlass } from "vue-loading-spinner";
  import commonConstants from "@/common/constant.js";
  import CanPayLoader from './CustomLoader/CanPayLoader.vue';
  export default {
    mixins: [validationMixin],
    data() {
      return {
        headerTextVariant: "light",
        selectedCp: "",
        showReloadBtn: false,
        isLoadingCp: false,
        cpList: [],
        allCashbackModel: [],
        cashbackFeedback: [],
        isLoading: false,
        loading: false,
        program_name: '',
        edit_id: '',
        more_feedback: '',
        currentPage: 1,
        perPage: 10,
        totalPage: 0,
        totalItems: 0,
        rewardWheelAPIUrl: process.env.MIX_REWARD_WHEEL_APP_URL,
      };
    },
    components: {
      HourGlass,
      CanPayLoader
    },
    methods: {
      hideModal(modalID){
        var self = this;
        self.$bvModal.hide(modalID);
      },
      truncatedText(fullText) {
        return fullText.length > 50
          ? fullText.substring(0, 50)
          : fullText;
      },
      viewMoreFeedBack(fullText) {
        var self = this;
        self.more_feedback = fullText;
        self.$bvModal.show("show-more-feedback");
      },
      //get the list of All CP
      getAllActiveCorporateParent(searchtxt) {
        var self = this;
        if(searchtxt.length >= 3){
          self.isLoadingCp = true;
          self.cpList = [];
          var request = {
            searchtxt: searchtxt,
          };
          reportapi
            .getAllActiveCorporateParent(request)
            .then(function (response) {
              if (response.code == 200) {
                self.cpList = response.data;
                self.storeList = [];
                self.isLoadingCp = false;
              } else {
                error(response.message);
              }
            })
            .catch(function (error) {
              error(error);
            });
        }
      },
      reloadDatatable() {
        var self = this;
        self.loadDT();
      },
      loadDT: function () {
        var self = this;  
        self.loading = true;      
        var selectedCp = self.selectedCp ? self.selectedCp.id : null;
        $("#cashbackMasterTable").DataTable({
          pagingType: "simple_numbers",
          processing: true,
          serverSide: true,
          destroy: true,
          columnDefs: [
            { orderable: false, targets: [3, 4] },
            { className: "dt-left break-all", targets: [0, 1, 2, 3] },
            { className: "dt-center", targets: [4] },
          ],
          order: [[1, "asc"], [2, "desc"]],
          orderClasses: false,
          language: {
            processing:
              '<i class="fa fa-spinner fa-spin fa-3x fa-fw"></i><span class="sr-only">Loading...</span> ',
            emptyTable: "No Points Program Available.",
            search: "_INPUT_",
            searchPlaceholder: "Search records",
            oPaginate: {
              sNext: '<i class="fas fa-angle-double-right"></i>',
              sPrevious: '<i class="fas fa-angle-double-left"></i>',
            },
            sLengthMenu:
              "<label class='label_dropdown_dt'>Per page</label> _MENU_",
          },
          ajax: {
            headers: {
              Authorization: "Bearer "+localStorage.getItem("token"),
            },
            url: self.rewardWheelAPIUrl + "/api/merchantadmin/getcashbackprogramlist",
            type: "POST",
            data: {
              _token: "{{csrf_token()}}",
              user_id: selectedCp,
            },
            dataType: "json",
            dataSrc: function (result) {
              self.loading = false;
              self.showReloadBtn = false;
              self.allCashbackModel = result.data;
              return self.allCashbackModel;
            },
            error: function (request) {
              self.loading = false;
              if (
                request.responseJSON.code == 401 &&
                request.responseJSON.data != null
              ) {
                localStorage.setItem("token", request.responseJSON.data);
                self.loadDT();
              }else{
                error(Constants.datatable_error);
                $('#cashbackMasterTable_processing').hide();
                self.showReloadBtn = true;
              }
            },
          },
          columns: [
            { data: "cashback_program_name"},
            {
              render: function (data, type, full, meta) {
                if (full.is_default == 1) {
                  return '';
                } else {
                  return full.status;
                }
              },
            },  
            {
              render: function (data, type, full, meta) {
                if (full.is_default == 1) {
                  return '';
                } else {
                  return full.created_at;
                }
              },
            },  
            {
              render: function (data, type, full, meta) {
                if (full.is_default == 1) {
                  return '';
                } else {
                  return full.cp_name;
                }
              },
            },  
            {
              render: function (data, type, full, meta) {
                if (full.is_default == 1) {
                  return '';
                } else {
                  return '<label class="switch">' +
                        '<input class="toggleMerchantFunded" type="checkbox" ' + (full.is_merchant_funded ? 'checked' : '') + ' data-id="' + full.edit_id + '" data-program-name="' + full.cashback_program_name + '">' +
                        '<span class="slider round"></span>' +
                        '</label>';
                }
              },
            },  
            {
              render: function (data, type, full, meta) {
                if (full.feedback) {
                  return (
                    '<b-button data-user-id="' + full.edit_id + '"  data-program-name="' + full.cashback_program_name + '" class="showCashbackFeedback custom-edit-btn" title="Show Feedback" variant="outline-success"><i class="nav-icon fas fa-comment"></i></b-button><b-button data-user-id="</b-button>'
                  );
                } else {
                  return '';
                }
              },
            }
          ],
        });

        $("#cashbackMasterTable").on("page.dt", function () {
          $("html, body").animate({ scrollTop: 0 }, "slow");
          $("th:first-child").focus();
        });

        //Search in the table only after 3 characters are typed
        // Call datatables, and return the API to the variable for use in our code
        // Binds datatables to all elements with a class of datatable
        var dtable = $("#cashbackMasterTable").dataTable().api();

        // Grab the datatables input box and alter how it is bound to events
        $(".dataTables_filter input")
        .unbind() // Unbind previous default bindings
        .bind("input", function(e) { // Bind our desired behavior
            // If the length is 3 or more characters, or the user pressed ENTER, search
            if(this.value.length >= 3 || e.keyCode == 13) {
                // Call the API search function
                dtable.search(this.value).draw();
            }
            // Ensure we clear the search if they backspace far enough
            if(this.value == "") {
                dtable.search("").draw();
            }
            return;
        });
      },
      reset(){
      },
      getCashbackFeedback(edit_id, program_name){
        var self = this;
        self.program_name = program_name;
        var request = {
          id: edit_id,
          page: self.currentPage,
          per_page: self.perPage,
        };
        self.loading = true;
        rewardwheelapi
        .getCashbackFeedback(request)
        .then(function (response) {
          if (response.code == 200) {
            self.cashbackFeedback = response.data.data;
            self.totalPage = response.data.total_pages;
            self.totalItems = response.data.total;
            self.currentPage = response.data.current_page;
            self.$bvModal.show("show-cashback-feedback");
            self.loading = false;
          } else {
            error(response.message);
            self.loading = false;
          }
        })
        .catch(function (error) {
          // error(error);
          self.loading = false;
        });
      },
      showCashbackFeedback(){
        var self = this;
        $(document).on("click", ".showCashbackFeedback", function (e) {
          self.edit_id = $(e.currentTarget).attr("data-user-id");
          self.program_name = $(e.currentTarget).attr("data-program-name");
          self.getCashbackFeedback(self.edit_id, self.program_name);
        })
      },
      toggleMerchantFunded(){
        var self = this;
        $(document).on("click", ".toggleMerchantFunded", function (e) {
          self.edit_id = $(e.currentTarget).attr("data-id");
          self.program_name = $(e.currentTarget).attr("data-program-name");
          var request = {
            id: self.edit_id
          };
          self.loading = true;
          rewardwheelapi
            .toggleMerchantFunded(request)
            .then(response => {
              if ((response.code = 200)) {
                success(response.message);
              } else {
                error(response.message);
              }
              self.loading = false;
            })
            .catch(err => {
              error(err.response.data.message);
              self.loading = false;
            });
        })
      },
    },
    mounted() {
      var self = this;
      setTimeout(function () {
        self.loadDT();
      }, 1000);
      self.showCashbackFeedback();
      self.toggleMerchantFunded();
    },
    beforeDestroy(){
      $(document).off('click', '.showCashbackFeedback');
      $(document).off('click', '.toggleMerchantFunded');
    },
  };
  </script>
 <style>
.cashback-fix-with {
  word-wrap: break-word !important;
  max-width: 150px !important;
}
</style>
