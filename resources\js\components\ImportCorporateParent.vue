<template>
 <div>
  <div v-if="loading">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Import Corporate Parents</h3>
                  <b-button
                  class="btn-danger export-api-btn"
                  @click="reloadDatatable"
                  v-if="showReloadBtn"
                  >
                    <i class="fas fa-redo"></i> Reload
                  </b-button>
                </div>
                <!-- /.card-header -->
                <div class="card-body">
                  <div
                    class="alert alert-success alert-dismissible"
                    v-if="success_message != null"
                  >
                    <a
                      href="#"
                      class="close"
                      data-dismiss="alert"
                      aria-label="close"
                      style="text-decoration: none"
                      @click="success_message = null"
                      >&times;</a
                    >
                    <strong>Success!</strong> {{ success_message }}
                  </div>
                  <div class="form-group">
                    <label for="exampleInputFile"
                      >Upload Corporate Parents</label
                    >
                    <button
                      type="button"
                      class="btn btn-danger ml-10"
                      style="float:right; margin-top:-14px;"
                      @click="downloadSampleFile();"
                    >
                      Download Sample <i
                        class="fa fa-download ml-10"
                        aria-hidden="true"
                      ></i>
                    </button>
                    <div class="input-group">
                      <div class="custom-file">
                        <input
                          type="file"
                          ref="corporate_parents_file"
                          id="exampleInputFile"
                          v-on:change="handleFileUpload('corporate_parents')"
                          class="custom-file-input"
                          accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
                        />
                        <label
                          for="exampleInputFile"
                          class="custom-file-label"
                          >{{ corporate_parents_label }}</label
                        >
                      </div>
                    </div>
                  </div>
                  <span
                    style="float: right"
                    class="btn btn-success"
                    @click="migrate"
                    >Import Corporate Parents</span
                  >
                </div>
                <hr />
                <div class="card-body">
                  <h5>Import History</h5>
                  <table
                    id="CorporateParentsImportTable"
                    class="table"
                    style="width: 100%; white-space: normal"
                  >
                    <thead>
                      <tr>
                        <th>Status</th>
                        <th>Data Imported</th>
                        <th>Duplicate Data Found</th>
                        <th>Imported Data Updated</th>
                        <th>Uploaded By</th>
                        <th>Imported On</th>
                      </tr>
                    </thead>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</div>
</template>
<script>
import api from "@/api/import.js";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "./CustomLoader/CanPayLoader.vue";
import commonConstants from "@/common/constant.js";
export default {
  components: {
    HourGlass,
    CanPayLoader
  },
  data() {
    return {
      corporate_parents: null,
      canpay_transaction_fees: null,
      corporate_parents_label: "Choose File",
      success_message: null,
      is_visible: 0,
      showReloadBtn:false,
      loading: false
    };
  },
  methods: {
    reloadDatatable(){
      var self = this;
      self.loadDT();
    },
    handleFileUpload(sheet_name) {
      if (sheet_name === "corporate_parents") {
        this.corporate_parents = this.$refs.corporate_parents_file.files[0];
        this.corporate_parents_label = this.$refs.corporate_parents_file.files[0].name;
      }
    },
    /*Submits the file to the server*/
    migrate() {
      var self = this;
      self.is_visible = 1;
      if (
        self.corporate_parents == null
      ) {
        self.is_visible = 0;
        error("Please select a file to import.");
        return false;
      }
      /*Initialize the form data*/
      let formData = new FormData();
      formData.append(
        "corporate_parents",
        self.corporate_parents
      );
      /*call to the import excel api */
      this.loading = true;
      api
        .migrateCorporateParents(formData)
        .then((response) => {
          if (response.code == 200) {
            self.$refs.corporate_parents_file.value = null;
            self.corporate_parents = null;
            self.corporate_parents_label = "Choose File";
            self.success_message = response.message;
            self.is_visible = 0;
            self.loadDT();
          } else {
            error(response.message);
            self.is_visible = 0;
          }
          this.loading = false;
        })
        .catch((response) => {
          error(response);
          self.is_visible = 0;
          this.loading = true
        });
    },
    loadDT: function () {
      var self = this;
      $("#CorporateParentsImportTable").DataTable({
        searching: false,
        pagingType: "simple_numbers",
        processing: true,
        serverSide: true,
        destroy: true,
        columnDefs: [
          { orderable: false, targets: [0] },
          { className: "dt-left", targets: [0, 1, 2, 3, 4, 5] },
        ],
        order: [[5, "desc"]],
        orderClasses: false,
        bLengthChange: false,
        bPaginate: false,
        language: {
          processing:
            '<i class="fa fa-spinner fa-spin fa-3x fa-fw"></i><span class="sr-only">Loading...</span> ',
          emptyTable: "No Import Log Available.",
          search: "_INPUT_",
          searchPlaceholder: "Search records",
          oPaginate: {
            sNext: '<i class="fas fa-angle-double-right"></i>',
            sPrevious: '<i class="fas fa-angle-double-left"></i>',
          },
          sLengthMenu:
            "<label class='label_dropdown_dt'>Per page</label> _MENU_",
        },
        ajax: {
          headers: {
            Authorization: "Bearer " + localStorage.getItem("token"),
          },
          url: "/api/getmigrationlog",
          type: "POST",
          data: { _token: "{{csrf_token()}}", type: "Corporate Parent" },
          dataType: "json",
          dataSrc: function (result) {
            self.showReloadBtn = false;
            return result.data;
          },
          error: function(data){
            error(commonConstants.datatable_error);
            $('#CorporateParentsImportTable_processing').hide();
            self.showReloadBtn = true;
          }
        },
        columns: [
          { data: "summary" },
          { data: "actual_data_imported" },
          { data: "duplicate_date_imported" },
          { data: "migrated_data_updated" },
          { data: "uploaded_by" },
          { data: "created_at" },
        ],
      });

      $("#CorporateParentsImportTable").on("page.dt", function () {
        $("html, body").animate({ scrollTop: 0 }, "slow");
        $("th:first-child").focus();
      });

      //Search in the table only after 3 characters are typed
      // Call datatables, and return the API to the variable for use in our code
      // Binds datatables to all elements with a class of datatable
      var dtable = $("#CorporateParentsImportTable").dataTable().api();

      // Grab the datatables input box and alter how it is bound to events
      $(".dataTables_filter input")
      .unbind() // Unbind previous default bindings
      .bind("input", function(e) { // Bind our desired behavior
          // If the length is 3 or more characters, or the user pressed ENTER, search
          if(this.value.length >= 3 || e.keyCode == 13) {
              // Call the API search function
              dtable.search(this.value).draw();
          }
          // Ensure we clear the search if they backspace far enough
          if(this.value == "") {
              dtable.search("").draw();
          }
          return;
      });
    },
    downloadSampleFile(){
      window.location.href = "sample_import_excel/Corporate_Parent_Import.xlsx";
    }
  },
  mounted() {
    var self = this;
    self.loading = true;
    setTimeout(function () {
      self.loadDT();
      self.loading = false;
    }, 1000);
    document.title = "CanPay - Import Corporate Parents";
  },
};
</script>
