<template>
<div>
  <div v-if="loading">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px;">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Consumer Global Search</h3>
                </div>

                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <input type="checkbox" name="manual_identity_review" id="manual_identity_review" v-model="manual_identity_review" /> Manual Identity Review
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <input type="checkbox" name="v1_manual_review" id="v1_manual_review" v-model="v1_manual_review" /> V1 Manual Review
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <input type="checkbox" name="global_radar_review" id="global_radar_review" v-model="global_radar_review" /> Global Radar Review
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <input
                                    class="form-control"
                                    placeholder="First Name (min 3 chars)"
                                    id="consumer"
                                    v-model="consumer_first_name"
                                />
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <input
                                    class="form-control"
                                    placeholder="Middle Name (min 3 chars)"
                                    id="consumer"
                                    v-model="consumer_middle_name"
                                />
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <input
                                    class="form-control"
                                    placeholder="Last Name (min 3 chars)"
                                    id="consumer"
                                    v-model="consumer_last_name"
                                />
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                            <input
                                class="form-control"
                                placeholder="Phone No."
                                id="phone_no"
                                v-model="phone_no"
                            />
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                            <input
                                class="form-control"
                                placeholder="Email"
                                id="email"
                                v-model="email"
                            />
                            </div>
                        </div>
                    </div>
                </div>
              <div class="card-footer">
                <button
                  type="button"
                  class="btn btn-success"
                  @click="searchConsumer()"
                >
                  Search
                </button>
              </div>
                <!-- /.card-header -->
                <div class="card-body">
                  <div class="row">
                  <div class="col-12"><b-table-simple
                      responsive
                      show-empty
                      bordered
                      sticky-header="800px"
                    >
                      <b-thead head-variant="light">
                        <b-tr>
                          <b-th class="text-left">Name</b-th>
                          <b-th class="text-left">Email</b-th>
                          <b-th class="text-left">Phone</b-th>
                          <b-th class="text-center">Status</b-th>
                          <b-th class="text-center">Action</b-th>
                        </b-tr>
                      </b-thead>
                      <b-tbody v-for="(row, index) in allConsumerModel" :key="index">
                        <b-tr>
                          <b-td class="text-left text-gray">{{
                            row.name
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.email
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.phone
                          }}</b-td>
                          <b-td class="text-center text-gray">{{
                            row.status
                          }}</b-td>
                          <b-td class="text-center text-gray" v-html="row.action"></b-td>
                        </b-tr>
                      </b-tbody>
                    </b-table-simple>
                  </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</div>
</template>
<script>
import api from "@/api/user.js";
import moment from "moment";
import { validationMixin } from "vuelidate";
import { required, minLength } from "vuelidate/lib/validators";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "../CustomLoader/CanPayLoader.vue"
export default {
  mixins: [validationMixin],
  data() {
    return {
      allConsumerModel: [],
      currentUser: localStorage.getItem("user")
        ? JSON.parse(localStorage.getItem("user"))
        : null,
      email:"",
      phone_no:"",
      isLoading: false,
      manual_identity_review:1,
      v1_manual_review:1,
      global_radar_review:1,
      loading:false,
      consumer_first_name: '',
      consumer_middle_name: '',
      consumer_last_name: '',
    };
  },
  components: {
    HourGlass,
    CanPayLoader
  },
  created() {
  },
  methods: {

    searchConsumer(){
      var self = this;
      if(((self.consumer_first_name).trim().length < 3 && (self.consumer_middle_name).trim().length < 3 && (self.consumer_last_name).trim().length < 3) && $("#phone_no").val().trim() === '' &&  $("#email").val().trim() === ''){
        error("Please provide Consumer name (Min 3 chars) or email(exact) or phone no(exact)");
        return false;
      }
      var request = {
        consumer_first_name: self.consumer_first_name,
        consumer_middle_name: self.consumer_middle_name,
        consumer_last_name: self.consumer_last_name,
        email:self.email,
        phone_no:self.phone_no,
        manual_identity_review:self.manual_identity_review,
        v1_manual_review:self.v1_manual_review,
        global_radar_review:self.global_radar_review,
      };
      self.loading = true;
      api
      .searchConsumer(request)
      .then(function (response) {
        if (response.code == 200) {
          self.allConsumerModel = response.data;
          self.loading = false;
        } else {
          error(response.message);
          self.loading = false;
        }
      })
      .catch(function (error) {
        // error(error);
        self.loading = false;
      });
    }
  },
  mounted() {
  }
};
</script>

