const login = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/login', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const forgotPassword = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/forgotpassword', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

export default {
    login,
    forgotPassword,
};