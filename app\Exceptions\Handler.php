<?php

namespace App\Exceptions;

use App\Exceptions\ApplicationException;
use App\Exceptions\ValidationException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Support\Facades\Log;
use Throwable;

class Handler extends ExceptionHandler
{

    /**
     * A list of the exception types that are not reported.
     *
     * @var array
     */
    protected $dontReport = [
        AuthorizationException::class,
        HttpException::class,
        ModelNotFoundException::class,
        //ValidationException::class,
        MethodNotAllowedHttpException::class,
    ];

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var array
     */
    protected $dontFlash = [
        'password',
        'password_confirmation',
    ];

    /**
     * Report or log an exception.
     *
     * @param  \Exception  $exception
     * @return void
     */
    public function report(Throwable $exception)
    {
        //parent::report($exception);
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Exception  $exception
     * @return \Illuminate\Http\Response
     */
    public function render($request, Throwable $e)
    {
        $fail = FAIL;
        $message = "";
        if ($this->isHttpException($e)) {
            $fail = UNAUTH;
            Log::error('Log for HttpException ' . $this->formatMessage($e));
            $message = $e->getMessage();
        } else if ($e instanceof ApplicationException) {
            Log::error('Log for ApplicationException ' . $this->formatMessage($e));
            $message = $e->getMessage();
        } else if ($e instanceof MailException) {
            Log::error('Log for MailException ' . $this->formatMessage($e));
            $message = $e->getMessage();
        } else if ($e instanceof ValidationException) {
            $fail = VALIDATION;
            Log::error('Log for ValidationException ' . $this->formatMessage($e));
            $message = $e->getMessage();
        } else if ($e instanceof \GuzzleHttp\Exception\GuzzleException) {
            $fail = FAIL;
            Log::error('Log for GuzzleException');
            $message = 'Internal Server Error. Please Try again';
        } else if ($e instanceof \Symfony\Component\HttpKernel\Exception\UnauthorizedHttpException || $e instanceof AuthException) {
            $fail = UNAUTH;
            Log::error('Log for UnauthorizedHttpException ' . $this->formatMessage($e));
            $message = $e->getMessage();
        } else if (config('app.debug')) {
            Log::error('Log for error Exception ' . $this->formatMessage($e));
            $message = $e->getMessage();
        } else if ($e instanceof \PDOException) {
            Log::error('Log for Database Exception ' . $this->formatMessage($e));
            $message = "Something Went Wrong. Please try again.";
        } else {
            Log::error('Log for Exception ' . $this->formatMessage($e));
            $message = $e->getMessage();
        }
        return renderResponse($fail, $message, null, true);
    }

    // format message for writting in log file
    protected function formatMessage($e)
    {
        return 'Exception occured on [' . addslashes($e->getFile()) . '] Line number[' . $e->getLine() . '] with Code [' . $e->getCode() . '] and  Message [' . $e->getMessage() . ']';
    }

}
