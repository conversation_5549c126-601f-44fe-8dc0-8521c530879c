import { loadProgressBar } from 'axios-progress-bar'

const getAllActiveStores = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/allActiveStores', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getAllActiveCorporateParent = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/allActiveCorporateParent', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const generateSettlementReport = (request) => {
    var instance = axios.create({
        baseURL: process.env.MIX_API_URL,
    });
    instance.defaults.headers.common["Authorization"] = localStorage.getItem("token");
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.post('/merchantadmin/reports/settlementreport', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const getReducedTransactionFeesReport = (request) => {
    var instance = axios.create({
        baseURL: process.env.MIX_API_URL,
    });
    instance.defaults.headers.common["Authorization"] = localStorage.getItem("token");
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.post('/merchantadmin/reports/getreducedtransactionfeesreport', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const getReducedTransactionFeesExport = (request) => {
    var header = {
        responseType: 'blob',
    };
    var instance = axios.create({
        baseURL: process.env.MIX_API_URL,
    });
    instance.defaults.headers.common["Authorization"] = localStorage.getItem("token");
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.post('/merchantadmin/reports/getreducedtransactionfeesexport', request, header)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const viewMerchantRewarsList = (request) => {
    var instance = axios.create({
        baseURL: process.env.MIX_API_URL,
    });
    instance.defaults.headers.common["Authorization"] = localStorage.getItem("token");
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.post('/merchantadmin/reports/viewmerchantrewards', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const exportSettlementReport = (request) => {
    var header = {
        responseType: 'blob',
    };
    var instance = axios.create({
        baseURL: process.env.MIX_API_URL,
    });
    instance.defaults.headers.common["Authorization"] = localStorage.getItem("token");
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.post('/merchantadmin/reports/getsettlementexport', request, header)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const generateMerchantTransactionReport = (request) => {
    var instance = axios.create({
        baseURL: process.env.MIX_API_URL,
    });
    instance.defaults.headers.common["Authorization"] = localStorage.getItem("token");
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.post('merchantadmin/reports/getmerchantlocationtransactionreport', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getMerchantTransactionExport = (request) => {
    var instance = axios.create({
        baseURL: process.env.MIX_API_URL,
    });
    instance.defaults.headers.common["Authorization"] = localStorage.getItem("token");
    loadProgressBar({}, instance)
    var header = {
        responseType: 'blob',
    };
    return new Promise((res, rej) => {
        instance.post('merchantadmin/reports/getmerchantlocationtransactionexport', request, header)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const generateFinicityReturns = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/generatefinicityreturns', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getFinicityReturnsExport = (request) => {
    var header = {
        responseType: 'blob',
    };
    return new Promise((res, rej) => {
        axios.post('api/export/finicityreturnsexport', request, header)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const generateManualBankingReturns = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/generatemanualbankingreturns', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getManualBankingReturnsExport = (request) => {
    var header = {
        responseType: 'blob',
    };
    return new Promise((res, rej) => {
        axios.post('api/export/manualbankingreturnsexport', request, header)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const generateManualReviewReturns = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/generatemanualreviewreturns', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getManualReviewReturnsExport = (request) => {
    var header = {
        responseType: 'blob',
    };
    return new Promise((res, rej) => {
        axios.post('api/export/manualreviewreturnsexport', request, header)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const generateReturnDashboardReport = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/generatereturnreportdashboard', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const generateReturnDashboardReportExport = (request) => {
    var header = {
        responseType: 'blob',
    };
    return new Promise((res, rej) => {
        axios.post('api/export/generatereturnreportdashboardExport', request, header)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const generateMonthlySalesGrowth = (request) => {
    return new Promise((res, rej) => {
        axios.post('/api/getmonthlysalesgrowth', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const exportGenerateMonthlySalesGrowth = (request) => {
    var header = {
        responseType: 'blob',
    };
    return new Promise((res, rej) => {
        axios.post('/api/export/getmonthlysalesgrowthexport', request, header)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const generateStorewiseMonthlySales = (request) => {
    return new Promise((res, rej) => {
        axios.post('/api/generatestorewisemonthlysales', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const exportGenerateStorewiseMonthlySales = (request) => {
    var header = {
        responseType: 'blob',
    };
    return new Promise((res, rej) => {
        axios.post('/api/export/getstorewisemonthlysalesexport', request, header)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getAchFileLists = (request) => {
    var instance = axios.create({
        baseURL: process.env.MIX_ACH_APP_URL,
    });
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.post('/api/admin/fetchachfilelists', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const downloadAchFile = (request) => {
    var instance = axios.create({
        baseURL: process.env.MIX_ACH_APP_URL,
    });
    var header = {
        responseType: 'blob',
    };
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.post('/api/admin/downloadachfile', request, header)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const generateAchTransactionsExcel = (request) => {
    var instance = axios.create({
        baseURL: process.env.MIX_ACH_APP_URL,
    });
    var header = {
        responseType: 'blob',
    };
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.post('/api/admin/export/generateachtransactionsexcel', request, header)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const uploadAchFile = (request) => {
    var instance = axios.create({
        baseURL: process.env.MIX_ACH_APP_URL,
    });
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.post('/api/admin/uploadtosftpserver', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const downloadDeliliterizedAchFile = (request) => {
    var instance = axios.create({
        baseURL: process.env.MIX_ACH_APP_URL,
    });
    var header = {
        responseType: 'blob',
    };
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.post('/api/admin/adddelimitersinach', request, header)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const validateEntryHash = (request) => {
    var instance = axios.create({
        baseURL: process.env.MIX_ACH_APP_URL,
    });
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.post('/api/admin/validateentryhash', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const reverseAchFile = (request) => {
    var instance = axios.create({
        baseURL: process.env.MIX_ACH_APP_URL,
    });
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.post('/api/admin/createachreversalfile', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const generateRefreshToken = () => {
    var instance = axios.create({
        baseURL: process.env.MIX_API_URL,
    });
    loadProgressBar({}, instance)
    instance.defaults.headers.common["Authorization"] = localStorage.getItem("token");
    return new Promise((res, rej) => {
        instance.get('/merchantadmin/reports/refreshtokenforblobtypes')
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                console.log(err);
                rej(err);
            })
    })
};

const exportv1SettlementReportAllStores = (request) => {
    var instance = axios.create({
        baseURL: process.env.MIX_API_URL,
    });
    var header = {
        responseType: 'blob',
    };
    loadProgressBar({}, instance)
    instance.defaults.headers.common["Authorization"] = localStorage.getItem("token");
    return new Promise((res, rej) => {
        instance.post('merchantadmin/reports/getsettlementexportallstores', request, header)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const generateMerchantPointReport = (request) => {
    return new Promise((res, rej) => {
        axios.post('/api/generatemerchantpointreport', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const exportMerchantPointReport = (request) => {
    var header = {
        responseType: 'blob',
    };
    return new Promise((res, rej) => {
        axios.post('/api/export/generatemerchantpointreportexport', request, header)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
export default {
    getAllActiveStores,
    getAllActiveCorporateParent,
    generateSettlementReport,
    getReducedTransactionFeesReport,
    getReducedTransactionFeesExport,
    viewMerchantRewarsList,
    exportSettlementReport,
    generateMerchantTransactionReport,
    getMerchantTransactionExport,
    generateFinicityReturns,
    getFinicityReturnsExport,
    generateManualBankingReturns,
    getManualBankingReturnsExport,
    generateManualReviewReturns,
    getManualReviewReturnsExport,
    generateReturnDashboardReport,
    generateReturnDashboardReportExport,
    generateMonthlySalesGrowth,
    exportGenerateMonthlySalesGrowth,
    generateStorewiseMonthlySales,
    exportGenerateStorewiseMonthlySales,
    generateMerchantPointReport,
    exportMerchantPointReport,
    getAchFileLists,
    downloadAchFile,
    generateAchTransactionsExcel,
    uploadAchFile,
    downloadDeliliterizedAchFile,
    validateEntryHash,
    reverseAchFile,
    generateRefreshToken,
    exportv1SettlementReportAllStores
};