<template>
<div>
  <div v-if="loading">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Transaction Report</h3>
                </div>
                <div class="card-body">
                <div class="row">
                  <div class="col-md-4">
                    <div class="form-group">
                      <multiselect
                        id="corporateparent"
                        name="corporateparent"
                        v-model="selectedCp"
                        placeholder="Select Corporate Parent (Min 3 chars)"
                        label="corporateparent"
                        track-by="id"
                        :options="cpList"
                        :multiple="false"
                        :loading="isLoadingCp"
                        :internal-search="false"
                        @search-change="getAllActiveCorporateParent"
                        @input="dateDiff"
                        @select="refreshStores"
                      ></multiselect>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <multiselect
                        id="store"
                        name="store"
                        v-model="selectedStore"
                        placeholder="Select Store (Min 3 chars)"
                        label="retailer"
                        track-by="id"
                        :options="storeList"
                        :multiple="true"
                        :loading="isLoadingSt"
                        :internal-search="true"
                        @input="dateDiff"
                      ></multiselect>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="start-date form-control"
                        placeholder="Start Date"
                        id="start-date"
                        onkeydown="return false"
                        autocomplete="off"
                      />
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="end-date form-control"
                        placeholder="End Date"
                        id="end-date"
                        onkeydown="return false"
                        autocomplete="off"
                      />
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <label class="switch"><input class="enable-employee-login" type="checkbox" v-model="toggle_void"><span class="slider round"></span></label> Voids
                    </div>
                  </div>
                </div>
              </div>
              <div class="card-footer">
                <button
                  type="button"
                  class="btn btn-success"
                  @click="generateReport(false)"
                >
                  Generate
                </button>
                <button
                  type="button"
                  @click="generateReport(true)"
                  class="btn btn-danger ml-10"
                >
                  Generate & Export<i
                    class="fa fa-download ml-10"
                    aria-hidden="true"
                  ></i>
                </button>
                <button
                  type="button"
                  @click="reset()"
                  class="btn btn-success margin-left-5"
                >
                  Reset
                </button>
                <span class="helper-text-right" v-if="helptext_visible">*Today's sales are not settled and can still be voided.</span>
              </div>


              <div class="card-body">
                <div class="row">
                  <div class="col-12">
                  <b-btn @click="showAllDetails" class="mb-10 btn-dark" v-if="report.length > 0">Expand All</b-btn>
                  <b-btn @click="hideAllDetails" class="ml-10 mb-10 btn-dark" v-if="report.length > 0">Collapse All</b-btn>
                    <b-table
                      show-empty
                      :fields="fields"
                      responsive
                      bordered
                      sticky-header="600px"
                      id="merchantLocationTransactionTable"
                      :items="report"
                      :tbody-tr-class="rowClass"
                    >
                      <template #cell(store_category)="data">
                        {{data.item.store_category}} <br/>
                        <b-button size="sm" @click="data.toggleDetails" class="mr-2 btn-dark" v-if="data.item.store_category!=''">
                          {{ data.detailsShowing ? 'Hide' : 'Show'}} Details
                        </b-button>
                      </template>
                      <template #row-details="data">
                        <b-table-simple :fields="fields" :tbody-tr-class="rowClass" class="merchantLocationTableCls">
                          <b-thead head-variant="light">
                            <b-tr>
                              <b-th class="text-center">Terminal ID</b-th>
                              <b-th class="text-center">Transaction Date</b-th>
                              <b-th class="text-center" v-if="data.item.report_data1[1].transaction_time!=''">Transaction Time</b-th>
                              <b-th class="text-center">Transaction No</b-th>
                              <b-th class="text-center">Total Payment</b-th>
                              <b-th class="text-center">Base Amount</b-th>
                              <b-th class="text-center">Tip</b-th>
                              <b-th class="text-center">Trans Count</b-th>
                              <b-th class="text-center">Transaction Status</b-th>
                              <b-th class="text-center">Consumer Identifier</b-th>
                            </b-tr>
                          </b-thead>
                          <b-tbody>
                            <b-tr v-for="(terminalList,index) in data.item.report_data1" :key="index">
                              <b-td class="text-center">{{terminalList.terminalID}}</b-td>
                              <b-td :class="terminalList.transaction_status== 'Voided'?'text-gray-italic':'text-center'">{{terminalList.transaction_date | moment("MM/DD/YYYY")}}</b-td>
                              <b-td :class="terminalList.transaction_status== 'Voided'?'text-gray-italic':'text-center'" v-if="data.item.report_data1[1].transaction_time!=''">{{terminalList.transaction_time | moment("hh:mm A")}}</b-td>
                              <b-td :class="terminalList.transaction_status== 'Voided'?'text-gray-italic':'text-center'">{{terminalList.transaction_no}}</b-td>
                              <b-td :class="terminalList.transaction_status== 'Voided'?'text-gray-italic':'text-center'" v-if="terminalList.terminalID!=''"><b>{{terminalList.total_payment}}</b></b-td>
                              <b-td :class="terminalList.transaction_status== 'Voided'?'text-gray-italic':'text-center'" v-else>{{terminalList.total_payment}}</b-td>
                              <b-td :class="terminalList.transaction_status== 'Voided'?'text-gray-italic':'text-center'" v-if="terminalList.terminalID!=''"><b>{{terminalList.base_amount}}</b></b-td>
                              <b-td :class="terminalList.transaction_status== 'Voided'?'text-gray-italic':'text-center'" v-else>{{terminalList.base_amount}}</b-td>
                              <b-td :class="terminalList.transaction_status== 'Voided'?'text-gray-italic':'text-center'" v-if="terminalList.terminalID!=''"><b>{{terminalList.tip}}</b></b-td>
                              <b-td :class="terminalList.transaction_status== 'Voided'?'text-gray-italic':'text-center'" v-else>{{terminalList.tip}}</b-td>
                              <b-td :class="terminalList.transaction_status== 'Voided'?'text-gray-italic':'text-center'" v-if="terminalList.terminalID!=''"><b>{{terminalList.trans_count}}</b></b-td>
                              <b-td :class="terminalList.transaction_status== 'Voided'?'text-gray-italic':'text-center'" v-else>{{terminalList.trans_count}}</b-td>
                              <b-td :class="terminalList.transaction_status== 'Voided'?'text-gray-italic':'text-center'">{{terminalList.transaction_status}}</b-td>
                              <b-td :class="terminalList.transaction_status== 'Voided'?'text-gray-italic':'text-center'">{{terminalList.consumer_identifier}}</b-td>
                            </b-tr>
                          </b-tbody>
                        </b-table-simple>
                      </template>
                      <template #cell(total_payment)="data">
                        <div v-if="data.item.transaction_status === ''">
                          <b>{{ data.value }}</b>
                        </div>
                        <div v-else>
                          {{ data.value }}
                        </div>
                      </template>
                      <template #cell(base_amount)="data">
                        <div v-if="data.item.transaction_status === ''">
                          <b>{{ data.value }}</b>
                        </div>
                        <div v-else>
                          {{ data.value }}
                        </div>
                      </template>
                      <template #cell(tip)="data">
                        <div v-if="data.item.transaction_status === ''">
                          <b>{{ data.value }}</b>
                        </div>
                        <div v-else>
                          {{ data.value }}
                        </div>
                      </template>
                      <template #cell(trans_count)="data">
                        <div v-if="data.item.transaction_status === ''">
                          <b>{{ data.value }}</b>
                        </div>
                        <div v-else>
                          {{ data.value }}
                        </div>
                      </template>
                    </b-table>
                  </div>
                </div>
              </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</div>
</template>
<script>
import api from "@/api/reports.js";
import apiuser from "@/api/user.js";
import moment from "moment";
import { saveAs } from "file-saver";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "./CustomLoader/CanPayLoader.vue"
export default {
  data() {
    return {
      report: [],
      reportExportArr: [],
      reportHiddenArr:[],
      fields: [
        {"key":"store_name" ,"class":"text-center"},
        {"key":"store_type" ,"class":"text-center"},
        {"key":"store_category" ,"class":"text-center"},
        {"key":"total_payment" ,"class":"text-center"},
        {"key":"base_amount" ,"class":"text-center"},
        {"key":"tip" ,"class":"text-center"},
        {"key":"trans_count" ,"class":"text-center"},
      ],
      storelist: [],
      selectedStore: null,
      selectedTerminals: [],
      terminallist: [],
      void_label: "",
      toggle_void: 0,
      search_element: "",
      voided_index: [],
      total_amount_index: [],
      loading: false,
      helptext_visible: false,
      storeList: [],
      selectedStore: [],
      cpList: [],
      selectedCp: "",
      isLoadingCp: false,
      isLoadingSt: false
    };
  },
  components: {
    HourGlass,
    CanPayLoader
  },
  watch: {
    toggle_void: function (newval, oldval) {
      var self = this;
      if (newval == 0) {
        self.void_label = "Checked";
      } else {
        self.void_label = "";
      }
    },
  },
  created() {
  },
  mounted() {
    $("#start-date").datepicker({
      format: "mm/dd/yyyy",
      autoclose: true,
      todayHighlight: true,
    });
    $("#end-date").datepicker({
      format: "mm/dd/yyyy",
      autoclose: true,
      todayHighlight: true,
    });
    $("#start-date , #end-date").datepicker("setDate", new Date());

    $("#checkall").change(function () {
      $(".cb-element").prop("checked", this.checked);
    });

    $(".cb-element").change(function () {
      if ($(".cb-element:checked").length == $(".cb-element").length) {
        $("#checkall").prop("checked", true);
      } else {
        $("#checkall").prop("checked", false);
      }
    });
  },
  methods: {
    //get the list of All CP
    getAllActiveCorporateParent(searchtxt) {
      var self = this;
      if(searchtxt.length >= 3){
        self.isLoadingCp = true;
        self.cpList = [];
        var request = {
          searchtxt: searchtxt,
        };
        api
          .getAllActiveCorporateParent(request)
          .then(function (response) {
            if (response.code == 200) {
              self.cpList = response.data;
              self.storeList = [];
              self.isLoadingCp = false;
            } else {
              error(response.message);
            }
          })
          .catch(function (error) {
            error(error);
          });
      }
    },
    //get the list of All Stores
    getAllActiveStores() {
      var self = this;
      self.isLoadingSt = true;
      self.storeList = [];
      var request = {
        user_id: self.selectedCp.id,
      };
      api
        .getAllActiveStores(request)
        .then(function (response) {
          if (response.code == 200) {
            self.storeList = response.data;
            self.isLoadingSt = false;
          } else {
            error(response.message);
          }
        })
        .catch(function (error) {
          error(error);
        });
    },
    // this method returns the voided rows as gray colored rows
    rowClass(item, type) {
      if (!item || type !== "row") return;
      if (item.transaction_status === "Voided") return "text-gray";
    },
    // this method arranges the rows to show in the report table
    drawTable(raw_report, reportExport) {
      var self = this;
      // variable for the hidden section of the report
      var reportData1 = {};
      var strType = 1;
      var strCat = 2;
      var strTerm = 3;
      var strTrans = 4;
      // creating the first row with only store level information
      if (raw_report[0]["trans_count"] != 0) {
        $.each(raw_report, function (key, strVal) {
          if(strVal.hasOwnProperty("id")){
            var tbody_start = {
              store_name: strVal.store_name,
              store_type: "",
              store_category: "",
              terminalID: "",
              transaction_date: "",
              transaction_time: "",
              transaction_no:"",
              total_payment: strVal.total_payment,
              base_amount: strVal.base_amount,
              tip: strVal.tip,
              trans_count: strVal.trans_count,
              transaction_status: "",
              consumer_identifier: "",
            };
        //For Report
        self.report.push(tbody_start);
        //For Excel Export
        self.reportExportArr.push(tbody_start);
        // creating the second row which is repeatative for store type
        $.each(raw_report[strType], function (key, value) {
          tbody_start = {
            store_name: "",
            store_type: value.store_type,
            store_category: "",
            terminalID: "",
            transaction_date: "",
            transaction_time: "",
            transaction_no:"",
            total_payment: value.total_payment,
            base_amount: value.base_amount,
            tip: value.tip,
            trans_count: value.trans_count,
            transaction_status: "",
            consumer_identifier: "",
          };
          //For Report
          self.report.push(tbody_start);
          //For Excel Export
          self.reportExportArr.push(tbody_start);
          self.reportHiddenArr = [];
          // creating the third row which is repeatative for store category
          $.each(raw_report[strCat], function (k, v) {
            if (value.store_type == v.store_type) {
              self.reportHiddenArr = [];
              $.each(raw_report[strTerm], function (a, val) {
                if (
                  val.store_category == v.store_category &&
                  val.store_type == v.store_type
                ) {
                  reportData1 = {
                    store_name: "",
                    store_type: "",
                    store_category: "",
                    terminalID: val.terminalID,
                    transaction_date: "",
                    transaction_time: "",
                    transaction_no:"",
                    total_payment: val.total_payment,
                    base_amount: val.base_amount,
                    tip: val.tip,
                    trans_count: val.trans_count,
                    transaction_status: "",
                    consumer_identifier: "",
                  };
                  self.reportHiddenArr.push(reportData1);
                  self.voided_index_hidden = [];
                  // creating the terminal listing (Excel Export)
                  $.each(raw_report[strTrans], function (x, y) {
                    if (
                      val.store_category == y.store_category &&
                      val.store_type == y.store_type &&
                      val.terminalID == y.terminalID
                    ) {
                      reportData1 = {
                        store_name: "",
                        store_type: "",
                        store_category: "",
                        terminalID: "",
                        transaction_date: moment(y.transaction_time).format(
                          "MM/DD/YYYY"
                        ),
                        transaction_time: value.ecommerce_store == 0 ? moment(y.transaction_time).format(
                            "hh:mm A"
                          ) : "",
                        transaction_no:y.transaction_no,
                        total_payment: y.total_payment,
                        base_amount: y.base_amount,
                        tip: y.tip,
                        trans_count: y.trans_count,
                        transaction_status: y.transaction_status,
                        consumer_identifier: y.consumer_identifier,
                      };
                      self.reportHiddenArr.push(reportData1);
                    }
                  });
                }
              });
              //For Report
              tbody_start = {
                store_name: "",
                store_type: "",
                store_category: v.store_category,
                terminalID: "",
                transaction_date: "",
                transaction_time: "",
                transaction_no:"",
                total_payment: v.total_payment,
                base_amount: v.base_amount,
                tip: v.tip,
                trans_count: v.trans_count,
                transaction_status: "",
                consumer_identifier: "",
                report_data1:self.reportHiddenArr,
                report_data2:raw_report[strTrans],
              };
              self.report.push(tbody_start);
              //For Excel Export
              var tbody_start_excel = {
                store_name: "",
                store_type: "",
                store_category: v.store_category,
                terminalID: "",
                transaction_date: "",
                transaction_time: "",
                transaction_no:"",
                total_payment: v.total_payment,
                base_amount: v.base_amount,
                tip: v.tip,
                trans_count: v.trans_count,
                transaction_status: "",
                consumer_identifier: "",
              };
              self.reportExportArr.push(tbody_start_excel);
              // creating the fourth row which is repeatative for specific terminal (Excel Export)
              $.each(raw_report[strTerm], function (a, val) {
                if (
                  val.store_category == v.store_category &&
                  val.store_type == v.store_type
                ) {
                  tbody_start_excel = {
                    store_name: "",
                    store_type: "",
                    store_category: "",
                    terminalID: val.terminalID,
                    transaction_date: "",
                    transaction_time: "",
                    transaction_no:"",
                    total_payment: val.total_payment,
                    base_amount: val.base_amount,
                    tip: val.tip,
                    trans_count: val.trans_count,
                    transaction_status: "",
                    consumer_identifier: "",
                  };
                  self.reportExportArr.push(tbody_start_excel);
                  self.voided_index = [];
                  // creating the terminal listing (Excel Export)
                  $.each(raw_report[strTrans], function (x, y) {
                    if (
                      val.store_category == y.store_category &&
                      val.store_type == y.store_type &&
                      val.terminalID == y.terminalID
                    ) {
                      tbody_start_excel = {
                        store_name: "",
                        store_type: "",
                        store_category: "",
                        terminalID: "",
                        transaction_date: moment(y.transaction_time).format(
                          "MM/DD/YYYY"
                        ),
                        transaction_time: value.ecommerce_store == 0 ? moment(y.transaction_time).format(
                            "hh:mm A"
                          ) : "",
                        transaction_no:y.transaction_no,
                        total_payment: y.total_payment,
                        base_amount: y.base_amount,
                        tip: y.tip,
                        trans_count: y.trans_count,
                        transaction_status: y.transaction_status,
                        consumer_identifier: y.consumer_identifier,
                      };
                      self.reportExportArr.push(tbody_start_excel);
                    }
                  });
                }
              });
            }
          });
        });

          strType = strType+5;
          strCat = strCat+5;
          strTerm = strTerm+5;
          strTrans = strTrans+5;
          }
        });
        if (reportExport) {
          self.exportReport();
        } else {
          self.loading = false;
        }
      } else {
        error("No records found!");
        self.loading = false;
      }
    },
    // API call to generate the merchant location transaction report
    generateReport(reportExport) {
      var self = this;
      if (self.selectedCp == "") {
        error("Please select a Corpoarate Parent in order to generate the report.");
        return false;
      }
      if(self.selectedStore.length == 0){
        error("Please select a store in order to generate the report.");
        return false;
      }
      if (
        moment($("#start-date").val()).format("YYYY-MM-DD") >
        moment($().val()).format("YYYY-MM-DD")
      ) {
        error("Start date cannot be from future.");
        return false;
      }
      if (
        moment($("#end-date").val()).format("YYYY-MM-DD") >
        moment($().val()).format("YYYY-MM-DD")
      ) {
        error("End date cannot be from future.");
        return false;
      }
      var today = moment();
      if(moment($("#end-date").val()).isSame(today, 'day')){
        self.helptext_visible = true;
      }else{
        self.helptext_visible = false;
      }

      var stores = [];
      if (self.selectedStore != []) {
        $.each(self.selectedStore, function (key, value) {
          stores.push(value.id);
        });
      }

      self.report = [];
      self.reportExportArr = [];
      var request = {
        from_date: moment($("#start-date").val()).format("YYYY-MM-DD"),
        to_date: moment($("#end-date").val()).format("YYYY-MM-DD"),
        show_void: self.toggle_void,
        store_id: stores,
        user_id: self.selectedCp.id,
        initiated_by: 'Admin'
      };
      if(request.from_date > request.to_date){
        error("To Date cannot be greater than From date");
        return false;
      }
      self.loading = true;
      api
        .generateMerchantTransactionReport(request)
        .then(function (response) {
          if (response.code == 200) {
            self.drawTable(response.data, reportExport);
          } else {
            error(response.message);
            self.loading = false;
          }
        })
        .catch(function (error) {
          // error(error);
          self.loading = false;
        });
    },
    // exports the report
    exportReport() {
      var self = this;
      self.loading = true;
      var request = {
        from_date: moment($("#start-date").val()).format("YYYY-MM-DD"),
        to_date: moment($("#end-date").val()).format("YYYY-MM-DD"),
        report: self.reportExportArr,
        voided_index: self.voided_index,
        total_amount_index: self.total_amount_index,
        initiated_by: 'Admin'
      };
      var count = self.report[2].report_data1.length;
      $.each(self.reportExportArr, function (key, value) {
        if (value.transaction_status === "Voided") {
          if(count <= count+2){
            self.voided_index.push(key);
          }else{
            self.voided_index.push(key+3);
          }
        } else if (value.transaction_status === "") {
          self.total_amount_index.push(key);
        }
      });
      api
        .getMerchantTransactionExport(request)
        .then(function (response) {
          var FileSaver = require("file-saver");
          var blob = new Blob([response], {
            type: "application/xlsx",
          });
          FileSaver.saveAs(
            blob,
            moment().format("MM/DD/YYYY") +
              "_CanPay_Transaction_Report.xlsx"
          );
          self.loading = false;
        })
        .catch(function (error) {
          // error(error);
          self.loading = false;
        });
    },
    showAllDetails() {
      this.report.forEach((item) => {
        if(item.store_category!=""){
          this.$set(item, '_showDetails', true)
        }
      })
    },
    hideAllDetails() {
      this.report.forEach((item) => {
        this.$set(item, '_showDetails', false)
      })
    },
    customLabel(terminal) {
      return `${terminal.terminal_name}`;
    },
    dateDiff(){
      var self = this;
      if ($("#start-date").val() != "") {
        var from_date = moment($("#start-date").val()).format("YYYY-MM-DD");
      } else {
        var from_date = "";
      }
      if ($("#end-date").val() != "") {
        var to_date = moment($("#end-date").val()).format("YYYY-MM-DD");
      } else {
        var to_date = "";
      }
      if(from_date!='' && to_date!=''){
        //calculate the date Difference
        var date1 = new Date(from_date);
        var date2 = new Date(to_date);
        // To calculate the time difference of two dates
        var Difference_In_Time = date2.getTime() - date1.getTime();

        // To calculate the no. of days between two dates
        var Difference_In_Days = Difference_In_Time / (1000 * 3600 * 24);

        if(Difference_In_Days > 60 || self.selectedStore.length!=1){
          $("#generateBtn").prop('disabled', true);
        }else{
          $("#generateBtn").prop('disabled', false);
        }
      }
    },
    reset(){
      var self = this;
      self.toggle_void = 0;
      self.selectedCp = "";
      self.selectedStore = [];
    },
    refreshStores(selectedOption){
      var self = this;
      self.storeList = [];
      self.selectedStore = [];
      self.selectedCp = selectedOption;
      self.getAllActiveStores();
    }
  },
};
</script>
