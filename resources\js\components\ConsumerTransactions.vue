<template>
<div>
  <div  v-if="loading">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px;">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Consumer Transaction History</h3>
                </div>

                <div class="card-body">
                <div class="row">
                  <div class="col-md-4">
                    <div class="form-group">
                      <multiselect
                        v-model="selectedConsumer"
                        placeholder="Select Consumer (Min 3 chars)"
                        id="consumer"
                        label="consumer_name"
                        :options="consumerList"
                        :loading="isLoading"
                        :internal-search="false"
                        @search-change="getConsumers"
                          >
                    </multiselect>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="form-control"
                        placeholder="Phone No."
                        id="phone_no"
                        v-model="phone_no"
                      />
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="form-control"
                        placeholder="Email"
                        id="email"
                        v-model="email"
                      />
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="start-date form-control"
                        placeholder="Start Date"
                        id="start-date"
                        onkeydown="return false"
                        autocomplete="off"
                      />
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="end-date form-control"
                        placeholder="End Date"
                        id="end-date"
                        onkeydown="return false"
                        autocomplete="off"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div class="card-footer">
                <button
                  type="button"
                  class="btn btn-success"
                  @click="generateReport(false)"
                >
                  Generate
                </button>
                <button
                  type="button"
                  @click="generateReport(true)"
                  class="btn btn-danger ml-10"
                >
                  Generate & Export<i
                    class="fa fa-download ml-10"
                    aria-hidden="true"
                  ></i>
                </button>
              </div>
                <!-- /.card-header -->
                <div class="card-body">
                  <div class="row">
                  <div class="col-12"><b-table-simple
                      responsive
                      show-empty
                      bordered
                      sticky-header="800px"
                    >
                      <b-thead head-variant="light">
                        <b-tr>
                          <b-th class="text-center">Transaction Number</b-th>
                          <b-th class="text-center">Consumer</b-th>
                          <b-th class="text-center">Merchant</b-th>
                          <b-th class="text-center">Store</b-th>
                          <b-th class="text-center">Terminal</b-th>
                          <b-th width="12%" class="text-center">Amount ($)</b-th>
                          <b-th width="14%" class="text-center">Bank Posting Amount ($)</b-th>
                          <b-th width="14%" class="text-center">Reward Point Amount ($)</b-th>
                          <b-th width="12%" class="text-center">Tip Amount ($)</b-th>
                          <b-th width="12%" class="text-center">Delivery Fee ($)</b-th>
                          <b-th class="text-center">Transaction Time</b-th>
                          <b-th class="text-center">Status</b-th>
                          <b-th class="text-center">Cancel Transaction</b-th>
                        </b-tr>
                      </b-thead>
                      <b-tbody v-for="(row, index) in report" :key="index">
                        <b-tr>
                          <b-td class="text-left text-gray">{{
                            row.transaction_number
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                           checkSpace(row.consumer_name)
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.merchant_name
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.store_name
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.terminal_name
                          }}</b-td>
                          <b-td class="text-left text-gray">
                            <p v-if="row.attempt_count > 0">${{row.amount}} ... ${{row.updated_amount}}<i class="nav-icon fas fa-eye custom-edit-btn" @click="modifiedTransactionHistory(row.edit, row.transaction_number)"></i>
                            </p>
                            <p v-else>${{row.amount}}</p>
                          </b-td>
                          <b-td class="text-left text-gray">
                            <p>${{row.consumer_bank_posting_amount == NULL ? '0.00': row.consumer_bank_posting_amount}}</p>
                          </b-td>
                          <b-td class="text-left text-gray">
                            <p>${{row.reward_amount_used == NULL ? '0.00' : row.reward_amount_used}}</p>
                          </b-td>
                          <b-td class="text-left text-gray">${{
                            row.last_approve_tip_amount
                          }}</b-td>
                          <b-td class="text-left text-gray">${{
                            row.delivery_fee
                          }}</b-td>
                          <b-td v-html="row.transaction_time" class="text-left text-gray"></b-td>
                          <b-td class="text-left text-gray">{{
                            row.status
                          }}</b-td>
                          <b-td class="text-left text-gray" v-if="row.cancelable  && row.status != 'Voided'">
                            <a style="color: white !important" class="btn btn-danger consumer-tr-leave-comment" :data-id="row.edit" @click="$bvModal.show('comment-modal')">Cancel</a></b-td>
                          <b-td class="text-left text-gray" v-else></b-td>
                        </b-tr>
                      </b-tbody>
                    </b-table-simple>
                  </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
    <!-- Transaction cancellation comment modal start -->
    <b-modal
      id="comment-modal"
      ref="comment-modal"
      ok-title="Save & Cancel"
      cancel-title="Close"
      ok-variant="success"
      @ok="cancelTransaction"
      cancel-variant="outline-secondary"
      hide-header
      :no-close-on-esc="true"
      :no-close-on-backdrop="true"
    >
      <div class="row">
        <div class="col-12">
          <label for="comment">
            Tell us why you want to cancel this transaction
            <span class="red">*</span>
          </label>
          <textarea name="comment" type="text" v-model="comment" class="form-control" />
        </div>
        <input type="text" v-model="transaction_id" hidden />
      </div>
      <div class="row" v-if="showMsg">
        <div class="col-12">
          <label for="comment" class="red">Please fill in the required fields.</label>
        </div>
      </div>
      <div class="row" style="margin-bottom: 40px;"></div>
    </b-modal>
    <!-- Transaction cancellation comment modal end -->
    <!-- Transaction modification history modal start -->
    <b-modal
      id="modified-tr-history-modal"
      ref="modified-tr-history-modal"
      ok-only
      cancel-variant="outline-secondary"
      :header-text-variant="headerTextVariant"
      :title="historyModalTitle"
      :no-close-on-esc="true"
      :no-close-on-backdrop="true"
    >
      <div class="row">
          <div class="col-md-12">
            <b-table-simple
            class="cp-table"
            responsive
            show-empty
            bordered
            >
                <b-thead head-variant="light">
                    <tr>
                        <th width="15%">Amount ($)</th>
                        <th width="20%">Tip Amount ($)</th>
                        <th width="20%">Modify Time</th>
                        <th width="20%">Reason</th>
                        <th width="20%">Additional Reason</th>
                        <th width="15%">Status</th>
                    </tr>
                </b-thead>
                <b-tbody v-for="(row, index) in transactionHistory" :key="index">
                   <b-tr>
                          <b-td class="text-left text-gray">${{
                            row.amount
                          }}</b-td>
                          <b-td class="text-left text-gray">${{
                            row.tip_amount
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.local_transaction_time
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.reason
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.additional_reason
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.status
                          }}</b-td>
                        </b-tr>
                </b-tbody>
            </b-table-simple>
          </div>
        </div>
    </b-modal>
    <!-- Transaction modification history modal end -->
  </div>
</div>
</template>
<script>
import api from "@/api/transaction.js";
import moment from "moment";
import { validationMixin } from "vuelidate";
import { required, minLength } from "vuelidate/lib/validators";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "./CustomLoader/CanPayLoader.vue"
export default {
  mixins: [validationMixin],
  data() {
    return {
      allTransactionModel: {},
      showMsg: false,
      transaction_id: null,
      comment: "",
      currentUser: localStorage.getItem("user")
        ? JSON.parse(localStorage.getItem("user"))
        : null,
      loading: false,
      report: [],
      consumerList: [],
      transactionHistory: [],
      selectedConsumer: null,
      email:"",
      phone_no:"",
      isLoading: false,
      updatedDetails:{},
      headerTextVariant: "light",
      historyModalTitle: "Transaction history",
    };
  },
  components: {
    HourGlass,
    CanPayLoader
  },
  created() {
    this.consumerTrLeaveComment();
  },
  methods: {
  checkSpace(sentence) {
    let full_name = "";
    for(let curr_char of sentence){
      if(full_name.length>0 && full_name[full_name.length-1] == ' ' && curr_char == ' '){
        continue;
      }
      full_name+=curr_char;
    }
    return full_name;
  },
    //get the consumer list
    getConsumers(searchtxt) {
      var self = this;
      if(searchtxt.length >= 3){
        self.isLoading = true;
        var request = {
          searchtxt: searchtxt,
        };
        api
          .getConsumers(request)
          .then(function (response) {
            if (response.code == 200) {
              self.consumerList = response.data;
              self.isLoading = false;
            } else {
              error(response.message);
            }
          })
          .catch(function (error) {
            error(error);
          });
      }
    },
     modifiedTransactionHistory(transaction_id, transaction_number){

      var self = this;
      self.transactionHistory = [];
      self.historyModalTitle = "Transaction history for " +transaction_number;
      self.loading = true;
      var request = {
        transaction_id: transaction_id,
      };
      api
        .modifiedTransactionHistory(request)
        .then((response) => {
          if ((response.code = 200)) {
            self.transactionHistory = response.data;
            self.$bvModal.show("modified-tr-history-modal");
            success(response.message);
          } else {
            error(response.message);
          }
          self.loading = false;
        })
        .catch((err) => {
          self.loading = false;
          error(err);
        });
    },
    // API call to generate the merchant location transaction report
    generateReport(reportExport) {
      var self = this;
      if(self.selectedConsumer === null && $("#phone_no").val() == '' &&  $("#email").val() == ''){
        error("Please select either customer or phone no. or email.");
        return false;
      }
      if($("#start-date").val()!=''){
        var from_date = moment($("#start-date").val()).format("YYYY-MM-DD");
      }else{
        var from_date = '';
      }
      if($("#end-date").val()!=''){
        var to_date = moment($("#end-date").val()).format("YYYY-MM-DD");
      }else{
        var to_date = '';
      }

      if(self.selectedConsumer === null){
        var consumer = '';
      }else{
        var consumer = self.selectedConsumer.user_id;
      }
      self.report = [];
      var request = {
        from_date: from_date,
        to_date: to_date,
        consumer: consumer,
        email:self.email,
        phone_no:self.phone_no
      };
      if(request.from_date > request.to_date){
        error("To Date cannot be greater than From date");
        return false;
      }
      self.loading = true;
      api
        .generateConsumerTransactionReport(request)
        .then(function (response) {
          if (response.code == 200) {
            self.report = response.data;
            if(self.report.length > 0){
              if (reportExport) {
                self.exportReport();
              } else {
                self.loading = false;
              }
            }else {
              error("No records found!");
              self.loading = false;
            }
          } else {
            error(response.message);
            self.loading = false;
          }
        })
        .catch(function (error) {
          // error(error);
          self.loading = false;
        });
    },

    // exports the report
    exportReport() {
      var self = this;
      self.loading = true;

      if($("#start-date").val()!=''){
        var from_date = moment($("#start-date").val()).format("YYYY-MM-DD");
      }else{
        var from_date = '';
      }
      if($("#end-date").val()!=''){
        var to_date = moment($("#end-date").val()).format("YYYY-MM-DD");
      }else{
        var to_date = '';
      }

      var request = {
        report: self.report,
      };
      api
        .exportgenerateConsumerTransactionReport(request)
        .then(function (response) {
          var FileSaver = require("file-saver");
          var blob = new Blob([response], {
            type: "application/xlsx",
          });
          FileSaver.saveAs(
            blob,
            moment().format("MM/DD/YYYY") + "_consumer_transaction_report.xlsx"
          );
          self.loading = false;
        })
        .catch(function (error) {
          // error(error);
          self.loading = false;
        });
    },
    consumerTrLeaveComment() {
      var self = this;
      $(document).on("click", ".consumer-tr-leave-comment", function (e) {
        //open the modal
        self.comment = "";
        // self.$refs["comment-modal"].show();
        //set transaction id to a hidden field for future use
        self.transaction_id = $(e.currentTarget).attr("data-id");
      });
    },
    cancelTransaction(bvModalEvt) {
      var self = this;
      self.updatedDetails = self.report.find(
            p => p.edit == self.transaction_id
        );
      if (self.comment == "") {
        self.showMsg = true;
        bvModalEvt.preventDefault();
      } else {
        self.showMsg = false;
        var request = {
          transaction_id: self.transaction_id,
          comment: self.comment,
          transaction_place: "Admin",
        };
        api
          .cancelTransaction(request)
          .then((response) => {
            if ((response.code = 200)) {
              success(response.message);
              self.$refs["comment-modal"].hide();
              self.comment == "";
              self.updatedDetails.status = 'Voided';
              self.updatedDetails.cancelable = 0;
            } else {
              error(response.message);
            }
          })
          .catch((err) => {
            error(err);
          });
      }
    },
  },
  mounted() {

    $("#start-date").datepicker({
      format: "mm/dd/yyyy",
      autoclose: true,
      todayHighlight: true,
    });
    $("#end-date").datepicker({
      format: "mm/dd/yyyy",
      autoclose: true,
      todayHighlight: true,
    });
    $("#start-date , #end-date").datepicker("setDate", new Date());
  },
};
</script>

