<template>
<div>
  <div v-if="loading">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Reward Wheel Send Invitation</h3>
                </div>
                <div class="card-body">
                  <div class="card" style="box-shadow: none;">
                    <div class="card-body">
                      <h6>Search by State, ZIP</h6>
                      <div class="row">
                        <div class="col-md-4">
                          <div class="form-group mb-0">
                              <multiselect
                              v-model="state"
                              :options="states"
                              :custom-label="stateCustomName"
                              :multiple="true"
                              ref="state"
                              placeholder="Select State"
                              track-by="state">
                              </multiselect>
                              <span v-show="errors.has('state')" class="text-danger">{{
                              errors.first("state")
                              }}</span>
                          </div>
                        </div>
                        <div class="col-md-4">
                          <div class="form-group mb-0">
                            <input
                              class="form-control"
                              placeholder="Zip"
                              id="zip"
                              v-model="zip"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="card" style="box-shadow: none;">
                    <div class="card-body">
                      <h6>Search By Consumer Status</h6>
                      <div class="row">
                        <div class="col-md-4">
                          <div class="form-group mb-0">
                            <multiselect
                            v-model="consumer_status"
                            :options="statusList"
                            :custom-label="stateCustomStatus"
                            :multiple="false"
                            ref="consumer_status"
                            placeholder="Select Consumer Status"
                            track-by="status">
                            </multiselect>
                              <span v-show="errors.has('consumer_status')" class="text-danger">{{
                              errors.first("consumer_status")
                              }}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="card" style="box-shadow: none;">
                    <div class="card-body">
                      <h6>Consumer Details(Email or Phone)</h6>
                      <div class="row">
                        <div class="col-md-4 d-flex align-items-end">
                          <div class="form-group mb-0 w-100">
                            <input
                              class="form-control"
                              placeholder="Phone No (Exact)"
                              id="phone_no"
                              v-model="phone_no"
                            />
                          </div>
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                          <div class="form-group mb-0 w-100">
                            <input
                              class="form-control"
                              placeholder="Email (Exact)"
                              id="email"
                              v-model="email"
                            />
                          </div>
                        </div>
                        <div class="col-md-4">
                          <label for=""><small>User Type: </small></label>
                          <div class="row m-0">
                            <div class="d-flex mr-3">
                              <input class="control__indicator mr-1" type="checkbox" name="user_type_v1" v-model="user_type_v1" value="v1"> <span>V1</span>
                            </div>
                            <div class="d-flex">
                              <input class="control__indicator mr-1" type="checkbox" name="user_type_v2" v-model="user_type_v2" value="v2"> <span>V2</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="card-footer">
                  <button
                    type="button"
                    @click="searchRWRegisteredConsumerAndInvite()"
                    class="btn btn-success margin-left-5"
                  >
                    Invite Selected Consumers
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</div>
</template>
<script>
import moment from "moment";
import api from "@/api/user.js";
import rewardwheelapi from "@/api/user.js";
import { validationMixin } from "vuelidate";
import { required, minLength,decimal } from "vuelidate/lib/validators";
import commonConstants from "@/common/constant.js";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "../CustomLoader/CanPayLoader.vue"
export default {
  mixins: [validationMixin],
  data() {
    return {
        state: [],
        zip:"",
        consumer_status:"",
        phone_no:"",
        email:"",
        user_type_v1:"",
        user_type_v2:"",
        states: [],
        statusList: [],
        loading:false,
        consumers: [],
    };
  },
  components: {
    HourGlass,
    CanPayLoader
  },
  methods: {
    loadInitialData(){
      var self = this;
      self.loading = true
      Promise.all([
        self.getUserStatus(),
        self.getAllStates()
      ])
      .then(response => {
        self.loading = false
      })
      .catch(errors => {
        self.loading = false
      });
    },
    getUserStatus() {
      return new Promise((resolve, reject) => {
      var self = this;
      api
        .getUserStatus()
        .then((response) => {
          if ((response.code = 200)) {
            self.statusList = response.data;
            resolve(true)
          } else {
            error(response.message);
            reject(false)
          }
        })
        .catch((err) => {
          error(err);
          reject(false)
        });
      })
    },
    getAllStates(){
      return new Promise((resolve, reject) => {
        var self = this;
        rewardwheelapi
          .getAllStates()
          .then((response) => {
            if ((response.code = 200)) {
              self.states = response.data;
              resolve(true)
            } else {
              error(response.message);
              reject(false)
            }
          })
          .catch((err) => {
            error(err);
            reject(false)
          });
      })
    },
    stateCustomName (data) {
    return data.state
    },
    stateCustomStatus (data) {
    return data.status
    },
    reset(){
      var self = this;
      self.state = ''
      self.zip = ''
      self.consumer_status = ''
      self.phone_no = ''
      self.email = ''
      self.user_type_v1 = ''
      self.user_type_v2 = ''
      self.consumers = []
    },
    // API call to fetch all consumers
    searchRWRegisteredConsumerAndInvite() {
      var self = this;

      if(
        self.state == '' && self.zip == '' && self.consumer_status == '' && self.phone_no == '' && self.email == '' && self.user_type_v1 == '' && self.user_type_v2 == ''
      ){
        error('Please fill atleast one field.');
        return
      }

      Vue.swal({
      title: "Are you sure to invite the consumer(s)?",
      text: "Once done you will not be able to undo this action!",
      type: "warning",
      showCancelButton: true,
      confirmButtonColor: "#149240",
      confirmButtonText: "Yes, send it!",
      cancelButtonText: "No, cancel it!",
      closeOnConfirm: true,
      closeOnCancel: true,
      }).then((result) => {
        if (result.isConfirmed) {

          var states = []
          if(self.state.length > 0){
            self.state.forEach(val => {
              states.push(val.state)
            });
          }else{
            states = ''
          }

          var request = {
            state: states,
            zip: self.zip,
            consumer_status: self.consumer_status,
            phone_no: self.phone_no,
            email: self.email,
            user_type_v1: self.user_type_v1 ? 1 : 0,
            user_type_v2: self.user_type_v2 ? 1 : 0
          };
        
          self.loading = true;
          api
            .searchRWRegisteredConsumerAndInvite(request)
            .then(function (response) {
              if (response.code == 200) {

                self.$swal(response.message, "", "success")
              
                self.loading = false;
              } else {
                self.$swal({
                  title: response.message,
                  width: 600,
                  icon: 'error'
                })
                self.loading = false;
              }
            })
            .catch(function (error) {
              self.$swal({
                title: error.response.data.message,
                width: 600,
                icon: 'error'
              })
              self.loading = false;
            });
          }
      })
    }
  },
  mounted() {
    var self = this;
    self.loadInitialData();
    document.title = "CanPay - Reward Wheel Send Invitation";
  },
};
</script>

<style lang="css" scoped>
.control__indicator{
  cursor: pointer;
}
</style>