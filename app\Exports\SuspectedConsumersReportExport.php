<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Events\AfterSheet;

class SuspectedConsumersReportExport implements FromCollection, WithHeadings, ShouldAutoSize, WithEvents
{
    protected $request;

    public function __construct($request)
    {
        $this->request = $request; // Declaring the request variable
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        $collection_array = $this->request['report']; // Storing the array received from requests
        return collect([
            $collection_array,
        ]);
    }

    public function headings(): array
    {
        $returnArray = array(
            [
                'CanPay Suspected Consumers List',
            ],
            [],
            [
                'Name',
                'Email',
                'Phone',
                'Age',
                'Address',
                'Enroll Date',
                'Bank Link Type',
                'Routing No.',
                'Account No.',
            ],
        );

        return $returnArray;
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $event->sheet->getStyle('A1:B1')->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);

                $event->sheet->getStyle('A3:I3')->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);

                //Apply Center Alignment
                $event->sheet->getStyle('A:I')->getAlignment()->applyFromArray(
                    array('horizontal' => 'center')
                );

                $event->sheet->mergeCells('A1:B1');
            },
        ];
    }
}
