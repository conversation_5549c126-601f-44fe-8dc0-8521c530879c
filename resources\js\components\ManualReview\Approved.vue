<template>
<div>
  <div v-if="loading">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Review Identity</h3>
                </div>

                <!-- /.card-header -->
                <div class="card-body">
                  <div class="row">
                      <div class="col-md-4">
                    <div class="form-group">
                     <input
                        class="form-control"
                        placeholder="Consumer Name (min 3 chars)"
                        id="consumer"
                        v-model="consumer"
                      />
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="form-control"
                        placeholder="Phone No (Exact)"
                        id="phone_no"
                        v-model="phone_no"
                      />
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="form-control"
                        placeholder="Email (Exact)"
                        id="email"
                        v-model="email"
                      />
                    </div>
                  </div>
                    <div class="col-md-4">
                        <div class="form-group">
                        <input
                            class="start-date form-control"
                            placeholder="Start Date"
                            id="start-date"
                            autocomplete="off"
                        />
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                        <input
                            class="end-date form-control"
                            placeholder="End Date"
                            id="end-date"
                            autocomplete="off"
                        />
                        </div>
                    </div>
                  </div>
                  <p class="red" v-show="text_show">Search will not work if the difference between start and end days is greater than 30 days</p>
                </div>
                  <div class="card-footer">
                    <button
                      type="button"
                      class="btn btn-success"
                      id="searchBtn"
                      @click="searchConsumers()"
                    >
                      Search
                    </button>
                    <button
                      type="button"
                      @click="reset()"
                      class="btn btn-success margin-left-5"
                    >
                      Reset
                    </button>
                  </div>
                  <div class="card-body">
                  <b-table-simple
                      responsive
                      show-empty
                      bordered
                      sticky-header="800px"
                      v-if="allDetails.length > 0"
                    >
                      <b-thead head-variant="light">
                        <tr>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Phone</th>
                            <th class="text-center">Created On</th>
                            <th class="text-center">Action(s)</th>
                        </tr>
                      </b-thead>
                      <b-tbody v-for="(row, index) in allDetails" :key="index">
                        <b-tr>
                          <b-td class="text-left text-gray">{{
                            row.name
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.email
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.phone
                          }}</b-td>
                          <b-td class="text-center text-gray">{{
                            row.created_at
                          }}</b-td>
                          <b-td class="text-center text-gray">
                            <a :data-id="row.edit" :data-session="row.session_id" class="get-scores custom-edit-btn" title="Change Status" variant="outline-success"><i class="nav-icon fas fa-edit"></i></a></b-td>
                        </b-tr>
                      </b-tbody>
                    </b-table-simple>
                    <p v-else>No data displayed. Please refine your search criteria.</p>
                    </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
    <!-- Edit Modal Start -->
    <b-modal
      id="approved-edit-modal"
      ref="approved-edit-modal"
      hide-footer
      title="More Identity Validation Details"
      :no-close-on-esc="false"
      :no-close-on-backdrop="false"
    >
      <div class="row">
        <div class="col-12">
          <span>
            <b>Consumer Identity Document:</b>
          </span>
        </div>
      </div>
      <hr />
      <div class="row">
        <div class="col-6" style="text-align: center">
          <img
            slot="image"
            disabled
            v-bind:src="imagesrcfront"
            class="id-preview"
          />
        </div>
        <div class="col-6" style="text-align: center">
          <img
            slot="image"
            disabled
            v-bind:src="imagesrcback"
            class="id-preview"
          />
        </div>
      </div>
      <hr />
      <div class="row">
        <div class="col-12">
          <span>
            <b>Consumer Input:</b>
          </span>
        </div>
      </div>
      <hr />
      <div class="row">
        <div class="col-4">
          <span>
            <b>Name:</b>
            {{ input_name }}
          </span>
        </div>
        <div class="col-4">
          <span>
            <b>Mobile Number:</b>
            {{ input_phone }}
          </span>
        </div>
        <div class="col-4">
          <span>
            <b>Address:</b>
            {{ input_address }}
          </span>
        </div>
      </div>
      <div class="row">
        <div class="col-4">
          <span>
            <b>Date Of Birth:</b>
            {{ input_dob }}
          </span>
        </div>
        <div class="col-4">
          <span>
            <b>Last 4 Digit Of SSN:</b>
            {{ input_ssn }}
          </span>
        </div>
      </div>
      <br />
      <div class="row">
        <div class="col-12">
          <span>
            <b>Cognito Output:</b>
          </span>
        </div>
      </div>
      <hr />
      <div class="row">
        <div class="col-6">
          <span>
            <b>Maximum Average Score:</b>
            {{ average_score }}
          </span>
        </div>
      </div>
      <div class="row">
        <div class="col-6">
          <span>
            <b>Risk Score:</b>
            {{ risk_score }}
          </span>
        </div>
      </div>
      <div class="row">
        <div class="col-12">
          <span>
            <b>Outcome Reason:</b>
            {{ reason }}
          </span>
        </div>
      </div>
      <div class="row" v-if="items.length > 0">
        <div class="col-12">
          <b-table striped hover :items="items" :fields="fields"></b-table>
        </div>
      </div>
    </b-modal>
    <!-- Edit Modal End -->
        <b-modal
      id="approved-error-modal"
      ref="approved-error-modal"

      title="More Identity Validation Details"
      :no-close-on-esc="false"
      :no-close-on-backdrop="false"
       hide-footer
    >
      <div class="row">
        <div class="col-12">
          <span>
            <b>Consumer Identity Document:</b>
          </span>
        </div>
      </div>
      <hr />
      <div class="row">
        <div class="col-6" style="text-align: center">
          <img
            slot="image"
            disabled
            v-bind:src="imagesrcfront"
            class="id-preview"
          />
        </div>
        <div class="col-6" style="text-align: center">
          <img
            slot="image"
            disabled
            v-bind:src="imagesrcback"
            class="id-preview"
          />
        </div>
      </div>
    <div class="modal-footer">
      <div class="row">
        <div class="col-8">
        <div class="row">
        <div class="col-1 mt-1">
  <svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="20" height="20" fill="#000000" viewBox="0 0 90 90" xml:space="preserve" style="float: left;"><path d="M45,0C20.1,0,0,20.1,0,45s20.1,45,45,45s45-20.1,45-45C90,20.2,69.8,0,45,0z M45,82C24.6,82,8,65.4,8,45S24.6,8,45,8
	s37,16.6,37,37S65.4,82,45,82z M40,20c0-2.8,2.2-5,5-5s5,2.2,5,5s-2.2,5-5,5S40,22.8,40,20z M56,65.4V68c0,1.1-0.9,2-2,2H36
	c-1.1,0-2-0.9-2-2v-2.6c0-0.9,0.6-1.6,1.4-1.9l4.1-1.3c0.3-0.1,0.5-0.4,0.5-0.7V41h-3.1c-1.6,0-2.9-1.3-2.9-2.9c0-1.2,0.8-2.3,2-2.7
	l11.4-3.6c1.1-0.3,2.2,0.3,2.5,1.3c0.1,0.2,0.1,0.4,0.1,0.6v27.8c0,0.3,0.2,0.6,0.5,0.7l4.1,1.3C55.4,63.7,56,64.5,56,65.4z"></path></svg>
  </div><div class="col-11"><span class="float-left" style="color: black; font-size: 0.85rem; margin-left: 0.5rem;"><span class="align-top">If the complete assessment record is not</span>
   <span class="align-top">visible then please click </span><span class="align-top">on the refresh button.</span></span>
   </div></div></div>
   <div class="col-4">
          <b-button
         
          variant="outline-secondary"
          @click="Refresh"
        ><i class="nav-icon fas fa-redo"></i>&nbsp;Refresh</b-button>
        </div>
        </div>
      </div>
    </b-modal>
  </div>
</div>
</template>
<script>
import api from "@/api/review.js";
import moment from "moment";
import commonConstants from "@/common/constant.js";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "../CustomLoader/CanPayLoader.vue"
export default {
  data() {
    return {
      fields: ["source", "score"],
      items: [],
      review_flag: "",
      modalTitle: "",
      options: [
        { code: 202, status: "Pending" }, //code is based on db status code
        { code: 601, status: "Approve" }, //code is based on db status code
        { code: 600, status: "Archive" }, //code is based on db status code
      ],
      review_status: { code: 202, status: "Pending" }, //code is based on db status code
      record_id: "",
      allDetails: {},
      imagesrcfront: "",
      imagesrcback: "",
      source_name: "",
      source_phone: "",
      source_address: "",
      source_dob: "",
      source_ssn: "",
      input_name: "",
      input_phone: "",
      input_address: "",
      input_dob: "",
      input_ssn: "",
      name_score: "",
      address_score: "",
      phone_score: "",
      ssn_score: "",
      dob_score: "",
      average_score: "",
      reason: "",
      headerTextVariant: "light",
      searchvalue:"",
      showReloadBtn:false,
      constants: commonConstants,
      loading:false,
      text_show:false,
      consumer:"",
      phone_no:"",
      email:"",
      session_id:null,
      user_id:null,
      risk_score:""
    };
  },
  components: {
    HourGlass,
    CanPayLoader
  },
  created() {
    this.getScores();
    this.email = this.$route.query.email;
  },
  methods: {
    searchConsumers(){
      var self = this;
      if (
        moment($("#start-date").val()).format("YYYY-MM-DD") >
        moment().format("YYYY-MM-DD") && $("#start-date").val()!= ''
      ) {
        error("Start date cannot be from future.");
        return false;
      }
      if (
        moment($("#end-date").val()).format("YYYY-MM-DD") >
        moment().format("YYYY-MM-DD") && $("#end-date").val()!= ''
      ) {
        error("End date cannot be from future.");
        return false;
      }
      if($("#start-date").val()!=''){
        var from_date = moment($("#start-date").val()).format("YYYY-MM-DD");
      }else{
        var from_date = '';
      }
      if($("#end-date").val()!=''){
        var to_date = moment($("#end-date").val()).format("YYYY-MM-DD");
      }else{
        var to_date = '';
      }
      if(from_date > to_date){
        error("End Date cannot be greater than Start Date");
        return false;
      }
      if((self.consumer).trim().length < 3 && (self.consumer).trim().length > 0){
        error("Please provide Consumer Name (Min 3 chars)");
        return false;
      }
      var request = {
        from_date: from_date,
        to_date: to_date,
        consumer: self.consumer,
        email:self.email,
        phone_no:self.phone_no,
        type:601
      };
      self.loading = true;
      api
      .getAllManualReviewDetails(request)
      .then(function (response) {
        if (response.code == 200) {
          self.allDetails = response.data;
          self.loading = false;
        } else {
          error(response.message);
          self.loading = false;
        }
      })
      .catch(function (error) {
        // error(error);
        self.loading = false;
      });
    },
    getScores() {
      var self = this;
      $(document).on("click", ".get-scores", function (e) {
        self.record_id = $(e.currentTarget).attr("data-id");
        var request = {
          id: self.record_id,
        };
        var id = $(e.currentTarget).attr("data-id");
        var type = $(e.currentTarget).attr("data-type");
        api
          .getScores(request)
          .then((response) => {
            if ((response.code = 200)) {
            if(response.data.cognito_response == "success")
              {
              self.risk_score = response.data.risk_score;
              self.source_name = response.data.log.source_name;
              self.source_phone = response.data.log.source_phone;
              self.source_address = response.data.log.source_address;
              self.source_dob = response.data.log.source_dob;
              self.source_ssn = response.data.log.source_ssn;
              self.input_name = response.data.log.input_name;
              self.input_phone = response.data.log.input_phone;
              self.input_address = response.data.log.input_address;
              self.input_dob = moment(response.data.log.input_dob).format(
                "MM-DD-YYYY"
              );
              self.input_ssn = response.data.log.input_ssn;
              self.name_score = response.data.log.X_name_score_max;
              self.address_score = response.data.log.X_address_score_max;
              self.phone_score = response.data.log.X_phone_score_max;
              self.ssn_score = response.data.log.X_ssn_score_max;
              self.dob_score = response.data.log.X_dob_score_max;
              self.average_score = response.data.log.X_average_score_max;
              self.reason = response.data.log.outcome_reason;
              self.imagesrcfront = response.data.image_front;
              self.imagesrcback = response.data.image_back;
              self.items = [];
              if (response.data.log.scores.length > 0) {
                self.items = [
                  { source: self.source_name, score: self.name_score },
                  { source: self.source_phone, score: self.phone_score },
                  { source: self.source_address, score: self.address_score },
                  { source: self.source_dob, score: self.dob_score },
                  { source: self.source_ssn, score: self.ssn_score },
                ];
              }
              self.$refs["approved-edit-modal"].show();
              }
              else {
              self.session_id = response.data.session_id
              self.imagesrcfront = response.data.image_front;
              self.imagesrcback = response.data.image_back;

                self.$refs["approved-error-modal"].show();
              }
            } 

            
          })
          .catch((err) => {
            console.log(err);
              self.imagesrcfront = response.data.image_front;
              self.imagesrcback = response.data.image_back;
                self.$refs["approved-error-modal"].show();
          });
      });
    },
     dateDiff(){
      var self = this;
      if ($("#start-date").val() != "") {
        var from_date = moment($("#start-date").val()).format("YYYY-MM-DD");
      } else {
        var from_date = "";
      }
      if ($("#end-date").val() != "") {
        var to_date = moment($("#end-date").val()).format("YYYY-MM-DD");
      } else {
        var to_date = "";
      }
      if(from_date!='' && to_date!=''){
        //calculate the date Difference
        var date1 = new Date(from_date);
        var date2 = new Date(to_date);
        // To calculate the time difference of two dates
        var Difference_In_Time = date2.getTime() - date1.getTime();

        // To calculate the no. of days between two dates
        var Difference_In_Days = Difference_In_Time / (1000 * 3600 * 24);

        if(Difference_In_Days > 30){
          $("#searchBtn").prop('disabled', true);
          self.text_show = true;
        }else{
          $("#searchBtn").prop('disabled', false);
          self.text_show = false;
        }
      }
    },
    reset(){
      var self = this;
      self.consumer = "";
      self.phone_no = "";
      self.email = "";
    },
        Refresh()
    {
        var self = this;
        var request = {
          sessionId: self.session_id,

        };
        api
          .getassessment(request)
          .then((response) => {
            if ((response.code = 200)) {
              self.$refs["approved-error-modal"].hide();
              console.log(self.record_id);
        var request = {
          id: self.record_id,
        };
        api
          .getScores(request)
          .then((response) => {
            if ((response.code = 200)) {
              
             if(response.data.cognito_response == "success")
              {
              self.source_name = response.data.log.source_name;
              self.source_phone = response.data.log.source_phone;
              self.source_address = response.data.log.source_address;
              self.source_dob = response.data.log.source_dob;
              self.source_ssn = response.data.log.source_ssn;
              self.input_name = response.data.log.input_name;
              self.input_phone = response.data.log.input_phone;
              self.input_address = response.data.log.input_address;
              self.input_dob = moment(response.data.log.input_dob).format(
                "MM-DD-YYYY"
              );
              self.input_ssn = response.data.log.input_ssn;
              self.name_score = response.data.log.X_name_score_max;
              self.address_score = response.data.log.X_address_score_max;
              self.phone_score = response.data.log.X_phone_score_max;
              self.ssn_score = response.data.log.X_ssn_score_max;
              self.dob_score = response.data.log.X_dob_score_max;
              self.average_score = response.data.log.X_average_score_max;
              self.reason = response.data.log.outcome_reason;
              self.imagesrcfront = response.data.image_front;
              self.imagesrcback = response.data.image_back;
              self.items = [];
              if (response.data.log.scores.length > 0) {
                self.items = [
                  { source: self.source_name, score: self.name_score },
                  { source: self.source_phone, score: self.phone_score },
                  { source: self.source_address, score: self.address_score },
                  { source: self.source_dob, score: self.dob_score },
                  { source: self.source_ssn, score: self.ssn_score },
                ];
              }
              self.$refs["approved-edit-modal"].show();
            } 
            else{
              self.$refs["approved-error-modal"].show();
              self.session_id = response.data.session_id
            }
            }

          })
          .catch((err) => {
            self.session_id = response.data.session_id
            self.$refs["approved-error-modal"].show();
          });


            } else {

            }
          })
          .catch((err) => {
            error(err);
          });
    }
  },
  mounted() {
    var self = this;
    if(self.email){
      self.searchConsumers();
    }
    $("#start-date").datepicker({
      format: "mm/dd/yyyy",
      autoclose: true,
      todayHighlight: true,
    }).on('changeDate', function (ev) {
        self.dateDiff();
    });
    $("#end-date").datepicker({
      format: "mm/dd/yyyy",
      autoclose: true,
      todayHighlight: true,
    }).on('changeDate', function (ev) {
        self.dateDiff();
    });
    $("#start-date , #end-date").datepicker("setDate", new Date());
    document.title = "CanPay - Approved Identity";
  },
};
</script>

