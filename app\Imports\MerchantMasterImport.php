<?php

namespace App\Imports;

use App\Models\RegisteredMerchantMaster;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;

class MerchantMasterImport implements ToModel, WithHeadingRow, WithBatchInserts, WithValidation, WithChunkReading
{
    use Importable;
    /**
     * @param array $row
     * This function actully imports the data as row from Excel Sheet. Here we used the WithHeadingRow to get the Data with Heading. Do Not try to get the rows with index.
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row)
    {
        $merchant_id = $row['merchantcode'];
        if (!empty($merchant_id)) {
            try {
                // Check if the merchant is already added in Database
                $checkMerchantExists = RegisteredMerchantMaster::where('merchant_id', $merchant_id)->first();
                if (empty($checkMerchantExists)) {
                    $action = 'added';
                    // Insertion Started in Registered Merchant Master Table
                    $merchant_master = new RegisteredMerchantMaster();
                    $merchant_master->merchant_id = $merchant_id;
                    $merchant_master->merchant_name = $row['merchantname1'];
                    $merchant_master->save();
                } else {
                    $action = 'updated';
                    // Registered Merchant Details Updated
                    $checkMerchantExists->merchant_name = $row['merchantname1'];
                    $checkMerchantExists->save();
                }

                Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Merchant " . $action . " in Registered Merchant Master Table for Merchant ID : " . $merchant_id);
            } catch (\Exception $e) {
                Log::channel('datamigration')->error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured during insertion in Registered Merchant Master Table for Merchant ID : " . $merchant_id . ".", [EXCEPTION => $e]);
                insertSkippedDataLog('V1 Merchant', 'merchantcode', $row['merchantcode'], $e, json_encode($row), 'CanPay Transaction Fees');
            }
        }
    }

    public function batchSize(): int
    {
        return 1000;
    }

    public function chunkSize(): int
    {
        return 5000;
    }

    public function rules(): array
    {
        return [
            '*.merchantcode' => 'required',
        ];
    }
}
