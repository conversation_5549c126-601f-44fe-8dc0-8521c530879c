<?php

namespace App\Http\Controllers;

use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class DevController extends Controller
{
    protected $client;

    public function __construct()
    {
        $this->client = new Client();
    }

    public function testFactory(Request $request)
    {
        echo "test";
        exit;
        try {
            Log::info("CAlling factory function");
            $response = $this->client->post('http://centralized-api.test/api/testcallfactory');
            $data = json_decode($response->getBody(), true);
            Log::info($data);
            return $data;
        } catch (\Exception $e) {
            Log::error('Error calling factory function: ' . $e->getMessage());
            return null;
        }
    }

    public function testAwsSecretManager()
    {
        $secretValue['TEST_SECRET_MANAGER_KEY'] = config('app.test_secret_manager');

        return renderResponse(SUCCESS, 'Success', $secretValue);
    }
}
