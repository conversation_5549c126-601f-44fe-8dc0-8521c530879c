<template>
<div>
  <div v-if="loading">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Release Consumers</h3>
                </div>
                <div class="card-body">
                  <div class="row">
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="form-control"
                        placeholder="Phone No (Exact)"
                        id="phone_no"
                        v-model="phone_no"
                      />
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="form-control"
                        placeholder="Email (Exact)"
                        id="email"
                        v-model="email"
                      />
                    </div>
                  </div>
                </div>
                </div>
                <div class="card-footer">
                  <button
                    type="button"
                    class="btn btn-success"
                    @click="searchConsumersReturnTransactions()"
                  >
                    Search
                  </button>
                  <button
                    type="button"
                    @click="reset()"
                    class="btn btn-success margin-left-5"
                  >
                    Reset
                  </button>
                  <button
                    type="button"
                    @click="releaseConsumer"
                    class="btn btn-success"
                    style="float:right"
                    v-if="allTransactionsModel.length > 0"
                  >
                    Release Consumer
                  </button>
                </div>
                <!-- /.card-header -->
                <div class="card-body">
                  <div class="row">
                    
                    <div class="col-12">
                      <b-table-simple
                      responsive
                      show-empty
                      bordered
                      sticky-header="800px"
                      v-if="allTransactionsModel.length > 0"
                    >
                      <b-thead head-variant="light">
                        <b-tr>
                          <b-th class="text-left">Transaction Number</b-th>
                          <b-th class="text-center">Local Transaction Date</b-th>
                          <b-th class="text-center">Amount($)</b-th>
                          <b-th class="text-center">Return Reason Code</b-th>
                          <b-th class="text-center">Attempted Settlement</b-th>
                          <b-th class="text-center">Status</b-th>
                          <b-th class="text-center">Represented On</b-th>
                          <b-th class="text-center">Expected Clearance</b-th>
                          <b-th class="text-center">Action</b-th>
                        </b-tr>
                      </b-thead>
                      <b-tbody v-for="(row, index) in allTransactionsModel" :key="index">
                        <b-tr>
                          <b-td class="text-left text-gray">{{
                            row.transaction_number
                          }}</b-td>
                          <b-td v-html="row.transaction_time" class="text-center text-gray"></b-td>
                          <b-td class="text-right text-gray">{{
                            row.total_amount
                          }}</b-td>
                          <b-td class="text-right text-gray">{{
                            row.reason_code
                          }}</b-td>
                          <b-td class="text-center text-gray">{{
                            row.represent_count
                          }}</b-td>
                          <b-td class="text-center text-gray">{{
                            row.status
                          }}</b-td>
                          <b-td class="text-center text-gray"><span v-if="row.status == 'Pending'" v-html="row.represented_on"></span></b-td>
                          <b-td class="text-center text-gray"><span v-if="row.status == 'Pending'" >{{row.expected_clearance}}</span></b-td>
                          <b-td class="text-center text-gray">
                            <input type="checkbox" name="transaction_id" :value="row.transaction_id" v-if="row.status == 'Pending'" />
                          </b-td>
                        </b-tr>
                      </b-tbody> 
                    </b-table-simple>
                    <p v-else>No data displayed. Please refine your search criteria.</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
    
    
  </div>
</div>
</template>
<script>
import api from "@/api/user.js";
import moment from "moment";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "./CustomLoader/CanPayLoader.vue"
export default {
  data() {
    return {
      phone_no:"",
      email:"",
      allTransactionsModel:{},
      loading: false,
    };
  },
  components: {
    HourGlass,
    CanPayLoader
  },
  created() {},
  methods: {
    searchConsumersReturnTransactions(){
      var self = this;

      if($("#phone_no").val().trim() === '' &&  $("#email").val().trim() === ''){
        error("Please provide email(exact) or phone no(exact)");
        return false;
      }
      var request = {
        email:self.email,
        phone_no:self.phone_no,
      };
      self.loading = true;
      api
      .searchConsumersReturnTransactions(request)
      .then(function (response) {
        if (response.code == 200) {
          self.allTransactionsModel = response.data;
          self.loading = false;
        } else {
          error(response.message);
          self.loading = false;
        }
      })
      .catch(function (error) {
        // error(error);
        self.loading = false;
      });
    },
    reset(){
      var self = this;
      self.phone_no = "";
      self.email = "";
    },
    releaseConsumer(){
      var self = this;
      
      var transaction_ids = [];
      $.each($("input[name='transaction_id']:checked"), function(){
          transaction_ids.push($(this).val());
      });
      
      if(transaction_ids.length == 0){
        alert('Please select a Transaction!');
      }else{
        var r = confirm(
          "Do you want to release these Consumers?"
        );
        if (r == true) { 
          self.loading = true;
          var request = {
              transaction_ids: transaction_ids.join(", ")
          };
          api
            .releaseConsumer(request)
            .then((response) => {
              if ((response.code = 200)) {
                success(response.message);
                self.searchConsumersReturnTransactions();
                $("input[name='transaction_id']").prop('checked', false);
              } else {
                error(response.message);
                self.loading = false;
              }
            })
            .catch((err) => {
              error(err);
              self.loading = false;
            });
        } else {
          return false;
        }
      }
    }
  },
  mounted() {
  },
};
</script>


