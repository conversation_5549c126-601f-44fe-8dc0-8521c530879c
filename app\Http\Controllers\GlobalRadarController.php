<?php

namespace App\Http\Controllers;

use App\Http\Factories\EmailExecutor\EmailExecutorFactory;
use App\Models\GlobalRadarReview;
use App\Models\StatusMaster;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class GlobalRadarController extends Controller
{

    public function __construct(Request $request)
    {
        $this->request = $request;
        $this->emailexecutor = new EmailExecutorFactory();
    }

    /**
     * Listing page for all the users for global radar validation based on status
     */
    public function getAllusersForGlobalRadar(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Global Radar Review Details search started...");

        // Search with in Global Radar Review
        $globalRadars = $this->_getAllUsersGlobalRadar($request);

        $message = trans('message.manual_review_search_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Global Radar Review Details search complete.");
        return renderResponse(SUCCESS, $message, $globalRadars);
    }

    /**
     * _getAllUsersGlobalRadar
     * Fetch Global Radar Review
     * @param  mixed $searchArray
     * @return void
     */
    private function _getAllUsersGlobalRadar($request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Global Radar Review Details Search Started.");

        $from_date = $request['from_date'];
        $to_date = $request['to_date'] . ' 23:59:59';

        $sql = 'SELECT users.*, status_master.code, cognito_rules_log.response, global_radar_reviews.id as edit FROM global_radar_reviews JOIN cognito_rules_log ON cognito_rules_log.user_id = global_radar_reviews.user_id JOIN users ON users.user_id = global_radar_reviews.user_id JOIN user_roles ON users.role_id = user_roles.role_id JOIN status_master ON status_master.id = global_radar_reviews.status WHERE status_master.code = ?  ';

        $searchStr = [$request['status']];
        if ($from_date && $to_date) {
            $sql .= " AND global_radar_reviews.created_at BETWEEN ? AND ? ";
            array_push($searchStr, $from_date, $to_date);
        }
        if (trim($request['email'])) {
            $sql .= " AND users.email = ? ";
            array_push($searchStr,$request['email']);
        }
        if (trim($request['phone_no'])) {
            $sql .= " AND users.phone = ? ";
            array_push($searchStr,$request['phone_no']);
        }
        if (trim($request['consumer'])) {
            $sql .= ' AND LOWER(
                REPLACE(CONCAT(COALESCE(
                REPLACE(users.first_name, " ",""), "")," ", COALESCE(
                REPLACE(users.middle_name, " ",""), "")," ", COALESCE(
                REPLACE(users.last_name, " ",""), "")),"  "," ")) LIKE ? ';
            array_push($searchStr,'%' . $request['consumer'] . '%');
        }
        $sql .= " GROUP BY users.user_id ORDER BY global_radar_reviews.created_at DESC LIMIT 100";

        $consumers = DB::connection(MYSQL_RO)->Select($sql,$searchStr);

        $consumerArr = [];
        if (!empty($consumers)) {
            foreach ($consumers as $consumer) {

                $data = [];
                $data['name'] = $consumer->first_name . ' ' . $consumer->middle_name . ' ' . $consumer->last_name;
                $data['phone'] = $consumer->phone;
                $data['email'] = $consumer->email;
                $data['date_of_birth'] = date('m-d-Y', strtotime($consumer->date_of_birth));
                $data['address'] = $consumer->street_address . ' ' . $consumer->city . ' ' . $consumer->state . ' ' . $consumer->zipcode;
                $data['edit'] = $consumer->edit;
                $data['cognito_response'] = $consumer->response;
                $data['bank_link_type'] = $consumer->bank_link_type == 0 ? 'Manual' : 'Finicity';
                $data['status'] = $consumer->status;

                array_push($consumerArr, $data);
            }
        } else {
            $consumerArr = [];
        }

        return $consumerArr;
    }

    /**
     * updateGlobalRadarReviewStatus
     * Global radar manual review status update
     * @param  mixed $this->request
     * @return void
     */
    public function updateGlobalRadarReviewStatus()
    {
        $data = $this->request->all();
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Updating status of global radar manual review details with id " . $data['id']);
        try {
            $details = GlobalRadarReview::where('id', $data['id'])->first();
            $status = StatusMaster::where('code', $data['code'])->first();
            $details->status = $status->id;
            $details->save();
            if ($data['code'] == APPROVED) { //if status is approved send an email to consumer email id
                $user = User::where("user_id", $details->user_id)->first();
                //get all the details needed to send the email
                $this->emailexecutor->approvedManualReview($user);
            }
            $message = trans('message.mark_record_reviewed') . $status->status;
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") -" . $message);
            return renderResponse(SUCCESS, $message, null);
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while updating global radar manual review details.", [EXCEPTION => $e]);
            $message = trans('message.db_transaction_failed');
            return renderResponse(FAIL, $message, $e);
        }
    }
}
