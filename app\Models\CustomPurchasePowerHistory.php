<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
class CustomPurchasePowerHistory extends Model
{

    protected $table = 'custom_purchase_power_history';

    /**
     * __construct
     * This function will insert an alphanumeric 32 character unique id as primary key in the table whenever a data get inserted
     * @param  mixed $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        $this->id = generateUUID();

        parent::__construct($attributes);
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'consumer_id',
        'old_purchase_power',
        'old_purchase_power_date',
        'total_pending_amount',
        'new_purchase_power',
        'old_weekly_spending_limit',
        'new_weekly_spending_limit',
        'admin_control',
    ];
    public $timestamps = true;
    public $incrementing = false;
}
