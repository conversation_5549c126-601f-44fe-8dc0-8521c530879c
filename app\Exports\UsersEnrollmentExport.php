<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Events\AfterSheet;
use Illuminate\Support\Facades\Log;

class UsersEnrollmentExport implements FromCollection, WithHeadings, ShouldAutoSize, WithEvents
{
    protected $request;

    public function __construct($request)
    {
        $this->request = $request; // Declaring the request variable
    }

    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        $collection_array = $this->request['users']; // Storing the array received from request
        $users = array();
        foreach ($collection_array as $user_data) {
            $nestedData['reviewed_user_name'] = $user_data->reviewed_user_name;
            $nestedData['state'] = $user_data->state;
            $nestedData['name'] = $user_data->name;
            $nestedData['date_of_birth'] = $user_data->date_of_birth;
            $nestedData['address'] = $user_data->address;
            $nestedData['email'] = $user_data->email;
            $nestedData['consumer_type'] = $user_data->consumer_type;
            $nestedData['bank_balance'] = $user_data->bank_balance;
            $nestedData['purchase_power'] = $user_data->purchase_power;
            $nestedData['purchase_power_rule'] = $user_data->purchase_power_rule;
            $nestedData['effective_purchase_power'] = $user_data->effective_purchase_power;
            $nestedData['pending_amount'] = $user_data->pending_amount;
            $nestedData['bank_matched_users'] = $user_data->bank_matched_users;
            $nestedData['phone'] = $user_data->phone;
            $nestedData['routing_no'] = $user_data->routing_no;
            $nestedData['enrollment_date'] = $user_data->enrollment_date;

            array_push($users, $nestedData);
        }
        return collect([
            $users,
        ]);
    }

    public function headings(): array
    {
        $returnArray = array(
            [
                'Enrollment Report',
            ],
            [
                'Report Date:', '', 
            date('m/d/Y', strtotime($this->request['from_date'])) . ' - ' . date('m/d/Y', strtotime($this->request['to_date'])),
            ],
            [],
            [
                'Reviewed By',
                'State',
                'Name', 
                'Date Of Birth',
                'Address',
                'Email',
                'Consumer Type',
                'Bank balance', 
                'Purchase Power/Limit ($)', 
                'Purchase Power Rule',
                'Effective Purchase Power ($)', 
                'Pending Amount ($)', 
                'Matches any other banking?',
                'Phone',
                'Bank name or routing number',
                'Time of enrollment (PST)',
            ],
        );

        return $returnArray;
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event){
                $event->sheet->getStyle('A1:D1')->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);

                $event->sheet->getStyle('A2:D2')->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);

                $event->sheet->getStyle('A4:P4')->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);

                // Merging cells
                $event->sheet->mergeCells('A1:B1');
                $event->sheet->mergeCells('A2:B2');
                $event->sheet->mergeCells('C2:D2');
            },
        ];
    }
}
