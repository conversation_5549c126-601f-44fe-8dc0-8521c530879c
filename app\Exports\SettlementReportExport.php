<?php

namespace App\Exports;

use DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Events\AfterSheet;
use Illuminate\Support\Facades\Log;

class SettlementReportExport implements FromCollection, WithHeadings, ShouldAutoSize, WithEvents
{
    protected $request;
    protected $singularDebit;

    public function __construct($request)
    {
        $this->request = $request; // Declaring the request variable
        $this->singularDebit = $this->request['singular_debit'];
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        $collection_array = $this->request['report']; // Storing the array received from request
        return collect([
            $collection_array,
        ]);
    }

    public function headings(): array
    {
        $returnArray = array(
            // 1st header
            [
                'CanPay Settlement Report',
            ],
            // 2nd header
            [
                'Sales Date Range:', '',
                date('m/d/Y', strtotime($this->request['from_date'])) . ' - ' . date('m/d/Y', strtotime($this->request['to_date'])),
            ],
        );

        return $returnArray;
    }


    public function registerEvents(): array
    {
        $count = count((array) $this->request['report']);
        return [
            AfterSheet::class => function (AfterSheet $event) use ($count) {
                $endBorderColumn = $this->singularDebit ? 'N' : 'M';
                $event->sheet->getStyle('A3:P3')->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);
                $event->sheet->getStyle('A3:I3')->getAlignment()->applyFromArray(
                    array('horizontal' => 'center')
                );
                $event->sheet->getStyle('J3:P3')->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);
                $event->sheet->getStyle('J3:P3')->getAlignment()->applyFromArray(
                    array('horizontal' => 'center')
                );
                $event->sheet->getStyle('Q3:W3')->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);
                $event->sheet->getStyle('Q3:W3')->getAlignment()->applyFromArray(
                    array('horizontal' => 'center')
                );

                //Center align all columns
                $event->sheet->getStyle('A4:W' . ($count + 2))->getAlignment()->applyFromArray(
                    array('horizontal' => 'center')
                );

                //Bold Total Volume Column
                $event->sheet->getStyle('D4:D' . ($count + 2))->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);

                //Bold Total Fees Column
                $event->sheet->getStyle('E4:E' . ($count + 2))->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);

                if ($this->singularDebit) {
                    $boldColumns = ['D', 'L', 'M', 'N', 'O'];
                } else {
                    $boldColumns = ['D', 'L', 'M', 'N'];
                }

                // Apply bold styling to all specified columns
                foreach ($boldColumns as $column) {
                    $event->sheet->getStyle($column . '4:' . $column . ($count + 2))->applyFromArray([
                        'font' => [
                            'bold' => true,
                        ],
                    ]);
                }

                $event->sheet->getStyle('A' . ($count + 2) . ':AB' . ($count + 5))->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);
                
                $event->sheet->getStyle('A3:N3')->applyFromArray([
                    'borders' => [
                        'allBorders' => [
                            'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THICK,
                            'color' => ['argb' => '000000'],
                        ],
                    ],
                ]);
                $event->sheet->getStyle('M4:M'.($count + 2))->applyFromArray([
                    'borders' => [
                        'allBorders' => [
                            'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                            'color' => ['argb' => '000000'],
                        ],
                        'left' => [
                            'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THICK,
                            'color' => ['argb' => '000000'],
                        ],
                    ],
                ]);
                $event->sheet->getStyle('N4:N'.($count + 2))->applyFromArray([
                    'borders' => [
                        'allBorders' => [
                            'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                            'color' => ['argb' => '000000'],
                        ],
                        'right' => [
                            'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THICK,
                            'color' => ['argb' => '000000'],
                        ],
                    ],
                ]);
                if ($this->singularDebit) { 

                    $event->sheet->getStyle('O4:O'.($count + 2))->applyFromArray([
                        'borders' => [
                            'allBorders' => [
                                'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THICK,
                                'color' => ['argb' => '000000'],
                            ],
                        ],
                    ]);
                }
                
                for ($i = 4; $i <= ($count + 2); $i++) {
                    $event->sheet->getStyle('B' . $i . ':L'. $i)->applyFromArray([
                        'borders' => [
                            'allBorders' => [
                                'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                                'color' => ['argb' => '000000'],
                            ],
                        ],
                    ]);
                }
                $event->sheet->getStyle('C4:C'.($count + 2))->applyFromArray([
                    'borders' => [
                        'right' => [
                            'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THICK,
                            'color' => ['argb' => '000000'],
                        ],
                    ],
                ]);
                for ($i = 1; $i <= ($count + 2); $i++) {
                    $event->sheet->getStyle('A' . $i . ':AB' . $i)->applyFromArray([
                        'font' => [
                            'size' => 7,
                        ],
                    ]);
                }

                // Merging cells
                $event->sheet->mergeCells('A1:B1');
                $event->sheet->mergeCells('A2:B2');
                $event->sheet->mergeCells('A3:C3');
                $event->sheet->mergeCells('D3:L3');
                $event->sheet->mergeCells('M3:N3');

                //Total For the First Store
                $event->sheet->getStyle('A5'.':W5')->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);
                $redFontStyle = [
                    'font' => [
                        'color' => ['rgb' => 'FF0000'], // Red color
                    ],
                ];
                //Style the Headings for different Stores
                $inc = $this->request['date_diff']+8;
                $startRow = 5;
                $endRow = $startRow + ($this->request['date_diff'] + 2);
                for($i=1;$i<=$this->request['stores'];$i++){
                    if($i == 1){
                        $event->sheet->mergeCells('A'.($inc).':C'.($inc));
                        $event->sheet->mergeCells('D'.($inc).':L'.($inc));
                        $event->sheet->mergeCells('M'.($inc).':N'.($inc));

                        //Total
                        $event->sheet->getStyle('A'.($inc+2).':W'.($inc+2))->applyFromArray([
                            'font' => [
                                'bold' => true,
                            ],
                        ]);
                        $event->sheet->getStyle('A'.($inc-1).':W'.($inc-1))->applyFromArray([
                            'font' => [
                                'bold' => true,
                            ],
                        ]);

                        //Heading
                        $event->sheet->getStyle('A'.($inc).':W'.($inc))->applyFromArray([
                            'font' => [
                                'bold' => true,
                            ],
                        ]);
                        // Apply the red font style to the calculated range
                        $event->sheet->getStyle('K' . $startRow . ':K' . $endRow)->applyFromArray($redFontStyle);
                        
                    }else{
                        $event->sheet->mergeCells('A'.($inc+$this->request['date_diff']+5).':C'.($inc+$this->request['date_diff']+5));
                        $event->sheet->mergeCells('D'.($inc+$this->request['date_diff']+5).':L'.($inc+$this->request['date_diff']+5));
                        $event->sheet->mergeCells('M'.($inc+$this->request['date_diff']+5).':N'.($inc+$this->request['date_diff']+5));
                                
                        //Red mark on Free Processing Reimbursement
                        $event->sheet->getStyle('A'.($inc+$this->request['date_diff']+7).':W'.($inc+$this->request['date_diff']+7))->applyFromArray([
                            'font' => [
                                'bold' => true,
                            ],
                        ]);
                        //Total
                        $event->sheet->getStyle('A'.($inc+$this->request['date_diff']+7).':W'.($inc+$this->request['date_diff']+7))->applyFromArray([
                            'font' => [
                                'bold' => true,
                            ],
                        ]);
                        $event->sheet->getStyle('A'.($inc+$this->request['date_diff']+4).':W'.($inc+$this->request['date_diff']+4))->applyFromArray([
                            'font' => [
                                'bold' => true,
                            ],
                        ]);

                        //Heading
                        $event->sheet->getStyle('A'.($inc+$this->request['date_diff']+5).':W'.($inc+$this->request['date_diff']+5))->applyFromArray([
                            'font' => [
                                'bold' => true,
                            ],
                        ]);
                        $inc = $inc+$this->request['date_diff']+5;

                        $startRow = $endRow + 3;
                        $endRow = $startRow + ($this->request['date_diff'] + 2);
                        $event->sheet->getStyle('K' . $startRow . ':K' . $endRow)->applyFromArray($redFontStyle);
                    }
                }
            },
        ];
    }
}
