<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class TransactionRewardDetail extends Model
{
    /**
     * __construct
     * This function will insert an alphanumeric 32 character unique id as primary key in the table whenever a data get inserted
     * @param  mixed $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        $this->id = generateUUID();

        parent::__construct($attributes);
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'transaction_id',
        'reward_wheel_id',
        'reward_point',
        'reward_amount',
        'exchange_rate',
        'is_returned',
        'is_generic_point',
        'petition_id',
    ];
    public $timestamps = true;
    public $incrementing = false;
    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
}
