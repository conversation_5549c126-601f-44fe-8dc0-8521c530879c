<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class RESTAPILogger
{

    public function handle($request, Closure $next)
    {
        $version = ENV('VERSION', '???!!!');
        if (isset($_SERVER['REMOTE_ADDR']) && !empty($_SERVER['REMOTE_ADDR'])) {
            $remote_ip = $_SERVER['REMOTE_ADDR'];
        } else {
            $remote_ip = null;
        }
        $path = $request->getPathInfo();
        $function_name_arr = explode('/', $path);
        if (!in_array('export', $function_name_arr)) {
            $requests = $request->all();
            unset($requests['password']);
            $response = $next($request);
            $response_content = json_decode($response->content(), true);
            $responses = $response_content;
            if (isset($responses['data']['payment_pin'])) {
                unset($responses['data']['payment_pin']);
            }
            if (isset($responses['data']['qr_url'])) {
                unset($responses['data']['qr_url']);
            }
            $user_id = Auth::user() ? Auth::user()->user_id : "";
            $execution_time = number_format(microtime(true) - LARAVEL_START, 3);
            $log_arr = [
                '@timestamp' => '%datetime%',
                'V' => $version,
                'EC2' => config('app.ec2_instance_name'),
                'IP' => $remote_ip,
                'PU' => getHostName(),
                'TID' => defined('TID') ? TID : substr(generateUUID(), 0, 16),
                'USER_ID' => $user_id,
                'LEVEL' => '%level_name%',
                'PATH' => $request->path(),
                'METHOD' => $request->method(),
                'PARAM' => $requests,
                'STATUS' => $response->status(),
                'ET' => $execution_time,
            ];
            Log::channel('restapilog')->info('', $log_arr);
            return $response;
        } else {
            return $next($request);
        }
    }
}
