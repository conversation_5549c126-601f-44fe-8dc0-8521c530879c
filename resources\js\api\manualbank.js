const addEditRoutingNumber = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/addeditrestrictedroutingnumber', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const getAllState = (request) => {
    return new Promise((res, rej) => {
        axios.get('api/getallstate', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const statusChangeRoutingNumber = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/statuschangerestrictedroutingnumber', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

export default {
    addEditRoutingNumber,
    getAllState,
    statusChangeRoutingNumber,
};