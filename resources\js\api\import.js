import { loadProgressBar } from 'axios-progress-bar'

const migrateConsumers = (formData) => {
    return new Promise((res, rej) => {
        axios.defaults.headers.common["Content-Type"] = 'multipart/form-data';
        axios.post('api/import/consumers', formData)
            .then((response) => {
                res(response.data);
            })
            .catch((response) => {
                rej(response);
            })
    })
};

const migrateMerchants = (formData) => {
    return new Promise((res, rej) => {
        axios.defaults.headers.common["Content-Type"] = 'multipart/form-data';
        axios.post('api/import/merchants', formData)
            .then((response) => {
                res(response.data);
            })
            .catch((response) => {
                rej(response);
            })
    })
};

const migrateTransactions = (formData) => {
    return new Promise((res, rej) => {
        axios.defaults.headers.common["Content-Type"] = 'multipart/form-data';
        axios.post('api/import/transactions', formData)
            .then((response) => {
                res(response.data);
            })
            .catch((response) => {
                rej(response);
            })
    })
};

const migrateVoidedTransactions = (formData) => {
    return new Promise((res, rej) => {
        axios.defaults.headers.common["Content-Type"] = 'multipart/form-data';
        axios.post('api/import/voidedtransactions', formData)
            .then((response) => {
                res(response.data);
            })
            .catch((response) => {
                rej(response);
            })
    })
};

const importIntegrators = (formData) => {
    return new Promise((res, rej) => {
        axios.defaults.headers.common["Content-Type"] = 'multipart/form-data';
        axios.post('api/import/integratorExcel', formData)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const migrateCorporateParents = (formData) => {
    return new Promise((res, rej) => {
        axios.defaults.headers.common["Content-Type"] = 'multipart/form-data';
        axios.post('api/import/corporateParents', formData)
            .then((response) => {
                res(response.data);
            })
            .catch((response) => {
                rej(response);
            })
    })
};
const migrateHolidayList = (formData) => {
    return new Promise((res, rej) => {
        axios.defaults.headers.common["Content-Type"] = 'multipart/form-data';
        axios.post('api/import/holidayimport', formData)
            .then((response) => {
                res(response.data);
            })
            .catch((response) => {
                rej(response);
            })
    })
};

const importreturnTransactions = (formData) => {
    return new Promise((res, rej) => {
        axios.defaults.headers.common["Content-Type"] = 'multipart/form-data';
        axios.post('api/import/returntransactions', formData)
            .then((response) => {
                res(response.data);
            })
            .catch((response) => {
                rej(response);
            })
    })
};

const updateAkoyaProviderId = (formData) => {
    return new Promise((res, rej) => {
        axios.defaults.headers.common["Content-Type"] = 'multipart/form-data';
        axios.post('api/import/updateakoyaproviderid', formData)
            .then((response) => {
                res(response.data);
            })
            .catch((response) => {
                rej(response);
            })
    })
};
const updateMxInstitutionCode = (formData) => {
    return new Promise((res, rej) => {
        axios.defaults.headers.common["Content-Type"] = 'multipart/form-data';
        axios.post('api/import/updatemxinstitutioncode', formData)
            .then((response) => {
                res(response.data);
            })
            .catch((response) => {
                rej(response);
            })
    })
};


export default {
    migrateConsumers,
    migrateMerchants,
    migrateTransactions,
    migrateVoidedTransactions,
    importIntegrators,
    migrateCorporateParents,
    migrateHolidayList,
    importreturnTransactions,
    updateAkoyaProviderId,
    updateMxInstitutionCode
};