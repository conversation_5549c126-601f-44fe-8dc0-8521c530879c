<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class AccessLevelMaster extends Model
{
    protected $table = 'access_level_master';

    /**
     * __construct
     * This function will insert an alphanumeric 32 character unique id as primary key in the table whenever a data get inserted
     * @param  mixed $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        $this->id = generateUUID();

        parent::__construct($attributes);
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'label',
        'view',
        'use',
        'admin',
        'add',
        'edit',
        'delete',
    ];
    public $timestamps = true;
    public $incrementing = false;
}
