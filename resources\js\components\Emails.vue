<template>
<div>
  <div v-if="loading">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px;">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Email Templates</h3>
                  <b-button
                  class="btn-danger export-api-btn"
                  @click="reloadDatatable"
                  v-if="showReloadBtn"
                  >
                    <i class="fas fa-redo"></i> Reload
                  </b-button>
                </div>
                <!-- /.card-header -->
                <div class="card-body">
                  <table
                    id="emailTable"
                    class="table"
                    style="width:100%;white-space: normal;"
                  >
                    <thead>
                      <tr>
                        <th>Template Name</th>
                        <th>Template Subject</th>
                        <th>Updated On</th>
                        <th>Action</th>
                      </tr>
                    </thead>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
    <!-- Edit Email Template Modal Start -->
    <b-modal
      id="emialtemplate-modal"
      ref="modal"
      title="Edit Email Template"
      @show="resetModal"
      @hidden="resetModal"
      ok-title="Save"
      ok-variant="success"
      cancel-variant="outline-secondary"
      @ok="handleOk"
      :no-close-on-esc="true"
      :no-close-on-backdrop="true"
    >
      <form ref="form" @submit.stop.prevent="save" class="needs-validation">
        <div class="row">
            <div class="col-md-12">
                <label for="user_type">Template Name <span class="red">*</span></label>
                <input id="template_name" name="template_name" v-validate="'required'" type="text" v-model="templateModel.template_name" class="form-control" readonly />
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <label for="user_type">Template Subject <span class="red">*</span></label>
                <input id="template_subject" name="template_subject" v-validate="'required'" type="text" v-model="templateModel.template_subject" class="form-control" />
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <label for="user_type">Template Options</label>
                <select class="form-control" id="template_opts" @change="placeValue($event)">
                    <option value>Select Template Option</option>
                    <option v-for="(option, index) in template_options" :key="index" v-bind:value="option.value">{{ option.title }}</option>
                </select>
                <span class="helper-text">Use this Template Option to modify variables encoded in curly braces. Don't modify them manually.</span>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <label for="user_type">Template Body <span class="red">*</span></label>
                <vue-ckeditor
                    id="template_body"
                    name="template_body"
                    v-model="templateModel.template_body"
                    :config="config"
                />
            </div>
        </div>

      </form>
    </b-modal>
    <!-- Edit Email Template Modal End -->

    <!-- Preview Email Template Modal Start -->
    <b-modal
      id="previewemialtemplate-modal"
      ref="modal"
      :header-text-variant="headerTextVariant"
      title="Preview Email Template"
      @show="resetModal"
      @hidden="resetModal"
      ok-only
      ok-title="Close"
      ok-variant="success"
      :no-close-on-esc="true"
      :no-close-on-backdrop="true"
    >
        <div class="row">
            <div class="col-md-12">
                <div style="width: 100%; padding: 0 3%; margin-bottom: 2px; overflow-x: auto; white-space: nowrap;" v-html="templateBody"></div>
            </div>
        </div>
    </b-modal>
    <!-- Preview Email Template Modal End -->

    <b-modal
      id="sendemail-modal"
      ref="modal"
      title="Send Test Email"
      @show="resetModal"
      @hidden="resetModal"
      ok-title="Send"
      ok-variant="success"
      cancel-variant="outline-secondary"
      @ok="handleValidation"
      :no-close-on-esc="true"
      :no-close-on-backdrop="true"
    >
      <form ref="form" @submit.stop.prevent="save" class="needs-validation">
        <div class="row">
            <div class="col-md-12">
                <label for="user_type">Email <span class="red">*</span></label>
                <input id="email_address" name="email_address" v-validate="'required|email'" type="text" v-model="templateModel.email_address" class="form-control" />
                <span v-show="errors.has('email_address')" class="text-danger">{{ errors.first('email_address') }}</span>
            </div>
        </div>
      </form>
    </b-modal>
  </div>
</div>
</template>
<script>
import api from "@/api/email.js";
import { validationMixin } from "vuelidate";
import { required, minLength } from "vuelidate/lib/validators";
import VueCkeditor from "vue-ckeditor2";
import commonConstants from "@/common/constant.js";
import CanPayLoader from "./CustomLoader/CanPayLoader.vue";
export default {
  mixins: [validationMixin],
  components: {
    VueCkeditor,
    CanPayLoader
  },
  data() {
    return {
      headerTextVariant: "light",
      templateBody: null,
      allEmailTemplateModel : {},
      templateModel:{},
      templateId: null,
      config: {
        height: 300
      },
      currentUser: localStorage.getItem("user")? JSON.parse(localStorage.getItem("user")): null,
      template_options: [
            {
                value: "$user_details->first_name",
                title: "Recipient First Name"
            },
            {
                value: "$user_details->middle_name",
                title: "Recipient Middle Name"
            },
            {
                value: "$user_details->last_name",
                title: "Recipient Last Name"
            },
            {
                value: '$user_details->first_name." ".$user_details->middle_name." ".$user_details->last_name',
                title: "Recipient Full Name"
            },
            {
                value: '$password',
                title: "Password"
            },
            {
                value: '$login_pin',
                title: "Login Pin"
            }

        ],
        showReloadBtn:false,
        loading: false,
    };
  },
  created() {
    this.editemailtemplate();
    this.previewmailtemplate();
    this.sendemail();
  },
  methods: {
    reloadDatatable(){
      var self = this;
      self.loadDT();
    },
    placeValue(event) {
      let body_text = CKEDITOR.instances["template_body"].getData();
      let new_text = " {{" + event.target.value + "}} ";
      CKEDITOR.instances["template_body"].insertText(new_text);
      $("#template_opts").val("");
    },
    previewmailtemplate() {
      var self = this;
      $(document).on("click", ".previewmailtemplate", function(e) {
        self.templateId = $(e.currentTarget).attr("data-template-id");
        self.loading = true;
        api
          .getTemplateDetails({ template_id: self.templateId })
          .then(response => {
            self.templateBody = response.data.template_body;
            self.$bvModal.show("previewemialtemplate-modal");
            self.loading = false;
          })
          .catch(err => { this.loading = false;});
      });
    },
    editemailtemplate() {
      var self = this;
      $(document).on("click", ".editemailtemplate", function(e) {
        self.templateId = $(e.currentTarget).attr("data-template-id");
        self.getTemplateDetails(self.templateId);
        self.$bvModal.show("emialtemplate-modal");
      });
    },
    sendemail() {
      var self = this;
      $(document).on("click", ".sendemail", function(e) {
        self.$bvModal.show("sendemail-modal");
        self.templateModel.email_address = self.currentUser.email;
        self.templateModel.template_name = $(e.currentTarget).attr("data-template-name");
      });
    },
    handleValidation(bvModalEvt) {
      var self = this;
      // Prevent modal from closing
      bvModalEvt.preventDefault();
      // Trigger submit handler
      self.onMailSend();
    },
    onMailSend() {
        let self = this;
        this.$validator.validateAll().then(result => {
            if (result) {
              self.loading = true;
            api
                .sendMail(self.templateModel)
                .then(response => {
                if (response.code == 200) {
                    success(response.message);
                    self.$bvModal.hide("sendemail-modal");
                    self.resetModal();
                } else {
                    error(response.message);
                }
                self.loading = false;
                })
                .catch(err => {
                  self.loading = false;
                    error(err);
                });
            }
        });
    },
    getTemplateDetails(id){
        var self = this;
        self.loading = true;
        api
        .getTemplateDetails({ template_id: id })
        .then(response => {
            if ((response.code == 200)) {
                self.templateModel = response.data;
            } else {
                error(response.message);
            }
            self.loading = false;
        })
        .catch(err => {
          self.loading = false;
            error(err.response.data.message);
        });
    },
    resetModal() {
      var self = this;
      self.templateModel = {};
      self.templateId = null;
    },
    handleOk(bvModalEvt) {
      var self = this;
      // Prevent modal from closing
      bvModalEvt.preventDefault();
      // Trigger submit handler
      self.save();
    },
    save(){
        var self = this;
        self.loading = true;
        api
        .updateEmailTemplate(self.templateModel)
        .then(response => {
            if ((response.code == 200)) {
                success(response.message);
                self.userId = null;
                $("#emailTable").DataTable().ajax.reload(null, false);
                self.$bvModal.hide("emialtemplate-modal");
                self.store_ids = null;
                self.resetModal();
            } else {
                error(response.message);
            }
            self.loading = false;
        })
        .catch(err => {
          self.loading = false;
            error(err.response.data.message);
        });
    },
    loadDT: function() {
      var self = this;
      $("#emailTable").DataTable({
        pagingType: "simple_numbers",
        processing: true,
        serverSide: true,
        destroy: true,
        columnDefs: [
          { orderable: false, targets: [3] },
          { className: "dt-left", targets: [0, 1, 2] },
          { className: "dt-center", targets: [3] },
        ],
        order: [[2, "desc"]],
        orderClasses: false,
        language: {
          processing: '<i class="fa fa-spinner fa-spin fa-3x fa-fw"></i><span class="sr-only">Loading...</span> ',
          emptyTable: "No Email Templates Available.",
          search: "_INPUT_",
          searchPlaceholder: "Search records",
          oPaginate: {
            sNext: '<i class="fas fa-angle-double-right"></i>',
            sPrevious: '<i class="fas fa-angle-double-left"></i>'
          },
          sLengthMenu:
            "<label class='label_dropdown_dt'>Per page</label> _MENU_"
        },
        ajax: {
          headers: {
            Authorization: "Bearer " + localStorage.getItem("token")
          },
          url: "/api/getallemailtemplates",
          type: "POST",
          data: { _token: "{{csrf_token()}}" },
          dataType: "json",
          dataSrc: function(result) {
            self.showReloadBtn = false;
            self.allEmailTemplateModel = result.data;
            return self.allEmailTemplateModel;
          },
          error: function(data){
            error(commonConstants.datatable_error);
            $('#emailTable_processing').hide();
            self.showReloadBtn = true;
          }
        },
        columns: [
          { data: "template_name" },
          { data: "template_subject" },
          { data: "updated_at" },
          {
            render: function(data, type, full, meta) {
              return (
                '<b-button data-template-id="' +
                full.edit +
                '" class="previewmailtemplate custom-edit-btn" title="View Email Template" variant="outline-success"><i class="nav-icon fas fa-eye"></i></b-button>&nbsp;&nbsp;<b-button data-template-id="' +
                full.edit +
                '" class="editemailtemplate custom-edit-btn" title="Edit Email Template" variant="outline-success"><i class="nav-icon fas fa-edit"></i></b-button>&nbsp;&nbsp;<b-button data-template-id="' +
                full.edit +
                '" data-template-name="' +
                full.template_name +
                '" class="sendemail custom-edit-btn" title="Send Test Email" variant="outline-success"><i class="nav-icon fas fa-envelope"></i></b-button>'
              );
            }
          }
        ]
      });

      $("#emailTable").on("page.dt", function() {
        $("html, body").animate({ scrollTop: 0 }, "slow");
        $("th:first-child").focus();
      });

      //Search in the table only after 3 characters are typed
      // Call datatables, and return the API to the variable for use in our code
      // Binds datatables to all elements with a class of datatable
      var dtable = $("#emailTable").dataTable().api();

      // Grab the datatables input box and alter how it is bound to events
      $(".dataTables_filter input")
      .unbind() // Unbind previous default bindings
      .bind("input", function(e) { // Bind our desired behavior
          // If the length is 3 or more characters, or the user pressed ENTER, search
          if(this.value.length >= 3 || e.keyCode == 13) {
              // Call the API search function
              dtable.search(this.value).draw();
          }
          // Ensure we clear the search if they backspace far enough
          if(this.value == "") {
              dtable.search("").draw();
          }
          return;
      });
    }
  },
  mounted() {
    var self = this;
    self.loading = true;
    setTimeout(function() {
      self.loadDT();
      self.loading = false;
    }, 1000);
    document.title = "CanPay - Email Templates";
  }
};
</script>

