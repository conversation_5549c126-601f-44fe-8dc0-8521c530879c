<?php

// PostController.php

namespace App\Http\Controllers;

use App\Models\BlacklistedRoutingNumbers;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class BlacklistController extends Controller
{

    public function __construct(Request $request)
    {
        $this->request = $request;
    }
    /**
     * Fetch all the blacklisted routing numbers
     */
    public function getAllRoutingNumbers()
    {
        // Columns defined for Sorting
        $columns = array(
            0 => 'blacklisted_routing_numbers.routing_number',
            1 => 'blacklisted_routing_numbers.created_at',
        );
        // Main Query
        // Main Query
        $query = BlacklistedRoutingNumbers::on(MYSQL_RO);

        //Count Query 
        $queryCount = BlacklistedRoutingNumbers::on(MYSQL_RO)
        ->selectRaw('COUNT(*) as total_count');
        $totalData = $queryCount->first()->total_count; // Getting total no of rows

        $query = DB::table('blacklisted_routing_numbers');
        $totalData = $query->get()->count(); // Getting total no of rows
        $totalFiltered = $totalData;
        $limit = intval($this->request->input('length'));
        $start = intval($this->request->input('start'));
        $order = $columns[$this->request->input('order.0.column')];
        $dir = $this->request->input('order.0.dir');
        if (empty($this->request->input('search.value')) && empty($order) && empty($dir)) {
            $details = $query->offset($start)->limit(intval($limit))->orderBy('blacklisted_routing_numbers.created_at', 'DESC')->get();
        } else if (empty($this->request->input('search.value'))) {
            $details = $query->offset($start)->limit(intval($limit))->orderBy($order, $dir)->get();
        } else {
            $search = $this->request->input('search.value');
            $search_query = $query->where(function ($q) use ($search) {
                $q->Where('blacklisted_routing_numbers.routing_number', 'LIKE', "%{$search}%");
            });

            $search_query_count = $queryCount->where(function ($q) use ($search) {
                $q->Where('blacklisted_routing_numbers.routing_number', 'LIKE', "%{$search}%");
            });

            $totalFiltered = $search_query_count->first()->total_count;

            $details = $search_query->offset($start)->limit(intval($limit))->orderBy($order, $dir)->get();
        }
        $data = array();
        if (!empty($details)) {
            // Creating array to show the values in frontend
            foreach ($details as $detail) {
                $nestedData['routing_number'] = $detail->routing_number;
                $nestedData['edit'] = $detail->id;
                $nestedData['created_at'] = date('m-d-Y h:i A', strtotime($detail->created_at));
                $data[] = $nestedData;
            }
        }
        // Drawing the Datatable
        $json_data = array(
            "draw" => intval($this->request->input('draw')),
            "recordsTotal" => intval($totalData),
            "recordsFiltered" => intval($totalFiltered),
            "data" => $data,
        );

        Log::info(addslashes(__METHOD__) . "(" . LINE . __LINE__ . ") : Blacklisted routing numbers fetched successfully.");
        echo json_encode($json_data); // Rerurning the data
    }
    /**
     * add new routing numbers to the blacklist
     */
    public function addRoutingNumber()
    {
        $data = $this->request->all();
        $routing_number = BlacklistedRoutingNumbers::where('routing_number', $data['routing_number'])->first();
        if (!empty($routing_number)) {
            $message = trans('message.routing_number_error');
            return renderResponse(FAIL, $message, null);
        }
        $new = new BlacklistedRoutingNumbers();
        $new->routing_number = $data['routing_number'];
        $new->save();
        $message = trans('message.routing_number_success');
        return renderResponse(SUCCESS, $message, null);
    }


    
}
