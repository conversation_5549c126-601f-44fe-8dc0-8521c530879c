<?php

namespace App\Http\Clients;

use MrShan0\PHPFirestore\FirestoreClient;
use Illuminate\Support\Facades\Log;

/**
 *
 * @package App\Http\Clients
 */
class FirestoreHttpClient
{
    /**
     * @var Client
     */
    private $db;
    /**
     * FirestoreHttpClient constructor.
     */
    public function __construct()
    {
        $this->client = new FirestoreClient(config('app.firebase_project_id'), config('app.firebase_api_key'), [
            'database' => '(default)',
        ]);
    }
    /**
     * Stores data into firestore collection
     */
    public function storeIntoCollections($params)
    {
        $this->client->updateDocument(
            $params['collection'] . '/' . $params['identifier'],
            $params['data'],
        );
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Data stored into firestore", $params['data']);
    }
}
