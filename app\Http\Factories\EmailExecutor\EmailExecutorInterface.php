<?php

namespace App\Http\Factories\EmailExecutor;

interface EmailExecutorInterface
{
    public function corporateParentWelcomeEmail($params);
    public function adminUsersWelcomeEmail($params);
    public function approvedConsumerIdenity($params);
    public function consecutiveFailedLoginOTPEmail($params);
    public function forgotPassword($params);
    public function testEmail($params);
    public function consumerMonthlyTransactionActivity($params);
    public function corporateParentDailyTransactionActivity($params);
    public function corporateParentEmailChange($params);
    public function helpdeskWelcomeEmail($params);
    public function suspendConsumerIdenity($params);
    public function approvedV1ConsumerIdenity($params);
    public function consumerRetakeUploadDocumentMail($params);
    public function consumerReturnTransactionFetchMail($params);
    public function emailForFifthThirdUserDetected($params);
    public function petitionRejectionEmail($params);
}
