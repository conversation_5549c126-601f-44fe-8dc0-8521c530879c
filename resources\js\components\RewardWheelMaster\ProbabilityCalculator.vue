<template>
    <div>
        <p class="pribability-calc-title">Probability Calculator</p>
        <div class="pribability-number">
            <div :class="totalUsedProbability > 100 ? 'pribability-used-number mr-3 excess' : 'pribability-used-number mr-3'"><div class="mr-1"></div>{{totalUsedProbability > 0 ? totalUsedProbability : '0'}} Used</div>
            <div class="pribability-left-number"><div class="mr-1"></div>{{totalUsedProbability <= 100 ? parseFloat(totalRemaningProbability) : '0'}} Remaining</div>
        </div>
    </div>
</template>

<script>
export default {
    props:{
        segments: 0
    },  
    data(){
        return{
            totalUsedProbability: 0,
            totalProbability: 100,
            totalRemaningProbability: 100
        }
    },
    mounted(){
        if(this.segments){
            this.totalUsedProbability = 0
            this.segments.forEach((segment, index) => {
                if(segment.probability){
                    this.totalUsedProbability += parseFloat(segment.probability)
                    if(parseFloat(this.totalUsedProbability).toFixed(8) <= 100){
                        this.totalRemaningProbability = parseFloat(parseFloat(this.totalProbability) - parseFloat(this.totalUsedProbability)).toFixed(8)
                    }
                }
            });
        }
    },
    watch:{
        segments:{
            handler(val){
                this.totalUsedProbability = 0
                this.segments.forEach((segment, index) => {
                    if(segment.probability && segment.probability != '.'){
                        this.totalUsedProbability += parseFloat(segment.probability)
                        if(parseFloat(this.totalUsedProbability).toFixed(8) <= 100){
                            this.totalRemaningProbability = parseFloat(parseFloat(this.totalProbability) - parseFloat(this.totalUsedProbability)).toFixed(8)
                        }
                    }else{
                        this.totalRemaningProbability = parseFloat(parseFloat(this.totalProbability) - parseFloat(this.totalUsedProbability)).toFixed(8)
                    }
                });
                this.totalUsedProbability = parseFloat(this.totalUsedProbability).toFixed(8)
            },
            deep: true 
        },
        totalRemaningProbability: function (val){
            if(this.totalRemaningProbability.toString().split('.')[1] && this.totalRemaningProbability.toString().split('.')[1].length > 8){
                this.totalRemaningProbability  = this.totalRemaningProbability.toString().slice(0, -1);
            }
            this.totalRemaningProbability  = this.totalRemaningProbability.replace(/(?<=\d)0*$/, "");
        },
        totalUsedProbability: function (val){
            if(this.totalUsedProbability.toString().split('.')[1] && this.totalUsedProbability.toString().split('.')[1].length > 8){
                this.totalUsedProbability  = this.totalUsedProbability.toString().slice(0, -1);
            }
            this.totalUsedProbability  = this.totalUsedProbability.replace(/(?<=\d)0*$/, "");
        }
    }
}
</script>


<style lang="scss" scoped>
$skills-max-width: 100%; // We could use px but rem will adapt if the user changes the font size of the body.

.probability-mesure {
    font-size: 10px;
    font-weight: 500;
    background: #eee;
    max-width: $skills-max-width;
    display: flex;
}

.pribability-used {
    padding: 5px;
    text-align: right;
    color: #fff!important;
    background-color: #149240;
    background-image: linear-gradient(90deg, #149240 0%, #149240 100%);
    background-size: 100% 100%;
    box-sizing: border-box;
}
.pribability-left {
    padding: 5px;
    text-align: right;
    color: #000000!important;
    background-color: #e2e2e2;
    background-image: linear-gradient(90deg, #e2e2e2 0%, #e2e2e2 100%);
    background-size: 100% 100%;
    box-sizing: border-box;
} 
.pribability-calc-title{
    margin-bottom: 0;
    font-size: 12px;
}
.pribability-used-number{
    display: flex;
    align-items: center;
    font-size: 14px;
    width: auto;
}
.pribability-used-number.excess {
    color: #ff0000;
}
.pribability-used-number div{
    width: 15px;
    height: 15px;
    background: #149240;
    border-radius: 3px;
}

.pribability-left-number{
    display: flex;
    align-items: center;
    font-size: 14px;
    width: auto;
}
.pribability-left-number div{
    width: 15px;
    height: 15px;
    background: #e2e2e2;
    border-radius: 3px;
}
</style>