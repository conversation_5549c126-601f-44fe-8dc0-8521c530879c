<?php

namespace App\Imports;

use App\Http\Clients\GooglePlacesHttpClient;
use App\Models\AccessLevelMaster;
use App\Models\AuthorisedSignerInfo;
use App\Models\BankAccountInfo;
use App\Models\BusinessTypeMaster;
use App\Models\LegalEntityInfo;
use App\Models\MerchantApiKeyMap;
use App\Models\MerchantStores;
use App\Models\Petition;
use App\Models\POSMaster;
use App\Models\RegisteredMerchantMaster;
use App\Models\StatusMaster;
use App\Models\StoreTransactionTypeMap;
use App\Models\StoreUserMap;
use App\Models\TerminalMaster;
use App\Models\TimezoneMaster;
use App\Models\TransactionPostingDecisionTable;
use App\Models\TransactionTypeMaster;
use App\Models\User;
use Carbon\Carbon;
use Datetime;
use DateTimeZone;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class MerchantsImport implements ToModel, WithHeadingRow, WithBatchInserts, WithChunkReading
{
    use Importable;
    private $rows = 0;
    private $storeIds = [];

    public function __construct()
    {
        $this->googleplaces = new GooglePlacesHttpClient();
    }

    /**
     * @param array $row
     * This function actully imports the data as row from Excel Sheet. Here we used the WithHeadingRow to get the Data with Heading. Do Not try to get the rows with index.
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row)
    {
        DB::beginTransaction();
        try {
            if (!empty($row['merchant_id'])) {
                $timezone_details = isset($row['timezone_name']) ?  $row['timezone_name'] : '';
                //skip google api calls when timezone_name, gps_latitude and gps_longitude not empty
                if (!empty($timezone_details) && !empty($row['gps_latitude']) && !empty($row['gps_longitude'])) {
                    $lat = $row['gps_latitude'];
                    $long = $row['gps_longitude'];
                } else {
                    if (!empty($row['gps_latitude']) && !empty($row['gps_longitude'])) {
                        $timezone_details = $this->_getTimezone($row['gps_latitude'], $row['gps_longitude']);
                        $lat = $row['gps_latitude'];
                        $long = $row['gps_longitude'];
                        $timezone_details = $this->_getTimezone($lat, $long);
                        Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "timezone details from gps.");
                    } else if (!empty($row['dba_street_address'])) {
                        $address_string = str_replace(' ', '+', $row['dba_street_address'] . ',' . $row['dba_city'] . ',' . $row['dba_state'] . ',' . $row['dba_zip']);
                        $address = preg_replace('/[^A-Za-z0-9\+,]/', '', $address_string);
                        $response = $this->googleplaces->getLatLong($address);
                        $result = json_decode($response, true);
                        if (!empty($result['results'])) {
                            $lat = $result['results'][0]['geometry']['location']['lat'];
                            $long = $result['results'][0]['geometry']['location']['lng'];
                            $timezone_details = $this->_getTimezone($lat, $long);
                            Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "timezone details from address");
                        }
                    }
                }
                if ($timezone_details) {
                    // Check if merchant already exists in Database
                    $checkMerchantExists = RegisteredMerchantMaster::where('merchant_id', $row['merchant_id'])->first();
                    Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Merchant exists.");
                    if (empty($checkMerchantExists)) {
                        // If not exists insert it into database with user merchant relation
                        Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Merchant didnt exist");
                        $merchant_id = $this->_addMerchantDetails($row);
                    } else {
                        // If exists then continue adding stores with the existing merchant id
                        Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Merchant exists so add stores");
                        $merchant_id = $this->_updateMerchantDetails($row, $checkMerchantExists->id);
                    }

                    $storeCode = $row['store_identifier'];
                    $actualStoreCode = substr($storeCode, 0, 4);
                    $storeTransactionType = [];
                    substr($storeCode, 5, 1) != '' ? array_push($storeTransactionType, substr($storeCode, 5, 1)) : null;
                    substr($storeCode, 6, 1) != '' ? array_push($storeTransactionType, substr($storeCode, 6, 1)) : null;
                    //Check if Store exists in the sam emerchant
                    $checkStoreUnderMerchant = MerchantStores::where(['merchant_id' => $merchant_id, 'store_id' => $actualStoreCode])->first();
                    if (empty($checkStoreUnderMerchant)) {
                        // Check if timezone exists in master table. If not then insert it and map it with store.
                        $checkTimezoneExists = TimezoneMaster::where('timezone_name', $timezone_details)->first();
                        if (empty($checkTimezoneExists)) {
                            $timezone = new TimezoneMaster();
                            $timezone->timezone_name = $timezone_details;
                            $timezone->save();
                            $timezone_id = $timezone->id;

                            // Insert the new timezone in Transaction posting decision table
                            $time = Carbon::now()->format('Y-m-d') . ' ' . config('app.transaction_posting_block_end_time') . ':00:00';
                            $date = DateTime::createFromFormat(DB_DATE_FORMAT, $time, new DateTimeZone('America/New_York'));
                            $date->setTimeZone(new DateTimeZone($timezone->timezone_name));
                            $transaction_posting_decision_table = new TransactionPostingDecisionTable();
                            $transaction_posting_decision_table->timezone_id = $timezone_id;
                            $transaction_posting_decision_table->start_time = "00:00:01";
                            $transaction_posting_decision_table->end_time = $date->format('H:i:s');
                            $transaction_posting_decision_table->save();
                        } else {
                            $timezone_id = $checkTimezoneExists->id;
                        }
                        $merchantStoreDetailsArr = array();
                        $merchantStoreDetailsArr['lat'] = $lat;
                        $merchantStoreDetailsArr['long'] = $long;
                        $merchantStoreDetailsArr['timezone_id'] = $timezone_id;
                        $merchantStoreDetailsArr['merchant_id'] = $merchant_id;
                        $merchantStoreDetailsArr['actualStoreCode'] = $actualStoreCode;
                        $merchantStoreDetailsArr['rowDetails'] = $row;

                        // Insertion Started for Merchant Stores with Relation
                        $merchantStoreID = $this->_addMerchantStoreDetails($merchantStoreDetailsArr);
                        $merchantstores = MerchantStores::find($merchantStoreID);

                        // mapping started for Store and Transaction type
                        if (!empty($storeTransactionType)) {
                            foreach ($storeTransactionType as $transaction_type) {
                                $transaction_type_id = getTransactionTypeId($transaction_type);
                                $checkStoreTransactionTypeExsists = StoreTransactionTypeMap::where(['store_id' => $merchantStoreID, 'transaction_type_id' => $transaction_type_id])->first();
                                if (empty($checkStoreTransactionTypeExsists) && $transaction_type != '') {
                                    $store_transaction_map = new StoreTransactionTypeMap();
                                    $store_transaction_map->store_id = $merchantStoreID;
                                    $store_transaction_map->transaction_type_id = $transaction_type_id;
                                    $store_transaction_map->save();
                                }
                            }
                        } else {
                            $store_transaction_map = new StoreTransactionTypeMap();
                            $store_transaction_map->store_id = $merchantStoreID;
                            $store_transaction_map->transaction_type_id = getTransactionTypeId('');
                            $store_transaction_map->save();
                        }

                        // Mapping Creation for Global Canpay Device Manager started in Store User Map
                        $access_level_details = AccessLevelMaster::where('label', ADMIN_RIGHT)->first();
                        // Fetching Global Canpay Device Manager
                        $device_manager = User::where('email', DEFAULT_DEVICE_MANAGER_EMAIL)->first();
                        $storeusermap = new StoreUserMap();
                        $storeusermap->store_id = $merchantStoreID;
                        $storeusermap->user_id = $device_manager->user_id;
                        $storeusermap->user_access_level_id = $access_level_details->id;
                        $storeusermap->save();

                        $status = StatusMaster::where("status", TEMPLATE_ACTIVE)->first();
                        $inactive_status = StatusMaster::where("status", TEMPLATE_INACTIVE)->first();
                        $params['status'] = $status->id;
                        $params['merchant_store_id'] = $merchantStoreID;
                        if (strtoupper($row['pos_web_identifier']) == TERMINAL_WEB) {
                            $params['terminal_name'] = "WEB " . $merchantstores->store_id;
                            $params['is_web'] = 1;
                            $this->_createTerminal($params);
                        } else if (strtoupper($row['pos_web_identifier']) == TERMINAL_POS) {
                            $params['terminal_name'] = "POS " . $merchantstores->store_id;
                            $params['is_web'] = 0;
                            $this->_createTerminal($params);
                        } else {
                            $params['terminal_name'] = "WEB " . $merchantstores->store_id;
                            $params['is_web'] = 1;
                            $this->_createTerminal($params);
                            $params['terminal_name'] = "POS " . $merchantstores->store_id;
                            $params['is_web'] = 0;
                            $this->_createTerminal($params);
                        }

                        // Check if API Key already exists for that merchant
                        $checkAPiKeyexists = MerchantApiKeyMap::where(['merchant_id' => $merchantstores->merchant_id, 'store_id' => $merchantStoreID])->first();
                        if (empty($checkAPiKeyexists)) {
                            //create all the api keys which will be used by the 3rd party merchants during API implementation
                            $key_details = new MerchantApiKeyMap();
                            $key_details->merchant_id = $merchantstores->merchant_id;
                            $key_details->store_id = $merchantStoreID;
                            $key_details->app_key = config('app.api_environment') . "_key_" . generateRandomString(8);
                            $key_details->api_secret = generateRandomString(8);
                            $key_details->status = strtoupper($row['pos_web_identifier']) == 'N' ? $inactive_status->id : $params['status'];
                            $key_details->save();

                            Log::channel('datamigration')->info(addslashes(__METHOD__) . "(" . LINE . ": " . __LINE__ . ") - " . "App Key and API Secret generated successfully for Store ID: " . $merchantstores->store_id . " and Merchant ID: " . $merchantstores->merchant_id);
                        }

                        DB::commit();
                        ++$this->rows;
                        Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Merchant and its relevant Information added for Merchant ID : " . $row['merchant_id'] . " and Store ID : " . $row['store_identifier']);
                        $this->storeIds[] = $merchantStoreID;
                    } else {
                        // Check if timezone exists in master table. If not then insert it and map it with store.
                        $checkTimezoneExists = TimezoneMaster::where('timezone_name', $timezone_details)->first();
                        if (empty($checkTimezoneExists)) {
                            $timezone = new TimezoneMaster();
                            $timezone->timezone_name = $timezone_details;
                            $timezone->save();
                            $timezone_id = $timezone->id;

                            // Insert the new timezone in Transaction posting decision table
                            $time = Carbon::now()->format('Y-m-d') . ' ' . config('app.transaction_posting_block_end_time') . ':00:00';
                            $date = DateTime::createFromFormat(DB_DATE_FORMAT, $time, new DateTimeZone('America/New_York'));
                            $date->setTimeZone(new DateTimeZone($timezone->timezone_name));
                            $transaction_posting_decision_table = new TransactionPostingDecisionTable();
                            $transaction_posting_decision_table->timezone_id = $timezone_id;
                            $transaction_posting_decision_table->start_time = "00:00:01";
                            $transaction_posting_decision_table->end_time = $date->format('H:i:s');
                            $transaction_posting_decision_table->save();
                        } else {
                            $timezone_id = $checkTimezoneExists->id;
                        }
                        // Update Merchant Store Details
                        $merchantStoreDetailsArr = array();
                        $merchantStoreDetailsArr['merchant_id'] = $checkStoreUnderMerchant->id;
                        $merchantStoreDetailsArr['lat'] = $lat;
                        $merchantStoreDetailsArr['long'] = $long;
                        $merchantStoreDetailsArr['timezone_id'] = $timezone_id;
                        $merchantStoreDetailsArr['rowDetails'] = $row;

                        $this->_updateMerchantStoreDetails($merchantStoreDetailsArr);

                        DB::commit();
                        Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Update Merchant Store Details for Merchant ID : " . $row['merchant_id'] . " and Store ID : " . $row['store_identifier']);
                        $this->storeIds[] = $checkStoreUnderMerchant->id;
                    }
                    $this->_merchantStorePetitionCheck($merchantStoreDetailsArr);
                } else {
                    Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Merchant Store insertion skipped due to non availability of Timezone.");
                    insertSkippedDataLog('V2 Merchant', 'merchant_id', $row['merchant_id'], "Merchant Store insertion skipped due to non availability of Timezone.", json_encode($row), 'Merchant excel');
                    DB::commit();
                }
            } else {
                Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Merchant Store insertion skipped due to non availability of Merchant ID.");
                insertSkippedDataLog('V2 Merchant', 'merchant_id', $row['merchant_id'], "Merchant Store insertion skipped due to non availability of Merchant name.", json_encode($row), 'Merchant excel');
                DB::commit();
            }
        } catch (\Exception $e) {
            Log::channel('datamigration')->error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured during insertion for Merchant ID : " . $row['merchant_id'] . ".", [EXCEPTION => $e]);
            DB::rollback();
            insertSkippedDataLog('V2 Merchant', 'merchant_id', $row['merchant_id'], $e, json_encode($row), 'Merchant excel');
            DB::commit();
        }
    }

    private function _createTerminal($params)
    {
        // Check if Terminal already exists for that store
        $checkTerminalExists = TerminalMaster::where(['merchant_store_id' => $params['merchant_store_id'], 'terminal_name' => $params['terminal_name']])->first();
        if (empty($checkTerminalExists)) {
            // Insertion begins in Terminal Master Table
            $terminal = new TerminalMaster();
            $terminal->merchant_store_id = $params['merchant_store_id'];
            $terminal->terminal_name = $params['terminal_name'];
            $terminal->unique_identification_id = generateUUID();
            $terminal->status = $params['status'];
            $terminal->is_web = $params['is_web'];
            $terminal->save();
        }
    }

    private function _getTimezone($lat, $long)
    {
        $response = $this->googleplaces->getTimezone($lat, $long);

        $result = json_decode($response, true);
        Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "after google locate.") . $result['status'];

        return $result['status'] == 'OK' ? $result['timeZoneId'] : '';
    }

    private function _addMerchantDetails($row)
    {
        $ach_identifier = generateAchIdentifier();
        // Ensure uniqueness of the generated string
        while (RegisteredMerchantMaster::where('ach_identifier', $ach_identifier)->exists()) {
            $ach_identifier = generateAchIdentifier();
        }

        Log::channel('datamigration')->info("ACH Identifier: " . $ach_identifier);
        // Insertion Started for Registered Merchant Master
        $merchantmaster = new RegisteredMerchantMaster();
        $merchantmaster->merchant_id = $row['merchant_id'];
        $merchantmaster->merchant_name = $row['first_name'] . ' ' . $row['last_name'];
        $merchantmaster->contact_no = $row['your_phone_number'];
        $merchantmaster->email = $row['your_email'];
        $merchantmaster->authorized_signer = $row['authorized_signer'] == 'No' ? 0 : 1;
        $merchantmaster->title = $row['your_title'];
        $merchantmaster->email_for_report = $row['what_email_should_reports_be_sent_to'];
        $merchantmaster->username = $row['user_name'];
        $merchantmaster->password = Hash::make($row['password']);
        $merchantmaster->volume_value = number_format($row['discount_rate_for_the_000_fee'] * 100, 2);
        $merchantmaster->per_transaction_value = str_replace('$', '', $row['transaction_fee_for_the_000_per_transaction_fee']);
        $merchantmaster->ach_identifier = $ach_identifier;
        $merchantmaster->save();

        $merchant_id = $merchantmaster->id;

        $status = StatusMaster::where('code', USER_ACTIVE)->first();

        // Insertion Started for Merchant's Legal Entity Info
        $legal_entity_info = new LegalEntityInfo();
        $legal_entity_info->merchant_id = $merchant_id;
        $legal_entity_info->legal_entity_name = $row['legal_entity_name'];
        $legal_entity_info->street_address = $row['street_address'];
        $legal_entity_info->city = $row['legal_entity_city'];
        $legal_entity_info->state = $row['legal_entity_state'];
        $legal_entity_info->zip = $row['legal_entity_zip'];
        $legal_entity_info->federal_tax_id = $row['federal_tax_id'];
        $business_type = BusinessTypeMaster::where('business_type', $row['business_type'])->first();
        $legal_entity_info->business_type_id = !empty($business_type) ? $business_type->id : null;
        $legal_entity_info->corporate_contact_name = $row['corporate_contact_name'];
        $legal_entity_info->title = $row['legal_entity_title'];
        $legal_entity_info->phone = $row['legal_entity_phone_number'];
        $legal_entity_info->email = $row['email'];
        $legal_entity_info->recepient_address = $row['mail_should_be_sent_to'];
        $legal_entity_info->save();

        // Insertion Started for Merchant's Authorised Signer Info
        $authorised_signer_info = new AuthorisedSignerInfo();
        $authorised_signer_info->merchant_id = $merchant_id;
        $authorised_signer_info->fullname = $row['full_name'];
        $authorised_signer_info->title = $row['title'];
        $authorised_signer_info->ssn = $row['social_security_number'];
        $authorised_signer_info->signer_email = $row['signer_email_address'];
        $authorised_signer_info->street_address = $row['residence_street_address'];
        $authorised_signer_info->city = $row['city'];
        $authorised_signer_info->state = $row['state'];
        $authorised_signer_info->zip = $row['zip'];
        $authorised_signer_info->phone = $row['phone_number'];
        $authorised_signer_info->save();

        // Insertion Started for Merchant's Bank Deposit Info
        $bank_details = new BankAccountInfo();
        $bank_details->merchant_id = $merchant_id;
        $bank_details->bank_name = $row['financial_institution_name'];
        $bank_details->banker = $row['financial_institution_contact_person'];
        $bank_details->routing_no = $row['routing_number'];
        $bank_details->account_no = $row['account_number'];
        $bank_details->account_no_last_four = getAccountNumberLastFourDigit($row['account_number']);
        $bank_details->fees_account_number = $row['fees_bank_account'];
        $status = StatusMaster::where('code', BANK_ACTIVE)->first();
        $bank_details->status = $status->id;
        $bank_details->save();

        return $merchant_id;
    }

    private function _updateMerchantDetails($row, $merchant_id)
    {
        // Update Registered Merchant Details
        $merchantmaster = RegisteredMerchantMaster::find($merchant_id);
        if ($row['first_name'] != '' && $row['last_name'] != '') {
            $merchantmaster->merchant_name = $row['first_name'] . ' ' . $row['last_name'];
        }
        $merchantmaster->contact_no = $row['your_phone_number'] != '' ? $row['your_phone_number'] : $merchantmaster->contact_no;
        $merchantmaster->email = $row['your_email'] != '' ? $row['your_email'] : $merchantmaster->email;
        $merchantmaster->authorized_signer = $row['authorized_signer'] == 'No' ? 0 : 1;
        $merchantmaster->title = $row['your_title'] != '' ? $row['your_title'] : $merchantmaster->title;
        $merchantmaster->email_for_report = $row['what_email_should_reports_be_sent_to'] != '' ? $row['what_email_should_reports_be_sent_to'] : $merchantmaster->email_for_report;
        $merchantmaster->username = $row['user_name'] != '' ? $row['user_name'] : $merchantmaster->username;
        $merchantmaster->volume_value = $row['discount_rate_for_the_000_fee'] != '' ? number_format($row['discount_rate_for_the_000_fee'] * 100, 2) : $merchantmaster->volume_value;
        $merchantmaster->per_transaction_value = $row['transaction_fee_for_the_000_per_transaction_fee'] != '' ? str_replace('$', '', $row['transaction_fee_for_the_000_per_transaction_fee']) : $merchantmaster->per_transaction_value;
        $merchantmaster->save();

        $status = StatusMaster::where('code', USER_ACTIVE)->first();

        // Update Merchant's Legal Entity Info
        $checkLegalInfoExists = LegalEntityInfo::where(['merchant_id' => $merchant_id])->first();
        $legal_entity_info = !empty($checkLegalInfoExists) ? LegalEntityInfo::where(['merchant_id' => $merchant_id])->first() : new LegalEntityInfo();
        $legal_entity_info->merchant_id = $merchant_id;
        $legal_entity_info->legal_entity_name = $row['legal_entity_name'] != '' ? $row['legal_entity_name'] : $legal_entity_info->legal_entity_name;
        $legal_entity_info->street_address = $row['street_address'] != '' ? $row['street_address'] : $legal_entity_info->street_address;
        $legal_entity_info->city = $row['legal_entity_city'] != '' ? $row['legal_entity_city'] : $legal_entity_info->city;
        $legal_entity_info->state = $row['legal_entity_state'] != '' ? $row['legal_entity_state'] : $row['legal_entity_state'];
        $legal_entity_info->zip = $row['legal_entity_zip'] != '' ? $row['legal_entity_zip'] : $legal_entity_info->zip;
        $legal_entity_info->federal_tax_id = $row['federal_tax_id'] != '' ? $row['federal_tax_id'] : $legal_entity_info->federal_tax_id;
        $business_type = BusinessTypeMaster::where('business_type', $row['business_type'])->first();
        $legal_entity_info->business_type_id = !empty($business_type) ? $business_type->id : null;
        $legal_entity_info->corporate_contact_name = $row['corporate_contact_name'] != '' ? $row['corporate_contact_name'] : $legal_entity_info->corporate_contact_name;
        $legal_entity_info->title = $row['legal_entity_title'] != '' ? $row['legal_entity_title'] : $legal_entity_info->title;
        $legal_entity_info->phone = $row['legal_entity_phone_number'] != '' ? $row['legal_entity_phone_number'] : $legal_entity_info->phone;
        $legal_entity_info->email = $row['email'] != '' ? $row['email'] : $legal_entity_info->email;
        $legal_entity_info->recepient_address = $row['mail_should_be_sent_to'] != '' ? $row['mail_should_be_sent_to'] : $legal_entity_info->recepient_address;
        $legal_entity_info->save();

        // Update Merchant's Authorised Signer Info
        $checkAuthorisedSignerExists = AuthorisedSignerInfo::where(['merchant_id' => $merchant_id])->first();
        $authorised_signer_info = !empty($checkAuthorisedSignerExists) ? AuthorisedSignerInfo::where(['merchant_id' => $merchant_id])->first() : new AuthorisedSignerInfo();
        $authorised_signer_info->merchant_id = $merchant_id;
        $authorised_signer_info->fullname = $row['full_name'] != '' ? $row['full_name'] : $authorised_signer_info->fullname;
        $authorised_signer_info->title = $row['title'] != '' ? $row['title'] : $authorised_signer_info->title;
        $authorised_signer_info->ssn = $row['social_security_number'] != '' ? $row['social_security_number'] : $authorised_signer_info->ssn;
        $authorised_signer_info->signer_email = $row['signer_email_address'] != '' ? $row['signer_email_address'] : $authorised_signer_info->signer_email;
        $authorised_signer_info->street_address = $row['residence_street_address'] != '' ? $row['residence_street_address'] : $authorised_signer_info->street_address;
        $authorised_signer_info->city = $row['city'] != '' ? $row['city'] : $authorised_signer_info->city;
        $authorised_signer_info->state = $row['state'] != '' ? $row['state'] : $authorised_signer_info->state;
        $authorised_signer_info->zip = $row['zip'] != '' ? $row['zip'] : $authorised_signer_info->zip;
        $authorised_signer_info->phone = $row['phone_number'] != '' ? $row['phone_number'] : $authorised_signer_info->phone;
        $authorised_signer_info->save();

        // Update Merchant's Bank Deposit Info
        $checkBankInfoExists = BankAccountInfo::where(['merchant_id' => $merchant_id])->first();
        $bank_details = !empty($checkBankInfoExists) ? BankAccountInfo::where(['merchant_id' => $merchant_id])->first() : new BankAccountInfo();
        $bank_details->merchant_id = $merchant_id;
        $bank_details->bank_name = $row['financial_institution_name'] != '' ? $row['financial_institution_name'] : $bank_details->bank_name;
        $bank_details->banker = $row['financial_institution_contact_person'] != '' ? $row['financial_institution_contact_person'] : $bank_details->banker;
        $bank_details->routing_no = $row['routing_number'] != '' ? $row['routing_number'] : $bank_details->routing_no;
        $bank_details->account_no = $row['account_number'] != '' ? $row['account_number'] : $bank_details->account_no;
        $bank_details->account_no_last_four = getAccountNumberLastFourDigit($bank_details->account_no);
        $bank_details->fees_account_number = $row['fees_bank_account'];
        $status = StatusMaster::where('code', BANK_ACTIVE)->first();
        $bank_details->status = $status->id;
        $bank_details->save();

        return $merchant_id;
    }

    private function _addMerchantStoreDetails($merchantStoreDetailsArr)
    {
        //Fetch the Active Status ID from Status Master table
        $storestatus = StatusMaster::where('status', ACTIVE)->first();
        $storestatusid = $storestatus->id;

        $merchantstores = new MerchantStores();
        $merchantstores->merchant_id = $merchantStoreDetailsArr['merchant_id'];
        $merchantstores->store_id = $merchantStoreDetailsArr['actualStoreCode'];
        $merchantstores->retailer = $merchantStoreDetailsArr['rowDetails']['dba_name_of_location'];
        $merchantstores->lat = $merchantStoreDetailsArr['lat'];
        $merchantstores->long = $merchantStoreDetailsArr['long'];
        $merchantstores->timezone_id = $merchantStoreDetailsArr['timezone_id'];
        $merchantstores->address = $merchantStoreDetailsArr['rowDetails']['dba_street_address'];
        $merchantstores->city = $merchantStoreDetailsArr['rowDetails']['dba_city'];
        $merchantstores->state = $merchantStoreDetailsArr['rowDetails']['dba_state'];
        $merchantstores->zip = $merchantStoreDetailsArr['rowDetails']['dba_zip'];
        $merchantstores->county = $merchantStoreDetailsArr['rowDetails']['county'];
        $merchantstores->contact_no = $merchantStoreDetailsArr['rowDetails']['dba_phone_number'];
        $merchantstores->email = $merchantStoreDetailsArr['rowDetails']['signer_email_address'];
        $merchantstores->website_address = $merchantStoreDetailsArr['rowDetails']['website_address'];
        $merchantstores->location_type = $merchantStoreDetailsArr['rowDetails']['account_type'];
        $merchantstores->average_monthly_sales = $merchantStoreDetailsArr['rowDetails']['average_monthly_total_monthly_sales'];
        $merchantstores->average_ticket_per_sale = $merchantStoreDetailsArr['rowDetails']['average_ticket_per_sale'];
        $transaction_type = TransactionTypeMaster::where('type', $merchantStoreDetailsArr['rowDetails']['state_marijuana_license_types'])->first();
        $merchantstores->transaction_type_id = isset($transaction_type->id) ? $transaction_type->id : null;
        $pos_provider = POSMaster::where('name', $merchantStoreDetailsArr['rowDetails']['pos_provider'])->first();
        $merchantstores->pos_provider = !empty($pos_provider) ? $pos_provider->id : null;
        $merchantstores->recreational_retail_terminal_needed = $merchantStoreDetailsArr['rowDetails']['how_many_recreational_or_retail_canpay_terminals_will_be_needed'];
        $merchantstores->medical_terminal_needed = $merchantStoreDetailsArr['rowDetails']['how_many_medical_canpay_terminals_will_be_needed'];
        $merchantstores->multi_tablet_charging_stand = $merchantStoreDetailsArr['rowDetails']['would_you_like_to_include_a_multi_tablet_charging_stands'];
        $merchantstores->hardware_shipped = $merchantStoreDetailsArr['rowDetails']['where_should_hardware_be_shipped'];
        $merchantstores->merchant_daily_velocity_limit = $merchantStoreDetailsArr['rowDetails']['merchant_daily_velocity_limit'];
        $merchantstores->status = $storestatusid;
        $merchantstores->save();

        return $merchantstores->id;
    }

    private function _updateMerchantStoreDetails($merchantStoreEditDetailsArr)
    {
        $merchantstores = MerchantStores::find($merchantStoreEditDetailsArr['merchant_id']);
        $merchantstores->retailer = $merchantStoreEditDetailsArr['rowDetails']['dba_name_of_location'] ? $merchantStoreEditDetailsArr['rowDetails']['dba_name_of_location'] : $merchantstores->retailer;
        $merchantstores->lat = $merchantStoreEditDetailsArr['lat'];
        $merchantstores->long = $merchantStoreEditDetailsArr['long'];
        $merchantstores->timezone_id = $merchantStoreEditDetailsArr['timezone_id'];
        $merchantstores->address = $merchantStoreEditDetailsArr['rowDetails']['dba_street_address'] ? $merchantStoreEditDetailsArr['rowDetails']['dba_street_address'] : $merchantstores->address;
        $merchantstores->city = $merchantStoreEditDetailsArr['rowDetails']['dba_city'] ? $merchantStoreEditDetailsArr['rowDetails']['dba_city'] : $merchantstores->city;
        $merchantstores->state = $merchantStoreEditDetailsArr['rowDetails']['dba_state'] ? $merchantStoreEditDetailsArr['rowDetails']['dba_state'] : $merchantstores->state;
        $merchantstores->zip = $merchantStoreEditDetailsArr['rowDetails']['dba_zip'] ? $merchantStoreEditDetailsArr['rowDetails']['dba_zip'] : $merchantstores->zip;
        $merchantstores->county = $merchantStoreEditDetailsArr['rowDetails']['county'] ? $merchantStoreEditDetailsArr['rowDetails']['county'] : $merchantstores->county;
        $merchantstores->contact_no = $merchantStoreEditDetailsArr['rowDetails']['dba_phone_number'] ? $merchantStoreEditDetailsArr['rowDetails']['dba_phone_number'] : $merchantstores->contact_no;
        $merchantstores->email = $merchantStoreEditDetailsArr['rowDetails']['signer_email_address'] ? $merchantStoreEditDetailsArr['rowDetails']['signer_email_address'] : $merchantstores->email;
        $merchantstores->website_address = $merchantStoreEditDetailsArr['rowDetails']['website_address'] ? $merchantStoreEditDetailsArr['rowDetails']['website_address'] : $merchantstores->website_address;
        $merchantstores->location_type = $merchantStoreEditDetailsArr['rowDetails']['account_type'] ? $merchantStoreEditDetailsArr['rowDetails']['account_type'] : $merchantstores->location_type;
        $merchantstores->average_monthly_sales = $merchantStoreEditDetailsArr['rowDetails']['average_monthly_total_monthly_sales'] ? $merchantStoreEditDetailsArr['rowDetails']['average_monthly_total_monthly_sales'] : $merchantstores->average_monthly_sales;
        $merchantstores->average_ticket_per_sale = $merchantStoreEditDetailsArr['rowDetails']['average_ticket_per_sale'] ? $merchantStoreEditDetailsArr['rowDetails']['average_ticket_per_sale'] : $merchantstores->average_ticket_per_sale;
        $transaction_type = TransactionTypeMaster::where('type', $merchantStoreEditDetailsArr['rowDetails']['state_marijuana_license_types'])->first();
        $merchantstores->transaction_type_id = isset($transaction_type->id) ? $transaction_type->id : $merchantstores->transaction_type_id;
        $pos_provider = POSMaster::where('name', $merchantStoreEditDetailsArr['rowDetails']['pos_provider'])->first();
        $merchantstores->pos_provider = !empty($pos_provider) ? $pos_provider->id : $merchantstores->pos_provider;
        $merchantstores->recreational_retail_terminal_needed = $merchantStoreEditDetailsArr['rowDetails']['how_many_recreational_or_retail_canpay_terminals_will_be_needed'] ? $merchantStoreEditDetailsArr['rowDetails']['how_many_recreational_or_retail_canpay_terminals_will_be_needed'] : $merchantstores->recreational_retail_terminal_needed;
        $merchantstores->medical_terminal_needed = $merchantStoreEditDetailsArr['rowDetails']['how_many_medical_canpay_terminals_will_be_needed'] ? $merchantStoreEditDetailsArr['rowDetails']['how_many_medical_canpay_terminals_will_be_needed'] : $merchantstores->medical_terminal_needed;
        $merchantstores->multi_tablet_charging_stand = $merchantStoreEditDetailsArr['rowDetails']['would_you_like_to_include_a_multi_tablet_charging_stands'] ? $merchantStoreEditDetailsArr['rowDetails']['would_you_like_to_include_a_multi_tablet_charging_stands'] : $merchantstores->multi_tablet_charging_stand;
        $merchantstores->hardware_shipped = $merchantStoreEditDetailsArr['rowDetails']['where_should_hardware_be_shipped'] ? $merchantStoreEditDetailsArr['rowDetails']['where_should_hardware_be_shipped'] : $merchantstores->hardware_shipped;
        $merchantstores->merchant_daily_velocity_limit = $merchantStoreEditDetailsArr['rowDetails']['merchant_daily_velocity_limit'] ? $merchantStoreEditDetailsArr['rowDetails']['merchant_daily_velocity_limit'] : $merchantstores->merchant_daily_velocity_limit;
        $merchantstores->save();
        return true;
    }

    private function _merchantStorePetitionCheck($merchantStoreDetailsArr)
    {
        // Map Merchant and Petition
        $provisioned = getStatus(PROVISIONED);
        $petition = Petition::where('store_name', $merchantStoreDetailsArr['rowDetails']['dba_name_of_location'])
            ->where('street_address', $merchantStoreDetailsArr['rowDetails']['dba_street_address'])
            ->where('city', $merchantStoreDetailsArr['rowDetails']['dba_city'])
            ->where('state', $merchantStoreDetailsArr['rowDetails']['dba_state'])
            ->where('zipcode', $merchantStoreDetailsArr['rowDetails']['dba_zip'])
            ->where('status_id', $provisioned)
            ->whereNull('onboarded_date')
            ->first();

        if ($petition) {
            merchantStorePetitionMap($merchantStoreDetailsArr['merchant_id'], $petition->id, 'datamigration');
        } else {
            Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No active petition found for the given address.");
        }

        return true;
    }

    public function batchSize(): int
    {
        return 1000;
    }

    public function chunkSize(): int
    {
        return 5000;
    }

    public function getRowCount(): int
    {
        return $this->rows;
    }

    // Method to get the inserted store IDs
    public function getInsertedStoreIds()
    {
        return $this->storeIds;
    }
}
