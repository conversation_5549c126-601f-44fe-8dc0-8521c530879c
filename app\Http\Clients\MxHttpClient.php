<?php

namespace App\Http\Clients;

use App\Models\BankingSolutionMaster;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;

/**
 *
 * @package App\Http\Clients
 */

class MxHttpClient
{
    /**
     * @var Client
     */
    private $client;
    private $clientNoError;
    private $commonHeaders;
    private $client_id;
    private $api_key;
    private $mx_id;
    /**
     * AkoyaHttpClient constructor.
     */
    public function __construct()
    {
        $this->client_id = config('app.mx_client_id');
        $this->api_key = config('app.mx_api_key');
        $this->client = new Client(['base_uri' => config('app.mx_base_url')]);
        $this->clientNoError = new Client(['base_uri' => config('app.mx_base_url'), 'http_errors' => false]);
        $this->commonHeaders = [
            'Accept' => 'application/vnd.mx.api.v1+json',
            'Authorization' => 'Basic ' . base64_encode("$this->client_id:$this->api_key"),
        ];
        $this->mx_id = BankingSolutionMaster::where('banking_solution_name', MX)->first()->id;
    }

    /**
     * getAccountDetails
     * This function will fetch a specific account details for a user
     * @param  mixed $params
     * @return void
     */
    public function getAccountDetails($params)
    {
        try {
            $params['api'] = '/users/' . $params['user_guid'] . '/accounts/' . $params['account_guid'];

            $params['data'] = [
                'headers' => $this->commonHeaders,
            ];
            $response = $this->client->get($params['api'], $params['data']);
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "MX get user account details returned response: " . $response->getBody());
            saveBankingSolutionResponseData($params, $response->getBody(), $this->mx_id);
            return $response->getBody();
        } catch (\GuzzleHttp\Exception\ClientException $ex) {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while get user account details in MX.", [EXCEPTION => $ex]);
            return $ex->getResponse()->getBody()->getContents();
        }
    }

    /**
     * identifyMember
     * This function call identify member api which is called before calling the Account Owner API
     * @param  mixed $params
     * @return void
     */
    public function identifyMember($params)
    {
        // TODO: Identify API call stops due to high billing.
        $fake_response = '{"member":{"guid":"MBR-fake-response","id":null,"user_guid":"USR-fake-response","user_id":"fake-response","aggregated_at":"2024-08-13T20:50:08Z","background_aggregation_is_disabled":true,"institution_code":"chase","is_being_aggregated":true,"is_managed_by_user":true,"is_manual":false,"is_oauth":true,"metadata":null,"name":"Chase Bank","oauth_window_uri":null,"successfully_aggregated_at":"2024-08-13T13:13:51Z","most_recent_job_detail_code":null,"most_recent_job_detail_text":null,"connection_status":"DENIED"}}';
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "MX identify returned fake response due to high billing: " . $fake_response);
        return $fake_response;
        
        /* try {
            $params['api'] = 'users/' . $params['user_guid'] . '/members/' . $params['member_guid'] . '/identify';

            $params['data'] = [
                'headers' => $this->commonHeaders,
            ];
            $response = $this->client->post($params['api'], $params['data']);
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "MX identify member API returned response: " . $response->getBody());
            saveBankingSolutionResponseData($params, $response->getBody(), $this->mx_id);
            return $response->getBody();
        } catch (\GuzzleHttp\Exception\ClientException $ex) {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while calling identify member API in MX.", [EXCEPTION => $ex]);
            return $ex->getResponse()->getBody()->getContents();
        } */
    }

    /**
     * getAccountOwners
     * This function will fetch the Account Owners for each Account
     * @param  mixed $params
     * @return void
     */
    public function getAccountOwners($params)
    {
        try {
            $params['api'] = '/users/' . $params['user_guid'] . '/members/' . $params['member_guid'] . '/account_owners';

            $params['data'] = [
                'headers' => $this->commonHeaders,
            ];
            $response = $this->client->get($params['api'], $params['data']);
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "MX get account owners API returned response: " . $response->getBody());
            saveBankingSolutionResponseData($params, $response->getBody(), $this->mx_id);
            return $response->getBody();
        } catch (\GuzzleHttp\Exception\ClientException $ex) {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while getting account owners in MX.", [EXCEPTION => $ex]);
            return $ex->getResponse()->getBody()->getContents();
        }
    }


    /**
     * getUserAccounts
     * This function will fetch accounts for a specific user
     * @param  mixed $params
     * @return void
     */
    public function getUserAccounts($params)
    {
        try {
            $params['api'] = '/users/' . $params['mx_consumer_id'] . '/accounts?page=' . $params['page'] . '&records_per_page=25';

            $params['data'] = [
                'headers' => $this->commonHeaders,
            ];
            $params['consumer_id'] = $params['mx_consumer_id'];
            $response = $this->client->get($params['api'], $params['data']);
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "MX get user accounts returned response: " . $response->getBody());
            saveBankingSolutionResponseData($params, $response->getBody(), $this->mx_id);
            return $response->getBody();
        } catch (\GuzzleHttp\Exception\ClientException $ex) {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while get user accounts in MX.", [EXCEPTION => $ex]);
            return $ex->getResponse()->getBody()->getContents();
        }
    }

    /**
     * getMemberData
     * This function will fetch the Memebr Data
     * @param  mixed $params
     * @return void
     */
    public function getMemberData($params)
    {
        try {
            $params['api'] = '/users/' . $params['user_guid'] . '/members/' . $params['member_guid'];

            $params['data'] = [
                'headers' => $this->commonHeaders,
            ];
            $response = $this->client->get($params['api'], $params['data']);
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "MX get member details API returned response: " . $response->getBody());
            saveBankingSolutionResponseData($params, $response->getBody(), $this->mx_id);
            return $response->getBody();
        } catch (\GuzzleHttp\Exception\ClientException $ex) {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while getting member details in MX.", [EXCEPTION => $ex]);
            return $ex->getResponse()->getBody()->getContents();
        }
    }

    /**
     * getMembers
     * This function will fetch all mermers 
     * @param  mixed $params
     * @return void
     */
    public function getMembers($params)
    {
        try {
            $params['api'] = '/users/' . $params['mx_consumer_id'] . '/members?page=' . $params['page'] . '&records_per_page=25';

            $params['data'] = [
                'headers' => $this->commonHeaders,
            ];
            $response = $this->client->get($params['api'], $params['data']);
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "MX get user members returned response for Consumer ID: ".$params['consumer_id']." is " . $response->getBody());
            saveBankingSolutionResponseData($params, $response->getBody(), $this->mx_id);
            return $response->getBody();
        } catch (\GuzzleHttp\Exception\ClientException $ex) {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while get user account details in MX for Consumer ID: ".$params['consumer_id'], [EXCEPTION => $ex]);
            return $ex->getResponse()->getBody()->getContents();
        }
    }
}
