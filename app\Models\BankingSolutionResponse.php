<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class BankingSolutionResponse extends Model
{

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'banking_solution_id',
        'consumer_id',
        'api',
        'akoya_interaction_id',
        'response',
    ];
    public $timestamps = true;
    public $incrementing = false;
    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
}
