<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Events\AfterSheet;

class DeliverySettlementReportExport implements FromCollection, WithHeadings, ShouldAutoSize, WithEvents
{
    protected $request;
    protected $singularDebit;

    public function __construct($request)
    {
        $this->request = $request; // Declaring the request variable
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        $collection_array = $this->request['report']; // Storing the array received from request
        return collect(
            $collection_array,
        );
    }

    public function headings(): array
    {
        $returnArray = array(
            // 1st header
            [
                'Delivery Settlement Report',
            ],
            // 2nd header
            [
                'Sales Date Range:',
                '',
                date('m/d/Y', strtotime($this->request['from_date'])) . ' - ' . date('m/d/Y', strtotime($this->request['to_date'])),
            ],
        );

        return $returnArray;
    }

    public function registerEvents(): array
    {
        $count = count($this->request['report']);
        return [
            AfterSheet::class => function (AfterSheet $event) use ($count) {
                $event->sheet->getStyle('A3:F3')->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);
                $event->sheet->getStyle('A3:F3')->getAlignment()->applyFromArray(
                    array('horizontal' => 'center')
                );
                //Center align all columns
                $event->sheet->getStyle('A4:F' . ($count + 6))->getAlignment()->applyFromArray(
                    array('horizontal' => 'center')
                );

                //Bold Total Volume Column
                $event->sheet->getStyle('B4:B' . ($count + 6))->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);

                //Bold Total Fees Column
                $event->sheet->getStyle('F4:F' . ($count + 6))->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);

                $boldColumns = ['C', 'G'];

                // Apply bold styling to all specified columns
                foreach ($boldColumns as $column) {
                    $event->sheet->getStyle($column . '4:' . $column . ($count + 2))->applyFromArray([
                        'font' => [
                            'bold' => true,
                        ],
                    ]);
                }

                $event->sheet->getStyle('A3:F3')->applyFromArray([
                    'borders' => [
                        'allBorders' => [
                            'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THICK,
                            'color' => ['argb' => '000000'],
                        ],
                    ],
                ]);

                for ($i = 4; $i <= ($count + 6); $i++) {
                    $event->sheet->getStyle('B' . $i . ':F' . $i)->applyFromArray([
                        'borders' => [
                            'allBorders' => [
                                'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                                'color' => ['argb' => '000000'],
                            ],
                        ],
                    ]);
                }
                $event->sheet->getStyle('B4:B' . ($count + 6))->applyFromArray([
                    'borders' => [
                        'right' => [
                            'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THICK,
                            'color' => ['argb' => '000000'],
                        ],
                    ],
                ]);
                for ($i = 1; $i <= ($count + 6); $i++) {
                    $event->sheet->getStyle('A' . $i . ':AB' . $i)->applyFromArray([
                        'font' => [
                            'size' => 7,
                        ],
                    ]);
                }

                // Merging cells
                $event->sheet->mergeCells('A1:B1');
                $event->sheet->mergeCells('A2:B2');
                $event->sheet->mergeCells('A3:B3');
                $event->sheet->mergeCells('C3:F3');
                $event->sheet->setCellValue('C3', 'Delivery Partner Activity');

                //Total For the First Store
                $event->sheet->getStyle('A5' . ':F5')->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);

                $endRow = $count + 6;
                $event->sheet->getStyle('A' . $endRow . ':F' . $endRow)->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);
            },
        ];
    }
}
