<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;


class TransactionModificationHistory extends Model
{
    

    protected $table = 'transaction_modification_history';
    /**
     * __construct
     * This function will insert an alphanumeric 32 character unique id as primary key in the table whenever a data get inserted
     * @param  mixed $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        $this->id = generateUUID();

        parent::__construct($attributes);
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'transaction_id',
        'amount',
        'tip_amount',
        'local_transaction_time',
        'local_transaction_date',
        'local_transaction_month',
        'local_transaction_year',
        'status_id',
        'reason_id',
        'additional_reason',
        'viewed_datetime',
        'expiration_datetime',
        'timezone_name'
    ];
    public $timestamps = true;
    public $incrementing = false;
    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
}
