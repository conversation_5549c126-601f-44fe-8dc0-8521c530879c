<template>
<div>
  <div v-if="loading">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Unknown Routing Numbers</h3>
                  <b-button
                    class="btn-danger export-api-btn"
                    @click="reloadDatatable"
                    v-if="showReloadBtn"
                  >
                    <i class="fas fa-redo"></i> Reload
                  </b-button>
                </div>
                <!-- /.card-header -->
                <div class="card-body">
                  <table
                    id="routingNumbersTable"
                    class="table"
                    style="width: 100%; white-space: normal"
                  >
                    <thead>
                      <tr>
                        <th>Institution Name</th>
                        <th>Consumer Name</th>
                        <th>Email</th>
                        <th>Phone</th>
                        <th>Routing Number</th>
                        <th>Created At</th>
                        <th>Action</th>
                      </tr>
                    </thead>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
    <b-modal
      id="view-transaction-history-modal"
      ref="view-transaction-history-modal"
      :header-text-variant="headerTextVariant"
      title="Transaction History"
      hide-footer
    >

      <div class="row odd-row">
        <div class="col-md-4 row-value">
          <label for="name">Consumer Name</label>
        </div>
        <div class="col-md-1 row-value">:</div>
        <div class="col-md-7 row-value">
          <span for="name">{{ consumerModel.first_name }} {{ consumerModel.middle_name }} {{ consumerModel.last_name }}</span>
        </div>
      </div>
      <div class="row even-row">
        <div class="col-md-4 row-value">
          <label for="access_rights">Email</label>
        </div>
        <div class="col-md-1 row-value">:</div>
        <div class="col-md-7 row-value">
          <span for="name">{{ consumerModel.email }}</span>
        </div>
      </div>
      <div class="row odd-row">
        <div class="col-md-4 row-value">
          <label for="access_rights">Phone</label>
        </div>
        <div class="col-md-1 row-value">:</div>
        <div class="col-md-7 row-value">
          <span for="name">{{ consumerModel.phone }}</span>
        </div>
      </div>
      <div class="row even-row">
        <div class="col-md-4 row-value">
          <label for="access_other_org_info">Address</label>
        </div>
        <div class="col-md-1 row-value">:</div>
        <div class="col-md-7 row-value">
          <span for="name">{{
            consumerModel.street_address +
              ", " +
              consumerModel.city +
              ", " +
              consumerModel.state +
              ", "  +
              consumerModel.zipcode
          }}</span>
        </div>
      </div>
      <div class="card-body">
        <h3>Transaction History</h3>
        <b-table-simple
            responsive
            show-empty
            bordered
            v-if="consumerModel.transaction_number != null"
        >
            <b-thead head-variant="light">
            <tr>
                <th>Transaction No.</th>
                <th>Amount</th>
                <th>Date</th>
                <th>Status</th>
            </tr>
            </b-thead>
            <b-tbody v-for="(row, index) in transactionModel" :key="index">
            <b-tr>
                <b-td class="text-left text-gray">{{
                row.transaction_number
                }}</b-td>
                <b-td class="text-left text-gray">{{
                row.total_amount
                }}</b-td>
                <b-td class="text-left text-gray">{{
                row.local_transaction_date
                }}</b-td>
                <b-td class="text-left text-gray">{{
                row.status
                }}</b-td>
            </b-tr>
            </b-tbody>
        </b-table-simple>
        <p v-else>No transaction history found for this consumer.</p>
        </div>
    </b-modal>
  </div>
</div>
</template>
<script>
import api from "@/api/whitelist.js";
import commonConstants from "@/common/constant.js";
import CanPayLoader from "./CustomLoader/CanPayLoader.vue"
export default {
  components:{
    CanPayLoader
  },
  data() {
    return {
      showMsg: false,
      headerTextVariant: "light",
      allRoutingNumbers: {},
      routing_number: "",
      spending_limit: "",
      showReloadBtn: false,
      constants: commonConstants,
      routingId: null,
      consumerId: null,
      transactionModel: {},
      consumerModel: {},
      loading:false
    };
  },
  created() {
    this.addRoutingToMaster();
    this.declineRouting();
    this.viewTransactionHistory();
  },
  methods: {
    reloadDatatable() {
      var self = this;
      self.loadDT();
    },
    addRoutingToMaster() {
      var self = this;
      $(document).on("click", ".addRoutingToMaster", function(e) {
        self.routingId = $(e.currentTarget).attr("data-routing-id");
        Vue.swal({
            title: "Are you sure to approve this Routing Number?",
            text: "Once done you will not be able to undo this action!",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#149240",
            confirmButtonText: "Yes, approve the number!",
            cancelButtonText: "No, cancel it!",
            closeOnConfirm: true,
            closeOnCancel: true,
            width: '800px'
            }).then((result) => {
                if (result.isConfirmed) {
                    let request = {
                        id: self.routingId
                    }
                    self.loading = true;
                    api
                    .addRoutingToMaster(request)
                    .then(response => {
                        if ((response.code == 200)) {
                            Vue.swal("Done!", response.message, "success");
                            self.loadDT();
                            self.loading = false;
                        } else {
                          self.loading = false;
                            Vue.swal(response.message, '', 'error')
                        }
                        
                    })
                    .catch(err => {
                      self.loading = false;
                        Vue.swal(err.response.data.message, '', 'error')
                    });
                }
            })
      });
    },
    declineRouting() {
      var self = this;
      $(document).on("click", ".declineRouting", function(e) {
        self.routingId = $(e.currentTarget).attr("data-routing-id");
        Vue.swal({
            title: "Are you sure to decline this Routing Number?",
            text: "Once done you will not be able to undo this action!",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#149240",
            confirmButtonText: "Yes, decline the number!",
            cancelButtonText: "No, cancel it!",
            closeOnConfirm: true,
            closeOnCancel: true,
            width: '800px'
            }).then((result) => {
                if (result.isConfirmed) {
                    let request = {
                        id: self.routingId
                    }
                    self.loading = true;
                    api
                    .declineRouting(request)
                    .then(response => {
                        if ((response.code == 200)) {
                            Vue.swal("Done!", response.message, "success");
                            self.loadDT();
                            self.loading = false;
                        } else {
                          self.loading = false;
                            Vue.swal(response.message, '', 'error')
                        }
                    })
                    .catch(err => {
                      self.loading = false;
                        Vue.swal(err.response.data.message, '', 'error')
                    });
                }
            })
      });
    },
    viewTransactionHistory() {
      var self = this;
      $(document).on("click", ".viewTransactionHistory", function(e) {
        self.consumerId = $(e.currentTarget).attr("data-consumer-id");
        let request = {
            consumer_id: self.consumerId
        }
        self.loading = true;
        api
              .viewTransactionHistory(request)
              .then((response) => {
                if (response.code == 200) {
                    self.transactionModel = response.data;
                    self.consumerModel = response.data[0];
                    self.$bvModal.show("view-transaction-history-modal");
                } else {
                  error(response.message);
                }
                self.loading = false;
              })
              .catch((err) => {
                self.loading = false;
                error(err.response.data.message);
              });
        });
    },
    loadDT: function () {
      var self = this;
      $("#routingNumbersTable").DataTable({
        pagingType: "simple_numbers",
        processing: true,
        serverSide: true,
        destroy: true,
        columnDefs: [
            {orderable: false, targets: [6] },
            { className: "dt-left", targets: [0, 1, 2, 3, 4, 5]},
            { className: "dt-center", targets: [6] },
          ],
        order: [[5, "desc"]],
        orderClasses: false,
        language: {
          processing:
            '<i class="fa fa-spinner fa-spin fa-3x fa-fw"></i><span class="sr-only">Loading...</span> ',
          emptyTable: "No Unknown Routing Number Available.",
          search: "_INPUT_",
          searchPlaceholder: "Search records",
          oPaginate: {
            sNext: '<i class="fas fa-angle-double-right"></i>',
            sPrevious: '<i class="fas fa-angle-double-left"></i>',
          },
          sLengthMenu:
            "<label class='label_dropdown_dt'>Per page</label> _MENU_",
        },
        ajax: {
          headers: {
            Authorization: "Bearer " + localStorage.getItem("token"),
          },
          url: "/api/getunknownroutingnumbers",
          type: "POST",
          data: { _token: "{{csrf_token()}}" },
          dataType: "json",
          dataSrc: function (result) {
            self.showReloadBtn = false;
            self.allRoutingNumbers = result.data;
            return self.allRoutingNumbers;
          },
          error: function (data) {
            error(commonConstants.datatable_error);
            $("#routingNumbersTable_processing").hide();
            self.showReloadBtn = true;
          },
        },
        columns: [
          { data: "institution_name" },
          { data: "consumer_name" },
          { data: "email" },
          { data: "phone" },
          { data: "routing_number" },
          { data: "created_at" },
          {
            render: function(data, type, full, meta) {
              return (
                '<b-button data-consumer-id="' +
                full.consumer_id +
                '" class="viewTransactionHistory custom-edit-btn" title="View Transaction hsitory of this Consumer" variant="outline-success"><i class="nav-icon fas fa-eye"></i></b-button>&nbsp;&nbsp;<b-button data-routing-id="' +
                full.edit +
                '" class="addRoutingToMaster custom-edit-btn" title="Approve this Routing Number" variant="outline-success"><i class="nav-icon fas fa-check"></i></b-button>&nbsp;&nbsp;<b-button data-routing-id="' +
                full.edit +
                '" class="declineRouting red" title="Decline this Routing Number" style="cursor: pointer" variant="outline-danger"><i class="nav-icon fas fa-times"></i></b'
              );
            }
          }
        ],
      });

      $("#routingNumbersTable").on("page.dt", function () {
        $("html, body").animate({ scrollTop: 0 }, "slow");
        $("th:first-child").focus();
      });

      //Search in the table only after 3 characters are typed
      // Call datatables, and return the API to the variable for use in our code
      // Binds datatables to all elements with a class of datatable
      var dtable = $("#routingNumbersTable").dataTable().api();

      // Grab the datatables input box and alter how it is bound to events
      $(".dataTables_filter input")
        .unbind() // Unbind previous default bindings
        .bind("input", function (e) {
          // Bind our desired behavior
          // If the length is 3 or more characters, or the user pressed ENTER, search
          if (this.value.length >= 3 || e.keyCode == 13) {
            // Call the API search function
            dtable.search(this.value).draw();
          }
          // Ensure we clear the search if they backspace far enough
          if (this.value == "") {
            dtable.search("").draw();
          }
          return;
        });
    },
  },
  mounted() {
    var self = this;
    self.loading = true;
    setTimeout(function () {
      self.loadDT();
      self.loading = false;
    }, 1000);
    document.title = "CanPay - Unknown Routing Numbers";
  },
};
</script>

