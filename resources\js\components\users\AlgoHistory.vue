<template>
<div>
  <div v-if="loading">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Algo Response History</h3>
                </div>
                <!-- /.card-header -->
                <div class="card-body">
                  <div class="row">
                    <div class="col-md-4">
                      <div class="form-group">
                      <input
                          class="form-control"
                          placeholder="Consumer Name (min 3 chars)"
                          id="consumer"
                          v-model="consumer"
                        />
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group">
                        <input
                          class="form-control"
                          placeholder="Phone No (Exact)"
                          id="phone_no"
                          v-model="phone_no"
                        />
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group">
                        <input
                          class="form-control"
                          placeholder="Email (Exact)"
                          id="email"
                          v-model="email"
                        />
                      </div>
                    </div>
                  </div>
                <div class="row">
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        autocomplete="off"
                        class="start-date form-control"
                        placeholder="Registration Date"
                        id="start-date"
                        onkeydown="return false"
                      />
                    </div>
                  </div>
                </div>
                </div>
                  <div class="card-footer">
                    <button
                      type="button"
                      class="btn btn-success"
                      @click="searchConsumerAlgoHistory()"
                    >
                      Search
                    </button>
                    <button
                      type="button"
                      @click="reset()"
                      class="btn btn-success margin-left-5"
                    >
                      Reset
                    </button>
                  </div>
                  <div class="card-body">
                    <div class="mb-3"> 
                      <span class="text-red" v-if="allUserModel.length > 0">The maximum purchase power for the new rule is ${{new_rule_max_pp}}, while for the other rules, it is ${{other_rule_max_pp}}.</span>
                    </div>
                    <b-table-simple
                      responsive
                      show-empty
                      bordered
                      sticky-header="800px"
                      v-if="allUserModel.length > 0"
                    >
                      <b-thead head-variant="light">
                        <b-tr>
                          <b-th class="text-left">Name</b-th>
                          <b-th class="text-left">Email</b-th>
                          <b-th class="text-left">Phone</b-th>
                          <b-th class="text-center">Consumer Type</b-th>
                          <b-th class="text-center">Date Of Birth</b-th>
                          <b-th class="text-center">Account No</b-th>
                          <b-th class="text-center">Risk Score</b-th>
                          <b-th class="text-center">Percentage</b-th>
                          <b-th class="text-center">Balance ($)</b-th>
                          <b-th class="text-center">Purchase Power ($)</b-th>
                          <b-th class="text-center">Purchase Power Rule</b-th>
                          <b-th class="text-center">Received At</b-th>
                          <b-th class="text-center">View</b-th>
                        </b-tr>
                      </b-thead>
                      <b-tbody v-for="(row, index) in allUserModel" :key="index">
                        <b-tr>
                          <b-td class="text-left text-gray">{{
                            row.name
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.email
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.phone
                          }}</b-td>
                          <b-td class="text-center text-gray">
                            <p>{{capitalizeFirstLetter(row.consumer_type)}}</p>
                          </b-td>
                          <b-td class="text-center text-gray">{{
                            row.date_of_birth
                          }}</b-td>
                          <b-td class="text-center text-gray">{{
                            row.masked_account_no
                          }}</b-td>
                          <b-td class="text-center text-gray">{{
                            row.risk_score
                          }}</b-td>
                          <b-td class="text-center text-gray">{{
                            row.percentage
                          }}</b-td>
                          <b-td class="text-center text-gray">{{
                            row.balance
                          }}</b-td>
                          <b-td class="text-center text-gray">{{
                            row.purchase_power
                          }}</b-td>
                          <b-td class="text-center text-gray">
                            <p v-if="row.consumer_type == constants.lite_consumer">N/A</p>
                            <p v-else>{{formattedPPSource(row.pp_source)}}</p></b-td>
                          <b-td class="text-center text-gray">{{
                            row.received_at
                          }}</b-td>
                          <b-td class="text-center text-gray">
                            <a :data-user-id="row.user_id" class="custom-edit-btn" title="View All Algo History" variant="outline-success" style="border:none" @click="viewAllAlgo(row)" v-if="row.consumer_type != constants.lite_consumer"><i class="nav-icon fas fa-eye"></i></a>
                          </b-td>
                        </b-tr>
                      </b-tbody>
                    </b-table-simple>
                    <div v-if="allUserModel.length > 0" style="float:right">
                        <b-pagination
                            v-model="currentPage"
                            :total-rows="totalItems"
                            :per-page="perPage"
                            prev-text="Prev"
                            next-text="Next"
                            :ellipsis="true"
                            :limit="5"
                        ></b-pagination>
                    </div>
                    <p v-else>No data displayed. Please refine your search criteria.</p>
                  </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>

    <b-modal
      id="user-algo-history-modal"
      ref="user-algo-history-modal"
      :header-text-variant="headerTextVariant"
      title="Consumer's Active Bank Algo Response History"
      hide-footer
    >
      <div class="row odd-row">
        <div class="col-md-4 row-value">
          <label for="name">Consumer Name</label>
        </div>
        <div class="col-md-1 row-value"><label class="consumer-details-label">:</label></div>
        <div class="col-md-7 row-value">
          <label class="consumer-details-label">{{ user.name }}</label>
        </div>
      </div>

      <div class="row even-row">
        <div class="col-md-4 row-value">
          <label for="email">Consumer Email</label>
        </div>
        <div class="col-md-1 row-value"><label class="consumer-details-label">:</label></div>
        <div class="col-md-7 row-value">
          <label class="consumer-details-label">{{ user.email }}</label>
        </div>
      </div>

      <div class="row odd-row">
        <div class="col-md-4 row-value">
          <label for="phone">Consumer Phone</label>
        </div>
        <div class="col-md-1 row-value"><label class="consumer-details-label">:</label></div>
        <div class="col-md-7 row-value">
          <label class="consumer-details-label">{{ user.phone }}</label>
        </div>
      </div>

      <div class="row even-row">
        <div class="col-md-4 row-value">
          <label for="account">Account No</label>
        </div>
        <div class="col-md-1 row-value"><label class="consumer-details-label">:</label></div>
        <div class="col-md-7 row-value">
          <label class="consumer-details-label">{{ user.masked_account_no }}</label>
        </div>
      </div>

      <div class="row odd-row">
        <div class="col-md-4 row-value">
          <label for="risk">Risk Score</label>
        </div>
        <div class="col-md-1 row-value"><label class="consumer-details-label">:</label></div>
        <div class="col-md-7 row-value">
          <label class="consumer-details-label">{{ user.risk_score }}</label>
        </div>
      </div>
      <div class="card-body">
        <label>Active Bank Algo Response History</label>
        <b-table-simple
          responsive
          show-empty
          bordered
          sticky-header="800px"
          v-if="userAlgoHistory.length > 0"
        >
          <b-thead head-variant="light">
            <b-tr>
              <b-th class="text-center">Balance ($)</b-th>
              <b-th class="text-center">Execution Time (mm:ss.ms)</b-th>
              <b-th class="text-center">Requested At</b-th>
              <b-th class="text-center">Percentage</b-th>
              <b-th class="text-center">Purchase Power ($)</b-th>
              <b-th class="text-center">Exception Reason</b-th>
              <b-th class="text-center">Received At</b-th>
            </b-tr>
          </b-thead>
          <b-tbody v-for="(row, index) in userAlgoHistory" :key="index">
            <b-tr>
              <b-td class="text-center text-gray">{{
                row.balance
              }}</b-td>
              <b-td class="text-center text-gray">{{
                row.execution_time
              }}</b-td>
              <b-td class="text-center text-gray">{{
                row.called_at
              }}</b-td>
              <b-td class="text-center text-gray">{{
                row.percentage
              }}</b-td>
              <b-td class="text-center text-gray">{{
                row.calculated_purchase_power
              }}</b-td>
              <b-td class="text-center text-gray">{{
                row.exception_message
              }}</b-td>
              <b-td class="text-center text-gray">{{
                row.received_at
              }}</b-td>
            </b-tr>
          </b-tbody>
        </b-table-simple>
        <div v-if="userAlgoHistory.length > 0" style="float:right">
            <b-pagination
                v-model="currentPage2"
                :total-rows="totalItems2"
                :per-page="perPage2"
                prev-text="Prev"
                next-text="Next"
                :ellipsis="true"
                :limit="5"
            ></b-pagination>
        </div>
        <p v-else>No data displayed. Please refine your search criteria.</p>
      </div>
    </b-modal>
  </div>
</div>
</template>
<script>
import moment from "moment";
import api from "@/api/user.js";
import { validationMixin } from "vuelidate";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "../CustomLoader/CanPayLoader.vue"
import commonConstants from "@/common/constant.js";

export default {
  mixins: [validationMixin],
  data() {
    return {
      allUserModel: {},
      userAlgoHistory: {},
      consumer:"",
      phone_no:"",
      email:"",
      loading:false,
      currentPage: 1,
      perPage: 10,
      totalPage: 0,
      totalItems: 0,
      currentPage2: 1,
      perPage2: 10,
      totalPage2: 0,
      totalItems2: 0,
      user: '',
      new_rule_max_pp: process.env.MIX_PP_LOWER_LIMIT_MAX_VALUE,
      other_rule_max_pp: process.env.MIX_MAX_AMOUNT_LIMIT_FOR_SINGLE_TRANSACTION,
      constants: commonConstants,
    };
  },
  components: {
    HourGlass,
    CanPayLoader
  },
  created() {
  },
  watch: {
    currentPage(newVal){
        this.searchConsumerAlgoHistory();
    },
    currentPage2(newVal){
      this.viewAllAlgo(this.user);
    },
  },
  methods: {
    capitalizeFirstLetter(str) {
      return str.charAt(0).toUpperCase() + str.slice(1);
    },
    reset(){
      var self = this;
      self.consumer = "";
      self.phone_no = "";
      self.email = "";
      $("#start-date").val('');
    },
    // Define a computed property to format the value
    formattedPPSource(source) {
      // Split the string by underscores, capitalize each word, and join them with spaces
      return source.split('_').map(word => {
        return word.charAt(0).toUpperCase() + word.slice(1);
      }).join(' ').trim();
    },
    viewAllAlgo(user){
      var self = this;
      self.user = user;
      var request = {
        user_id: user.user_id,
        account_id: user.active_account_id,
        page: self.currentPage2,
        per_page: self.perPage2,
      };
      self.loading = true;
      api
      .consumerAccountAlgoHistory(request)
      .then(function (response) {
        if (response.code == 200) {
          self.userAlgoHistory = response.data.data;
          self.totalPage2 = response.data.total_pages;
          self.totalItems2 = response.data.total;
          self.currentPage2 = response.data.current_page;
          self.$bvModal.show("user-algo-history-modal");
          self.loading = false;
        } else {
          error(response.message);
          self.loading = false;
        }
      })
      .catch(function (error) {
        // error(error);
        self.loading = false;
      });
    },
    searchConsumerAlgoHistory(){
      var self = this;
      if (
        moment($("#start-date").val()).format("YYYY-MM-DD") >
          moment().format("YYYY-MM-DD") &&
        $("#start-date").val() != ""
      ) {
        error("Start date cannot be from future.");
        return false;
      }
      if ($("#start-date").val() != "") {
        var from_date = moment($("#start-date").val()).format("YYYY-MM-DD");
      } else {
        var from_date = '';
      }
      if((self.consumer).trim().length < 3 && $("#phone_no").val().trim() === '' &&  $("#email").val().trim() === '' && from_date === ''){
        error("Please provide Consumer name (Min 3 chars) or email(exact) or phone no(exact) or registration date");
        return false;
      }
      var request = {
        from_date: from_date,
        to_date: from_date,
        consumer: self.consumer,
        email:self.email,
        phone_no:self.phone_no, 
        page: self.currentPage,
        per_page: self.perPage,
      };
      self.loading = true;
      api
      .searchConsumerAlgoHistory(request)
      .then(function (response) {
        if (response.code == 200) {
          self.allUserModel = response.data.data;
          self.totalPage = response.data.total_pages;
          self.totalItems = response.data.total;
          self.currentPage = response.data.current_page;
          self.loading = false;
        } else {
          error(response.message);
          self.loading = false;
        }
      })
      .catch(function (error) {
        // error(error);
        self.loading = false;
      });
    },
  },
  mounted() {
    var self = this;
    $("#start-date").datepicker({
      format: "mm/dd/yyyy",
      autoclose: true,
      todayHighlight: true,
    });
  },
};
</script>
<style>
.para-settings{
  margin-bottom: 0px !important;
  padding: 7px;
}
</style>
