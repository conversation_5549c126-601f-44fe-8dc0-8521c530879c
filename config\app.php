<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Application Name
    |--------------------------------------------------------------------------
    |
    | This value is the name of your application. This value is used when the
    | framework needs to place the application's name in a notification or
    | any other location as required by the application or its packages.
    |
     */

    'name' => env('APP_NAME', 'Laravel'),

    /*
    |--------------------------------------------------------------------------
    | Application Environment
    |--------------------------------------------------------------------------
    |
    | This value determines the "environment" your application is currently
    | running in. This may determine how you prefer to configure various
    | services the application utilizes. Set this in your ".env" file.
    |
     */

    'env' => env('APP_ENV', 'production'),

    /*
    |--------------------------------------------------------------------------
    | Application Debug Mode
    |--------------------------------------------------------------------------
    |
    | When your application is in debug mode, detailed error messages with
    | stack traces will be shown on every error that occurs within your
    | application. If disabled, a simple generic error page is shown.
    |
     */

    'debug' => env('APP_DEBUG', false),

    /*
    |--------------------------------------------------------------------------
    | Application URL
    |--------------------------------------------------------------------------
    |
    | This URL is used by the console to properly generate URLs when using
    | the Artisan command line tool. You should set this to the root of
    | your application so that it is used when running Artisan tasks.
    |
     */

    'url' => env('APP_URL', 'http://localhost'),

    'asset_url' => env('ASSET_URL', null),

    'email_send' => env('SEND_MAIL'),

    'firebase_login_info' => env('FIREBASE_LOGIN_INFORMATION'),

    'api_environment' => env('API_ENVIORNMENT'),

    'max_login_count' => env('MAX_CONSECUTIVE_LOGIN_COUNT'),
    'login_otp_validity' => env('LOGIN_OTP_VALIDITY'),
    'from_mail_name' => env('MAIL_FROM_NAME'),
    'from_mail_info' => env('MAIL_FROM_ADDRESS'),
    'google_places_api_key' => env('GOOGLE_PLACES_API_KEY'),
    'ec2_instance_name' => env('EC2_INSTANEC_NAME'),
    'bll_api_base_url' => env('BLL_API_BASE_URL'),
    'bll_api_base_key' => env('BLL_API_BASE_KEY'),
    'weekly_spending_limit_v1_consumer' => env('WEEKLY_SPENDING_LIMIT_FOR_V1_CONSUMER'),
    'canpay_return_recovery' => env('CANPAY_RETURN_RECOVERY'),
    'canpay_return_account_number' => env('CANPAY_RETURN_ACCOUNT_NUMBER'),
    'canpay_return_account_holder_name' => env('CANPAY_RETURN_ACCOUNT_HOLDER_NAME'),
    'performance_testing_mode' => env('PERFORMANCE_TESTING_MODE'),
    'daily_transaction_email' => env('DAILY_TRANSACTION_DETAILS_EMAIL'),
    'acheck_posting' => env('ACHECK_POSTING'),
    'email_for_fifth_third_user' => env('EMAILS_FOR_FIFTH_THIRD_USER_DETECTED'),
    'admin_driven_transaction_expiry_days' => env('ADMIN_DRIVEN_TRANSACTION_EXPIRY_DAYS'),
    'timezone_pst' => env('TIMEONE_PST'),
    'exchange_rate' => env('EXCHANGE_RATE'),
    'main_db' => env('DB_DATABASE'),
    'reward_wheel_db' => env('DB_DATABASE_REWARD_WHEEL'),
    'akoya_product_url' => env('AKOYA_PRODUCT_URL'),
    'akoya_idp_url' => env('AKOYA_IDP_URL'),
    'akoya_client_id' => env('AKOYA_CLIENT_ID'),
    'akoya_client_secret' => env('AKOYA_CLIENT_SECRET'),
    'akoya_redirect_url' => env('AKOYA_REDIRECT_URL'),
    'akoya_state' => env('AKOYA_STATE'),
    'akoya_version' => env('AKOYA_VERSION'),
    'mx_client_id' => env('MX_CLIENT_ID'),
    'mx_api_key' => env('MX_API_KEY'),
    'mx_base_url' => env('MX_BASE_URL'),
    'testing_mode' => env('TESTING_MODE'),
    'max_transaction_limit_weekly_for_bank_linked' => env('MAX_TRANSACTION_LIMIT_WEEKLY_FOR_BANK_LINKED'),
    'max_transaction_limit_weekly_for_non_bank_linked' => env('MAX_TRANSACTION_LIMIT_WEEKLY_FOR_NON_BANK_LINKED'),
    'max_transaction_limit_for_single_transaction' => env('MAX_AMOUNT_LIMIT_FOR_SINGLE_TRANSACTION'),
    'pp_lower_limit_max_value' => env('PP_LOWER_LIMIT_MAX_VALUE'),
    'v1_transaction_cutoff_date' => env('MIX_V1_TRANSACTION_CUTOFF_DATE'),
    'custom_purchase_power_update_time' => env('CUSTOM_PURCHASE_POWER_UPDATE_TIME'),

    'firebase_project_id' => env('FIREBASE_PROJECT_ID'),
    'firebase_api_key' => env('FIREBASE_API_KEY'),

    'aws_region' => env('AWS_DEFAULT_REGION'),
    'aws_access_id' => env('AWS_ACCESS_KEY_ID'),
    'aws_secret_key' => env('AWS_SECRET_ACCESS_KEY'),
    'aws_secret_id' => env('AWS_SECRET_ID'),

    'test_secret_manager' => env('TEST_SECRET_MANAGER_KEY'),
    /*
    |--------------------------------------------------------------------------
    | Application Timezone
    |--------------------------------------------------------------------------
    |
    | Here you may specify the default timezone for your application, which
    | will be used by the PHP date and date-time functions. We have gone
    | ahead and set this to a sensible default for you out of the box.
    |
     */

    'timezone' => 'UTC',

    /*
    |--------------------------------------------------------------------------
    | Application Locale Configuration
    |--------------------------------------------------------------------------
    |
    | The application locale determines the default locale that will be used
    | by the translation service provider. You are free to set this value
    | to any of the locales which will be supported by the application.
    |
     */

    'locale' => 'en',

    /*
    |--------------------------------------------------------------------------
    | Application Fallback Locale
    |--------------------------------------------------------------------------
    |
    | The fallback locale determines the locale to use when the current one
    | is not available. You may change the value to correspond to any of
    | the language folders that are provided through your application.
    |
     */

    'fallback_locale' => 'en',

    /*
    |--------------------------------------------------------------------------
    | Faker Locale
    |--------------------------------------------------------------------------
    |
    | This locale will be used by the Faker PHP library when generating fake
    | data for your database seeds. For example, this will be used to get
    | localized telephone numbers, street address information and more.
    |
     */

    'faker_locale' => 'en_US',

    /*
    |--------------------------------------------------------------------------
    | Encryption Key
    |--------------------------------------------------------------------------
    |
    | This key is used by the Illuminate encrypter service and should be set
    | to a random, 32 character string, otherwise these encrypted strings
    | will not be safe. Please do this before deploying an application!
    |
     */

    'key' => env('APP_KEY'),

    'cipher' => 'AES-256-CBC',

    'canpay_api_url' => env('MIX_API_URL'),
    'canpay_reward_wheel_url' => env('MIX_REWARD_WHEEL_APP_URL'),
    'consumer_app_url' => env('CONSUMER_APP_URL'),

    'transaction_settlement_timezone' => env('TRASNACTION_SETTLEMENT_TIMEZONE'),
    'transaction_settlement_start' => env('TRASNACTION_SETTLEMENT_START_TIME'),

    'acheck_base_url' => env('ACHECK_BASE_URL'),
    'acheck_username' => env('ACHECK_USERNAME'),
    'acheck_password' => env('ACHECK_PASSWORD'),

    'send_mail' => env('SEND_MAIL'),

    'transaction_posting_block_end_time' => env('TRANSACTION_POSTING_BLOCK_END_TIME'),

    'canpay_settlement_id' => env('CANPAY_SETTLEMENT_ID'),
    'canpay_fees_id' => env('CANPAY_FEES_ID'),
    'canpay_deposit' => env('CANPAY_DEPOSIT'),

    'canpay_account_number' => env('CANPAY_ACCOUNT_NUMBER'),
    'canpay_account_type' => env('CANPAY_ACCOUNT_TYPE'),
    'canpay_routing_number' => env('CANPAY_ROUTING_NUMBER'),
    'canpay_account_holder_name' => env('CANPAY_ACCOUNT_HOLDER_NAME'),
    'finicity_base_url' => env('FINICITY_BASE_URL'),
    'finicity_app_key' => env('FINICITY_APP_KEY'),
    'finicity_partner_secret' => env('FINICITY_PARTNER_SECRET'),
    'finicity_partner_id' => env('FINICITY_PARTNER_ID'),

    'send_email_after_1st_attempt' => env('SEND_EMAIL_AFTER_1st_ATTEMPT'),
    'send_email_after_2nd_attempt' => env('SEND_EMAIL_AFTER_2nd_ATTEMPT'),
    'send_email_after_3rd_attempt' => env('SEND_EMAIL_AFTER_3rd_ATTEMPT'),
    'send_email_count' => env('SEND_EMAIL_COUNT'),
    'send_email_block_duration' => env('SEND_EMAIL_BLOCK_DURATION'),

    'twilio_sid' => env('TWILIO_SID'),
    'twilio_token' => env('TWILIO_TOKEN'),
    'twilio_phone_no' => env('TWILIO_PHONE_NO'),
    'secondary_twilio_sid' => env('SECONDARY_TWILIO_SID'),
    'secondary_twilio_token' => env('SECONDARY_TWILIO_TOKEN'),
    'secondary_twilio_phone_no' => env('SECONDARY_TWILIO_PHONE_NO'),
    'country_code' => env('COUNTRY_CODE'),

    'resend_notification_after_first_attempt' => env('RESEND_NOTIFICATION_COOLDOWN_AFTER_FIRST_ATTEMPT'),
    'resend_notification_after_second_attempt' => env('RESEND_NOTIFICATION_COOLDOWN_AFTER_SECOND_ATTEMPT'),
    'resend_notification_from_third_attempt' => env('RESEND_NOTIFICATION_COOLDOWN_FROM_THIRD_ATTEMPT'),

    'cognito_base_url' => env('COGNITO_BASE_URL'),
    's3_file_expiry_time' => env('S3_FILE_EXPIRY_TIME'),
    'purchase_power_for_manual_bank_linked_consumer' => env('PURCHASE_POWER_FOR_MANUAL_BANK_LINKED_CONSUMER'),
    'active_allow_transaction_time_validity' => env('ACTIVE_ALLOW_TRANSACTION_TIME_VALIDITY'),
    'temp_password_validity' => env('TEMP_PASSWORD_VALIDITY'),
    'cognito_api_secret' => env('COGNITO_API_SECRET'),
    'cognito_api_key' => env('COGNITO_API_KEY'),
    'mx_failure_date_difference' => env('MX_FAILURE_DATE_DIFFERENCE'),
    'search_bank_limit' => env('SEARCH_BANK_LIMIT'),
    'mix_api_return_transaction_max_difference' => env('MIX_API_RETURN_TRASNACTION_MAX_DIFFERENCE'),
    'session_validity' => env('SESSION_VALIDITY'),
    /*
    |--------------------------------------------------------------------------
    | Autoloaded Service Providers
    |--------------------------------------------------------------------------
    |
    | The service providers listed here will be automatically loaded on the
    | request to your application. Feel free to add your own services to
    | this array to grant expanded functionality to your applications.
    |
     */

    'providers' => [

        /*
         * Laravel Framework Service Providers...
         */
        Illuminate\Auth\AuthServiceProvider::class,
        Illuminate\Broadcasting\BroadcastServiceProvider::class,
        Illuminate\Bus\BusServiceProvider::class,
        Illuminate\Cache\CacheServiceProvider::class,
        Illuminate\Foundation\Providers\ConsoleSupportServiceProvider::class,
        Illuminate\Cookie\CookieServiceProvider::class,
        Illuminate\Database\DatabaseServiceProvider::class,
        Illuminate\Encryption\EncryptionServiceProvider::class,
        Illuminate\Filesystem\FilesystemServiceProvider::class,
        Illuminate\Foundation\Providers\FoundationServiceProvider::class,
        Illuminate\Hashing\HashServiceProvider::class,
        Illuminate\Mail\MailServiceProvider::class,
        Illuminate\Notifications\NotificationServiceProvider::class,
        Illuminate\Pagination\PaginationServiceProvider::class,
        Illuminate\Pipeline\PipelineServiceProvider::class,
        Illuminate\Queue\QueueServiceProvider::class,
        Illuminate\Redis\RedisServiceProvider::class,
        Illuminate\Auth\Passwords\PasswordResetServiceProvider::class,
        Illuminate\Session\SessionServiceProvider::class,
        Illuminate\Translation\TranslationServiceProvider::class,
        Illuminate\Validation\ValidationServiceProvider::class,
        Illuminate\View\ViewServiceProvider::class,
        Tymon\JWTAuth\Providers\LaravelServiceProvider::class,
        Maatwebsite\Excel\ExcelServiceProvider::class,
        /*
         * Package Service Providers...
         */

        /*
         * Application Service Providers...
         */
        App\Providers\AppServiceProvider::class,
        App\Providers\AuthServiceProvider::class,
        // App\Providers\BroadcastServiceProvider::class,
        App\Providers\EventServiceProvider::class,
        // OwenIt\Auditing\AuditingServiceProvider::class,
        App\Providers\RouteServiceProvider::class,

    ],

    /*
    |--------------------------------------------------------------------------
    | Class Aliases
    |--------------------------------------------------------------------------
    |
    | This array of class aliases will be registered when this application
    | is started. However, feel free to register as many as you wish as
    | the aliases are "lazy" loaded so they don't hinder performance.
    |
     */

    'aliases' => [

        'App' => Illuminate\Support\Facades\App::class,
        'Arr' => Illuminate\Support\Arr::class,
        'Artisan' => Illuminate\Support\Facades\Artisan::class,
        'Auth' => Illuminate\Support\Facades\Auth::class,
        'Blade' => Illuminate\Support\Facades\Blade::class,
        'Broadcast' => Illuminate\Support\Facades\Broadcast::class,
        'Bus' => Illuminate\Support\Facades\Bus::class,
        'Cache' => Illuminate\Support\Facades\Cache::class,
        'Config' => Illuminate\Support\Facades\Config::class,
        'Cookie' => Illuminate\Support\Facades\Cookie::class,
        'Crypt' => Illuminate\Support\Facades\Crypt::class,
        'DB' => Illuminate\Support\Facades\DB::class,
        'Eloquent' => Illuminate\Database\Eloquent\Model::class,
        'Event' => Illuminate\Support\Facades\Event::class,
        'File' => Illuminate\Support\Facades\File::class,
        'Gate' => Illuminate\Support\Facades\Gate::class,
        'Hash' => Illuminate\Support\Facades\Hash::class,
        'Lang' => Illuminate\Support\Facades\Lang::class,
        'Log' => Illuminate\Support\Facades\Log::class,
        'Mail' => Illuminate\Support\Facades\Mail::class,
        'Notification' => Illuminate\Support\Facades\Notification::class,
        'Password' => Illuminate\Support\Facades\Password::class,
        'Queue' => Illuminate\Support\Facades\Queue::class,
        'Redirect' => Illuminate\Support\Facades\Redirect::class,
        'Redis' => Illuminate\Support\Facades\Redis::class,
        'Request' => Illuminate\Support\Facades\Request::class,
        'Response' => Illuminate\Support\Facades\Response::class,
        'Route' => Illuminate\Support\Facades\Route::class,
        'Schema' => Illuminate\Support\Facades\Schema::class,
        'Session' => Illuminate\Support\Facades\Session::class,
        'Storage' => Illuminate\Support\Facades\Storage::class,
        'Str' => Illuminate\Support\Str::class,
        'URL' => Illuminate\Support\Facades\URL::class,
        'Validator' => Illuminate\Support\Facades\Validator::class,
        'View' => Illuminate\Support\Facades\View::class,
        'JWTAuth' => Tymon\JWTAuth\Facades\JWTAuth::class,
        'JWTFactory' => Tymon\JWTAuth\Facades\JWTFactory::class,
        'Excel' => Maatwebsite\Excel\Facades\Excel::class,
    ],

];
