<?php

namespace App\Http\Factories\Mx;

use App\Http\Clients\MxHttpClient;
use App\Http\Factories\PurchasePower\PurchasePowerFactory;
use App\Models\FinancialInstitutionMaster;
use App\Models\User;
use App\Models\UserBankAccountInfo;
use Illuminate\Support\Facades\Log;

/**
 *
 * @package App\Http\Factories\Akoya
 */
class MxFactory implements MxInterface
{
    private $akoyaClient = null;

    public function __construct()
    {
        $this->mxClient = new MxHttpClient();
        $this->purchasePower = new PurchasePowerFactory();
    }

    /**
     * getConsumerAccountBalance
     * This function will return a consumer account details based on the account id
     * @param  mixed $account_id
     * @return void
     */
    public function getConsumerAccountBalance($account_id, $consumer_id, $for_scheduler = false)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Process started for fetching a specific account details for a consumer in MX...");
        /// checking if testing mode is set then return the custom bank balance
        if (config('app.testing_mode')) {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "=====================TESTING MODE ENABLED=======================");
            $balance = config('app.custom_bank_balance');
            $originalResponse = "Testing Mode. Not calling MX API.";
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Returning custom balance :" . $balance);
            return $for_scheduler != '' ? ["availableBalanceAmount" => $balance, "balance" => $balance, "effectiveBalance" => $balance, "banking_solution_response" => $originalResponse] : $balance;
        }
        try {
            $account_info = UserBankAccountInfo::where('account_id', $account_id)->first();
            $params['user_guid'] = $account_info->mx_consumer_id;
            $params['account_guid'] = $account_id;
            $params['consumer_id'] = $consumer_id;
            $getAccountDetails = $this->mxClient->getAccountDetails($params);
            $account_data = json_decode($getAccountDetails, true);
            if (isset($account_data['account'])) {
                $account_details = $account_data['account'];
                if (isset($account_details['available_balance'])) {
                    if ($account_details['available_balance'] > $account_details['balance']) {
                        $balance = $account_details['available_balance'];
                    } else {
                        $balance = $account_details['balance'];
                    }
                } else {
                    $balance = $account_details['balance'];
                }
                return $for_scheduler != '' ? ["availableBalanceAmount" => $account_details['available_balance'], "balance" => $account_details['balance'], "effectiveBalance" => $balance, "banking_solution_response" => $getAccountDetails] : $balance;
            } else {
                Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No account information foumd for user:  " . $consumer_id . ".");
                return $for_scheduler != '' ? ["availableBalanceAmount" => 0, "balance" => 0, "error" => 1, "effectiveBalance" => 0, "banking_solution_response" => "_ERR_No Account Details Found."] : false;
            }
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured while trying to fetch bank account information for user:  " . $consumer_id . ".", [EXCEPTION => $e]);
            return $for_scheduler != '' ? ["availableBalanceAmount" => 0, "balance" => 0, "error" => 1, "effectiveBalance" => 0, "banking_solution_response" => "_ERR_" . $e->getMessage()] : false;
        }
    }

    /**
     * This api refreshes balance and purchase power for a consumer
     */
    public function getConsumerRefreshBalance($active_bank, $user_id, $source)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Fetching Balance for MX... ");
        $balanceNPurchasePower = $this->getConsumerAccountBalance($active_bank->account_id, $user_id, 1);
        if (!isset($balanceNPurchasePower['error'])) {
            $user = User::where('user_id', $user_id)->first(); // Fetching User Details
            $purchase_power = $this->purchasePower->calculatePurchasePower($balanceNPurchasePower["effectiveBalance"], $user);
            $data['user_id'] = $user_id;
            $data['account_id'] = $active_bank->id;
            $data['balance'] = $balanceNPurchasePower["effectiveBalance"];
            $data['response_raw_balance'] = $balanceNPurchasePower['balance'];
            $data['response_available_balance'] = $balanceNPurchasePower['availableBalanceAmount'];
            $data['purchase_power'] = $purchase_power;
            $data['source'] = $source;
            $data['purchase_power_source'] = $user->purchase_power_source;
            $one_time_refresh = $source == ONE_TIME_BALANCE_FETCH ? 1 : 0;
            $data['one_time_refresh'] = $one_time_refresh;
            $data['banking_solution_response'] = $balanceNPurchasePower['banking_solution_response'];
            addCustomerAccounts($data);

            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Calculated purchase power for User ID: " . $user_id . " is : $" . $purchase_power);
            User::where(['user_id' => $user_id, 'disable_automatic_purchase_power' => 0])->update(array('purchase_power' => $purchase_power, 'refresh_balance_called' => 1));
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Consumer purchase power updated into database successfully.");
            $response = [
                'code' => SUCCESS,
                'message' => trans('message.balance_refresh'),
                'data' => null,
            ];
        } else {
            $response = [
                'code' => SUCCESS,
                'message' => null,
                'data' => 0,
            ];
        }
        return $response;
    }

    /**
     * getAccountOwnerInfo
     * This function will fetch the account owner information from akoya
     * @param  mixed $params
     * @return void
     */
    public function getAccountOwnerInfo($params)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Fetch process started for account owner information from MX... ");
        $params['user_guid'] = $params->mx_consumer_id;
        $params['member_guid'] = $params->mx_member_guid;
        $params['consumer_id'] = $params->user_id;

        $response = json_decode($this->mxClient->getAccountOwners($params), true);
        Log::info($response);
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Account owner information fetched successfully MX.");
        return $response;
    }


    /**
     * identifyMember
     * This function will call the Identify Member API which is required to call once before Account Owner API call
     * @param  mixed $params
     * @return void
     */
    public function identifyMember($params)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Identify Member API call started... ");
        $params['user_guid'] = $params->mx_consumer_id;
        $params['member_guid'] = $params->mx_member_guid;
        $response = json_decode($this->mxClient->identifyMember($params), true);
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Identify Member call completed successfully.");
        return $response;
    }

    /**
     * This function will fetch the Member Guid for a MX Consumer
     *
     * @return mixed
     */
    public function getMemberGuid($data)
    {
        try {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Process started for fetching accounts for a consumer in MX...");
            $data['page'] = 1;
            $accounts = [];
            while (true) {
                $apiResponse = json_decode($this->mxClient->getUserAccounts($data), true);
                $accounts = array_merge($accounts, $apiResponse['accounts']);
                if ($apiResponse['pagination']['current_page'] < $apiResponse['pagination']['total_pages']) {
                    $data['page']++;
                } else {
                    break; // All entries are loaded, exit the loop
                }
            }
            foreach ($accounts as $account) {
                return $account['member_guid'];
            }
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while fetching accounts for a consumer list in MX.", [EXCEPTION => $e]);
            return ['code' => FAIL, 'message' => trans('message.fetch_user_account_exception'), 'data' => null];
        }
    }

    /**
     * getMemberData
     * This function will call the member data API
     * @param  mixed $params
     * @return void
     */
    public function getMemberData($params)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Get Member Data API call started... ");
        $params['user_guid'] = $params->mx_consumer_id;
        $params['member_guid'] = $params->mx_member_guid;
        $response = json_decode($this->mxClient->getMemberData($params), true);
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Member data API call completed successfully.");
        return $response;
    }
    
    /**
     * This function will fetch the Member Guid for a MX Consumer
     *
     * @return mixed
     */
    public function getMembers($data)
    {
        try {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Process started for fetching members for Consumer ID: ".$data['consumer_id']." in MX...");
            $data['page'] = 1;
            $merbers = [];
            while (true) {
                $apiResponse = json_decode($this->mxClient->getMembers($data), true);
                $merbers = array_merge($merbers, $apiResponse['members']);
                if ($apiResponse['pagination']['current_page'] < $apiResponse['pagination']['total_pages']) {
                    $data['page']++;
                } else {
                    break; // All entries are loaded, exit the loop
                }
            }
            if(count($merbers) > 0){
                // Filter the array to keep only the arrays with "connection_status" set to "CONNECTED"
                $connectedArrays = array_filter($merbers, function($item) {
                    return $item['connection_status'] === 'CONNECTED' && $item['is_being_aggregated'] == false;
                });
                // Get the last array from the filtered result
                $lastConnectedArray = end($connectedArrays);
                if (empty($lastConnectedArray)) {
                    return ['code' => FAIL, 'message' => trans('message.connected_merber_not_found'), 'data' => null];  
                }
                $lastMerber = end($merbers);
                if ($lastMerber['connection_status'] != MX_CONNECTED && $data['skip_not_connected'] == false) {
                    return ['code' => 597, 'message' => trans('message.mx_last_merber_not_connected'), 'data' => null];
                } else {
                    $bank = FinancialInstitutionMaster::where('mx_institution_code', $lastConnectedArray['institution_code'])->first();
                    if ($bank) {
                        $returnData = array(
                            'banking_solution' => 'mx',
                            'bank_name' => $bank->bank_name,
                            'bank_id' => $bank->id,
                            'batch_id' => TID,
                            'member_guid' => $lastConnectedArray['guid'],
                            'consumer_id' => $data['consumer_id'],
                            'user_guid' => $data['mx_consumer_id'],
                            'is_finicity_delink' => false
                        );
                        return ['code' => SUCCESS, 'message' => trans('message.connected_merber_guid_found'), 'data' => $returnData];  
                    } else {
                        return ['code' => FAIL, 'message' => trans('message.connected_bank_not_found'), 'data' => null];
                    }
                }
            } else {
                return ['code' => FAIL, 'message' => trans('message.connected_merber_not_found'), 'data' => null];
            }
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while fetching members for a consumer list in MX for Consumer ID: ".$data['consumer_id'], [EXCEPTION => $e]);
            return ['code' => FAIL, 'message' => trans('message.fetch_user_members_exception'), 'data' => null];
        }
    }
}
