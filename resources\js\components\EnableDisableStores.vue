<template>
<div>
  <div v-if="loading">
  <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                <div class="card card-success">
                    <div class="card-header">
                    <h3 class="card-title">Enable/Disable Stores</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                <input
                                    class="form-control"
                                    placeholder="Merchant ID (Exact)"
                                    id="merchant_id"
                                    v-model="merchant_id"
                                />
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                <input
                                    class="form-control"
                                    placeholder="Store ID (Exact)"
                                    id="store_id"
                                    v-model="store_id"
                                />
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                <input
                                    class="form-control"
                                    placeholder="Retailer Name"
                                    id="retailer"
                                    v-model="retailer"
                                />
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                <select name="store_type" id="store_type" class="form-control" v-model="storeType">
                                    <option value="Active">Enabled Stores</option>
                                    <option value="Inactive">Disabled Stores</option>
                                </select>
                            </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <button
                        type="button"
                        class="btn btn-success"
                        @click="searchStores()"
                        >
                        Search
                        </button>
                        <button
                        type="button"
                        @click="reset()"
                        class="btn btn-success margin-left-5"
                        >
                        Reset
                        </button>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body">
                        <b-table-simple
                        responsive
                        show-empty
                        bordered
                        sticky-header="800px"
                        v-if="allStoreModel.length > 0"
                        >
                        <b-thead head-variant="light">
                            <tr>
                                <th width="10%">Merchant ID</th>
                                <th width="10%">Store ID</th>
                                <th width="10%">Retailer</th>
                                <th width="20%">Address</th>
                                <th width="14%">Contact No.</th>
                                <th width="12%">Email</th>
                                <th width="14%">Created On</th>
                                <th width="10%">Action</th>
                            </tr>
                        </b-thead>
                        <b-tbody v-for="(row, index) in allStoreModel" :key="index">
                            <b-tr>
                            <b-td class="text-left text-gray">{{
                                row.merchantID
                            }}</b-td>
                            <b-td class="text-left text-gray">{{
                                row.store_id
                            }}</b-td>
                            <b-td class="text-left text-gray">{{
                                row.retailer
                            }}</b-td>
                            <b-td class="text-center text-gray">
                                {{row.address}}
                            </b-td>
                            <b-td class="text-center text-gray">{{
                                row.contact_no
                            }}</b-td>
                            <b-td class="text-center text-gray">{{
                                row.email
                            }}</b-td>
                            <b-td class="text-center text-gray">{{
                                row.created_at
                            }}</b-td>
                            <b-td class="text-center text-gray">
                                <a :data-store-id="row.id" class="disableStore custom-edit-btn" title="Disable Store" variant="outline-success" style="border:none" v-if="row.status == 'Active'"><i class="nav-icon fas fa-edit"></i></a>
                                <a :data-store-id="row.id" class="enableStore custom-edit-btn" title="Enable Store" variant="outline-success" style="border:none" v-else><i class="nav-icon fas fa-edit"></i></a>
                            </b-td>
                            </b-tr>
                        </b-tbody>
                        </b-table-simple>
                        <p v-else>No data displayed. Please refine your search criteria.</p>
                    </div>
                </div>
            </div>
          </div>
        </div>
      </section>
    </div>

    <!-- Disable Store Start -->
    <b-modal
      id="disable-store-modal"
      ref="modal"
      :header-text-variant="headerTextVariant"
      :title="modalTitleDisable"
      @show="resetModal"
      @hidden="resetModal"
      ok-title="Save"
      ok-variant="success"
      cancel-variant="outline-secondary"
      @ok="handleOkDisable"
      :no-close-on-esc="true"
      :no-close-on-backdrop="true"
    >
      <form ref="form" @submit.stop.prevent="saveDisable" class="needs-validation">
        <div class="row">
          <div class="col-md-12">
            <label for="user_type">
              Disable Reasons
              <span class="red">*</span>
            </label>
            <input list="disable_reason_list" name="disable_reasons" id="disable_reasons" v-validate="'required'" class="form-control" v-model="disableStoreModel.disable_reasons" autocomplete="off">
            <datalist
              id="disable_reason_list"
            >
              <option
                v-for="(reasons, index) in disableReasons"
                :key="index"
                :value="reasons.reason"
              >
              </option>
            </datalist>
            <span v-show="errors.has('disable_reasons')" class="text-danger">{{
              errors.first("disable_reasons")
            }}</span>
          </div>
        </div>
      </form>
    </b-modal>
    <!-- Disable Store End -->
    <!-- Enable Store Start -->
    <b-modal
      id="enable-store-modal"
      ref="modal"
      :header-text-variant="headerTextVariant"
      :title="modalTitleEnable"
      @show="resetModal"
      @hidden="resetModal"
      ok-title="Save"
      ok-variant="success"
      cancel-variant="outline-secondary"
      @ok="handleOkEnable"
      :no-close-on-esc="true"
      :no-close-on-backdrop="true"
    >
      <form ref="form" @submit.stop.prevent="saveEnable" class="needs-validation">
        <div class="row">
          <div class="col-md-12">
            <label for="user_type">
              Enable Reasons
              <span class="red">*</span>
            </label>
            <input list="enable_reason_list" name="enable_reasons" id="enable_reasons" v-validate="'required'" class="form-control" v-model="enableStoreModel.enable_reasons" autocomplete="off">
            <datalist
              id="enable_reason_list"
            >
              <option
                v-for="(reasons, index) in enableReasons"
                :key="index"
                :value="reasons.reason"
              >
              </option>
            </datalist>
            <span v-show="errors.has('enable_reasons')" class="text-danger">{{
              errors.first("enable_reasons")
            }}</span>
          </div>
        </div>
      </form>
    </b-modal>
    <!-- Enable Store End -->
  </div>
</div>
</template>
<script>
import api from "@/api/stores.js";
import commonConstants from "@/common/constant.js";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "./CustomLoader/CanPayLoader.vue"
export default {
  data() {
    return {
      allStoreModel: {},
      currentUser: localStorage.getItem("user")
        ? JSON.parse(localStorage.getItem("user"))
        : null,
      storeID: null,
      enableStoreModel: {},
      disableStoreModel: {},
      enableReasons: {},
      disableReasons: {},
      headerTextVariant: "light",
      modalTitleEnable: "",
      modalTitleDisable: "",
      showReloadBtn:false,
      loading:false,
      merchant_id:"",
      store_id:"",
      retailer:"",
      storeType : "Active",
    };
  },
  components: {
    HourGlass,
    CanPayLoader
  },
  created() {
    this.enableStore();
    this.disableStore();
    this.enableDisableReasons();
  },
  methods: {
    searchStores(){
      var self = this;
      if($("#retailer").val().trim() === '' && $("#merchant_id").val().trim() === '' &&  $("#store_id").val().trim() === ''){
        error("Please provide Retailer Name or Merchant ID(exact) or Store ID(exact)");
        return false;
      }
      var request = {
        retailer: self.retailer,
        merchant_id:self.merchant_id,
        store_id:self.store_id,
        storeType:self.storeType
      };
      self.loading = true;
      api
      .searchEnableDisableStores(request)
      .then(function (response) {
        if (response.code == 200) {
          self.allStoreModel = response.data;
          self.loading = false;
        } else {
          error(response.message);
          self.loading = false;
        }
      })
      .catch(function (error) {
        // error(error);
        self.loading = false;
      });
    },
    resetModal() {
      var self = this;
      self.enableStoreModel = {};
      self.disableStoreModel = {};
    },
    handleOkEnable(bvModalEvt) {
      var self = this;
      // Prevent modal from closing
      bvModalEvt.preventDefault();
      // Trigger submit handler
      self.saveEnable();
    },
    handleOkDisable(bvModalEvt) {
      var self = this;
      // Prevent modal from closing
      bvModalEvt.preventDefault();
      // Trigger submit handler
      self.saveDisable();
    },
    saveEnable() {
      var self = this;
      // Exit when the form isn't valid
      this.$validator.validateAll().then((result) => {
        if (result) {
          //call to api to save the details
          api
            .enableStore(self.enableStoreModel)
            .then((response) => {
              if (response.code == 200) {
                success(response.message);
                self.enableDisableReasons();
                self.$bvModal.hide("enable-store-modal");
                self.resetModal();
                self.searchStores();
              } else {
                error(response.message);
              }
            })
            .catch((err) => {
              error(err.response.data.message);
            });
        }
      });
    },
    saveDisable() {
      var self = this;
      // Exit when the form isn't valid
      this.$validator.validateAll().then((result) => {
        if (result) {
          //call to api to save the details
          api
            .disableStore(self.disableStoreModel)
            .then((response) => {
              if (response.code == 200) {
                success(response.message);
                self.enableDisableReasons();
                self.$bvModal.hide("disable-store-modal");
                self.resetModal();
                self.searchStores();
              } else {
                error(response.message);
              }
            })
            .catch((err) => {
              error(err.response.data.message);
            });
        }
      });
    },
    enableStore() {
      var self = this;
      $(document).on("click", ".enableStore", function (e) {
        self.storeID = $(e.currentTarget).attr("data-store-id");
        var storedetails = self.allStoreModel.find((p) => p.id == self.storeID);
        self.modalTitleEnable = "Enable Store";
        self.$bvModal.show("enable-store-modal");
        self.enableStoreModel = storedetails;
      });
    },
    disableStore() {
      var self = this;
      $(document).on("click", ".disableStore", function (e) {
        self.storeID = $(e.currentTarget).attr("data-store-id");
        var storedetails = self.allStoreModel.find((p) => p.id == self.storeID);
        self.modalTitleDisable = "Disable Store";
        self.$bvModal.show("disable-store-modal");
        self.disableStoreModel = storedetails;
      });
    },
    enableDisableReasons() {
      var self = this;
      api
        .getEnableDisableReasons()
        .then((response) => {
          if (response.code == 200) {
            self.enableReasons = response.data.enableReasons;
            self.disableReasons = response.data.disableReasons;
          } else {
            error(response.message);
          }
        })
        .catch((err) => {
          error(err.response.data.message);
        });
    },
    reset(){
      var self = this;
      self.retailer = "";
      self.merchant_id = "";
      self.store_id = "";
      self.storeType = "";
    }
  },
  mounted() {
    var self = this;
    document.title = "CanPay - Enable/Disable Stores";
  },
};
</script>

