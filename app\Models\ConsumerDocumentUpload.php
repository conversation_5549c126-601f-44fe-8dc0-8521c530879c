<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
class ConsumerDocumentUpload extends Model
{

    protected $table = 'consumer_document_upload';

    /**
     * __construct
     * This function will insert an alphanumeric 32 character unique id as primary key in the table whenever a data get inserted
     * @param  mixed $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        $this->id = generateUUID();

        parent::__construct($attributes);
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'status',
        'comment',
    ];
    public $timestamps = true;
    public $incrementing = false;
}
