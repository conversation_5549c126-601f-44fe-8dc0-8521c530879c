<template>
    <div>
        <button @click="openModal('wheel-template-modal-'+type), setTemplateValue()" type="button" class="wheel-template-btn">{{label}}</button>

        <b-modal
        :title="modalTitle"
        header-text-variant="light"
        :id="'wheel-template-modal-'+type"
        :ref="'wheel-template-modal-'+type"
        cancel-title="Close"
        hide-footer
        cancel-variant="outline-secondary"
        :no-close-on-esc="true"
        :no-close-on-backdrop="true"
        >
            <div v-for="(template, index) in templates" :key="index">
                <div class="card" v-if="template.type == 'page'">
                    <div class="card-body">
                        <h6>{{template.wheel_name ? template.wheel_name : 'Default Configuration' }}</h6>
                        <div class="row">
                            <div class="col-6">
                                <p class="mb-1"><strong>{{ template.reward_wheel_design.page.image!='' && template.reward_wheel_design.page.image!=null ? 'Background Image' : 'Backgorund Color' }}</strong></p>
                                <img v-if="template.reward_wheel_design.page.image!='' && template.reward_wheel_design.page.image!=null" :src="template.reward_wheel_design.page.image" style="width: 100px;" />
                                <div class="row mx-0" v-else>
                                    <div>
                                        <small>Color 1</small>
                                        <div :style="'width: 42px; height: 42px; border: 1px solid #e5e5e5; border-radius: 6px; background:'+ template.reward_wheel_design.page.color_1"></div>
                                    </div>
                                    <div class="mx-3">
                                        <small>Color 2</small>
                                        <div :style="'width: 42px; height: 42px; border: 1px solid #e5e5e5; border-radius: 6px; background:'+ template.reward_wheel_design.page.color_2"></div>
                                    </div>
                                    <div>
                                        <small>Color 3</small>
                                        <div :style="'width: 42px; height: 42px; border: 1px solid #e5e5e5; border-radius: 6px; background:'+ template.reward_wheel_design.page.color_3"></div>
                                    </div>
                                </div>
                            </div>
                             <div class="col-6">
                                <small>Sparkles</small>
                                <p>Show: {{ template.reward_wheel_design.sparkles.show == 1 ? 'True' : 'False' }}</p>
                            </div>
                        </div>
                        <button class="wheel-template-copy-btn" type="button" @click="copyTemplate(template.reward_wheel_design)">Copy</button>
                    </div>
                </div>
                <div class="card" v-if="template.type == 'header'">
                    <div class="card-body" v-if="template.reward_wheel_design.header.header_type == 'text'">
                        <h6>{{template.wheel_name ? template.wheel_name : 'Default Configuration' }}</h6>
                        <div class="row">
                            <div class="col-3">
                                <small>Color</small>
                                <div :style="'width: 42px; height: 42px; border: 1px solid #e5e5e5; border-radius: 6px; background:'+ template.reward_wheel_design.header.color"></div>
                            </div>
                            <div class="col-3">
                                <small>Title</small>
                                <p>{{ template.reward_wheel_design.header.page_title }}</p>
                            </div>
                            <div class="col-3">
                                <small>Title Text Size</small>
                                <p>{{ template.reward_wheel_design.header.page_title_size }}</p>
                            </div>
                            <div class="col-3">
                                <small>Transparency</small>
                                <p>{{ template.reward_wheel_design.header.header_transparent == 1 ? 'True' : 'False' }}</p>
                            </div>
                            <div class="col-3">
                                <small>Sub Title Color</small>
                                <div :style="'width: 42px; height: 42px; border: 1px solid #e5e5e5; border-radius: 6px; background:'+ template.reward_wheel_design.header.header_sub_title_color"></div>
                            </div>
                            <div class="col-3">
                                <small>Sub Title</small>
                                <p>{{ template.reward_wheel_design.header.header_sub_title }}</p>
                            </div>
                            <div class="col-3">
                                <small>Sub Title Text Size</small>
                                <p>{{ template.reward_wheel_design.header.header_sub_title_size}}</p>
                            </div>
                        </div>
                        <button class="wheel-template-copy-btn" type="button" @click="copyTemplate(template.reward_wheel_design)">Copy</button>
                    </div>
                    <div class="card-body" v-if="template.reward_wheel_design.header.header_type == 'image'">
                        <h6>{{template.wheel_name ? template.wheel_name : 'Default Configuration' }}</h6>
                        <div class="row">
                            <div class="col-3">
                                <small>Color</small>
                                <div :style="'width: 42px; height: 42px; border: 1px solid #e5e5e5; border-radius: 6px; background:'+ template.reward_wheel_design.header.color"></div>
                            </div>
                            <div class="col-3">
                                <small>Sub Title Color</small>
                                <div :style="'width: 42px; height: 42px; border: 1px solid #e5e5e5; border-radius: 6px; background:'+ template.reward_wheel_design.header.header_sub_title_color"></div>
                            </div>
                            <div class="col-3">
                                <small>Sub Title</small>
                                <p>{{ template.reward_wheel_design.header.header_sub_title }}</p>
                            </div>
                            <div class="col-3">
                                <small>Sub Title Text Size</small>
                                <p>{{ template.reward_wheel_design.header.header_sub_title_size}}</p>
                            </div>
                            <div class="col-4">
                                <small>Header Title Image</small>
                                <div :style="'width:120px;overflow: hidden;padding:5px;border-radius:5px;background-color:'+template.reward_wheel_design.header.color">
                                    <img style="object-fit:cover;object-position: center;width:100%;height:100%;" :src="template.reward_wheel_design.header.header_title_image" alt="header Title Image">
                                </div>
                            </div>
                        </div>
                        <button class="wheel-template-copy-btn" type="button" @click="copyTemplate(template.reward_wheel_design)">Copy</button>
                    </div>
                </div>
                <div class="card" v-if="template.type == 'button'">
                    <div class="card-body" >
                        <h6>{{template.wheel_name ? template.wheel_name : 'Default Configuration' }}</h6>
                        <div class="row">
                            <div class="col-3">
                                <small>Color</small>
                                <div :style="'width: 42px; height: 42px; border: 1px solid #e5e5e5; border-radius: 6px; background:'+ template.wheel.button.wheel_bottom_button_background_color"></div>
                            </div>
                            <div class="col-3">
                                <small>Button Text Color</small>
                                <div :style="'width: 42px; height: 42px; border: 1px solid #e5e5e5; border-radius: 6px; background:'+ template.wheel.button.wheel_bottom_button_text_color"></div>
                            </div>
                            <div class="col-3">
                                <small>Button Text</small>
                                <p>{{ template.wheel.button.wheel_bottom_button_text }}</p>
                            </div>
                            <div class="col-3">
                                <small>Title Text Size</small>
                                <p>{{template.wheel.button.wheel_bottom_button_text_size}}</p>
                            </div>
                            
                        </div>
                        <button class="wheel-template-copy-btn" type="button" @click="copyTemplate(template.wheel)">Copy</button>
                    </div>
                </div>
                <div class="card" v-if="template.type == 'footer'">
                    <div class="card-body">
                        <h6>{{template.wheel_name ? template.wheel_name : 'Default Configuration' }}</h6>
                        <div class="row">
                            <div class="col-3">
                                <small>Color</small>
                                <div :style="'width: 42px; height: 42px; border: 1px solid #e5e5e5; border-radius: 6px; background:'+ template.reward_wheel_design.footer.color"></div>
                            </div>
                            <div class="col-3">
                                <small>Transparency</small>
                                <p>{{ template.reward_wheel_design.footer.footer_transparent == 1 ? 'True' : 'False' }}</p>
                            </div>
                        </div>
                        <button class="wheel-template-copy-btn" type="button" @click="copyTemplate(template.reward_wheel_design)">Copy</button>
                    </div>
                </div>
                <div class="card" v-if="template.type == 'countbadge'">
                    <div class="card-body">
                        <h6>{{template.wheel_name ? template.wheel_name : 'Default Configuration' }}</h6>
                        <div class="row">
                            <div class="col-3">
                                <small>Color</small>
                                <div :style="'width: 42px; height: 42px; border: 1px solid #e5e5e5; border-radius: 6px; background:'+ template.reward_wheel_design.spin_button.color"></div>
                            </div>
                            <div class="col-3">
                                <small>Text Color</small>
                                <div :style="'width: 42px; height: 42px; border: 1px solid #e5e5e5; border-radius: 6px; background:'+ template.reward_wheel_design.spin_button.text_color"></div>
                            </div>
                            <div class="col-3">
                                <small>Transparency</small>
                                <p>{{ template.reward_wheel_design.spin_button.spin_transparent == 1 ? 'True' : 'False' }}</p>
                            </div>
                        </div>
                        <button class="wheel-template-copy-btn" type="button" @click="copyTemplate(template.reward_wheel_design)">Copy</button>
                    </div>
                </div>
                <div class="card" v-if="template.type == 'wheelouter'">
                    <div class="card-body">
                        <h6>{{template.wheel_name ? template.wheel_name : 'Default Configuration' }}</h6>
                        <div class="row">
                            <div class="col-3">
                                <small>Color</small>
                                <div :style="'width: 42px; height: 42px; border: 1px solid #e5e5e5; border-radius: 6px; background:'+ template.reward_wheel_design.wheel.outer.color"></div>
                            </div>
                            <div class="col-3">
                                <small>Size</small>
                                <p>{{ template.reward_wheel_design.wheel.outer.size }}</p>
                            </div>
                            <div class="col-3">
                                <small>Shadow</small>
                                <p>{{ template.reward_wheel_design.wheel.outer_shadow  == 1 ? 'True' : 'False'}}</p>
                            </div>
                        </div>
                        <button class="wheel-template-copy-btn" type="button" @click="copyTemplate(template.reward_wheel_design)">Copy</button>
                    </div>
                </div>
                <div class="card" v-if="template.type == 'wheelinner'">
                    <div class="card-body">
                        <h6>{{template.wheel_name ? template.wheel_name : 'Default Configuration' }}</h6>
                        <div class="row">
                            <div class="col-6">
                                <div class="row mx-0">
                                    <div>
                                        <small>Color 1</small>
                                        <div :style="'width: 42px; height: 42px; border: 1px solid #e5e5e5; border-radius: 6px; background:'+ template.reward_wheel_design.wheel.inner.color_1"></div>
                                    </div>
                                    <div class="mx-3">
                                        <small>Color 2</small>
                                        <div :style="'width: 42px; height: 42px; border: 1px solid #e5e5e5; border-radius: 6px; background:'+ template.reward_wheel_design.wheel.inner.color_2"></div>
                                    </div>
                                    <div>
                                        <small>Color 3</small>
                                        <div :style="'width: 42px; height: 42px; border: 1px solid #e5e5e5; border-radius: 6px; background:'+ template.reward_wheel_design.wheel.inner.color_3"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-3">
                                <small>Size</small>
                                <p>{{ template.reward_wheel_design.wheel.inner.size }}</p>
                            </div>
                            <div class="col-3">
                                <small>Shadow</small>
                                <p>{{ template.reward_wheel_design.wheel.inner_shadow == 1 ? 'True' : 'False' }}</p>
                            </div>
                        </div>
                        <button class="wheel-template-copy-btn" type="button" @click="copyTemplate(template.reward_wheel_design)">Copy</button>
                    </div>
                </div>
                <div class="card" v-if="template.type == 'wheelpointer'">
                    <div class="card-body">
                        <h6>{{template.wheel_name ? template.wheel_name : 'Default Configuration' }}</h6>
                        <small>Pointer Arrow</small> <br>
                        <img :src="template.reward_wheel_design.wheel.pointer_arrow" style="width: 50px;" />
                        <button class="wheel-template-copy-btn" type="button" @click="copyTemplate(template.reward_wheel_design)">Copy</button>
                    </div>
                </div>
                <div class="card" v-if="template.type == 'wheellights'">
                    <div class="card-body">
                        <h6>{{template.wheel_name ? template.wheel_name : 'Default Configuration' }}</h6>
                        <div class="row">
                            <div class="col-3">
                                <small>Color</small>
                                <div :style="'width: 42px; height: 42px; border: 1px solid #e5e5e5; border-radius: 6px; background:'+ template.reward_wheel_design.wheel.pin.color"></div>
                            </div>
                            <div class="col-3">
                                <small>Size</small>
                                <p>{{ template.reward_wheel_design.wheel.pin.size }}</p>
                            </div>
                            <div class="col-3">
                                <small>Glow</small>
                                <p>{{ template.reward_wheel_design.wheel.pin.glow == 1 ? 'True' : 'False' }}</p>
                            </div>
                            <div class="col-3">
                                <small>Count</small>
                                <p>{{ template.reward_wheel_design.wheel.pin.count }}x</p>
                            </div>
                        </div>
                        <button class="wheel-template-copy-btn" type="button" @click="copyTemplate(template.reward_wheel_design)">Copy</button>
                    </div>
                </div>
                <div class="card" v-if="template.type == 'canpaylogo'">
                    <div class="card-body">
                        <h6>{{template.wheel_name ? template.wheel_name : 'Default Configuration' }}</h6>
                        <div class="row">
                            <div class="col-4">
                                <small>CP Logo</small> <br>
                                <img :src="template.reward_wheel_design.wheel.cp_logo" style="width: 50px;" />
                            </div>
                            <div class="col-3">
                                <small>Shadow</small>
                                <p>{{ template.reward_wheel_design.wheel.cp_shadow == 1 ? 'True' : 'False' }}</p>
                            </div>
                        </div>
                        <button class="wheel-template-copy-btn" type="button" @click="copyTemplate(template.reward_wheel_design)">Copy</button>
                    </div>
                </div>
            </div>
        </b-modal>
    </div>
</template>

<script>
import api from "@/api/rewardwheel.js";

export default {
    props: {
        value: Object,
        type: String,
        label: String,
        allTemplates: Array
    },
    data(){
        return {
            modalTitle: "",
            templates: []
        }
    },
    mounted(){
        this.setModalHeaderTitle();
    },
    methods: {
        setModalHeaderTitle(){
            switch (this.type) {
                case "page":
                    this.modalTitle = "Page Template";
                    break;
                case "header":
                    this.modalTitle = "Header Template";
                    break;
                case "footer":
                    this.modalTitle = "Footer Template";
                    break;
                case "countbadge":
                    this.modalTitle = "Spin Count Badge Template";
                    break;
                case "wheelouter":
                    this.modalTitle = "Wheel Outer Template";
                    break;
                case "wheelinner":
                    this.modalTitle = "Wheel Inner Template";
                    break;
                case "wheelpointer":
                    this.modalTitle = "Wheel Pointer Template";
                    break;
                case "wheellights":
                    this.modalTitle = "Wheel Lights Template";
                    break;
                case "canpaylogo":
                    this.modalTitle = "CanPay Logo Template";
                    break;
                case "button":
                    this.modalTitle = "Wheel Below Button";
            }
        },
        openModal(modal){
            this.$bvModal.show(modal);
        },
        setTemplateValue(){
                this.allTemplates.forEach(template => {
                    switch (this.type) {
                        case "page":
                            this.templates.push({
                                wheel_name: template.wheel_name,
                                type: 'page',
                                reward_wheel_design: {
                                    page: {
                                        color_1: template.reward_wheel_design.page.color_1,
                                        color_2: template.reward_wheel_design.page.color_2,
                                        color_3: template.reward_wheel_design.page.color_3,
                                        is_image: template.reward_wheel_design.page.is_image,
                                        image: template.reward_wheel_design.page.image,
                                    },
                                    sparkles: {
                                        show: template.reward_wheel_design.sparkles.show ? 1 : 0
                                    }
                                }

                            });
                            break;
                        case "button":
                            this.templates.push({
                                wheel_name: template.wheel_name,
                                type: 'button',
                                wheel: {
                                    button: {
                                        wheel_bottom_button_background_color: template.reward_wheel_design.wheel.wheel_bottom_button_background_color,
                                        wheel_bottom_button_text: (template.reward_wheel_design.wheel.wheel_bottom_button_text == "" || template.reward_wheel_design.wheel.wheel_bottom_button_text == null || template.reward_wheel_design.wheel.wheel_bottom_button_text == undefined)?'N/A':template.reward_wheel_design.wheel.wheel_bottom_button_text,
                                        wheel_bottom_button_text_color: template.reward_wheel_design.wheel.wheel_bottom_button_text_color,
                                        wheel_bottom_button_text_size: (template.reward_wheel_design.wheel.wheel_bottom_button_text_size == "" || template.reward_wheel_design.wheel.wheel_bottom_button_text_size == undefined || template.reward_wheel_design.wheel.wheel_bottom_button_text_size == null)?'N/A':template.reward_wheel_design.wheel.wheel_bottom_button_text_size+"px",
                                    }
                                }
                            });
                            break;   
                        case "header":
                            this.templates.push({
                                wheel_name: template.wheel_name,
                                type: 'header',
                                reward_wheel_design: {
                                    header: {
                                        color: template.reward_wheel_design.header.color,
                                        page_title: template.reward_wheel_design.header.page_title,
                                        page_title_size: template.reward_wheel_design.header.page_title_size,
                                        header_transparent: template.reward_wheel_design.header.header_transparent ? 1 : 0,
                                        header_title_image: template.reward_wheel_design.header.header_title_image,
                                        header_sub_title: (template.reward_wheel_design.header.header_sub_title == "" || template.reward_wheel_design.header.header_sub_title == null || template.reward_wheel_design.header.header_sub_title == undefined)?'N/A':template.reward_wheel_design.header.header_sub_title,
                                        header_sub_title_size:(template.reward_wheel_design.header.header_sub_title_size == "" || template.reward_wheel_design.header.header_sub_title_size == null || template.reward_wheel_design.header.header_sub_title_size == undefined)?'N/A':template.reward_wheel_design.header.header_sub_title_size+"px",
                                        header_sub_title_color: template.reward_wheel_design.header.header_sub_title_color,
                                        header_type: template.reward_wheel_design.header.header_type
                                    }
                                }
                            });
                            break;
                        case "footer":
                            this.templates.push({
                                wheel_name: template.wheel_name,
                                type: 'footer',
                                reward_wheel_design: {
                                    footer: {
                                        color: template.reward_wheel_design.footer.color,
                                        footer_transparent: template.reward_wheel_design.footer.footer_transparent ? 1 : 0
                                    }
                                }
                            });
                            break;
                        case "countbadge":
                            this.templates.push({
                                wheel_name: template.wheel_name,
                                type: 'countbadge',
                                reward_wheel_design: {
                                    spin_button: {
                                        color: template.reward_wheel_design.spin_button.color,
                                        text_color: template.reward_wheel_design.spin_button.text_color,
                                        spin_transparent: template.reward_wheel_design.spin_button.spin_transparent ? 1 : 0
                                    }
                                }
                            });
                            break;
                        case "wheelouter":
                            this.templates.push({
                                wheel_name: template.wheel_name,
                                type: 'wheelouter',
                                reward_wheel_design: {
                                    wheel: {
                                        outer: {
                                            color: template.reward_wheel_design.wheel.outer.color,
                                            size: template.reward_wheel_design.wheel.outer.size
                                        },
                                        outer_shadow: template.reward_wheel_design.wheel.outer_shadow ? 1 : 0
                                    },
                                }
                            });
                            break;
                        case "wheelinner":
                            this.templates.push({
                                wheel_name: template.wheel_name,
                                type: 'wheelinner',
                                reward_wheel_design: {
                                    wheel: {
                                        inner: {
                                            color_1: template.reward_wheel_design.wheel.inner.color_1,
                                            color_2: template.reward_wheel_design.wheel.inner.color_2,
                                            color_3: template.reward_wheel_design.wheel.inner.color_3,
                                            size: template.reward_wheel_design.wheel.inner.size
                                        },
                                        inner_shadow: template.reward_wheel_design.wheel.inner_shadow ? 1 : 0
                                    }
                                }
                            });
                            break;
                        case "wheelpointer":
                            this.templates.push({
                                wheel_name: template.wheel_name,
                                type: 'wheelpointer',
                                reward_wheel_design: {
                                    wheel: {
                                        pointer_arrow: template.reward_wheel_design.wheel.pointer_arrow
                                    }
                                }
                            });
                            break;
                        case "wheellights":
                            this.templates.push({
                                wheel_name: template.wheel_name,
                                type: 'wheellights',
                                reward_wheel_design: {
                                    wheel: {
                                        pin: {
                                            color: template.reward_wheel_design.wheel.pin.color,
                                            size: template.reward_wheel_design.wheel.pin.size,
                                            glow: template.reward_wheel_design.wheel.pin.glow ? 1 : 0,
                                            count: template.reward_wheel_design.wheel.pin.count
                                        }
                                    }
                                }
                            });
                            break;
                        case "canpaylogo":
                            this.templates.push({
                                wheel_name: template.wheel_name,
                                type: 'canpaylogo',
                                reward_wheel_design: {
                                    wheel: {
                                        cp_logo: template.reward_wheel_design.wheel.cp_logo,
                                        cp_shadow: template.reward_wheel_design.wheel.cp_shadow ? 1 : 0
                                    }
                                }
                            });
                            break;
                    }
                });
        },
        copyTemplate(template) {
            let formTemplate = JSON.parse(JSON.stringify(this.value)); // Make a deep copy of the original object
            this.updateTemplateObject(formTemplate, template);
            this.formTemplate = formTemplate; // Assign the updated object to the formTemplate variable
            this.$emit("input", this.formTemplate)
            this.$bvModal.hide('wheel-template-modal-'+this.type)
        },
        updateTemplateObject(obj, updatedValues) {
            for (const key in updatedValues) {
                if (typeof updatedValues[key] === 'object' && typeof obj[key] === 'object') {
                this.updateTemplateObject(obj[key], updatedValues[key]);
                } else {
                this.$set(obj, key, updatedValues[key]); // Use Vue's $set method to update the property value
                }
            }
        }
    }
}
</script>

<style>
.wheel-template-btn{
    border: 1px solid #cbcbcb;
    background-color: white;
    padding: 5px 15px;
    border-radius: 50px;
}
.wheel-template-copy-btn{
    border: 1px solid #c9c9c9;
    position: absolute;
    top: 10px;
    right: 10px;
    padding: 5px 15px;
    background: #f7f7f7;
    color: #000;
    border-radius: 50px;
    font-size: 12px;
}
.copied{
    border: 0;
    background: #000;
    color: #fff;
}


</style>

