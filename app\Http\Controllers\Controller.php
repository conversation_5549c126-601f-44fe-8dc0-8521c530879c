<?php

namespace App\Http\Controllers;

use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use App\Exceptions\ValidationException;
use Illuminate\Support\Facades\Validator;
use App\Exceptions\ApplicationException;
use App\Exceptions\AuthException;

class Controller extends BaseController
{
    use AuthorizesRequests, DispatchesJobs, ValidatesRequests;

    /**
     *
     * @param type $rules
     * @param type $data
     * @throws ValidationException
     */
    function __validate($data, $rules) {
        $validator = Validator::make($data, $rules);
        if ($validator->fails()) {
            throw new ValidationException(error2ul((array) $validator->errors()->all()));
        }
    }

    function __applicationExcetion($message) {
            throw new ApplicationException($message);
    }

    function __unAuthException($message) {
            throw new AuthException($message);
    }

    function __validationException($message) {
            throw new ValidationException($message);
    }
}
