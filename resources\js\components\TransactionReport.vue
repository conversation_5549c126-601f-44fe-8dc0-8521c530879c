<template>
<div>
  <div v-if="loading">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px;">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Transaction Report</h3>
                </div>

                <div class="card-body">
                  <div class="row">
                  <div class="col-12">
                    <b-table
                      show-empty
                      head-variant="light"
                      responsive
                      bordered
                      sticky-header="600px"
                      :fields="fields"
                      :items="report"
                      :tbody-tr-class="report.length!= ''?rowClass:''"
                    >
                    </b-table>

                    <b-table-simple
                      show-empty
                      head-variant="light"
                      responsive
                      bordered
                      sticky-header="600px"
                    >
                    <b-tbody>
                      <b-tr>
                        <b-td width="55%">
                          Represented Transaction
                        </b-td>
                        <b-td>
                          {{total_represented}}
                        </b-td>
                      </b-tr>
                      <b-tr>
                        <b-td width="55%">
                          New Transactions
                        </b-td>
                        <b-td>
                          {{new_transaction_amount}}
                        </b-td>
                      </b-tr>
                      <b-tr>
                        <b-td width="55%">
                          Total Transaction Represented
                        </b-td>
                        <b-td>
                          {{represented_transaction_amount}}
                        </b-td>
                      </b-tr>
                      <b-tr>
                        <b-td width="55%">
                          Total Canpay Offset
                        </b-td>
                        <b-td>
                          {{canpay_offset}}
                          <span class="export-api-btn" v-if="canpay_offset!=0"><a href="javascript:void(0);" class="custom-edit-btn" title="Download Return List" @click="downloadReturnList();"><i class="nav-icon fa fa-download"></i></a></span>
                        </b-td>
                      </b-tr>
                    </b-tbody>
                    </b-table-simple>
                  </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>


  </div>
</div>
</template>
<script>
import api from "@/api/transaction.js";
import { saveAs } from "file-saver";
import moment from "moment";
import { validationMixin } from "vuelidate";
import { required, minLength } from "vuelidate/lib/validators";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "./CustomLoader/CanPayLoader.vue"
export default {
  mixins: [validationMixin],
  data() {
    return {
      fields: [
        {"key":"head" ,"class":"text-left"},
        {"key":"credit" ,"class":"text-center"},
        {"key":"debit" ,"class":"text-center"},
      ],
      loading: false,
      report: [],
      total_represented:0,
      new_transaction_amount:0,
      represented_transaction_amount:0,
      canpay_offset:0
    };
  },
  components: {
    HourGlass,
    CanPayLoader
  },
  created() {
  },
  methods: {
    // this method returns the rows colours
    rowClass(item, type) {
      return item.rowClass;
    },
    // API call to generate the all the Transactions Report
    generateReport() {
      var self = this;
      self.report = [];
      self.loading = true;
      api
        .generateTransactionReport()
        .then(function (response) {
          if (response.code == 200) {
            self.report = response.data.report;
            self.total_represented = response.data.total_represented;
            self.new_transaction_amount = response.data.new_transaction_amount;
            self.represented_transaction_amount = response.data.represented_transaction_amount;
            self.canpay_offset = response.data.canpay_offset;
            self.loading = false;
          } else {
            error(response.message);
            self.loading = false;
          }
        })
        .catch(function (error) {
          // error(error);
          self.loading = false;
        });
    },
    downloadReturnList(){
      var self = this;
      
      api
        .exportReturnList()
        .then(function (response) {
          var FileSaver = require("file-saver");
          var blob = new Blob([response], {
            type: "application/xlsx",
          });
          FileSaver.saveAs(
            blob,
            moment().format("MM/DD/YYYY") +
              "_return_list_daily_export.xlsx"
          );
        })
        .catch(function (error) {
          // error(error);
        });
    }
  },
  mounted() {
    $("#report_date").datepicker({
      format: "mm/dd/yyyy",
      autoclose: true,
      endDate: '-1d',
    });
    $("#report_date").datepicker("setDate", "-1d");
    this.generateReport();
  },
};
</script>
