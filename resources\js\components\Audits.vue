<template>
  <div class="content-wrapper" style="min-height: 36px;">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Audit Trails</h3>
                  <b-button
                  class="btn-danger export-api-btn"
                  @click="reloadDatatable"
                  v-if="showReloadBtn"
                  >
                    <i class="fas fa-redo"></i> Reload
                  </b-button>
                </div>
                <!-- /.card-header -->
                <div class="card-body">
                  <table
                    id="auditsTable"
                    class="table"
                    style="width:100%;white-space: normal;"
                  >
                    <thead>
                      <tr>
                        <th>Module</th>
                        <th>Action</th>
                        <th>User</th>
                        <th>Action Time</th>
                        <th style="width:10%;">View Details</th>
                      </tr>
                    </thead>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
    <!-- View Audit Details Modal Start -->
    <b-modal
      id="view-audit-modal"
      ref="view-audit-modal"
      :header-text-variant="headerTextVariant"
      title="Audit Details"
      hide-footer
    >
    <h4>Old Record</h4>
    <div v-html="auditModel.old_values" v-if="auditModel.old_values != ''"></div>
    <div v-else class="alert alert-primary" role="alert">
        No Old Records Found
    </div>
    <h4>New Record</h4>
    <div v-html="auditModel.new_values"></div>
      <div class="modal-footer">
        <b-button
          class="mt-3"
          variant="outline-success"
          @click="$bvModal.hide('view-audit-modal')"
        >Close</b-button>
      </div>
    </b-modal>
  </div>
</template>
<script>
import commonConstants from "@/common/constant.js";
export default {
  data() {
    return {
      headerTextVariant: "light",
      allAuditModel : {},
      auditModel: {},
      currentUser: localStorage.getItem("user")? JSON.parse(localStorage.getItem("user")): null,
      showReloadBtn:false,
    };
  },
  created() {
      this.viewAuditDetails();
  },
  methods: {
    reloadDatatable(){
      var self = this;
      self.loadDT();
    },
    viewAuditDetails() {
      var self = this;
      $(document).on("click", ".viewAuditDetails", function(e) {
        var auditDetails = self.allAuditModel.find(
          p => p.edit == $(e.currentTarget).attr("data-audit-id")
        );
        self.auditModel = auditDetails;
        //self.auditModel.new_values = auditDetails.new_values;
        self.$bvModal.show("view-audit-modal");
      });
    },
    loadDT: function() {
      var self = this;
      $("#auditsTable").DataTable({
        pagingType: "simple_numbers",
        processing: true,
        serverSide: true,
        destroy: true,
        columnDefs: [
          { orderable: false, targets: [4] },
          { className: "dt-left", targets: [1, 2, 3] },
          { className: "dt-center", targets: [4] },
          { className: "width-class-dt", "targets": [ 0 ] }
        ],
        order: [[3, "desc"]],
        orderClasses: false,
        language: {
          processing: '<i class="fa fa-spinner fa-spin fa-3x fa-fw"></i><span class="sr-only">Loading...</span> ',
          emptyTable: "No Audit Trail Available.",
          search: "_INPUT_",
          searchPlaceholder: "Search records",
          oPaginate: {
            sNext: '<i class="fas fa-angle-double-right"></i>',
            sPrevious: '<i class="fas fa-angle-double-left"></i>'
          },
          sLengthMenu:
            "<label class='label_dropdown_dt'>Per page</label> _MENU_"
        },
        ajax: {
          headers: {
            Authorization: "Bearer " + localStorage.getItem("token")
          },
          url: "/api/getallaudits",
          type: "POST",
          data: { _token: "{{csrf_token()}}" },
          dataType: "json",
          dataSrc: function(result) {
            self.showReloadBtn = false;
            self.allAuditModel = result.data;
            return self.allAuditModel;
          },
          error: function(data){
            error(commonConstants.datatable_error);
            $('#auditsTable_processing').hide();
            self.showReloadBtn = true;
          }
        },
        columns: [
          { data: "model" },
          { data: "event" },
          { data: "name" },
          { data: "created_at" },
          {
            render: function(data, type, full, meta) {
                return (
                    '<b-button data-audit-id="' +
                    full.edit +
                    '" class="viewAuditDetails custom-edit-btn" title="View Audit Details" variant="outline-success"><i class="nav-icon fas fa-eye"></i></b-button>'
                );
            }
          }
        ]
      });

      $("#auditsTable").on("page.dt", function() {
        $("html, body").animate({ scrollTop: 0 }, "slow");
        $("th:first-child").focus();
      });

      //Search in the table only after 3 characters are typed
      // Call datatables, and return the API to the variable for use in our code
      // Binds datatables to all elements with a class of datatable
      var dtable = $("#auditsTable").dataTable().api();

      // Grab the datatables input box and alter how it is bound to events
      $(".dataTables_filter input")
      .unbind() // Unbind previous default bindings
      .bind("input", function(e) { // Bind our desired behavior
          // If the length is 3 or more characters, or the user pressed ENTER, search
          if(this.value.length >= 3 || e.keyCode == 13) {
              // Call the API search function
              dtable.search(this.value).draw();
          }
          // Ensure we clear the search if they backspace far enough
          if(this.value == "") {
              dtable.search("").draw();
          }
          return;
      });
    }
  },
  mounted() {
    var self = this;
    setTimeout(function() {
      self.loadDT();
    }, 1000);
    document.title = "CanPay - Audit Trail";
  }
};
</script>
<style>
.width-class-dt{
    width:5% !important;
}
</style>

