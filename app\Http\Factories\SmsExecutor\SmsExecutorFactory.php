<?php

namespace App\Http\Factories\SmsExecutor;

use App\Http\Clients\TwilioHttpClient;
use App\Models\NotificationLog;
use Illuminate\Support\Facades\Log;

class SmsExecutorFactory implements SmsExecutorInterface
{
    private $twilio = null;

    public function __construct()
    {
        $this->twilio = new TwilioHttpClient();
    }

    /**
     * This function sends OTP to the given phone number during registration process
     */
    public function resendSMS($params)
    {
        $notification = NotificationLog::find($params['notification_id']);

        $params['parent_id'] = $notification->id;
        $params['user_id'] = $notification->user_id;
        $params['event'] = $notification->notification_event;
        $params['phone_no'] = $notification->to_phone;
        $params['is_resend'] = 1;
        $params['sms_body'] = $notification->sms_body;

        $notification_data = $this->_createNotificationData($params);
        $client_name = $notification_data['client_name'];

        // calling the client to send SMS
        return $this->$client_name->sendSMS($notification_data);
    }

    private function _createNotificationData($params)
    {
        $params['type'] = SMS;
        $notification_channel = getNotificationChannel($params);
        $params['client'] = $notification_channel->val;
        $params['notification_channel'] = $notification_channel->name;
        $params['client_name'] = strtolower(explode(" ", $notification_channel->name)[0]);

        // Prepare the notification body
        $notification_data = [
            'parent_id' => isset($params['parent_id']) ? $params['parent_id'] : 0,
            'user_id' => $params['user_id'],
            'type' => $params['type'],
            'to_phone' => $params['phone_no'],
            'sms_body' => $params['sms_body'],
            'gateway_used' => $notification_channel->id,
            'event' => $params['event'],
            'is_resend' => isset($params['is_resend']) ? $params['is_resend'] : 0,
        ];
        $params['notification_id'] = insertNotificationData($notification_data); // Send the data for insertion
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Notification data inserted and sending data to Client.");
        return $params;
    }

}
