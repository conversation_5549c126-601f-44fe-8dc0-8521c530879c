<?php
namespace App\Http\Clients;

use App\Models\ValidationLog;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 *
 * @package App\Http\Clients
 */
class FinicityHttpClient
{
    /**
     * @var Client
     */
    private $client;
    private $clientNoError;
    /**
     * FinicityHttpClient constructor.
     */
    public function __construct()
    {
        $this->client = new Client(['base_uri' => config('app.finicity_base_url')]);
        $this->clientNoError = new Client(['base_uri' => config('app.finicity_base_url'), 'http_errors' => false]);
    }

    public function getAccessToken($params)
    {
        try {
            $params['api'] = '/aggregation/v2/partners/authentication';
            $request['headers'] = $params['headers'];
            $request['body'] = $params['body'];
            $response = $this->client->post($params['api'], $request);
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Finicity partner aunthentication returned response : " . $response->getBody());
            $params['response'] = $response->getBody();
            //storing the response in the database
            $this->storeResponseInDB($params);
            return $response->getBody();
        } catch (\GuzzleHttp\Exception\ClientException $ex) {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while sending request to finicity.", [EXCEPTION => $ex]);
            return $ex->getResponse()->getBody()->getContents();
        }
    }

    /**
     * Store API details to database to keep a log
     *
     * @return mixed
     */
    public function storeResponseInDB($params)
    {
        $log_data = array(
            'api' => $params['api'],
            'phone' => $params['phoneNo'],
            'response' => $params['response'],
            'type' => 'finicity',
            'session_id' => isset($params['session_id']) ? $params['session_id'] : null, //session id is only for registration session
        );
        ValidationLog::create($log_data);
        DB::commit();
    }
    /**
     * Gets the account details fro customer
     *
     * @return mixed
     */
    public function getCustomerAccounts($params)
    {
        try {
            $params['api'] = '/aggregation/v1/customers/' . $params['customer_id'] . '/accounts';
            $request['headers'] = $params['headers'];
            $response = $this->client->get($params['api'], $request);
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Finicity get accounts by customer returned response : " . $response->getBody());
            $params['response'] = $response->getBody();
            //storing the response in the database
            $this->storeResponseInDB($params);
            return $response->getBody();
        } catch (\GuzzleHttp\Exception\ClientException $ex) {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while sending request to finicity.", [EXCEPTION => $ex]);
            return $ex->getResponse()->getBody()->getContents();
        }
    }

    /**
     * ACH account verification which returns routing number and accoount number in the response
     *
     * @return mixed
     */
    public function getCustomerACHAccounts($params)
    {
        try {
            $params['api'] = '/aggregation/v1/customers/' . $params['customer_id'] . '/accounts/' . $params['account_id'] . '/details';
            $request['headers'] = $params['headers'];
            $response = $this->client->get($params['api'], $request);
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Finicity get ACH account details returned response : " . $response->getBody());
            $params['response'] = $response->getBody();
            //storing the response in the database
            $this->storeResponseInDB($params);
            return $response->getBody();
        } catch (\GuzzleHttp\Exception\ClientException $ex) {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while sending request to finicity.", [EXCEPTION => $ex]);
            $responceValue = json_decode($ex->getResponse()->getBody()->getContents(), true);
            if ($responceValue['code'] == REAL_ACCOUNT_NUMBER_NOT_FOUND) {
                return REAL_ACCOUNT_NUMBER_NOT_FOUND;
            } else {
                return $ex->getResponse()->getBody();
            }

        }
    }

    /**
     * Gets the account details fro customer
     *
     * @return mixed
     */
    public function getConsumerRefreshBalance($params)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Refresh account Balance called for Consumer ID : " . $params['customer_id'] . " and Institution Login ID : " . $params['institution_login_id']);
        try {
            $params['api'] = '/aggregation/v1/customers/' . $params['customer_id'] . '/institutionLogins/' . $params['institution_login_id'] . '/accounts';
            $request['headers'] = $params['headers'];
            $response = $this->client->get($params['api'], $request);
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Finicity get accounts by customer and institution returned response : " . $response->getBody());
            $params['response'] = $response->getBody();
            //storing the response in the database
            $this->storeResponseInDB($params);
            return $response->getBody();
        } catch (\GuzzleHttp\Exception\ClientException $ex) {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while sending request to finicity.", [EXCEPTION => $ex]);
            return $ex->getResponse()->getBody()->getContents();
        }
    }

    /**
     * Get customer active account owner information
     *
     * @return mixed
     */
    public function getAccountOwnerInfo($params)
    {
        try {
            $params['api'] = '/aggregation/v1/customers/' . $params['customer_id'] . '/accounts/' . $params['account_id'] . '/owner';
            $request['headers'] = $params['headers'];
            $response = $this->client->get($params['api'], $request);
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Finicity get account owner information returned response : " . $response->getBody());
            $params['response'] = $response->getBody();
            return $response->getBody();
        } catch (\GuzzleHttp\Exception\ClientException $ex) {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while sending request to finicity.", [EXCEPTION => $ex]);
            return $ex->getResponse()->getBody()->getContents();
        }
    }

    /**
     * Refresh Account Balance
     *
     * @return mixed
     */
    public function getConsumerRefreshAccountsBalance($params)
    {
        try {
            $params['api'] = '/aggregation/v1/customers/' . $params['customer_id'] . '/accounts';
            $request['headers'] = $params['headers'];
            $response = $this->client->post($params['api'], $request);
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Finicity Refresh Customer Accounts returned response : " . $response->getBody());
            $params['response'] = $response->getBody();
            //storing the response in the database
            $this->storeResponseInDB($params);
            return $response->getBody();
        } catch (\GuzzleHttp\Exception\ClientException $ex) {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while sending request to finicity for Refresh Customer Accounts.", [EXCEPTION => $ex]);
            return $ex->getResponse()->getBody()->getContents();
        }
    }
    
    /**
     * Get consumer institution details
     *
     * @return mixed
     */
    public function getInstitution($params)
    {
        try {
            $params['api'] = '/institution/v2/institutions/' . $params['institution_id'];
            $request['headers'] = $params['headers'];
            $response = $this->client->get($params['api'], $request);
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Finicity get institution returned response : " . $response->getBody());
            $params['response'] = $response->getBody();
            //storing the response in the database
            $this->storeResponseInDB($params);
            return $response->getBody();
        } catch (\GuzzleHttp\Exception\ClientException $ex) {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while sending request to finicity.", [EXCEPTION => $ex]);
            return $ex->getResponse()->getBody()->getContents();
        }
    }
    
    /**
     * Delete Customer Accounts by Institution Login
     *
     * @return mixed
     */
    public function deleteConsumerAccounts($params)
    {
        try {
            $params['api'] = '/aggregation/v1/customers/' . $params['customer_id'] . '/institutionLogins/' . $params['institution_login_id'];
            $request['headers'] = $params['headers'];
            $response = $this->client->delete($params['api'], $request);
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Finicity delete consumer accounts returned response : " . $response->getBody());
            $params['response'] = $response->getBody();
            //storing the response in the database
            $this->storeResponseInDB($params);
            return $response->getBody();
        } catch (\GuzzleHttp\Exception\ClientException $ex) {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while sending request to finicity.", [EXCEPTION => $ex]);
            return $ex->getResponse()->getBody()->getContents();
        }
    }

}
