<?php

namespace App\Http\Factories\Akoya;

use App\Http\Clients\AkoyaHttpClient;
use App\Http\Factories\PurchasePower\PurchasePowerFactory;
use App\Models\AkoyaRefreshToken;
use App\Models\User;
use App\Models\UserBankAccountInfo;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

/**
 *
 * @package App\Http\Factories\Akoya
 */
class AkoyaFactory implements AkoyaInterface
{
    private $akoyaClient = null;

    public function __construct()
    {
        $this->akoyaClient = new AkoyaHttpClient();
        $this->purchasePower = new PurchasePowerFactory();
    }

    /**
     * getConsumerAccountBalance
     * THis function will return a consumer account balance based on the account id
     * @param  mixed $account_id
     * @return void
     */
    public function getConsumerAccountBalance($account_id, $consumer_id, $for_scheduler = false)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Get counstomer account balance started...");
        try {
            // Get the token
            $provider = UserBankAccountInfo::where('account_id', $account_id)->where('user_id', $consumer_id)->first();
            $params['id_token'] = $this->refreshToken($consumer_id, $provider->institution_id);
            $params['akoya_provider_id'] = $provider->institution_id;
            $params['consumer_id'] = $consumer_id;
            $getBalance = $this->akoyaClient->getAccountInfo($params, $account_id);
            $balance_data = json_decode($getBalance, true);
            $balance = 0;
            if (isset($balance_data['accounts'])) {
                foreach ($balance_data['accounts'] as $account) {
                    foreach ($account as $value) {
                        if (is_array($value) && array_key_exists('currentBalance', $value)) {
                            if (isset($value['availableBalance'])) {
                                if ($value['availableBalance'] > $value['currentBalance']) {
                                    $balance = $value['availableBalance'];
                                } else {
                                    $balance = $value['currentBalance'];
                                }
                            } else {
                                $balance = $value['currentBalance'];
                            }
                            break 2; // Break both loops once the currentBalance is found
                        }
                    }
                }
                $return = $for_scheduler == 1 ?
                    [
                        "availableBalanceAmount" => isset($value['availableBalance']) ? $value['availableBalance'] : 0,
                        "balance" => $value['currentBalance'],
                        "effectiveBalance" => $balance,
                        "banking_solution_response" => $getBalance,
                    ] :
                    $balance;
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Akoya Balance fetch returned response from Factory: " . json_encode($return));
                return $return;
            } else {
                Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No account information foumd for user:  " . $consumer_id . ".");
                return $for_scheduler == 1 ? ["availableBalanceAmount" => 0, "balance" => 0, "error" => 1, "effectiveBalance" => 0, "banking_solution_response" => "_ERR_No Account Details Found."] : false;
            }
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured while trying to fetch bank account information for user:  " . $consumer_id . ".", [EXCEPTION => $e]);
            return $for_scheduler == 1 ? ["availableBalanceAmount" => 0, "balance" => 0, "error" => 1, "effectiveBalance" => 0, "banking_solution_response" => "_ERR_" . $e->getMessage()] : false;
        }
    }

    /**
     * refreshToken
     * This function will return a refreshed id_token along with a refresh token for a specific consumer
     * @param  mixed $consumer_id
     * @return void
     */
    public function refreshToken($consumer_id, $institution_id)
    {
        $current_date = Carbon::now();
        $token = AkoyaRefreshToken::where('consumer_id', $consumer_id)->where('institution_id', $institution_id)->first();
        if (empty($token) || $current_date->gte(Carbon::parse($token->token_expiration_time))) {
            // Continue with the regular token refresh logic
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Token does not exists or has been expired for Consumer: " . $consumer_id);
            $refreshed_token = json_decode($this->akoyaClient->getRefreshedToken($token->refresh_token, $consumer_id, $institution_id), true);

            // Update the refresh token for the consumer
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Token refresh/update start for Consumer ID: " . $consumer_id . " . New Refresh Token: " . $refreshed_token['refresh_token']);

            $token->id_token = $refreshed_token['id_token'];
            $token->refresh_token = $refreshed_token['refresh_token'];
            $token->token_expiration_time = Carbon::now()->addSeconds($refreshed_token['expires_in'])->toDateTimeString();
            $token->error = 0;
            $token->save();

            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Token updated for Consumer ID: " . $consumer_id);
            return $refreshed_token['id_token'];
        } else {
            // Existing token is valid
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Existing token is valid for Consumer: " . $consumer_id);
            return $token->id_token;
        }
        return false;
    }

    /**
     * This api refreshes balance and purchase power for a consumer
     */
    public function getConsumerRefreshBalance($bank_account, $user_id, $source, $active_status)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Fetching Balance for Akoya... ");
        $balanceNPurchasePower = $this->getConsumerAccountBalance($bank_account->account_id, $user_id, 1);
        if (!isset($balanceNPurchasePower['error'])) {
            $user = User::where('user_id', $user_id)->first(); // Fetching User Details
            $purchase_power = $this->purchasePower->calculatePurchasePower($balanceNPurchasePower["effectiveBalance"], $user);
            $data['user_id'] = $user_id;
            $data['account_id'] = $bank_account->id;
            $data['balance'] = $balanceNPurchasePower["effectiveBalance"];
            $data['response_raw_balance'] = $balanceNPurchasePower['balance'];
            $data['response_available_balance'] = $balanceNPurchasePower['availableBalanceAmount'];
            $data['purchase_power'] = $purchase_power;
            $data['source'] = $source;
            $data['purchase_power_source'] = $user->purchase_power_source;
            $one_time_refresh = $source == ONE_TIME_BALANCE_FETCH ? 1 : 0;
            $data['one_time_refresh'] = $one_time_refresh;
            $data['banking_solution_response'] = $balanceNPurchasePower['banking_solution_response'];
            addCustomerAccounts($data);
            if ($active_status === $bank_account->status) {
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Calculated purchase power for User ID: " . $user_id . " is : $" . $purchase_power);
                User::where(['user_id' => $user_id, 'disable_automatic_purchase_power' => 0])->update(array('purchase_power' => $purchase_power, 'refresh_balance_called' => 1));
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Consumer purchase power updated into database successfully.");
            }
            $response = [
                'code' => SUCCESS,
                'message' => trans('message.balance_refresh'),
                'data' => null,
            ];
        } else {
            $response = [
                'code' => SUCCESS,
                'message' => null,
                'data' => 0,
            ];
        }
        return $response;
    }

    /**
     * getAccountOwnerInfo
     * This function will fetch the account owner information from akoya
     * @param  mixed $account_id
     * @return void
     */
    public function getAccountOwnerInfo($bank_details)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Fetch process started for account owner information from Akoya... ");
        $params['id_token'] = $this->refreshToken($bank_details->user_id, $bank_details->institution_id); // Get the token
        $params['akoya_provider_id'] = $bank_details->institution_id;
        $params['account_id'] = $bank_details->account_id;
        $params['consumer_id'] = $bank_details->user_id;

        if ($bank_details->institution_id == 'capitalone') {
            $response = json_decode($this->akoyaClient->getAccountOwnerInfoCO($params), true);
            if (isset($response['holders'][0]['name'])) {
                $firstName = $response['holders'][0]['name']['first'] ?? '';
                $middleName = $response['holders'][0]['name']['middle'] ?? '';
                $lastName = $response['holders'][0]['name']['last'] ?? '';
                $response['ownerName'] = trim("$firstName $middleName $lastName");
            }
            if (isset($response['addresses'])) {
                $addressData = $response['addresses'];
                // Create a full address
                $response['ownerAddress'] = implode(', ', array_filter([
                    $addressData[0]['line1'] ?? null,
                    $addressData[0]['line2'] ?? null,
                    $addressData[0]['line3'] ?? null,
                    $addressData[0]['city'] ?? null,
                    $addressData[0]['state'] ?? null,
                    $addressData[0]['postalCode'] ?? null,
                    $addressData[0]['country'] ?? null,
                ]));
            }
        } else {
            $response = json_decode($this->akoyaClient->getAccountOwnerInfo($params), true);
            if (isset($response['customer']['name'])) {
                $firstName = $response['customer']['name']['first'] ?? '';
                $middleName = $response['customer']['name']['middle'] ?? '';
                $lastName = $response['customer']['name']['last'] ?? '';
                $response['ownerName'] = trim("$firstName $middleName $lastName");
            }
            if (isset($response['customer']['addresses'])) {
                $addressData = $response['customer']['addresses'];
                // Create a full address
                $response['ownerAddress'] = implode(', ', array_filter([
                    $addressData[0]['line1'] ?? null,
                    $addressData[0]['line2'] ?? null,
                    $addressData[0]['city'] ?? null,
                    $addressData[0]['state'] ?? null,
                    $addressData[0]['postalCode'] ?? null,
                    $addressData[0]['country'] ?? null,
                ]));
            }
        }
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Account owner information fetched successfully for Bank ID: " . $bank_details->id);
        Log::info($response);
        return $response;
    }
}
