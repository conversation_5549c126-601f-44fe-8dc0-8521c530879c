<?php

namespace App\Http\Controllers;

use App\Models\ManualBankLinkRestrictedRoutingNumber;
use App\Models\ManualBankLinkRestrictedRoutingNumberStateMap;
use App\Models\State;
use App\Models\User;
use App\Models\UserBankAccountInfo;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ManualBankLinkRestrictionsController extends Controller
{
    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    /**
     * Fetch all the Whitelist routing numbers
     */
    public function getManualBankLinkRestrictedRoutingNumbers()
    {
        // Columns defined for Sorting
        $columns = array(
            0 => 'manual_bank_link_restricted_routing_numbers.routing_no',
            2 => 'manual_bank_link_restricted_routing_numbers.restriction_year',
            3 => 'manual_bank_link_restricted_routing_numbers.created_at',
        );

        // Main Query
        $query = ManualBankLinkRestrictedRoutingNumber::on(MYSQL_RO)
        ->leftjoin('manual_bank_link_restricted_routing_number_state_maps as mbrrnsm', function ($leftjoin) {
            $leftjoin->on("mbrrnsm.manual_bank_link_restricted_routing_number_id", "=", "manual_bank_link_restricted_routing_numbers.id");
            $leftjoin->join('states', 'states.id', '=', 'mbrrnsm.state_id');
        })
        ->select('manual_bank_link_restricted_routing_numbers.*', DB::raw("GROUP_CONCAT(distinct(states.code) SEPARATOR ',') as state"), DB::raw("GROUP_CONCAT(distinct(states.id) SEPARATOR ',') as state_ids"))->whereNotNull('manual_bank_link_restricted_routing_numbers.age')
        ->groupBy('manual_bank_link_restricted_routing_numbers.routing_no', 'manual_bank_link_restricted_routing_numbers.age');

        //Count Query
        $queryCount = ManualBankLinkRestrictedRoutingNumber::on(MYSQL_RO)
        ->whereNotNull('manual_bank_link_restricted_routing_numbers.age')
        ->groupBy('manual_bank_link_restricted_routing_numbers.routing_no', 'manual_bank_link_restricted_routing_numbers.age');
        $totalData = $queryCount->get()->count(); // Getting total no of rows

        $totalFiltered = $totalData;
        $limit = intval($this->request->input('length'));
        $start = intval($this->request->input('start'));
        $order = $columns[$this->request->input('order.0.column')];
        $dir = $this->request->input('order.0.dir');
        
        if (empty($this->request->input('search.value')) && empty($order) && empty($dir)) {
            $details = $query->offset($start)->limit(intval($limit))->orderBy('manual_bank_link_restricted_routing_numbers.created_at', 'DESC')->get();
        } else if (empty($this->request->input('search.value'))) {
            $details = $query->offset($start)->limit(intval($limit))->orderBy($order, $dir)->get();
        } else {
            $search = $this->request->input('search.value');
            $search_query = $query->where(function ($q) use ($search) {
                $q->Where('manual_bank_link_restricted_routing_numbers.routing_no', 'LIKE', "%{$search}%");
            });

            $search_query_count = $queryCount->where(function ($q) use ($search) {
                $q->Where('manual_bank_link_restricted_routing_numbers.routing_no', 'LIKE', "%{$search}%");
            });

            $totalFiltered = $search_query_count->get()->count();

            $details = $search_query->offset($start)->limit(intval($limit))->orderBy($order, $dir)->get();
        }
        $data = array();
        if (!empty($details)) {
            // Creating array to show the values in frontend
            foreach ($details as $detail) {
                $nestedData['routing_no'] = $detail->routing_no;
                $nestedData['manual_bank_link_restrictions_id'] = $detail->manual_bank_link_restrictions_id;
                $nestedData['state'] = explode(",", $detail->state);
                $nestedData['state_ids'] = explode(",", $detail->state_ids);
                $nestedData['age'] = $detail->age;
                $nestedData['restriction_year'] = $detail->restriction_year;
                $nestedData['edit'] = $detail->id;
                $nestedData['created_at'] = date('m-d-Y h:i A', strtotime($detail->created_at));
                $data[] = $nestedData;
            }
        }
        // Drawing the Datatable
        $json_data = array(
            "draw" => intval($this->request->input('draw')),
            "recordsTotal" => intval($totalData),
            "recordsFiltered" => intval($totalFiltered),
            "data" => $data,
        );

        Log::info(addslashes(__METHOD__) . "(" . LINE . __LINE__ . ") : Manual bank link restricted routing numbers fetched successfully.");
        echo json_encode($json_data); // Need plain JSON data for drawing the datatable
    }

    /**
     * Fetch all State
     */
    public function getAllState()
    {
        $states = State::all();
        $message = trans('message.all_state_success');
        return renderResponse(SUCCESS, $message, $states); // API Response returned with 200 status
    }

    /**
     * Add/edit restricted routing number
     */
    public function addEditRestrictedRoutingNumber()
    {
        $this->validate($this->request, [
            'restriction_year' => VALIDATION_REQUIRED,
            'routing_number' => VALIDATION_REQUIRED,
            'selectedState' => VALIDATION_REQUIRED,
        ]);
        
        $existsCheck = ManualBankLinkRestrictedRoutingNumber::where(['routing_no' => $this->request->get('routing_number'), 'restriction_year' => $this->request->get('restriction_year')])->first();

        DB::beginTransaction();

        try {
            // Edit time
            if ($this->request->get('edit')) {
                $editRestrictedRoutingNumber = ManualBankLinkRestrictedRoutingNumber::find($this->request->get('edit'));
                if (!$editRestrictedRoutingNumber) {
                    $message = trans('message.manual_bank_restricted_routing_updation_error');
                    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No Manual Bank Link Restricted Routing Numbers found for Id: " . $this->request->get('edit'));
                    return renderResponse(FAIL, $message, NULL);
                }
                if ($existsCheck && $existsCheck->id != $this->request->get('edit')) {
                    $message = trans('message.manual_bank_restricted_routing_exists_error');
                    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . $message);
                    return renderResponse(FAIL, $message, NULL);
                }
                ManualBankLinkRestrictedRoutingNumberStateMap::where('manual_bank_link_restricted_routing_number_id', $this->request->get('edit'))->delete();
                $message = trans('message.manual_bank_restricted_routing_updation_success');
            } else {
                if ($existsCheck) {
                    $message = trans('message.manual_bank_restricted_routing_exists_error');
                    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . $message);
                    return renderResponse(FAIL, $message, NULL);
                } 
                $editRestrictedRoutingNumber = new ManualBankLinkRestrictedRoutingNumber();
                $message = trans('message.manual_bank_restricted_routing_created_success');
            }
            $editRestrictedRoutingNumber->is_routing_no_check_required = 1;
            $editRestrictedRoutingNumber->age = date('Y') - $this->request->get('restriction_year');
            $editRestrictedRoutingNumber->routing_no = $this->request->get('routing_number');
            $editRestrictedRoutingNumber->restriction_year = $this->request->get('restriction_year');
            $editRestrictedRoutingNumber->save();

            if (count($this->request->get('selectedState')) > 0) {
                foreach ($this->request->get('selectedState') as $value) {
                    $stateMap = new ManualBankLinkRestrictedRoutingNumberStateMap();
                    $stateMap->manual_bank_link_restricted_routing_number_id = $editRestrictedRoutingNumber->id;
                    $stateMap->state_id = $value;
                    $stateMap->save();
                }
            }
            DB::commit();
            return renderResponse(SUCCESS, $message, null); // API Response returned with 200 status
        } catch (\Exception $e) {
            $message = trans('message.db_transaction_failed');
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while storing data.", [EXCEPTION => $e]);
            DB::rollback();
            return renderResponse(FAIL, $message, NULL); // API Response returned with 200 status
        }
    }

    /**
     * Status Changed restricted routing number
     */
    public function statusChangeRestrictedRoutingNumber()
    {
        $this->validate($this->request, [
            'edit' => VALIDATION_REQUIRED
        ]);
        
        try {
            $editRestrictedRoutingNumber = ManualBankLinkRestrictedRoutingNumber::find($this->request->get('edit'));
            if (!$editRestrictedRoutingNumber) {
                $message = trans('message.manual_bank_restricted_routing_updation_error');
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No Manual Bank Link Restricted Routing Numbers found for Id: " . $this->request->get('edit'));
                return renderResponse(FAIL, $message, NULL);
            } else {
                // toggle the value
                $editRestrictedRoutingNumber->is_active = $editRestrictedRoutingNumber->is_active == 1 ? 0 : 1;
                $editRestrictedRoutingNumber->save();
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Manual Bank Link Restricted Routing Numbers with ID: " . $this->request->get('edit') . " updating status" );
                $message = $editRestrictedRoutingNumber->is_active == 1 ? trans('message.manual_bank_restricted_routing_status_enabled_success') : trans('message.manual_bank_restricted_routing_status_disabled_success');
                // API Response returned with 200 status
                return renderResponse(SUCCESS, $message, null);
            }
        } catch (\Exception $e) {
            $message = trans('message.db_transaction_failed');
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while Manual Bank Link Restricted Routing Numbers status  update time .", [EXCEPTION => $e]);
            return renderResponse(FAIL, $message, NULL); // API Response returned with 200 status
        }
    }

    /**
     * Checking for Manual Bank Link Restrictions
    */
    public function checkrestrictedroutingnumber()
    {
        $this->validate($this->request, [
            'userID' => VALIDATION_REQUIRED,
            'selecte_bank_account' => VALIDATION_REQUIRED
        ]);

        $user_details = User::where(['user_id' => $this->request->get('userID')])->first();
        if (!$user_details) {
            $message = trans('message.user_not_found');
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Manual Bank Link Restricted Routing Numbers Checking time user not found. User ID: " . $this->request->get('userID'));
            return renderResponse(FAIL, $message, NULL);
        }

        $bank_details = UserBankAccountInfo::find($this->request->get('selecte_bank_account'));
        if (!$bank_details) {
            $message = trans('message.user_not_found');
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Manual Bank Link Restricted Routing Numbers Checking time Bank Account not found. Account ID: " . $this->request->get('selecte_bank_account'));
            return renderResponse(FAIL, $message, NULL);
        }
        $birth_year = date('Y', strtotime($user_details->date_of_birth));

        $checkRoutingNoRestriction = ManualBankLinkRestrictedRoutingNumber::join('manual_bank_link_restricted_routing_number_state_maps', 'manual_bank_link_restricted_routing_number_state_maps.manual_bank_link_restricted_routing_number_id', '=', 'manual_bank_link_restricted_routing_numbers.id')
        ->join('states', 'states.id', '=', 'manual_bank_link_restricted_routing_number_state_maps.state_id')
        ->where(['manual_bank_link_restricted_routing_numbers.routing_no' => $bank_details->routing_no, 'states.code' => $user_details->state, 'manual_bank_link_restricted_routing_numbers.is_active' => 1])->where('manual_bank_link_restricted_routing_numbers.restriction_year', '<=', $birth_year)->first();

        if (!empty($checkRoutingNoRestriction)) {
            $message = trans('message.manual_bank_restricted_routing_check_found');
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . " State, Age and Routing Number is restricted for the consumer. User ID: " . $this->request->get('userID') . " Routing No: " . $bank_details->routing_no . " State: " . $user_details->state . " Restriction Year: " . $birth_year);
            $data = [
                'routing_no' => $bank_details->routing_no,
                'state' => $user_details->state,
                'birth_year' => $birth_year,
            ];
        } else {
            $message = trans('message.manual_bank_restricted_routing_check_not_found');
            $data = null;
        }
        return renderResponse(SUCCESS, $message, $data);
    }

}
