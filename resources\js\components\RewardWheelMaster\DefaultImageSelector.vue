<template>
    <div>
        <div class="default-image-title mb-3">Select Default Image</div>
        <div class="row">
            <div class="col-2 mb-3 position-relative" v-for="(image, i) in allDefaultImages" :key="i">
                <a @click="selectImage(image.image_link)" href="javascript:void(0);">
                    <div :class="selectedimage == image.image_link ? 'default-image-card active' : 'default-image-card'">
                        <img class="w-100" :src="image.image_link" alt="">
                    </div>
                </a>
                <button @click="deleteImageConfirmation(image.id)" type="button" class="default-image-delete">
                    <svg width="50px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M19.207 6.207a1 1 0 0 0-1.414-1.414L12 10.586 6.207 4.793a1 1 0 0 0-1.414 1.414L10.586 12l-5.793 5.793a1 1 0 1 0 1.414 1.414L12 13.414l5.793 5.793a1 1 0 0 0 1.414-1.414L13.414 12l5.793-5.793z" fill="#000000"/></svg>
                </button>
            </div>
        </div>
        <div class="row justify-content-end mx-0">
            <button
            @click="hideModal()"
            class="btn btn-outline-secondary mr-3"
            >
            Cancel
            </button>
            <button
            @click="submit()"
            class="btn btn-success"
            >
            Select
            </button>
        </div>


        <!-- Confirmation modal start -->
        <b-modal ref="confirmation-delete-modal" hide-footer hide-header id="confirmation-delete-modal">
        <div class="color">
          <div class="col-12 text-center">
            <svg fill="#149240" width="100px" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M5.755,20.283,4,8H20L18.245,20.283A2,2,0,0,1,16.265,22H7.735A2,2,0,0,1,5.755,20.283ZM21,4H16V3a1,1,0,0,0-1-1H9A1,1,0,0,0,8,3V4H3A1,1,0,0,0,3,6H21a1,1,0,0,0,0-2Z"/></svg>
          </div>
          <div class="purchaserpower-modal-text">
            <div class="d-block text-center my-3">
              <label class="text-justify text-secondary h4">
                Are you sure want to delete this image?
              </label>
            </div>
            <div class="row">
              <div class="col-12 text-center">
                <button
                  @click="cancelDeleteImageConfirmationModal()"
                  class="btn btn-secondary btn-md center-block mr-2"
                >
                  <label class="forgetpassword-ok-label my-0 cursor-pointer text-white">Cancel</label>
                </button>
                <button
                  @click="deleteImage()"
                  class="btn btn-success btn-md center-block ml-2"
                >
                  <label class="forgetpassword-ok-label my-0 cursor-pointer text-white">Yes</label>
                </button>
              </div>
            </div>
          </div>
        </div>
      </b-modal>
    </div>
</template>

<script>
import api from "@/api/rewardwheel.js";

export default {
    props:{
        value: '',
        modalObj: '',
        defaultImages: []
    },
    data(){
        return{
            selectedimage: '',
            defaultImageID: '',
            allDefaultImages: []
        }
    },
    mounted(){
        if(this.value){
            this.selectedimage = this.value
        }
        this.allDefaultImages = this.defaultImages
    },  
    methods:{
        selectImage(image){
            if(this.selectedimage == image){
                this.selectedimage = ''
            }else{
                this.selectedimage = image
            }
        },
        submit(){
            this.$emit("input", this.selectedimage);
            this.modalObj.hide()
        },
        hideModal(){
            console.log("modal", this.modalObj)
            this.modalObj.hide()
        },
        deleteImageConfirmation(id){
            this.defaultImageID = id
            this.$refs['confirmation-delete-modal'].show()
        },
        cancelDeleteImageConfirmationModal(){
            this.defaultImageID = ''
            this.$refs['confirmation-delete-modal'].hide()
        },
        deleteImage(){
            var self = this;
            api
            .deleteDefaultImage({id: self.defaultImageID})
            .then(response => {
                if (response.code == 200) {
                    this.getAllDefaultImages()
                    this.$refs['confirmation-delete-modal'].hide()
                    success(response.message);
                } else {
                    this.$refs['confirmation-delete-modal'].hide()
                    error(response.message);
                }
            })
            .catch(err => {
                this.$refs['confirmation-delete-modal'].hide()
                error(err.response.data.message);
            });
        },
        getAllDefaultImages(){
            var self = this;
            api
            .getAllDefaultImages()
            .then(response => {
                if (response.code == 200) {
                    self.allDefaultImages = response.data
                } else {
                    error(response.message);
                }
            })
            .catch(err => {
                error(err);
            });
        },
    }
}
</script>

<style>
.default-image-title{
    font-size: 20px;
    font-weight: 500
}
.default-image-card{
    border: 1px solid #c2c2c2;
    padding: 10px;
    border-radius: 6px;
    position: relative;
}
.default-image-card.active{
    border: 2px solid #149240;
}
.default-image-delete{
    border: 0;
    width: 35px;
    height: 35px;
    position: absolute;
    border-radius: 100%;
    top: -13px;
    right: 2px;
    display: flex;
    justify-content: center;
    align-items: center;
}
.default-image-delete svg{
    width: 20px;
}
</style>