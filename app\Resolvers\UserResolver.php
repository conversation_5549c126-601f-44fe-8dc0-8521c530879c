<?php

namespace App\Resolvers;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use App\Models\User;

class UserResolver implements \OwenIt\Auditing\Contracts\UserResolver
{
    /**
     * {@inheritdoc}
     */
    public static function resolve()
    {
        $guards = Config::get('audit.user.guards', [
            'web',
            'api',
        ]);
        foreach ($guards as $guard) {
            if (Auth::guard($guard)->check()) {
                return  Auth::guard($guard)->user();
            }
            else
            {
                return User::find(config('user_id', null));
            }
        }
    }
}
