<?php

namespace App\Console\Commands;

use Aws\Exception\AwsException;
use Aws\SecretsManager\SecretsManagerClient;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class FetchAwsSecrets extends Command
{
    protected $signature = 'secrets:fetch';
    protected $description = 'Fetch secrets from AWS Secrets Manager and write to .env file';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $this->info('Fetching secrets from AWS Secrets Manager...');
        Log::info('Fetching secrets from AWS Secrets Manager...');
        $client = new SecretsManagerClient([
            'version' => 'latest',
            'region' => config('app.aws_region'),
            'credentials' => [
                'key' => config('app.aws_access_id'),
                'secret' => config('app.aws_secret_key'),
            ],
        ]);

        try {
            $result = $client->getSecretValue([
                'SecretId' => config('app.aws_secret_id'),
            ]);

            if (isset($result['SecretString'])) {
                $secrets = json_decode($result['SecretString'], true);
                $this->writeToEnv($secrets);
                $this->info('Secrets have been written to .env file.');
                Log::info('Secrets have been written to .env file.');
            } else {
                $this->error('SecretString is not set in the AWS Secrets Manager response.');
                Log::error('SecretString is not set in the AWS Secrets Manager response.');
            }
        } catch (AwsException $e) {
            $this->error($e->getMessage());
        }
    }

    protected function writeToEnv(array $secrets)
    {
        $envPath = base_path('.env');
        $envContent = file_get_contents($envPath);

        foreach ($secrets as $key => $value) {
            //$key = strtoupper($key);

            // Handle boolean true/false explicitly
            if ($value === true) {
                $value = 'true';
            } elseif ($value === false) {
                $value = 'false';
            } elseif (is_string($value)) {
                // Enclose the value in double quotes if it contains spaces, special characters, or quotes
                $value = '"' . str_replace('"', '\"', $value) . '"';
            }

            $line = "{$key}={$value}";
            if (preg_match("/^{$key}=.*/m", $envContent)) {
                $envContent = preg_replace("/^{$key}=.*/m", $line, $envContent);
            } else {
                $envContent .= PHP_EOL . $line;
            }
        }

        file_put_contents($envPath, $envContent);
    }
}
