<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;


class UserBankAccountOwnerInfoMatchDetail extends Model
{
    /**
     * __construct
     * This function will insert an alphanumeric 32 character unique id as primary key in the table whenever a data get inserted
     * @param  mixed $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        $this->id = generateUUID();

        parent::__construct($attributes);
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_bank_account_owner_info_id',
        'consumer_id',
        'account_id',
        'name_during_regitsration',
        'name_from_banking_solution',
        'address_during_regitsration',
        'address_from_banking_solution',
        'name_match_percentage',
        'address_match_percentage',
    ];
    public $timestamps = true;
    public $incrementing = false;
    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
}
