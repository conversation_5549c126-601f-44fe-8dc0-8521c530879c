#!/bin/bash
echo "======================================="
echo "Setting up PORTAL....."
echo "======================================="
file=./composer.phar

# Get APP_ENV from the .env file using awk
APP_ENV=$(awk -F '=' '/^APP_ENV=/ {print $2}' .env)

# Strip quotes from APP_ENV, if any
APP_ENV=$(echo "$APP_ENV" | sed 's/^"\(.*\)"$/\1/')

currentHash="$(git rev-parse HEAD | cut -c1-7)"
printf "VERSION = V-$currentHash" > .env.rdate

# Install global dependencies
composer install

composer dump-autoload

# Install dependencies
npm install

# Run the build process
npm run prod

# Run migrations
php artisan migrate --force

# Run secrets:fetch if APP_ENV is staging or production
if [ "$APP_ENV" = "production" ] || [ "$APP_ENV" = "staging" ]; then
    php artisan secrets:fetch
fi

# Clear and cache configuration
php artisan config:clear
php artisan config:cache

# Clear and cache routes
php artisan route:clear
php artisan route:cache

EXIT_STATUS=$?
GREEN='\033[0;32m' #Green Color
RED='\033[0;31m' #Red Color
NC='\033[0m' # No Color

if [ "$EXIT_STATUS" -eq "0" ]
then
    echo -e "${GREEN}=======================================${NC}"
    echo -e "${GREEN}PORTAL set up Successfully${NC}"
    echo -e "${GREEN}=======================================${NC}"
else
    echo -e "${RED}=======================================${NC}"
    echo -e "${RED}PORTAL set up TERMINATED${NC}"
    echo -e "${RED}=======================================${NC}"
fi
