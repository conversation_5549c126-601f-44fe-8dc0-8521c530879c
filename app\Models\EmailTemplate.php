<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
class EmailTemplate extends Model
{

    /**
     * __construct
     * This function will insert an alphanumeric 32 character unique id as primary key in the table whenever a data get inserted
     * @param  mixed $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        $this->id = generateUUID();

        parent::__construct($attributes);
    }

    protected $fillable = [
        'template_name',
        'template_subject',
        'template_body',
        'status',
        'return_reason_id'
    ];
    public $timestamps = true;
    public $incrementing = false;

    /**
     * getParamValue
     * This function is used to fetch the Email Template details by its Name
     * @param  mixed $sys_param_name
     * @param  mixed $default
     * @return void
     */
    public static function getParamValue($sys_param_name, $default = null)
    {
        $result = static::where(['template_name' => $sys_param_name, 'status' => TEMPLATE_ACTIVE])->first();
        if (empty($result)) {
            return $default;
        }
        return $result;
    }
}
