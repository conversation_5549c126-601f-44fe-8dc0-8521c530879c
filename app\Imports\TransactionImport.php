<?php

namespace App\Imports;

use App\Models\ConsumerCardMap;
use App\Models\MerchantStores;
use App\Models\TerminalMaster;
use App\Models\TransactionDetails;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class TransactionImport implements ToModel, WithHeadingRow, WithBatchInserts, WithChunkReading
{
    use Importable;
    private $rows = 0;
    private $duplicate_rows = 0;
    private $updated_rows = 0;

    /**
     * @param array $row
     * This function actully imports the data as row from Excel Sheet. Here we used the WithHeadingRow to get the Data with Heading. Do Not try to get the rows with index.
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row)
    {
        $card_number = $row['cardnumber1'];
        if (!empty($card_number) && strcmp($row['transactionnumber1'], "") != 0 && strcmp($row['authcode1'], "") != 0) {
            try {
                // Fetch the consumer details via card number
                $consumer_card_details = ConsumerCardMap::whereRaw('card_number ="' . $card_number . '"')->first();
                // Check if it a duplicate transaction
                $checkDuplicateTransaction = TransactionDetails::join('status_master', 'transaction_details.status_id', '=', 'status_master.id')->where(['transaction_details.zipline_trans_id' => $row['transactionnumber1'], 'transaction_details.authcode' => $row['authcode1'], 'status_master.code' => SUCCESS])->where('transaction_details.consumer_id', '!=', '')->first();
                if (!empty($consumer_card_details)) { // If the consumer exists then proceed for insertion
                    if (empty($checkDuplicateTransaction)) { // If the transaction is not duplicate then proceed for insertion
                        // Formatting the excel/csv time
                        if (is_numeric($row['transactiondate1']) == 1) { // Checking is the sheet is excel
                            $UNIX_DATE = ($row['transactiondate1'] - 25569) * 86400;
                            $transaction_time = gmdate("Y-m-d H:i:s", $UNIX_DATE);
                        } else { // It is CSV
                            $transaction_time = date("Y-m-d H:i:s", strtotime($row['transactiondate1']));
                        }

                        // Store and Terminal mapping checking started and if not exists then create the map
                        $store = $row['storename1'];
                        $terminal = $row['lanenumber11'];
                        $terminal_id = null;
                        // Check if store exists or not
                        $checkStoreExists = MerchantStores::where('retailer', $store)->leftJoin('timezone_masters', 'timezone_masters.id', '=', 'merchant_stores.timezone_id')->select('merchant_stores.*', 'timezone_masters.timezone_name')->first();
                        if (!empty($checkStoreExists)) {
                            // Check if timezone exists for store
                            if ($checkStoreExists->timezone_name) {
                                // Check if terminal exists under this store
                                $checkterminalExists = TerminalMaster::where(['terminal_name' => $terminal, 'merchant_store_id' => $checkStoreExists->id])->first();
                                if (!empty($checkterminalExists)) {
                                    $terminal_id = $checkterminalExists->id;
                                } else {
                                    $terminal_details = new TerminalMaster();
                                    $terminal_details->merchant_store_id = $checkStoreExists->id;
                                    $terminal_details->terminal_name = $terminal;
                                    $terminal_details->transaction_type_id = getStoretype($terminal); // Get the Transaction Type for the terminal
                                    $terminal_details->unique_identification_id = generateUUID();
                                    $terminal_details->save();

                                    $terminal_id = $terminal_details->id;
                                }

                                // Check if the transaction already exists
                                $checkTransactionExists = TransactionDetails::where(['authcode' => $row['authcode1'], 'amount' => floatval(preg_replace("/[^0-9.]/", '', $row['textbox549'])), 'terminal_id' => $terminal_id, 'is_v1' => 1])->first();
                                if (!empty($checkTransactionExists)) {
                                    // Update the transaction
                                    $action = 'updated';
                                    $transaction_details = TransactionDetails::find($checkTransactionExists->id);
                                    $transaction_details->consumer_id = $consumer_card_details->user_id;
                                    $transaction_details->status_id = getStatus(SUCCESS);
                                    $transaction_details->transaction_imported_by = Auth::user()->user_id;
                                    $transaction_details->save();
                                    ++$this->updated_rows;
                                } else {
                                    $action = 'added';
                                    // Insertion started in Transaction Details Table
                                    $transaction_details = new TransactionDetails();
                                    $transaction_details->transaction_number = generateTransactionId();
                                    $transaction_details->consumer_id = $consumer_card_details->user_id;
                                    $transaction_details->terminal_id = $terminal_id;
                                    $local_time = Carbon::createFromFormat('Y-m-d H:i:s', $transaction_time, $checkStoreExists->timezone_name); // Parsing time in local timezone
                                    $utc_time = $local_time->setTimezone('UTC'); // Converting local time to UTC
                                    $transaction_details->transaction_time = $utc_time;
                                    $transaction_details->local_transaction_time = $transaction_time;
                                    $transaction_details->local_transaction_year = date("Y", strtotime($transaction_details->local_transaction_time));
                                    $transaction_details->local_transaction_month = date("m", strtotime($transaction_details->local_transaction_time));
                                    $transaction_details->local_transaction_date = date("Y-m-d", strtotime($transaction_details->local_transaction_time));
                                    $transaction_details->timezone_id = $checkStoreExists->timezone_id;
                                    $transaction_details->zipline_trans_id = $row['transactionnumber1'];
                                    $transaction_details->authcode = $row['authcode1'];
                                    $transaction_details->amount = floatval(preg_replace("/[^0-9.]/", '', $row['textbox549']));
                                    $transaction_details->tip_amount = floatval(preg_replace("/[^0-9.]/", '', $row['tipamount3']));
                                    $transaction_details->tip_add_time = $row['tipamount3'] != '' ? $transaction_time : null;
                                    $transaction_details->status_id = getStatus(SUCCESS);
                                    $transaction_details->transaction_imported_by = Auth::user()->user_id;
                                    $transaction_details->is_v1 = 1;
                                    $transaction_details->save();
                                    ++$this->rows;
                                }
                                Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction " . $action . " in Transaction Details Table with Transaction ID : " . $transaction_details->id . " for Consumer ID: " . $consumer_card_details->user_id);
                            } else {
                                Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction insertion skipped due to non availability of Timezone in Store named : " . $store . " for Transaction Number: " . $row['transactionnumber1'] . " and Authcode: " . $row['authcode1']);
                                insertSkippedDataLog('Transaction', 'transactionnumber1', $row['transactionnumber1'], "Transaction insertion skipped due to non availability of Timezone in Store named : " . $store . " for Transaction Number: " . $row['transactionnumber1'] . " and Authcode: " . $row['authcode1'], json_encode($row), 'Canpay Transaction Summary');
                            }
                        } else {
                            Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction insertion skipped due to non availability of store named : " . $store . " for Transaction Number: " . $row['transactionnumber1'] . " and Authcode: " . $row['authcode1']);
                            insertSkippedDataLog('Transaction', 'transactionnumber1', $row['transactionnumber1'], "Transaction insertion skipped due to non availability of store named : " . $store . " for Transaction Number: " . $row['transactionnumber1'] . " and Authcode: " . $row['authcode1'], json_encode($row), 'Canpay Transaction Summary');
                        }
                    } else {
                        ++$this->duplicate_rows;
                        Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction insertion skipped due to duplicate entry found in Transaction Details Table for Transaction Number: " . $row['transactionnumber1'] . " and Authcode: " . $row['authcode1']);
                        insertSkippedDataLog('Transaction', 'transactionnumber1', $row['transactionnumber1'], "Transaction insertion skipped due to duplicate entry found in Transaction Details Table for Transaction Number: " . $row['transactionnumber1'] . " and Authcode: " . $row['authcode1'], json_encode($row), 'Canpay Transaction Summary');
                    }

                } else { // Insertion skipped due to non availability of consumer or due to duplicate records
                    Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction insertion skipped due to non availability of consumer in Transaction Details Table for Consumer Card Number: " . $row['cardnumber1']);
                    insertSkippedDataLog('Transaction', 'transactionnumber1', $row['transactionnumber1'], "Transaction insertion skipped due to non availability of consumer in Transaction Details Table for Consumer Card Number: " . $row['cardnumber1'], json_encode($row), 'Canpay Transaction Summary');
                }
            } catch (\Exception $e) {
                Log::channel('datamigration')->error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured during insertion in Transaction Details Table for Consumer Card Number: " . $row['cardnumber1'] . ".", [EXCEPTION => $e]);
                insertSkippedDataLog('Transaction', 'transactionnumber1', $row['transactionnumber1'], $e, json_encode($row), 'Canpay Transaction Summary');
            }
        } else {
            Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction insertion skipped due to non availability of Card Number or Transaction ID or Authcode in Excel Sheet.");
            insertSkippedDataLog('Transaction', 'transactionnumber1', '', 'Transaction insertion skipped due to non availability of Card Number or Transaction ID or Authcode in Excel Sheet.', json_encode($row), 'Canpay Transaction Summary');
        }
    }

    public function getRowCount()
    {
        return $this->rows . '|' . $this->duplicate_rows . '|' . $this->updated_rows;
    }

    public function batchSize(): int
    {
        return 1000;
    }

    public function chunkSize(): int
    {
        return 5000;
    }
}
