<?php
namespace App\Http\Clients;

use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;

/**
 *
 * @package App\Http\Clients
 */
class Acheck21HttpClient
{
    /**
     * @var Client
     */
    private $client;
    /**
     * Acheck21HttpClient constructor.
     */
    public function __construct()
    {
        $this->client = new Client(['base_uri' => config('app.acheck_base_url')]);
    }

    /**
     * Acheck 21 transaction creation (https://acheck21.readme.io/docs/creating-a-transaction-1)
     */
    public function createTransaction($params)
    {
        // Acheck account ID for Submerchant needs to pass through URL. Hence replacing 'default' by 'acheck_account_id'
        $acheck_account_id = $params['acheck_account_id'] != '' ? $params['acheck_account_id'] : 'default';
        $params['api'] = '/GlobalGateway/api/v1/checks/' . $acheck_account_id;
        $params['headers'] = [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ];
        try {
            $response = $this->client->put($params['api'], [
                'headers' => $params['headers'],
                'auth' => [config('app.acheck_username'), config('app.acheck_password')],
                'body' => $params['body'],
            ]);
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck21 create transaction with API URL " . $params['api'] . " and body " . $params['body'] . " returned response : " . $response->getBody());
            return $response->getBody();
        } catch (\GuzzleHttp\Exception\ServerException $ex) {
            Log::error(__METHOD__ . "(Line: " . __LINE__ . ") - " . "Exception while sending request to Acheck21.", ['Exception' => $ex]);
            return $ex->getResponse()->getBody()->getContents();
        }
    }


    /**
     * Acheck 21 Delete Transaction
     */
    public function deleteTransaction($params)
    {
        if ($params['acheck_account_id'] != '') {
            $params['api'] = '/GlobalGateway/api/v1/checks/' . $params['acheck_account_id'] . '/' . $params['acheck_document_id'];
        } else {
            $params['api'] = '/GlobalGateway/api/v1/checks/' . $params['acheck_document_id'];
        }
        $params['headers'] = [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ];
        try {
            $response = $this->client->delete($params['api'], [
                'headers' => $params['headers'],
                'auth' => [config('app.acheck_username'), config('app.acheck_password')],
            ]);
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck21 delete transaction returned response : " . $response->getStatusCode());
            return $response->getStatusCode();
        } catch (\GuzzleHttp\Exception\GuzzleException $ex) {
            Log::error(__METHOD__ . "(Line: " . __LINE__ . ") - " . "Exception while sending request to Acheck21.", ['Exception' => $ex]);
            return null;
        }
    }

    public function createMockTransaction($params)
    {
        $params['api'] = 'https://q443euvdi4.execute-api.us-east-2.amazonaws.com/v1/acheck';
        $params['headers'] = [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ];
        try {
            $response = $this->client->post($params['api'], [
                'headers' => $params['headers'],
                'body' => $params['body'],
            ]);
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck21 create transaction returned response : " . $response->getBody());
            return $response->getBody();
        } catch (\GuzzleHttp\Exception\ServerException $ex) {
            Log::error(__METHOD__ . "(Line: " . __LINE__ . ") - " . "Exception while sending request to Acheck21.", ['Exception' => $ex]);
            return $ex->getResponse()->getBody()->getContents();
        }
    }

}
