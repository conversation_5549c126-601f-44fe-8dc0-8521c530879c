<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
class Acheck21HistoryTable extends Model
{
    protected $table = 'acheck21_history_table';

    /**
     * __construct
     * This function will insert an alphanumeric 32 character unique id as primary key in the table whenever a data get inserted
     * @param  mixed $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        $this->id = generateUUID();

        parent::__construct($attributes);
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'transaction_id',
        'transaction_posting',
        'void_transaction_posting',
        'status_id',
    ];

    public $incrementing = false;
    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
}
