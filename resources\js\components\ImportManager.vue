<template>
<div>
<div v-if="loading">
  <CanPayLoader/>
</div>
  <div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <section class="content">
      <div class="container-fluid">
        <div class="row">
          <div class="col-md-12">
            <div class="card card-success">
              <div class="card-header">
                <h3 class="card-title">Import Merchants</h3>
                <b-button
                  class="btn-danger export-api-btn"
                  @click="reloadDatatable"
                  v-if="showReloadBtn"
                  >
                    <i class="fas fa-redo"></i> Reload
                  </b-button>
              </div>
              <div class="card-body">
                <div class="form-group">
                  <label for="exampleInputFile">Upload Merchant Excel </label>
                  <button
                    type="button"
                    class="btn btn-danger ml-10"
                    style="float:right; margin-top:-14px;"
                    @click="downloadSampleFile();"
                  >
                    Download Sample <i
                      class="fa fa-download ml-10"
                      aria-hidden="true"
                    ></i>
                  </button>
                  <div class="input-group">
                    <div class="custom-file">
                      <input
                        type="file"
                        ref="file"
                        id="exampleInputFile"
                        v-on:change="handleFileUpload()"
                        class="custom-file-input"
                        accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
                      />
                      <label for="exampleInputFile" class="custom-file-label">{{
                        fileName
                      }}</label>
                    </div>
                    <div
                      class="input-group-append"
                      style="margin-left: 15px; margin-top: -2px"
                    >
                      <span class="btn btn-success" @click="importExcel"
                        >Import</span
                      >
                    </div>
                  </div>
                </div>
                <!-- /.card-header -->
                <div class="card-body">
                  <table
                    id="regionalManagerTable"
                    class="table"
                    style="width: 100%; white-space: normal"
                  >
                    <thead>
                      <tr>
                        <th>Status</th>
                        <th>Data Imported</th>
                        <th>Imported By</th>
                        <th>Imported On</th>
                      </tr>
                    </thead>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</div>
</template>
<script>
import api from "@/api/user.js";
import store_api from "@/api/stores.js";
import commonConstants from "@/common/constant.js";
import CanPayLoader from "./CustomLoader/CanPayLoader.vue"
export default {
  components:{
    CanPayLoader
  },
  data() {
    return {
      count: 0,
      excel: "",
      fileName: "Choose File",
      importFlag:0,
      showReloadBtn:false,
      loading:false,
      commaSeparatedIds: '',
    };
  },
  methods: {
    reloadDatatable(){
      var self = this;
      self.loadDT();
    },
    /*Handles a change on the file upload*/
    handleFileUpload() {
      this.file = this.$refs.file.files[0];
      this.fileName = this.$refs.file.files[0].name;
    },

    /*Submits the file to the server*/
    importExcel() {
      if(this.importFlag == 0){
        this.importFlag = 1;
        /*Initialize the form data*/
        let formData = new FormData();
        formData.append("excel", this.file);

        /*call to the import excel api */
        this.loading = true;
        api
          .importExcel(formData)
          .then((response) => {
            if (response.code == 200) {
              this.$refs.file.value = null;
              this.file = "";
              this.fileName = "Choose File";
              this.importFlag = 0;
              this.commaSeparatedIds = response.data;
              success(response.message);
              this.loading = false;
              this.loadDT();
              if (this.commaSeparatedIds != '') {
                this.updateStoreTimings();
              }
              //call to fetch the log api
            } else {
              this.importFlag = 0;
              error(response.message);
              this.loading = false;
            }
              this.loading = false;
          })
          .catch((err) => {
            this.importFlag = 0;
            error('Unable to Upload Merchant. There is a problem with the file.');
            this.loading = false;
          });
      }

    },
    loadDT: function () {
      var self = this;
      $("#regionalManagerTable").DataTable({
        searching: false,
        pagingType: "simple_numbers",
        processing: true,
        serverSide: true,
        destroy: true,
        columnDefs: [{ className: "dt-left", targets: [0, 1, 2, 3] }],
        order: [[3, "desc"]],
        orderClasses: false,
        language: {
          processing:
            '<i class="fa fa-spinner fa-spin fa-3x fa-fw"></i><span class="sr-only">Loading...</span> ',
          emptyTable: "No Imported Excel Available.",
          search: "_INPUT_",
          searchPlaceholder: "Search records",
          oPaginate: {
            sNext: '<i class="fas fa-angle-double-right"></i>',
            sPrevious: '<i class="fas fa-angle-double-left"></i>',
          },
          sLengthMenu:
            "<label class='label_dropdown_dt'>Per page</label> _MENU_",
        },
        ajax: {
          headers: {
            Authorization: "Bearer " + localStorage.getItem("token"),
          },
          url: "/api/import/merchantDetails",
          type: "GET",
          data: { excel_type: 0 },
          dataType: "json",
          dataSrc: function (result) {
            self.showReloadBtn = false;
            return result.data;
          },
          error: function(data){
            error(commonConstants.datatable_error);
            $('#regionalManagerTable_processing').hide();
            self.showReloadBtn = true;
          }
        },
        columns: [{ data: "summary" }, { data: "data_imported" }, { data: "added_by" }, { data: "created_at" }],
      });

      $("#regionalManagerTable").on("page.dt", function () {
        $("html, body").animate({ scrollTop: 0 }, "slow");
        $("th:first-child").focus();
      });

      //Search in the table only after 3 characters are typed
      // Call datatables, and return the API to the variable for use in our code
      // Binds datatables to all elements with a class of datatable
      var dtable = $("#regionalManagerTable").dataTable().api();

      // Grab the datatables input box and alter how it is bound to events
      $(".dataTables_filter input")
      .unbind() // Unbind previous default bindings
      .bind("input", function(e) { // Bind our desired behavior
          // If the length is 3 or more characters, or the user pressed ENTER, search
          if(this.value.length >= 3 || e.keyCode == 13) {
              // Call the API search function
              dtable.search(this.value).draw();
          }
          // Ensure we clear the search if they backspace far enough
          if(this.value == "") {
              dtable.search("").draw();
          }
          return;
      });
    },
    downloadSampleFile(){
      window.location.href = "sample_import_excel/Merchant_Import.xlsx";
    },
    updateStoreTimings() {
        var self = this;
        var request = {
            store_ids: self.commaSeparatedIds
        }

        store_api.updateStoreTiming(request)
        .then(response => {
            if (response.code == 200) {
                self.loading = false;
            } else {
                error(response.message);
                self.loading = false;
            }
        })
        .catch(err => {
            error(err.response.data.message);
            self.loading = false;
        });
    }
  },
  mounted() {
    var self = this;
    self.loading = true;
    setTimeout(function () {
      self.loading = false;
      self.loadDT();
    }, 1000);
    document.title = "CanPay - Import Merchants";
  },
};
</script>
