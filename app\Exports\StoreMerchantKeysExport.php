<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Events\AfterSheet;
use Illuminate\Support\Facades\Log;

class StoreMerchantKeysExport implements FromCollection, WithHeadings, ShouldAutoSize, WithEvents
{
    protected $data;

    public function __construct($data)
    {
        $this->data = $data; // Declaring the request variable
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        $collection_array = $this->data; // Storing the array received from request
        return collect([
            $collection_array,
        ]);
    }

    public function headings(): array
    {
        $returnArray = array(
            // 1st header
            [
                'Merchant Store ApiKeys',
            ],
            [
                'Store ID',
                'Retailer',
                'Integrator ID',
                'CanPay Internal Version',
                'APP Key',
                'API Secret',
                'Created At',
                'Status',
            ],
        );

        return $returnArray;
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event){
                $event->sheet->getStyle('A1:E1')->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);

                //Apply Center Alignment
                $event->sheet->getStyle('A:E')->getAlignment()->applyFromArray(
                    array('horizontal' => 'center')
                );
            },
        ];
    }
}
