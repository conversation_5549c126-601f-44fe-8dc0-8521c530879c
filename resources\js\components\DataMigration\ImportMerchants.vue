<template>
<div>
  <div v-if="is_visible == 1">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Import Merchant Details</h3>
                  <b-button
                  class="btn-danger export-api-btn"
                  @click="reloadDatatable"
                  v-if="showReloadBtn"
                  >
                    <i class="fas fa-redo"></i> Reload
                  </b-button>
                </div>
                <!-- /.card-header -->
                <div class="card-body">

                  <div
                    class="alert alert-success alert-dismissible"
                    v-if="success_message != null"
                  >
                    <a
                      href="#"
                      class="close"
                      data-dismiss="alert"
                      aria-label="close"
                      style="text-decoration: none"
                      @click="success_message = null"
                      >&times;</a
                    >
                    <strong>Success!</strong> {{ success_message }}
                  </div>
                  <div class="form-group">
                    <label for="exampleInputFile"
                      >Upload Store List By Merchant Group</label
                    >
                    <button
                      type="button"
                      class="btn btn-danger ml-10"
                      style="float:right; margin-top:-12px;"
                      @click="downloadSampleFile('store_list_by_merchant_group');"
                    >
                      Download Sample <i
                        class="fa fa-download ml-10"
                        aria-hidden="true"
                      ></i>
                    </button>
                    <div class="input-group">
                      <div class="custom-file">
                        <input
                          type="file"
                          ref="store_list_by_merchant_group_file"
                          id="exampleInputFile"
                          v-on:change="handleFileUpload('store_list_by_merchant_group')"
                          class="custom-file-input"
                          accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
                        />
                        <label
                          for="exampleInputFile"
                          class="custom-file-label"
                          >{{ store_list_by_merchant_group_label }}</label
                        >
                      </div>
                    </div>
                  </div>
                  <div class="form-group">
                    <label for="exampleInputFile"
                      >Upload CanPay Transaction Fees</label
                    >
                    <button
                      type="button"
                      class="btn btn-danger ml-10"
                      style="float:right; margin-top:-12px;"
                      @click="downloadSampleFile('canpay_transaction_fees');"
                    >
                      Download Sample <i
                        class="fa fa-download ml-10"
                        aria-hidden="true"
                      ></i>
                    </button>
                    <div class="input-group">
                      <div class="custom-file">
                        <input
                          type="file"
                          ref="canpay_transaction_fees_file"
                          id="exampleInputFile"
                          v-on:change="handleFileUpload('canpay_transaction_fees')"
                          class="custom-file-input"
                          accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
                        />
                        <label
                          for="exampleInputFile"
                          class="custom-file-label"
                          >{{ canpay_transaction_fees_label }}</label
                        >
                      </div>
                    </div>
                  </div>
                  <span
                    style="float: right"
                    class="btn btn-success"
                    @click="migrate"
                    >Migrate To V2</span
                  >
                </div>
                <hr />
                <div class="card-body">
                  <h5>Migration History</h5>
                  <table
                    id="merchantMigrationTable"
                    class="table"
                    style="width: 100%; white-space: normal"
                  >
                    <thead>
                      <tr>
                        <th>Status</th>
                        <th>Data Imported</th>
                        <th>Duplicate Data Imported</th>
                        <th>Migrated Data Updated</th>
                        <th>Uploaded By</th>
                        <th>Migrated On</th>
                      </tr>
                    </thead>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
</div>
</div>
</template>
<script>
import api from "@/api/import.js";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "../CustomLoader/CanPayLoader.vue"
import commonConstants from "@/common/constant.js";
export default {
  components: {
    HourGlass,
    CanPayLoader
  },
  data() {
    return {
      store_list_by_merchant_group: null,
      canpay_transaction_fees: null,
      store_list_by_merchant_group_label: "Choose File",
      canpay_transaction_fees_label: "Choose File",
      success_message: null,
      is_visible: 0,
      showReloadBtn:false,
      constants: commonConstants,
    };
  },
  methods: {
    reloadDatatable(){
      var self = this;
      self.loadDT();
    },
    handleFileUpload(sheet_name) {
      if (sheet_name === "store_list_by_merchant_group") {
        this.store_list_by_merchant_group = this.$refs.store_list_by_merchant_group_file.files[0];
        this.store_list_by_merchant_group_label = this.$refs.store_list_by_merchant_group_file.files[0].name;
      } else {
        this.canpay_transaction_fees = this.$refs.canpay_transaction_fees_file.files[0];
        this.canpay_transaction_fees_label = this.$refs.canpay_transaction_fees_file.files[0].name;
      }
    },
    /*Submits the file to the server*/
    migrate() {
      var self = this;
      self.is_visible = 1;
      if (
        self.store_list_by_merchant_group == null ||
        self.canpay_transaction_fees == null
      ) {
        self.is_visible = 0;
        error("Please upload both the data sheet to migrate.");
        return false;
      }
      /*Initialize the form data*/
      let formData = new FormData();
      formData.append(
        "store_list_by_merchant_group",
        self.store_list_by_merchant_group
      );
      formData.append("canpay_transaction_fees", self.canpay_transaction_fees);
      /*call to the import excel api */
      api
        .migrateMerchants(formData)
        .then((response) => {
          if (response.code == 200) {
            self.$refs.store_list_by_merchant_group_file.value = null;
            self.$refs.canpay_transaction_fees_file.value = null;
            self.store_list_by_merchant_group = null;
            self.canpay_transaction_fees = null;
            self.store_list_by_merchant_group_label = "Choose File";
            self.canpay_transaction_fees_label = "Choose File";
            self.success_message = response.message;
            self.is_visible = 0;
            self.loadDT();
          } else {
            error(response.message);
            self.is_visible = 0;
          }
        })
        .catch((response) => {
          error(response);
          self.is_visible = 0;
        });
    },
    loadDT: function () {
      var self = this;
      $("#merchantMigrationTable").DataTable({
        searching: false,
        pagingType: "simple_numbers",
        processing: true,
        serverSide: true,
        destroy: true,
        columnDefs: [
          { orderable: false, targets: [0] },
          { className: "dt-left", targets: [0, 1, 2, 3, 4, 5] },
        ],
        order: [[5, "desc"]],
        orderClasses: false,
        bLengthChange: false,
        bPaginate: false,
        language: {
          processing:
            '<i class="fa fa-spinner fa-spin fa-3x fa-fw"></i><span class="sr-only">Loading...</span> ',
          emptyTable: "No Migration Log Available.",
          search: "_INPUT_",
          searchPlaceholder: "Search records",
          oPaginate: {
            sNext: '<i class="fas fa-angle-double-right"></i>',
            sPrevious: '<i class="fas fa-angle-double-left"></i>',
          },
          sLengthMenu:
            "<label class='label_dropdown_dt'>Per page</label> _MENU_",
        },
        ajax: {
          headers: {
            Authorization: "Bearer " + localStorage.getItem("token"),
          },
          url: "/api/getmigrationlog",
          type: "POST",
          data: { _token: "{{csrf_token()}}", type: "Merchant" },
          dataType: "json",
          dataSrc: function (result) {
            self.showReloadBtn = false;
            return result.data;
          },
          error: function(data){
            error(commonConstants.datatable_error);
            $('#merchantMigrationTable_processing').hide();
            self.showReloadBtn = true;
          }
        },
        columns: [
          { data: "summary" },
          { data: "actual_data_imported" },
          { data: "duplicate_date_imported" },
          { data: "migrated_data_updated" },
          { data: "uploaded_by" },
          { data: "created_at" },
        ],
      });

      $("#merchantMigrationTable").on("page.dt", function () {
        $("html, body").animate({ scrollTop: 0 }, "slow");
        $("th:first-child").focus();
      });

      //Search in the table only after 3 characters are typed
      // Call datatables, and return the API to the variable for use in our code
      // Binds datatables to all elements with a class of datatable
      var dtable = $("#merchantMigrationTable").dataTable().api();

      // Grab the datatables input box and alter how it is bound to events
      $(".dataTables_filter input")
      .unbind() // Unbind previous default bindings
      .bind("input", function(e) { // Bind our desired behavior
          // If the length is 3 or more characters, or the user pressed ENTER, search
          if(this.value.length >= 3 || e.keyCode == 13) {
              // Call the API search function
              dtable.search(this.value).draw();
          }
          // Ensure we clear the search if they backspace far enough
          if(this.value == "") {
              dtable.search("").draw();
          }
          return;
      });
    },
    downloadSampleFile($file_type){
      if($file_type == 'store_list_by_merchant_group'){
        window.location.href = "sample_import_excel/Store_List_Merchant_Group.csv";
      }else{
        window.location.href = "sample_import_excel/CanPay_Transaction_Fees.csv";
      }
    }
  },
  mounted() {
    var self = this;
    setTimeout(function () {
      self.loadDT();
    }, 1000);
    document.title = "CanPay - Import Merchants";
  },
};
</script>
