const commonConstants = {
    test_constatnt: 'This is a Test Constant',
    role_helpdesk: 'Help Desk',
    role_super_admin: 'Super Admin',
    role_report_admin: 'Report Admin',
    role_admin: 'Admin',
    datatable_error: 'Try reloading data, if problem persists please contact administrator.',
    disable_weekly_spending_limit_help_text: 'This checkbox controls the activation or deactivation of the weekly spending limit. When checked, the weekly limit will not affect purchases. When unchecked, the amount specified in the weekly spending limit box will be enforced, preventing consumers from exceeding the weekly limit.',
    effective_purchase_power_help_text: 'Purchase power is computed by taking the lesser value between the weekly limit or bank balance, then deducting pending transactions. This figure is displayed in the Consumer App.',
    purchase_power_help_text: "This purchase power is calculated exclusively from the consumer's bank balance and is provided for informational purposes only. It does not appear in the consumer app.",
    role_admin_small: 'admin',
    retry_count: 5,
    lite_consumer: 'lite'
};

export default commonConstants;