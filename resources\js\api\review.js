/**
 * Manual identity validation review apis
 */
const reviewDone = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/reviewDone', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const getScores = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/getIdentityValidationDetails', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const getConsumerIDImage = (request) => {
    var instance = axios.create({
        baseURL: process.env.MIX_API_URL,
    });
    return new Promise((res, rej) => {
        instance.post('/getConsumerIDImage', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
/**
 * Global Radar Review APIs
 */
const getGlobalRadarReviewList = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/getglobalradarreviewlist', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const updateReviewStatus = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/updateglobalradarreviewstatus', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const getDetailsV1 = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/getIdentityValidationDetailsForV1', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const updateReviewStatusForV1 = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/updateReviewStatusForV1', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const getConsumerDocumentDetails = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/getconsumerdocumentdetails', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const updateConsumerUploadDocumentStatus = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/updateconsumeruploaddocumentstatus', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getAllManualReviewDetails = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/getAllManualReviewDetails', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getAllConsumerUploadDocument = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/getallconsumeruploaddocument', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getAllManualReviewDetailsForV1 = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/getAllv1ManualReviewDetails', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getAllusersForGlobalRadar = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/getglobalradarreviewlist', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getassessment = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/getassessment', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getconsumercognitodetail = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/getconsumercognitodetail', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
}
export default {
    reviewDone,
    getConsumerIDImage,
    getScores,
    getGlobalRadarReviewList,
    updateReviewStatus,
    getDetailsV1,
    updateReviewStatusForV1,
    getConsumerDocumentDetails,
    updateConsumerUploadDocumentStatus,
    getAllManualReviewDetails,
    getAllConsumerUploadDocument,
    getAllManualReviewDetailsForV1,
    getAllusersForGlobalRadar,
    getassessment,
    getconsumercognitodetail
};