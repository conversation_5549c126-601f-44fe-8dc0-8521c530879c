<?php

// api.php

use Illuminate\Http\Request;

Route::middleware('auth:api')->get('/user', function (Request $request) {
    return $request->user();
});
/**
 * Login routes
 */
Route::post('/login', 'Auth\LoginController@login');
Route::post('/forgotpassword', 'Auth\LoginController@forgotPassword');
Route::get('/testawssecretmanager', 'DevController@testAwsSecretManager');

Route::get('/config/consumer-app-url', function () {
    $url = config('app.consumer_app_url');

    if (!$url) {
        return response()->json(['error' => 'CONSUMER_APP_URL not found'], 500);
    }

    return response()->json(['url' => $url]);
});
/**
 * Passing all the APIs through JWT middleware
 */
Route::group(['middleware' => ['jwt.verify']], function () {
    Route::post('/update/user', 'UserController@updateUser')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . ',' . REPORT_ADMIN . '');
    Route::post('/addcorporateparent', 'UserController@addCorporateParent')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/editcorporateparent', 'UserController@editCorporateParent')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/getmerchantstores', 'UserController@getMerchantStores')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/searchcorporateparents', 'UserController@getAllCorporateParents')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/statuschange', 'UserController@statusChange')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/import/merchantExcel', 'ImportController@importMerchantExcel')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::get('/import/merchantDetails', 'ImportController@getMerchantImportExcelLog')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/import/corporateLogo', 'ImportController@importCorporateLogo')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/import/integratorExcel', 'ImportController@importIntegratorExcel')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/userrolelist', 'UserController@getAdminUserRoles')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/adminuserlist', 'UserController@getAllAdminUsers')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/adduser', 'UserController@addUser')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/edituser', 'UserController@editUser')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/getallstores', 'StoreController@getAllStores')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/toggleEcommerceAdminDriven', 'StoreController@toggleEcommerceAdminDriven')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/getallemailtemplates', 'EmailTemplateController@getAllEmailTemplates')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/gettemplatedetails', 'EmailTemplateController@getTemplateDetails')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/updateemailtemplate', 'EmailTemplateController@updateEmailTemplate')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/sendmail', 'EmailTemplateController@sendMail')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/getallnotifications', 'NotificationController@getAllNotifications')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/addnotification', 'NotificationController@addNotification')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/sendnotification', 'NotificationController@sendNotification')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/changePassword', 'UserController@changePassword')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . ',' . REPORT_ADMIN . '');
    Route::post('/getallaudits', 'AuditController@getAllAudits')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/getAllManualReviewDetails', 'ManualReviewController@getAllManualReviewDetails')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/reviewDone', 'ManualReviewController@updateReviewStatus')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/getIdentityValidationDetails', 'ManualReviewController@getIdentityValidationDetails')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/cancelTransaction', 'TransactionController@cancelTransaction')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/revokeVoidTransaction', 'TransactionController@revokeVoidTransaction')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/getAllRoutingNumbers', 'BlacklistController@getAllRoutingNumbers')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/addRoutingNumber', 'BlacklistController@addRoutingNumber')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::get('/getUserStatus', 'UserController@getAllUserStatus')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/import/consumers', 'DataMigrationController@importConsumersFromExcel')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/import/merchants', 'DataMigrationController@importMerchantsFromExcel')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/import/transactions', 'DataMigrationController@importTransactionsFromExcel')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/import/voidedtransactions', 'DataMigrationController@importVoidedTransactionsFromExcel')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/getmigrationlog', 'DataMigrationController@getMigrationLog')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/getAllMerchants', 'StoreController@getAllMerchants')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/updateMerchant', 'StoreController@updateMerchant')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/getglobalradarreviewlist', 'GlobalRadarController@getAllusersForGlobalRadar')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/updateglobalradarreviewstatus', 'GlobalRadarController@updateGlobalRadarReviewStatus')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/getallapikeys', 'StoreController@getAllApiKeys')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/export/storeapikeys', 'StoreController@getStoreApiKeysExport')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/getAllConsumers', 'UserController@getAllConsumers')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/getconsumertransactionreport', 'TransactionController@getConsumerTransactionReport')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/getmonthlysalesgrowth', 'TransactionController@getMonthlySalesGrowth')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/export/getmonthlysalesgrowthexport', 'TransactionController@getMonthlySalesGrowthExport')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/export/getconsumertransactionexport', 'TransactionController@getConsumerTransactionExport')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/getvoidtransactionreport', 'TransactionController@getVoidTransactionReport')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/export/getvoidtransactionexport', 'TransactionController@getVoidTransactionExport')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/alltransactions', 'TransactionController@getAllTransactions')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/modifiedtransactionhistory', 'TransactionController@modifiedtransactionhistory')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/import/corporateParents', 'ImportController@importCorporateParentFromExcel')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/getstoresbytype', 'StoreController@getStoresByType')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/getenabledisablereasons', 'StoreController@getEnableDisableReasons')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/disablestore', 'StoreController@disableStore')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/enablestore', 'StoreController@enableStore')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/allpendingtransactions', 'TransactionController@getAllPendingTransactions')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/updatetransactionstatus', 'TransactionController@updateTransactionStatus')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/runmerchantscheduler', 'TransactionController@runMerchantScheduler')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/export/exportalltransactions', 'TransactionController@getAllTransactionExport')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . ',' . REPORT_ADMIN . '');
    Route::post('/generatetransactionreport', 'TransactionController@generateTransactionReport')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/export/returnlist', 'TransactionController@exportReturnList')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/dashboard/transactiondetails', 'TransactionController@getTransactionDetails')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . ',' . REPORT_ADMIN . '');
    Route::get('/dashboard/getmanualvsdirectlinkeddata', 'TransactionController@getManualvsDirectLinkedData')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . ',' . REPORT_ADMIN . '');
    Route::post('/helpdeskuserlist', 'UserController@getAllHelpdeskUsers')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/helpdeskuserrolelist', 'UserController@getHelpdeskUserRoles')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/addstoredailytransactionemail', 'StoreController@addStoreDailyTransactionEmail')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/storeHideFromMap', 'StoreController@storeHideFromMap')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::get('/getreturnstatus', 'TransactionController@getReturnStatus')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . ',' . REPORT_ADMIN . '');
    Route::get('/getreturnreasons', 'TransactionController@getReturnReasons')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . ',' . REPORT_ADMIN . '');
    Route::post('/waivetransaction', 'TransactionController@waiveTransaction')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/genarteReturnTransactionDetails', 'TransactionController@generateReturnTransactionDetails')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/getmerchantdetails', 'StoreController@getMerchantDetails')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/getstoredetails', 'StoreController@getStoreDetails')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/getmerchentstoretransactiontype', 'StoreController@getMerchentStoreTransactionType')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/getauthorizedstores', 'StoreController@getAuthorizedStores')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . ',' . REPORT_ADMIN . '');
    Route::post('/getreturnreasonlist', 'TransactionController@getReturnReasonList')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . REPORT_ADMIN . '');
    Route::post('/sendmailbasedonreturn', 'TransactionController@sendMailBasedOnReturn')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/getreturnlog', 'ReturnController@getReturnLog')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/post/returns', 'ReturnController@postReturnsFromExcel')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/export/returnskippedrecords', 'ReturnController@getReturnSkippedLogsExcel')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');

    Route::post('/getconsumerdetails', 'UserController@getConsumerDetails')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/searchConsumer', 'UserController@searchConsumer')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/getConsumersIncUnregistered', 'UserController@getConsumersIncUnregistered')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/getV1Consumers', 'UserController@getV1Consumers')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/searchV1Consumer', 'UserController@searchV1Consumer')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/getconsumerdetailsByUserID', 'UserController@getConsumerdetailsByUserid')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/updateConsumerDetails', 'UserController@updateConsumerDetails')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::get('/logoutuser', 'UserController@logoutUser')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . ',' . REPORT_ADMIN . '');

    Route::post('/getreturntransactionreport', 'TransactionController@generateReturnTransactionReport')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . ',' . REPORT_ADMIN . '');
    Route::post('export/exportreturntransactions', 'TransactionController@getReturnTransactionReportExport')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . ',' . REPORT_ADMIN . '');
    Route::post('/getreturntransactiongroupreport', 'TransactionController@generateReturnTransactionGroupReport')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . REPORT_ADMIN . '');
    Route::post('export/exportreturntransactionsgroup', 'TransactionController@getReturnTransactionGroupReportExport')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . REPORT_ADMIN . '');
    Route::post('/getAllv1ManualReviewDetails', 'ManualReviewController@getAllManualReviewDetailsForV1')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/getIdentityValidationDetailsForV1', 'ManualReviewController@getIdentityValidationDetailsForV1')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/updateReviewStatusForV1', 'ManualReviewController@updateReviewStatusForV1')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/updatemerchantstoretransactiontype', 'StoreController@updateMerchantStoreTransactionType')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');

    Route::post('/searchRegisteredConsumer', 'UserController@searchRegisteredConsumer')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/searchconsumeralgohistory', 'UserController@searchConsumerAlgoHistory')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/consumeraccountalgohistory', 'UserController@consumerAccountAlgoHistory')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/searchconsumeraccounts', 'UserController@searchConsumerAccounts')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/searchRegisteredMerchant', 'StoreController@searchRegisteredMerchant')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/getUsersPpLists', 'TransactionController@getUsersPpLists')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/getUsersPurchase', 'TransactionController@getUsersPurchase')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/export/exportUsersPpLists', 'TransactionController@getUsersPpListsExport')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/export/exportUsersPurchase', 'TransactionController@getUsersPurchaseExport')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/viewconsumerbalance', 'UserController@viewConsumerBalance')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/unlockConsumer', 'UserController@unlockConsumer')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');

    Route::post('/allActiveStores', 'StoreController@getAllActiveStores')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/allActiveCorporateParent', 'UserController@getAllActiveCorporateParent')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/merchant/reports/settlementreport', 'ReportController@settlementReport')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/merchant/export/getsettlementexport', 'ReportController@getSettlementExport')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/getWhitelistAllRoutingNumbers', 'WhitelistController@getWhitelistAllRoutingNumbers')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/addWhitelistRoutingNumber', 'WhitelistController@addWhitelistRoutingNumber')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/merchantadmin/reports/getmerchanttransactionreport', 'ReportController@getMerchantTransactionReport')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/merchantadmin/export/getmerchanttransactionexport', 'ReportController@getMerchantTransactionExport')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');

    Route::post('/generatefraudreport', 'AlertController@generateFraudReport')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/getconsumertransactionhistory', 'AlertController@getConsumerTransactionHistory')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/makeuserinactive', 'AlertController@makeUserInactive')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');

    Route::post('/searchConsumersReturnTransactions', 'UserController@searchConsumersReturnTransactions')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/releaseConsumer', 'UserController@releaseConsumers')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');

    Route::post('/generatefinicityreturns', 'ReportController@getFinicityReturns')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . REPORT_ADMIN . '');
    Route::post('/export/finicityreturnsexport', 'ReportController@getFinicityReturnsExport')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . REPORT_ADMIN . '');
    Route::post('/generatemanualbankingreturns', 'ReportController@getManualBankingReturns')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . REPORT_ADMIN . '');
    Route::post('/export/manualbankingreturnsexport', 'ReportController@getManualBankingReturnsExport')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . REPORT_ADMIN . '');
    Route::post('/generatemanualreviewreturns', 'ReportController@getManualReviewReturns')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . REPORT_ADMIN . '');
    Route::post('/export/manualreviewreturnsexport', 'ReportController@getManualReviewReturnsExport')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . REPORT_ADMIN . '');

    Route::post('/generatereturnreportdashboard', 'ReportController@generateReturnReportDashboard')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . REPORT_ADMIN . '');
    Route::post('/export/generatereturnreportdashboardExport', 'ReportController@generateReturnReportDashboardExport')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . REPORT_ADMIN . '');

    Route::post('/getconsumerreturntransaction', 'UserController@getConsumerReturnTransaction')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');

    Route::post('/getconsumercomments', 'UserController@getConsumerComments')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/saveconsumercomment', 'UserController@saveConsumerComment')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/savebankdelinked', 'UserController@saveBankDelinked')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');

    Route::post('/fetchconsumeraccountbalance', 'UserController@fetchConsumerAccountBalance')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/refreshconsumeraccountbalance', 'UserController@refreshConsumerAccountBalance')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/bypassmicrobilterror', 'UserController@bypassMicrobiltError')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/import/holidayimport', 'HolidayListController@importHolidayExcel')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/holidaylist', 'HolidayListController@getHolidayList')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/getallconsumeruploaddocument', 'ConsumerDocumentUploadController@getAllConsumerUploadDocument')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/getconsumerdocumentdetails', 'ConsumerDocumentUploadController@getConsumerDocumentDetails')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/updateconsumeruploaddocumentstatus', 'ConsumerDocumentUploadController@updateConsumerUploadDocumentStatus')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/getsettingsvalue', 'SettingController@getSettingsValue')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::get('/getallsettingsvalue', 'SettingController@getAllSettingsValue')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/updatesettingsvalue', 'SettingController@updateSettingsValue')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/getallreleasenote', 'MerchantController@getAllReleaseNote')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/addreleasenote', 'MerchantController@addReleaseNote')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/import/returntransactions', 'ReturnController@importReturnTransactions')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/unknownreturnreasonlist', 'ReturnController@getUnknownReturnReasonList')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/addreturnreason', 'ReturnController@addReturnReason')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/searchblacklistedaccountno', 'BlacklistAccountNumberController@searchBlacklistedAccountNumber')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/searchconsumeraccountno', 'BlacklistAccountNumberController@searchConsumerAccountNo')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/deleteblacklistedaccountno', 'BlacklistAccountNumberController@deleteBlacklistedAccountNumber')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/addaccounttoblacklist', 'BlacklistAccountNumberController@addAccountToBlacklist')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/searchvalidaccountno', 'BlacklistAccountNumberController@searchValidAccountNumber')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/addvalidaccountnumber', 'BlacklistAccountNumberController@addValidAccountNumber')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/addaccountnumber', 'BlacklistAccountNumberController@addConsumerAccountNumber')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/deletevalidaccountno', 'BlacklistAccountNumberController@deleteValidAccountNumber')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/consumeraccountlist', 'BlacklistAccountNumberController@consumerAccountList')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/generatestorewisemonthlysales', 'ReportController@getStorewiseMonthlySales')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/export/getstorewisemonthlysalesexport', 'ReportController@getStorewiseMonthlySalesExport')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/getassessment', 'ManualReviewController@getAssessmentWithSSN')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/searchconsumers', 'UserController@searchConsumers')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/togglesuspectedconsumer', 'UserController@toggleSuspectedConsumer')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::get('/export/suspectedconsumersexport', 'UserController@exportSuspectedConsumersReport')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/searchconsumerswithprobableretuns', 'UserController@searchConsumersWithProbableRetuns')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/releaseconsumerfromreturn', 'UserController@releaseConsumerFromReturn')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::get('/export/allconsumerswithprobablereturn', 'UserController@exportAllConsumersWithProbableRetuns')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/updatestorelocation', 'StoreController@updatestorelocation')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/getunknownroutingnumbers', 'WhitelistController@getUnknownRoutingNumbers')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/addroutingtomaster', 'WhitelistController@addRoutingToMaster')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/declinerouting', 'WhitelistController@declineRouting')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/viewtransactionhistory', 'WhitelistController@viewTransactionHistory')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');

    Route::post('/searchnotifications', 'NotificationLogController@searchNotifications')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/resendnotification', 'NotificationLogController@resendNotification')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/userrolelist', 'UserController@getAdminUserRoles')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/adminintegratorlist', 'MerchantController@getAllIntegrator')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/editintegrator', 'MerchantController@editIntegrator')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/addintegrator ', 'MerchantController@addIntegrator')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');

    Route::post('/ecommerceCategoryList', 'IntregatorController@getAllCategory')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/ecommerceCategoryEdit', 'IntregatorController@editCategory')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/ecommerceCategoryAdd', 'IntregatorController@addCategory')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');

    Route::post('/allMerchants', 'IntregatorController@getAllMerchants')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/allEcommerceCategory', 'IntregatorController@getAllEcommerceCategory')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/merchantKeyList', 'IntregatorController@getAllMerchantKey')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/export/exportmerchantKeyList', 'IntregatorController@getAllMerchantKey')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/merchantKeyAdd', 'IntregatorController@addMerchantKey')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/merchantKeyStatusChange', 'IntregatorController@merchantKeyStatusChange')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/import/integratorLogo', 'ImportController@importIntegratorLogo')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/searchconsumerswithprobableretuns', 'UserController@searchConsumersWithProbableRetuns')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/releaseconsumerfromreturn', 'UserController@releaseConsumerFromReturn')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::get('/export/allconsumerswithprobablereturn', 'UserController@exportAllConsumersWithProbableRetuns')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/searchtransactionmodificationreason', 'TransactionController@getAllTransactionModificationReason')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/addtransactionmodificationreason', 'TransactionController@addTransactionModificationReason')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/edittransactionmodification', 'TransactionController@editTransactionModificationReason')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/deletetransactionmodificationreason', 'TransactionController@deleteTransactionModificationReason')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/searchtransactionmodificationcustomreason', 'TransactionController@getAllTransactionModificationCustomReason')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/deletetransactionmodificationcustomreason', 'TransactionController@deleteTransactionModificationCustomReason')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');

    Route::post('/callaccountownerapi', 'UserController@storeFinicityAccountOwnerInfo')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/showaccountownerhistory', 'UserController@showAccountOwnerHistory')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/toggleallowmerchantfeesreport', 'UserController@toggleAllowMerchantFeesReport')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/togglefreezesponsorpoints', 'UserController@toggleFreezeSponsorPoints')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');

    Route::post('/gettransactiondetailsforconsumer', 'UserController@getTransactionDetailsForConsumer')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');

    Route::post('/getrewardspinreport', 'ReportController@getRewardSpinReport')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');

    Route::post('/export/getrewardspinreportexport', 'ReportController@getRewardSpinReportExport')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/getfinancialinstitutions', 'UserController@getConsumerFinancialInstitutions')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/removeinstitution', 'UserController@removeFinancialInstitution')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/getusersenrollmentreport', 'UserController@getUsersEnrollmentReport')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/export/exportusersenrollmentreport', 'UserController@getUsersEnrollmentReport')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/getmanualbanklinkrestrictedroutingnumbers', 'ManualBankLinkRestrictionsController@getManualBankLinkRestrictedRoutingNumbers')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::get('/getallstate', 'ManualBankLinkRestrictionsController@getAllState')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/addeditrestrictedroutingnumber', 'ManualBankLinkRestrictionsController@addEditRestrictedRoutingNumber')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/statuschangerestrictedroutingnumber', 'ManualBankLinkRestrictionsController@statusChangeRestrictedRoutingNumber')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/checkrestrictedroutingnumber', 'ManualBankLinkRestrictionsController@checkrestrictedroutingnumber')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/saveuserreviewcomment', 'UserController@saveUserReviewComment')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/getuserreviewdata', 'UserController@getUserReviewData')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');

    Route::post('/restrictedConsumer', 'MicrobiltReportController@searchForRestrictedConsumer')->middleware('role:' . SUPERADMIN);
    Route::post('/updateReviewStatus', 'MicrobiltReportController@updateReviewStatus')->middleware('role:' . SUPERADMIN);
    Route::post('/consumerDetailMicrobilt', 'MicrobiltReportController@getRequiredConsumerDetails')->middleware('role:' . SUPERADMIN);
    Route::post('/searchDifferentUser', 'MicrobiltReportController@searchForOtherUserWithSameAccount')->middleware('role:' . SUPERADMIN);
    Route::post('/getReportOfConsumerDeclinedForBankValidation', 'MicrobiltReportController@getReportOfConsumerDeclinedForBankValidation')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/getbanklist', 'BankController@getAllBank')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/editbank', 'BankController@editBank')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');

    Route::post('/import/banklogo', 'ImportController@importBankLogo')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/getbanksolutionlist', 'BankController@getAllBankSolution')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/editbanksolution', 'BankController@editBankSolution')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/getbanksolutionstatus', 'BankController@getBankSolutionStatus')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/getbankroutingnumbers', 'BankController@getBankRoutingNumbers')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/import/updateakoyaproviderid', 'ImportController@updateAkoyaProviderId')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/import/updatemxinstitutioncode', 'ImportController@updateMxInstitutionCode')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/mxinstitution', 'BankController@getMxInstitution')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/toggleStoreAsSponsor', 'StoreController@toggleStoreAsSponsor')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');

    Route::post('/getconsumercognitodetail', 'ManualReviewController@getConsumerCognitoDetail')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/fetchmxbankdata', 'UserController@fetchMxBankData')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');

    Route::post('/updatepurchasepowerrule', 'UserController@updatePurchasePowerRule')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/getconsumerbanklist', 'UserController@getConsumerBankList')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/makereturnrepayment', 'TransactionController@makeReturnRepayment')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/getregistrationfailure', 'UserController@getRegistrationFailure')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/getbanklinksurveyhistory', 'BankController@getBankLinkSurveyHistory')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/addbankdetail', 'BankController@addBankDetail')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/saveroutingnumber', 'BankController@saveRoutingNumber')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/getbanklinksurveyhistory', 'BankController@getBankLinkSurveyHistory')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');

    Route::post('/getcampaignlists', 'BrandController@getCampaignLists')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/getActiveRemotepayStoreList', 'StoreController@getAllActiveRemotePayStore')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/getdeliveryfeereport', 'ReportController@getDeliveryFeeReport')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/getallmerchantsanddeliverypartners', 'ReportController@getAllMerchantsAndDeliveryPartners')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/export/getdeliveryfeetransactionexport', 'ReportController@getDeliveryFeeTransactionExport')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/deliveryfeesettlementreport', 'ReportController@deliveryFeeSettlementReport')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/export/deliveryfeesettlementreportexport', 'ReportController@deliveryFeeSettlementReportExport')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/store-wise-health-checks', 'MerchantController@getHealthChecksByStoreAndDate')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/getpetitions', 'PetitionController@getpetitions')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::get('/getpetitionstatus', 'PetitionController@getPetitionStatus')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/import/petitionlogo', 'ImportController@importPetitionLogo')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/editpetition', 'PetitionController@editPetition')->middleware('role:' . SUPERADMIN . ',' . ADMIN . '');
    Route::post('/getpetitionsignedusers', 'PetitionController@getPetitionSignedUsers')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/linkpetitiontostore', 'PetitionController@linkPetitionToStore')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/generatemerchantpointreport', 'ReportController@generateMerchantPointReport')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/export/generatemerchantpointreportexport', 'ReportController@generateMerchantPointReportExport')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
    Route::post('/sendpetitionemail', 'PetitionController@sendPetitionEmail')->middleware('role:' . SUPERADMIN . ',' . ADMIN . ',' . HELPDESK . '');
});
