<?php

namespace App\Exports;

use App\Models\ReturnManualPostLog;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Events\AfterSheet;
use Illuminate\Support\Facades\Log;

class ReturnSkippedRecordsExport implements FromCollection, WithHeadings, ShouldAutoSize, WithEvents
{
    protected $request;

    public function __construct($request)
    {
        $this->request = $request; // Declaring the request variable
    }

    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        //get the list of Skipped records
        $returnSkippedRecords = ReturnManualPostLog::where(['return_posted' => '0','batch_id' => $this->request['batch_id']])->get();

        $returnLogs = [];
        foreach($returnSkippedRecords as $record){
            $row_data = json_decode($record['row_data']);
            $nestedData['id'] = $row_data->id;  
            $nestedData['transaction_id'] = $row_data->transaction_id;  
            $nestedData['consumer_id'] = $row_data->consumer_id;  
            $nestedData['consumer_bank_link_type'] = $row_data->consumer_bank_link_type!=1 ? '0' : $row_data->consumer_bank_link_type;
            $nestedData['account_id'] = $row_data->account_id;  
            $nestedData['amount'] = $row_data->amount;  
            $nestedData['tip_amount'] = $row_data->tip_amount;  
            $nestedData['transaction_time'] = $row_data->transaction_time;  
            $nestedData['local_transaction_time'] = $row_data->local_transaction_time;  
            $nestedData['status_id'] = $row_data->status_id;  
            $nestedData['transaction_account_no'] = $row_data->transaction_account_no;  
            $nestedData['transaction_routing_no'] = $row_data->transaction_routing_no;  
            $nestedData['current_account_no'] = $row_data->current_account_no;  
            $nestedData['current_routing_no'] = $row_data->current_routing_no;  
            $nestedData['ach_account_no'] = $row_data->ach_account_no;  
            $nestedData['ach_routing_no'] = $row_data->ach_routing_no;  
            $nestedData['match_percentage_account_no'] = $row_data->match_percentage_account_no;  
            $nestedData['match_percentage_routing_no'] = $row_data->match_percentage_routing_no;  
            $nestedData['match_percentage_transaction_account_no'] = $row_data->match_percentage_transaction_account_no;  
            $nestedData['match_percentage_transaction_routing_no'] = $row_data->match_percentage_transaction_routing_no;  
            $nestedData['is_posted'] = $row_data->is_posted!=1 ? '0' : $row_data->is_posted;
            $nestedData['posting_time'] = $row_data->posting_time;  
            $nestedData['created_at'] = $row_data->created_at;  
            $nestedData['updated_at'] = $row_data->updated_at;  
            $nestedData['message'] = $record['message'];      

            array_push($returnLogs,$nestedData);
        }
        
        return collect([
            $returnLogs,
        ]);
    }

    public function headings(): array
    { 
        $returnArray = array(
            [
                'id',
                'transaction_id',
                'consumer_id',
                'consumer_bank_link_type',
                'account_id',
                'amount',
                'tip_amount',
                'transaction_time',
                'local_transaction_time',
                'status_id',
                'transaction_account_no',
                'transaction_routing_no',
                'current_account_no',
                'current_routing_no',
                'ach_account_no',
                'ach_routing_no',
                'match_percentage_account_no',
                'match_percentage_routing_no',
                'match_percentage_transaction_account_no',
                'match_percentage_transaction_routing_no',
                'is_posted',
                'posting_time',
                'created_at',
                'updated_at',
                'message'
            ],
        );

        return $returnArray;
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event){
                $event->sheet->getStyle('A1:Y1')->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);
            },
        ];
    }
}
