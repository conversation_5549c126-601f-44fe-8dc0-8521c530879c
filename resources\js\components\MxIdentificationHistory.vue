<template>
    <div>
        <div v-if="loading">
            <CanPayLoader/>
        </div>
  <div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
                <div class="card card-success">
                  <div class="card-header">
                      <h3 class="card-title">MX Identification History</h3>
                  </div>
                <div class="card-body">
                <div class="row">
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        autocomplete="off"
                        class="start-date form-control"
                        placeholder="Select Date"
                        id="start-date"
                        onkeydown="return false"
                      />
                    </div>
                  </div>
                </div>
                </div>
                  <div class="card-footer">
                    <button
                      type="button"
                      class="btn btn-success"
                      @click="fetchMxInstitutionHistory(true)"
                    >
                      Search
                    </button>
                    <button
                      type="button"
                      class="btn btn-success"
                      @click="reset()"
                    >
                      Reset
                    </button>
                  </div>
                  <div v-if="items.length>0" class="p-2">
                      <b-table
                      bordered
                      id="mx-tabl-indentification-hitory-pagination"
                      head-variant="light"
                      :per-page="perPage"
                      :current-page="currentPage"
                      :items="items" 
                      :fields="fields">
                      <template #cell(source)="row">
                        {{capitalizeWords(row.item.source)}}
                      </template>
                      <template #cell(action)="row">
                        <a v-if="row.item.completed_at == null && row.item.response_received_at == null && row.item.source == admin && row.item.retry_count<retryCount" @click="MxInstitutionRetry(row.item.id,row.item.consumer_name)" class="custom-edit-btn" title="Retry Identity Verification" variant="outline-success" style="border:none"><i class="nav-icon fas fa-sync-alt"></i></a>
                      </template>
                      </b-table>
                      <b-pagination
                      v-model="currentPage"
                      :total-rows="rows"
                      :per-page="perPage"
                      prev-text="Prev"
                      next-text="Next"
                      :ellipsis="true"
                      :limit="5"
                      aria-controls="mx-tabl-indentification-hitory-pagination"
                      align="right"
                    ></b-pagination>
                  </div>
                  <div v-else class="p-4">
                    <p>No data displayed. Please refine your search criteria.</p>
                  </div>
                </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</div>
</template>
<script>
import commonConstants from "@/common/constant.js";
import api from '@/api/user.js'
import moment from "moment";
import CanPayLoader from "./CustomLoader/CanPayLoader.vue"
export default {
components: {
  CanPayLoader
},
mounted(){
    var self = this;
    console.log(self.admin);
      $("#start-date").datepicker({
      format: "mm/dd/yyyy",
      autoclose: true,
      endDate: moment(new Date()).format("MM/DD/YYYY"),
      todayHighlight: true,
      });
       $("#start-date").val(moment(new Date()).format("MM/DD/YYYY"));
},
data(){
  return {
    start_data:"",
    items:[],
    fields:[
      {
        key:"consumer_name",
        label:"Consumer Name",
        thClass:"text-center",
        tdClass:"text-center"
      },
      {
        key:"phone",
        label:"Consumer Phone",
        thClass:"text-center",
        tdClass:"text-center"
      },
      {
        key:"email",
        label:"Consumer Email",
        thClass:"text-center",
        tdClass:"text-center"
      },
      {
        key:"mx_user_id",
        label:"MX User ID",
        thClass:"text-center",
        tdClass:"text-center"
      },
      {
        key:"mx_member_id",
        label:"MX Member ID",
        thClass:"text-center",
        tdClass:"text-center"
      },
      {
        key:"completed_at",
        label:"Completed At",
        thClass:"text-center",
        tdClass:"text-center"
      },
      {
        key:"source",
        label:"Source",
        thClass:"text-center",
        tdClass:"text-center"
      },
      {
        key:"action",
        label:"Action",
        thClass:"text-center",
        tdClass:"text-center"
      },
    ],
    currentPage:1,
    perPage:10,
    loading:false,  
    admin:commonConstants.role_admin_small,
    retryCount:commonConstants.retry_count,
    rows:0
  }
},
methods:{
  capitalizeWords(str) {
    if(str == null || str == undefined | str == ''){
      return '';
    }
     return str
          .split(' ')                  // Split the string into words
          .map(word =>                  // Map over each word
              word.charAt(0).toUpperCase() +  // Capitalize the first letter
              word.slice(1).toLowerCase()     // Convert the rest to lowercase
          )
          .join(' ');                   // Join the words back into a single string
  },
  reset(){
     $("#start-date").val('');
  },
  fetchMxInstitutionHistory(value){
    let self = this;
    if(moment($("#start-date").val()).format("YYYY-MM-DD") == "Invalid date"){
        error("Please Select a Date.");
        return;
      }
      self.loading = value;
    const payload = {
      date:moment($("#start-date").val()).format("YYYY-MM-DD")
    }
    api
    .getIdentifyMemberCallList(payload)
    .then((response)=>{
      self.loading = false;
      self.items = response.data;
      self.rows = response.data.length;
    })
    .catch((err) => {
      self.loading = false;
      error(err.response.data.message);
    })
  },
  MxInstitutionRetry(id,name){
    let self = this;
    const payload = {
      record_id:id
    }
    api
    .retryIdentityVerification(payload)
    .then((response) => {
      console.log(response);
      Vue.swal(response.message,'', "success");
      self.fetchMxInstitutionHistory(false);
    })
    .catch((err) => {
      Vue.swal(err.response.data.message,'', "error");
      self.fetchMxInstitutionHistory(false);
    })
  }

}
}
</script>
<style scoped>

</style>