<?php

namespace App\Exports;

use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Carbon\Carbon;

class MerchantPointReportExport implements FromCollection, WithHeadings, ShouldAutoSize, WithStyles
{
    protected $request;
    protected $pointTypes;

    public function __construct($request)
    {
        $this->request = $request;
        $this->pointTypes = [SPONSOR_POINTS, BRAND_POINT, CASHBACK_POINTS, PETITION_POINTS];
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        $report = $this->request->get('report');
        $data = $report['data'] ?? [];
        
        $exportData = [];
        
        // Add report title and date range
        $exportData[] = ['Merchant Point Report'];
        $exportData[] = ['Date Range:', $this->request->get('from_date') . ' to ' . $this->request->get('to_date')];
        $exportData[] = ['']; // Empty row
        foreach ($this->pointTypes as $type) {
            if (!isset($data[$type])) {
                continue;
            }
            
            // Add point type header with title case
            $exportData[] = [ucwords($type)];
            $exportData[] = ['Date', 'Points Given', 'Amount Given ($)', 'Points Redeemed', 'Amount Redeemed ($)'];
            
            // Add data rows
            foreach ($data[$type] as $date => $item) {
                $exportData[] = [
                    $item['date'],
                    (int)$item['cr']['points'],
                    number_format((float)$item['cr']['amounts'], 2),
                    (int)$item['dr']['points'],
                    number_format((float)$item['dr']['amounts'], 2)
                ];
            }
            
            // Add totals
            if (isset($report['totals'][$type])) {
                $totals = $report['totals'][$type];
                $exportData[] = [
                    'Total',
                    (int)$totals['total_cr']['points'],
                    number_format((float)$totals['total_cr']['amounts'], 2),
                    (int)$totals['total_dr']['points'],
                    number_format((float)$totals['total_dr']['amounts'], 2)
                ];
            }
            
            $exportData[] = ['']; // Empty row
        }
        
        return collect($exportData);
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return []; // Headers are included in the collection data
    }

    /**
     * @param mixed $row
     * @return array
     */
    public function map($row): array
    {
        return [
            $row->id,
            $row->corporate_name,
            $row->store_name,
            $row->points,
            $row->description,
            Carbon::parse($row->transaction_date)->format('Y-m-d H:i:s')
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return void
     */
    public function styles(Worksheet $sheet)
    {
        // Style the title row
        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
        
        // Style the date range row
        $sheet->getStyle('A2:B2')->getFont()->setBold(true);
        
        // Find all point type headers and column headers and make them bold
        $highestRow = $sheet->getHighestRow();
        for ($row = 2; $row <= $highestRow; $row++) {
            $value = $sheet->getCellByColumnAndRow(1, $row)->getValue();
            if (in_array(strtolower($value), array_map('strtolower', $this->pointTypes))) {
                // Merge cells A to E for the point type header
                $sheet->mergeCells('A'.$row.':E'.$row);
                
                // Style point type header
                $sheet->getStyle('A'.$row)->getFont()->setBold(true)->setSize(12);
                
                // Make the next row (column headers) bold and add borders
                $sheet->getStyle('A'.($row+1).':E'.($row+1))->getFont()->setBold(true);
                $sheet->getStyle('A'.($row+1).':E'.($row+1))->applyFromArray([
                    'borders' => [
                        'allBorders' => [
                            'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                            'color' => ['argb' => '000000'],
                        ],
                        'bottom' => [
                            'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                            'color' => ['argb' => '000000'],
                        ],
                    ],
                ]);
                
                // Find the end of this section (next empty row or end of sheet)
                $sectionEnd = $row + 1;
                for ($i = $row + 2; $i <= $highestRow; $i++) {
                    $cellValue = $sheet->getCellByColumnAndRow(1, $i)->getValue();
                    if (empty($cellValue)) {
                        $sectionEnd = $i - 1;
                        break;
                    }
                    
                    // Add borders to data rows
                    if ($cellValue !== 'Total') {
                        $sheet->getStyle('A'.$i.':E'.$i)->applyFromArray([
                            'borders' => [
                                'allBorders' => [
                                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                                    'color' => ['argb' => '000000'],
                                ],
                            ],
                        ]);
                    }
                }
            }
            
            if ($value === 'Total') {
                // Style total row with bold font and borders
                $sheet->getStyle('A'.$row.':E'.$row)->getFont()->setBold(true);
                $sheet->getStyle('A'.$row.':E'.$row)->applyFromArray([
                    'borders' => [
                        'allBorders' => [
                            'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                            'color' => ['argb' => '000000'],
                        ],
                        'top' => [
                            'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                            'color' => ['argb' => '000000'],
                        ],
                        'bottom' => [
                            'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_MEDIUM,
                            'color' => ['argb' => '000000'],
                        ],
                    ],
                ]);
            }
        }
        
        // Add border to title and date range
        $sheet->getStyle('A1:E2')->applyFromArray([
            'borders' => [
                'outline' => [
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                    'color' => ['argb' => '000000'],
                ],
            ],
        ]);
        
        return [];
    }
}
