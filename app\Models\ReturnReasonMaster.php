<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
class ReturnReasonMaster extends Model
{
    /**
     * __construct
     * This function will insert an alphanumeric 32 character unique id as primary key in the table whenever a data get inserted
     * @param  mixed $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        $this->id = generateUUID();

        parent::__construct($attributes);
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'reason_code',
        'title',
        'description',
        'time_frame',
        'canpay_represent',
        'new_banking',
        'new_banking_after_second_attempt',
        'bank_login',
        'bank_login_after_second_attempt',
    ];
    public $timestamps = true;
    public $incrementing = false;
}
