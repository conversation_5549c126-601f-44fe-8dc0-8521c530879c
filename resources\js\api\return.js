const postReturn = (formData) => {
    return new Promise((res, rej) => {
        axios.defaults.headers.common["Content-Type"] = 'multipart/form-data';
        axios.post('api/post/returns', formData)
            .then((response) => {
                res(response.data);
            })
            .catch((response) => {
                rej(response);
            })
    })
};

const getReturnSkippedRecords = (request) => {
    var header = {
        responseType: 'blob',
    };
    return new Promise((res, rej) => {
        axios.post('/api/export/returnskippedrecords', request, header)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const addReturnReason = (request) => {
    return new Promise((res, rej) => {
        axios.post('/api/addreturnreason', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

export default {
    postReturn,
    getReturnSkippedRecords,
    addReturnReason
};