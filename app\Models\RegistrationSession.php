<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;


class RegistrationSession extends Model
{

    /**
     * __construct
     * This function will insert an alphanumeric 32 character unique id as primary key in the table whenever a data get inserted
     * @param  mixed $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        $this->id = generateUUID();

        parent::__construct($attributes);
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'session_id',
        'email',
        'phone',
        'first_name',
        'middle_name',
        'last_name',
        'date_of_birth',
        'street_address',
        'state',
        'suffix',
        'city',
        'zipcode',
        'ssn_number',
        'password',
        'id_validation',
        'bank_validation',
        'status_id',
        'is_validation_success',
        'steps_completed',
    ];
    public $table = 'registration_session_details';
    public $incrementing = false;
}
