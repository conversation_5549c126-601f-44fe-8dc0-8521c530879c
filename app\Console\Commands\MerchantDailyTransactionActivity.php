<?php

namespace App\Console\Commands;

use App\Http\Factories\EmailExecutor\EmailExecutorFactory;
use App\Models\User;
use App\Models\StoreUserTransactionActivityEmailMaps;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class MerchantDailyTransactionActivity extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'merchant:email_transactions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will email the previous day transactions to all the Corporate Parents';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->emailexecutor = new EmailExecutorFactory();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->info("Processing....");
        //Email Sent for all the Stores
        $corporate_parents = User::join('user_roles', 'users.role_id', '=', 'user_roles.role_id')->leftJoin('corporate_parent_transaction_activity_email_maps', 'users.user_id', '=', 'corporate_parent_transaction_activity_email_maps.user_id')->select('users.*')->addSelect(DB::raw("GROUP_CONCAT(distinct(corporate_parent_transaction_activity_email_maps.email) SEPARATOR ',') as cp_emails"))->where('user_roles.role_name', CORPORATE_PARENT)->groupBy('users.user_id')->get();
        $email_ids = [];
        foreach ($corporate_parents as $corporate_parent) {
            // Sending Daily Activity Mail to the Consumer
            array_push($email_ids, $corporate_parent->contact_person_email);
            $other_emails = explode(',', $corporate_parent->cp_emails);
            foreach ($other_emails as $email) {
                if ($email) {
                    array_push($email_ids, trim($email));
                }
            }
            $email_params = [
                'user_id' => $corporate_parent->user_id,
                'date' => date('m-d-Y', strtotime("-1 days")),
                'email' => $email_ids,
            ];
            $this->emailexecutor->corporateParentDailyTransactionActivity($email_params);
            $email_ids = [];
        }
        
        //Email to be sent to Store managers and Accountant for their Stores only (If the Email is not in the corporate_parent_transaction_activity_email_maps table)
        $storeManagersAccountants = User::join('user_roles', 'users.role_id', '=', 'user_roles.role_id')
        ->select('users.*')
        ->where('receive_daily_transaction_email','1')
        ->whereIn('user_roles.role_name', [STORE_MANAGER, ACCOUNTANT])
        ->get();
        
        foreach ($storeManagersAccountants as $cpUsers) {
            // Sending Daily Activity Mail to the Store Managers and Accountant
            if (!in_array($cpUsers->email, $email_ids)){
                $email_params_sm = [
                    'user_id' => $cpUsers->user_id,
                    'date' => date('m-d-Y', strtotime("-1 days")),
                    'email' => $cpUsers->email,
                ];
                $this->emailexecutor->corporateParentDailyTransactionActivity($email_params_sm);
            }
        }

        //Email to be Sent to Users at the Store Level
        $storeUsers = StoreUserTransactionActivityEmailMaps::get();
        foreach ($storeUsers as $stUsers) {
            // Sending Daily Activity Mail to the users at the Store Level
            $email_params_su = [
                'store_id' => $stUsers->store_id,
                'date' => date('m-d-Y', strtotime("-1 days")),
                'email' => $stUsers->email,
            ];
            $this->emailexecutor->storeUserDailyTransactionActivity($email_params_su);
        }
        
        $this->info("Merchants Daily Activity Email sent successfully.");
    }
}
