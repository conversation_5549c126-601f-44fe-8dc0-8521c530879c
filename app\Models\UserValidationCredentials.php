<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;


class UserValidationCredentials extends Model
{

    /**
     * __construct
     * This function will insert an alphanumeric 32 character unique id as primary key in the table whenever a data get inserted
     * @param  mixed $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        $this->id = generateUUID();

        parent::__construct($attributes);
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'phone',
        'cognito_profile_id',
        'finicity_customer_id',
        'finicity_consumer_id',
        'finicity_account_id',
        'account_number',
        'routing_number',
        'account_type',
        'session_id',
    ];
    public $table = 'user_validation_credentials';
    public $incrementing = false;
}
