<template>
<div>
  <div v-if="loading">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Health Checks</h3>
                  <b-button
                  class="btn-danger export-api-btn"
                  @click="reloadDatatable"
                  v-if="showReloadBtn"
                  >
                    <i class="fas fa-redo"></i> Reload
                  </b-button>
                </div>
                <div class="card-body">
                  <div class="row">
                    <div class="col-md-4">
                      <div class="form-group">
                        <input
                          autocomplete="off"
                          class="start-date form-control"
                          placeholder="Start Date"
                          id="start-date"
                          onkeydown="return false"
                        />
                      </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                        <multiselect
                            id="store"
                            v-model="selectedStore"
                            placeholder="Select Store (Min 3 chars)"
                            label="retailer"
                            :options="storelist"
                            :loading="isLoading"
                            :internal-search="false"
                            v-validate="'required'"
                            @search-change="getAllStores"
                        ></multiselect>
                        </div>
                    </div>
                  </div>
                  <div class="row">
                  <div class="col-md-4">
                      <button
                        type="button"
                        class="btn btn-success"
                        @click="generateReport(false)"
                        id="generateBtn"
                      >
                        Generate</button>
                      <button
                        type="button"
                        @click="reset()"
                        class="btn btn-danger margin-left-5"
                      >
                        Reset
                      </button>
                  </div>
                </div>
                </div>
                <div class="card-footer"></div>
                <!-- /.card-header -->
                <div class="card-body">
                  <div class="row">
                    <div class="col-12">
                      <table
                        id="transactionTable"
                        class="table"
                        style="width: 100%; white-space: normal"
                      >
                        <thead>
                          <tr>
                            <th>Store Name</th>
                            <th>Health Check Time (ET)</th>
                          </tr>
                        </thead>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</div>
</template>
<script>
import api from "@/api/transaction.js";
import moment from "moment";
import { validationMixin } from "vuelidate";
import { required, minLength } from "vuelidate/lib/validators";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "./CustomLoader/CanPayLoader.vue"
import commonConstants from "@/common/constant.js";
export default {
  mixins: [validationMixin],
  data() {
    return {
      allTransactionModel: {},
      showMsg: false,
      transaction_id: null,
      comment: "",
      currentUser: localStorage.getItem("user")
        ? JSON.parse(localStorage.getItem("user"))
        : null,
      loading: false,
      report: [],
      generateExport: false,
      showReloadBtn:false,
      constants: commonConstants,
      consumer:"",
      storelist: [],
      transactionHistory: [],
      selectedStore: null,
      isLoading: false,
      headerTextVariant: "light",
      historyModalTitle: "Transaction history",
    };
  },
  components: {
    HourGlass,
    CanPayLoader
  },
  created() {
    this.trLeaveComment();
  },
  methods: {
    //API call to fetch All stores
    getAllStores(searchtxt) {
      var self = this;
      if(searchtxt.length >= 3){
        self.isLoading = true;
        var request = {
          searchtxt: searchtxt,
        };
      api
        .getStores(request)
        .then(function (response) {
          if (response.code == 200) {
            self.storelist = response.data;
            self.isLoading = false;
          }else {
            error(response.message);
            self.isLoading = false;
          }
        })
        .catch(function (error) {
        });
      }
    },
    reloadDatatable(){
      var self = this;
      self.loadDT();
    },
    // API call to generate the all the Transactions Report
    generateReport() {
      var self = this;
      if (
        moment($("#start-date").val()).format("YYYY-MM-DD") >
          moment().format("YYYY-MM-DD") &&
        $("#start-date").val() != ""
      ) {
        error("Start date cannot be from future.");
        return false;
      }
      if (
        moment($("#end-date").val()).format("YYYY-MM-DD") >
          moment().format("YYYY-MM-DD") &&
        $("#end-date").val() != ""
      ) {
        error("End date cannot be from future.");
        return false;
      }
      if ($("#start-date").val() != "") {
        var from_date = moment($("#start-date").val()).format("YYYY-MM-DD");
      } else {
        var from_date = "";
      }
      if ($("#end-date").val() != "") {
        var to_date = moment($("#end-date").val()).format("YYYY-MM-DD");
      } else {
        var to_date = "";
      }
      if(self.selectedStore === null){
        var store_id = '';
        error("Please select a store");
        return false;
      }else{
        var store_id = self.selectedStore.id;
      }
      self.report = [];
      var request = {
        from_date: from_date,
        to_date: to_date,
        store_id: store_id,
        consumer: self.consumer,
      };
      if (request.from_date > request.to_date) {
        error("To Date cannot be greater than From date");
        return false;
      }
      if (self.generateExport == false) {
        self.loading = true;
      }
      self.loadDT(request);
    },
    loadDT: function (request) {
      var self = this;
      $("#transactionTable").DataTable({
        searching:false,
        processing: true,
        serverSide: true,
        destroy: true,
        info: false,
        scrollY: "100vh",
        scrollX: "100%",
        fixedHeader: true,
        scroller: {
          displayBuffer: 100,
          boundaryScale: 0.5,
          loadingIndicator: false,
        },
        stateSave: false,
        columnDefs: [
          { orderable: false, targets: [0] },
          { className: "dt-left", targets: [0, 1] },
        ],
        order: [[1, "desc"]],
        orderClasses: false,
        language: {
          processing:
            '<i class="fa fa-spinner fa-spin fa-3x fa-fw"></i><span class="sr-only"></span> ',
          emptyTable: "No health Checks Data Available.",
          search: "_INPUT_",
          searchPlaceholder: "Search records",
        },
        ajax: {
          headers: {
            Authorization: "Bearer " + localStorage.getItem("token"),
          },
          url: "/api/store-wise-health-checks",
          type: "POST",
          data: {
            _token: "{{csrf_token()}}",
            from_date: request.from_date,
            store_id: request.store_id,
          },
          dataType: "json",
          dataSrc: function (result) {
            self.showReloadBtn = false;
            self.allTransactionModel = result.data;
            if (self.generateExport == false) {
              self.loading = false;
            }
            return self.allTransactionModel;
          },
          error: function(data){
            error(commonConstants.datatable_error);
            $('#transactionTable_processing').hide();
            self.showReloadBtn = true;
          }
        },
        columns: [
          { data: "store_name" },
          { data: "health_check_time" },
        ],
      });
    },

    reset(){
      var self = this;
      self.selectedStore = null;
    }
  },
  mounted() {
    var self = this;
    $("#start-date").datepicker({
      format: "mm/dd/yyyy",
      autoclose: true,
      todayHighlight: true,
    });
    $("#start-date").datepicker("setDate", new Date());
  },
};
</script>
