<template>
<div>
    <div v-if="loading">
        <CanPayLoader/>
    </div>
    <div class="content-wrapper" style="min-height: 36px">
        <section class="content-header">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-12">
                        <div class="card card-success">
                            <div class="card-header">
                                <h3 class="card-title">Integrator</h3>
                            </div>
                            <!-- /.card-header -->
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <input
                                                class="form-control"
                                                placeholder="Integrator name (min 3 chars)"
                                                id="integrator_name"
                                                v-model="integrator_name"
                                            />
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <input
                                                class="form-control"
                                                placeholder="Integrator ID (Exact)"
                                                id="integrator_id"
                                                v-model="integrator_id"
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer">
                                <button
                                    type="button"
                                    class="btn btn-success"
                                    @click="searchIntegrator()"
                                >
                                    Search
                                </button>
                                <button
                                    type="button"
                                    @click="reset()"
                                    class="btn btn-success margin-left-5"
                                >
                                    Reset
                                </button>
                                <b-button
                                    @click="openModal('add')"
                                    class="btn btn-success margin-left-5"
                                >
                                    <i class="far fa-plus-square"></i> Add
                                    Integrator
                                </b-button>
                            </div>
                            <div class="card-body">
                                <b-table-simple
                                    responsive
                                    show-empty
                                    bordered
                                    sticky-header="800px"
                                    v-if="allIntegrators.length > 0"
                                >
                                    <b-thead head-variant="light">
                                        <tr>
                                            <th>Ecommerce Integrator Name</th>
                                            <th>Integrator Id</th>
                                            <th>Location Type</th>
                                            <th>Allow Remote Payment</th>
                                            <th>Consumer Auth</th>
                                            <th>Created At</th>
                                            <th class="text-center">Action</th>
                                        </tr>
                                    </b-thead>
                                    <b-tbody
                                        v-for="(row, index) in allIntegrators"
                                        :key="index"
                                    >
                                        <b-tr>
                                            <b-td class="text-left text-gray">{{
                                                row.ecommerce_integrator_name
                                            }}</b-td>
                                            <b-td class="text-left text-gray">{{
                                                row.integrator_id
                                            }}</b-td>
                                            <b-td class="text-left text-gray">{{
                                                row.location_type
                                            }}</b-td>
                                            <b-td class="text-left text-gray">{{
                                                row.allow_ecommerce_transaction ==
                                                1
                                                    ? "Yes"
                                                    : "No"
                                            }}</b-td>
                                            <b-td class="text-left text-gray">{{
                                                row.allow_consumer_auth == 1
                                                    ? "Yes"
                                                    : "No"
                                            }}</b-td>
                                            <b-td class="text-left text-gray">{{
                                                row.created_at
                                            }}</b-td>
                                            <b-td class="text-center text-gray">
                                                <b-button
                                                    @click="
                                                        editIntegrator(row.id)
                                                    "
                                                    class="editIntegrator custom-edit-btn"
                                                    title="Edit Integrator"
                                                    variant="outline-success"
                                                    style="border:none"
                                                    ><i
                                                        class="nav-icon fas fa-edit"
                                                    ></i
                                                ></b-button>
                                            </b-td>
                                        </b-tr>
                                    </b-tbody>
                                </b-table-simple>
                                <p v-else>
                                    No data displayed. Please refine your search
                                    criteria.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- CP Modal Start -->
        <b-modal
            id="integrator-modal"
            ref="modal"
            :header-text-variant="headerTextVariant"
            :title="modalTitle"
            @show="resetModal"
            @hidden="resetModal"
            ok-title="Save"
            ok-variant="success"
            cancel-variant="outline-secondary"
            @ok="handleOk"
            :no-close-on-esc="true"
            :no-close-on-backdrop="true"
        >
            <form
                ref="form"
                @submit.stop.prevent="save"
                class="needs-validation"
            >
                <div class="row">
                    <div class="col-md-12">
                        <label for="location_type">
                            Location Type
                            <span class="red">*</span>
                        </label>
                        <select
                            class="form-control"
                            id="location_type"
                            name="location_type"
                            v-model="integratorModel.location_type"
                            v-validate="'required'"
                        >
                            <option
                                v-for="(location, index) in locationTypeArray"
                                :key="index"
                                :value="location.location_type"
                            >
                                {{ location.location_type }}
                            </option>
                        </select>
                        <span
                            v-show="errors.has('location_type')"
                            class="text-danger"
                            >{{ errors.first("location_type") }}</span
                        >
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-8">
                        <label for="ecommerce_integrator_name">
                            Ecommerce Integrator Name
                            <span class="red">*</span>
                        </label>
                        <input
                            id="ecommerce_integrator_name"
                            name="ecommerce_integrator_name"
                            v-validate="'required|min:3'"
                            type="text"
                            v-model="integratorModel.ecommerce_integrator_name"
                            class="form-control"
                        />
                        <span
                            v-show="errors.has('ecommerce_integrator_name')"
                            class="text-danger"
                            >{{
                                errors.first("ecommerce_integrator_name")
                            }}</span
                        >
                    </div>
                </div>

                <br />
                <h4>RemotePay Setting</h4>

                <div class="row">
                    <div class="col-md-6">
                        <label for="allow_ecommerce_transaction">
                            RemotePay Transaction
                            <span class="red">*</span>
                        </label>

                        <label class="switch"
                            ><input
                                type="checkbox"
                                id="allow_ecommerce_transaction"
                                name="allow_ecommerce_transaction"
                                v-model="
                                    integratorModel.allow_ecommerce_transaction
                                "
                                true-value="1"
                                false-value="0"
                                class="enable-employee-login" /><span
                                class="slider round"
                            ></span
                        ></label>
                    </div>

                    <div class="col-md-6">
                        <label for="allow_ecommerce_transaction">
                            Consumer Auth
                            <span class="red">*</span>
                        </label>

                        <label class="switch"
                            ><input
                                type="checkbox"
                                id="allow_consumer_auth"
                                name="allow_consumer_auth"
                                v-model="integratorModel.allow_consumer_auth"
                                true-value="1"
                                false-value="0"
                                class="enable-employee-login" /><span
                                class="slider round"
                            ></span
                        ></label>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <label for="integrator_logo"> Integrator Logo </label>
                        <div class="input-group">
                            <div class="custom-file">
                                <input
                                    type="file"
                                    ref="logo_file"
                                    name="integrator_logo"
                                    id="integrator_logo"
                                    class="custom-file-input"
                                    accept="image/*"
                                    v-on:change="handleFileUpload()"
                                />
                                <label
                                    for="integrator_logo"
                                    class="custom-file-label"
                                    >{{ logo_fileName }}</label
                                >
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6"></div>
                    <div class="col-md-6">
                        <b-button
                            style="float: right; margin-top: 7px"
                            @click="clear"
                        >
                            Clear File
                        </b-button>
                    </div>
                </div>
                      <hr />
      <div class="row" v-if="logo_image">
        <div class="col-6" style="text-align: center">
          <img
            slot="image"
            disabled
            v-bind:src="logo_image"
            class="id-preview-integrator"
          />
        </div>
        <div class="col-6" style="text-align: center">

        </div>
      </div>
            </form>
        </b-modal>
        <!-- CP Modal End -->
    </div>
</div>
</template>
<script>
import api from "@/api/user.js";
import { validationMixin } from "vuelidate";
import { required, minLength } from "vuelidate/lib/validators";
import CanPayLoader from "../CustomLoader/CanPayLoader.vue"
export default {
    mixins: [validationMixin],
    components:{
        CanPayLoader
    },
    data() {
        return {
            modalTitle: "",
            statusList: [],
            headerTextVariant: "light",
            isDisabled: false,
            locationTypeArray: [
                { location_type: "Web" },
                { location_type: "Retail" },
            ],
            currentUser: localStorage.getItem("user")
                ? JSON.parse(localStorage.getItem("user"))
                : null,

            id: null,
            allIntegrators: [],
            integratorModel: {},

            integrator_name: "",
            integrator_id: "",
            loading: false,
            logo_file: null,
            logo_fileName: "Choose File",
            logo_image:null
        };
    },
    methods: {
        editIntegrator(id) {
            var self = this;
            self.id = id;
            var integratorDetails = self.allIntegrators.find(
                (p) => p.id == self.id
            );
            self.modalTitle = "Edit Integrator";
            self.$bvModal.show("integrator-modal");
            self.integratorModel = Object.assign({}, integratorDetails);
            self.integratorModel.id = integratorDetails.id;
            self.logo_image = integratorDetails.integrator_logo;
            self.isDisabled = true;
        },
        openModal(type) {
            var self = this;
            self.modalTitle = "Add Integrator";
            self.$bvModal.show("integrator-modal");
        },
        resetModal() {
            var self = this;
            self.integratorModel = {};
            self.isDisabled = false;
            self.logo_fileName = "Choose File";
            self.logo_file = null;
            self.upload_changed = false;
            self.id = null;
            self.logo_image=null;
            
        },
        reset() {
            var self = this;
            self.integratorModel = {};
            self.integrator_name = "";
            self.integrator_id = "";
            self.allIntegrators = [];
            self.logo_image=null;
            self.loadDT();
        },
        handleOk(bvModalEvt) {
            var self = this;
            bvModalEvt.preventDefault();
            self.save();
        },
        save() {
            var self = this;
            let formData = new FormData();
            formData.append("logo", self.logo_file);
            // Exit when the form isn't valid
            this.$validator.validateAll().then((result) => {
                if (result) {
                    //call to api to save the details
                    if (!self.integratorModel.id) {
                        // Add section
                        if (self.logo_file) {
                            api.importIntegratorLogo(formData).then(
                                (response) => {
                                    self.integratorModel.integrator_logo =
                                        response.data;
                                    self.addIntegrator(self);
                                }
                            );
                        } else {
                            self.addIntegrator(self);
                        }
                    } else {
                        // Edit Section
                        if (self.logo_file) {
                            api.importIntegratorLogo(formData).then(
                                (response) => {
                                    self.integratorModel.integrator_logo =
                                        response.data;
                                    self.editIntegratorApi(self);
                                }
                            );
                        } else {
                            self.editIntegratorApi(self);
                        }
                    }
                }
            });
        },
        searchIntegrator() {
            var self = this;

            var validate = false;

            if (self.integrator_name.trim() != "") {
                if (self.integrator_name.trim().length < 3) {
                    validate = true;
                } else {
                    validate = false;
                }
            } else if (self.integrator_id.trim() != "") {
                validate = false;
            } else {
                validate = true;
            }

            if (validate) {
                error(
                    "Please provide Integrator Name (Min 3 chars) OR Integrator ID"
                );
                return false;
            }

            self.loadDT();
        },
        loadDT() {
            var self = this;
            var request = {
                integrator_name: self.integrator_name,
                integrator_id: self.integrator_id,
            };
            self.loading = true;
            api.searchIntegrators(request)
                .then(function (response) {
                    if (response.code == 200) {
                        self.allIntegrators = response.data;
                        self.loading = false;
                    } else {
                        error(response.message);
                        self.loading = false;
                    }
                })
                .catch(function (error) {
                    error(error);
                    self.loading = false;
                });
        },
        clear(e) {
            var self = this;
            self.logo_fileName = "Choose File";
            self.$refs.logo_file.value = null;
            e.preventDefault();
        },
        handleFileUpload() {
            let self = this;
            self.logo_file = self.$refs.logo_file.files[0];
            self.logo_fileName = self.$refs.logo_file.files[0].name;
            self.logo_fileName = self.$refs.logo_file.files[0].name;
            self.upload_changed = true;
            self.logo_image=null
        },
        addIntegrator(self) {
            api.addIntegrator(self.integratorModel)
                .then((response) => {
                    if (response.code == 200) {
                        success(response.message);
                        self.$bvModal.hide("integrator-modal");
                        self.resetModal();
                        self.loadDT();
                    } else {
                        error(response.message);
                    }
                })
                .catch((err) => {
                    error(err.response.data.message);
                });
        },
        editIntegratorApi(self) {
            api.editIntegrator(self.integratorModel)
                .then((response) => {
                    if (response.code == 200) {
                        success(response.message);
                        self.id = null;
                        self.$bvModal.hide("integrator-modal");
                        self.store_ids = null;
                        self.resetModal();
                        self.loadDT();
                    } else {
                        error(response.message);
                    }
                })
                .catch((err) => {
                    error(err.response.data.message);
                });
        },
    },
    mounted() {
        var self = this;
        document.title = "CanPay - Integrator";
        setTimeout(function () {
            self.loadDT();
        }, 1000);
    },
};
</script>
