<?php

namespace App\Http\Middleware;

use Closure;
use Exception;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use JWTAuth;
use <PERSON>mon\JWTAuth\Http\Middleware\BaseMiddleware;

class JwtMiddleware extends BaseMiddleware
{

    /**
     * JWT Middleware checks for the JWT provided as Authorization token in the APIs
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        //FIXME - Need to fix with Animesh
        try {
            //Access token from the request
            $token = JWTAuth::parseToken();
            //Try authenticating user
            $user = $token->authenticate();
        } catch (Exception $e) {
            if ($e instanceof \Tymon\JWTAuth\Exceptions\TokenInvalidException) {
                Log::debug(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Invalid token.");
                $message = trans('message.invalid_token');
                return renderResponse(UNAUTH, $message, null);
            } else if ($e instanceof \Tymon\JWTAuth\Exceptions\TokenExpiredException) {
                Log::debug(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Token expired.");
                $message = trans('message.token_expired');
                return renderResponse(UNAUTH, $message, null);
            } else {
                Log::debug(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Authorization token not provided.");
                $message = trans('message.token_not_found');
                return renderResponse(UNAUTH, $message, null);
            }
        }

        //If user was authenticated successfully.
        if (!$user) {
            Log::debug(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Invalid token.");
            $message = trans('message.invalid_token');
            return renderResponse(UNAUTH, $message, null);
        }

        return $next($request);
    }
}
