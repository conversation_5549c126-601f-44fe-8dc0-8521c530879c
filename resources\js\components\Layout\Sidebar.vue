<template>
  <!-- Main Sidebar Container -->
  <aside class="main-sidebar sidebar-dark-primary elevation-4">
    <!-- Brand Logo -->
    <router-link to="/dashboard" class="brand-link nav-link">
      <img
        :src="'/img/logo-white.png'"
        alt="CanPay Logo"
        class="brand-image"
        style="opacity: 0.8"
      />
      <span class="brand-text font-weight-light">CanPay Admin</span>
    </router-link>

    <!-- Sidebar -->
    <div class="sidebar scroll-style-3">
      <!-- Sidebar user panel (optional) -->
      <div class="user-panel mt-3 pb-3 mb-3 d-flex">
        <div class="image">
          <b-avatar
            variant="success"
            :text="name_initials"
            class="mr-3"
          ></b-avatar>
        </div>
        <div class="info">
          <a
            href="#collapseOne"
            class="d-block accordion-toggle collapsed"
            data-toggle="collapse"
          >
            {{ full_name }} &nbsp;
            <i class="fa fa-caret-down"></i>
          </a>
          <div id="collapseOne" class="collapse">
            <ul
              class="nav nav-pills nav-sidebar flex-column"
              data-widget="treeview"
              role="menu"
              data-accordion="false"
            >
              <li class="nav-item" style="margin-left: -17px; margin-top: 10px">
                <router-link to="/userProfile" class="nav-link">
                  <i class="nav-icon fas fa-edit"></i>
                  <p>Edit Profile</p>
                </router-link>
              </li>
              <li
                class="nav-item"
                style="margin-left: -17px; margin-top: 10px"
                v-if="user.role_name == constants.role_super_admin"
              >
                <router-link to="/settingsfd01952d34f7" class="nav-link">
                  <i class="nav-icon fas fa-cog"></i>
                  <p>Settings (Beta)</p>
                </router-link>
              </li>
              <li class="nav-item" style="margin-left: -17px; margin-top: 10px">
                <a class="nav-link" @click="logout" style="cursor: pointer">
                  <i class="nav-icon fas fa-power-off"></i>
                  <p>Logout</p>
                </a>
              </li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Sidebar Menu -->
      <nav class="mt-2">
        <ul
          class="nav nav-pills nav-sidebar flex-column"
          data-widget="treeview"
          role="menu"
          data-accordion="false"
        >
          <!-- Add icons to the links using the .nav-icon class
          with font-awesome or any other icon font library-->

          <li class="nav-item">
            <router-link to="/dashboard" class="nav-link">
              <i class="nav-icon fas fa-columns"></i>
              <p>Dashboard</p>
            </router-link>
          </li>
          <li
            class="nav-item"
            v-if="user.role_name !== constants.role_report_admin"
          >
            <router-link to="/alertDashboard" class="nav-link">
              <i class="nav-icon fas fa-bell"></i>
              <p>Alert Dashboard</p>
            </router-link>
          </li>
          <li
            class="nav-item has-treeview"
            v-if="user.role_name !== constants.role_report_admin"
          >
            <a href="javascript:void(0);" class="nav-link">
              <i class="nav-icon fas fa-warehouse"></i>
              <p>
                Merchant
                <i class="right fas fa-angle-left"></i>
              </p>
            </a>
            <ul class="nav nav-treeview">
              <li class="nav-item">
                <router-link to="/corporateParent" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Corporate Parents</p>
                </router-link>
              </li>
              <li
                class="nav-item"
                v-if="user.role_name !== constants.role_helpdesk"
              >
                <router-link to="/importManager" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Import Merchants</p>
                </router-link>
              </li>
              <li
                class="nav-item"
                v-if="user.role_name !== constants.role_helpdesk"
              >
                <router-link to="/importCorporateParent" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Import Corporate Parents</p>
                </router-link>
              </li>
              <li
                class="nav-item"
                v-if="user.role_name !== constants.role_helpdesk"
              >
                <router-link to="/merchants" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Registered Merchants</p>
                </router-link>
              </li>
              <li
                class="nav-item"
                v-if="user.role_name !== constants.role_helpdesk"
              >
                <router-link to="/petitions" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>All Petitions</p>
                </router-link>
              </li>
              <li class="nav-item">
                <router-link to="/stores" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>All Stores</p>
                </router-link>
              </li>
              <li class="nav-item">
                <router-link to="/StoreLocator" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Store Locator</p>
                </router-link>
              </li>
              <li
                class="nav-item"
                v-if="user.role_name !== constants.role_helpdesk"
              >
                <router-link to="/enableDisableStores" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Enable/Disable Stores</p>
                </router-link>
              </li>
              <li
                class="nav-item"
                v-if="user.role_name !== constants.role_helpdesk"
              >
                <router-link to="/apiKeys" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Store API Keys</p>
                </router-link>
              </li>
            </ul>
          </li>

          <li
            class="nav-item has-treeview"
          >
            <a href="javascript:void(0);" class="nav-link">
              <i class="nav-icon fas fa-users"></i>
              <p>
                Consumers
                <i class="right fas fa-angle-left"></i>
              </p>
            </a>
            <ul class="nav nav-treeview">
              <li class="nav-item" v-if="user.role_name !== constants.role_report_admin">
                <router-link to="/consumers" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Consumers</p>
                </router-link>
              </li>
              <li class="nav-item" v-if="user.role_name !== constants.role_report_admin">
                <router-link to="/algohistory" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Algo Response</p>
                </router-link>
              </li>
              <li class="nav-item" v-if="user.role_name !== constants.role_report_admin && user.role_name !== constants.role_helpdesk">
                <router-link to="/suspectedconsumers" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Suspected Consumers</p>
                </router-link>
              </li>
              <li class="nav-item" v-if="user.role_name !== constants.role_report_admin">
                <router-link to="/consumerviewbalance" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Consumer View Balance</p>
                </router-link>
              </li>
              <li class="nav-item" v-if="user.role_name !== constants.role_report_admin">
                <router-link to="/consumersearch" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Consumer Global Search</p>
                </router-link>
              </li>
              <li class="nav-item" v-if="user.role_name !== constants.role_report_admin">
                <router-link to="/banklinkfailure" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Bank Link Failure</p>
                </router-link>
              </li>
              <li class="nav-item" v-if="user.role_name !== constants.role_report_admin">
                <router-link to="/v1edit" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>V1 Consumer Details Edit</p>
                </router-link>
              </li>
              <li class="nav-item" v-if="user.role_name !== constants.role_report_admin && user.role_name !== constants.role_helpdesk">
                <router-link to="/accountownerinfo" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Manage Account Owner Info</p>
                </router-link>
              </li>
              <li class="nav-item has-treeview">
                <a href="javascript:void(0);" class="nav-link">
                  <i class="nav-icon fas fa-edit"></i>
                  <p>
                    Manual Identity Review
                    <i class="right fas fa-angle-left"></i>
                  </p>
                </a>
                <ul class="nav nav-treeview">
                  <li class="nav-item" v-if="user.role_name !== constants.role_report_admin">
                    <router-link to="/pending" class="nav-link">
                      <i class="fa fa-hourglass-half nav-icon"></i>
                      <p>Pending</p>
                    </router-link>
                  </li>
                  <li class="nav-item" v-if="user.role_name !== constants.role_report_admin">
                    <router-link to="/approved" class="nav-link">
                      <i class="fa fa-check-circle nav-icon"></i>
                      <p>Approved</p>
                    </router-link>
                  </li>
                  <li class="nav-item" v-if="user.role_name !== constants.role_report_admin">
                    <router-link to="/archived" class="nav-link">
                      <i class="fa fa-archive nav-icon"></i>
                      <p>Archived</p>
                    </router-link>
                  </li>
                  <li class="nav-item" v-if="user.role_name !== constants.role_report_admin && user.role_name !== constants.role_helpdesk">
                    <router-link to="/suspectedfraud" class="nav-link">
                      <i class="fa fa-user-times nav-icon"></i>
                      <p>Suspected Fraud</p>
                    </router-link>
                  </li>
                </ul>
              </li>

              <li class="nav-item has-treeview" v-if="user.role_name !== constants.role_report_admin">
                <a href="javascript:void(0);" class="nav-link">
                  <i class="nav-icon fas fa-edit"></i>
                  <p>
                    Bank Statement Review
                    <i class="right fas fa-angle-left"></i>
                  </p>
                </a>
                <ul class="nav nav-treeview">
                  <li class="nav-item">
                    <router-link
                      to="/consumerdocumentverifypending"
                      class="nav-link"
                    >
                      <i class="fa fa-hourglass-half nav-icon"></i>
                      <p>Pending</p>
                    </router-link>
                  </li>
                  <li class="nav-item">
                    <router-link
                      to="/consumerdocumentverifyretake"
                      class="nav-link"
                    >
                      <i class="fa fa-redo nav-icon"></i>
                      <p>Retake</p>
                    </router-link>
                  </li>
                  <li class="nav-item">
                    <router-link
                      to="/consumerdocumentverifyarchive"
                      class="nav-link"
                    >
                      <i class="fa fa-archive nav-icon"></i>
                      <p>Archived</p>
                    </router-link>
                  </li>
                </ul>
              </li>
              <li class="nav-item has-treeview" v-if="user.role_name !== constants.role_report_admin">
                <router-link to="/v1manualreview" class="nav-link">
                  <i class="nav-icon fas fa-edit"></i>
                  <p>V1 Manual Review</p>
                </router-link>
              </li>
              <li class="nav-item has-treeview" v-if="user.role_name !== constants.role_report_admin">
                <a href="javascript:void(0);" class="nav-link">
                  <i class="nav-icon fas fa-edit"></i>
                  <p>
                    Global Radar Review
                    <i class="right fas fa-angle-left"></i>
                  </p>
                </a>
                <ul class="nav nav-treeview">
                  <li class="nav-item">
                    <router-link to="/globalRadarPending" class="nav-link">
                      <i class="fa fa-hourglass-half nav-icon"></i>
                      <p>Pending</p>
                    </router-link>
                  </li>
                  <li class="nav-item">
                    <router-link to="/globalRadarApproved" class="nav-link">
                      <i class="fa fa-check-circle nav-icon"></i>
                      <p>Approved</p>
                    </router-link>
                  </li>
                  <li class="nav-item">
                    <router-link to="/globalRadarArchived" class="nav-link">
                      <i class="fa fa-archive nav-icon"></i>
                      <p>Archived</p>
                    </router-link>
                  </li>
                </ul>
              </li>
            </ul>
          </li>
          <li class="nav-item has-treeview">
            <a href="javascript:void(0);" class="nav-link">
              <i class="nav-icon fas fa-exchange-alt"></i>
              <p>
                Return Transactions
                <i class="right fas fa-angle-left"></i>
              </p>
            </a>
            <ul class="nav nav-treeview">
              <li
                class="nav-item"
                v-if="user.role_name !== constants.role_helpdesk"
              >
                <router-link to="/returnDashboard" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Return Dashboard</p>
                </router-link>
              </li>
              <li
                class="nav-item"
                v-if="user.role_name !== constants.role_helpdesk"
              >
                <router-link to="/returntransactions" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Return Transactions</p>
                </router-link>
              </li>
              <li
                class="nav-item"
                v-if="user.role_name == constants.role_helpdesk"
              >
                <router-link to="/consumerreturns" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Consumer Returns</p>
                </router-link>
              </li>
              <li
                class="nav-item"
                v-if="user.role_name !== constants.role_helpdesk"
              >
                <router-link to="/groupreturntransactions" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Group Return Transactions</p>
                </router-link>
              </li>
              <li
                class="nav-item"
                v-if="
                  user.role_name !== constants.role_helpdesk &&
                  user.role_name !== constants.role_report_admin
                "
              >
                <router-link to="/returntransactionsemail" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Return Transactions Email</p>
                </router-link>
              </li>
              <li
                class="nav-item"
                v-if="user.role_name !== constants.role_helpdesk"
              >
                <router-link to="/finicityreturns" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Finicity R01 Returns</p>
                </router-link>
              </li>
              <li
                class="nav-item"
                v-if="user.role_name !== constants.role_helpdesk"
              >
                <router-link to="/manualbankingreturns" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Manual Banking Returns</p>
                </router-link>
              </li>
              <li
                class="nav-item"
                v-if="user.role_name !== constants.role_helpdesk"
              >
                <router-link to="/manualreviewreturns" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Manual Review Returns</p>
                </router-link>
              </li>
            </ul>
          </li>
          <li class="nav-item has-treeview">
            <a href="javascript:void(0);" class="nav-link">
              <i class="nav-icon fas fa-cog"></i>
              <p>
                Reward Wheel
                <i class="right fas fa-angle-left"></i>
              </p>
            </a>
            <ul class="nav nav-treeview">
              <li
                class="nav-item"
                v-if="
                  user.role_name == constants.role_super_admin
                "
              >
                <router-link to="/rewardWheel" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Reward Wheel Master</p>
                </router-link>
              </li>
              <li
                class="nav-item"
                v-if="
                  user.role_name == constants.role_super_admin
                "
              >
                <router-link to="/rewardWheelsearch" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Reward Wheel Search</p>
                </router-link>
              </li>
              <li
                class="nav-item"
                v-if="
                  user.role_name == constants.role_super_admin
                "
              >
                <router-link to="/rewardwheelsendinvitation" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Send Invitation</p>
                </router-link>
              </li>
              <li
                class="nav-item"
                v-if="
                  user.role_name == constants.role_super_admin
                "
              >
                <router-link to="/invitationstatus" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Invitation Status</p>
                </router-link>
              </li>
              <li
                class="nav-item"
                v-if="
                  user.role_name == constants.role_super_admin
                "
              >
                <router-link to="/rewardwheelreport" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Report</p>
                </router-link>
              </li>

              <li
                class="nav-item"
                v-if="
                  user.role_name == constants.role_super_admin
                "
              >
                <router-link to="/probabilitytest" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Probability Test</p>
                  </router-link></li>
                <li
                class="nav-item"
                v-if="
                  user.role_name == constants.role_super_admin
                "
              >
                <router-link to="/jackpotreset" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Base Values</p>
                </router-link>
              </li>
            </ul>
          </li>
          <li class="nav-item has-treeview">
            <a href="javascript:void(0);" class="nav-link">
              <i class="nav-icon fas fa-clipboard"></i>
              <p>
                Transactions
                <i class="right fas fa-angle-left"></i>
              </p>
            </a>
            <ul class="nav nav-treeview">
              <li class="nav-item">
                <router-link to="/transactions" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>All Transactions</p>
                </router-link>
              </li>
              <li class="nav-item">
                <router-link to="/consumerTransactions" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Consumer Transactions</p>
                </router-link>
              </li>
              <li class="nav-item" v-if="user.role_name !== constants.role_report_admin">
                <router-link to="/voidTransactions" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>POS Void Transactions</p>
                </router-link>
              </li>
              <li class="nav-item">
                <router-link to="/deliveryfeereport" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Delivery Fee Report</p>
                </router-link>
              </li>
              <li class="nav-item">
                <router-link to="/deliverysettlementreport" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Delivery Settlement Report</p>
                </router-link>
              </li>
              <!-- Not needed because we use the ACH report -->
              <!-- <li
                class="nav-item"
                v-if="user.role_name == constants.role_super_admin || user.role_name == constants.role_admin"
              >
                <router-link to="/transactionReport" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Transaction Report</p>
                </router-link>
              </li> -->

            </ul>
          </li>

          <li class="nav-item has-treeview">
            <a href="javascript:void(0);" class="nav-link">
              <i class="nav-icon fas fa-clipboard"></i>
              <p>
                Remote Payment
                <i class="right fas fa-angle-left"></i>
              </p>
            </a>
            <ul class="nav nav-treeview">
              <li class="nav-item">
                <router-link to="/integrator" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Integrator Master</p>
                </router-link>
              </li>
              <li class="nav-item">
                <router-link to="/ecommerceCategory" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Canpay Internal Version</p>
                </router-link>
              </li>
              <li
                class="nav-item"
                v-if="user.role_name !== constants.role_helpdesk"
              >
                <router-link to="/merchantKeyMaster" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Merchant Key Master</p>
                </router-link>
              </li>
            </ul>
          </li>
          <li class="nav-item has-treeview">
            <a href="javascript:void(0);" class="nav-link">
              <i class="nav-icon fas fa-clipboard"></i>
              <p>
                Ecommerce Transaction
                <i class="right fas fa-angle-left"></i>
              </p>
            </a>
            <ul class="nav nav-treeview">

              <li class="nav-item" v-if="user.role_name !== constants.role_report_admin">
                <router-link to="/ecommerceVoidTransactions" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Void Transactions</p>
                </router-link>
              </li>

              <li class="nav-item">
                <router-link to="/remotepaytransactionreport" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Remote Pay Transaction</p>
                </router-link>
              </li>

              <li class="nav-item">
                <router-link to="/transactionmodificationreason" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Modify Reason</p>
                </router-link>
              </li>
              <li class="nav-item">
                <router-link to="/transactionmodificationcustomreason" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Modify Custom Reason</p>
                </router-link>
              </li>
            </ul>
          </li>
          <li
            class="nav-item has-treeview"
            v-if="user.role_name !== constants.role_report_admin"
          >
            <a href="javascript:void(0);" class="nav-link">
              <i class="nav-icon fas fa-user"></i>
              <p>
                Admin
                <i class="right fas fa-angle-left"></i>
              </p>
            </a>
            <ul class="nav nav-treeview">
              <li
                class="nav-item"
                v-if="user.role_name !== constants.role_helpdesk"
              >
                <router-link to="/canpayUsers" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Canpay Users</p>
                </router-link>
              </li>
              <li
                class="nav-item"
                v-if="user.role_name !== constants.role_helpdesk"
              >
                <router-link to="/emails" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Email Templates</p>
                </router-link>
              </li>
              <li
                class="nav-item"
                v-if="user.role_name !== constants.role_helpdesk"
              >
                <router-link to="/notifications" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Global Notifications</p>
                </router-link>
              </li>
              <li class="nav-item">
                <router-link to="/importholidaylists" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Holiday List</p>
                </router-link>
              </li>
              <li class="nav-item">
                <router-link to="/releasenote" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Release Note</p>
                </router-link>
              </li>
              <li class="nav-item">
                <router-link to="/whitelistRoutingNumbers" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Whitelist Routing Numbers</p>
                </router-link>
              </li>
               <li class="nav-item">
                <router-link to="/blacklistedaccountnumber" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Blacklisted Account Number</p>
                </router-link>
              </li>
               <li class="nav-item">
                <router-link to="/manualbanklinkrestrictedroutingnumbers" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Manual Bank Link Restrictions</p>
                </router-link>
              </li>
                <li class="nav-item">
                <router-link to="/validaccountnumber" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Valid Account Number</p>
                </router-link>
              </li>
              <li class="nav-item">
                <router-link to="/postConsumerReturn" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Post V1 Consumer Return</p>
                </router-link>
              </li>
              <li class="nav-item">
                <router-link to="/releaseConsumers" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Release Consumer</p>
                </router-link>
              </li>
              <li class="nav-item">
                <router-link to="/unknownreturncodes" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Manage Unknown Return Codes</p>
                </router-link>
              </li>
              <li class="nav-item">
                <router-link to="/unknownroutingnumbers" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Manage Unknown Routing Numbers</p>
                </router-link>
              </li>
              <li class="nav-item">
                <router-link to="/notificationlogs" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Manage Notifications</p>
                </router-link>
              </li>
              <li class="nav-item">
                <router-link to="/banklist" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Manage Bank</p>
                </router-link>
              </li>
              <li class="nav-item">
                <router-link to="/banksolutionlist" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Manage Banking Solution</p>
                </router-link>
              </li>
              <li class="nav-item">
                <router-link to="/importAkoyaProviderID" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Import Akoya Provider</p>
                </router-link>
              </li>
              <li class="nav-item">
                <router-link to="/importmxinstitutioncode" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Import MX Institution Code</p>
                </router-link>
              </li>
              <li class="nav-item">
                <router-link to="/mxinstitution" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>MX Institutions</p>
                </router-link>
              </li>
              <li class="nav-item">
                <router-link to="/bankreportsurvey" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Missing Bank Survey</p>
                </router-link>
              </li>
              <li class="nav-item">
                <router-link to="/mxidentificationhistory" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Mx Identification History</p>
                </router-link>
              </li>
              <li class="nav-item">
                <router-link to="/healthchecks" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Health Checks</p>
                </router-link>
              </li>
              <!-- <li class="nav-item" v-if="user.role_name == constants.role_super_admin || user.role_name == constants.role_admin">
                <router-link to="/importTransactionTraceNumber" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Import Transaction Trace No.</p>
                </router-link>
              </li> -->
              <li
                v-if="user.role_name !== constants.role_helpdesk"
                class="nav-item has-treeview"
              >
                <a href="javascript:void(0);" class="nav-link">
                  <i class="nav-icon fas fa-database"></i>
                  <p>
                    V1 Data Migration
                    <i class="right fas fa-angle-left"></i>
                  </p>
                </a>
                <ul class="nav nav-treeview">
                  <li class="nav-item">
                    <router-link to="/importConsumers" class="nav-link">
                      <i class="far fa-circle nav-icon"></i>
                      <p>Import Consumers</p>
                    </router-link>
                  </li>
                  <li class="nav-item">
                    <router-link to="/importMerchants" class="nav-link">
                      <i class="far fa-circle nav-icon"></i>
                      <p>Import Merchants</p>
                    </router-link>
                  </li>
                  <li class="nav-item">
                    <router-link to="/importTransactions" class="nav-link">
                      <i class="far fa-circle nav-icon"></i>
                      <p>Import Transactions</p>
                    </router-link>
                  </li>
                  <li class="nav-item">
                    <router-link
                      to="/importVoidedTransactions"
                      class="nav-link"
                    >
                      <i class="far fa-circle nav-icon"></i>
                      <p>Import Voided Transactions</p>
                    </router-link>
                  </li>
                </ul>
              </li>
            </ul>
          </li>
          <li
            class="nav-item has-treeview"
            v-if="user.role_name == constants.role_super_admin || user.role_name == constants.role_admin"
          >
            <a href="javascript:void(0);" class="nav-link">
              <i class="nav-icon fas fa-file"></i>
              <p>
                ACH
                <i class="right fas fa-angle-left"></i>
              </p>
            </a>
            <ul class="nav nav-treeview">
              <li
                class="nav-item"
              >
                <router-link to="/achTransactionReport" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Transaction Report</p>
                </router-link>
              </li>
                <li class="nav-item">
                <router-link to="/achfilelists" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Download ACH File</p>
                </router-link>
              </li>
              <li class="nav-item">
                <router-link to="/achreturns" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Return Report</p>
                </router-link>
              </li>
            </ul>
          </li>
          <li
            class="nav-item has-treeview"
            v-if="user.role_name !== constants.role_report_admin"
          >
            <a href="javascript:void(0);" class="nav-link">
              <i class="nav-icon fas fa-file"></i>
              <p>
                Fed Report
                <i class="right fas fa-angle-left"></i>
              </p>
            </a>
            <ul class="nav nav-treeview">
              <li class="nav-item">
                <router-link to="/importreturntransactions" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Import File</p>
                </router-link>
              </li>
              <li class="nav-item">
                <router-link to="/releaseconusmersfromreturn" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Release Consumer</p>
                </router-link>
              </li>
            </ul>
          </li>
          <li
            class="nav-item has-treeview"
            v-if="user.role_name !== constants.role_report_admin"
          >
            <a href="javascript:void(0);" class="nav-link">
              <i class="nav-icon fas fa-exchange-alt"></i>
              <p>
                Merchant Points <br> Program
              </p>
              <p class="beta-badge">Beta</p>
              <i class="right fas fa-angle-left"></i>
            </a>
            <ul class="nav nav-treeview">
              <li class="nav-item">
                <router-link to="/pointsbackmaster" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Points Program Master</p>
                </router-link>
              </li>
              <li class="nav-item">
                <router-link to="/pointsbackprogramfeedback" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Merchant Feedback</p>
                </router-link>
              </li>
              <li class="nav-item">
                <router-link to="/pointsbackreport" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Points Program Report</p>
                </router-link>
              </li>
              <!-- <li class="nav-item">
                <router-link to="/pointsbackprogramreportold" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Points Program Report</p>
                </router-link>
              </li> -->
            </ul>
          </li>
          <li
            class="nav-item has-treeview"
            v-if="user.role_name !== constants.role_report_admin"
          >
            <a href="javascript:void(0);" class="nav-link">
              <i class="nav-icon fas fa-table"></i>
              <p>
                Merchant Reports
                <i class="right fas fa-angle-left"></i>
              </p>
            </a>
            <ul class="nav nav-treeview">
              <li class="nav-item">
                <router-link to="/merchantTransactionReport" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Transaction Report</p>
                </router-link>
              </li>
              <li class="nav-item">
                <router-link to="/settlementReport" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Settlement/Fees Report</p>
                </router-link>
              </li>
              <li class="nav-item">
                <router-link to="/discountedfeereport" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Reduced Transaction Fees Report</p>
                </router-link>
              </li>
              <li class="nav-item">
                <router-link to="/monthlySalesGrowth" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Monthly Sales Growth</p>
                </router-link>
              </li>
              <li class="nav-item">
                <router-link to="/storewiseMonthlySales" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Storewise Monthly Sales</p>
                </router-link>
              </li>
              <li class="nav-item">
                <router-link to="/merchantpointreport" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Merchant Point Report</p>
                </router-link>
              </li>
            </ul>
          </li>
<!-- Microbilt Error start -->
          <li
            class="nav-item has-treeview"
            v-if="user.role_name === constants.role_super_admin"
          >
          <!-- Microbilt Error Report -->
            <a href="javascript:void(0);" class="nav-link">
              <i class="nav-icon fas fa-solid fa-table"></i>
              <p>
                Microbilt Error Report
                <i class="right fas fa-angle-left"></i>
              </p>
            </a>
            <ul class="nav nav-treeview">
            <!-- consumer declined -->
              <li class="nav-item"  v-if="user.role_name == constants.role_super_admin">
                <router-link to="/microbiltConsumerDeclinedForBankValidation" class="nav-link">
                  <i class="far fa-circle nav-icon"></i>
                  <p>Consumer Declined</p>
                </router-link>
              </li>

            </ul>
          </li>
<!-- Microbilt Error end -->
        <li
            class="nav-item"
            v-if="user.role_name == constants.role_super_admin"
        >
        <router-link to="/MicrobiltReview" class="nav-link">
            <a href="javascript:void(0);">
              <i class="nav-icon fas fa-solid fa-table"></i>
              <p>
                Microbilt Bank Review
              </p>
            </a>
        </router-link>
        </li>
        </ul>

      </nav>
      <!-- /.sidebar-menu -->
    </div>
    <!-- /.sidebar -->
  </aside>
</template>
<script>
import api from "@/api/user.js";
import * as AdminLte from "../../../../public/js/adminlte.js";
import commonConstants from "@/common/constant.js";
export default {
  data() {
    return {
      user: JSON.parse(localStorage.getItem("user")),
      show: true,
      full_name: null,
      name_initials: null,
      constants: commonConstants,
    };
  },
  methods: {
    logout() {
      api
        .logoutUser(self.userModel)
        .then((response) => {
          if (response.code == 200) {
            localStorage.removeItem("token");
            localStorage.removeItem("user");
            localStorage.clear();
            success("Logged out successfully.");
            document.title = "CanPay Admin";
            this.$router.push("/login");
          } else {
            error(response.message);
          }
        })
        .catch((err) => {
          error(err.response.data.message);
        });
    },
  },
  mounted() {
    var self = this;
    if (self.user.middle_name) {
      self.full_name =
        self.user.first_name +
        " " +
        self.user.middle_name +
        " " +
        self.user.last_name;
    } else {
      self.full_name = self.user.first_name + " " + self.user.last_name;
    }
    let matches = self.full_name.match(/\b(\w)/g);
    self.name_initials = matches.join("");
    $('[data-widget="treeview"]').each(function () {
      AdminLte.Treeview._jQueryInterface.call($(this), "init");
    });
  },
};
</script>
<style scoped>
.link-property:hover{
  color:white !important;
}
.beta-badge {
  position: absolute;
  right: 2rem;
  top: 0.7rem;
  margin-top: -10px !important;
  background-color: #149240;
  color: #ffffff;
  padding: 1px 3px;
  font-size: 0.75rem;
  border-radius: 3px;
  font-weight: bold;
  animation: blink 1s infinite;
}
</style>
