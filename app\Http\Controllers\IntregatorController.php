<?php

namespace App\Http\Controllers;

use App\Exports\StoreMerchantKeysExport;
use App\Models\EcommerceCategoryCodes;
use App\Models\EcommerceIntegrator;
use App\Models\EcommerceMerchantKey;
use App\Models\MerchantStores;
use App\Models\StatusMaster;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;

class IntregatorController extends Controller
{
    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    /**
     * getUnknownReturnReasonList
     * Listing for Ecommerce category Codes
     * @param  mixed $request
     * @return void
     */
    public function getAllCategory(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Ecommerce Category search started...");
        // Validating input request
        $this->validate($request, [
            // 'category_name' => VALIDATION_REQUIRED_WITHOUT_ALL . ':category_id,nomenclature',
            // 'category_id' => VALIDATION_REQUIRED_WITHOUT_ALL . ':category_name,nomenclature',
            // 'nomenclature' => VALIDATION_REQUIRED_WITHOUT_ALL . ':category_name,category_id',
        ]);

        //Search with in Corpoarate Parents
        $categories = $this->_getCategorySearch($request);

        $message = 'Ecommerce Category fetched';
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Ecommerce Category search complete.");
        return renderResponse(SUCCESS, $message, $categories);
    }

    public function _getCategorySearch($request){

        // Main Query
        $categories = EcommerceCategoryCodes::select('ecommerce_category_codes.*');

        if (strlen(trim($request['category_name'])) >= 3) {
            $categories = $categories->where(function ($q) use ($request) {
                $q->where('ecommerce_category_codes.category_name', 'like', '%' . $request->category_name . '%');
            });
        }

        if (strlen(trim($request['category_code'])) > 0) {
            $categories = $categories->where(function ($q) use ($request) {
                $q->where('ecommerce_category_codes.category_code', $request->category_code);
            });
        }

        if (strlen(trim($request['nomenclature']))  > 0) {
            $categories = $categories->where(function ($q) use ($request) {
                $q->where('ecommerce_category_codes.nomenclature', $request->nomenclature);
            });
        }

        if (strlen(trim($request['category_name'])) >= 3 && strlen(trim($request['category_code']))  > 0 && strlen(trim($request['nomenclature']))  > 0) {
            $categories = $categories->get();
        } else if (strlen(trim($request['category_name'])) >= 3) {
            $categories = $categories->get();
        } else if (strlen(trim($request['category_code']))  > 0) {
            $categories = $categories->get();
        } else if (strlen(trim($request['nomenclature']))  > 0) {
            $categories = $categories->get();
        }else{
            $categories = $categories->latest()->limit(10)->get();
        }

        $categoriesArr = array();
        if (!empty($categories)) {
            // Creating array to show the values in frontend
            foreach ($categories as $integrator) {
                $data = [];
                $data['category_name'] = $integrator->category_name;
                $data['category_code'] = $integrator->category_code;
                $data['nomenclature'] = $integrator->nomenclature;
                $data['created_at'] = date('m-d-Y h:i A', strtotime($integrator->created_at));
                $data['id'] = $integrator->id;
                array_push($categoriesArr, $data);
            }
        } else {
            $data = [];
        }
      
        return $categoriesArr;
    }

    public function addCategory(Request $request)
    {

        $rule = array(
            'category_name' => [VALIDATION_REQUIRED, 'unique:ecommerce_category_codes,category_name'],
            'category_code' => VALIDATION_REQUIRED,
            'nomenclature' => VALIDATION_REQUIRED,
        );
        $this->__validate($request->all(), $rule);

        DB::beginTransaction();
        try {

            $ecomCategory = new EcommerceCategoryCodes();
            $ecomCategory->category_name = $request->get('category_name');
            $ecomCategory->category_code = $request->get('category_code');
            $ecomCategory->nomenclature = $request->get('nomenclature');
            $ecomCategory->save();

            DB::commit();
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "");
            $message = trans('message.ecom_category_creation_success');
            // API Response returned with 200 status
            return renderResponse(SUCCESS, $message, null);
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured during Category Addition. ", [EXCEPTION => $e]);
            DB::rollback();
            $message = trans('message.ecom_category_creation_error');
            // Exception Returned
            return renderResponse(FAIL, $e, null);
        }
    }

    public function editCategory(Request $request)
    {
        $rule = array(
            'category_name' => [VALIDATION_REQUIRED, 'unique:ecommerce_category_codes,category_name,'. $request->get('id').',id'],
            'category_code' => VALIDATION_REQUIRED,
            'nomenclature' => VALIDATION_REQUIRED,
        );
        $this->__validate($request->all(), $rule);

        DB::beginTransaction();
        try {
            // Updation begins in User Table
            $ecomCategory = EcommerceCategoryCodes::find($request->get('id'));
            $ecomCategory->category_name = $request->get('category_name');
            $ecomCategory->category_code = $request->get('category_code');
            $ecomCategory->nomenclature = $request->get('nomenclature');
            $ecomCategory->save();
            DB::commit();
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "");
            $message = trans('message.ecom_category_updation_success');
            // API Response returned with 200 status
            return renderResponse(SUCCESS, $message, null);
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured during Category Updation. ", [EXCEPTION => $e]);
            DB::rollback();
            $message = trans('message.ecom_category_updation_error');
            // Exception Returned
            return renderResponse(FAIL, $message, null);
        }
    }

    public function getAllMerchants(Request $request)
    {
        $reatilers = new MerchantStores();
        if (strlen(trim($request->searchtxt))) {
            $reatilers = $reatilers->where('retailer', 'like', '%' . $request->searchtxt . '%')->get()->toArray();
        }

        $message = trans('message.merchant_fetch_success');
        return renderResponse(SUCCESS, $message, $reatilers);
    }

    public function getAllEcommerceCategory(Request $request)
    {
        $ecommerceCategories = EcommerceCategoryCodes::get()->toArray();
        $message = trans('message.ecom_category_fetch_success');
        return renderResponse(SUCCESS, $message, $ecommerceCategories);
    }

    public function getAllMerchantKey(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Merchant Key search started...");
        // Validating input request
        $this->validate($request, [
            'merchant_name' => VALIDATION_REQUIRED_WITHOUT_ALL . ':merchant_id,category_id,cp_id',
            'merchant_id' => VALIDATION_REQUIRED_WITHOUT_ALL . ':category_id,merchant_name,cp_id',
            'category_id' => VALIDATION_REQUIRED_WITHOUT_ALL . ':merchant_id,merchant_name,cp_id',
            'cp_id' => VALIDATION_REQUIRED_WITHOUT_ALL . ':merchant_id,merchant_name,category_id',
        ]);

        //Search with in Corpoarate Parents
        $merchantKeys = $this->_getMerchantKeySearch($request);
        if(!$request->has('is_export')){
            $message = trans('message.merchant_key_fetch_success');
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Merchant Key search started complete.");
            return renderResponse(SUCCESS, $message, $merchantKeys);
        }else{
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Merchant Key export complete.");
            return Excel::download(new StoreMerchantKeysExport($merchantKeys), 'store_merchant_keys' . date('m-d-Y H:i:s') . '.xlsx');
        }
    }

    /**
     * _getMerchantKeySearch
     * Fetch the Ecommerce categories
     * @param  mixed $searchArray
     * @return void
     */
    private function _getMerchantKeySearch($request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Merchant Key Search Started.");

        $merchantKeys = EcommerceMerchantKey::select(
            'ecommerce_merchant_keys.id',
            'ecommerce_merchant_keys.app_key',
            'ecommerce_merchant_keys.api_secret',
            'ecommerce_merchant_keys.status',
            'status_master.status as status_master',
            'merchant_stores.retailer',
            'merchant_stores.store_id',
            'ecommerce_category_codes.category_code',
            'ecommerce_integrators.integrator_id',
            'ecommerce_merchant_keys.created_at'
        )
            ->join('registered_merchant_master', 'registered_merchant_master.id', 'ecommerce_merchant_keys.merchant_id')
            ->join('merchant_stores', 'registered_merchant_master.id', 'merchant_stores.merchant_id')
            ->join('status_master', 'status_master.id', 'ecommerce_merchant_keys.status')
            ->join('ecommerce_category_codes', 'ecommerce_category_codes.id', 'ecommerce_merchant_keys.ecommerce_category_id')
            ->join('ecommerce_integrators', 'ecommerce_integrators.id', 'ecommerce_merchant_keys.ecommerce_integrator_id');

        if (strlen(trim($request['merchant_name'])) >= 3) {
            $merchantKeys = $merchantKeys->where(function ($q) use ($request) {
                $q
                    ->where('merchant_stores.retailer', 'like', '%' . $request->merchant_name . '%');
            });
        }

        if (strlen(trim($request['merchant_id'])) >= 3) {
            $merchantKeys = $merchantKeys->where(function ($q) use ($request) {
                $q
                    ->where('merchant_stores.store_id', 'like', '%' . $request->merchant_id . '%');
            });
        }
        
        if ($request['cp_id']) {
            $cp_id = $request['cp_id'];
            $merchantKeys = $merchantKeys->join('store_user_map', function ($join) use ($cp_id) {
                $join->on("store_user_map.store_id", "=", "merchant_stores.id");
                $join->where("store_user_map.user_id", $cp_id);
            });
        }

        if (strlen(trim($request['category_id']))) {
            $merchantKeys = $merchantKeys->where(function ($q) use ($request) {
                $q
                    ->where('ecommerce_merchant_keys.ecommerce_category_id', $request->category_id);
            });
        }

        if (strlen(trim($request['merchant_name'])) >= 3 || strlen(trim($request['merchant_id'])) >= 3 || strlen(trim($request['category_id'])) || $request['cp_id']) {
            $merchantKeys = $merchantKeys->get();
        }

        $merchantKeysArr = [];
        if (!empty($merchantKeys)) {
            foreach ($merchantKeys as $merchantKey) {
                $data = [];
                if(!$request->has('is_export')){
                    $data['id'] = $merchantKey->id;
                }
                $data['store_id'] = $merchantKey->store_id;
                $data['retailer'] = $merchantKey->retailer;
                $data['integrator_id'] = $merchantKey->integrator_id;
                $data['category_code'] = $merchantKey->category_code;
                $data['app_key'] = $merchantKey->app_key;
                $data['api_secret'] = $merchantKey->api_secret;
                $data['created_at'] = date('m-d-Y', strtotime($merchantKey->created_at));
                $data['status_master'] = $merchantKey->status_master;
                array_push($merchantKeysArr, $data);
            }
        } else {
            $merchantKeysArr = [];
        }

        return $merchantKeysArr;
    }

    public function addMerchantKey(Request $request)
    {
        
        $rule = array(
            'integratorSelectType' => VALIDATION_REQUIRED,
            'categorySelectType' => VALIDATION_REQUIRED,
            'retailer' => VALIDATION_REQUIRED . '| alpha_num',
        );
        $this->__validate($request->all(), $rule);
        if($request->get('integratorSelectType') == 'create'){
            $rule = array(
                'ecommerce_integrator_name' => [VALIDATION_REQUIRED, 'unique:ecommerce_integrators,ecommerce_integrator_name'],
                'location_type' => VALIDATION_REQUIRED,
            );
            $this->__validate($request->get('integratorModel'), $rule);
        }else{
            $rule = array(
                'selected_integrator' => VALIDATION_REQUIRED,
            );
            $this->__validate($request->all(), $rule);
        }
        if($request->get('categorySelectType') == 'create'){
            $rule = array(
                'category_name' => [VALIDATION_REQUIRED, 'unique:ecommerce_category_codes,category_name'],
                'category_code' => VALIDATION_REQUIRED,
                'nomenclature' => VALIDATION_REQUIRED,
            );
            $this->__validate($request->get('categoryModel'), $rule);
        }else{
            $rule = array(
                'selected_category' => VALIDATION_REQUIRED,
            );
            $this->__validate($request->all(), $rule);
        }
        DB::beginTransaction();
        try {
            if($request->get('integratorSelectType') == 'create'){
                $integrator = new EcommerceIntegrator();
                $integrator->ecommerce_integrator_name = $request->get('integratorModel')['ecommerce_integrator_name'];
                $integrator->location_type = $request->get('integratorModel')['location_type'];
                $integrator->allow_ecommerce_transaction = isset($request->get('integratorModel')['allow_ecommerce_transaction']) ? $request->get('integratorModel')['allow_ecommerce_transaction'] : 0;
                $integrator->allow_consumer_auth =  isset($request->get('integratorModel')['allow_consumer_auth']) ? $request->get('integratorModel')['allow_consumer_auth'] : 0;
                $integrator->integrator_logo = isset($request->get('integratorModel')['integrator_logo']) ? $request->get('integratorModel')['integrator_logo'] : null;
                $integrator->save();
                $ecommerce_integrator_id = $integrator->id;
            }else{
                $ecommerce_integrator_id = $request->get('selected_integrator');
                $ecommerce_integrator = EcommerceIntegrator::find($ecommerce_integrator_id);
                if(!$ecommerce_integrator){
                    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No ecommerce integrator found on your given input: " . $ecommerce_integrator_id);
                    $message = trans('message.ecom_integrator_not_found');
                    return renderResponse(FAIL, $message, null);
                }
            }
            if($request->get('categorySelectType') == 'create'){
                $ecomCategory = new EcommerceCategoryCodes();
                $ecomCategory->category_name = $request->get('categoryModel')['category_name'];
                $ecomCategory->category_code = $request->get('categoryModel')['category_code'];
                $ecomCategory->nomenclature = $request->get('categoryModel')['nomenclature'];
                $ecomCategory->save();   
                $ecommerce_category_id = $ecomCategory->id;
            }else{
                $ecommerce_category_id = $request->get('selected_category');
                $ecommerce_category = EcommerceCategoryCodes::find($ecommerce_category_id);
                if(!$ecommerce_category){
                    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No ecommerce category found on your given input: " . $ecommerce_category_id);
                    $message = trans('message.ecom_category_not_found');
                    return renderResponse(FAIL, $message, null);
                }
                
            }

            $store_details = MerchantStores::find($request->get('retailer'));
            if(!$store_details){
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No retailer found on your given input: " . $request->get('retailer'));
                $message = trans('message.retailer_not_found');
                return renderResponse(FAIL, $message, null);
            }

            $previousEcommerceMerchantKey = EcommerceMerchantKey::where('ecommerce_category_id', $ecommerce_category_id)
            ->where('merchant_id', $store_details->merchant_id);

            
            if (!$previousEcommerceMerchantKey->exists()) {

                $ecommerceCategory = EcommerceCategoryCodes::where('id', $ecommerce_category_id)->first();

                $ecomMerchantKey = new EcommerceMerchantKey();
                $ecomMerchantKey->merchant_id = $store_details->merchant_id;
                $ecomMerchantKey->app_key = $ecommerceCategory->nomenclature . generateRandomString((9 - strlen($ecommerceCategory->nomenclature)));
                $ecomMerchantKey->api_secret = generateRandomString(8);
                $ecomMerchantKey->ecommerce_category_id = $ecommerce_category_id;
                $ecomMerchantKey->ecommerce_integrator_id = $ecommerce_integrator_id;
                $ecomMerchantKey->status = getStatus('204');
                $ecomMerchantKey->save();

                $store_details->is_ecommerce = 1;
                $store_details->save();

                $response = [
                    'success' => true,
                    'message' => trans('message.ecom_merchant_key_creation_success'),
                ];
            } else {
                $response = [
                    'success' => false,
                    'message' => trans('message.ecom_merchant_key_duplicate_creation_fail'),
                ];
            }
            
            DB::commit();
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "");
            // API Response returned with 200 status
            if ($response['success'] == true) {
                return renderResponse(SUCCESS, $response['message'], null);
            } else {
                return renderResponse(FAIL, $response['message'], null);
            }
        } catch (\Exception $e) {
            dd($e);
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured during Merchant Key Add. ", [EXCEPTION => $e]);
            DB::rollback();
            $message = trans('message.ecom_merchant_key_creation_error');
            // Exception Returned
            return renderResponse(FAIL, $e, null);
        }
    }

    public function merchantKeyStatusChange(Request $request)
    {
        $rule = array(
            'merchant_key_id' => VALIDATION_REQUIRED,
            'status' => VALIDATION_REQUIRED,
        );
        $this->__validate($request->all(), $rule);

        DB::beginTransaction();
        try {

            $statusMaster = StatusMaster::where('status', $request->get('status'))->first();

            if ($statusMaster) {
                // Updation begins in User Table
                $merchantKey = EcommerceMerchantKey::find($request->get('merchant_key_id'));
                $merchantKey->status = $statusMaster->id;
                $merchantKey->save();

                $response = [
                    'success' => true,
                    'message' => trans('message.merchant_key_status_updation_success'),
                ];
            } else {
                $response = [
                    'success' => true,
                    'message' => trans('message.merchant_key_status_updation_error'),
                ];
            }

            DB::commit();
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "");

            if ($response['success'] == true) {
                return renderResponse(SUCCESS, $response['message'], null);
            } else {
                return renderResponse(FAIL, $response['message'], null);
            }

        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured during User (");
            DB::rollback();
            $message = trans('message.merchant_key_status_updation_error');
            // Exception Returned
            return renderResponse(FAIL, $message, null);
        }
    }
}
