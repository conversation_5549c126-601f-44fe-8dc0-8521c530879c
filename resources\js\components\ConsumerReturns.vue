<template>
<div>
  <div  v-if="loading">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px;">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Consumer Returns</h3>
                </div>

                <div class="card-body">
                <div class="row">
                  <div class="col-md-4">
                    <div class="form-group">
                      <multiselect
                        v-model="selectedConsumer"
                        placeholder="Select Consumer (Min 3 chars)"
                        id="consumer"
                        label="consumer_name"
                        :options="consumerList"
                        :loading="isLoading"
                        :internal-search="false"
                        @search-change="getConsumers"
                          >
                    </multiselect>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="form-control"
                        placeholder="Phone No."
                        id="phone_no"
                        v-model="phone_no"
                        autocomplete="off"
                      />
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="form-control"
                        placeholder="Email"
                        id="email"
                        v-model="email"
                        autocomplete="off"
                      />
                    </div>
                  </div>
                </div>

                <div class="row">
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="start-date form-control"
                        placeholder="Start Date"
                        id="start-date"
                        onkeydown="return false"
                        autocomplete="off"
                      />
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="end-date form-control"
                        placeholder="End Date"
                        id="end-date"
                        onkeydown="return false"
                        autocomplete="off"
                      />
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <label class="switch"><input class="enable-employee-login" type="checkbox" v-model="toggle_local_transaction_time_search"><span class="slider round"></span></label> Local Transaction Date
                    </div>
                  </div>
                </div>
              </div>
              <div class="card-footer">
                <button
                  type="button"
                  class="btn btn-success"
                  @click="generateReport(false)"
                >
                  Generate
                </button>
              </div>
                <!-- /.card-header -->
                <div class="card-body">
                  <div class="row">
                  <div class="col-12">
                    <b-table-simple
                      responsive
                      show-empty
                      bordered
                      sticky-header="800px"
                    >
                      <b-thead head-variant="light">
                        <b-tr>
                          <b-th class="text-center">Transaction Number</b-th>
                          <b-th class="text-center">Consumer</b-th>
                          <b-th class="text-center">Phone Number</b-th>
                          <b-th class="text-center">User Type</b-th>
                          <b-th class="text-center">Bank Link Type</b-th>
                          <b-th class="text-center">Current Purchase Power</b-th>
                          <b-th width="10%" class="text-center">Local Transaction Date</b-th>
                          <b-th class="text-center">Amount</b-th>
                          <b-th class="text-center">Return Reason Code</b-th>
                          <b-th class="text-center">Attempted Settlement </b-th>
                          <b-th class="text-center">Representment Status</b-th>
                          <b-th class="text-center">Represented On</b-th>
                          <b-th class="text-center">Expected Clearance</b-th>
                        </b-tr>
                      </b-thead>
                      <b-tbody v-for="(row, index) in report" :key="index">
                        <b-tr>
                          <b-td class="text-left text-gray">
                            <a style="color: white !important" class="btn btn-success" @click="viewTransactionDetails(row)">{{row.transaction_number}}</a></b-td>
                          <b-td class="text-left text-gray">{{
                            row.consumer_name
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.phone
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.user_type
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.bank_link_type
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.current_purchase_power
                          }}</b-td>
                           <b-td v-html="row.transaction_time" class="text-left text-gray"></b-td>
                           <b-td class="text-left text-gray">{{
                            row.total_amount
                          }}</b-td>
                          <b-td class="text-left text-gray">
                                <a style="color: white !important" class="btn btn-success " @click="detailsReason(row)">{{
                            row.reason_code
                          }} </a>

                          </b-td>
                          <b-td class="text-left text-gray">{{
                            row.represent_count
                          }}</b-td>
                          <b-td class="text-left text-gray">{{row.status}}</b-td>
                          <b-td class="text-left text-gray"><span v-if="row.status == 'Pending'" v-html="row.represented_on"></span></b-td>
                          <b-td class="text-left text-gray"><span v-if="row.status == 'Pending'" >{{row.expected_clearance}}</span></b-td>
                        </b-tr>
                      </b-tbody>
                    </b-table-simple>
                  </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>

    <!-- Transaction Return Reason Modal Start -->
    <b-modal
      id="modal-approved-review"
      ref="modal-approved-review"
      hide-footer
      title="Return Reason"
      :no-close-on-esc="false"
      :no-close-on-backdrop="false"
    >
    <div class="row mb-2">
        <div class="col-12">
            <b> Return Code </b> : {{return_code}}
        </div>
    </div>
    <div class="row mb-2">
        <div class="col-12">
            <b> Customer Name </b> : {{customer_name}}
        </div>
    </div>
    <div class="row mb-2">
        <div class="col-12">
            <b> Transaction ID </b> : {{transaction_id}}
        </div>
    </div>
    <div class="row mb-2">
        <div class="col-12">
            <b> Title </b> : {{reasonCodeTitle}}
        </div>
    </div>
    <div class="row mb-2">
        <div class="col-12">
            <b> Description </b> : {{reasonCodeDiscription}}
        </div>
    </div>
    </b-modal>
    <!-- Transaction Return Reason Modal end -->

    <!-- Transaction Details modal start -->
    <b-modal
      id="transaction-details-modal"
      ref="transaction-details-modal"
      :header-text-variant="headerTextVariant"
      :title="modal_header"
      hide-footer
    >
    <div class="row mb-2">
        <div class="col-12">
            <b> Customer Name </b> : {{customer_name}}
        </div>
    </div>
    <div class="row mb-2">
        <div class="col-12">
            <b> Transaction ID </b> : {{transaction_id}}
        </div>
    </div>
    <div class="row mb-2">
        <div class="col-12">
            <b> Transaction History </b> :
            <p v-for="(row, index) in returnTransactionDetails" :key="index">
                <span>{{row.local_transaction_date}} - {{row.status}}</span>
            </p>
        </div>
    </div>
    </b-modal>
    <!-- Transaction Details modal end -->
  </div>
</div>
</template>
<script>
import api from "@/api/transaction.js";
import moment from "moment";
import { validationMixin } from "vuelidate";
import { required, minLength } from "vuelidate/lib/validators";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "./CustomLoader/CanPayLoader.vue"
export default {
  mixins: [validationMixin],
  data() {
    return {
      allTransactionModel: {},
      showMsg: false,
      transaction_id: null,
      statusList: [],
      reasonList:[],
      comment: "",
      currentUser: localStorage.getItem("user")
        ? JSON.parse(localStorage.getItem("user"))
        : null,
      loading: false,
      report: [],
      consumerList: [],
      selectedConsumer: null,
      email:"",
      phone_no:"",
      reasonCodeTitle: "",
      reasonCodeDiscription: "",
      isLoading: false,
      transaction_id: "",
      status:null,
      reason:null,
      toggle_local_transaction_time_search: false,
      new_representable: null,
      returnDetails:{},
      headerTextVariant: "light",
      returnTransactionDetails:{},
      modal_header: null,
      customer_name: null,
      transaction_id: null,
      return_code: null
    };
  },
  components: {
    HourGlass,
    CanPayLoader
  },
  created() {

  },
  methods: {
    viewTransactionDetails(data){
      var self = this;
      self.loading = true;
      var request = {
          transaction_number: data.transaction_number
      };
      api
        .getReturnTransactionDetails(request)
        .then((response) => {
          if ((response.code = 200)) {
            self.returnTransactionDetails = response.data;
            self.transaction_id = data.transaction_number;
            self.customer_name = data.consumer_name;
            self.modal_header = 'Transaction Details';
            self.$bvModal.show("transaction-details-modal");
            self.loading = false;
          } else {
            error(response.message);
            self.loading = false;
          }
        })
        .catch((err) => {
          error(err);
          self.loading = false;
        });
    },
    getReturnStatus() {
      var self = this;
      api
        .getReturnStatus()
        .then((response) => {
          if ((response.code = 200)) {
            self.statusList = response.data;
          } else {
            error(response.message);
          }
        })
        .catch((err) => {
          error(err);
        });
    },
    getReturnReasons() {
      var self = this;
      api
        .getReturnReasons()
        .then((response) => {
          if ((response.code = 200)) {
            self.reasonList = response.data;
          } else {
            error(response.message);
          }
        })
        .catch((err) => {
          error(err);
        });
    },
    //get the consumer list
    getConsumers(searchtxt) {
      var self = this;
      if(searchtxt.length >= 3){
        self.isLoading = true;
        var request = {
          searchtxt: searchtxt,
        };
        api
          .getConsumers(request)
          .then(function (response) {
            if (response.code == 200) {
              self.consumerList = response.data;
              self.isLoading = false;
            } else {
              error(response.message);
            }
          })
          .catch(function (error) {
            error(error);
          });
      }
    },
    // API call to generate the merchant location transaction report
    generateReport(reportExport) {
      var self = this;
      if(self.selectedConsumer === null && $("#phone_no").val() == '' &&  $("#email").val() == ''){
        error("Please select either customer or phone no. or email.");
        return false;
      }
      if($("#end-date").val() == '' &&  $("#start-date").val() == ''){
        error("Please select Start Date & End Date then Generate.");
        return false;
      }
      if (
        moment($("#start-date").val()).format("YYYY-MM-DD") >
        moment().format("YYYY-MM-DD") && $("#start-date").val()!= ''
      ) {
        error("Start date cannot be from future.");
        return false;
      }
      if (
        moment($("#end-date").val()).format("YYYY-MM-DD") >
        moment().format("YYYY-MM-DD") && $("#end-date").val()!= ''
      ) {
        error("End date cannot be from future.");
        return false;
      }
      if($("#start-date").val()!=''){
        var from_date = moment($("#start-date").val()).format("YYYY-MM-DD");
      }else{
        var from_date = '';
      }
      if($("#end-date").val()!=''){
        var to_date = moment($("#end-date").val()).format("YYYY-MM-DD");
      }else{
        var to_date = '';
      }

      if(self.selectedConsumer === null){
        var consumer = '';
      }else{
        var consumer = self.selectedConsumer.user_id;
      }
      self.report = [];
      var request = {
        from_date: from_date,
        to_date: to_date,
        consumer: consumer,
        phone_no:self.phone_no,
        toggle_local_transaction_time_search: self.toggle_local_transaction_time_search,
        new_representable: self.new_representable
      };
      if(request.from_date > request.to_date){
        error("To Date cannot be greater than From date");
        return false;
      }
      self.loading = true;
      api
        .generateReturnTransactionReport(request)
        .then(function (response) {
          if (response.code == 200) {
            self.report = response.data.report;
            self.returnDetails = response.data.returnDetails;
            if(self.report.length > 0){
              self.loading = false;
            }else {
              error("No records found!");
              self.loading = false;
            }
          } else {
            error(response.message);
            self.loading = false;
          }
        })
        .catch(function (error) {
          // error(error);
          self.loading = false;
        });
    },
    detailsReason(data)
    {
        var self = this;
        self.customer_name = data.consumer_name;
        self.transaction_id = data.transaction_number;
        self.return_code = data.reason_code;
        self.reasonCodeTitle = data.title;
        self.reasonCodeDiscription = data.description;
        self.$refs["modal-approved-review"].show();
    }
  },
  mounted() {
    var self = this;
    self.getReturnStatus();
    self.getReturnReasons();
    $("#start-date").datepicker({
      format: "mm/dd/yyyy",
      autoclose: true,
      todayHighlight: true,
    });
    $("#end-date").datepicker({
      format: "mm/dd/yyyy",
      autoclose: true,
      todayHighlight: true,
    });
    $("#start-date , #end-date").datepicker("setDate", new Date());
  },
};
</script>


