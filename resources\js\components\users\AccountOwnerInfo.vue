<template>
<div>
  <div v-if="loading">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Manage Account Owner Info</h3>
                </div>

                <!-- /.card-header -->
                <div class="card-body">
                  <div class="row">
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="form-control"
                        placeholder="Phone No (Exact)"
                        id="phone_no"
                        v-model="phone_no"
                      />
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="form-control"
                        placeholder="Email (Exact)"
                        id="email"
                        v-model="email"
                      />
                    </div>
                  </div>
                </div>
                </div>
                  <div class="card-footer">
                    <button
                      type="button"
                      class="btn btn-success"
                      @click="searchConsumerAccounts()"
                    >
                      Search
                    </button>
                    <button
                      type="button"
                      @click="reset()"
                      class="btn btn-success margin-left-5"
                    >
                      Reset
                    </button>
                  </div>
                  <div class="card-body">
                    <div class="row">
                        <div class="col-8 mb-10" v-if="allUserModel.length > 0">
                            <span class="text-red">Real Account No. is shown in bracket.</span>
                        </div>
                    </div>
                    <div class="row" v-if="allUserModel.length > 0">
                      <div class="col-md-4">
                        <div class="form-group">
                          <label>Name: </label><span>&nbsp;{{allUserModel[0].name}}</span>
                        </div>
                      </div>
                      <div class="col-md-4">
                        <div class="form-group">
                          <label>Phone: </label><span>&nbsp;{{allUserModel[0].phone}}</span>
                        </div>
                      </div>
                      <div class="col-md-4">
                        <div class="form-group">
                          <label>Email: </label><span>&nbsp;{{allUserModel[0].email}}</span>
                        </div>
                      </div>
                    </div>
                  <b-table-simple
                      responsive
                      show-empty
                      bordered
                      sticky-header="800px"
                      v-if="allUserModel.length > 0"
                    >
                      <b-thead head-variant="light">
                        <b-tr>
                          <b-th class="text-left">Account No.</b-th>
                          <b-th class="text-left">Routing No.</b-th>
                          <b-th class="text-left">Bank Name</b-th>
                          <b-th class="text-left">Banking Solution</b-th>
                          <b-th class="text-left">Status</b-th>
                          <b-th class="text-left">Created On</b-th>
                          <b-th class="text-center">Action</b-th>
                        </b-tr>
                      </b-thead>
                      <b-tbody v-for="(row, index) in allUserModel" :key="index">
                        <b-tr>
                          <b-td class="text-left text-gray">{{
                            row.account_no
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.routing_no
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.bank_name
                          }}</b-td>
                          <b-td class="text-left text-gray text-capitalize">{{
                            row.banking_solution_name
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.status
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.created_on
                          }}</b-td>
                          <b-td class="text-center text-gray">
                            <a :data-user-id="row.edit" class="callAccountOwnerApi custom-edit-btn" title="Call Account Owner API" variant="outline-success" style="border:none"  @click="callAccountOwnerApi(row)"><i class="nav-icon fas fa-redo"></i></a>
                            <a :data-user-id="row.edit" class="viewHistory custom-edit-btn" title="View Account Owner History" variant="outline-success" style="border:none" v-if="row.account_owner_called == 1" @click="showHistory(row)"><i class="nav-icon fas fa-eye"></i></a>
                            <a
  :data-user-id="row.edit"
  class="retryApiCall custom-edit-btn"
  title="Retry Account Owner API Call"
  variant="outline-warning"
  style="border:none"
  v-if="'mx_record_id' in row && row.mx_record_id !== null"
  @click="retryApiCall(row.mx_record_id)"
>
  <i class="nav-icon fas fa-sync"></i>
</a>

                          </b-td>
                        </b-tr>
                      </b-tbody>
                    </b-table-simple>
                    <p v-else>No data displayed. Please refine your search criteria.</p>
                    </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>

    <!-- Account Owner History Modal Start -->

    <b-modal
      id="account-owner-history-modal"
      ref="modal"
      :header-text-variant="headerTextVariant"
      title="Account Owner History"
      :no-close-on-esc="true"
      :no-close-on-backdrop="true"
      hide-footer
    >
      <div class="card-body">
        <div class="row" v-if="account_owner_history.length > 0">
        <div class="col-md-12">
            <label>Account No:  </label><span>&nbsp;{{account_owner_history[0].account_no}}</span>
        </div>
        <div class="col-md-12">
            <label>Routing No:  </label><span>&nbsp;{{account_owner_history[0].routing_no}}</span>
        </div>
        </div>
        <b-table-simple
          responsive
          show-empty
          bordered
          sticky-header="800px"
          v-if="account_owner_history.length > 0"
        >

          <b-thead head-variant="light">
            <b-tr>
              <b-th class="text-left">Name from Registration</b-th>
              <b-th class="text-left">Name from Banking Solution</b-th>
              <b-th class="text-left">Name Match Percentage(%)</b-th>
              <b-th class="text-left">Address from Registration</b-th>
              <b-th class="text-left">Address from Banking Solution</b-th>
              <b-th class="text-left">Address Match Percentage(%)</b-th>
              <b-th class="text-center">API Called On</b-th>
            </b-tr>
          </b-thead>
          <b-tbody v-for="(row, index) in account_owner_history" :key="index">
            <b-tr>
              <b-td class="text-left text-gray">{{ row.name_during_registration  }}</b-td>
              <b-td class="text-left text-gray">{{ row.name_from_banking_solution }}</b-td>
              <b-td class="text-left text-gray">{{ row.name_match_percentage }}</b-td>
              <b-td class="text-left text-gray">{{ row.address_during_registration }}</b-td>
              <b-td class="text-left text-gray">{{ row.address_from_banking_solution }}</b-td>
              <b-td class="text-left text-gray">{{ row.address_match_percentage }}</b-td>
              <b-td class="text-left text-gray">{{ row.created_date }}</b-td>
            </b-tr>
          </b-tbody>
        </b-table-simple>
        <p v-else>No data displayed. Please refine your search criteria.</p>
      </div>
    </b-modal>
  <!-- Account Owner History Modal End -->



  </div>
</div>
</template>
<script>
import moment from "moment";
import api from "@/api/user.js";
import { validationMixin } from "vuelidate";
import { required, minLength,decimal } from "vuelidate/lib/validators";
import commonConstants from "@/common/constant.js";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "../CustomLoader/CanPayLoader.vue"
export default {
  mixins: [validationMixin],
  data() {
    return {
      allUserModel: {},
      headerTextVariant: "light",
      currentUser: localStorage.getItem("user")
        ? JSON.parse(localStorage.getItem("user"))
        : null,
      constants: commonConstants,
      consumer:"",
      phone_no:"",
      email:"",
      loading:false,
      account_owner_history:{}
    };
  },
  components: {
    HourGlass,
    CanPayLoader
  },
  created() {
  },
  methods: {
    isNumber: function (evt) {
      evt = evt ? evt : window.event;
      var charCode = evt.which ? evt.which : evt.keyCode;
      if (
        charCode != 46 &&
        charCode != 45 &&
        charCode > 31 &&
        (charCode < 48 || charCode > 57)
      ) {
      evt.preventDefault();
      } else {
        return true;
      }
    },
    searchConsumerAccounts(){
      var self = this;
      if((self.consumer).trim().length < 3 && $("#phone_no").val().trim() === '' &&  $("#email").val().trim() === ''){
        error("Please provide Consumer name (Min 3 chars) or email(exact) or phone no(exact)");
        return false;
      }
      var request = {
        consumer: self.consumer,
        email:self.email,
        phone_no:self.phone_no,
      };
      self.loading = true;
      api
      .searchConsumerAccounts(request)
      .then(function (response) {
        if (response.code == 200) {
          self.allUserModel = response.data;
          self.loading = false;
        } else {
          error(response.message);
          self.loading = false;
        }
      })
      .catch(function (error) {
        // error(error);
        self.loading = false;
      });
    },
    callAccountOwnerApi(row){
      var self = this;
      if(row.last_call_date!=null){
          var alert_msg = 'Account Owner API last called for this Account on '+row.last_call_date+'. Do you want to call it again?';
      }else{
          var alert_msg = 'Account Owner API is a paid API? Do you want to proceed?';
      }
      Vue.swal({
            title: "Warning!!!",
            text: alert_msg,
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#149240",
            confirmButtonText: "Yes, proceed!",
            cancelButtonText: "No, cancel it!",
            closeOnConfirm: true,
            closeOnCancel: true,
            width: '800px'
            }).then((result) => {
                if (result.isConfirmed) {
                    var request = {
                        user_id: row.edit,
                        account_id: row.account_id,
                    };
                    self.loading = true;
                    api
                    .callAccountOwnerApi(request)
                    .then(function (response) {
                        if (response.code == 200) {
                            Vue.swal('Done!',response.message,'success');
                            self.searchConsumerAccounts();
                        }
                        self.loading = false;
                    })
                    .catch(function (error) {
                        var message = error.response.data.message;
                        // error(error);
                        Vue.swal('Error!', message, 'error');
                        self.loading = false;
                    });
                }
            })
    },
    retryApiCall(record_id){
        var self = this;
        var alert_msg = 'Do you want to retry?';
        Vue.swal({
            title: "Warning!!!",
            text: alert_msg,
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#149240",
            confirmButtonText: "Yes, proceed!",
            cancelButtonText: "No, cancel it!",
            closeOnConfirm: true,
            closeOnCancel: true,
            width: '800px'
            }).then((result) => {
                if (result.isConfirmed) {
                    var request = {
                        record_id: record_id,
                    };
                    self.loading = true;
                    api
                    .retryAccountOwnerApiCall(request)
                    .then(function (response) {
                        if (response.code == 200) {
                            Vue.swal('Done!',response.message,'success');
                            self.searchConsumerAccounts();
                        }
                        self.loading = false;
                    })
                    .catch(function (error) {
                        var message = error.response.data.message;
                        // error(error);
                        Vue.swal('Error!', message, 'error');
                        self.loading = false;
                    });
                }
            });
    },
    showHistory(row){
      var self = this;
        var request = {
            user_id: row.edit,
            account_id: row.account_id,
        };
        self.loading = true;
        api
        .showAccountOwnerHistory(request)
        .then(function (response) {
            self.account_owner_history = response.data;
            self.$bvModal.show("account-owner-history-modal");
            self.loading = false;
        })
        .catch(function (error) {
            // error(error);
            self.loading = false;
        });
    },
    reset(){
      var self = this;
      self.consumer = "";
      self.phone_no = "";
      self.email = "";
    }
  },
  mounted() {
    var self = this;
    document.title = "CanPay - Manage Account Owner Info";
  },
};
</script>
