import { loadProgressBar } from 'axios-progress-bar'

const createRewardWheel = (request) => {
    var instance = axios.create({
        baseURL: process.env.MIX_REWARD_WHEEL_APP_URL,
    });
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.defaults.headers.common["Content-Type"] = 'multipart/form-data';
        instance.post('/api/admin/addrewardwheel', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const updateRewardWheel = (request) => {
    var instance = axios.create({
        baseURL: process.env.MIX_REWARD_WHEEL_APP_URL,
    });
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.post('/api/admin/editrewardwheel', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};


const getSingleRewardWheel = (request) => {
    var instance = axios.create({
        baseURL: process.env.MIX_REWARD_WHEEL_APP_URL,
    });
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.post('/api/admin/viewrewardwheel', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getSegments = (request) => {
    var instance = axios.create({
        baseURL: process.env.MIX_REWARD_WHEEL_APP_URL,
    });
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.post('/api/admin/getsements', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getAllCorporateParents = () => {
    var instance = axios.create({
        baseURL: process.env.MIX_REWARD_WHEEL_APP_URL,
    });
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.get('/api/admin/getallcorporateparents')
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getAllStores = (states, cp_ids) => {
    var instance = axios.create({
        baseURL: process.env.MIX_REWARD_WHEEL_APP_URL,
    });
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.post('/api/admin/getauthorizedstores', { state: states, id: cp_ids })
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getAllStatuses = () => {
    var instance = axios.create({
        baseURL: process.env.MIX_REWARD_WHEEL_APP_URL,
    });
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.get('/api/admin/getrewardwheelstatus')
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getAllStates = () => {
    var instance = axios.create({
        baseURL: process.env.MIX_REWARD_WHEEL_APP_URL,
    });
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.get('/api/admin/getallstates')
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getAllDefaultImages = () => {
    var instance = axios.create({
        baseURL: process.env.MIX_REWARD_WHEEL_APP_URL,
    });
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.get('/api/admin/getdefaultimages')
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const deleteDefaultImage = (params) => {
    var instance = axios.create({
        baseURL: process.env.MIX_REWARD_WHEEL_APP_URL,
    });
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.post('/api/admin/deletedefaultimage', params)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getActiveRewardwheels = () => {
    var instance = axios.create({
        baseURL: process.env.MIX_REWARD_WHEEL_APP_URL,
    });
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.get('/api/admin/getactiverewardwheels')
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getCurrentJackpotValue = () => {
    var instance = axios.create({
        baseURL: process.env.MIX_REWARD_WHEEL_APP_URL,
    });
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.post('/api/admin/fetchjackpotbasepoint')
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const saveJackpotReset = (params) => {
    var instance = axios.create({
        baseURL: process.env.MIX_REWARD_WHEEL_APP_URL,
    });
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.post('/api/admin/addnewjackpotbasepoint', params)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getRewardSpinReportExport = (request) => {
    var header = {
        responseType: 'blob',
    };
    return new Promise((res, rej) => {
        axios.post('/api/export/getrewardspinreportexport', request, header)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const generateReportAndPost = (request) => {
    var instance = axios.create({
        baseURL: process.env.MIX_REWARD_WHEEL_APP_URL,
    });
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.post('/api/admin/preparereportdataforjobs', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getAllRewardwheels = () => {
    var instance = axios.create({
        baseURL: process.env.MIX_REWARD_WHEEL_APP_URL,
    });
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.get('/api/admin/getrewardwheels')
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const saveLotteryValue = (params) => {
    var instance = axios.create({
        baseURL: process.env.MIX_REWARD_WHEEL_APP_URL,
    });
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.get('/api/admin/addlotterywinningdetails')
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};


const fetchAllTemplates = (request) => {
    var instance = axios.create({
        baseURL: process.env.MIX_REWARD_WHEEL_APP_URL,
    });
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.post('/api/admin/fetchdesignlibraries', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getAllCashbackPrograms = () => {
    var instance = axios.create({
        baseURL: process.env.MIX_REWARD_WHEEL_APP_URL,
    });
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.get('/api/getallcashbackprograms')
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const deleteLotteryReward = (params) => {
    var instance = axios.create({
        baseURL: process.env.MIX_REWARD_WHEEL_APP_URL,
    });
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.post('/api/admin/deletelotteryreward', params)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getAllStoreWiseWheels = (store_id, searchtxt, currentPage, perPage) => {
    var instance = axios.create({
        baseURL: process.env.MIX_REWARD_WHEEL_APP_URL,
    });
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.post('/api/admin/getstorewiserewardwheels', { id: store_id, searchtxt: searchtxt, currentPage: currentPage, perPage: perPage })
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};


const getCashbackProgramReportExport = (request) => {
    var header = {
        responseType: 'blob',
    };
    var instance = axios.create({
        baseURL: process.env.MIX_REWARD_WHEEL_APP_URL,
    });
    loadProgressBar({}, instance)

    return new Promise((res, rej) => {
        instance.post('/api/export/getcashbackprogramreportexport', request, header)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const exportPointsProgramReport = (request) => {
    var header = {
        responseType: 'blob',
    };
    var instance = axios.create({
        baseURL: process.env.MIX_REWARD_WHEEL_APP_URL,
    });
    loadProgressBar({}, instance)

    return new Promise((res, rej) => {
        instance.post('/api/export/pointsprogramreport', request, header)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const generatePointsProgramReport = (params) => {
    var instance = axios.create({
        baseURL: process.env.MIX_REWARD_WHEEL_APP_URL,
    });
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.post('/api/generatepointsprogramreport', params)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const searchRewardWheelStores = (request) => {
    var instance = axios.create({
        baseURL: process.env.MIX_REWARD_WHEEL_APP_URL,
    });
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.post('/api/admin/searchrewardwheelstores', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const assignRewardWheel = (request) => {
    var instance = axios.create({
        baseURL: process.env.MIX_REWARD_WHEEL_APP_URL,
    });
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.post('/api/admin/assignrewardwheel', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const removeWheels = (request) => {
    var instance = axios.create({
        baseURL: process.env.MIX_REWARD_WHEEL_APP_URL,
    });
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.post('/api/admin/removewheels', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const getCashbackFeedback = (request) => {
    var instance = axios.create({
        baseURL: process.env.MIX_REWARD_WHEEL_APP_URL,
    });
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.post('/api/admin/getcashbackfeedback', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const toggleMerchantFunded = (request) => {
    var instance = axios.create({
        baseURL: process.env.MIX_REWARD_WHEEL_APP_URL,
    });
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.post('/api/admin/togglemerchantfunded', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const cloneRewardWheel = (request) => {
    var instance = axios.create({
        baseURL: process.env.MIX_REWARD_WHEEL_APP_URL,
    });
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.post('/api/admin/clonerewardwheel', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

export default {
    createRewardWheel,
    updateRewardWheel,
    getSingleRewardWheel,
    getSegments,
    getAllCorporateParents,
    getAllStores,
    getAllStatuses,
    getAllStates,
    getAllDefaultImages,
    deleteDefaultImage,
    getActiveRewardwheels,
    getRewardSpinReportExport,
    generateReportAndPost,
    getAllRewardwheels,
    getCurrentJackpotValue,
    saveJackpotReset,
    saveLotteryValue,
    deleteLotteryReward,
    getAllStoreWiseWheels,
    searchRewardWheelStores,
    assignRewardWheel,
    removeWheels,
    fetchAllTemplates,
    getAllCashbackPrograms,
    getCashbackProgramReportExport,
    getCashbackFeedback,
    toggleMerchantFunded,
    cloneRewardWheel,
    exportPointsProgramReport,
    generatePointsProgramReport
};