<?php

namespace App\Imports;

use App\Http\Factories\Transaction\TransactionFactory;
use App\Models\TransactionDetails;
use App\Models\UserBankAccountInfo;
use App\Models\ReturnRepresentHistory;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class ReturnDataImport implements ToModel, WithHeadingRow, WithBatchInserts, WithChunkReading
{
    use Importable;
    private $rows = 0;
    private $duplicate_rows = 0;
    // protected $_batchID = null;

    public function __construct($batch_id)
    {
        $this->transaction = new TransactionFactory();
        $this->_batchID = $batch_id;
    }
    
    
    public function model(array $row)
    {
        $transaction_id = $row['transaction_id'];
        if (!empty($transaction_id)) {
            try {
                // Fetch the Transaction Data
                $transaction = TransactionDetails::where('id',$transaction_id)->first();

                //Fetch the Status ID for return status
                $returned = getStatus(RETURNED);
                $approvedByAdmin = getStatus(APPROVED_BY_ADMIN);
                // Check if the Return is already posted
                if ($transaction->status_id == $returned && $transaction->is_represented == 0) { 
                    //Update Bank Account Details
                    $updateBankAccountDetails = UserBankAccountInfo::where('id',$row['account_id'])
                    ->update([
                        'account_no' => $row['ach_account_no'],
                        'routing_no' => $row['ach_routing_no']]);

                    //Update Transaaction Details
                    $updateTransactionDetails = TransactionDetails::where('id',$row['transaction_id'])
                    ->update([
                        'status_id' => $approvedByAdmin,
                        'approved_by_admin' => 1,
                        'transaction_represented' => $row['account_id'], 
                        'represent_block' => 1, 
                        'return_from_primary_account' => 1]);
                    
                    //post the consumer debit transaction
                    $this->_representTransaction($row);

                } else { // Return posting skipped as the return is already posted
                    ++$this->duplicate_rows;
                    Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Return posting skipped as return already posted for Transaction ID : " . $row['transaction_id']);
                    insertManualPostedReturnLog('transaction_id', $row['transaction_id'], "Return posting skipped as return already posted for Transaction ID :  ".$row['transaction_id'], json_encode($row), '0',$this->_batchID);
                }
            } catch (\Exception $e) {
                ++$this->duplicate_rows;
                Log::channel('return-transaction')->error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured during return posting in  Transaction Details Table for Transaction ID: " . $row['transaction_id'] . ".", [EXCEPTION => $e]);
                insertManualPostedReturnLog('transaction_id', $row['transaction_id'], "Exception occured during return posting in  Transaction Details Table for Transaction ID:  ".$row['transaction_id'], json_encode($row), '0',$this->_batchID);
            }
        } else {
            ++$this->duplicate_rows;
            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Return posting skipped due to non availability of Transaction ID in Excel Sheet.");
            insertManualPostedReturnLog('consumer_id', $row['consumer_id'], "Return posting skipped due to non availability of Transaction ID in Excel Sheet for Consumer ID :  ".$row['consumer_id']."  Account ID:  ".$row['account_id']."  and Transaction Date: " .$row['local_transaction_time'], json_encode($row), '0',$this->_batchID);
        }
    }

    /**
     * This function fetches bank balance for all consumers who has added bank from finicity and keeps record in the database
     */
    private function _representTransaction($row)
    {
        $success = getStatus(SUCCESS);
        $failed = getStatus(FAILED);
        //fetch all the approved by consumer returned transactions
        $transaction = TransactionDetails::join('terminal_master', 'terminal_master.id', '=', 'transaction_details.terminal_id')->join('merchant_stores', 'terminal_master.merchant_store_id', '=', 'merchant_stores.id')->join('registered_merchant_master', 'registered_merchant_master.id', '=', 'merchant_stores.merchant_id')->join('users', 'users.user_id', '=', 'transaction_details.consumer_id')->join('status_master', 'transaction_details.status_id', '=', 'status_master.id')->leftJoin('timezone_masters', 'timezone_masters.id', '=', 'transaction_details.timezone_id')->select('transaction_details.*', 'registered_merchant_master.acheck_account_id')->where('transaction_details.is_represented', 0)->where('status_master.code', APPROVED_BY_ADMIN)->where('transaction_details.id',$row['transaction_id'])->first();
        
        if (!empty($transaction)) {
            // storing into history table for each transaction
            $history = new ReturnRepresentHistory();
            $history->consumer_id = $transaction->consumer_id;
            $history->transaction_id = $transaction->id;
            $history->amount = $transaction->amount;
            $history->is_manual = 0;
            try {
                //post the consumer debit transaction
                Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Posting consumer transaction to acheck21 for transaction amount: " . $transaction->amount . " with id: " . $transaction->id);
                $params['amount'] = $transaction->amount + $transaction->tip_amount;
                $params['consumer_id'] = $transaction->consumer_id;
                $params['acheck_account_id'] = $transaction->acheck_account_id;
                // send over the current active bank account id
                $params['account_id'] = $transaction->transaction_represented;
                //calling the factory function to create consumer transaction into acheck21
                $response = $this->transaction->createConsumerReturnTransaction($params);
                $response_decoded = json_decode($response, true);
                $this->_createTransaction($transaction, $response_decoded['documentId']);
                // update the parent transaction
                $transaction->is_represented = 1;
                $transaction->represent_count = $transaction->represent_count + 1;
                $transaction->save();
                //adding details to store into history table
                $history->outcome = "Success. New Transaction posted into acheck21. Executed from manual post return.";
                $history->reason_representable = 0;
                $history->status_id = $success;
                $history->save();

                Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction represented(created new) for transaction amount: " . $transaction->amount . " with id: " . $transaction->id);
                insertManualPostedReturnLog('transaction_id', $row['transaction_id'], "Transaction represented(created new) for transaction ID:  ".$row['transaction_id'], json_encode($row), '1',$this->_batchID);
                
                ++$this->rows;
            } catch (\Exception $e) {
                //adding details to store into history table
                $history->outcome = $e;
                $history->status_id = $failed;
                $history->save();

                ++$this->duplicate_rows;
                Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "There was a problem trying to represent transaction with id: " . $transaction->id, [EXCEPTION => $e]);
                insertManualPostedReturnLog('transaction_id', $row['transaction_id'], "There was a problem trying to represent Transaction ID :  ".$row['transaction_id'], json_encode($row), '0',$this->_batchID);
            }
        }else{
            ++$this->duplicate_rows;
            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No transaction found to represent for Transaction ID :.".$row['transaction_id']);
            insertManualPostedReturnLog('transaction_id', $row['transaction_id'], "No transaction found to represent for Transaction ID :  ".$row['transaction_id'], json_encode($row), '0',$this->_batchID);
        }
    }

    private function _createTransaction($transaction, $doc_id)
    {
        //create a new transaction
        $transaction_details = new TransactionDetails();
        $transaction_details->transaction_number = $transaction->transaction_number;
        $transaction_details->transaction_ref_no = $transaction->id;
        $transaction_details->user_id = $transaction->user_id;
        $transaction_details->consumer_id = $transaction->consumer_id;
        $transaction_details->terminal_id = $transaction->terminal_id;
        $transaction_details->account_id = $transaction->transaction_represented;
        $transaction_details->transaction_time = Carbon::now();
        $transaction_details->local_transaction_time = Carbon::now($transaction->timezone_name);
        $transaction_details->local_transaction_year = date("Y", strtotime($transaction_details->local_transaction_time));
        $transaction_details->local_transaction_month = date("m", strtotime($transaction_details->local_transaction_time));
        $transaction_details->local_transaction_date = date("Y-m-d", strtotime($transaction_details->local_transaction_time));
        $transaction_details->timezone_id = $transaction->timezone_id;
        $transaction_details->amount = $transaction->amount;
        $transaction_details->tip_amount = $transaction->tip_amount;
        $transaction_details->tip_type = $transaction->tip_type;
        $transaction_details->tip_add_time = $transaction->tip_add_time;
        $transaction_details->used_qr_id = $transaction->used_qr_id;
        $transaction_details->status_id = getStatus(PROCESSED_FOR_ACHECK21);
        $transaction_details->transaction_type_id = $transaction->transaction_type_id;
        $transaction_details->transaction_place = ACHECK21;
        $transaction_details->acheck_document_id = $doc_id;
        $transaction_details->is_represented = 1;
        $transaction_details->save();
        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction details was stored successfully.");
    }

    public function getRowCount()
    {
        return $this->rows . '|' . $this->duplicate_rows;
    }

    public function batchSize(): int
    {
        return 1000;
    }

    public function chunkSize(): int
    {
        return 5000;
    }
}
