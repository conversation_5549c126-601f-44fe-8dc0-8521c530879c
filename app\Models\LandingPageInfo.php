<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
class LandingPageInfo extends Model
{
    protected $table = 'landing_page_info';
    public $incrementing = false;

    /**
     * __construct
     * This function will insert an alphanumeric 32 character unique id as primary key in the table whenever a data get inserted
     * @param  mixed $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        $this->id = generateUUID();

        parent::__construct($attributes);
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'corporate_name',
        'corporate_parent_user_id',
        'vanity_url',
        'corporate_color',
        'logo_url',
        'unique_clients',
        'active',
        'created_at',
        'updated_at'
    ];

    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
}
