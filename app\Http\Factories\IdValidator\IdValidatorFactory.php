<?php

namespace App\Http\Factories\IdValidator;

use App\Http\Clients\CognitoHttpClient;
use App\Http\Factories\CognitoValidationRules\CognitoRulesWithoutSSN;
use App\Http\Factories\CognitoValidationRules\CognitoRulesWithSSN;
use App\Models\UserValidationCredentials;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 *
 * @package App\Http\Factories\IdValidator
 */
class IdValidatorFactory implements IdValidatorInterface
{

    public function __construct()
    {
        $this->cognito = new CognitoHttpClient();
    }
    /**
     * Get the cognito headers after digesting and encryption
     *
     * @return mixed
     */
    public function getCognitoHeaders($params)
    {
        $date = gmdate('D, d M Y H:i:s T');
        $body = $params['body'];
        $hash = hash('sha256', $body, true);
        $base_encode = base64_encode($hash);
        $digest = "SHA-256=" . $base_encode; //To form the digest, take your request body and run it through SHA256. Then take this value and get its base64 value
        $request_target = $params['request-target'];
        $signing_string = "(request-target): " . $request_target . "\ndate: " . $date . "\ndigest: " . $digest;
        $key = config('app.cognito_api_secret');
        $sign_hashed = hash_hmac("sha256", $signing_string, $key, true);
        $signature = base64_encode($sign_hashed);
        //authorize header format given in the official documentation
        $authorization = 'Signature keyId="' . config('app.cognito_api_key') . '",algorithm="hmac-sha256",headers="(request-target) date digest",signature="' . $signature . '"';
        $headers = [
            'Content-Type' => 'application/vnd.api+json',
            'Accept' => 'application/vnd.api+json',
            'Cognito-Version' => '2016-09-01',
            'Authorization' => $authorization,
            'Date' => $date,
            'Digest' => $digest,
        ];
        return $headers;
    }
    /**
     * Identity validation with phone name and ssn
     *
     * @return mixed
     */
    public function validateIdentity($params)
    {
        //getting cognito profile id
        $params['cognito_id'] = $this->getCognitoProfile($params);
        $response_json = json_decode($params['cognito_id'], true);

        //checking for errors if any
        if (isset($response_json['cognito_id']['errors'])) {
            return $params['cognito_id'];
        }

        //getting the cognito authentication headers
        $params['request-target'] = API_IDENTITY_SEARCH;
        //setting uo the request body
        $params['type'] = SEARCH;
        $params['body'] = $this->getCognitoBody($params); //getting cognito body according to type of the api call
        $params['headers'] = $this->getCognitoHeaders($params);

        //calling the search api
        $response = $this->cognito->searchIdentity($params);
        return $response;
    }
    /**
     * Getiing the cognito profile for which the searches will be created
     *
     * @return mixed
     */
    public function getCognitoProfile($params)
    {
        //fetch the existing token from the table and check whether its valid or not
        DB::beginTransaction();
        try {
            $result = UserValidationCredentials::where('phone', $params['phoneNo'])
                ->orderBy('created_at', 'DESC')
                ->first();
            if (empty($result)) {
                //get the headers
                $params['request-target'] = API_PROFILE;
                $params['type'] = CREATE_PROFILE;
                $params['body'] = $this->getCognitoBody($params); //getting cognito body according to type of the api call
                $params['headers'] = $this->getCognitoHeaders($params);
                //make call to the create profile api
                $response['response'] = $this->cognito->createCognitoProfile($params);
                $response_json = json_decode($response['response'], true);
                //if could not create the cognito profile
                if (isset($params['response']['errors'])) {
                    return $response; //return the whole response back to handle exception
                }
                $id = $response_json['data']['id'];
                $data = array(
                    'phone' => $params['phoneNo'],
                    'cognito_profile_id' => $id,
                    'session_id' => $params['session_id'],
                );
                //store the id to the database for future use
                UserValidationCredentials::create($data);
                DB::commit();
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Cognito profile created successfully with profile id : " . $id);
            } else {
                $id = $result['cognito_profile_id'];
            }
            return $id;
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(Line: " . __LINE__ . ") - " . "Exception occured while trying to store cognito profile id", ['Exception' => $e]);
            DB::rollback();
            return null;
        }
    }
    /**
     * Getting the JSON body that needs to be provided while calling the cognito API
     *
     * @return mixed
     */
    public function getCognitoBody($params)
    {
        switch ($params['type']) {
            /**
                 * This body is used when creating the cognito profile
                 */
            case CREATE_PROFILE:
                $body = '{"data": {"type": "profile"}}';
                break;

            /**
                 * This body is used for /identity_search API
                 */
            case SEARCH:
                $dob = explode('-', $params['dateOfBirth']);
                $day = ltrim($dob[2], 0);
                $month = ltrim($dob[1], 0);
                $year = $dob[0];
                $body = '{
                    "data": {
                      "type": "identity_search",
                      "attributes": {
                        "phone": {
                          "number": "+1' . $params['phoneNo'] . '"
                        },
                        "name": {
                          "first": "' . $params['firstName'] . '",
                          "last": "' . $params['lastName'] . '"
                        },
                        "birth": {
                            "day": ' . $day . ',
                            "month": ' . $month . ',
                            "year": ' . $year . '
                        },
                        "us_address": {
                          "street": "' . $params['address'] . '",
                          "city": "' . $params['city'] . '",
                          "subdivision": "' . $params['state'] . '",
                          "postal_code": "' . $params['zip'] . '"
                        }
                      },
                      "relationships": {
                        "profile": {
                          "data": {
                            "type": "profile",
                            "id": "' . $params['cognito_id'] . '"
                          }
                        }
                      }
                    }
                  }';
                break;
            /**
                 * This body is used for /identity_assessments API
                 */
            case ASSESSMENT_WITH_SSN:
                //formatting the dob
                $dob = explode('-', $params['dateOfBirth']);
                $day = ltrim($dob[2], 0);
                $month = ltrim($dob[1], 0);
                $year = $dob[0];
                $body = '{
                    "data": {
                      "type": "identity_assessment",
                      "attributes": {
                        "phone": {
                          "number": "+1' . $params['phoneNo'] . '"
                        },
                        "name": {
                          "first": "' . $params['firstName'] . '",
                          "last": "' . $params['lastName'] . '"
                        },
                        "birth": {
                            "day": ' . $day . ',
                            "month": ' . $month . ',
                            "year": ' . $year . '
                        },
                        "ssn": {
                          "serial": "' . $params['ssn'] . '"
                        },
                        "us_address": {
                          "street": "' . $params['address'] . '",
                          "city": "' . $params['city'] . '",
                          "subdivision": "' . $params['state'] . '",
                          "postal_code": "' . $params['zip'] . '"
                        }
                      },
                      "relationships": {
                        "identity_search": {
                          "data": {
                            "type": "identity_search",
                            "id": "' . $params['search_id'] . '"
                          }
                        }
                      }
                    }
                  }';
                break;
            /**
                 * This body is used for /identity_assessments API with out ssn
                 */
            case ASSESSMENT:
                //formatting the dob
                $dob = explode('-', $params['dateOfBirth']);
                $day = ltrim($dob[2], 0);
                $month = ltrim($dob[1], 0);
                $year = $dob[0];
                $body = '{
                    "data":{
                        "type":"identity_assessment",
                        "attributes":{
                            "phone":{
                                "number":"+1' . $params['phoneNo'] . '"
                            },
                            "name":{
                                "first":"' . $params['firstName'] . '",
                                "last":"' . $params['lastName'] . '"
                            },
                            "birth":{
                                "day": ' . $day . ',
                                "month": ' . $month . ',
                                "year": ' . $year . '
                            },
                            "us_address": {
                                "street": "' . $params['address'] . '",
                                "city": "' . $params['city'] . '",
                                "subdivision": "' . $params['state'] . '",
                                "postal_code": "' . $params['zip'] . '"
                            }},
                            "relationships":{
                                "identity_search":{
                                    "data":{
                                        "type":"identity_search",
                                        "id":"' . $params['search_id'] . '"
                                        }
                                    }
                                }
                            }
                        }';
                break;
            case SEARCH_NAME_PHONE:
                $body = '{
                    "data": {
                      "type": "identity_search",
                      "attributes": {
                        "phone": {
                          "number": "+1' . $params['phoneNo'] . '"
                        },
                        "name": {
                          "first": "' . $params['firstName'] . '",
                          "last": "' . $params['lastName'] . '"
                        },
                        "us_address": {
                          "street": "' . $params['street'] . '",
                          "city": "' . $params['city'] . '",
                          "subdivision": "' . $params['subdivision'] . '",
                          "postal_code": "' . $params['postal_code'] . '"
                        }
                      },
                      "relationships": {
                        "profile": {
                          "data": {
                            "type": "profile",
                            "id": "' . $params['cognito_id'] . '"
                          }
                        }
                      }
                    }
                  }';
                break;
        }
        return $body;
    }

    /**
     * Getting the Cognito identity assemenent response and forming the final json set which is to be sent over the rule engine
     *
     * @return mixed
     */
    public function getIdentityAssessment($params)
    {
        //get the headers
        $params['body'] = $this->getCognitoBody($params);
        $params['request-target'] = API_IDENTITY_ASSESSMENT;
        $params['headers'] = $this->getCognitoHeaders($params);

        //cheking for date of death results
        $included = $params['response']['included'];
        $params['deaths'] = array();
        foreach ($included as $attribute) {
            //if the response returns death attributes
            if ($attribute['type'] == 'death') {
                //make an array of the death records
                array_push($params['deaths'], $attribute['attributes']);
            }
        }
        //calling to identity assessment API to get the score from cognito
        $response = $this->cognito->identityAssessment($params);
        $response_json = json_decode($response, true); //decoding the json
        //picking out the data part from the array removing all the uneccessary informations
        $records = $response_json['data']['relationships']['identity_record_comparisons']['data'];
        $included = $response_json['included']; //picking out the included part which actually has the score details

        $average_score_set = array();
        //getting the array of average scores
        foreach ($included as $dataset) {
            if ($dataset['type'] == 'identity_record_comparison') {
                //for empty array simply take the first score from the response array
                if (empty($average_score_set)) {
                    array_push($average_score_set, $dataset['attributes']['score']);
                } else {
                    //need to check if the array already has this average score
                    foreach ($average_score_set as $score) {
                        if ($dataset['attributes']['score'] != $score) { //if  average score is same as the previous one then dont store
                            array_push($average_score_set, $dataset['attributes']['score']);
                        }
                    }
                }

            }
        }

        //initializing the final array
        $final_array_of_records = array();

        //flattening of the records start from here
        foreach ($records as $record) {

            //getting the reord from the response based on the id
            $data = $this->searchForId($record['id'], $included);

            //picking out the average score from the cognito assessment response
            $average_score = $data['attributes']['score'];

            //taking the size of maximum returned number of result
            $sizeOfarray['name_comparisons'] = sizeof($data['relationships']['name_comparisons']['data']);
            $sizeOfarray['phone_comparisons'] = sizeof($data['relationships']['phone_comparisons']['data']);
            $sizeOfarray['address_comparisons'] = sizeof($data['relationships']['address_comparisons']['data']);
            $sizeOfarray['ssn_comparisons'] = sizeof($data['relationships']['ssn_comparisons']['data']);
            $sizeOfarray['birth_comparisons'] = sizeof($data['relationships']['birth_comparisons']['data']);
            $max = 0;
            $attr = '';

            //getting the name of the attribute with maximum number of retured array so that we know the limit of the loop
            foreach ($sizeOfarray as $key => $val) {
                if ($val > $max) {
                    $max = $val;
                    $attr = $key;
                }
            }

            //running the final loop to form the flattened aray
            foreach ($data['relationships'][$attr]['data'] as $key => $val) {
                $phone = null;
                $name = null;
                $address = null;
                $ssn = null;
                $birth = null;
                //separating the components from the response that contains the compared score and the other details
                //sestion to separate phone components
                if (isset($data['relationships']['phone_comparisons']['data'][$key])) {
                    //getting the single record from the response
                    $final_record = $this->searchForId($data['relationships']['phone_comparisons']['data'][$key]['id'], $included);
                    //picking up the individual score
                    $phone = json_encode($final_record['attributes']['score'], true);
                    //picking out the components
                    $phone_components = json_encode($final_record['attributes']['components'], true);
                }
                //section to separate name components
                if (isset($data['relationships']['name_comparisons']['data'][$key])) {
                    //getting the single record from the response
                    $final_record = $this->searchForId($data['relationships']['name_comparisons']['data'][$key]['id'], $included);
                    //picking up the individual score
                    $name = json_encode($final_record['attributes']['score'], true);
                    //picking out the components
                    $name_components = json_encode($final_record['attributes']['components'], true);
                }
                //section to separate address components
                if (isset($data['relationships']['address_comparisons']['data'][$key])) {
                    //getting the single record from the response
                    $final_record = $this->searchForId($data['relationships']['address_comparisons']['data'][$key]['id'], $included);
                    //picking up the individual score
                    $address = json_encode($final_record['attributes']['score'], true);
                    //picking out the components
                    $address_components = json_encode($final_record['attributes']['components'], true);
                }
                //section to separate ssn components
                if (isset($data['relationships']['ssn_comparisons']['data'][$key])) {
                    //getting the single record from the response
                    $final_record = $this->searchForId($data['relationships']['ssn_comparisons']['data'][$key]['id'], $included);
                    //picking up the individual score
                    $ssn = json_encode($final_record['attributes']['score'], true);
                    //picking out the components
                    $ssn_components = json_encode($final_record['attributes']['components'], true);
                }
                //section to separate birth components
                if (isset($data['relationships']['birth_comparisons']['data'][$key])) {
                    //getting the single record from the response
                    $final_record = $this->searchForId($data['relationships']['birth_comparisons']['data'][$key]['id'], $included);
                    //picking up the individual score
                    $birth = json_encode($final_record['attributes']['score'], true);
                    //picking out the components
                    $birth_components = json_encode($final_record['attributes']['components'], true);
                }

                $returned_record_list['average_score'] = $average_score;
                $returned_record_list['phone_score'] = empty($phone) ? -1 : $phone;
                $returned_record_list['name_score'] = empty($name) ? -1 : $name;
                $returned_record_list['address_score'] = empty($address) ? -1 : $address;
                $returned_record_list['ssn_score'] = empty($ssn) ? -1 : $ssn;
                $returned_record_list['birth_score'] = empty($birth) ? -1 : $birth;

                $returned_record_list['phone_components'] = isset($phone_components) ? $phone_components : '';
                $returned_record_list['name_components'] = isset($name_components) ? $name_components : '';
                $returned_record_list['address_components'] = isset($address_components) ? $address_components : '';
                $returned_record_list['ssn_components'] = isset($ssn_components) ? $ssn_components : '';
                $returned_record_list['birth_components'] = isset($birth_components) ? $birth_components : '';

                //getting the final array ready out of which the data will get flattened
                array_push($final_array_of_records, $returned_record_list);
            }
        }

        //making the final set of array which is need to be send over the rule engine
        $final_records['cognito_search_id'] = $params['search_id'];
        $final_records['session_id'] = $params['session_id'];
        $final_records['input_name'] = $params['firstName'] . ' ' . $params['middleName'] . ' ' . $params['lastName'];
        $final_records['input_phone'] = $params['phoneNo'];
        $final_records['input_address'] = $params['address'] . ' ' . $params['city'] . ' ' . $params['state'] . ' ' . $params['zip'];
        $final_records['input_ssn'] = isset($params['ssn']) && !empty($params['ssn']) ? $params['ssn'] : '';
        $final_records['input_dob'] = $params['dateOfBirth'];
        $final_records['deaths'] = $params['deaths'];
        $final_records['average_scores'] = $average_score_set;
        $final_records['scores'] = $final_array_of_records;

        //calling the rule engine for cognito validation
        $cognitoRule = (isset($params['ssn']) && !empty($params['ssn'])) ? new CognitoRulesWithSSN() : new CognitoRulesWithoutSSN();
        //enriching the data after assessment
        $enrichedData = $cognitoRule->enrichCognitoData($final_records);
        //matching with the cognito rules
        $data = $cognitoRule->checkCognitoRules($enrichedData);

        return $data;
    }

    /**
     * Searches provided id value from given cognito response array(only for cognito response pattern)
     *
     * @return mixed
     */
    public function searchForId($id, $array)
    {
        foreach ($array as $val) {
            if ($val['id'] === $id) {
                return $val;
            }
        }
        return null;
    }

    /**
     * Identity search for canpay exsisting users (currently not in use)
     *
     * This function was used to parse record from spreadsheet given by clients
     * @return mixed
     */
    public function validateIdentityforExistingUsers($params)
    {
        //getting cognito profile id
        //get the headers
        $params['request-target'] = API_PROFILE;
        $params['type'] = CREATE_PROFILE;
        $params['body'] = $this->getCognitoBody($params); //getting cognito body according to type of the api call
        $params['headers'] = $this->getCognitoHeaders($params);
        //make call to the create profile api
        $response = $this->cognito->createCognitoProfile($params);
        $response_json = json_decode($response, true);
        $params['cognito_id'] = $response_json['data']['id'];
        //getting the cognito authentication headers
        $params['request-target'] = API_IDENTITY_SEARCH;
        //setting uo the request body
        $params['type'] = SEARCH;
        $params['body'] = $this->getCognitoBody($params); //getting cognito body according to type of the api call
        $params['headers'] = $this->getCognitoHeaders($params);
        $search_id = $this->storeCognitoRecordIntoDB($params);

        //getting assessment done
        $params['type'] = ASSESSMENT;
        $params['search_id'] = $search_id;
        $params['body'] = $this->getCognitoBody($params);
        $params['request-target'] = API_IDENTITY_ASSESSMENT;
        $params['headers'] = $this->getCognitoHeaders($params);

        $this->storeCognitoAssessmentIntoDB($params);

        return "success";
    }

/**
 * This function flattens the record and stores them into DB which is returned after cognito search
 */
    public function storeCognitoRecordIntoDB($params)
    {
        //calling the search api with phone number
        $response = $this->cognito->searchIdentity($params);
        $response_json = json_decode($response, true);
        $records = $response_json['data']['relationships']['identity_records']['data'];
        $included = $response_json['included'];
        foreach ($records as $record) {
            $data = $this->searchForId($record['id'], $included);
            $attr = $this->getCognitoRecordMaximumIndex($data);

            foreach ($data['relationships'][$attr]['data'] as $key => $val) {
                $address = null;
                $phone = null;
                $name = null;
                $birth = null;
                $ssn = null;
                $death = null;
                if (isset($data['relationships']['addresses']['data'][$key])) {
                    $final_record = $this->searchForId($data['relationships']['addresses']['data'][$key]['id'], $included);
                    $address = json_encode($final_record['attributes'], true);
                }
                if (isset($data['relationships']['births']['data'][$key])) {
                    $final_record = $this->searchForId($data['relationships']['births']['data'][$key]['id'], $included);
                    $birth = json_encode($final_record['attributes'], true);
                }
                if (isset($data['relationships']['deaths']['data'][$key])) {
                    $final_record = $this->searchForId($data['relationships']['deaths']['data'][$key]['id'], $included);
                    $death = json_encode($final_record['attributes'], true);
                }
                if (isset($data['relationships']['names']['data'][$key])) {
                    $final_record = $this->searchForId($data['relationships']['names']['data'][$key]['id'], $included);
                    $name = json_encode($final_record['attributes'], true);
                }
                if (isset($data['relationships']['phones']['data'][$key])) {
                    $final_record = $this->searchForId($data['relationships']['phones']['data'][$key]['id'], $included);
                    $phone = json_encode($final_record['attributes'], true);
                }
                if (isset($data['relationships']['ssns']['data'][$key])) {
                    $final_record = $this->searchForId($data['relationships']['ssns']['data'][$key]['id'], $included);
                    $ssn = json_encode($final_record['attributes'], true);
                }
                //storing the details into database
                DB::table('cognito_record')->insert(
                    ['cognito_search_id' => $response_json['data']['id'],
                        'source_phone_no' => $params['phoneNo'],
                        'source_first_name' => $params['firstName'],
                        'source_middle_name' => $params['middleName'],
                        'source_last_name' => $params['lastName'],
                        'source_address' => $params['street'] . ' ' . $params['city'] . ' ' . $params['subdivision'] . ' ' . $params['postal_code'],
                        'result_phone' => $phone,
                        'result_name' => $name,
                        'result_address' => $address,
                        'result_birth' => $birth,
                        'result_death' => $death,
                        'result_ssn' => $ssn,
                    ]);
            }
        }

        return $response_json['data']['id'];
    }

/**
 * This function returns the index having maximum records for the cognito search results
 */
    public function getCognitoRecordMaximumIndex($data)
    {
        $sizeOfarray['addresses'] = sizeof($data['relationships']['addresses']['data']);
        $sizeOfarray['births'] = sizeof($data['relationships']['births']['data']);
        $sizeOfarray['deaths'] = sizeof($data['relationships']['deaths']['data']);
        $sizeOfarray['names'] = sizeof($data['relationships']['names']['data']);
        $sizeOfarray['phones'] = sizeof($data['relationships']['phones']['data']);
        $sizeOfarray['ssns'] = sizeof($data['relationships']['ssns']['data']);
        $max = 0;
        $attr = '';
        foreach ($sizeOfarray as $key => $val) {
            if ($val > $max) {
                $max = $val;
                $attr = $key;
            }
        }
        return $attr;
    }

/**
 * This function flattens the records returned from cognito assessment and stores them into the database
 */
    public function storeCognitoAssessmentIntoDB($params)
    {
        $assessment_response = $this->cognito->identityAssessment($params);
        $response_json = json_decode($assessment_response, true);
        $records = $response_json['data']['relationships']['identity_record_comparisons']['data'];
        $included = $response_json['included'];
        foreach ($records as $record) {
            $data = $this->searchForId($record['id'], $included);
            $average_score = $data['attributes']['score'];

            $sizeOfarray['name_comparisons'] = sizeof($data['relationships']['name_comparisons']['data']);
            $sizeOfarray['phone_comparisons'] = sizeof($data['relationships']['phone_comparisons']['data']);
            $sizeOfarray['address_comparisons'] = sizeof($data['relationships']['address_comparisons']['data']);
            $max = 0;
            $attr = '';
            foreach ($sizeOfarray as $key => $val) {
                if ($val > $max) {
                    $max = $val;
                    $attr = $key;
                }
            }
            foreach ($data['relationships'][$attr]['data'] as $key => $val) {
                $phone = null;
                $name = null;
                $address = null;
                if (isset($data['relationships']['phone_comparisons']['data'][$key])) {
                    $final_record = $this->searchForId($data['relationships']['phone_comparisons']['data'][$key]['id'], $included);
                    $phone = json_encode($final_record['attributes']['score'], true);
                    $phone_components = json_encode($final_record['attributes']['components'], true);
                }
                if (isset($data['relationships']['name_comparisons']['data'][$key])) {
                    $final_record = $this->searchForId($data['relationships']['name_comparisons']['data'][$key]['id'], $included);
                    $name = json_encode($final_record['attributes']['score'], true);
                    $name_components = json_encode($final_record['attributes']['components'], true);
                }
                if (isset($data['relationships']['address_comparisons']['data'][$key])) {
                    $final_record = $this->searchForId($data['relationships']['address_comparisons']['data'][$key]['id'], $included);
                    $address = json_encode($final_record['attributes']['score'], true);
                    $address_components = json_encode($final_record['attributes']['components'], true);
                }
                //storing the details into database
                DB::table('cognito_assessment')->insert(
                    ['cognito_search_id' => $params['search_id'],
                        'average_score' => $average_score,
                        'phone_score' => $phone,
                        'name_score' => $name,
                        'address_score' => $address,
                        'phone_components' => isset($phone_components) ? $phone_components : '',
                        'name_components' => $name_components,
                        'address_component' => $address_components,
                    ]);
            }
        }

    }
}
