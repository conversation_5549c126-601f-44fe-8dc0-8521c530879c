<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Imports\HolidaysImport;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\HolidayList;

class HolidayListController extends Controller
{
    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    /**
     * importHolidayListFromExcel
     * This function is used to import transactions for consumers in V2 system from V1
     * @return void
     */

    public function importHolidayExcel()
    {
        DB::beginTransaction();
        try {
            $excel = $this->request->file('excel');
            if (empty($excel)) {
                $message = "Please upload the data sheets to import Holiday list";
                return renderResponse(FAIL, $message, null);
            }
            $allowed = array('xls', 'xlsx', 'csv');
            if (!in_array($excel->getClientOriginalExtension(), $allowed)) {
                $message = trans('message.invalid_excel');
                return renderResponse(FAIL, $message, null);
            }
            //====================Code Segment For Parsing holiday list==========================
            if (!empty($excel)) {
                Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Holiday list excel upload done and import started");
                $import = new HolidaysImport();
                Excel::import($import, $excel);
                Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Holiday list excel import completed");
            }
            DB::commit();
            $message = trans('message.excel_import_success');
            //adding details to import excel log table
            return renderResponse(SUCCESS, $message, null);
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occurred while importing transaction data.", [EXCEPTION => $e]);
            DB::rollback();
            $message = "There was some problem while trying to import Holiday list data. Processed data for current period has been rolled back.";
            return renderResponse(FAIL, $message, null);
        }
    }

    /**
     * getHolidayList
     * Listing for Holiday List for all types of data migration in Datatable
     * @param  mixed $request
     * @return void
     */
       /**
     * Fetch all manual identity verification details
     */
    public function getHolidayList(Request $request)
    {
        // Columns defined for Sorting
        $columns = array(
          0 => 'holiday_lists.holiday_name',
          1 => 'holiday_lists.holiday_year',
          2 => 'holiday_lists.holiday_date',
          3 => 'holiday_lists.reason',
          4 => 'users.first_name',
          5 => 'holiday_lists.created_at',
      );
      // Main Query
      $query = HolidayList::on(MYSQL_RO)->join('users', 'users.user_id', '=', 'holiday_lists.added_by')->select('holiday_lists.*', 'users.first_name', 'users.middle_name', 'users.last_name');

      //Count Query
      $queryCount = HolidayList::on(MYSQL_RO)->join('users', 'users.user_id', '=', 'holiday_lists.added_by')
          ->selectRaw('COUNT(*) as total_count');

      $totalData = $queryCount->first()->total_count; // Getting total no of rows

        $totalFiltered = $totalData;
        $limit = intval($this->request->input('length'));
        $start = intval($this->request->input('start'));
        $order = $columns[$this->request->input('order.0.column')];
        $dir = $this->request->input('order.0.dir');
        if (empty($this->request->input('search.value')) && empty($order) && empty($dir)) {
            $holiday_lists = $query->offset($start)->limit(intval($limit))->orderBy('holiday_lists.holiday_date', 'ASC')->get();
        } else if (empty($this->request->input('search.value'))) {
            $holiday_lists = $query->offset($start)->limit(intval($limit))->orderBy($order, $dir)->get();
        } else {
            
            $search = $this->request->input('search.value');
            $search_query = $query->where(function ($q) use ($search) {

                $q->where('holiday_lists.id', 'LIKE', "%{$search}%")->orWhere('holiday_lists.holiday_name', 'LIKE', "%{$search}%")->orWhere('holiday_lists.holiday_year', 'LIKE', "%{$search}%")->orWhere('holiday_lists.holiday_date', 'LIKE', "%{$search}%")->orWhere('holiday_lists.reason', 'LIKE', "%{$search}%")->orWhereRaw("lower(concat_ws(' ', users.first_name, users.middle_name, users.last_name)) LIKE ? ", ['%' . $search . '%']);
            });

            $search_query_count = $queryCount->where(function ($q) use ($search) {
                $q->where('holiday_lists.id', 'LIKE', "%{$search}%")->orWhere('holiday_lists.holiday_name', 'LIKE', "%{$search}%")->orWhere('holiday_lists.holiday_year', 'LIKE', "%{$search}%")->orWhere('holiday_lists.holiday_date', 'LIKE', "%{$search}%")->orWhere('holiday_lists.reason', 'LIKE', "%{$search}%")->orWhereRaw("lower(concat_ws(' ', users.first_name, users.middle_name, users.last_name)) LIKE ? ", ['%' . $search . '%']);
            });

            $totalFiltered = $search_query_count->first()->total_count;

            $holiday_lists = $search_query->offset($start)->limit(intval($limit))->orderBy($order, $dir)->get();
        }

        $data = array();
      if (!empty($holiday_lists)) {
          // Creating array to show the values in frontend
          foreach ($holiday_lists as $holiday_list) {
              $nestedData['holiday_name'] = $holiday_list->holiday_name;
              $nestedData['holiday_year'] = $holiday_list->holiday_year;
              $nestedData['holiday_date'] = $holiday_list->holiday_date;
              $nestedData['reason'] = $holiday_list->reason;
              $nestedData['added_by'] = $holiday_list->first_name . ' ' . $holiday_list->middle_name . ' ' . $holiday_list->last_name;
              $nestedData['created_at'] = date('m-d-Y h:i A', strtotime($holiday_list->created_at));
              $data[] = $nestedData;
          }
      }
      // Drawing the Datatable
      $json_data = array(
          "draw" => intval($request->input('draw')),
          "recordsTotal" => intval($totalData),
          "recordsFiltered" => intval($totalFiltered),
          "data" => $data,
      );

      Log::info(addslashes(__METHOD__) . "(" . LINE . __LINE__ . ") : Holiday List fetched successfully");
      echo json_encode($json_data); // Rerurning the data
    }
}
