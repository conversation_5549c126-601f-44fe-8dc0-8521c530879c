<template>
<div>
  <div v-if="loading">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Registered Merchants</h3>
                </div>
                <div class="card-body">
                  <div class="row">
                    <div class="col-md-4">
                      <div class="form-group">
                        <input
                          class="form-control"
                          placeholder="Merchant ID (Min 3 chars)"
                          id="merchant_id"
                          v-model="merchant_id"
                        />
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group">
                        <input
                          class="form-control"
                          placeholder="Retailer (Min 3 chars)"
                          id="retailer"
                          v-model="retailer"
                        />
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group">
                        <input
                          class="form-control"
                          placeholder="Email (Exact)"
                          id="email"
                          v-model="email"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div class="card-footer">
                  <button
                    type="button"
                    class="btn btn-success"
                    @click="searchRegisteredMerchants()"
                  >
                    Search
                  </button>
                  <button
                    type="button"
                    @click="reset()"
                    class="btn btn-success margin-left-5"
                  >
                    Reset
                  </button>
                </div>
                <!-- /.card-header -->
                <div class="card-body">
                  <b-table-simple
                    responsive
                    show-empty
                    bordered
                    sticky-header="800px"
                    v-if="allMerchantModel.length > 0"
                  >
                    <b-thead head-variant="light">
                      <b-tr>
                        <b-th class="text-left">Merchant ID</b-th>
                        <b-th class="text-left">Retailer</b-th>
                        <b-th class="text-left">Email</b-th>
                        <b-th class="text-center">Customer Since</b-th>
                        <b-th class="text-center">Action</b-th>
                      </b-tr>
                    </b-thead>
                    <b-tbody
                      v-for="(row, index) in allMerchantModel"
                      :key="index"
                    >
                      <b-tr>
                        <b-td class="text-left text-gray">{{
                          row.merchant_id
                        }}</b-td>
                        <b-td class="text-left text-gray">{{
                          row.retailer
                        }}</b-td>
                        <b-td class="text-left text-gray">{{ row.email }}</b-td>
                        <b-td class="text-center text-gray">{{
                          formatDate(row.created_at)
                        }}</b-td>
                        <b-td class="text-center text-gray">
                          <a
                            :data-user-id="row.id"
                            class="viewStoreDetails custom-edit-btn"
                            title="View Assigned Stores"
                            variant="outline-success"
                            ><i class="nav-icon fas fa-eye"></i
                          ></a>
                          <a
                            :data-user-id="row.id"
                            class="editmerchant custom-edit-btn"
                            title="Edit Merchant"
                            variant="outline-success"
                            ><i class="nav-icon fas fa-edit"></i></a
                        ></b-td>
                      </b-tr>
                    </b-tbody>
                  </b-table-simple>
                  <p v-else>
                    No data displayed. Please refine your search criteria.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
    <!-- CP Modal Start -->
    <b-modal
      id="merchant-modal"
      ref="modal"
      :header-text-variant="headerTextVariant"
      title="Edit Merchant"
      @hidden="resetModal"
      ok-title="Save"
      ok-variant="success"
      cancel-variant="outline-secondary"
      @ok="handleOk"
      :no-close-on-esc="true"
      :no-close-on-backdrop="true"
    >
      <form ref="form" @submit.stop.prevent="save" class="needs-validation">
        <h4>Profile Details</h4>
        <div class="row">
          <div class="col-md-6">
            <label for="merchant_id">
              Merchant ID
              <span class="red">*</span>
            </label>
            <input
              id="merchant_id"
              name="merchant_id"
              v-validate="'required|alpha'"
              type="text"
              v-model="merchantDetails.merchant_id"
              class="form-control"
              disabled
            />
            <span v-show="errors.has('merchant_id')" class="text-danger">{{
              errors.first("merchant_id")
            }}</span>
          </div>
          <div class="col-md-6">
            <label for="contact_no"> Contact Number </label>
            <input
              id="contact_no"
              name="contact_no"
              type="text"
              v-model="merchantDetails.store_contact_no"
              class="form-control"
            />
            <span v-show="errors.has('contact_no')" class="text-danger">{{
              errors.first("contact_no")
            }}</span>
          </div>
        </div>
        <div class="row">
          <div class="col-md-6">
            <label for="retailer">
              Retailer Name
              <span class="red">*</span>
            </label>
            <input
              id="retailer"
              name="retailer"
              type="text"
              v-validate="'required'"
              v-model="merchantDetails.retailer"
              class="form-control"
            />
            <span v-show="errors.has('retailer')" class="text-danger">{{
              errors.first("retailer")
            }}</span>
          </div>

          <div class="col-md-6">
            <label for="retailer">
                Website Address
            </label>
            <input
                id="website_address"
                name="website_address"
                type="text"
                v-model="merchantDetails.website_address"
                v-validate="'url'"
                class="form-control"
            />
            <span
                v-show="errors.has('website_address')"
                class="text-danger"
                >{{ errors.first("website_address") }}</span
            >
        </div>
        </div>
        <br />
        <h4>Acheck Account Details</h4>
        <div class="row">
          <div class="col-md-6">
            <label for="acheck_account_name">
              Account Name
            </label>
            <input
              id="acheck_account_name"
              name="acheck_account_name"
              v-validate="'alpha_num'"
              type="text"
              v-model="merchantDetails.acheck_account_name"
              class="form-control"
            />
            <span
              v-show="errors.has('acheck_account_name')"
              class="text-danger"
              >{{ errors.first("acheck_account_name") }}</span
            >
          </div>

          <div class="col-md-6">
            <label for="acheck_account_id">
              Account ID
            </label>
            <input
              id="acheck_account_id"
              name="acheck_account_id"
              v-validate="'numeric'"
              type="text"
              v-model="merchantDetails.acheck_account_id"
              class="form-control"
            />
            <span
              v-show="errors.has('acheck_account_id')"
              class="text-danger"
              >{{ errors.first("acheck_account_id") }}</span
            >
          </div>
        </div>
        <br />
        <h4>Bank Account Details</h4>
        <div class="row">
          <div class="col-md-6">
            <label for="account_no">
              Deposit Account
              <span class="red">*</span>
            </label>
            <input
              id="account_no"
              name="account_no"
              v-validate="'required|numeric'"
              type="text"
              v-model="merchantDetails.account_no"
              class="form-control"
            />
            <span v-show="errors.has('account_no')" class="text-danger">{{
              errors.first("account_no")
            }}</span>
          </div>

          <div class="col-md-6">
            <label for="fees_account_number">Fees Account</label>
            <input
              id="fees_account_number"
              name="fees_account_number"
              type="text"
              v-validate="'numeric'"
              v-model="merchantDetails.fees_account_number"
              class="form-control"
            />
            <span
              v-show="errors.has('fees_account_number')"
              class="text-danger"
              >{{ errors.first("fees_account_number") }}</span
            >
          </div>
        </div>
        <div class="row">
          <div class="col-md-6">
            <label for="routing_no">
              Routing No.
              <span class="red">*</span>
            </label>
            <input
              id="routing_no"
              name="routing_no"
              v-validate="'required|numeric'"
              type="text"
              v-model="merchantDetails.routing_no"
              class="form-control"
            />
            <span v-show="errors.has('routing_no')" class="text-danger">{{
              errors.first("routing_no")
            }}</span>
          </div>
        </div>
        <br />
        <h4>Transaction Fee</h4>

        <div class="row">
          <div class="col-md-6">
            <label for="volume_value">
              Discount Rate
              <span class="red">*</span>
            </label>
            <div class="input-group mb-3">
              <input
                id="volume_value"
                name="volume_value"
                v-validate="'decimal'"
                type="text"
                v-model="merchantDetails.volume_value"
                class="form-control"
              />
              <div class="input-group-append">
                <span class="input-group-text">%</span>
              </div>
            </div>
            <span v-show="errors.has('volume_value')" class="text-danger">{{
              errors.first("volume_value")
            }}</span>
          </div>

          <div class="col-md-6">
            <label for="per_transaction_value">Per Transaction Fees</label>
            <input
              id="per_transaction_value"
              name="per_transaction_value"
              type="text"
              v-validate="'decimal'"
              v-model="merchantDetails.per_transaction_value"
              class="form-control"
            />
            <span
              v-show="errors.has('per_transaction_value')"
              class="text-danger"
              >{{ errors.first("per_transaction_value") }}</span
            >
          </div>
        </div>
        <br />
        <h4>Points Program Setting
            <a class="mb-1" href="javascript:void(0)" v-b-tooltip.hover title="If it is turned on then the deduction of merchant points program points and canpay fees will be posted separately.">
                <svg width="15px" version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
                viewBox="0 0 490 490" style="enable-background:new 0 0 490 490; margin-bottom: 2px;" xml:space="preserve"><g><g><g><path d="M245,490C109.9,490,0,380.1,0,245S109.9,0,245,0s245,109.9,245,245S380.1,490,245,490z M245,62C144.1,62,62,144.1,62,245s82.1,183,183,183s183-82.1,183-183S345.9,62,245,62z"/></g><g><g><circle cx="241.3" cy="159.2" r="29.1"/></g><g><polygon points="285.1,359.9 270.4,359.9 219.6,359.9 204.9,359.9 204.9,321 219.6,321 219.6,254.8 205.1,254.8 205.1,215.9 219.6,215.9 263.1,215.9 270.4,215.9 270.4,321 285.1,321 				"/></g></g></g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g>
                </svg>
            </a>
        </h4>

    	  <div class="row">
          <div class="col-md-12">
            <label for="pos_employee_login">
               Enable Split Points Program Posting
              <span class="red">*</span>
            </label>

            <label class="switch"
              ><input
                type="checkbox"
                id="split_cashback_posting"
                name="split_cashback_posting"
                v-model="merchantDetails.split_cashback_posting"
                true-value="1"
                false-value="0"
                class="enable-employee-login" /><span
                class="slider round"
              ></span
            ></label>
          </div>
        </div>
        
        <div v-if="merchantDetails.split_cashback_posting == 1"> 
          <br />
          <h4>Bank Account Details For Split Points Program</h4>
          <div class="row">
            <div class="col-md-6">
              <label for="merchant_points_program_account_no">
                Deposit Account
                <span class="red">*</span>
              </label>
              <input
                id="merchant_points_program_account_no"
                name="merchant_points_program_account_no"
                v-validate="'required|numeric'"
                type="text"
                v-model="merchantDetails.merchant_points_program_account_no"
                class="form-control"
              />
              <span v-show="errors.has('merchant_points_program_account_no')" class="text-danger">{{
                errors.first("merchant_points_program_account_no")
              }}</span>
            </div>

            <div class="col-md-6">
              <label for="merchant_points_program_fees_account_number">Fees Account</label>
              <input
                id="merchant_points_program_fees_account_number"
                name="merchant_points_program_fees_account_number"
                type="text"
                v-validate="'numeric'"
                v-model="merchantDetails.merchant_points_program_fees_account_number"
                class="form-control"
              />
              <span
                v-show="errors.has('merchant_points_program_fees_account_number')"
                class="text-danger"
                >{{ errors.first("merchant_points_program_fees_account_number") }}</span
              >
            </div>
          </div>
          
          <div class="row">
            <div class="col-md-6">
              <label for="merchant_points_program_routing_no">
                Routing No.
                <span class="red">*</span>
              </label>
              <input
                id="merchant_points_program_routing_no"
                name="merchant_points_program_routing_no"
                v-validate="'required|numeric'"
                type="text"
                v-model="merchantDetails.merchant_points_program_routing_no"
                class="form-control"
              />
              <span v-show="errors.has('merchant_points_program_routing_no')" class="text-danger">{{
                errors.first("merchant_points_program_routing_no")
              }}</span>
            </div>
          </div>
        </div>
        <br>
        <h4>Activate Brand Settings</h4>
    	  <div class="row">
          <div class="col-md-12">
            <label for="pos_employee_login">
               Enable the Merchant as Brand
              <span class="red">*</span>
            </label>

            <label class="switch"
              ><input
                type="checkbox"
                id="split_cashback_posting"
                name="split_cashback_posting"
                v-model="merchantDetails.type"
                true-value="brand"
                false-value="merchant"
                class="enable-employee-login" /><span
                class="slider round"
              ></span
            ></label>
          </div>
        </div>
        <br />
        <h4>POS Setting</h4>

    	  <div class="row">
          <div class="col-md-12">
            <label for="pos_employee_login">
               Enable POS Employee Login
              <span class="red">*</span>
            </label>

            <label class="switch"
              ><input
                type="checkbox"
                id="pos_employee_login"
                name="pos_employee_login"
                v-model="merchantDetails.pos_employee_login"
                true-value="1"
                false-value="0"
                class="enable-employee-login" /><span
                class="slider round"
              ></span
            ></label>
          </div>
        </div>
        <div v-if="merchantDetails.enable_new_ach_process_for_all_merchant == 0">
          <br />
          <h4>ACH Setting</h4>
          
          <div class="row">
            <div class="col-md-12">
              <label for="enable_new_ach_process">
                Enable New ACH Process
                <span class="red">*</span>
              </label>

              <label class="switch"
                ><input
                  type="checkbox"
                  id="enable_new_ach_process"
                  name="enable_new_ach_process"
                  v-model="merchantDetails.is_enabled_new_ach_process"
                  true-value="1"
                  false-value="0"
                  class="enable-employee-login" /><span
                  class="slider round"
                ></span
              ></label>
            </div>
          </div>
        </div>
        <br />
        <h4>Split funding merchant</h4>
          <!------ Delivery Fees Start ------>
          <div class="row">
          <div class="col-md-6">
            <label for="enable_delivery_fees">
               Enable split funding merchant
              <span class="red">*</span>
            </label>

            <label class="switch"
              ><input
                type="checkbox"
                id="enable_delivery_fees"
                name="enable_delivery_fees"
                v-model="merchantDetails.type"
                true-value="delivery_partner"
                false-value="merchant"
                class="enable-employee-login"
                @change="splitFundingChange()"/><span
                class="slider round"
                
              ></span
            ></label>
          </div>
          </div>
          <!--- Per Transaction Fees start --->
        <div class="row" v-if="merchantDetails.type == 'delivery_partner'">
          <div class="col-md-6">
            <label for="canpay_commission">
              Per Transaction Fees
              <span class="red">*</span>
            </label>
            <div class="input-group">
              <input
                id="canpay_commission"
                name="canpay_commission"
                v-validate="'decimal'"
                type="text"
                required
                v-model="merchantDetails.canpay_commission"
                class="form-control"
              />
              <div class="input-group-append">
                <span class="input-group-text">%</span>
              </div>
            </div>
            <span v-show="errors.has('canpay_commission')" class="text-danger">{{
              errors.first("canpay_commission")
            }}</span>
          </div>
        </div>
        <!--- Per Transaction Fees end --->
        <!--- Select Store start --->
        <div class="row" v-if="merchantDetails.type == 'delivery_partner'">
          <div class="col-md-12">
            <label for="selectedStoreForFees">
              Select Store
              <span class="red">*</span>
            </label>
            <div class="input-group">
              <multiselect
                  id="selectedStoreForFees"
                  v-model="selectedStoreForFees"
                  placeholder="Select Store (Min 3 chars)"
                  label="retailer"
                  track-by="id"
                  :multiple="true"
                  :options="storelist"
                  :loading="isLoading"
                  :internal-search="false"
                  @search-change="getAllTheStore"
              ></multiselect>
            </div>
            <div v-if="selectedStoreForFees.length == 0 && showSelectedStoreForFees">
              <span style="color:red;margin-left:1px;">Select store to proceed.</span>
            </div>
          </div>
        </div>
        <!--- Select Store End --->
        <div>
        </div>
          <br/>
          <!------ Delivery Fees End -------->
        <h4>RemotePay Setting</h4>
        
        <div class="row">
          <div class="col-md-6">
            <label for="allow_ecommerce_transaction">
               RemotePay Transaction
              <span class="red">*</span>
            </label>

            <label class="switch"
              ><input
                type="checkbox"
                id="allow_ecommerce_transaction"
                name="allow_ecommerce_transaction"
                v-model="merchantDetails.allow_ecommerce_transaction"
                true-value="1"
                false-value="0"
                class="enable-employee-login" 
                @change="remotePayTransactionChange()"/><span
                class="slider round"
                
              ></span
            ></label>
          </div>
          <div class="col-md-6">
            <label for="allow_ecommerce_transaction">
               Consumer Auth
              <span class="red">*</span>
            </label>

            <label class="switch"
              ><input
                type="checkbox"
                id="allow_consumer_auth"
                name="allow_consumer_auth"
                v-model="merchantDetails.allow_consumer_auth"
                true-value="1"
                false-value="0"
                class="enable-employee-login" :disabled="merchantDetails.allow_ecommerce_transaction == 0" />
              <span
                :class="
                  merchantDetails.allow_ecommerce_transaction == 1
                    ? 'slider round'
                    : 'slider round disabled'
                "
              ></span>
            </label>
          </div>
        </div>
        <div class="row">
          <div class="col-md-12">
            <label for="retailer">
              Merchant Profile Name
            </label>
            <input
                id="merchant_profile_name"
                name="merchant_profile_name"
                type="text"
                v-model="merchantDetails.merchant_profile_name"
                v-validate="'max:255'"
                class="form-control"
            />
            <span
                v-show="errors.has('merchant_profile_name')"
                class="text-danger"
                >{{ errors.first("merchant_profile_name") }}</span
            >
          </div>
        </div>
        <div class="row">
          <div class="col-md-6">
            <label for="retailer">
                Sandbox Webhook URL
            </label>
            <input
                id="sandbox_webhook_url"
                name="sandbox_webhook_url"
                type="text"
                v-model="merchantDetails.sandbox_webhook_url"
                v-validate="'url'"
                class="form-control"
            />
            <span
                v-show="errors.has('sandbox_webhook_url')"
                class="text-danger"
                >{{ errors.first("sandbox_webhook_url") }}</span
            >
            </div>

            <div class="col-md-6">
            <label for="retailer">
                Live Webhook URL
            </label>
            <input
                id="live_webhook_url"
                name="live_webhook_url"
                type="text"
                v-model="merchantDetails.live_webhook_url"
                v-validate="'url'"
                class="form-control"
            />
            <span
                v-show="errors.has('live_webhook_url')"
                class="text-danger"
                >{{ errors.first("live_webhook_url") }}</span
            >
            </div>
        </div>
        <div v-if="merchantDetails.type == 'sponsor'">
          <br />
          <h4>Sponsor Setting</h4>
          
          <div class="row">
            <div class="col-md-12">
              <label for="retailer">
                  SignUp URL
              </label>
              <input
                  id="sponsor_signup_url"
                  name="sponsor_signup_url"
                  type="text"
                  v-model="merchantDetails.sponsor_signup_url"
                  v-validate="'url'"
                  class="form-control"
              />
              <span
                  v-show="errors.has('sponsor_signup_url')"
                  class="text-danger"
                  >{{ errors.first("sponsor_signup_url") }}</span
              >
              </div>
          </div>
        </div>
      </form>
    </b-modal>
    <!-- CP Modal End -->

    <!-- View Store Modal Start -->
    <b-modal
      id="view-store-modal"
      ref="view-store-modal"
      :header-text-variant="headerTextVariant"
      title="Merchant Details"
      hide-footer
    >
      <div class="row odd-row">
        <div class="col-md-4 row-value">
          <label for="name">Merchant Name</label>
        </div>
        <div class="col-md-1 row-value">:</div>
        <div class="col-md-7 row-value">
          <span for="name">{{ merchant.merchant_name }}</span>
        </div>
      </div>
      <div class="row even-row">
        <div class="col-md-4 row-value">
          <label for="access_rights">Merchant ID</label>
        </div>
        <div class="col-md-1 row-value">:</div>
        <div class="col-md-7 row-value">
          <span for="name">{{ merchant.merchant_id }}</span>
        </div>
      </div>
      <div class="row odd-row">
        <div class="col-md-4 row-value">
          <label for="access_other_org_info">Email</label>
        </div>
        <div class="col-md-1 row-value">:</div>
        <div class="col-md-7 row-value">
          <span for="name">{{ merchant.email }}</span>
        </div>
      </div>
      <div class="row even-row">
        <div class="col-md-4 row-value">
          <label for="access_rights">Contact No.</label>
        </div>
        <div class="col-md-1 row-value">:</div>
        <div class="col-md-7 row-value">
          <span for="name">{{ merchant.store_contact_no }}</span>
        </div>
      </div>
      <div class="row odd-row">
        <div class="col-md-4 row-value">
          <label for="access_rights">Store Access</label>
        </div>
        <div class="col-md-1 row-value">:</div>
        <div class="col-md-7 row-value">
          <span for="name"
            >{{ merchant.store_id }} - {{ merchant.retailer }}</span
          >
        </div>
      </div>
      <div class="row even-row">
        <div class="col-md-4 row-value">
          <label for="access_rights">Type</label>
        </div>
        <div class="col-md-1 row-value">:</div>
        <div class="col-md-7 row-value">
          <span for="name">{{merchant.type}}</span>
        </div>
      </div>
      <div class="row odd-row">
        <div class="col-md-4 row-value">
          <label for="access_rights">Health Check Identifier</label>
        </div>
        <div class="col-md-1 row-value">:</div>
        <div class="col-md-7 row-value">
          <span for="name">{{ merchant.ach_identifier }} </span>
        </div>
      </div>
    </b-modal>
    <!-- View Store Modal Start -->
  </div>
</div>
</template>
<script>
import CanPayLoader from "./CustomLoader/CanPayLoader.vue"
import moment from "moment";
import api from "@/api/user.js";
import store_api from "@/api/stores.js"
import { validationMixin } from "vuelidate";
import { required, minLength } from "vuelidate/lib/validators";
import commonConstants from "@/common/constant.js";
export default {
  mixins: [validationMixin],
  components:{
    CanPayLoader
  },
  data() {
    return {
      modalTitle: "",
      statusList: [],
      headerTextVariant: "light",
      merchantDetails: {},
      userId: null,
      stores: {},
      store_ids: null,
      value: [],
      fields: [{ key: "retailer", label: "Store Name", class: "text-center" }],
      merchant: {},
      showReloadBtn: false,
      merchant_id: "",
      retailer: "",
      email: "",
      allMerchantModel: {},
      loading: false,
      storelist:[],
      isLoading:false,
      selectedStoreForFees: [],
      showSelectedStoreForFees:false
    };
  },
  created() {
    this.editmerchant();
    this.viewStoreDetails();
  },
  methods: {
    getAllTheStore(input){
      let self = this;
      if(input.length >= 3){
        const paylaod = {
          searchtxt: input
        }
        self.isLoading = true;
        store_api
        .getActiveRemotePayStore(paylaod)
        .then((res)=>{
          self.isLoading = false;
          self.storelist = res.data;
        })
        .catch((error)=>{
           self.isLoading = false;
        })
      }

    },
    customLabel(store) {
      return `${store.retailer}`;
      console.log("test");
    },
    splitFundingChange(){
      if(this.merchantDetails.type == 'merchant'){
        this.merchantDetails.canpay_commission = null;
      }
    },
    remotePayTransactionChange() {
      if(this.merchantDetails.allow_ecommerce_transaction == 0){
        this.merchantDetails.allow_consumer_auth =0;
      }
    },
    reset() {
      var self = this;
      self.merchant_id = "";
      self.retailer = "";
      self.email = "";
    },
    viewStoreDetails() {
      var self = this;
      $(document).on("click", ".viewStoreDetails", function (e) {
        var merchantID = $(e.currentTarget).attr("data-user-id");
        var request = {
          merchantID: merchantID,
        };
        self.loading = true;
        api
          .getmerchantdetails(request)
          .then((response) => {
            if (response.code == 200) {
              self.merchant = response.data;
              self.$bvModal.show("view-store-modal");
            } else {
              error(response.message);
            }
            self.loading = false;
          })
          .catch((err) => {
            self.loading = false;
            error(err.response.data.message);
          });
      });
    },
    editmerchant() {
      var self = this;
      $(document).on("click", ".editmerchant", function (e) {
        var merchantID = $(e.currentTarget).attr("data-user-id");
        var request = {
          merchantID: merchantID,
        };
        self.selectedStoreForFees = [];
        self.storelist = [];
        self.loading = true;
        api
          .getmerchantdetails(request)
          .then((response) => {
            if (response.code == 200) {
              self.merchantDetails = response.data;
              self.selectedStoreForFees = self.merchantDetails.selectedStore;
              self.$bvModal.show("merchant-modal");
            } else {
              error(response.message);
            }
            self.loading = false;
          })
          .catch((err) => {
            self.loading = false;
            error(err.response.data.message);
          });
      });
    },
    resetModal() {
      var self = this;
      self.merchantDetails = {};
      self.selectedStoreForFees = [];
      self.showSelectedStoreForFees = false;
      self.store_ids = null;
    },
    handleOk(bvModalEvt) {
      var self = this;
      // Prevent modal from closing
      bvModalEvt.preventDefault();
      // Trigger submit handler
      self.save();
    },
    save() {
      var self = this;
      // Exit when the form isn't valid
      this.$validator.validateAll().then((result) => {
        if (result) {
          if(self.selectedStoreForFees.length == 0 && self.merchantDetails.type =='delivery_partner'){
            self.showSelectedStoreForFees = true;
            return;
          }
          self.loading = true;
          self.merchantDetails.selectedStore = self.selectedStoreForFees;
          api
            .editmerchant(self.merchantDetails)
            .then((response) => {
              if (response.code == 200) {
                success(response.message);
                $("#merchantTable").DataTable().ajax.reload(null, false);
                self.$bvModal.hide("merchant-modal");
                self.store_ids = null;
                self.resetModal();
              } else {
                error(response.message);
              }
              self.loading = false
            })
            .catch((err) => {
              self.loading = false;
              error(err.response.data.message);
            });
        }
      });
      
    },
    searchRegisteredMerchants() {
      var self = this;

      if (
        self.retailer.trim().length < 3 &&
        self.merchant_id.trim().length < 3 &&
        $("#email").val().trim() === ""
      ) {
        error(
          "Please provide Merchant ID (Min 3 chars) or Retailer (Min 3 chars) or email(exact)"
        );
        return false;
      }
      var request = {
        merchant_id: self.merchant_id,
        retailer: self.retailer,
        email: self.email,
      };
      self.loading = true;
      api
        .searchRegisteredMerchants(request)
        .then(function (response) {
          if (response.code == 200) {
            self.allMerchantModel = response.data;
            self.loading = false;
          } else {
            error(response.message);
            self.loading = false;
          }
        })
        .catch(function (error) {
          // error(error);
          self.loading = false;
        });
    },
    formatDate(value) {
      return moment(String(value)).format("MM-DD-YYYY hh:mm");
    },
  },
  mounted() {
    var self = this;
    document.title = "CanPay - Merchants";
  },
};
</script>
<style>
.disabled {
    background-color: #e9ecef;
}
</style>