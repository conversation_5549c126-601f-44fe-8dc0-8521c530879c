{"@timestamp":"2025-08-04T07:48:08.191694+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"2ddb9ade7a4e4089f59b8f6691cbfb9a","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\Auth\LoginController::login(Line: 103) - User logged in successfully with email id: <EMAIL> []"}
{"@timestamp":"2025-08-04T07:48:10.346312+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"852ea2ffd84d61fcfd42cbeeb2cc8d90","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\TransactionController::getTransactionDetails(Line: 1090) - Dashboard Info Box population started. []"}
{"@timestamp":"2025-08-04T07:48:10.745282+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"852ea2ffd84d61fcfd42cbeeb2cc8d90","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\TransactionController::getTransactionDetails(Line: 1188) - Dashboard Information fetched :  {"total_sales_v1":"0.00","total_transactions_v1":"0","total_sales_v2":"0.00","total_transactions_v2":"0","total_monthly_sales_v1":"0.00","total_monthly_transactions_v1":"0","total_monthly_sales_v2":"0.00","total_monthly_transactions_v2":"0","new_enrollments_v1":"0","total_enrollments_v1":"35","new_enrollments_v2":"0","total_enrollments_v2":"539","avg_weekly_sales":"0.00","avg_weekly_transactions":"0","start_date":"08/03/2025","end_date":"07/28/2025","remotepay_daily_registration":0,"remotepay_monthly_registration":0,"total_monthly_admin_driven_not_posted_amount":"0.00","total_daily_admin_driven_not_posted_amount":"0.00","lite_users_today":0,"lite_users_monthly":0,"lite_to_standard_conversions_today":0,"lite_to_standard_conversions_monthly":0}"}
{"@timestamp":"2025-08-04T07:49:39.549544+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"080297c300f14bba3950faba89099d42","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\\Http\\Controllers\\DataMigrationController::getMigrationLog(Line457) : Migration Log List fetched successfully []"}
{"@timestamp":"2025-08-04T07:49:43.428888+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"ed465f9eaa2c9eaf8c1c77a045a29b7c","USER_ID":"","PU":"LAPTOP-FKD2B6A9","LEVEL":"ERROR","MESSAGE":"Log for HttpException Exception occured on [C:\\Users\\<USER>\\Desktop\\CanPay\\canpay_admin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\AbstractRouteCollection.php] Line number[123] with Code [0] and  Message [The POST method is not supported for route api/getpetitions. Supported methods: GET, HEAD.] []"}
{"@timestamp":"2025-08-04T07:49:45.093228+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"1b1c379a01e6a300008ac5009dc4ddf9","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\Auth\LoginController::login(Line: 103) - User logged in successfully with email id: <EMAIL> []"}
{"@timestamp":"2025-08-04T07:49:45.820709+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"72af7e4369c880e9567f8f2217097a82","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\TransactionController::getTransactionDetails(Line: 1090) - Dashboard Info Box population started. []"}
{"@timestamp":"2025-08-04T07:49:45.861471+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"72af7e4369c880e9567f8f2217097a82","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\TransactionController::getTransactionDetails(Line: 1188) - Dashboard Information fetched :  {"total_sales_v1":"0.00","total_transactions_v1":"0","total_sales_v2":"0.00","total_transactions_v2":"0","total_monthly_sales_v1":"0.00","total_monthly_transactions_v1":"0","total_monthly_sales_v2":"0.00","total_monthly_transactions_v2":"0","new_enrollments_v1":"0","total_enrollments_v1":"35","new_enrollments_v2":"0","total_enrollments_v2":"539","avg_weekly_sales":"0.00","avg_weekly_transactions":"0","start_date":"08/03/2025","end_date":"07/28/2025","remotepay_daily_registration":0,"remotepay_monthly_registration":0,"total_monthly_admin_driven_not_posted_amount":"0.00","total_daily_admin_driven_not_posted_amount":"0.00","lite_users_today":0,"lite_users_monthly":0,"lite_to_standard_conversions_today":0,"lite_to_standard_conversions_monthly":0}"}
{"@timestamp":"2025-08-04T07:50:07.230262+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"f92b303395926cb81de4487c262c1093","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\\Http\\Controllers\\ImportController::getMerchantImportExcelLog(Line130) : Import Excel Log fetched successfully []"}
{"@timestamp":"2025-08-04T09:05:24.925776+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"2767510a5e0c6b471f87568a70dfa80a","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\\Http\\Controllers\\ImportController::getMerchantImportExcelLog(Line130) : Import Excel Log fetched successfully []"}
{"@timestamp":"2025-08-04T09:05:50.068344+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"03d264650d3b4cac179c75615958dfcd","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\\Http\\Controllers\\DataMigrationController::getMigrationLog(Line457) : Migration Log List fetched successfully []"}
{"@timestamp":"2025-08-04T09:06:01.237984+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"f6caa7a021ec87f71827a7ca431d3804","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\\Http\\Controllers\\ImportController::getMerchantImportExcelLog(Line130) : Import Excel Log fetched successfully []"}
{"@timestamp":"2025-08-04T09:13:31.259573+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"6262ce2cf265f97b06a2f4e90253b576","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"ERROR","MESSAGE":"App\Http\Controllers\ImportController::importCorporateLogo(Line: 153) - Exception while uploading logo. {"Exception":"[object] (InvalidArgumentException(code: 0): Found 1 error while validating the input provided for the GetObject operation:\n[Key] expected string length to be >= 1, but found string length of 0 at C:\\Users\\<USER>\\Desktop\\CanPay\\canpay_admin\\vendor\\aws\\aws-sdk-php\\src\\Api\\Validator.php:65)"}"}
{"@timestamp":"2025-08-04T09:18:22.087575+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"95bf106b0c8e3438974142caeef071f4","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"ERROR","MESSAGE":"Log for ValidationException Exception occured on [C:\\Users\\<USER>\\Desktop\\CanPay\\canpay_admin\\app\\Http\\Controllers\\Controller.php] Line number[27] with Code [0] and  Message [The Corporate Color Field Is Required When Corporate Vanity Url / Logo Url / Display Name / Corporate Url Is Present.] []"}
{"@timestamp":"2025-08-04T09:18:42.633521+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"875f86327e9b0e8f9df6130f0b90c481","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"ERROR","MESSAGE":"Log for ValidationException Exception occured on [C:\\Users\\<USER>\\Desktop\\CanPay\\canpay_admin\\app\\Http\\Controllers\\Controller.php] Line number[27] with Code [0] and  Message [The Corporate Vanity Url Field Is Required When Corporate Color / Logo Url / Display Name / Corporate Url Is Present.] []"}
{"@timestamp":"2025-08-04T09:19:04.964700+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"37b862c9a67f98a15d618c18678e8737","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"ERROR","MESSAGE":"App\Http\Controllers\ImportController::importCorporateLogo(Line: 153) - Exception while uploading logo. {"Exception":"[object] (InvalidArgumentException(code: 0): Found 1 error while validating the input provided for the GetObject operation:\n[Key] expected string length to be >= 1, but found string length of 0 at C:\\Users\\<USER>\\Desktop\\CanPay\\canpay_admin\\vendor\\aws\\aws-sdk-php\\src\\Api\\Validator.php:65)"}"}
{"@timestamp":"2025-08-04T09:36:50.311885+00:00","V":"V-74b0c23","EC2":"Local-Server","IP":"127.0.0.1","TID":"b0fe5b8a23666171cd71f98f39f7d25b","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"ERROR","MESSAGE":"App\Http\Controllers\ImportController::importCorporateLogo(Line: 153) - Exception while uploading logo. {"Exception":"[object] (InvalidArgumentException(code: 0): Found 1 error while validating the input provided for the GetObject operation:\n[Key] expected string length to be >= 1, but found string length of 0 at C:\\Users\\<USER>\\Desktop\\CanPay\\canpay_admin\\vendor\\aws\\aws-sdk-php\\src\\Api\\Validator.php:65)"}"}
{"@timestamp":"2025-08-04T09:48:57.256076+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"a0be381a911d123c2e0610801dd5b1bb","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"INFO","MESSAGE":"App\Http\Controllers\ImportController::importCorporateLogo(Line: 151) - File uploaded to S3, path:  []"}
{"@timestamp":"2025-08-04T09:48:57.257504+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"a0be381a911d123c2e0610801dd5b1bb","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"ERROR","MESSAGE":"App\Http\Controllers\ImportController::importCorporateLogo(Line: 155) - putFile returned empty path []"}
{"@timestamp":"2025-08-04T09:51:46.479461+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"82a7589f5486078ef8ce1d12a1c7e644","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"ERROR","MESSAGE":"App\Http\Controllers\ImportController::importCorporateLogo(Line: 153) - Exception while uploading logo. {"Exception":"[object] (InvalidArgumentException(code: 0): Found 1 error while validating the input provided for the GetObject operation:\n[Key] expected string length to be >= 1, but found string length of 0 at C:\\Users\\<USER>\\Desktop\\CanPay\\canpay_admin\\vendor\\aws\\aws-sdk-php\\src\\Api\\Validator.php:65)"}"}
{"@timestamp":"2025-08-04T09:52:00.219799+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"e35bd62ffdd1173f4790407d500f601c","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"ERROR","MESSAGE":"App\Http\Controllers\ImportController::importCorporateLogo(Line: 153) - Exception while uploading logo. {"Exception":"[object] (InvalidArgumentException(code: 0): Found 1 error while validating the input provided for the GetObject operation:\n[Key] expected string length to be >= 1, but found string length of 0 at C:\\Users\\<USER>\\Desktop\\CanPay\\canpay_admin\\vendor\\aws\\aws-sdk-php\\src\\Api\\Validator.php:65)"}"}
{"@timestamp":"2025-08-04T09:58:43.610143+00:00","V":"V-c55ce3f","EC2":"Local-Server","IP":"127.0.0.1","TID":"50f6da9c58163bd26971159aaf664f1a","USER_ID":"578fe7fcfb246aa88009794ec2581edf","PU":"LAPTOP-FKD2B6A9","LEVEL":"ERROR","MESSAGE":"App\Http\Controllers\ImportController::importCorporateLogo(Line: 153) - Exception while uploading logo. {"Exception":"[object] (InvalidArgumentException(code: 0): Found 1 error while validating the input provided for the GetObject operation:\n[Key] expected string length to be >= 1, but found string length of 0 at C:\\Users\\<USER>\\Desktop\\CanPay\\canpay_admin\\vendor\\aws\\aws-sdk-php\\src\\Api\\Validator.php:65)"}"}
