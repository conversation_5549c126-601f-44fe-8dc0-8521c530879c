<?php
namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class CheckMapsApiKey extends Command
{
    protected $signature = 'checkmaps:apikey {key} {--lat=} {--lng=}';
    protected $description = 'Check if an API key is working for a given latitude and longitude';

    public function handle()
    {
        $apiKey = $this->argument('key');
        $lat = doubleval($this->option('lat'));
        $lng = doubleval($this->option('lng'));

        // Check if latitude and longitude are set and valid
        if (!is_numeric($lat) || !is_numeric($lng)) {
            $this->error('Invalid latitude or longitude values.');
            return;
        }

        $url = "https://maps.googleapis.com/maps/api/timezone/json?location={$lat},{$lng}&timestamp=" . time() . "&key={$apiKey}";

        $response = Http::get($url);

        if ($response->ok()) {
            $responseData = $response->json();
            $this->info('Response Data:');
            $this->info(json_encode($responseData, JSON_PRETTY_PRINT));
        } else {
            $this->error('Failed to make the API request. HTTP status code: ' . $response->status());
        }
    }

}
