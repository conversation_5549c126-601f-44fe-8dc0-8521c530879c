const getDeliveryFeeReport = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/getdeliveryfeereport', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getAllMerchantsAndDeliveryPartners = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/getallmerchantsanddeliverypartners', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getDeliveryFeeTransactionExport = (request) => {
    var header = {
        responseType: 'blob',
    };
    return new Promise((res, rej) => {
        axios.post('api/export/getdeliveryfeetransactionexport', request, header)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
}

const getDeliveryFeeSettlementTransactionExport = (request) => {
    var header = {
        responseType: 'blob',
    };
    return new Promise((res, rej) => {
        axios.post('api/export/deliveryfeesettlementreportexport', request, header)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
}


const getDeliveryFeeSettlementReport = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/deliveryfeesettlementreport', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

export default{
    getDeliveryFeeReport,
    getAllMerchantsAndDeliveryPartners,
    getDeliveryFeeTransactionExport,
    getDeliveryFeeSettlementTransactionExport,
    getDeliveryFeeSettlementReport
}
