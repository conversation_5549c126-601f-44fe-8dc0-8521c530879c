<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
class ReturnRepresentHistory extends Model
{
    protected $table = 'return_representation_history';

    /**
     * __construct
     * This function will insert an alphanumeric 32 character unique id as primary key in the table whenever a data get inserted
     * @param  mixed $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        $this->id = generateUUID();

        parent::__construct($attributes);
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'transaction_id',
        'consumer_id',
        'amount',
        'reason_representable',
        'outcome',
        'is_manual',
        'status_id'
    ];
    public $incrementing = false;
    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
}
