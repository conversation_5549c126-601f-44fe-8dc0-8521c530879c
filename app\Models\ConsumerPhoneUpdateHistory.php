<?php

namespace App\Models;

use Illuminate\Auth\Authenticatable;
use Illuminate\Database\Eloquent\Model;
class ConsumerPhoneUpdateHistory extends Model
{
    protected $table = 'consumer_phone_update_history';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'consumer_id',
        'phone',
        'source',
    ];
    public $timestamps = true;
    public $incrementing = false;
    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
}
