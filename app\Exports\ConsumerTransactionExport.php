<?php

namespace App\Exports;

use DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Events\AfterSheet;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;

class ConsumerTransactionExport implements FromCollection, WithHeadings, ShouldAutoSize, WithEvents, WithColumnFormatting
{

    protected $request;

    public function __construct($request)
    {
        $this->request = $request; // Declaring the request variable
    }


    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        $collection_array = $this->request->get('report'); // Storing the array received from request

        $transactions = array();
        foreach($collection_array as $transaction){
            Log::debug(__METHOD__ . "==> ",(array) $transaction);
            $nestedData['transaction_number'] = $transaction['transaction_number'];
            $nestedData['consumer_name'] = $transaction['consumer_name'];
            $nestedData['merchant_name'] = $transaction['merchant_name'];
            $nestedData['store_name'] = $transaction['store_name'];
            $nestedData['terminal_name'] = $transaction['terminal_name'];
            $nestedData['amount'] = $transaction['amount'];
            $nestedData['consumer_bank_posting_amount'] = $transaction['consumer_bank_posting_amount'] == NULL?'0.00':$transaction['consumer_bank_posting_amount'];
            $nestedData['reward_amount_used'] = $transaction['reward_amount_used'] == NULL?'0.00':$transaction['reward_amount_used'];
            $nestedData['updated_amount'] = $transaction['updated_amount'];
            $nestedData['last_approve_tip_amount'] = $transaction['last_approve_tip_amount'];
            $nestedData['delivery_fee'] = $transaction['delivery_fee'];
            $nestedData['transaction_time'] = str_replace("<br/>"," ",$transaction['transaction_time']);
            $nestedData['status'] = $transaction['status'];

            array_push($transactions,$nestedData);
        }

        return collect([
            $transactions,
        ]);
    }

    public function headings(): array
    {
        $returnArray = array(
            [
                'Transaction Number',
                'Consumer',
                'Merchant',
                'Store',
                'Terminal',
                'Amount($)',
                'Bank Posting Amount ($)',
                'Reward Amount Used ($)',
                'Updated Amount($)',
                'Tip Amount($)',
                'Delivery Fee($)',
                'Transaction Time',
                'Status'
            ],
        );

        return $returnArray;
    }

    public function columnFormats(): array
    {
        return [
            'F' => '0.00',
        ];
    }


    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event){
                $event->sheet->getStyle('A1:L1')->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);

                //Apply Center Alignment
                $event->sheet->getStyle('A:J')->getAlignment()->applyFromArray(
                    array('horizontal' => 'center')
                );
            },
        ];
    }
}
