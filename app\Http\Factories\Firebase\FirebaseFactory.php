<?php

namespace App\Http\Factories\Firebase;

use App\Http\Clients\FirestoreHttpClient;
use Illuminate\Support\Facades\Log;

/**
 *
 * @package App\Http\Factories\Firebase
 */
class FirebaseFactory implements FirebaseInterface
{

    public function __construct()
    {
        $this->firestore = new FirestoreHttpClient();
    }

    /**
     * Stores created user status and id into Firestore Collection
     *
     */
    public function storeUserStatusIntoFireStore($user)
    {
        $data['collection'] = 'users';
        $data['identifier'] = $user['user_id'];
        $data['data'] = array(
            'user_id' => $user['user_id'],
            'status' => $user['status_name'],
        );
        //calling fire store client to store into collections
        $this->firestore->storeIntoCollections($data);
    }

    /**
     * Stores the user's review status into the Firestore collection.
     *
     * @param array $user The user data containing user_id and review_status_code.
     * 
     * This function organizes the user data into a format suitable for storage
     * in the 'users' collection in Firestore and then calls the Firestore client
     * to store the data.
     */
    public function storeUserReviewStatusIntoFireStore($user)
    {
        $data['collection'] = 'users';
        $data['identifier'] = $user['user_id'];
        $data['data'] = array(
            'user_id' => $user['user_id'],
            'review_status_code' => $user['review_status_code'],
        );
        //calling fire store client to store into collections
        $this->firestore->storeIntoCollections($data);
    }

    /**
     * Update Terminals Notification Count in Firestore Collection
     *
     */
    public function updateTerminalNotificationCountIntoFireStore($params)
    {
        $data['collection'] = 'terminals';
        $data['identifier'] = $params['unique_identification_id'];
        $data['data'] = array(
            'unique_identification_id' => $params['unique_identification_id'],
            'notification_count' => $params['notification_count'],
        );
        //calling fire store client to store into collection
        $this->firestore->storeIntoCollections($data);
    }

    /**
     * Updates the all_bank_delined status if all the banks are delinked
     *
     */
    public function storebankdelikedstatus($user)
    {
        $data['collection'] = 'users';
        $data['identifier'] = $user['user_id'];
        $data['data'] = array(
            'user_id' => $user['user_id'],
            'all_bank_delinked' => $user['all_bank_delinked'],
            'bank_link_type' => $user['bank_link_type'],
            'active_account_no' => $user['active_account_no'],
            'active_account_id' => $user['active_account_id'],
        );
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "All Bank Delinked Flag updated in Firebase for User ID: " . $user['user_id']);
        //calling fire store client to store into collections
        $this->firestore->storeIntoCollections($data);
    }
    
    /**
     * Removes MX action needed data from Firestore.
     *
     * @param array $params The parameters for removing the MX action needed data.
     *                     It should contain the following key:
     *                     - user_id: The identifier of the user.
     * @throws Some_Exception_Class If an error occurs while removing the data.
     * @return void
     */
    public function removeMxActionNeededData($params)
    {
        $data['collection'] = 'users';
        $data['identifier'] = $params['user_id'];
        $data['data'] = array(
            'mx_action_needed' => null,
        );
        //calling fire store client to store into collection
        $this->firestore->storeIntoCollections($data);
    }

}
