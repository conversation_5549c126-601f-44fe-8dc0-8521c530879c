<template>
  <div class="content-wrapper" style="min-height: 36px;">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <b-button
                variant="outline-success"
                style="margin-top: -48px;"
                @click="openModal('add')"
              >
                <i class="fas fa-user-plus"></i> Add New
              </b-button>
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Blacklisted Routing Numbers</h3>
                  <b-button
                  class="btn-danger export-api-btn"
                  @click="reloadDatatable"
                  v-if="showReloadBtn"
                  >
                    <i class="fas fa-redo"></i> Reload
                  </b-button>
                </div>
                <!-- /.card-header -->
                <div class="card-body">
                  <table
                    id="routingNumbersTable"
                    class="table"
                    style="width:100%;white-space: normal;"
                  >
                    <thead>
                      <tr>
                        <th>Routing Number</th>
                        <th>Added On</th>
                      </tr>
                    </thead>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
    <!-- Add routing numbers modal start -->
    <b-modal
      id="routing-number-modal"
      ref="routing-number-modal"
      ok-title="Add"
      cancel-title="Close"
      ok-variant="success"
      @ok="save"
      cancel-variant="outline-secondary"
      hide-header
      :no-close-on-esc="true"
      :no-close-on-backdrop="true"
    >
      <div class="row">
        <div class="col-12">
          <label for="routing_number">
            Enter New Routing Number
            <span class="red">*</span>
          </label>
          <input name="routing_number" type="text" v-model="routing_number" class="form-control" />
        </div>
      </div>
      <div class="row" v-if="showMsg">
        <div class="col-12">
          <label for="routing_number" class="red">Please fill in the required fields.</label>
        </div>
      </div>
      <div class="row" style="margin-bottom: 40px;"></div>
    </b-modal>
    <!-- Transaction cancellation comment modal end -->
  </div>
</template>
<script>
import api from "@/api/blacklist.js";
import commonConstants from "@/common/constant.js";
export default {
  data() {
    return {
      showMsg: false,
      headerTextVariant: "light",
      allRoutingNumbers: {},
      routing_number: "",
      showReloadBtn:false,
      constants: commonConstants,
    };
  },
  methods: {
    reloadDatatable(){
      var self = this;
      self.loadDT();
    },
    openModal(type) {
      var self = this;
      self.resetModal();
      self.$bvModal.show("routing-number-modal");
    },
    resetModal() {
      var self = this;
      self.routing_number = "";
    },
    save(bvModalEvt) {
      var self = this;
      var request = {
        routing_number: self.routing_number,
      };
      if (self.routing_number == "") {
        self.showMsg = true;
        bvModalEvt.preventDefault();
      } else {
        self.showMsg = false;
        bvModalEvt.preventDefault();
        api
          .addRoutingNumber(request)
          .then((response) => {
            if (response.code == 200) {
              success(response.message);
              self.loadDT();
              self.$bvModal.hide("routing-number-modal");
              self.resetModal();
            } else {
              error(response.message);
            }
          })
          .catch((err) => {
            error(err.response.data.message);
          });
      }
    },
    loadDT: function () {
      var self = this;
      $("#routingNumbersTable").DataTable({
        pagingType: "simple_numbers",
        processing: true,
        serverSide: true,
        destroy: true,
        columnDefs: [
          { className: "dt-center", targets: [0, 1] },
        ],
        order: [[1, "desc"]],
        orderClasses: false,
        language: {
          processing:
            '<i class="fa fa-spinner fa-spin fa-3x fa-fw"></i><span class="sr-only">Loading...</span> ',
          emptyTable: "No Routing Number Available.",
          search: "_INPUT_",
          searchPlaceholder: "Search records",
          oPaginate: {
            sNext: '<i class="fas fa-angle-double-right"></i>',
            sPrevious: '<i class="fas fa-angle-double-left"></i>',
          },
          sLengthMenu:
            "<label class='label_dropdown_dt'>Per page</label> _MENU_",
        },
        ajax: {
          headers: {
            Authorization: "Bearer " + localStorage.getItem("token"),
          },
          url: "/api/getAllRoutingNumbers",
          type: "POST",
          data: { _token: "{{csrf_token()}}" },
          dataType: "json",
          dataSrc: function (result) {
            self.showReloadBtn = false;
            self.allRoutingNumbers = result.data;
            return self.allRoutingNumbers;
          },
          error: function(data){
            error(commonConstants.datatable_error);
            $('#routingNumbersTable_processing').hide();
            self.showReloadBtn = true;
          }
        },
        columns: [{ data: "routing_number" }, { data: "created_at" }],
      });

      $("#routingNumbersTable").on("page.dt", function () {
        $("html, body").animate({ scrollTop: 0 }, "slow");
        $("th:first-child").focus();
      });

      //Search in the table only after 3 characters are typed
      // Call datatables, and return the API to the variable for use in our code
      // Binds datatables to all elements with a class of datatable
      var dtable = $("#routingNumbersTable").dataTable().api();

      // Grab the datatables input box and alter how it is bound to events
      $(".dataTables_filter input")
      .unbind() // Unbind previous default bindings
      .bind("input", function(e) { // Bind our desired behavior
          // If the length is 3 or more characters, or the user pressed ENTER, search
          if(this.value.length >= 3 || e.keyCode == 13) {
              // Call the API search function
              dtable.search(this.value).draw();
          }
          // Ensure we clear the search if they backspace far enough
          if(this.value == "") {
              dtable.search("").draw();
          }
          return;
      });
    },
  },
  mounted() {
    var self = this;
    setTimeout(function () {
      self.loadDT();
    }, 1000);
    document.title = "CanPay - BlackLists";
  },
};
</script>

