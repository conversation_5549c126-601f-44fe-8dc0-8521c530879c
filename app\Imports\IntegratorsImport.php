<?php

namespace App\Imports;

use App\Models\LegalEntityInfo;
use App\Models\MerchantApiKeyMap;
use App\Models\MerchantStores;
use App\Models\RegisteredMerchantMaster;
use App\Models\StatusMaster;
use App\Models\TerminalMaster;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class IntegratorsImport implements ToModel, WithHeadingRow
{
    /**
     * @param array $row
     * This function actully imports the data as row from Excel Sheet. Here we used the WithHeadingRow to get the Data with Heading. Do Not try to get the rows with index.
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row)
    {
        //logger($row);exit;
        DB::beginTransaction();
        try {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Importing 3rd party Integrators....");
            // Insertion Started for Registered Merchant Master
            $merchantmaster = new RegisteredMerchantMaster();
            $merchantmaster->merchant_id = generateRandomString(4);
            $merchantmaster->merchant_name = $row['primary_contact'];
            $merchantmaster->email = $row['primary_contact_email'];
            $merchantmaster->is_vendor = 1;
            $merchantmaster->save();

            $merchant_id = $merchantmaster->id;

            // Insertion Started for Merchant's Legal Entity Info
            $legal_entity_info = new LegalEntityInfo();
            $legal_entity_info->merchant_id = $merchant_id;
            $legal_entity_info->legal_entity_name = $row['legal_entity_name'];
            $legal_entity_info->corporate_contact_name = $row['primary_contact'];
            $legal_entity_info->email = $row['primary_contact_email'];
            $legal_entity_info->save();

            //Fetch the Active Status ID from Status Master table
            $storestatus = StatusMaster::where('status',ACTIVE)->first();
            $storestatusid = $storestatus->id;

            // Insertion Started for Merchant Stores with Relation
            $merchantstores = new MerchantStores();
            $merchantstores->merchant_id = $merchant_id;
            $merchantstores->store_id = generateRandomString(4);
            $merchantstores->retailer = $row['dba_name'];
            $merchantstores->website_address = $row['website'];
            $merchantstores->status = $storestatusid;
            $merchantstores->save();

            //after merchant and store creation new terminal needs to be created for the 3rd party merchants
            $status = StatusMaster::where("status", TEMPLATE_ACTIVE)->first();
            $params['merchant_store_id'] = $merchantstores->id;
            $params['status'] = $status->id;

            switch (strtoupper($row['type_of_integration_pos_web_both'])) {
                case TERMINAL_WEB:
                    $params['terminal_name'] = "WEB " . $merchantstores->store_id;
                    $params['is_web'] = 1;
                    $this->_createTerminal($params);
                    break;
                case TERMINAL_POS:
                    $params['is_web'] = 0;
                    $params['terminal_name'] = "POS " . $merchantstores->store_id;
                    $this->_createTerminal($params);
                    break;
                case TERMINAL_BOTH:
                    $params['terminal_name'] = "WEB " . $merchantstores->store_id;
                    $params['is_web'] = 1;
                    $this->_createTerminal($params);
                    $params['terminal_name'] = "POS " . $merchantstores->store_id;
                    $params['is_web'] = 0;
                    $this->_createTerminal($params);
                    break;
            }

            //create all the api keys which will be used by the 3rd party merchants during API implementation
            $key_details = new MerchantApiKeyMap();
            $key_details->merchant_id = $merchant_id;
            $key_details->app_key = config('app.api_environment') . "_key_" . generateRandomString(8);
            $key_details->api_secret = generateRandomString(8);
            $key_details->status = $status->id;
            $key_details->save();

            DB::commit();
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Integrators and its relevant Information added for Merchant ID : " . $merchantmaster->merchant_id);
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured during integrator information insertion for Merchant ID : " . $merchantmaster->merchant_id . ".", [EXCEPTION => $e]);
            DB::rollback();
        }
    }

    private function _createTerminal($params)
    {
        // Insertion begins in Terminal Master Table
        $terminal = new TerminalMaster();
        $terminal->merchant_store_id = $params['merchant_store_id'];
        $terminal->terminal_name = $params['terminal_name'];
        $terminal->unique_identification_id = generateUUID();
        $terminal->status = $params['status'];
        $terminal->is_web = $params['is_web'];
        $terminal->save();
    }
}
