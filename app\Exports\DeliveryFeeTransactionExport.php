<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maat<PERSON>bsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Events\AfterSheet;

class DeliveryFeeTransactionExport implements FromCollection, WithHeadings, ShouldAutoSize, WithEvents, WithColumnFormatting
{

    protected $request;

    public function __construct($request)
    {
        $this->request = $request; // Declaring the request variable
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        $collection_array = $this->request->get('report'); // Storing the array received from request

        $transactions = array();
        foreach ($collection_array as $transaction) {
            $nestedData['transaction_number'] = $transaction['transaction_number'];
            $nestedData['consumer_name'] = $transaction['consumer_name'];
            $nestedData['delivery_partner'] = $transaction['delivery_partner'];
            $nestedData['merchant_name'] = $transaction['merchant_name'];
            $nestedData['store_name'] = $transaction['store_name'];
            $nestedData['terminal_name'] = $transaction['terminal_name'];
            $nestedData['amount'] = $transaction['amount'] == null ? 0.00 : $transaction['amount'];
            $nestedData['delivery_fee'] = $transaction['delivery_fee'] == null ? '0.00' : $transaction['delivery_fee'];
            $nestedData['merchant_funding_amount'] = $transaction['merchant_funding_amount'] == null ? '0.00' : $transaction['merchant_funding_amount'];
            $nestedData['transaction_time'] = str_replace("<br/>", " ", $transaction['transaction_time']);
            $nestedData['status'] = $transaction['status'];

            array_push($transactions, $nestedData);
        }

        return collect([
            $transactions,
        ]);
    }

    public function headings(): array
    {
        $returnArray = array(
            [
                'Transaction Number',
                'Consumer',
                'Delivery Partner',
                'Merchant',
                'Store',
                'Terminal',
                'Amount ($)',
                'Delivery Fee ($)',
                'Merchant Funding Amount ($)',
                'Transaction Time',
                'Status',
            ],
        );

        return $returnArray;
    }

    public function columnFormats(): array
    {
        return [
            'G' => '0.00',
            'H' => '0.00',
            'I' => '0.00',
        ];
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $event->sheet->getStyle('A1:L1')->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);

                //Apply Center Alignment
                $event->sheet->getStyle('A:J')->getAlignment()->applyFromArray(
                    array('horizontal' => 'center')
                );
            },
        ];
    }
}
