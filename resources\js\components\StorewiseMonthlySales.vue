<template>
<div>
  <div v-if="loading">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Storewise Monthly Sales</h3>
                </div>

                <div class="card-body">
                <div class="row">
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="start-date form-control"
                        placeholder="Start Date"
                        id="start-date"
                        onkeydown="return false"
                        autocomplete="off"
                        @input="dateDiff"
                      />
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="end-date form-control"
                        placeholder="End Date"
                        id="end-date"
                        onkeydown="return false"
                        autocomplete="off"
                        @input="dateDiff"
                      />
                    </div>
                  </div>
                </div>
                <small class="text-red" id="error-code" style="display:none;">The report period should be of maximum 40 days.</small>
              </div>
              <div class="card-footer">
                <button
                  type="button"
                  class="btn btn-success"
                  id="generateBtn"
                  @click="generateReport(false)"
                >
                  Generate
                </button>
                <button
                  type="button"
                  @click="generateReport(true)"
                  class="btn btn-danger ml-10"
                  id="generateExportBtn"
                >
                  Generate & Export<i
                    class="fa fa-download ml-10"
                    aria-hidden="true"
                  ></i>
                </button>
              </div>


              <div class="card-body">
                <div class="row">
                  <div class="col-12">
                    <b-table-simple
                      responsive
                      show-empty
                      bordered
                      sticky-header="800px"
                    >
                      <b-thead head-variant="light">
                        <b-tr>
                          <b-th class="text-center">Store ID</b-th>
                          <b-th class="text-center">Retailer</b-th>
                          <b-th class="text-center">Routing Number</b-th>
                          <b-th class="text-center">Transaction Count</b-th>
                          <b-th class="text-center">Sales</b-th>
                        </b-tr>
                      </b-thead>
                      <b-tbody v-if="report.length > 0">
                        <b-tr v-for="(row, index) in report" :key="index">
                          <b-td class="text-center text-gray">{{
                            row.store_id
                          }}</b-td>
                          <b-td class="text-center text-gray">{{
                            row.retailer
                          }}</b-td>
                          <b-td class="text-center text-gray">{{
                            row.routing_no
                          }}</b-td>
                          <b-td class="text-center text-gray">{{
                            row.transaction_count
                          }}</b-td>
                          <b-td class="text-center text-gray">{{
                            row.sales
                          }}</b-td>
                        </b-tr>
                      </b-tbody>
                      <b-tbody v-else>
                          <b-tr>
                            <b-td colspan="5" class="text-center text-gray">There are no records to show</b-td>
                          </b-tr>
                      </b-tbody>
                    </b-table-simple>
                  </div>
                </div>
              </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</div>
</template>
<script>
import api from "@/api/reports.js";
import apiuser from "@/api/user.js";
import moment from "moment";
import { saveAs } from "file-saver";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "./CustomLoader/CanPayLoader.vue"
export default {
  data() {
    return {
      report: [],
      loading: false,
      storeList: [],
      selectedStore: "",
      cpList: [],
      selectedCp: "",
      isLoadingCp: false,
      isLoadingSt: false
    };
  },
  components: {
    HourGlass,
    CanPayLoader
  },
  watch: {
  },
  created() {
  },
  mounted() {
    var self = this;
    $("#start-date,#end-date").datepicker({
      format: "mm/dd/yyyy",
      autoclose: true,
      todayHighlight: true,
    }).on('changeDate', function (ev) {
        self.dateDiff();
    });
    $("#start-date , #end-date").datepicker("setDate", new Date());
  },
  methods: {
    dateDiff(){
      var self = this;
      if ($("#start-date").val() != "") {
        var from_date = moment($("#start-date").val()).format("YYYY-MM-DD");
      } else {
        var from_date = "";
      }
      if ($("#end-date").val() != "") {
        var to_date = moment($("#end-date").val()).format("YYYY-MM-DD");
      } else {
        var to_date = "";
      }
      if(from_date!='' && to_date!=''){
        //calculate the date Difference
        var date1 = new Date(from_date);
        var date2 = new Date(to_date);
        // To calculate the time difference of two dates
        var Difference_In_Time = date2.getTime() - date1.getTime();

        // To calculate the no. of days between two dates
        var Difference_In_Days = Difference_In_Time / (1000 * 3600 * 24);

        if(Difference_In_Days > 39){
          $("#generateBtn,#generateExportBtn").prop('disabled', true);
          $("#error-code").fadeIn(500);
        }else{
          $("#generateBtn,#generateExportBtn").prop('disabled', false);
          $("#error-code").fadeOut(500);
        }
      }
    },
    // API call to generate the storewise monthly sales report
    generateReport(reportExport) {
      var self = this;
      if (
        moment($("#start-date").val()).format("YYYY-MM-DD") >
        moment($().val()).format("YYYY-MM-DD")
      ) {
        error("Start date cannot be from future.");
        return false;
      }
      if (
        moment($("#end-date").val()).format("YYYY-MM-DD") >
        moment($().val()).format("YYYY-MM-DD")
      ) {
        error("End date cannot be from future.");
        return false;
      }
      self.report = [];
      var request = {
        from_date: moment($("#start-date").val()).format("YYYY-MM-DD"),
        to_date: moment($("#end-date").val()).format("YYYY-MM-DD"),
      };
      if(request.from_date > request.to_date){
        error("To Date cannot be greater than From date");
        return false;
      }
      self.loading = true;
      api
        .generateStorewiseMonthlySales(request)
        .then(function (response) {
        if (response.code == 200 && response.data.length > 0) {
            self.report = response.data;
            if (reportExport) {
                self.exportReport();
            } else {
                self.loading = false;
            }
        } else {
            error(response.message);
            self.loading = false;
        }
        })
        .catch(function (error) {
          self.loading = false;
        });
    },

    // exports the report
    exportReport() {
      var self = this;
      self.loading = true;
      var request = {
        report: self.report,
        from_date: moment($("#start-date").val()).format("YYYY-MM-DD"),
        to_date: moment($("#end-date").val()).format("YYYY-MM-DD"),
      };
      api
        .exportGenerateStorewiseMonthlySales(request)
        .then(function (response) {
          var FileSaver = require("file-saver");
          var blob = new Blob([response], {
            type: "application/xlsx",
          });
          FileSaver.saveAs(
            blob,
            moment().format("MM/DD/YYYY") + "_storewise_monthly_sales.xlsx"
          );
          self.loading = false;
        })
        .catch(function (error) {
          // error(error);
          self.loading = false;
        });
    },
  },
};
</script>
