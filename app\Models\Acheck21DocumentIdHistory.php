<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
class Acheck21DocumentIdHistory extends Model
{
    protected $table = 'acheck21_document_id_history';

    /**
     * __construct
     * This function will insert an alphanumeric 32 character unique id as primary key in the table whenever a data get inserted
     * @param  mixed $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        $this->id = generateUUID();

        parent::__construct($attributes);
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'transaction_id',
        'transaction_ref_no',
        'amount',
        'acheck_document_id',
        'ignore_flag',
    ];
    public $timestamps = true;
    public $incrementing = false;
}
