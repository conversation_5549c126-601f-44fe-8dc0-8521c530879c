<template>
<div>
    <div v-if="is_visible == 1">
        <CanPayLoader/>
    </div>  
    <div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    
    </section>
    <div class="hold-transition sidebar-mini">
        <section class="content">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-12">
                        <div class="card card-success">
                            <div class="card-header">
                                <h3 class="card-title">MX Institutions</h3>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-4">
                                        <input
                                            class="form-control"
                                            placeholder="Bank code"
                                            id="code"
                                            v-model="code"
                                        />
                                    </div>
                                    <div class="col-4">
                                        <input
                                            class="form-control"
                                            placeholder="Bank name"
                                            id="name"
                                            v-model="name"
                                        />
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer">
                                <button
                                    type="button"
                                    class="btn btn-success"
                                    id="generateBtn"
                                    @click="mxInstitution()"
                                >
                                    Generate
                                </button>
                                <button
                                    type="button"
                                    class="btn btn-success margin-left-5"
                                    @click="reset()"
                                >
                                    Reset
                                </button>
                            </div>
                            <div class="card-body">
                                <div v-if="items.length>0">
                                <b-table-simple
                                    id="mx_instituition"
                                    show-empty
                                    bordered
                                    sticky-header="900px"

                                >
                                <b-thead head-variant="light">
                                <tr>
                                    <th>Logo</th>
                                    <th>Name</th>
                                    <th>Code</th>
                                    <th class="text-center">Supports Account Identification</th>
                                    <th class="text-center">Supports Account Statement</th>
                                    <th class="text-center">Supports Account Verification</th>
                                    <th>Url</th>
                                </tr>
                                </b-thead>
                                <b-tbody v-for="(row, index) in items" :key="index">
                                <b-tr>
                                    <b-td class="text-center text-gray">
                                     <b-img :src="row.small_logo_url"></b-img>
                                    </b-td>
                                    <b-td class="text-left text-gray">{{
                                    row.name
                                    }}</b-td>
                                    <b-td class="text-left text-gray">{{
                                    row.code
                                    }}</b-td>
                                    <b-td class="text-center text-gray">{{
                                    row.supports_account_identification
                                    }}</b-td>
                                    <b-td class="text-center text-gray">{{
                                    row.supports_account_statement
                                    }}</b-td>
                                    <b-td class="text-center text-gray">{{
                                    row.supports_account_verification
                                    }}</b-td>
                                    <b-td class="text-left text-gray">
                                    <a :href="row.url" target="_blank">{{ row.url }}</a>
                                    </b-td>
                                </b-tr>
                                </b-tbody>
                                </b-table-simple>
                               
                                <b-pagination
                                    v-model="currentPage"
                                    :total-rows="totalItems"
                                    :per-page="perPage"
                                    aria-controls="mx_instituition"
                                    align="right"
                                    prev-text="Prev"
                                    next-text="Next"
                                    :ellipsis="true"
                                    :limit="5"
                                ></b-pagination>
                                </div>
                                <div v-if="items.length==0" class="p-2">
                                    <p>No data displayed. Refine your search criteria.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
    </div>
</div>
</template>
<script>
import api from "@/api/mx.js";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "../CustomLoader/CanPayLoader.vue"
export default {
  components: {
    HourGlass,
    CanPayLoader
  },
    data(){
        return {
            code:'',
            name:'',
            currentPage: 1,
            perPage: 50,
            totalPage: 0,
            totalItems: 0,
            items:[],
            is_visible: 1,
        }
    },
    computed: {
      rows() {
        return this.items.length
      }
    },
    methods:{
        reset(){
          
            let self = this;
            self.currentPage = 1;
            self.code="";
            self.name="";
        },
         mxInstitution(){
        let self = this;
        self.is_visible = 1;
        if(self.code.length > 0  || self.name.length > 0){
            self.currentPage = 1;
        }
        const paylaod = {
            code:self.code,
            name:self.name,
            currentPage:self.currentPage,
            perPage:self.perPage
        }

        api.mxInstitution(paylaod)
        .then((response)=>{
           self.items = response.data.result;
           self.totalItems = response.data.total; 
           self.is_visible = 0;   
        })
        .catch((error)=>{
        error(response.data.message);
            self.is_visible = 0;
        })
        }
    },
    watch: {
        currentPage(newVal){
            this.mxInstitution();
        }
    },
    mounted(){
        let self = this;
        self.mxInstitution();
    }
}
</script>
