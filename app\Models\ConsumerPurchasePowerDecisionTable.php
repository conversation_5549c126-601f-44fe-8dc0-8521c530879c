<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ConsumerPurchasePowerDecisionTable extends Model
{
    protected $table = 'consumer_purchase_power_decision_table';

    /**
     * __construct
     * This function will insert an alphanumeric 32 character unique id as primary key in the table whenever a data get inserted
     * @param  mixed $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        $this->id = generateUUID();

        parent::__construct($attributes);
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'consumer_id',
        'account_id',
        'account_no',
        'balance',
        'percentage',
        'calculated_purchase_power',
        'risk_score',
        'prediction',
        'is_response_interrupted',
        'exception_message',
        'execution_time',
        'before_registration',
    ];
    public $timestamps = true;
    public $incrementing = false;
}
