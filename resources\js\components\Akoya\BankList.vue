<template>
<div>
  <div  v-if="loading">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Bank List</h3>
                </div>

                <!-- /.card-header -->
                <div class="card-body">
                  <div class="row">
                    <div class="col-md-4">
                      <div class="form-group">
                      <input
                          class="form-control"
                          placeholder="Bank Name / Routing No (min 3 chars)"
                          id="bankname"
                          v-model="searchBankName"
                        />
                      </div>
                    </div>

                  <div class="col-md-4">
                    <div class="form-group">
                      <select class="form-control" v-model="frontend_visibility">
                          <option value="">Select Frontend Visibility</option>
                          <option value="1">Yes</option>
                          <option value="0">No</option>
                      </select>
                    </div>
                  </div>

                  <div class="col-md-4">
                    <div class="form-group">
                      <select class="form-control" v-model="banking_solution">
                          <option value="">Select Banking Solution</option>
                          <option value="finicity" v-if="finicity_active">Finicity</option>
                          <option value="akoya" v-if="akoya_active">Akoya</option>
                          <option value="mx" v-if="mx_active">MX</option>
                      </select>
                    </div>
                  </div>

                  <div class="col-md-4">
                    <div class="form-group">
                      <select class="form-control" v-model="tan_from_fi">
                          <option value="">Select TAN Existence</option>
                          <option value="1">Yes</option>
                          <option value="0">No</option>
                      </select>
                    </div>
                  </div>

                  </div>
                </div>
                  <div class="card-footer">
                    <button
                      type="button"
                      class="btn btn-success"
                      @click="searchFilter()"
                    >
                      Search
                    </button>
                    <button
                      type="button"
                      @click="reset()"
                      class="btn btn-success margin-left-5"
                    >
                      Reset
                    </button>
                    <button
                      type="button"
                      class="btn btn-success margin-left-5"
                      @click="showAddModal()"
                    >
                      Add Bank
                    </button>
                  </div>
                 <div class="card-body">
                  <b-table-simple
                      responsive
                      show-empty
                      bordered
                      sticky-header="800px"
                      v-if="allBankModel.length > 0"
                    >
                      <b-thead head-variant="light">
                        <tr>
                          <th>Bank Name</th>
                          <th>Frontend Visibility</th>
                          <th>TAN Existence</th>
                          <th v-if="finicity_active">Finicity Allowed</th>
                          <th v-if="akoya_active">Akoya Allowed</th>
                          <th v-if="mx_active">MX Allowed</th>
                          <th class="text-center">Action</th>
                        </tr>
                      </b-thead>
                      <b-tbody v-for="(row, index) in allBankModel" :key="index">
                        <b-tr>
                          <b-td class="text-left text-gray">{{
                            row.bank_name
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.frontend_visibility == 1 ? 'Yes' : 'No'
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.tan_from_fi == 1 ? 'Yes' : 'No'
                          }}</b-td>
                          <b-td class="text-left text-gray" v-if="finicity_active">{{
                            row.is_finicity == 1 ? 'Yes' : 'No'
                          }}</b-td>
                          <b-td class="text-left text-gray" v-if="akoya_active">{{
                            row.is_akoya == 1 ? 'Yes' : 'No'
                          }}</b-td>
                          <b-td class="text-left text-gray" v-if="mx_active">{{
                            row.is_mx == 1 ? 'Yes' : 'No'
                          }}</b-td>
                          <b-td class="text-center text-gray">
                            <a :data-user-id="row.id" class="custom-edit-btn" title="View Routing Numbers" variant="outline-success" style="border:none" @click="viewAllRouting(row)"><i class="nav-icon fas fa-eye"></i></a>
                            <a :data-user-id="row.id" class="custom-edit-btn" title="Edit Bank" variant="outline-success" style="border:none" @click="editBank(row)"><i class="nav-icon fas fa-edit"></i></a>
                            <a :data-user-id="row.id" class="custom-edit-btn" title="Add Routing Number" variant="outline-success" style="border:none" @click="addRoutingNumber(row)"><i class="nav-icon fas fa-plus"></i></a>
                          </b-td>
                        </b-tr>
                      </b-tbody>
                    </b-table-simple>
                    <!-- Display pagination controls -->
                    <div v-if="allBankModel.length > 0" style="float:right">
                        <b-pagination
                            v-model="currentPage"
                            :total-rows="totalItems"
                            :per-page="perPage"
                            prev-text="Prev"
                            next-text="Next"
                            :ellipsis="true"
                            :limit="5"
                        ></b-pagination>
                    </div>
                    <p v-else>No data displayed. Please refine your search criteria.</p>
                    </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
    <!-- CP Modal Start -->
    <b-modal
      id="bank-modal"
      ref="modal"
      :header-text-variant="headerTextVariant"
      :title="modalTitle"
      @show="resetModal"
      @hidden="resetModal"
      ok-title="Save"
      ok-variant="success"
      cancel-variant="outline-secondary"
      @ok="handleOk"
      :no-close-on-esc="true"
      :no-close-on-backdrop="true"
    >
      <form ref="form" @submit.stop.prevent="save" class="needs-validation">

        <div class="row">
          <div class="col-md-12">
            <label for="bank_name">
              Bank Name
            </label>
            <input
              id="bank_name"
              name="bank_name"
              type="text"
              disabled='disabled'
              v-model="bankModel.bank_name"
              class="form-control"
            />
          </div>
        </div>

        <div class="row">
            <div class="col-md-12">
              <label for="bankLogo"> Bank Logo
              <a href="javascript:void(0)" v-b-tooltip.hover title="Bank Logo will be shown in Bank listing landing page.">
                  <svg  width="15px" version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
                  viewBox="0 0 490 490" style="margin-bottom: 2px;enable-background:new 0 0 490 490;" xml:space="preserve"><g><g><g><path d="M245,490C109.9,490,0,380.1,0,245S109.9,0,245,0s245,109.9,245,245S380.1,490,245,490z M245,62C144.1,62,62,144.1,62,245s82.1,183,183,183s183-82.1,183-183S345.9,62,245,62z"/></g><g><g><circle cx="241.3" cy="159.2" r="29.1"/></g><g><polygon points="285.1,359.9 270.4,359.9 219.6,359.9 204.9,359.9 204.9,321 219.6,321 219.6,254.8 205.1,254.8 205.1,215.9 219.6,215.9 263.1,215.9 270.4,215.9 270.4,321 285.1,321 				"/></g></g></g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g>
                  </svg>
              </a>
                 (151 x 88)</label>
              <div class="input-group">
                <div class="custom-file">
                  <input
                    type="file"
                    ref="logo_file"
                    name="bankLogo"
                    id="bankLogo"
                    v-on:change="handleFileUpload()"
                    class="custom-file-input"
                    accept="image/*"
                  />
                  <label for="bankLogo" class="custom-file-label">{{
                    logo_fileName
                  }}</label>
                </div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6">
            </div>
            <div class="col-md-6">
              <b-button
                style="float:right; margin-top: 7px"
                @click="clear"
              >
                Clear File
              </b-button>
            </div>
          </div>
        <div class="row">
            <div class="col-md-12">
              <label for="smallBankLogo"> Small Logo
              <!-- sgv start -->
              <a href="javascript:void(0)" v-b-tooltip.hover title="Bank Small Logo will be used beside Bank Name in Bank search field.">
                  <svg  width="15px" version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
                  viewBox="0 0 490 490" style="margin-bottom: 3px;enable-background:new 0 0 490 490;" xml:space="preserve"><g><g><g><path d="M245,490C109.9,490,0,380.1,0,245S109.9,0,245,0s245,109.9,245,245S380.1,490,245,490z M245,62C144.1,62,62,144.1,62,245s82.1,183,183,183s183-82.1,183-183S345.9,62,245,62z"/></g><g><g><circle cx="241.3" cy="159.2" r="29.1"/></g><g><polygon points="285.1,359.9 270.4,359.9 219.6,359.9 204.9,359.9 204.9,321 219.6,321 219.6,254.8 205.1,254.8 205.1,215.9 219.6,215.9 263.1,215.9 270.4,215.9 270.4,321 285.1,321 				"/></g></g></g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g>
                  </svg>
              </a>
              <!-- sgv end -->
                 (50 x 50)</label>
              <div class="input-group">
                <div class="custom-file">
                  <input
                    type="file"
                    ref="small_logo_file"
                    name="smallBankLogo"
                    id="smallBankLogo"
                    v-on:change="handleFileChange()"
                    class="custom-file-input"
                    accept="image/*"
                  />
                  <label for="smallBankLogo" class="custom-file-label">{{
                    small_logo_fileName
                  }}</label>
                </div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6">
              <div class="float:left; mt-2">
                <span class="red-error  ml-1">Maximum size 5 mb is allowed</span>
              </div>

            </div>
            <div class="col-md-6">  
              <b-button
                style="float:right; margin-top: 7px"
                @click="smallLogoClear"
              >
                Clear File
              </b-button>
            </div>
          </div>
            <div class="row" v-if="bankModel.small_logo_base64_image">
              <div class="col-12"> 
              <label>
                Current Small Logo
              </label>
              </div>
              <div class="col-12">  
                <span></span>
               <img :src="'data:image/' + bankModel.small_logo_extension + ';base64,' + bankModel.small_logo_base64_image" alt="">
              </div>
            </div>
          <div class="row">
            <div class="col-md-12">
              <input type="checkbox" id="checkbox1" v-model="bankModel.tan_from_fi" />
              <label for="checkbox1">TAN From Financial Institution</label>
            </div>
          </div>
          <div class="row">
            <div class="col-md-12">
              <input type="checkbox" id="checkbox1" v-model="bankModel.frontend_visibility" />
              <label for="checkbox1">Frontend Visibility</label>
            </div>
          </div>
          <div class="row" v-if="finicity_active">
            <div class="col-md-12">
              <input type="checkbox" id="checkbox2" v-model="bankModel.is_finicity" />
              <label for="checkbox2">Finicity Allowed</label>
            </div>
          </div>
          <div class="row" v-if="akoya_active">
            <div class="col-md-12">
              <input type="checkbox" id="checkbox3" v-model="bankModel.is_akoya" />
              <label for="checkbox3">Akoya Allowed</label>
              <br>
              <b-form-input type="text" v-model="bankModel.akoya_provider_id" v-if="bankModel.is_akoya==1" placeholder="Akoya provider id"></b-form-input>
            </div>
          </div>
          <div class="row" v-if="mx_active">
            <div class="col-md-12">
              <input type="checkbox" id="checkbox4" v-model="bankModel.is_mx" />
              <label for="checkbox4">MX Allowed</label>
              <br>

              <b-form-input type="text" v-model="bankModel.mx_institution_code" v-if="bankModel.is_mx==1" placeholder="MX institution code"></b-form-input>
            </div>
          </div>
      </form>
    </b-modal>
    <!-- CP Modal End -->
    <!-- CP Modal Start -->
    <b-modal
      id="routing-list-modal"
      ref="routing-list-modal"
      :header-text-variant="headerTextVariant"
      title="Bank Routing Numbers List"
      hide-footer
    >
      <div class='row odd-row'>
        <p class="para-settings"><span class="font-bolder p-2 ">Bank Name:</span> {{bankModel.bank_name}}</p>
      </div>
      <div class="card-body">
      <div class="mb-4" style="width:90%;position:absolute;right:2.25rem;">
          <b-input-group size="md" >
            <b-form-input
              id="filter-input"
              v-model="filter"
              type="search"
              placeholder="Search for records..."
            ></b-form-input>
          </b-input-group>
      </div>
        <b-table
          id="routing_no_table"
          responsive
          :per-page="perPageRoutingNo"
          :current-page="currentPageRoutingNo"
          bordered
          :items="bankRoutingNumbers"
          :filter="filter"
          sticky-header="800px"
          :fields="routingNoField"
          head-variant="light"
          @filtered="onFiltered"
          class="margin-top-routing-class"
        >
        </b-table>
          <b-pagination
            v-model="currentPageRoutingNo"
            :total-rows="rowsRoutingNo"
            :per-page="perPageRoutingNo"
            aria-controls="routing_no_table"
            class="float-right"
          ></b-pagination>
      </div>
    </b-modal>
    <!-- CP Modal End -->
    <!-- Add Bank modal start -->
    <b-modal
      id="add-bank-modal"
      ref="add-bank-modal"
      :header-text-variant="headerTextVariant"
      title="Add Bank"
      hide-footer
    >

        <div class="row" >
          <div class="col-md-12  custom-dropdown">
            <label for="bank_name">
              Bank Name <span class="red-error">*</span>
            </label>
            <input
              id="bank_name"
              name="bank_name"
              type="text"
              placeholder="Add Bank Name"
              v-model="bankModel.bank_name"
              @input="upperCaseBankName()"
              class="form-control"
            />
          </div>
        </div>

        <div class="mt-1" v-if="bankModel.bank_name != null && bankModel.bank_name.length == 0">
          <p style="color:red;">Provide bank name.</p>
        </div>


        <div class="row">
          <div class="col-md-12">
            <label for="routing_no">
              Routing Number
              <!-- sgv start -->
              <a href="javascript:void(0)" v-b-tooltip.hover title="Add Multiple Routing Number Separated by Comma (Example: *********,*********)">
                  <svg  width="15px" version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
                  viewBox="0 0 490 490" style="margin-bottom: 5px;enable-background:new 0 0 490 490;" xml:space="preserve"><g><g><g><path d="M245,490C109.9,490,0,380.1,0,245S109.9,0,245,0s245,109.9,245,245S380.1,490,245,490z M245,62C144.1,62,62,144.1,62,245s82.1,183,183,183s183-82.1,183-183S345.9,62,245,62z"/></g><g><g><circle cx="241.3" cy="159.2" r="29.1"/></g><g><polygon points="285.1,359.9 270.4,359.9 219.6,359.9 204.9,359.9 204.9,321 219.6,321 219.6,254.8 205.1,254.8 205.1,215.9 219.6,215.9 263.1,215.9 270.4,215.9 270.4,321 285.1,321 				"/></g></g></g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g>
                  </svg>
              </a>
              <!-- sgv end -->
              <span class="red-error">*</span>
            </label>
            <input
              id="routing_no"
              name="routing_no"
              type="text"
              placeholder="Provide Routing Number"
              v-model="bankModel.routing_no"
              class="form-control"
            />
          </div>
        </div> 
        <div class="mt-1" v-if="(bankModel.routing_no!=null && bankModel.routing_no.length == 0)  || valid_routing_number">
          <p style="color:red;">{{error_message}}</p>
        </div>
        <div class="row">
          <div class="col-md-12">
            <label for="routing_no">
              State Code
            </label>
            <input
              id="state"
              name="state"
              type="text"
              @input="check_state()"
              placeholder="Provide State Code"
              v-model="bankModel.state"
              class="form-control"
            />
          </div>
        </div>
        <div class="row">
            <div class="col-md-12">
              <label for="bankLogo"> Bank Logo
              <!-- sgv start -->
              <a href="javascript:void(0)" v-b-tooltip.hover title="Bank Logo will be shown in Bank listing landing page.">
                  <svg  width="15px" version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
                  viewBox="0 0 490 490" style="margin-bottom: 2px;enable-background:new 0 0 490 490;" xml:space="preserve"><g><g><g><path d="M245,490C109.9,490,0,380.1,0,245S109.9,0,245,0s245,109.9,245,245S380.1,490,245,490z M245,62C144.1,62,62,144.1,62,245s82.1,183,183,183s183-82.1,183-183S345.9,62,245,62z"/></g><g><g><circle cx="241.3" cy="159.2" r="29.1"/></g><g><polygon points="285.1,359.9 270.4,359.9 219.6,359.9 204.9,359.9 204.9,321 219.6,321 219.6,254.8 205.1,254.8 205.1,215.9 219.6,215.9 263.1,215.9 270.4,215.9 270.4,321 285.1,321 				"/></g></g></g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g>
                  </svg>
              </a>
              <!-- sgv end -->
                 (151 x 88)</label>
              <div class="input-group">
                <div class="custom-file">
                  <input
                    type="file"
                    ref="logo_file"
                    name="bankLogo"
                    id="bankLogo"
                    v-on:change="handleFileUpload()"
                    class="custom-file-input"
                    accept="image/*"
                  />
                  <label for="bankLogo" class="custom-file-label">{{
                    logo_fileName
                  }}</label>
                </div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6">
            </div>
            <div class="col-md-6">
              <b-button
                style="float:right; margin-top: 7px"
                @click="clear"
              >
                Clear File
              </b-button>
            </div>
          </div>
        <div class="row">
            <div class="col-md-12">
              <label for="smallBankLogo"> Small Logo
              <!-- sgv start -->
              <a href="javascript:void(0)" v-b-tooltip.hover title="Bank Small Logo will be used beside Bank Name in Bank search field.">
                  <svg  width="15px" version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
                  viewBox="0 0 490 490" style="margin-bottom: 3px;enable-background:new 0 0 490 490;" xml:space="preserve"><g><g><g><path d="M245,490C109.9,490,0,380.1,0,245S109.9,0,245,0s245,109.9,245,245S380.1,490,245,490z M245,62C144.1,62,62,144.1,62,245s82.1,183,183,183s183-82.1,183-183S345.9,62,245,62z"/></g><g><g><circle cx="241.3" cy="159.2" r="29.1"/></g><g><polygon points="285.1,359.9 270.4,359.9 219.6,359.9 204.9,359.9 204.9,321 219.6,321 219.6,254.8 205.1,254.8 205.1,215.9 219.6,215.9 263.1,215.9 270.4,215.9 270.4,321 285.1,321 				"/></g></g></g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g>
                  </svg>
              </a>
              <!-- sgv end -->
               (50 x 50)</label>
              <div class="input-group">
                <div class="custom-file">
                  <input
                    type="file"
                    ref="small_logo_file"
                    name="smallBankLogo"
                    id="smallBankLogo"
                    v-on:change="handleFileChange()"
                    class="custom-file-input"
                    accept="image/*"
                  />
                  <label for="smallBankLogo" class="custom-file-label">{{
                    small_logo_fileName
                  }}</label>
                </div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6">
              <div class="float:left; mt-2">
                <span class="red-error  ml-1">Maximum size 5 mb is allowed</span>
              </div>

            </div>
            <div class="col-md-6">  
              <b-button
                style="float:right; margin-top: 7px"
                @click="smallLogoClear"
              >
                Clear File
              </b-button>
            </div>
          </div>

          <div class="row">
            <div class="col-md-12">
              <input type="checkbox" id="checkbox1" v-model="bankModel.tan_from_fi" />
              <label for="checkbox1">TAN From Financial Institution</label>
            </div>
          </div>
          <div class="row">
            <div class="col-md-12">
              <input type="checkbox" id="checkbox1" v-model="bankModel.frontend_visibility" />
              <label for="checkbox1">Frontend Visibility</label>
            </div>
          </div>
          <div class="row" v-if="finicity_active">
            <div class="col-md-12">
              <input type="checkbox" id="checkbox2" v-model="bankModel.is_finicity" />
              <label for="checkbox2">Finicity Allowed</label>
            </div>
          </div>
          <div class="row">
            <div class="col-md-12" v-if="akoya_active">
              <input type="checkbox" id="checkbox3" v-model="bankModel.is_akoya" />
              <label for="checkbox3">Akoya Allowed</label>
              <br>
              <b-form-input type="text" v-model="bankModel.akoya_provider_id" v-if="bankModel.is_akoya==1" placeholder="Akoya provider id"></b-form-input>
            </div>
          </div>
          <div class="row" v-if="mx_active">
            <div class="col-md-12">
              <input type="checkbox" id="checkbox4" v-model="bankModel.is_mx" />
              <label for="checkbox4">MX Allowed</label>
              <br>

              <b-form-input type="text" v-model="bankModel.mx_institution_code" v-if="bankModel.is_mx==1" placeholder="MX institution code"></b-form-input>
            </div>
          </div>
          <hr>
      <div class="row float-right">
        <div class="col-12">
          <b-button type="submit" variant="outline-secondary" v-on:click="closeAddBankModal">
            Cancel
          </b-button>
          <b-button type="submit" variant="success" @click="saveBank">
            Save
          </b-button>
        </div>

      </div>
    </b-modal>
    <!-- Add Bank modal end -->
    <!-- Add Routing Number start -->
    <b-modal
      id="add-routing-number-modal"
      ref="add-routing-number-modal"
      :header-text-variant="headerTextVariant"
      title="Add Routing Number"
      hide-footer
    >
      <div>
        <div class="row">
          <div class="col-md-12">
            <label for="bank_name">
              Bank Name
            </label>
            <input
              id="bank_name"
              name="bank_name"
              type="text"
              disabled='disabled'
              v-model="bankModel.bank_name"
              class="form-control"
            />
          </div>
        </div>
      </div>
        <div class="row">
          <div class="col-md-12">
            <label for="routing_no">
              Routing Number 
              <!-- sgv start -->
              <a href="javascript:void(0)" v-b-tooltip.hover title="Add Multiple Routing Number Separated by Comma (Example: *********,*********)">
                  <svg width="15px" version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
                  viewBox="0 0 490 490" style="margin-bottom: 5px;enable-background:new 0 0 490 490;" xml:space="preserve"><g><g><g><path d="M245,490C109.9,490,0,380.1,0,245S109.9,0,245,0s245,109.9,245,245S380.1,490,245,490z M245,62C144.1,62,62,144.1,62,245s82.1,183,183,183s183-82.1,183-183S345.9,62,245,62z"/></g><g><g><circle cx="241.3" cy="159.2" r="29.1"/></g><g><polygon points="285.1,359.9 270.4,359.9 219.6,359.9 204.9,359.9 204.9,321 219.6,321 219.6,254.8 205.1,254.8 205.1,215.9 219.6,215.9 263.1,215.9 270.4,215.9 270.4,321 285.1,321 				"/></g></g></g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g><g></g>
                  </svg>
              </a>
              <!-- sgv end -->
              <span class="red-error">*</span>
            </label>
            <input
              id="routing_no"
              name="routing_no"
              type="text"
              placeholder="Provide Routing Number"
              v-model="bankModel.routing_no"
              class="form-control"
            />
          </div>
        </div> 
        <div class="mt-1" v-if="bankModel.routing_no!=null &&  valid_routing_number">
          <p style="color:red;">{{error_message}}</p>
        </div>
        <div class="row">
          <div class="col-md-12">
            <label for="routing_no">
              State
            </label>
            <input
              id="routing_no"
              name="routing_no"
              type="text"
              placeholder="Provide State Code"
              v-model="bankModel.state"
              class="form-control"
            />
          </div>
        </div> 
        <div class="row float-right">
          <div class="col-12 mt-4">
            <b-button type="submit" variant="outline-secondary" v-on:click="closeAddRoutingNumberModal">
              Cancel
            </b-button>
            <b-button type="submit" variant="success" @click="saveRoutingNumber">
              Save
            </b-button>
          </div>

        </div>
    </b-modal>
    <!-- Add Routing Number end -->
  </div>
</div>
</template>
<script>
import api from "@/api/bank.js";
import { validationMixin } from "vuelidate";
import { required, minLength } from "vuelidate/lib/validators";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "../CustomLoader/CanPayLoader.vue"
export default {
  mixins: [validationMixin],
  data() {
    return {
      bankModel: {},
      allBankModel: {},
      currentUser: localStorage.getItem("user")
        ? JSON.parse(localStorage.getItem("user"))
        : null,
      loading:false,
      currentPage: 1,
      perPage: 10,
      totalPage: 0,
      totalItems: 0,
      searchBankName: '',
      headerTextVariant: "light",
      modalTitle: "Edit Bank",
      upload_changed: false,
      small_upload_changed:false,
      logo_file: null,
      small_logo_file:null,
      frontend_visibility: '',
      tan_from_fi: '',
      banking_solution: '',
      logo_fileName: "Choose File",
      small_logo_fileName: "Choose File",
      bankRoutingNumbers: {},
      options: [],
      value: '',
      numeric_error_routing_number:false,
      numeric_error_new_routing_number:false,
      showDropdown: false,
      bankNameOptions:[],
      valid_routing_number:false,
      error_message:'',
      currentPageRoutingNo:1,
      rowsRoutingNo:0,
      perPageRoutingNo:10,
      routingNoField: [
      {
        key: 'routing_no',
        label:'Routing Number'
      },
      {
        key: 'new_routing_no',
        label:'New Routing Number'
      },
      {
        key: 'state',
        label: 'State'
      }],
      filter: null,
      akoya_active:true,
      finicity_active:true,
      mx_active:true
    };
  },
  components: {
    HourGlass,
    CanPayLoader
  },
  created() {
    this.searchBank();
  },
  watch: {
    currentPage(newPage) {
      this.searchBank(); // Call the method to fetch the users based on the new page
    },
    'bankModel.routing_no':function(newValue, oldValue){
      let self = this;
      if(self.bankModel.routing_no!=null)
      self.routing_no_validation();
    },
    'bankModel.state':function(newValue, oldValue){
      let self = this;
      if(self.bankModel.state!=undefined){
        self.bankModel.state = self.bankModel.state.toUpperCase();  
        self.check_state();
      }

    }
  },
  methods: {
    onFiltered(filteredItems) {
      this.rowsRoutingNo = filteredItems.length
      this.currentPageRoutingNo = 1
    },
    resetModal() {
      var self = this;
      self.upload_changed = false;
      self.logo_file = null;
      self.logo_fileName = "Choose File";
    },
    upperCaseBankName(){
      let self = this;
      self.bankModel.bank_name = self.bankModel.bank_name.toUpperCase();
    },
    addRoutingNumber(bank){
      let self = this;
      self.bankModel = Object.assign({}, bank);
      self.$bvModal.show('add-routing-number-modal');
    },
    checkRoutingConstraint(){
      let self = this;
      if(!self.isNumericString(self.bankModel.routing_no) || self.valid_routing_number || !self.finalcheck()){
            error("Provide a valid routing number");
            return 0;
      }
      return 1;
    },
    saveRoutingNumber(){
      let self = this;
      if(self.checkRoutingConstraint()){
        const payload = {
          bank_id: self.bankModel.id,
          routing_no: self.bankModel.routing_no,
          state: self.bankModel.state
        }
        api
        .saveRoutingNumber(payload)
        .then((response) => {
          success(response.message)
          self.$bvModal.hide('add-routing-number-modal');
        }).catch((error)=>{
          error(error.response.data.message);
        })
      }
    },
    closeAddRoutingNumberModal(){
      let self = this;
      self.$bvModal.hide('add-routing-number-modal');
    },
    reset() {
      var self = this;
      self.searchBankName = '';
      self.frontend_visibility = '';
      self.tan_from_fi = '';
      self.banking_solution = '';
    },
    handleFileUpload() {
      let self = this;
      self.logo_file = self.$refs.logo_file.files[0];
      self.logo_fileName = self.$refs.logo_file.files[0].name;
      self.upload_changed = true;
    },
    handleFileChange(){
      let self = this;
      if(self.$refs.small_logo_file.files[0].size > 5 * 1024 * 1024){
        error("Maximum size 5 mb is allowed");
        return;
      }
      self.small_logo_file = self.$refs.small_logo_file.files[0];
      self.small_logo_fileName = self.$refs.small_logo_file.files[0].name;
      self.small_upload_changed = true;
    },
    convertBlobToBase64(file) {
      const blob = new Blob([file], { type: file.type });
      return new Promise((resolve, reject) => {
        console.log(blob);
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result.replace(/^data:.+;base64,/, ''));
        reader.onerror = error => reject(error);
        reader.readAsDataURL(blob);
      });
    },
    clear(e) {
      var self = this;
      self.logo_fileName = "Choose File";
      self.$refs.logo_file.value = null;
      self.upload_changed = false;
      self.logo_url = null;
      self.bankModel.logo_url = "";
      e.preventDefault();
    },
    smallLogoClear(e) {
      var self = this;
      self.small_logo_fileName = "Choose File";
      self.$refs.small_logo_file = null;
      self.small_logo_url = null;
      self.bankModel.small_logo = "";
      self.bankModel.small_logo_extension = "";
      e.preventDefault();
    },
    handleOk(bvModalEvt) {
      var self = this;
      // Prevent modal from closing
      bvModalEvt.preventDefault();
      // Trigger submit handler
      self.editSaveBank();
    },
    searchFilter(){
        var self = this;
        self.currentPage = 1;
        self.searchBank();
    },
    searchBank(){
      var self = this;
      var request = {
          bank_name: self.searchBankName,
          frontend_visibility: self.frontend_visibility,
          tan_from_fi: self.tan_from_fi,
          banking_solution: self.banking_solution,
          page: self.currentPage,
          per_page: self.perPage,
      };
      self.loading = true;
      api
      .searchBankList(request)
      .then(function (response) {
        if (response.code == 200) {
          self.allBankModel = response.data.data;
          self.totalPage = response.data.total_pages;
          self.totalItems = response.data.total;
          self.currentPage = response.data.current_page;
          self.finicity_active = response.data.finicity_active;
          self.akoya_active = response.data.akoya_active;
          self.mx_active = response.data.mx_active;
          self.loading = false;
        } else {
          error(response.message);
          self.loading = false;
        }
      })
      .catch(function (error) {
        // error(error);
        self.loading = false;
      });
    },
    editBank(bank){
      var self = this;
      self.bankModel = Object.assign({}, bank);
      self.upload_changed = false;
      self.smallLogoClear
      self.$bvModal.show("bank-modal");
      if (bank.logo_url == null || bank.logo_url == "") {
        self.logo_fileName = "Choose File";
      } else {
        self.logo_fileName = bank.logo_url.replace(/^.*[\\\/]/, '');
      }
      self.small_logo_fileName = "Choose File";
    },
    viewAllRouting(bank){
      var self = this;
      self.bankRoutingNumbers = {};
      self.bankModel = Object.assign({}, bank);
      var request = {
        financial_institution_id: bank.id
      };
      self.loading = true;
      api
      .getBankRoutingNumbers(request)
      .then(function (response) {
        if (response.code == 200) {
          self.bankRoutingNumbers = response.data;
          self.rowsRoutingNo = self.bankRoutingNumbers.length;
          self.loading = false;
          self.filter = '';
          self.$bvModal.show("routing-list-modal");
        } else {
          error(response.message);
          self.loading = false;
        }
      })
      .catch(function (error) {
        // error(error);
        self.loading = false;
      });
    },
    editSaveBank(bank){
      var self = this;
      self.loading = true;
      let formData = new FormData();
      formData.append("logo", self.logo_file);
      formData.append("bankName", self.bankModel.bank_name);
      // Exit when the form isn't valid
      this.$validator.validateAll().then((result) => {
        if (result) {
          if (self.bankModel.logo_url === "Choose File") {
            self.bankModel.logo_url = null;
          }
          if (self.upload_changed) {
            self.bankModel.logo_url = self.logo_fileName.valueOf();
            api.importLogo(formData).then((response) => {
              self.bankModel.logo_url = process.env.MIX_AWS_CLOUDFRONT_URL +  response.data.replace(/^.*[\\\/]/, '');
              self.checkBlobData();
            });
          } else {
            self.checkBlobData();
          }
        }
      });
    },
    checkBlobData(){
      let self = this;
      if(self.small_logo_fileName != "Choose File"){
        self.convertBlobToBase64(self.small_logo_file)
        .then((blob) => {
            self.bankModel.small_logo_base64_image = blob
            let filename = self.small_logo_file.name;
            self.bankModel.small_logo_extension = filename.slice(filename.lastIndexOf('.') + 1);
            self.editBankApi(self);
        })
        .catch((err)=>{
          error(err.message);
        })
      }else{
        self.editBankApi(self);
      }
      
    },
    editBankApi(self) {
      if(self.bankModel.is_akoya == 1 && (self.bankModel.akoya_provider_id == '' || self.bankModel.akoya_provider_id == null) ){
        self.loading = false;
        return error("Akoya provider id is missing.");
      }
      if(self.bankModel.is_mx == 1 && (self.bankModel.mx_institution_code == '' || self.bankModel.mx_institution_code == null) ){
        self.loading = false;
        return error("MX institution code is missing.");
      }
      api
        .editBank(self.bankModel)
        .then((response) => {
          if (response.code == 200) {
            self.$bvModal.hide("bank-modal");
            success(response.message);
            self.searchBank();
            self.loading = false;
          } else {
            error(response.message);
            self.loading = false;
          }
        })
        .catch((err) => {
          error(err.response.data.message);
          self.loading = false;
        });
    },
    closeAddBankModal(){
      let self = this;
      self.$bvModal.hide("add-bank-modal");
    },
    check_state(){
      let self = this;
      if(self.bankModel.state.length>2){
        self.bankModel.state = self.bankModel.state.slice(0,-1);
      }
    },

    routing_no_validation(){
      let self = this;
      // to check is any charector is used except comma.
      if(self.bankModel.routing_no != '' && (self.bankModel.routing_no[self.bankModel.routing_no.length-1]<'0' || self.bankModel.routing_no[self.bankModel.routing_no.length-1]>'9') && self.bankModel.routing_no[self.bankModel.routing_no.length-1] != ','){
        self.bankModel.routing_no = self.bankModel.routing_no.slice(0,-1);
        self.error_message = "Provide a valid routing number";
        return;
      }
      // to check if multiple times comma is being used or not one after another.
      if(self.bankModel.routing_no !='' && self.bankModel.routing_no.length>=2 && self.bankModel.routing_no[self.bankModel.routing_no.length-1] == ',' && self.bankModel.routing_no[self.bankModel.routing_no.length-2] == ','){
        self.bankModel.routing_no = self.bankModel.routing_no.slice(0,-1);
        if(self.valid_routing_number == false){
        self.valid_routing_number = true;
        self.error_message = "Provide a valid routing number";
        }
        return;
      }
      // checking for first routing number.
      if(self.bankModel.routing_no !='' && self.bankModel.routing_no.indexOf(',') == -1){
        if(self.bankModel.routing_no.length<9){
          if(self.valid_routing_number == false){
            self.error_message = "Provide a valid routing number";
            self.valid_routing_number = true;
          }
        }else if(self.bankModel.routing_no.length > 9){
          self.bankModel.routing_no = self.bankModel.routing_no.slice(0,-1);
        }else if(self.bankModel.routing_no.length == 9){
          if(self.bankModel.routing_no == "*********"){
            self.bankModel.routing_no = self.bankModel.routing_no.slice(0, -9);
            if(self.valid_routing_number == false){
              self.error_message="Routing number ********* is not allowed";
              self.valid_routing_number = true;
            }
            return;
          }
          self.valid_routing_number = false;
        }
        return;
      }
      if(self.bankModel.routing_no !='' && self.bankModel.routing_no[self.bankModel.routing_no.length-1] == ','){
        if(self.valid_routing_number == false){
          self.error_message = "Provide a valid routing number";
          self.valid_routing_number = true;
        }
        return;
      }
      if(self.bankModel.routing_no !='' && self.bankModel.routing_no.indexOf(',') != -1){
        let routing_number = self.bankModel.routing_no;
        routing_number = routing_number.split(',');
        routing_number = routing_number[routing_number.length-1];
        if(routing_number.length<9){
          if(self.valid_routing_number == false){
            self.error_message = "Provide a valid routing number";
            self.valid_routing_number = true;
          } 
        }else if(routing_number.length > 9){
          self.bankModel.routing_no = self.bankModel.routing_no.slice(0,-1);
          self.valid_routing_number = false;
        }else if(routing_number.length == 9){
          if(routing_number == "*********"){
            self.bankModel.routing_no = self.bankModel.routing_no.slice(0, -9);
            if(self.valid_routing_number == false){
              self.error_message="Routing number ********* is not allowed";
              self.valid_routing_number = true;
            }
            return;
          }
          self.valid_routing_number = false;
        }
        return;
      }
       self.numeric_error_routing_number = false;
       self.valid_routing_number = false;
    },
    isNumericString(str) {
          return /^[0-9,]*$/.test(str); 
      },
    constraintCheck(){
          let self = this;
          if(!self.isNumericString(self.bankModel.routing_no) || self.valid_routing_number || !self.finalcheck()){
                error("Provide a valid routing number");
                return 0;
          }
          // check if bank name is provided or not.
          if(self.bankModel.bank_name == '' || self.bankModel.bank_name == null ){
            error("Please provide bank name.");
            return 0;
          }
          // akoya provider id is requried when enabled.
          if(self.bankModel.is_akoya == 1 && (self.bankModel.akoya_provider_id == '' || self.bankModel.akoya_provider_id == null)){
            error("Please provide akoya provider id.");
            return 0;
          }
          // mx institution code is required when enabled
          if(self.bankModel.is_mx == 1 && (self.bankModel.mx_institution_code == '' ||self.bankModel.mx_institution_code == null )){
            error("Please provide mx institution code.");
            return 0;
          }
          return 1;
    },
    finalcheck(){
      let self = this;
      let routing_numbers = self.bankModel.routing_no;
      routing_numbers = routing_numbers.split(',');
      if(routing_numbers.length == 0){
        return self.bankModel.routing_no.length == 9;
      }else{
        for(let i=0;i<routing_numbers.length;i++){
          if(routing_numbers[i].length != 9){
            return false;
          }
        }
        return true;
      }
    },
    saveBank(){
      let self = this;

      if(!self.constraintCheck()){
        return;
      }
      let formData = new FormData();
      formData.append("logo", self.logo_file);
      formData.append("bankName", self.bankModel.bank_name);

        if (self.logo_fileName === "Choose File") {
            self.bankModel.logo_url = '';
        }
        if (self.logo_fileName != "Choose File" && self.upload_changed == true) {
            self.bankModel.logo_url = self.logo_fileName.valueOf();
            api.importLogo(formData).then((response) => {
              self.bankModel.logo_url = process.env.MIX_AWS_CLOUDFRONT_URL +  response.data.replace(/^.*[\\\/]/, '');
              self.saveBankCall();
            });
        } else {
            self.saveBankCall();
        }
    },
    saveBankCall(){
      let self = this;
      self.loading = true;

      self.bankModel.is_akoya = self.bankModel.is_akoya == false?0:1;
      self.bankModel.is_mx = self.bankModel.is_mx == false?0:1;
      self.bankModel.is_finicity = self.bankModel.is_finicity == false ? 0:1;
      self.bankModel.frontend_visibility = self.bankModel.frontend_visibility == false ? 0:1;
      self.bankModel.tan_from_fi = self.bankModel.tan_from_fi == false ? 0:1;

      if(self.small_logo_fileName != "Choose File"){
        self.convertBlobToBase64(self.small_logo_file)
        .then((blob) => {
            self.bankModel.small_logo = blob
            let filename = self.small_logo_file.name;
            self.bankModel.small_logo_extension = filename.slice(filename.lastIndexOf('.') + 1);
            self.callSaveApi();
        })
        .catch((err)=>{
          error(err.response.data.message);
          self.loading = false;
        })

        return;
      }
      self.callSaveApi();
    },
    callSaveApi(){
      let self = this;
      api
      .addBankDetail(self.bankModel)
      .then((response)=>{
        self.loading=false;
        self.$bvModal.hide("add-bank-modal");
        success(response.message);
      })
      .catch((err)=>{
          self.loading=false;  
          console.log(err.response.data.message);
          error(err.response.data.message);
      })
    },
    showAddModal(){
      let self = this;
      self.resetModal();
      self.selected_add_bank='not_accepted';
      self.resetAddBankModal();
      self.$bvModal.show("add-bank-modal");
    },
    resetAddBankModal(){
      let self = this;
      self.small_logo_fileName = "Choose File";
      self.$refs.small_logo_file = null;
      self.small_logo_url = null;
      self.bankModel.small_logo_url = "";
      self.bankModel = {
        routing_no:null,
        new_routing_no:'',
        bank_name:null,
        state:'',
        is_fed_excluded:'',
        is_finicity:0,
        is_akoya:0,
        is_mx:0,
        frontend_visibility:0,
        tan_from_fi:0,
        akoya_provider_id:'',
        mx_institution_code:'',
        logo_url:'',
        small_logo:'',
        small_logo_extension:'',
      }
    },
    selectOption(option) {
      let self = this;
      self.bankModel.bank_name = option.bank_name;
      self.bankModel.is_akoya = option.is_akoya;
      self.bankModel.is_finicity = option.is_finicity;
      self.bankModel.is_mx = option.is_mx;
      self.bankModel.mx_institution_code = option.mx_institution_code;
      self.bankModel.is_akoya = option.is_akoya;
      self.bankModel.akoya_provider_id = option.akoya_provider_id;
      self.bankModel.small_logo = option.small_logo_base64_image;
      self.bankModel.small_logo_extension = option.small_logo_extension;
      self.logo_fileName = "Choose File";
      if(option.logo_url){
        self.logo_fileName = option.logo_url;
      }
      self.showDropdown = false;
    },
    updateOptions() {
      var self = this;
      if(self.bankModel.bank_name.length <=3 && self.bankModel.bank_name.length>0){
        self.bankNameOptions = [];
        self.showDropdown = false;
        return;
      }
      if(self.bankModel.bank_name.length == 9){
        self.showDropDown = false;
        return;
      }

      const payload = {
        'bank_name':self.bankModel.bank_name
      }
      if(self.bankModel.bank_name.length>3){
        api
        .searchBankName(payload)
        .then((response)=>{
          self.bankNameOptions = response.data;
          if(response.data.length != 0){
              this.showDropdown = true;
          }else{
            this.showDropdown = false;
          }
         
        })
        .catch((error)=>{
          this.showDropdown = false;
          self.loading = false;
        })
      }

    },
  },
  mounted() {
    var self = this;
    document.title = "CanPay - Bank List";
  },
};
</script>
<style scoped>
.dropdown-options {
  top: 100%;
  left: 15px;
  position:absolute;
  z-index: 2400;
  max-height: 150px;
  width: 96%;
  color:#000!important;
  overflow-y:auto;
  background-color: #fff;
  border-top: none;
  list-style-type: none;
  padding: 0;
  margin: 0px;
  border-radius: 10px;
  box-shadow: rgba(0, 0, 0, 0.19) 0px 10px 20px, rgba(0, 0, 0, 0.23) 0px 6px 6px;
}
.dropdown-list {
  padding: 3px;
  font-size:14px;
  font-weight:600;
  cursor: pointer;

}
.font-bolder{
  font-weight:bolder;
}
.dropdown-list:hover {
  background-color: #ADE1F5;
}
.custom-dropdown {
  width:100%;
  position: relative;
  display: inline-block;
}
.red-error{
  color:#f01e2c;
}
.margin-top-routing-class{
  margin-top:60px!important;
}
.para-settings{
  margin-bottom: 0px !important;
  padding: 7px;
}
</style>

