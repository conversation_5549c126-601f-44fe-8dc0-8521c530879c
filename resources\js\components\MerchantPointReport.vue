<template>
<div>
  <div v-if="loading">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Generate Merchant Point Report</h3>
                </div>

                <div class="card-body">
                  <div class="row">
                    <div class="col-md-4">
                      <div class="form-group">
                        <input
                          class="start-date form-control"
                          placeholder="Start Date"
                          id="start-date"
                          onkeydown="return false"
                          autocomplete="off"
                          @input="dateDiff"
                        />
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group">
                        <input
                          class="end-date form-control"
                          placeholder="End Date"
                          id="end-date"
                          onkeydown="return false"
                          autocomplete="off"
                          @input="dateDiff"
                        />
                      </div>
                    </div>
                  </div>
                  <small class="text-red" id="error-code" style="display:none;">The report period should be of maximum 7 days.</small>
                </div>
                <div class="card-footer">
                  <button
                    type="button"
                    class="btn btn-success"
                    id="generateBtn"
                    @click="generateReport(false)"
                  >
                    Generate
                  </button>
                  <button
                    type="button"
                    @click="generateReport(true)"
                    class="btn btn-danger ml-10"
                    id="generateExportBtn"
                  >
                    Generate & Export<i
                      class="fa fa-download ml-10"
                      aria-hidden="true"
                    ></i>
                  </button>
                </div>
              
              </div><div class="mt-4">
                <div v-for="type in pointTypes" :key="type" class="card mt-3">
                  <div class="card-header">
                    <h4>{{ type.replace(/(?:^|\s)\w/g, match => match.toUpperCase()) }}</h4>
                  </div>
                  <div class="card-body">
                    <table class="table table-bordered table-striped">
                      <thead>
                        <tr>
                          <th>Date</th>
                          <th>Points Given</th>
                          <th>Amount Given ($)</th>
                          <th>Points Redeemed</th>
                          <th>Amount Redeemed ($)</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr v-for="(item, date) in reportData[type]" :key="date">
                          <td>{{ item.date }}</td>
                          <td>{{ parseInt(item.cr.points).toLocaleString() }}</td>
                          <td>{{ Number(item.cr.amounts).toFixed(2) }}</td>
                          <td>{{ parseInt(item.dr.points).toLocaleString() }}</td>
                          <td>{{ Number(item.dr.amounts).toFixed(2) }}</td>
                        </tr>
                        <tr class="font-weight-bold">
                          <td>Total</td>
                          <td>{{ parseInt(reportTotals[type].total_cr.points).toLocaleString() }}</td>
                          <td>{{ Number(reportTotals[type].total_cr.amounts).toFixed(2) }}</td>
                          <td>{{ parseInt(reportTotals[type].total_dr.points).toLocaleString() }}</td>
                          <td>{{ Number(reportTotals[type].total_dr.amounts).toFixed(2) }}</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</div>
</template>
<script>
import api from "@/api/reports.js";
import moment from "moment";
import { saveAs } from "file-saver";
import CanPayLoader from "./CustomLoader/CanPayLoader.vue";

export default {
  components: {
    CanPayLoader
  },
  data() {
    return {
      loading: false,
      reports: [],
      pointTypes: [],
      reportData: {},
      reportTotals: {}
    };
  },
  mounted() {
    var self = this;
    $("#start-date,#end-date").datepicker({
      format: "mm/dd/yyyy",
      autoclose: true,
      todayHighlight: true,
    }).on('changeDate', function (ev) {
        self.dateDiff();
    });
    $("#start-date , #end-date").datepicker("setDate", new Date());
  },
  methods: {
    dateDiff(){
      var self = this;
      if ($("#start-date").val() != "") {
        var from_date = moment($("#start-date").val()).format("YYYY-MM-DD");
      } else {
        var from_date = "";
      }
      if ($("#end-date").val() != "") {
        var to_date = moment($("#end-date").val()).format("YYYY-MM-DD");
      } else {
        var to_date = "";
      }
      if(from_date!='' && to_date!=''){
        //calculate the date Difference
        var date1 = new Date(from_date);
        var date2 = new Date(to_date);
        // To calculate the time difference of two dates
        var Difference_In_Time = date2.getTime() - date1.getTime();

        // To calculate the no. of days between two dates
        var Difference_In_Days = Difference_In_Time / (1000 * 3600 * 24);

        if(Difference_In_Days > 7){
          $("#generateBtn,#generateExportBtn").prop('disabled', true);
          $("#error-code").fadeIn(500);
        }else{
          $("#generateBtn,#generateExportBtn").prop('disabled', false);
          $("#error-code").fadeOut(500);
        }
      }
    },
    
    // API call to generate the merchant point report
    generateReport(reportExport) {
     var self = this;
      if (
        moment($("#start-date").val()).format("YYYY-MM-DD") >
        moment($().val()).format("YYYY-MM-DD")
      ) {
        error("Start date cannot be from future.");
        return false;
      }
      if (
        moment($("#end-date").val()).format("YYYY-MM-DD") >
        moment($().val()).format("YYYY-MM-DD")
      ) {
        error("End date cannot be from future.");
        return false;
      }
      var request = {
        from_date: moment($("#start-date").val()).format("YYYY-MM-DD"),
        to_date: moment($("#end-date").val()).format("YYYY-MM-DD"),
      };
      if(request.from_date > request.to_date){
        error("To Date cannot be greater than From date");
        return false;
      }
      self.loading = true;
      api
        .generateMerchantPointReport(request)
        .then(function (response) {
          if (response.code == 200) {
            self.reports = response.data;
            // Process the data for display
            self.pointTypes = response.data.rewardTypes;
            self.reportData = response.data.data;
            self.reportTotals = response.data.totals;
            if (reportExport) {
              self.exportReport();
            } else {
              self.loading = false;
            }
          } else {
            error(response.message);
            self.loading = false;
          }
        })
        .catch(function (error) {
          self.loading = false;
        });
    },
    exportReport() {
      var self = this;
      var request = {
        report: self.reports,
        from_date: moment($("#start-date").val()).format("YYYY-MM-DD"),
        to_date: moment($("#end-date").val()).format("YYYY-MM-DD"),
      };
      api
        .exportMerchantPointReport(request)
        .then(function (response) {
          var FileSaver = require("file-saver");
          var blob = new Blob([response], {
            type: "application/xlsx",
          });
          FileSaver.saveAs(
            blob,
            moment().format("MM/DD/YYYY") + "_merchant_point_report.xlsx"
          );
          self.loading = false;
        })
        .catch(function (error) {
          // error(error);
          self.loading = false;
        });
    }
  }
};
</script>
