const searchEmcommerceCategory = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/ecommerceCategoryList', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const editCategory = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/ecommerceCategoryEdit', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const addCategory = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/ecommerceCategoryAdd', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getAllMerchants = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/allMerchants', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const getAllEcommerceCategory = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/allEcommerceCategory', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const getAllEcommerceIntegrators = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/adminintegratorlist', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const searchMerchantKeys = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/merchantKeyList', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const exportSearchMerchantKeys = (request) => {
    var header = {
        responseType: 'blob',
    };
    return new Promise((res, rej) => {
        axios.post('api/export/exportmerchantKeyList', request, header)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const addMerchantKey = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/merchantKeyAdd', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const merchantKeyStatusChange = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/merchantKeyStatusChange', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
export default {
    searchEmcommerceCategory,
    editCategory,
    addCategory,
    getAllMerchants,
    getAllEcommerceCategory,
    getAllEcommerceIntegrators,
    searchMerchantKeys,
    exportSearchMerchantKeys,
    addMerchantKey,
    merchantKeyStatusChange
};