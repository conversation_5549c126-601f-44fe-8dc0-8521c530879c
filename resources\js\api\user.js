import { loadProgressBar } from 'axios-progress-bar'

const updateUser = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/update/user', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getStores = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/getmerchantstores', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const addCorporateParent = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/addcorporateparent', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const editCorporateParent = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/editcorporateparent', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getUserRoles = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/userrolelist', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const addUser = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/adduser', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const editUser = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/edituser', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const importExcel = (formData) => {
    return new Promise((res, rej) => {
        axios.defaults.headers.common["Content-Type"] = 'multipart/form-data';
        axios.post('api/import/merchantExcel', formData)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const importLogo = (formData) => {
    return new Promise((res, rej) => {
        axios.defaults.headers.common["Content-Type"] = 'multipart/form-data';
        axios.post('api/import/corporateLogo', formData)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const changeUserStatus = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/statuschange', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const changePassword = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/changePassword', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getUserStatus = () => {
    return new Promise((res, rej) => {
        axios.get('api/getUserStatus')
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const editmerchant = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/updateMerchant', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getHelpdeskUserRoles = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/helpdeskuserrolelist', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const editHelpdeskUser = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/edituser', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getmerchantdetails = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/getmerchantdetails', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getconsumerdetails = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/getconsumerdetails', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const fetchMXBankData = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/fetchmxbankdata', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const callConsumerUpdateBankAPI = (request) => {
    var instance = axios.create({
        baseURL: process.env.MIX_API_URL,
    });
    instance.defaults.headers.common["Authorization"] = localStorage.getItem("token");
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.post('/admin/consumerupdatebank', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const getConsumers = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/getAllConsumers', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getConsumersIncUnregistered = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/getConsumersIncUnregistered', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const searchConsumer = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/searchConsumer', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const logoutUser = (request) => {
    return new Promise((res, rej) => {
        axios.get('api/logoutuser', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const searchRegisteredConsumer = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/searchRegisteredConsumer', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const searchConsumerAlgoHistory = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/searchconsumeralgohistory', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const consumerAccountAlgoHistory = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/consumeraccountalgohistory', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getV1Consumers = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/getV1Consumers', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const searchRegisteredMerchants = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/searchRegisteredMerchant', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const searchV1Consumer = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/searchV1Consumer', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getV1consumerdetails = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/getconsumerdetailsByUserID', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const updateConsumerDetails = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/updateConsumerDetails', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const searchConsumersReturnTransactions = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/searchConsumersReturnTransactions', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const releaseConsumer = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/releaseConsumer', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getConsumerReturnTransaction = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/getconsumerreturntransaction', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getconsumercomments = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/getconsumercomments', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const saveconsumercomment = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/saveconsumercomment', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const fetchConsumerAccountBalance = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/fetchconsumeraccountbalance', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const refreshConsumerAccountBalance = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/refreshconsumeraccountbalance', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const bypassMicrobiltError = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/bypassmicrobilterror', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const updateGlobalSettings = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/updatesettingsvalue', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getAllSettings = () => {
    return new Promise((res, rej) => {
        axios.get('api/getallsettingsvalue')
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};


const searchCorporateParents = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/searchcorporateparents', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const getLocationType = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/locationlist', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const searchIntegrators = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/adminintegratorlist', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const editIntegrator = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/editintegrator', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const addIntegrator = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/addintegrator', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const searchAdminConsumers = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/adminuserlist', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const searchHelpdeskConsumers = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/helpdeskuserlist', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const viewConsumerBalance = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/viewconsumerbalance', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const searchConsumers = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/searchconsumers', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const importIntegratorLogo = (formData) => {
    return new Promise((res, rej) => {
        axios.defaults.headers.common["Content-Type"] = 'multipart/form-data';
        axios.post('api/import/integratorLogo', formData)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const unlockConsumer = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/unlockConsumer', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const toggleSupectedConsumer = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/togglesuspectedconsumer', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const exportSuspectedConsumersReport = () => {
    var header = {
        responseType: 'blob',
    };
    return new Promise((res, rej) => {
        axios.get('api/export/suspectedconsumersexport', header)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const searchConsumersWithProbableRetuns = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/searchconsumerswithprobableretuns', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const releaseConsumerFromReturn = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/releaseconsumerfromreturn', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const exportAllConsumersWithProbableRetuns = () => {
    var header = {
        responseType: 'blob',
    };
    return new Promise((res, rej) => {
        axios.get('api/export/allconsumerswithprobablereturn', header)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const searchConsumerAccounts = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/searchconsumeraccounts', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const callAccountOwnerApi = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/callaccountownerapi', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const showAccountOwnerHistory = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/showaccountownerhistory', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const retryAccountOwnerApiCall = (request) => {
    console.log("Retry API Call Request:", request); // Debug the payload
    var instance = axios.create({
        baseURL: process.env.MIX_API_URL,
    });
    instance.defaults.headers.common["Authorization"] = localStorage.getItem("token");
    console.log("Authorization Header:", localStorage.getItem("token")); // Debug token
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.post('/admin/retry-identity-verification', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const toggleAllowMerchantFeesReport = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/toggleallowmerchantfeesreport', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const toggleFreezeSponsorPoints = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/togglefreezesponsorpoints', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const saveBankDelinked = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/savebankdelinked', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getTransactionDetailsForConsumer = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/gettransactiondetailsforconsumer', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getAllStates = () => {
    var instance = axios.create({
        baseURL: process.env.MIX_REWARD_WHEEL_APP_URL,
    });
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.get('/api/admin/getallstates')
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const searchRWRegisteredConsumerAndInvite = (resquest) => {
    var instance = axios.create({
        baseURL: process.env.MIX_REWARD_WHEEL_APP_URL,
    });
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.post('/api/admin/consumersearch', resquest)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const searchRWConsumerStatus = (resquest) => {
    var instance = axios.create({
        baseURL: process.env.MIX_REWARD_WHEEL_APP_URL,
    });
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.post('/api/admin/invitationstatus', resquest)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getFinancialInstitutions = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/getfinancialinstitutions', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const deleteFinancialInstitution = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/removeinstitution', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const checkRestrictedRoutingNumber = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/checkrestrictedroutingnumber', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const updatePurchasePowerRule = (request) => {
    return new Promise((res, rej) => {
        axios.post('/api/updatepurchasepowerrule', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const consumerBankAccountList = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/getconsumerbanklist', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getRegistrationFailure = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/getregistrationfailure', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
}

const callConsumerRegistrationBankAPI = (request, v1_consumer) => {
    var instance = axios.create({
        baseURL: process.env.MIX_API_URL,
    });
    let api_point = "/admin/consumerregister";
    if (v1_consumer)
        api_point = "/admin/onboarding/finalonboarding";
    instance.defaults.headers.common["Authorization"] = localStorage.getItem("token");
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.post(api_point, request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const forceUpdatePurchasePower = (payload) => {
    var instance = axios.create({
        baseURL: process.env.MIX_API_URL,
    });
    let api_point = "/admin/force-update-purchase-power";
    instance.defaults.headers.common["Authorization"] = localStorage.getItem("token");
    console.log(payload);
    return new Promise((res, rej) => {
        instance.post(api_point, payload)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
}

const getIdentifyMemberCallList = (payload) => {
    var instance = axios.create({
        baseURL: process.env.MIX_API_URL,
    });
    let api_point = "admin/get-identify-member-call-list";
    instance.defaults.headers.common["Authorization"] = localStorage.getItem("token");
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.post(api_point, payload)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
}

const retryIdentityVerification = (payload) => {
    var instance = axios.create({
        baseURL: process.env.MIX_API_URL,
    });
    let api_point = "admin/retry-identity-verification";
    instance.defaults.headers.common["Authorization"] = localStorage.getItem("token");
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.post(api_point, payload)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
}

export default {
    updateUser,
    addCorporateParent,
    editCorporateParent,
    getStores,
    importExcel,
    importLogo,
    addUser,
    editUser,
    getUserRoles,
    changeUserStatus,
    changePassword,
    getUserStatus,
    editmerchant,
    getHelpdeskUserRoles,
    editHelpdeskUser,
    getmerchantdetails,
    getconsumerdetails,
    fetchMXBankData,
    callConsumerUpdateBankAPI,
    getConsumers,
    getConsumersIncUnregistered,
    searchConsumer,
    logoutUser,
    searchRegisteredConsumer,
    searchConsumerAlgoHistory,
    consumerAccountAlgoHistory,
    getV1Consumers,
    searchV1Consumer,
    getV1consumerdetails,
    updateConsumerDetails,
    searchRegisteredMerchants,
    searchConsumersReturnTransactions,
    releaseConsumer,
    getConsumerReturnTransaction,
    getconsumercomments,
    saveconsumercomment,
    fetchConsumerAccountBalance,
    updateGlobalSettings,
    getAllSettings,
    searchCorporateParents,
    searchAdminConsumers,
    searchHelpdeskConsumers,
    refreshConsumerAccountBalance,
    bypassMicrobiltError,
    viewConsumerBalance,
    searchConsumers,
    toggleSupectedConsumer,
    exportSuspectedConsumersReport,
    searchConsumersWithProbableRetuns,
    releaseConsumerFromReturn,
    exportAllConsumersWithProbableRetuns,
    unlockConsumer,
    getLocationType,
    editIntegrator,
    addIntegrator,
    searchIntegrators,
    importIntegratorLogo,
    searchConsumerAccounts,
    callAccountOwnerApi,
    showAccountOwnerHistory,
    retryAccountOwnerApiCall,
    toggleAllowMerchantFeesReport,
    toggleFreezeSponsorPoints,
    saveBankDelinked,
    getTransactionDetailsForConsumer,
    getAllStates,
    searchRWRegisteredConsumerAndInvite,
    searchRWConsumerStatus,
    getFinancialInstitutions,
    deleteFinancialInstitution,
    checkRestrictedRoutingNumber,
    updatePurchasePowerRule,
    consumerBankAccountList,
    getRegistrationFailure,
    callConsumerRegistrationBankAPI,
    forceUpdatePurchasePower,
    getIdentifyMemberCallList,
    retryIdentityVerification
};
