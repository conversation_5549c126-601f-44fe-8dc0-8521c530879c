<?php

use App\Http\Factories\EmailExecutor\EmailExecutorFactory;
use App\Models\AccessLevelMaster;
use App\Models\AchMerchantRewardHistory;
use App\Models\AchSixRecordDetail;
use App\Models\AchTransactionDetail;
use App\Models\BankAccountInfo;
use App\Models\BankingSolutionMaster;
use App\Models\BankingSolutionResponse;
use App\Models\BlacklistedAccountNumber;
use App\Models\ConsumerAccountBalance;
use App\Models\ConsumerAddressUpdateHistory;
use App\Models\ConsumerPhoneUpdateHistory;
use App\Models\ConsumerStatusUpdateHistory;
use App\Models\CustomPurchasePowerHistory;
use App\Models\DuplicateUser;
use App\Models\EmailTemplate;
use App\Models\FinancialInstitutionRoutingNumber;
use App\Models\InstitutionListForEmailNotification;
use App\Models\MerchantStores;
use App\Models\MicrobiltRuleMatchHistory;
use App\Models\NotificationLog;
use App\Models\Petition;
use App\Models\PetitionDetail;
use App\Models\RegistrationSession;
use App\Models\ReturnManualPostLog;
use App\Models\Reward;
use App\Models\RoutingNumberListForEmailNotification;
use App\Models\Setting;
use App\Models\SkippedMigrationDataLog;
use App\Models\StatusMaster;
use App\Models\StoreUserMap;
use App\Models\TransactionDetails;
use App\Models\TransactionRewardDetail;
use App\Models\TransactionTypeMaster;
use App\Models\User;
use App\Models\UserBankAccountInfo;
use App\Models\UserCurrentRewardDetail;
use App\Models\UserRewardUsageHistory;
use App\Models\UserRole;
use App\Models\ValidAccountNumber;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Tymon\JWTAuth\Facades\JWTAuth;
use Illuminate\Support\Str;

/**
 * stringBladeCompilationForBody
 * This function is used to dynamically generate the mail template body for all mails on taking input the template design and the dynamic variables
 * @return void
 */
function stringBladeCompilationForBody($template_name, $params)
{
    $template_details = EmailTemplate::getParamValue($template_name); // Fetching the detail of the template whose mail body is to be genearted

    $template = Blade::compileString(htmlspecialchars_decode($template_details->template_body)); // Decoding the special characters from of the html design of the mail template body

    if (isset($params['user_id'])) {
        $user_details = User::where('user_id', $params['user_id'])->select('users.first_name', 'users.middle_name', 'users.last_name')
            ->selectRaw('CASE WHEN users.contact_person_first_name!="" THEN users.contact_person_first_name ELSE users.first_name END as contact_person_first_name,CASE WHEN users.contact_person_last_name!="" THEN users.contact_person_last_name ELSE users.last_name END as contact_person_last_name')->first(); // Fetching user Details
        if (empty($user_details)) {
            $user_details = DuplicateUser::where('email', $params['email'])->select('duplicate_users.first_name', 'duplicate_users.middle_name', 'duplicate_users.last_name')
                ->selectRaw('duplicate_users.first_name  as contact_person_first_name, duplicate_users.last_name  as contact_person_last_name')->first();
        }
    } else if (isset($params['session_id'])) {
        $user_details = RegistrationSession::where('id', $params['id'])->first();
    } else {
        $user_details = new stdClass();
        $user_details->contact_person_first_name = '';
        $user_details->contact_person_last_name = '';
    }

    $transaction_details = new stdClass(); // Defining an empty object in case of non-transactional emails

    if ($template_name == CONSUMER_MONTHLY_TRANSACTION_ACTIVITY) {
        $transaction_details = TransactionDetails::join('terminal_master', 'terminal_master.id', '=', 'transaction_details.terminal_id')->join('merchant_stores', 'terminal_master.merchant_store_id', '=', 'merchant_stores.id')->selectRaw("transaction_details.transaction_number, DATE_FORMAT(local_transaction_time, '%m-%d-%Y %H:%i:%s') as local_transaction_time, transaction_details.amount, transaction_details.tip_amount, merchant_stores.retailer")->whereRaw("YEAR(local_transaction_time) = YEAR(CURRENT_DATE - INTERVAL 1 MONTH)")->whereRaw("MONTH(local_transaction_time) = MONTH(CURRENT_DATE - INTERVAL 1 MONTH)")->where('transaction_details.consumer_id', $params['user_id'])->whereRaw('transaction_details.transaction_ref_no is null')->where('transaction_details.isCanpay', '=', 0)->orderBy('transaction_details.local_transaction_time', 'DESC')->get();
    } else if ($template_name == CONSUMER_TRANSACTION_RETURN || $template_name == UNKNOWN_RETURN_REASON_RECEIVED) {
        $transaction_details = _getConsumerReturnTransactionTemplate($params['transaction_id']);
    } else if ($template_details->return_reason_id != null) {
        $transaction_details = TransactionDetails::join('terminal_master', 'terminal_master.id', '=', 'transaction_details.terminal_id')->join('merchant_stores', 'terminal_master.merchant_store_id', '=', 'merchant_stores.id')->selectRaw("transaction_details.transaction_number, DATE_FORMAT(local_transaction_time, '%m-%d-%Y %H:%i:%s') as local_transaction_time, transaction_details.amount, transaction_details.tip_amount, merchant_stores.retailer")->where(['transaction_details.id' => $params['transaction_id']])->first();
    } else if ($template_name == CORPORATE_PARENT_DAILY_TRANSACTION_ACTIVITY) {
        if (array_key_exists("store_id", $params)) {
            $store_id = $params['store_id'];
            //Transaction Email sent to users of particular store
            $transaction_details = TransactionDetails::join('terminal_master', 'terminal_master.id', '=', 'transaction_details.terminal_id')
                ->join('merchant_stores', 'merchant_stores.id', '=', 'terminal_master.merchant_store_id')
                ->join('registered_merchant_master', 'registered_merchant_master.id', '=', 'merchant_stores.merchant_id')
                ->selectRaw("transaction_details.transaction_number, DATE_FORMAT(transaction_details.local_transaction_time, '%m-%d-%Y %H:%i:%s') as local_transaction_time, transaction_details.amount, transaction_details.tip_amount, merchant_stores.retailer")
                ->whereRaw("local_transaction_date = DATE(NOW() - INTERVAL 1 DAY)")
                ->whereRaw('transaction_details.transaction_ref_no is null')
                ->where('transaction_details.isCanpay', '=', 0)
                ->where('merchant_stores.id', $params['store_id'])
                ->orderBy('transaction_details.local_transaction_time', 'DESC')
                ->get();
        } else {
            $user = User::where('user_id', $params['user_id'])->first();
            $user_id = $params['user_id'];

            if ($user->receive_daily_transaction_email == '1') {
                //Transaction details for Store manager/ Accountant the stores they are assigned to
                $transaction_details = TransactionDetails::join('terminal_master', 'terminal_master.id', '=', 'transaction_details.terminal_id')
                    ->join('merchant_stores', 'merchant_stores.id', '=', 'terminal_master.merchant_store_id')
                    ->join('store_user_map', function ($join) use ($user_id) {
                        $join->on("store_user_map.store_id", "=", "merchant_stores.id");
                        $join->on("store_user_map.user_id", "=", DB::raw("'$user_id'"));
                    })
                    ->join('registered_merchant_master', 'registered_merchant_master.id', '=', 'merchant_stores.merchant_id')
                    ->selectRaw("transaction_details.transaction_number, DATE_FORMAT(transaction_details.local_transaction_time, '%m-%d-%Y %H:%i:%s') as local_transaction_time, transaction_details.amount, transaction_details.tip_amount, merchant_stores.retailer")
                    ->whereRaw("local_transaction_date = DATE(NOW() - INTERVAL 1 DAY)")
                    ->whereRaw('transaction_details.transaction_ref_no is null')
                    ->where('transaction_details.isCanpay', '=', 0)
                    ->where('store_user_map.user_id', $params['user_id'])
                    ->orderBy('transaction_details.local_transaction_time', 'DESC')
                    ->get();
            } else {
                //Transaction detail for all Store
                $transaction_details = TransactionDetails::join('terminal_master', 'terminal_master.id', '=', 'transaction_details.terminal_id')
                    ->join('merchant_stores', 'merchant_stores.id', '=', 'terminal_master.merchant_store_id')
                    ->join('store_user_map', 'store_user_map.store_id', '=', 'merchant_stores.id')
                    ->join('registered_merchant_master', 'registered_merchant_master.id', '=', 'merchant_stores.merchant_id')
                    ->selectRaw("transaction_details.transaction_number, DATE_FORMAT(transaction_details.local_transaction_time, '%m-%d-%Y %H:%i:%s') as local_transaction_time, transaction_details.amount, transaction_details.tip_amount, merchant_stores.retailer")
                    ->whereRaw("local_transaction_date = DATE(NOW() - INTERVAL 1 DAY)")
                    ->whereRaw('transaction_details.transaction_ref_no is null')
                    ->where('transaction_details.isCanpay', '=', 0)
                    ->where('store_user_map.user_id', $params['user_id'])
                    ->orderBy('transaction_details.local_transaction_time', 'DESC')
                    ->get();
            }
        }
    }

    //To create a $data array for different dynamic variables in mail templates
    $data = array(
        'user_details' => $user_details,
        'password' => isset($params['password']) ? $params['password'] : null,
        'otp' => isset($params['otp']) ? $params['otp'] : null,
        'transaction_details' => $transaction_details,
        'month_year' => isset($params['month_year']) ? $params['month_year'] : null,
        'date' => isset($params['date']) ? $params['date'] : null,
        'consumer_app_url' => config('app.consumer_app_url'),
        'consumer_app_url_continue_registration' => config('app.consumer_app_url') . 'registration',
        'return_reason' => isset($params['return_reason_description']) ? $params['return_reason_description'] : null,
        'comment' => isset($params['comment']) ? $params['comment'] : null,
        'return_code' => isset($params['return_code']) ? $params['return_code'] : null,
        'notification_body' => isset($params['notification_body']) ? $params['notification_body'] : null,
        'notification_heading' => isset($params['notification_heading']) ? $params['notification_heading'] : null,
        'business_name' => isset($params['business_name']) ? $params['business_name'] : null,
        'store_contact_first_name' => isset($params['store_contact_first_name']) ? $params['store_contact_first_name'] : null,
        'petition_title' => isset($params['petition_title']) ? $params['petition_title'] : null,
        'first_name' => isset($params['first_name']) ? $params['first_name'] : null,
        'rejection_reason' => isset($params['rejection_reason']) ? $params['rejection_reason'] : null,
    );
    $renderedTemplate = Blade::render($template, $data);

    return $renderedTemplate;
}

function _getConsumerReturnTransactionTemplate($transaction_id)
{
    return $transaction_details = TransactionDetails::join('terminal_master', 'terminal_master.id', '=', 'transaction_details.terminal_id')->join('merchant_stores', 'terminal_master.merchant_store_id', '=', 'merchant_stores.id')->join('users', 'users.user_id', '=', 'transaction_details.consumer_id')->selectRaw("transaction_details.transaction_number, DATE_FORMAT(local_transaction_time, '%m-%d-%Y %H:%i:%s') as local_transaction_time, transaction_details.amount, transaction_details.tip_amount, merchant_stores.retailer, users.email")->where(['transaction_details.id' => $transaction_id])->first();
}

/**
 * stringBladeCompilationForSubject
 * This function is used to dynamically generate the mail template subject for all mails on taking input the template design and the dynamic variables
 * @return void
 */
function stringBladeCompilationForSubject($template_name, $params)
{
    $template_details = EmailTemplate::getParamValue($template_name); // Fetching the detail of the template whose mail body is to be genearted

    $template = Blade::compileString(htmlspecialchars_decode($template_details->template_subject)); // Decoding the special characters from of the html design of the mail template body

    if (isset($params['user_id'])) {
        if (isset($params['session_id'])) {
            $user_details = RegistrationSession::where('id', $params['id'])->first(); // Fetching user Details
        } else {
            $user_details = User::where('user_id', $params['user_id'])->first(); // Fetching user Details
        }
    } else {
        $user_details = new stdClass();
        $user_details->contact_person_first_name = '';
        $user_details->contact_person_last_name = '';
    }

    //To create a $data array for different dynamic variables in mail templates
    $data = array(
        'user_details' => $user_details,
        'petition_title' => isset($params['petition_title']) ? $params['petition_title'] : null,
    );
    $renderedTemplate = Blade::render($template, $data);

    return $renderedTemplate;
}

function stringBladeCompilationForBodyTest($template_name, $params)
{
    $template_details = EmailTemplate::getParamValue($template_name); // Fetching the detail of the template whose mail body is to be genearted

    $template = Blade::compileString(htmlspecialchars_decode($template_details->template_body));

    $user_details = new \stdClass();
    $user_details->first_name = "Test";
    $user_details->middle_name = "";
    $user_details->last_name = "User";
    $user_details->contact_person_first_name = "Test";
    $user_details->contact_person_last_name = "User";
    $user_details->phone = "**********";
    $user_details->email = "<EMAIL>";
    $user_details->street_address = "2345 Peachwood Circle Northeast";
    $user_details->city = "Atlanta";
    $user_details->state = "NC";
    $user_details->bank_link_type = 1;
    $user_details->zipcode = "30345";

    if (!in_array($template_name, [CONSUMER_MONTHLY_TRANSACTION_ACTIVITY, CORPORATE_PARENT_DAILY_TRANSACTION_ACTIVITY])) {
        $transaction_details = new \stdClass();
        $transaction_details->retailer = "Test Store";
        $transaction_details->email = "<EMAIL>";
        $transaction_details->transaction_number = "ELI7EBITI7AZ";
        $transaction_details->local_transaction_time = Carbon::now()->format('m-d-Y H:i:s');
        $transaction_details->amount = 10.00;
        $transaction_details->tip_amount = 0.50;
        $transaction_details->reward_point_used = 2000;
        $transaction_details->reward_amount_used = 2.5;
        $transaction_details->consumer_bank_posting_amount = 8;
        $transaction_details->transaction_status = 'Pending';
    } else {
        $transaction_details = [];
        $transaction_details_obj = new \stdClass();
        $transaction_details_obj->retailer = "Test Store";
        $transaction_details_obj->transaction_number = "ELI7EBITI7AZ";
        $transaction_details_obj->local_transaction_time = Carbon::now()->format('m-d-Y H:i:s');
        $transaction_details_obj->amount = 10.00;
        $transaction_details_obj->tip_amount = 0.50;
        array_push($transaction_details, $transaction_details_obj);
        $transaction_details_obj2 = new \stdClass();
        $transaction_details_obj2->retailer = "New Store";
        $transaction_details_obj2->transaction_number = "KAQAXE8I6A0E";
        $transaction_details_obj2->local_transaction_time = Carbon::now()->format('m-d-Y H:i:s');
        $transaction_details_obj2->amount = 12.00;
        $transaction_details_obj2->tip_amount = 0.75;
        array_push($transaction_details, $transaction_details_obj2);
    }

    $notification_arr[] = [
        'name' => 'Test',
        'phone' => '**********',
        'email' => '<EMAIL>',
        'date_of_birth' => date('m-d-Y', strtotime("-20 years")),
        'address' => '204, USA',
        'balance' => '300',
        'account_number' => '*********',
        'routing_number' => '*********',
        'used_by_other_user' => date('m-d-Y', strtotime("+1 year")),
    ];
    if (in_array($template_name, [SUSPECTED_DIRECT_LINK_CONSUMER_DETECTED])) {
        $notification_body = getNotificationBody($notification_arr, true);
    } else {
        $notification_body = getNotificationBody($notification_arr, false);
    }
    $store_name = 'Test Store';
    $user_dob = date('m-d-Y', strtotime("-20 years"));
    $account_no = '*********';
    $routing_no = '*********';
    $enrollment_date = date('m-d-Y');
    $token = generateUUID();

    $spin_report_email_body = '';
    $spin_report_email_body .= '<tr style="background-color:#eee;">';
    $spin_report_email_body .= '<td style="padding: 8px; text-align:left;">Test Spin</td>';
    $spin_report_email_body .= '<td style="padding: 8px; text-align:left;">Test segment</td>';
    $spin_report_email_body .= '<td style="padding: 8px; text-align:left;">30</td>';
    $spin_report_email_body .= '<td style="padding: 8px; text-align:right;">2</td>';
    $spin_report_email_body .= '</tr>';

    $duplicate_transaction_email_body = '<tr style="background-color:#eee;">';
    $duplicate_transaction_email_body .= '<td style="padding: 8px; text-align:left;">Test</td>';
    $duplicate_transaction_email_body .= '<td style="padding: 8px; text-align:left;"><EMAIL></td>';
    $duplicate_transaction_email_body .= '<td style="padding: 8px; text-align:left;">**********</td>';
    $duplicate_transaction_email_body .= '<td style="padding: 8px; text-align:right;">2</td>';
    $duplicate_transaction_email_body .= '</tr>';

    $onboarded_consumer_details_for_email = '<tr style="background-color:#eee;">';
    $onboarded_consumer_details_for_email .= '<td style="padding: 8px;">Test</td>';
    $onboarded_consumer_details_for_email .= '<td style="padding: 8px;">' . $user_dob . '</td>';
    $onboarded_consumer_details_for_email .= '<td style="padding: 8px;">204, USA</td>';
    $onboarded_consumer_details_for_email .= '<td style="padding: 8px;"><EMAIL></td>';
    $onboarded_consumer_details_for_email .= '<td style="padding: 8px;">**********</td>';
    $onboarded_consumer_details_for_email .= '<td style="padding: 8px;">1111</td>';
    $onboarded_consumer_details_for_email .= '<td style="padding: 8px;">NO</td>';
    $onboarded_consumer_details_for_email .= '<td style="padding: 8px;">' . $enrollment_date . '</td>';
    $onboarded_consumer_details_for_email .= '<td style="padding: 8px;">Active</td>';
    $onboarded_consumer_details_for_email .= '</tr>';

    $matched_consumer_details_for_email = '<tr style="background-color:#eee;">';
    $matched_consumer_details_for_email .= '<td style="padding: 8px;">CP</td>';
    $matched_consumer_details_for_email .= '<td style="padding: 8px;">Test</td>';
    $matched_consumer_details_for_email .= '<td style="padding: 8px;">' . $user_dob . '</td>';
    $matched_consumer_details_for_email .= '<td style="padding: 8px;">204, USA</td>';
    $matched_consumer_details_for_email .= '<td style="padding: 8px;"><EMAIL></td>';
    $matched_consumer_details_for_email .= '<td style="padding: 8px;">7900000078</td>';
    $matched_consumer_details_for_email .= '<td style="padding: 8px;">1111</td>';
    $matched_consumer_details_for_email .= '<td style="padding: 8px;">NO</td>';
    $matched_consumer_details_for_email .= '<td style="padding: 8px;">1</td>';
    $matched_consumer_details_for_email .= '<td style="padding: 8px;">0</td>';
    $matched_consumer_details_for_email .= '<td style="padding: 8px;">0</td>';
    $matched_consumer_details_for_email .= '<td style="padding: 8px;">' . date('m-d-Y', strtotime("-20 days")) . '</td>';
    $matched_consumer_details_for_email .= '<td style="padding: 8px;">Active</td>';
    $matched_consumer_details_for_email .= '</tr>';

    //To create a $data array for different dynamic variables in mail templates
    $data = array(
        'user_details' => $user_details,
        'transaction_details' => $transaction_details,
        'month_year' => date('F, Y', strtotime(date('Y-m') . " -1 month")),
        'date' => date('m-d-Y', strtotime("-1 days")),
        'password' => substr(md5(microtime()), rand(0, 26), 7),
        'verification_code' => rand(100000, 999999),
        'login_pin' => rand(100000, 999999),
        'otp' => rand(100000, 999999),
        'consumer_app_url' => config('app.consumer_app_url'),
        'consumer_app_url_continue_registration' => config('app.consumer_app_url') . 'registration',
        'updated_columns' => 'First name, Last Name',
        'notification_body' => $notification_body,
        'store_name' => $store_name,
        'user_dob' => $user_dob,
        'account_no' => $account_no,
        'routing_no' => $routing_no,
        'enrollment_date' => $enrollment_date,
        'lifetime_no_of_transactions' => 100,
        'reward_wheel_name' => 'Test Reward Wheel',
        'spin_count' => 100,
        'spin_report_email_body' => $spin_report_email_body,
        'cron_job_name' => 'Test Post Transaction',
        'exception_message' => 'Test Exception ',
        'failed_pin_count' => 5,
        'threshold_value' => 4,
        'interval' => 30,
        'duplicate_transaction_email_body' => $duplicate_transaction_email_body,
        'duplicate_transactions_count' => 5,
        'totalCredits' => '$72.45',
        'total_consumer_transaction_count' => 13,
        'total_consumer_transaction_value' => '$58.82',
        'total_reward_amount' => '$9.15',
        'total_consumer_and_reward_amount' => '$67.97',
        'total_merchant_transaction' => '$67.97',
        'total_canpay_fee_merchant_debit' => '$4.48',
        'total_canpay_fee' => '$4.48',
        'total_represented_value' => '$0.00',
        'new_transaction_value' => '$0.00',
        'representment_value' => '$0.00',
        'canpay_offset' => '$0.00',
        'reward_points_earned' => 0.00,
        'reward_amount_earned' => '$0.00',
        'onboarded_consumer_details_for_email' => $onboarded_consumer_details_for_email,
        'matched_consumer_details_for_email' => $matched_consumer_details_for_email,
        'notification_body' => $notification_body,
        'notification_heading' => 'We have detected a consumer with 2585856 Institution having balance greater than $100 The details are given below.',
        'return_reason' => 'Test Return.',
        'transaction_modification_link' => 'https://app.canpaydebit.com/updatedtransaction',
        'parent_transaction_details' => $transaction_details,
        'previous_transaction_details' => $transaction_details,
        'encoded_phone' => base64_encode('**********'),
        'encoded_email' => base64_encode('<EMAIL>'),
        'encoded_verification_code' => base64_encode('12345'),
        'role_name' => 'Test Role',
        'stores' => 'Test Store 1, Test Store 2, Test Store 3',
        'match_percentage' => 79,
        'return_code' => 'R01',
        'comment' => 'Please Upload Valid Document',
        'email' => '<EMAIL>',
        'session_id' => '12345',
        'token' => base64_encode($token),
        'from_date' => date('m-d-Y', strtotime("-3 days")),
        'to_date' => date('m-d-Y', strtotime("-1 days")),
        'transaction_status' => 'Pending',
        'earlier_transaction_amount' => '400',
        'total_amount' => '400',
        'earlier_transaction_store' => 'Test Store 1',
        'total_amount' => 100,
        'total_successful_transactions' => 5,
        'successful_transactions' => 3,
        'successful_return_transactions' => 2,
        'credit_count' => 5,
        'total_sponsor_amount' => '$89.00',
        'total_debit' => '$1999.00',
        'debit_count' => 122,
        'sponsor_debits' => [
            (object) [
                'name' => 'CannaPlan',
                'total_sponsor_debit' => '$300.00',
            ],
            (object) [
                'name' => 'PepsiCo',
                'total_sponsor_debit' => '$490.00',
            ],
        ],
        'total_sponsor_credit' => '$790',
        'store_name' => 'Test Store',
        'transaction_date' => date('m-d-Y'),
        'transaction_amount' => 100.25,
        'cashback_points_earned' => 15200,
        'cashback_amount_earned' => 1.52,
    );

    $renderedTemplate = Blade::render($template, $data);

    return $renderedTemplate;
}

function stringBladeCompilationForSubjectTest($template_name, $params)
{
    $template_details = EmailTemplate::getParamValue($template_name);

    $template = Blade::compileString(htmlspecialchars_decode($template_details->template_subject));

    $user_details = new \stdClass();
    $user_details->first_name = "Test";
    $user_details->middle_name = "";
    $user_details->last_name = "User";

    $data = [
        'reward_wheel_name' => 'Test Reward Wheel',
        'user_details' => $user_details,
        'transaction_date' => Carbon::now()->format('m-d-Y'),
    ];

    $renderedTemplate = Blade::render($template, $data);

    return $renderedTemplate;
}

/**
 * returnUserStatusId
 * This function will return the User's status ID based on the excel sheet's status
 * @param  mixed $status
 * @return void
 */
function returnUserStatusId($status)
{
    if ($status == 'Active' || $status == 'Active with Waiting Pin') {
        $status_details = StatusMaster::where('code', 701)->first();
    } else if ($status == 'Expired') {
        $status_details = StatusMaster::where('code', 702)->first();
    } else if ($status == 'Reject') {
        $status_details = StatusMaster::where('code', 704)->first();
    } else if ($status == 'Suspended') {
        $status_details = StatusMaster::where('code', 705)->first();
    } else {
        // Check Status Exists in database
        $checkStatusExists = StatusMaster::where('status', $status)->first();
        if (!empty($checkStatusExists)) {
            return $checkStatusExists->id;
        } else {
            // Get last code starting with 7
            $getLastStatusCode = StatusMaster::whereRaw("code Like '7%'")->orderBy('code', 'desc')->first();
            $status_master = new StatusMaster();
            $status_master->status = $status;
            $status_master->code = $getLastStatusCode->code + 1;
            $status_master->save();
            return $status_master->id;
        }
    }
    return $status_details->id;
}

/**
 * getStoretype
 * This function will return the Store type based on the Store ID
 * @param  mixed $storecode
 * @return void
 */
function getStoretype($storecode)
{
    $store_type = substr($storecode, -1);
    $transaction_type = TransactionTypeMaster::where('label', $store_type)->first();
    if (!empty($transaction_type)) {
        return $transaction_type->id;
    } else {
        return null;
    }
}

/**
 * getTransactionTypeId
 * This function will return the Store type based on the Store ID
 * @param  mixed $label
 * @return void
 */
function getTransactionTypeId($param)
{
    $label = $param != '' ? $param : 'R';
    $transaction_type = TransactionTypeMaster::where('label', $label)->first();
    if (!empty($transaction_type)) {
        return $transaction_type->id;
    } else {
        return null;
    }
}

/**
 * insertSkippedDataLog
 * This function is used to insert the skipped data during Data migration
 * @param  mixed $import_type
 * @param  mixed $primary_identifier_name
 * @param  mixed $primary_identifier_value
 * @param  mixed $message
 * @param  mixed $data
 * @param  mixed $sheet_name
 * @return void
 */
function insertSkippedDataLog($import_type, $primary_identifier_name, $primary_identifier_value, $message, $data, $sheet_name)
{
    $skipped_data_log = new SkippedMigrationDataLog();
    $skipped_data_log->import_type = $import_type;
    $skipped_data_log->primary_identifier_name = $primary_identifier_name;
    $skipped_data_log->primary_identifier_value = $primary_identifier_value;
    $skipped_data_log->message = $message;
    $skipped_data_log->row_data = $data;
    $skipped_data_log->sheet_name = $sheet_name;
    $skipped_data_log->imported_by = Auth::user()->user_id;
    $skipped_data_log->save();

    return true;
}
/**
 * getStatus
 * This function will return the status_id based on the parameter
 * @param  mixed $code
 * @return void
 */
function getStatus($code)
{
    $status = StatusMaster::where('code', $code)->first();
    return $status->id;
}

/**
 * getStatuses
 * This function will return the status_id based on the array of statuses
 * @param  array $codes
 * @return array
 */
function getStatuses(array $codes)
{
    $statuses = StatusMaster::whereIn('code', $codes)->get();
    return $statuses->pluck('id', 'code')->toArray();
}

/**
 * insertManualPostedReturnLog
 * This function is used to insert the Manually posted returns
 * @param  mixed $primary_identifier_name
 * @param  mixed $primary_identifier_value
 * @param  mixed $message
 * @param  mixed $data
 * @param  mixed $return_posted
 * @return void
 */
function insertManualPostedReturnLog($primary_identifier_name, $primary_identifier_value, $message, $data, $return_posted, $batch_id)
{
    $skipped_data_log = new ReturnManualPostLog();
    $skipped_data_log->primary_identifier_name = $primary_identifier_name;
    $skipped_data_log->primary_identifier_value = $primary_identifier_value;
    $skipped_data_log->message = $message;
    $skipped_data_log->row_data = $data;
    $skipped_data_log->return_posted = $return_posted;
    $skipped_data_log->imported_by = Auth::user()->user_id;
    $skipped_data_log->batch_id = $batch_id;
    $skipped_data_log->save();

    return true;
}

/**
 * getRole
 * This function will return the role_id based on the parameter
 * @param  mixed $name
 * @return void
 */
function getRole($name)
{
    $role = UserRole::where('role_name', $name)->first();
    return $role->role_id;
}

/**
 * getPendingSuccessReturnedStatus
 * This function will return the Success, Pending and Returned status ids from one query
 * @return void
 */
function getPendingSuccessReturnedStatus()
{
    $status_ids = DB::select("SELECT GROUP_CONCAT(CONCAT('''', id, '''' )) ids FROM status_master WHERE `code` IN (202, 200, 511)");
    return $status_ids[0]->ids;
}

/**
 * getActiveBankAccountNo
 * This function will return the Account No. of the Active Bank Account
 * @param  mixed $user_id
 * @return void
 */
function getActiveBankAccountNo($user_id)
{
    $activeBankAccount = UserBankAccountInfo::join('status_master', 'status_master.id', '=', 'user_bank_account_info.status')->where(['status_master.code' => BANK_ACTIVE, 'user_bank_account_info.user_id' => $user_id])->first();

    return !empty($activeBankAccount) ? $activeBankAccount->account_no : "";
}

/**
 * addAccountToBlacklist
 * This function will add the bank details in balclisted table
 * @param  mixed $bank_details
 * @return void
 */
function addAccountToBlacklist($bank_details)
{
    // Check if the bank details already exists in the table
    $checkAccountExists = BlacklistedAccountNumber::where(['account_no' => $bank_details->account_no, 'routing_no' => $bank_details->routing_no, 'is_delete' => 0])->count();
    if ($checkAccountExists == 0) { // If not add the bank details in the blacklist table
        $balcklist_account = new BlacklistedAccountNumber();
        $balcklist_account->account_no = $bank_details->account_no;
        $balcklist_account->routing_no = $bank_details->routing_no;
        $balcklist_account->is_delete = 0;
        $balcklist_account->save();
    }
    return true;
}

/**
 * addAccountToWhitelist
 * This function will add the consumer's current active bank account to the whitelist
 * @param  mixed $user_id
 * @return void
 */
function addAccountToWhitelist($user_id)
{
    $bank_active = getStatus(BANK_ACTIVE);
    // Get the current active bank account of this consumer
    $bank_account = BankAccountInfo::where(['user_id' => $user_id, 'status' => $bank_active])->first();
    if (!empty($bank_account)) {
        // Add this account to whitelist for this consumer
        $valid_account = new ValidAccountNumber();
        $valid_account->account_no = $bank_account->account_no;
        $valid_account->consumer_id = $bank_account->user_id;
        $valid_account->created_by = Auth::user()->user_id;
        $valid_account->save();
    }
    return true;
}

/**
 * savePhoneAddressHistory
 * This function will save th ephone number and address for a specific consumer for future use
 * @param  mixed $user
 * @return void
 */
function savePhoneAddressHistory($user, $source, $user_id = null)
{
    if (strcasecmp($source, ADDRESS_UPDATED_BY_ADMIN) != 0 && strcasecmp($source, ADDRESS_UPDATED_BY_CONSUMER) != 0) {
        // Adding data to phone number history table
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") Adding phone number to history table for User ID: " . $user_id);
        $phone_history = new ConsumerPhoneUpdateHistory();
        $phone_history->consumer_id = $user_id;
        $phone_history->phone = $user['phone'];
        $phone_history->source = $source;
        $phone_history->save();
    }
    if (strcasecmp($source, PHONE_NUMBER_UPDATE) != 0) {
        // Adding data to address history table
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") Adding address details to history table for User ID: " . $user_id);
        $address_history = new ConsumerAddressUpdateHistory();
        $address_history->consumer_id = $user_id;
        $address_history->street_address = $user['street_address'];
        $address_history->apt_number = $user['apt_number'];
        $address_history->city = $user['city'];
        $address_history->state = $user['state'];
        $address_history->zipcode = $user['zipcode'];
        $address_history->source = $source;
        $address_history->updated_by = isset(Auth::user()->user_id) ? Auth::user()->user_id : null;
        $address_history->save();
    }
    return true;
}

/**
 * checkFifthThirdUser
 * This function will check if the conusmer is a Fifth Third User or not. It will check if the bank routing number resides on the routing_number_list_for_email_notifications table. If found then it will send an email to Admin
 * @param  mixed $response
 * @param  mixed $type
 * @return void
 */
function checkForFifthThirdUser($response, $type, $send_mail, $user_id)
{
    $user = User::where('user_id', $user_id)->first(); // Fetch the consumer details
    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Checking started for Fifth Tird User for Consumer ID: " . $user->user_id);
    // Check if the consuemr resides in IL or IN state
    if ($user->state === ILLINIOS || $user->state === INDIANA || ($user->state === PENNSYLVANIA && $send_mail == 1) || ($user->state === DELAWARE && $send_mail == 1)) {
        $notification_arr = [];
        // Fetch all the routing numbers that need to be notified if found
        $all_routing_numbers_to_be_notified = RoutingNumberListForEmailNotification::selectRaw("GROUP_CONCAT(DISTINCT(routing_no)  SEPARATOR',') as routing_nos")->where('state', $user->state)->first();
        $routing_array = explode(',', $all_routing_numbers_to_be_notified->routing_nos); // Make the routing numbers as an indexed array so that we can use in_array to check
        // Get all he blacklisted account numbers to check if the new account number is one of them
        $blacklisted_account_numbers = BlacklistedAccountNumber::selectRaw("GROUP_CONCAT(DISTINCT(account_no)  SEPARATOR',') as account_nos")->where('is_delete', 0)->first();
        $account_array = explode(',', $blacklisted_account_numbers->account_nos); // Make the routing numbers as an indexed array so that we can use in_array to check

        // If the type is 1 then it is a direct link account. There should be a loop to check for fifth third user
        if ($type == 1) {
            foreach ($response as $res) {
                if (in_array($res['routingNumber'], $routing_array)) {
                    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Routing Number " . $res['routingNumber'] . " matched with the notification table data. Hence proceed to notify.");
                    $res['blacklisted'] = in_array($res['realAccountNumber'], $account_array) ? " (Blacklisted)" : "";
                    // Check if the bank details is being used by any other consumer
                    $checkUsedByOtherConsumer = UserBankAccountInfo::selectRaw("DISTINCT user_id, account_no, routing_no")->where(['account_no' => $res['realAccountNumber'], 'routing_no' => $res['routingNumber']])->where('user_id', '!=', $user->user_id)->get();
                    $res['used_by_other_consumer'] = count($checkUsedByOtherConsumer);
                    $data = getNotificationData($user, $res, $type); // Prepare the notification data
                    array_push($notification_arr, $data);
                }
            }
        } else { // This is a manual bank linked account
            if (in_array($response['routingNumber'], $routing_array)) {
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Routing Number " . $response['routingNumber'] . " matched with the notification table data. Hence proceed to notify.");
                $response['blacklisted'] = in_array($response['accountNumber'], $account_array) ? " (Blacklisted)" : "";
                // Check if the bank details is being used by any other consumer
                $checkUsedByOtherConsumer = UserBankAccountInfo::where(['account_no' => $response['accountNumber'], 'routing_no' => $response['routingNumber']])->count();
                $response['used_by_other_consumer'] = 0; // It will always be 0 as same bank account cannot be used in manual bank link
                $data = getNotificationData($user, $response, $type); // Prepare the notification data
                array_push($notification_arr, $data);
            }
        }

        if (!empty($notification_arr)) {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Sending Email to admin as the consumer is a Fifth Tird User with ID: " . $user->user_id);
            // Prepare the email loop section as lumen doesn't support the @foreach tag inside baldecomplier
            $notification_body = getNotificationBody($notification_arr);
            // Sending Mail Notification
            $email_params = [
                'user_id' => $user->user_id,
                'notification_body' => $notification_body,
            ];
            $emailexecutor = new EmailExecutorFactory();
            $emailexecutor->emailForFifthThirdUserDetected($email_params);
        }
    }
    return true;
}

/**
 * getNotificationData
 * Prepare the notification data based on the params
 * @param  mixed $user
 * @param  mixed $response
 * @return void
 */
function getNotificationData($user, $response, $type)
{
    return [
        'name' => $user->first_name . " " . $user->middle_name . " " . $user->last_name,
        'phone' => $user->phone,
        'email' => $user->email,
        'date_of_birth' => $user->date_of_birth,
        'address' => $user->street_address . ", " . $user->city . ", " . $user->state . ", " . $user->zipcode,
        'balance' => $type == 1 ? $response['balance'] : '',
        'routing_number' => $response['routingNumber'],
        'account_number' => $type == 1 ? 'x' . substr($response['realAccountNumber'], -4) . '' . $response['blacklisted'] : 'x' . substr($response['accountNumber'], -4) . '' . $response['blacklisted'],
        'used_by_other_user' => $response['used_by_other_consumer'],
    ];
}

/**
 * getNotificationBody
 * This fucntion will create the email notification body. We are writing this code here as lumen doesn't support@foreach tag inside the bladecompiler. These are not recommended for dynamic email body if the template doesn't conatins @foreach or @include
 * @param  mixed $notification_arr, $is_institution_fraud = false
 * @return void
 */
function getNotificationBody($notification_arr, $is_institution_fraud = false)
{
    $html = '';
    if (!empty($notification_arr)) {
        foreach ($notification_arr as $notification) {
            $html .= '<tr style="background-color:#eee;">';
            $html .= '<td style="padding: 8px;">' . $notification['name'] . '</td>';
            $html .= '<td style="padding: 8px;">' . $notification['phone'] . '</td>';
            $html .= '<td style="padding: 8px;">' . $notification['email'] . '</td>';
            $html .= '<td style="padding: 8px;">' . $notification['date_of_birth'] . '</td>';
            $html .= '<td style="padding: 8px;">' . $notification['address'] . '</td>';
            $html .= '<td style="padding: 8px;">' . $notification['balance'] . '</td>';
            $html .= '<td style="padding: 8px;">' . $notification['account_number'] . '</td>';
            $html .= '<td style="padding: 8px;">' . $notification['routing_number'] . '</td>';
            if (!$is_institution_fraud) {
                $html .= '<td style="padding: 8px; text-align:center">' . $notification['used_by_other_user'] . '</td>';
            }
            $html .= '</tr>';
        }
    }

    return $html;
}

/**
 * saveStatusHistory
 * This function will save the status for a specific consumer for future use
 * @param  mixed $user
 * @return void
 */
function saveStatusHistory($source, $reason, $status, $user_id)
{
    // Adding data to status history table
    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") Adding status to history table for User ID: " . $user_id);
    $status_history = new ConsumerStatusUpdateHistory();
    $status_history->consumer_id = $user_id;
    $status_history->status = $status;
    $status_history->source = $source;
    $status_history->reason = $reason;
    $status_history->updated_by = isset(Auth::user()->user_id) ? Auth::user()->user_id : null;
    $status_history->save();
    return true;
}

/* insertNotificationData
 * This function will store the notification details in table
 * @param  mixed $notification_data
 * @return void
 */
function insertNotificationData($notification_data)
{
    $notification_log = new NotificationLog();
    $notification_log->parent_id = isset($notification_data['parent_id']) ? $notification_data['parent_id'] : null;
    $notification_log->user_id = isset($notification_data['user_id']) ? $notification_data['user_id'] : null;
    $notification_log->type = $notification_data['type'];
    $notification_log->notification_event = $notification_data['event'];
    $notification_log->from_email = isset($notification_data['from_email']) ? $notification_data['from_email'] : null;
    $notification_log->to_email = isset($notification_data['to_email']) ? $notification_data['to_email'] : null;
    $notification_log->to_phone = isset($notification_data['to_phone']) ? $notification_data['to_phone'] : null;
    $notification_log->gateway_used = $notification_data['gateway_used'];
    $notification_log->email_subject = isset($notification_data['email_subject']) ? $notification_data['email_subject'] : null;
    $notification_log->email_body = isset($notification_data['email_body']) ? $notification_data['email_body'] : null;
    $notification_log->sms_body = isset($notification_data['sms_body']) ? $notification_data['sms_body'] : null;
    $notification_log->resend_expiration_time = isset($notification_data['resend_expiration_time']) ? $notification_data['resend_expiration_time'] : null;
    $notification_log->exception = isset($notification_data['exception']) ? $notification_data['exception'] : null;
    $notification_log->is_resend = isset($notification_data['is_resend']) ? $notification_data['is_resend'] : 0;
    $notification_log->notification_time = Carbon::now();
    $notification_log->save();
    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . $notification_data['type'] . " notification data inserted into database successfully.");
    return true;
}

/**
 * getNotificationChannel
 * Fetch the notification Channel for sending SMS and Emails
 * @param  mixed $data
 * @return void
 */
function getNotificationChannel($data)
{
    // This is not the first attempt then need to select a channel randomly except the last one used
    if (isset($data['resend']) && $data['resend'] == 1) {

        $sql = "SELECT s.*
        FROM settings s
        LEFT JOIN (SELECT id, gateway_used FROM notification_logs WHERE `notification_event` = ? ";
        $searchArray = [$data['event']];
        if ($data['type'] == SMS) {
            $sql .= " AND to_phone = ? ";
            array_push($searchArray, $data['phone_no']);
        } else {
            $sql .= " AND to_email = ? ";
            array_push($searchArray, $data['email']);
        }
        $sql .= " AND `type` = ? ORDER BY created_at DESC LIMIT 1) nl ON s.id = nl.gateway_used
        WHERE s.type = ? AND nl.id IS NULL
        ORDER BY RAND() LIMIT 1";
        array_push($searchArray, $data['type'], $data['type']);
        $channel = DB::select($sql, $searchArray);
        return isset($channel[0]) ? $channel[0] : Setting::where(['is_default' => 1, 'type' => $data['type']])->first();
    } else {
        // This is the first attempt, so we can use the default channel
        return Setting::where(['is_default' => 1, 'type' => $data['type']])->first();
    }
}

/**
 * institutionwiseFraudUser
 * It will check if the institution ID resides on the institution_list_for_email_notifications table. If found then it will send an email to Admin
 * @param  mixed $response
 * @param  mixed $type
 * @return void
 */
function institutionwiseFraudUser($response, $type, $send_mail, $user_id)
{
    $user = User::where('user_id', $user_id)->first(); // Fetch the consumer details
    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Checking started for institution wise fraud for Consumer ID: " . $user->user_id);
    $birth_year = date('Y', strtotime($user->date_of_birth));

    $notification_arr = [];
    // If the type is 1 then it is a direct link account. There should be a loop
    if ($type == 1 && $send_mail == 1) {
        foreach ($response as $res) {
            $institution_fraud = InstitutionListForEmailNotification::where('institution_id', $res['institutionId'])->where('start_birth_year', '<=', $birth_year)->where('end_birth_year', '>=', $birth_year)->where('state', $user->state)->where('min_balance', '<', $res['balance'])->first();
            if ($institution_fraud) {
                $institutionId = $res['institutionId'];
                $min_balance = $institution_fraud->min_balance;
                $res['blacklisted'] = '';
                $res['used_by_other_consumer'] = 0;
                $data = getNotificationData($user, $res, $type); // Prepare the notification data
                array_push($notification_arr, $data);
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Institution wise fraud found for the Institution ID is : " . $res['institutionId']);
            }
        }
    }
    if (!empty($notification_arr)) {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Sending Email to admin due to Institution wise fraud found for User ID: " . $user->user_id);
        // Prepare the email loop section as lumen doesn't support the @foreach tag inside baldecomplier
        $is_institution_fraud = true;
        $notification_body = getNotificationBody($notification_arr, $is_institution_fraud);
        // Sending Mail Notification
        $email_params = [
            'user_id' => $user->user_id,
            'notification_body' => $notification_body,
            'notification_heading' => 'We have detected a consumer with ' . $institutionId . ' Institution having balance greater than $' . $min_balance . '. The details are given below.',
        ];
        $emailexecutor = new EmailExecutorFactory();
        $emailexecutor->emailForInstitutionwiseFraudUserDetected($email_params);
    }
    return true;
}
/** addCreditAfterTransactionVoid
 * Add Credit row after the transaction is voided
 * @param  mixed $transaction
 * @return void
 */
function addCreditAfterTransactionVoid($transaction, $data, $requestdata)
{
    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Transaction Reward Points revert started for Void Transaction for Transaction ID: " . $transaction->id);

    $voided = getStatus(VOIDED);
    $pending = getStatus(PENDING);
    $transaction_rewards = TransactionRewardDetail::where(['transaction_id' => $transaction->id])->where('reward_amount', '>', 0)->get();
    Log::info("select rewards: " . getTimeElapsedArr($requestdata));
    if (!empty($transaction_rewards)) {
        // Prepare an array to hold data for batch insertion
        $userRewardUsageHistoryData = [];

        foreach ($transaction_rewards as $reward) {
            $userRewardUsageHistoryData[] = [
                'id' => generateUUID(),
                'user_id' => $transaction->consumer_id,
                'entry_type' => CREDIT,
                'transaction_id' => $transaction->id,
                'reason' => TRANSACTION_VOIDED,
                'reward_point' => $reward->reward_point,
                'reward_point_left' => $reward->reward_point,
                'exchange_rate' => $reward->exchange_rate,
                'reward_amount' => $reward->reward_amount,
                'reward_amount_left' => $reward->reward_amount,
                'is_generic_point' => $reward->is_generic_point,
                'is_merchant_funded' => $reward->is_merchant_funded,
                'reward_wheel_id' => !is_null($reward->reward_wheel_id) ? $reward->reward_wheel_id : null,
                'sponsor_link_id' => is_null($reward->reward_wheel_id) ? $reward->sponsor_link_id : null,
                'campaign_id' => $reward->campaign_id,
                'petition_id' => $reward->petition_id,
                'corporate_parent_id' => $reward->corporate_parent_id,
                'created_at' => now(), // assuming created_at and updated_at are timestamp fields
                'updated_at' => now(),
            ];

            // Update UserCurrentRewardDetail table
            $user_current_reward_details = UserCurrentRewardDetail::where('user_id', $transaction->consumer_id);
            if (!is_null($reward->campaign_id)) {
                $user_current_reward_details->where('campaign_id', $reward->campaign_id);
            } else if (!is_null($reward->corporate_parent_id) && $reward->is_generic_point == 0) {
                $user_current_reward_details->where('corporate_parent_id', $reward->corporate_parent_id);
            } else if (!is_null($reward->sponsor_link_id)) {
                $user_current_reward_details->where('sponsor_link_id', $reward->sponsor_link_id);
            } else if (!is_null($reward->reward_wheel_id) && $reward->is_generic_point == 1) {
                $user_current_reward_details->whereNull('sponsor_link_id')->whereNull('corporate_parent_id');
            } else {
                $user_current_reward_details->whereNull('sponsor_link_id')->whereNull('corporate_parent_id')->whereNull('reward_wheel_id');
            }

            $user_current_reward_details = $user_current_reward_details->first();
            $user_current_reward_details->reward_point = $user_current_reward_details->reward_point + $reward->reward_point;
            $user_current_reward_details->reward_amount = $user_current_reward_details->reward_amount + $reward->reward_amount;
            $user_current_reward_details->save();
        }

        Log::info("insert and update rewards: " . getTimeElapsedArr($requestdata));
        // Batch insert UserRewardUsageHistory records
        UserRewardUsageHistory::insert($userRewardUsageHistoryData);

        // Void Free Spin if not used yet
        Reward::where(['transaction_id' => $transaction->id, 'is_used' => 0, 'is_free_spin' => 1])
            ->update(['status_id' => $voided]);
        Log::info("update free spin: " . getTimeElapsedArr($requestdata));

        // Void the Reward against the free Spin
        Reward::where(['transaction_id' => $transaction->id, 'status_id' => $pending])
            ->where(function ($q) {
                $q->where('is_free_spin', 0)->orWhere('is_used', 1);
            })
            ->update(['status_id' => $voided]);
        Log::info("update free spin: " . getTimeElapsedArr($requestdata));

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Reward Points reverted for Void Transaction for Transaction ID: " . $transaction->id);
    } else {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - No Rewards Found for the Transaction ID: " . $transaction->id);
    }
    return true;
}

/**
 * addDebitAfterTransactionVoidRevoked
 * Add Debut Row row after the void transaction is revoked
 * @param  mixed $transaction
 * @return void
 */
function addDebitAfterTransactionVoidRevoked($transaction)
{
    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Transaction Reward Points revert started after Void Revoked for Transaction ID: " . $transaction->id);

    $active = getStatus(ACTIVE_CODE);
    $pending = getStatus(PENDING);
    $voided = getStatus(VOIDED);

    $transaction_modified = TransactionDetails::where(['id' => $transaction->id])->first();

    $transaction_id = $transaction_modified->change_request_transaction_ref_no != null ? $transaction_modified->change_request_transaction_ref_no : $transaction->id;
    // Update is_voided
    AchSixRecordDetail::where('transaction_id', $transaction_id)->update(['is_voided' => 0]);
    AchTransactionDetail::where('transaction_id', $transaction_id)->update(['is_voided' => 0]);
    $transaction_rewards = TransactionRewardDetail::where(['transaction_id' => $transaction->id])->where('reward_amount', '>', 0)->get();
    if (!empty($transaction_rewards)) {
        // Prepare data for batch insert
        $userRewardUsageHistoryData = [];

        foreach ($transaction_rewards as $reward) {
            $userRewardUsageHistoryData[] = [
                'id' => generateUUID(),
                'user_id' => $transaction->consumer_id,
                'entry_type' => DEBIT,
                'transaction_id' => $transaction->id,
                'reason' => TRANSACTION_VOID_REVOKED,
                'reward_point' => $reward->reward_point,
                'reward_point_left' => $reward->reward_point,
                'exchange_rate' => $reward->exchange_rate,
                'reward_amount' => $reward->reward_amount,
                'reward_amount_left' => $reward->reward_amount,
                'is_generic_point' => $reward->is_generic_point,
                'is_merchant_funded' => $reward->is_merchant_funded,
                'reward_wheel_id' => !is_null($reward->reward_wheel_id) ? $reward->reward_wheel_id : null,
                'sponsor_link_id' => is_null($reward->reward_wheel_id) ? $reward->sponsor_link_id : null,
                'corporate_parent_id' => !is_null($reward->corporate_parent_id) ? $reward->corporate_parent_id : null,
                'campaign_id' => !is_null($reward->campaign_id) ? $reward->campaign_id : null,
                'petition_id' => !is_null($reward->petition_id) ? $reward->petition_id : null,
                'created_at' => now(),
                'updated_at' => now(),
            ];

            // Update UserCurrentRewardDetail table
            $user_current_reward_details = UserCurrentRewardDetail::where('user_id', $transaction->consumer_id);
            if (!is_null($reward->campaign_id)) {
                $user_current_reward_details->where('campaign_id', $reward->campaign_id);
            } else if (!is_null($reward->corporate_parent_id) && $reward->is_generic_point == 0) {
                $user_current_reward_details->where('corporate_parent_id', $reward->corporate_parent_id);
            } else if (!is_null($reward->sponsor_link_id)) {
                $user_current_reward_details->where('sponsor_link_id', $reward->sponsor_link_id);
            } else if (!is_null($reward->reward_wheel_id) && $reward->is_generic_point == 1) {
                $user_current_reward_details->whereNull('sponsor_link_id')->whereNull('corporate_parent_id');
            } else {
                $user_current_reward_details->whereNull('sponsor_link_id')->whereNull('corporate_parent_id')->whereNull('reward_wheel_id');
            }

            $user_current_reward_details = $user_current_reward_details->first();
            if ($user_current_reward_details->reward_point - $reward->reward_point < 0) {
                Log::info(__METHOD__ . "(Line: " . __LINE__ . ") - " . "Sufficient reward points are not available for Transaction ID: " . $transaction->id . " in the user_current_reward_details table for user_current_reward_details ID: " . $user_current_reward_details->id);
                return false;
            }
            $user_current_reward_details->reward_point = $user_current_reward_details->reward_point - $reward->reward_point;
            $user_current_reward_details->reward_amount = $user_current_reward_details->reward_amount - $reward->reward_amount;
            $user_current_reward_details->save();

        }

        // Batch insert into UserRewardUsageHistory table
        UserRewardUsageHistory::insert($userRewardUsageHistoryData);

        // Change the Free Spin status to Active if not used and Voided
        Reward::where('transaction_id', $transaction->id)
            ->where('is_used', 0)
            ->where('is_free_spin', 1)
            ->update(['status_id' => $active]);

        // Change the status of reward to to pending
        Reward::where('transaction_id', $transaction->id)
            ->where('status_id', $voided)
            ->where(function ($q) {
                $q->where('is_free_spin', 0)->orWhere('is_used', 1);
            })
            ->update(['status_id' => $pending]);

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Reward Points reverted for Void Revoked Transaction ID: " . $transaction->id);
    } else {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - No Rewards Found for the Transaction ID: " . $transaction->id);
    }
    return true;
}

function revertPointsAganistTransaction($transaction, $type)
{
    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Revert Back the Points in the user_reward_usage_history table for Transaction ID: " . $transaction->id);

    // If Void Revoked the set the is_voided flag 0 again for ach tables
    if ($type == 0) {
        AchTransactionDetail::where(['transaction_id' => $transaction->id])->update(['is_voided' => 0]);
        AchSixRecordDetail::where(['transaction_id' => $transaction->id])->update(['is_voided' => 0]);
        AchMerchantRewardHistory::where(['transaction_id' => $transaction->id])->update(['is_voided' => 0]);
        UserRewardUsageHistory::where(['transaction_id' => $transaction->id, 'entry_type' => CREDIT, 'user_id' => $transaction->consumer_id])->update(['reward_point_left' => 0, 'reward_amount_left' => 0]);
    } else {
        AchMerchantRewardHistory::where(['transaction_id' => $transaction->id])->update(['is_voided' => 1]);
    }

    $sql = "WITH latest_rewards AS (
        SELECT m.*, ROW_NUMBER() OVER (PARTITION BY reward_id ORDER BY created_at DESC) AS rn
        FROM " . config('app.reward_wheel_db') . ".user_reward_usage_history AS m
        WHERE transaction_id = ? AND user_id = ? AND reason IN ('" . REWARD_POINT . "','" . TRANSACTION . "','" . CASHBACK_POINTS . "') and reward_id is not null and entry_type = '" . CREDIT . "'
      )
      SELECT * FROM latest_rewards WHERE rn = 1;
      ";
    $reward_for_transaction = DB::select($sql, [$transaction->id, $transaction->consumer_id]);
    if (!empty($reward_for_transaction)) {
        // Initialize an array to hold all the records to be inserted
        $insertData = [];

        foreach ($reward_for_transaction as $reward) {
            $insertData[] = [
                'id' => generateUUID(),
                'user_id' => $transaction->consumer_id,
                'reward_id' => $reward->reward_id,
                'store_id' => !is_null($reward->store_id) ? $reward->store_id : null,
                'corporate_parent_id' => !is_null($reward->corporate_parent_id) ? $reward->corporate_parent_id : null,
                'campaign_id'         => !is_null($reward->campaign_id) ? $reward->campaign_id : null,
                'petition_id'         => !is_null($reward->petition_id) ? $reward->petition_id : null,
                'cashback_program_id' => !is_null($reward->cashback_program_id) ? $reward->cashback_program_id : null,
                'reward_wheel_id' => !is_null($reward->reward_wheel_id) ? $reward->reward_wheel_id : null,
                'sponsor_link_id' => is_null($reward->reward_wheel_id) ? $reward->sponsor_link_id : null,
                'entry_type' => $type == 1 ? DEBIT : CREDIT,
                'transaction_id' => $transaction->id,
                'reason' => $type == 1 ? TRANSACTION_VOIDED : TRANSACTION_VOID_REVOKED,
                'reward_point' => $reward->reward_point,
                'reward_point_left' => $type == 1 ? 0 : $reward->reward_point,
                'exchange_rate' => $reward->exchange_rate,
                'reward_amount' => $reward->reward_amount,
                'reward_amount_left' => $type == 1 ? 0 : $reward->reward_amount,
                'is_generic_point' => $reward->is_generic_point,
                'is_merchant_funded' => $reward->is_merchant_funded,
                'created_at' => now(), // assuming timestamps are required
                'updated_at' => now(),
            ];
        }

        // Batch insert
        UserRewardUsageHistory::insert($insertData);

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Reward Points reverted for Void/Revoke Transaction ID: " . $transaction->id);
    } else {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - No Rewards Found for the Transaction ID: " . $transaction->id);
    }
    return true;
}

/**
 * getSettingsValue
 * This function will send the value for a specified Cron Job
 * @param  mixed $name
 * @param  mixed $defaultValue
 * @return void
 */
function getSettingsValue($name, $defaultValue)
{
    $settings = Setting::where('name', $name)->first();
    if (!empty($settings)) {
        return $settings->val;
    }
    return $defaultValue;
}

function microbiltRuleMatchHistoryCount($phone, $matching_decision_code = null, $bank_link_type = null, $is_count = true)
{
    $microbiltRuleMatchHistoryQuery = MicrobiltRuleMatchHistory::join('microbilt_rules as mr', 'microbilt_rule_match_history.rule_id', '=', 'mr.id')->where('microbilt_rule_match_history.phone', $phone);

    if (isset($matching_decision_code)) {
        $microbiltRuleMatchHistoryQuery = $microbiltRuleMatchHistoryQuery->where('mr.matching_decision_code', $matching_decision_code);
    } else {
        $microbiltRuleMatchHistoryQuery = $microbiltRuleMatchHistoryQuery->where('mr.matching_decision_code', '!=', MICROBILT_OUTCOME_ONE);
    }

    if (isset($bank_link_type)) {
        $microbiltRuleMatchHistoryQuery = $microbiltRuleMatchHistoryQuery->where('bank_link_type', $bank_link_type);
    }
    if ($is_count) {
        return $microbiltRuleMatchHistoryQuery->groupBy('microbilt_rule_match_history.account_no', 'microbilt_rule_match_history.routing_no')->get()->count();
    } else {
        return $microbiltRuleMatchHistoryQuery->select('microbilt_rule_match_history.*')->orderByDesc('microbilt_rule_match_history.updated_at')->first();
    }
}

function addCustomerAccounts($data)
{
    $consumer_balance = new ConsumerAccountBalance();
    $consumer_balance->consumer_id = $data['user_id'];
    $consumer_balance->account_id = $data['account_id'];
    $consumer_balance->balance = $data['balance'];
    $consumer_balance->response_raw_balance = $data['response_raw_balance'];
    $consumer_balance->response_available_balance = $data['response_available_balance'];
    $consumer_balance->purchase_power = $data['purchase_power'];
    $consumer_balance->source = $data['source'];
    $consumer_balance->purchase_power_source = $data['purchase_power_source'];
    $consumer_balance->one_time_refresh = $data['one_time_refresh'];
    $consumer_balance->banking_solution_response = $data['banking_solution_response'];
    $consumer_balance->save();

    return true;
}

/**
 * saveBankingSolutionResponseData
 * This function is used to store the banking solution response in a table
 * @param  mixed $params
 * @param  mixed $response
 * @param  mixed $banking_solution_id
 * @return void
 */
function saveBankingSolutionResponseData($params, $response, $banking_solution_id)
{
    $banking_solution_response = new BankingSolutionResponse();
    $banking_solution_response->banking_solution_id = $banking_solution_id;
    $banking_solution_response->consumer_id = $params['consumer_id'] ?? null;
    $banking_solution_response->api = $params['api'];
    $banking_solution_response->akoya_interaction_id = $params['akoya_interaction_id'] ?? null;
    $banking_solution_response->response = $response;
    $banking_solution_response->save();
    return true;
}

/**
 * getbankingSolutionMasterId
 * Fetch the Banking solution Master ID
 * @return void
 */
function getbankingSolutionMasterId($banking_solution_name)
{
    return BankingSolutionMaster::where(['banking_solution_name' => $banking_solution_name])->first()->id;
}

/**
 * checkWeeklyLimits
 * This function is used to check the Consumer Weekly limit
 * @param  mixed $user
 * @return void
 */
function checkWeeklyLimits($user_details)
{
    $time_array = [];
    // Fetching Weekly Total Transaction Done by the consumer
    $weekly_limit = DB::select("select IFNULL(sum(transaction_details.consumer_bank_posting_amount),0) as weekly_spent from `transaction_details` straight_join `status_master` on `transaction_details`.`status_id` = `status_master`.`id` and `status_master`.`code`  = '" . PENDING . "' where `transaction_details`.`consumer_id` = ? and `transaction_details`.`is_v1` = 0", [$user_details['user_id']]);
    $weeklyTransactionDone = $weekly_limit[0]->weekly_spent;
    if ($user_details['bank_link_type'] == 0) { // For New User who Linked bank Manually
        $max_weekly_transaction_limit = $user_details['weekly_spending_limit'] > 0 ? $user_details['weekly_spending_limit'] : config('app.max_transaction_limit_weekly_for_non_bank_linked');
    } else { // For New/Existing User who Linked bank via Finicity
        $max_weekly_transaction_limit = $user_details['disable_automatic_weekly_spending_limit'] == 1 && $user_details['weekly_spending_limit'] > 0 ? $user_details['weekly_spending_limit'] : config('app.max_transaction_limit_weekly_for_bank_linked');
    }
    $weekly_amount_left = max(0, floatVal($max_weekly_transaction_limit) - floatVal($weeklyTransactionDone)); // Weekly Amount Left
    return floor($weekly_amount_left);
}

/**
 * comparePurchasePowerWeeklyLimits
 * This function is used to check the Consumer Weekly limit
 * @param  mixed $user
 */
function comparePurchasePowerWeeklyLimits($user_details, $purchase_power)
{
    $weekly_amount_left = checkWeeklyLimits($user_details);
    if ($weekly_amount_left > $purchase_power) {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "The Purchase Power is less than Weekly amount left. Purchase power : " . $purchase_power . " , Weekly amount left: " . $weekly_amount_left . " For User ID: " . $user_details['user_id']);
        return $purchase_power;
    } else {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "The weekly amount left is less than Purchase Power. Purchase power : " . $purchase_power . " , Weekly amount left: " . $weekly_amount_left . " For User ID: " . $user_details['user_id']);
        return $weekly_amount_left;
    }
}

/**
 * storeWeeklySpendingLimitHistory
 * Store weekly spending limit history in CustomPurchasePowerHistory table
 * @return void
 */
function storeWeeklySpendingLimitHistory($user, $new_weekly_spending_limit, $admin_control)
{
    if ($user->weekly_spending_limit != $new_weekly_spending_limit) {
        $weekly_history = new CustomPurchasePowerHistory();
        $weekly_history->consumer_id = $user->user_id;
        $weekly_history->old_weekly_spending_limit = $user->weekly_spending_limit;
        $weekly_history->new_weekly_spending_limit = $new_weekly_spending_limit;
        $weekly_history->admin_control = $admin_control;
        $weekly_history->save();
    }
}

/**
 * get risk score against consumer for all unique account present for consumer.
 *
 * @return mixed
 */
function getRiskScore($phone)
{
    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Getting consumer risk score started...");

    $sql_risk_score = "WITH combined_results AS (
        SELECT
         CASE
         WHEN ROUND(cppdt.risk_score, 2) = ROUND(cppdt.risk_score)
         THEN CAST(ROUND(cppdt.risk_score) AS VARCHAR(255)) ELSE CAST(cppdt.risk_score AS VARCHAR(255)) END AS risk_score,
         cppdt.created_at
        FROM registration_session_details rsd
        JOIN consumer_purchase_power_decision_table cppdt ON rsd.id = cppdt.consumer_id
        WHERE rsd.phone = ? AND cppdt.risk_score IS NOT NULL
        UNION ALL
        SELECT
         CASE
         WHEN ROUND(cppdt.risk_score, 2) = ROUND(cppdt.risk_score)
         THEN CAST(ROUND(cppdt.risk_score) AS VARCHAR(255)) ELSE CAST(cppdt.risk_score AS VARCHAR(255)) END AS risk_score,
         cppdt.created_at
        FROM users u
        JOIN consumer_purchase_power_decision_table cppdt ON u.user_id = cppdt.consumer_id
        WHERE u.phone = ? AND cppdt.risk_score IS NOT NULL
        )
        SELECT risk_score
        FROM combined_results
        ORDER BY created_at DESC
        LIMIT 1";
    $risk_score = DB::connection(MYSQL_RO)->select($sql_risk_score, [$phone, $phone]);

    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Consumer risk score fetched successfully against id: " . $phone);
    if (empty($risk_score)) {
        return false;
    }
    return $risk_score[0]->risk_score;
}

function fedRoutingNumberExistsCheck($request_data, $routing_no)
{
    if (isset($request_data['bank_id'])) {
        $fed_routing = FinancialInstitutionRoutingNumber::select('financial_institution_masters.*')->join('financial_institution_masters', 'financial_institution_routing_numbers.financial_institution_id', '=', 'financial_institution_masters.id')->where('financial_institution_routing_numbers.routing_no', $routing_no)->where('financial_institution_routing_numbers.new_routing_no', '*********')->whereNull('financial_institution_routing_numbers.deleted_at')->where('financial_institution_masters.id', $request_data['bank_id'])->first();
        if ($fed_routing) {
            return $fed_routing;
        }
    }
    $fed_routing = FinancialInstitutionRoutingNumber::select('financial_institution_masters.*')->join('financial_institution_masters', 'financial_institution_routing_numbers.financial_institution_id', '=', 'financial_institution_masters.id')->where('financial_institution_routing_numbers.routing_no', $routing_no)->where('financial_institution_routing_numbers.new_routing_no', '*********')->whereNull('financial_institution_routing_numbers.deleted_at')->first();
    if ($fed_routing) {
        $bank_name = $request_data['bank_name'];
        if (strpos($fed_routing->bank_name, $bank_name) === false) {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Bank name not matched. Selcted Bank Name: " . $bank_name . " , Link Bank Name: " . $fed_routing->bank_name . ".");
        }
    }
    return $fed_routing;
}

function generateToken()
{
    $user_details = Auth::user(); // Fetching Logged In User Details
    // Find the user you want to generate a token for
    $user = User::find($user_details->user_id); // Replace with appropriate user ID or retrieval logic

    // Create a token for the user
    $token = JWTAuth::fromUser($user);

    return response()->json(compact('token'));
}

function extractTokenFromHttpResponse($response)
{
    // Regular expression to match the JSON part in the HTTP response
    $pattern = '/\{.*\}$/';

    // Check if the pattern matches and extract the JSON string
    if (preg_match($pattern, $response, $matches)) {
        $jsonString = $matches[0];

        // Decode the JSON string into a PHP array
        $data = json_decode($jsonString, true);

        // Check if the token exists in the array and return it
        if (isset($data['token'])) {
            return $data['token'];
        }
    }

    // Return null if token not found
    return null;
}

/**
 * This function will calculate the weighted match percentage between two strings
 * The weighted match is calculated as 100 - (levenshtein distance / max length of two strings * 100)
 * If the length of both strings is zero, return 100
 * @param string $str1 first string
 * @param string $str2 second string
 * @return int weighted match percentage
 */
function getWeightedMatch($str1, $str2)
{
    $levenshteinDistance = levenshtein($str1, $str2);
    $maxLength = max(strlen($str1), strlen($str2));
    if ($maxLength == 0) {
        return 100;
    }
    // To handle empty strings
    return 100 - ($levenshteinDistance / $maxLength * 100);
}

/**
 * getCampaignRewardAmount
 * This function will return the total reward amount for all campaigns for the given user
 * @param  $user_id 
 * @return object
 */
function getCampaignRewardAmount($user_id) {
    $canpay_points = UserCurrentRewardDetail::selectRaw('
        COALESCE(GREATEST(SUM(user_current_reward_details.reward_amount), 0), 0) as reward_amount
    ')
    ->where(['user_current_reward_details.user_id' => $user_id])->whereNotNull('campaign_id')->first();
    return $canpay_points;
}

/** getCorporateParentIdByStore
 * This function will return the corporate parent id for a particular store
 * @param  mixed $store_id
 * @return void
 */
function getCorporateParentIdByStore($store_id)
{
    $cp_role_id = getRole(CORPORATE_PARENT);
    $brand_admin_role_id = getRole(BRAND_ADMIN);
    $corporate_parent = StoreUserMap::join('users', 'users.user_id', '=', 'store_user_map.user_id')->select('store_user_map.*')->where('store_user_map.store_id', $store_id)->whereIn('users.role_id', [$cp_role_id, $brand_admin_role_id])->first();
    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "getCorporateParentIdByStore : " . json_encode($corporate_parent));
    return !empty($corporate_parent) ? $corporate_parent->user_id : null;
}

function merchantStorePetitionMap($merchant_id, $petition_id, $channel = 'datamigration')
{
    // Map Merchant and Petition
    $petition = Petition::where('id', $petition_id)
        ->whereNull('merchant_store_id')
        ->first();

    $merchant = MerchantStores::where('merchant_id', $merchant_id)
        ->whereNull('petition_id')
        ->first();

    if ($petition && $merchant) {
        $active = getStatus(ACTIVE_CODE);
        $merchant->petition_id = $petition_id;
        $merchant->save();
        $petition->merchant_store_id = $merchant->id;
        $petition->status_id = $active;
        $petition->onboarded_date = date('Y-m-d');
        $petition->save();
        Log::channel($channel)->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Merchant store updated with petition id: " . $petition_id . " for merchant id: " . $merchant_id);
        
        $corporate_parent_id = getCorporateParentIdByStore($merchant->id);
        if (!$corporate_parent_id) {
            createDefaultCpUser($merchant->id, null, $channel);
        } else {
            Log::channel($channel)->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Skipped default CP creation: already mapped with corporate parent ID: " . $corporate_parent_id . " for merchant ID: " . $merchant_id);
        }

    } else {
        Log::channel($channel)->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No active petition found for the given address.");
    }

    return true;
}

function updateStorePetitionPoints($petition_ids, $corporate_parent_id, $channel = 'daily')
{
    Log::channel($channel)->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Updating Store Petition Points.");
    $pending = getStatus(PENDING);
    $active = getStatus(ACTIVE_CODE);
    // Check if Reward Exists against this Petition ID
    $rewards = Reward::join('user_reward_usage_history', 'rewards.id', '=', 'user_reward_usage_history.reward_id')
        ->select('rewards.*', 'user_reward_usage_history.reward_point', 'user_reward_usage_history.reward_amount', 'user_reward_usage_history.is_generic_point')
        ->where(['rewards.status_id' => $pending, 'user_reward_usage_history.is_generic_point' => 0])
        ->whereIn('rewards.petition_id', $petition_ids)
        ->whereNull('rewards.corporate_parent_id')
        ->get();
    if ($rewards->isNotEmpty()) {
        
        foreach ($rewards as $reward) {
            // Update Reward to Active status after the Petition store map
            $reward_update = Reward::find($reward->id);
            $reward_update->status_id = $active;
            $reward_update->corporate_parent_id = $corporate_parent_id;
            $reward_update->save();
            
            UserRewardUsageHistory::where('reward_id', $reward->id)->update(['corporate_parent_id' => $corporate_parent_id]);

            // Update Reward points
            $user_current_reward_details = UserCurrentRewardDetail::where(['user_id' => $reward->user_id, 'is_generic_point' => $reward->is_generic_point])->whereNull('sponsor_link_id');
            $reward->is_generic_point == 0 ? $user_current_reward_details->where('corporate_parent_id', $corporate_parent_id) : '';
            $user_current_reward_details = $user_current_reward_details->first();
            if (!empty($user_current_reward_details)) {
                $user_current_reward_details->increment('reward_amount', $reward->reward_amount);
                $user_current_reward_details->increment('reward_point', $reward->reward_point);
            } else {
                $user_current_reward_details = new UserCurrentRewardDetail();
                if ($reward->is_generic_point == 0) {
                    $user_current_reward_details->corporate_parent_id = $corporate_parent_id;
                }
                $user_current_reward_details->user_id = $reward->user_id;
                $user_current_reward_details->reward_amount = $reward->reward_amount;
                $user_current_reward_details->reward_point = $reward->reward_point;
                $user_current_reward_details->is_generic_point = $reward->is_generic_point;
                $user_current_reward_details->save();
            }

            Log::channel($channel)->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Reward point Status updated to Status ID: " . $active . " for Petition IDs: " . implode(',', $petition_ids));
        }
    } else {
        Log::channel($channel)->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") No pending, unassigned corporate-parent reward points found for Petition IDs: " . implode(',', $petition_ids));

    }
    return true;
}

/**
 * Create a default corporate parent user
 *
 * @param string $cp_name Name of the corporate parent
 * @param int $store_id ID of the store
 *
 * @return User The created user
 */
function createDefaultCpUser($store_id, $old_cp_id = null, $channel = 'daily')
{
    $merchant = MerchantStores::find($store_id);
    if (!$merchant) {
        Log::channel($channel)->error(__METHOD__ . "(" . LINE . ": " . __LINE__ . "): " . "Merchant not found for store ID: $store_id.");
        return null;
    }
    $cp_name = $merchant->retailer;

    Log::channel($channel)->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . "): Creating default corporate parent user for store ID: $store_id.");

    $email = Str::slug($cp_name) . '_' . now()->format('Ymd_His') . '@example.com';
    Log::channel($channel)->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . "): Generated email: $email");

    // Retrieve required records
    $role_details = UserRole::where('role_name', CORPORATE_PARENT)->first();
    if (!$role_details) {
        Log::channel($channel)->error(__METHOD__ . "(" . LINE . ": " . __LINE__ . "): Role not found for role_name: " . CORPORATE_PARENT);
        return null;
    }

    $access_level_details = AccessLevelMaster::where('label', ADMIN_RIGHT)->first();
    if (!$access_level_details) {
        Log::channel($channel)->error(__METHOD__ . "(" . LINE . ": " . __LINE__ . "): Access level not found for label: " . ADMIN_RIGHT);
        return null;
    }

    $user_status = StatusMaster::where('code', USER_ACTIVE)->first();
    if (!$user_status) {
        Log::channel($channel)->error(__METHOD__ . "(" . LINE . ": " . __LINE__ . "): Status not found for code: " . USER_ACTIVE);
        return null;
    }

    // Create user
    $user = new User();
    $user->email = $email;
    $user->first_name = $cp_name;
    $user->contact_person_first_name = $cp_name;
    $user->role_id = $role_details->role_id;
    $user->status = $user_status->id;
    $user->show_void_transaction_menu = 0;
    $user->default_cp = 1;
    $user->save();
    
    Log::channel($channel)->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . "): User created with ID: " . $user->user_id);

    // Map user to store
    $storeusermap = new StoreUserMap();
    $storeusermap->store_id = $store_id;
    $storeusermap->user_id = $user->user_id;
    $storeusermap->user_access_level_id = $access_level_details->id;
    $storeusermap->save();
    Log::channel($channel)->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . "): Mapped user ID {$user->user_id} to store ID $store_id");
    
    // Update petition points if old_cp_id is not provided
    if (!$old_cp_id) {
        $petition_ids = MerchantStores::where('id', $store_id)->whereNotNull('petition_id')->distinct()->pluck('petition_id')->toArray();
        Log::channel($channel)->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . "): Found petition IDs: " . json_encode($petition_ids));

        if (!empty($petition_ids)) {
            updateStorePetitionPoints($petition_ids, $user->user_id, $channel);
            Log::channel($channel)->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . "): Updated petition points for user ID: " . $user->user_id);
        }
    }

    // // Update reward-related references if old_cp_id is provided
    // if ($old_cp_id) {
    //     // Update reward-related references
    //     // updateCorporateParentInRewards($old_cp_id, $user->user_id, $channel);
    // }

    return $user;
}

/**
 * Replaces all non-default corporate parents with a new one.
 * @param $new_store_ids The store IDs to check for non-default corporate parents.
 * @param $new_cp_id The new corporate parent ID to replace the old ones with.
 * @param $log_channel The log channel to use for logging. Defaults to 'daily'.
 * @return void
 */
function replaceNonDefaultCorporateParents($new_store_ids, $new_cp_id, $only_delete = false, $log_channel = 'daily')
{
    if (empty($new_store_ids)) {
        Log::channel($log_channel)->warning(__METHOD__ . "(" . LINE . ": " . __LINE__ . "): No store IDs provided.");
        return;
    }

    // Step 1: Get all user_ids mapped to the given store_ids where default_cp = 1
    $userIds = StoreUserMap::join('users', 'store_user_map.user_id', '=', 'users.user_id')
        ->whereIn('store_user_map.store_id', $new_store_ids)
        ->where('users.default_cp', 1)
        ->pluck('users.user_id');

    if ($userIds->isEmpty()) {
        Log::channel($log_channel)->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . "): No non-default CP users found for the given store IDs.");
        return;
    }

    foreach ($userIds as $old_user_id) {
        if (!empty($old_user_id)) {
            if (!$only_delete) {
                // Step 2: Update reward-related references
                updateCorporateParentInRewards($old_user_id, $new_cp_id, $log_channel);
            }

            // Step 3: Delete the old user and store_user_map
            StoreUserMap::where('user_id', $old_user_id)->delete();
            $user = User::where('user_id', $old_user_id)->where('default_cp', 1)->first();
            if ($user) {
                $user->delete();
            }

            Log::channel($log_channel)->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . "): Updated references from user ID: $old_user_id to new CP ID: $new_cp_id and deleted old user.");
        } else {
            Log::channel($log_channel)->warning(__METHOD__ . "(" . LINE . ": " . __LINE__ . "): Encountered an empty or invalid old user ID.");
        }
    }

}

/**
 * Update corporate parent references in reward-related tables.
 *
 * This function updates the corporate parent ID references in the 
 * UserCurrentRewardDetail, UserRewardUsageHistory, and Reward tables 
 * from an old corporate parent ID to a new corporate parent ID. 
 * It logs the update process to the specified log channel.
 *
 * @param $old_cp_id The old corporate parent ID to be replaced.
 * @param $new_cp_id The new corporate parent ID to replace with.
 * @param $log_channel The log channel for logging the updates. Defaults to 'daily'.
 * @return void
 */

function updateCorporateParentInRewards($old_cp_id, $new_cp_id, $log_channel = 'daily')
{
    if (!empty($old_cp_id)) {
        // Log entry before the update
        Log::channel($log_channel)->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . "): Starting update of reward-related references from CP ID: $old_cp_id to new CP ID: $new_cp_id.");

        UserCurrentRewardDetail::where('corporate_parent_id', $old_cp_id)
            ->update(['corporate_parent_id' => $new_cp_id]);

        UserRewardUsageHistory::where('corporate_parent_id', $old_cp_id)
            ->update(['corporate_parent_id' => $new_cp_id]);

        Reward::where('corporate_parent_id', $old_cp_id)
            ->update(['corporate_parent_id' => $new_cp_id]);

        Log::channel($log_channel)->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . "): Updated reward-related references from CP ID: $old_cp_id to new CP ID: $new_cp_id.");
    }
}
