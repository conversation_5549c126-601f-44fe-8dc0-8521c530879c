<template>
<div>
  <div v-if="loading">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Reduced Transaction Fees Report</h3>
                </div>
 
                <div class="card-body">
                <div class="row">
                  <div class="col-md-4">
                    <div class="form-group">
                      <multiselect
                        id="corporateparent"
                        name="corporateparent"
                        v-model="selectedCp"
                        placeholder="Select Corporate Parent (Min 3 chars)"
                        label="corporateparent"
                        track-by="id"
                        :options="cpList"
                        :multiple="false"
                        :loading="isLoadingCp"
                        :internal-search="false"
                        @search-change="getAllActiveCorporateParent"
                        @input="dateDiff"
                        @select="refreshStores"
                      ></multiselect>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <multiselect
                        id="store"
                        name="store"
                        v-model="selectedStore"
                        placeholder="Select Store (Min 3 chars)"
                        label="retailer"
                        track-by="id"
                        :options="storeList"
                        :multiple="true"
                        :loading="isLoadingSt"
                        :internal-search="true"
                        @input="dateDiff"
                      ></multiselect>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="start-date form-control"
                        placeholder="Start Date"
                        id="start-date"
                        onkeydown="return false"
                        autocomplete="off"
                      />
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="end-date form-control"
                        placeholder="End Date"
                        id="end-date"
                        onkeydown="return false"
                        autocomplete="off"
                      />
                    </div>
                  </div>
                </div>
                <div class="row ml-1">
                  <p style="font-size:12px; color:red; font-weight:bolder;">{{maxdateDiff == 7 ? 'Maximum accepted range between start date and end date is 7 days' : 'Maximum accepted range between start date and end date is 1 day'}}</p>
                </div>
              </div>
              <div class="card-footer">
                <button
                  type="button"
                  class="btn btn-success"
                  @click="generateReport(false)"
                  id="generateBtn"
                >
                  Generate
                </button>
                <button
                  type="button"
                  @click="generateReport(true)"
                  class="btn btn-danger ml-10"
                  id="generateExportBtn"
                >
                Generate & Export <i
                    class="fa fa-download ml-10"
                    aria-hidden="true"
                  ></i>
                </button>
                <button
                  type="button"
                  @click="reset()"
                  class="btn btn-success margin-left-5"
                >
                  Reset
                </button>
              </div>
              <!------ Table Start ----------------->
              <div class="card-body">
                <div class="row">
                <div class="col-12"><b-table-simple
                    responsive
                    show-empty
                    bordered
                    sticky-header="800px"
                    >
                    <b-thead head-variant="light">
                        <b-tr>
                        <b-th width="8%" class="text-center">Transaction Number</b-th>
                        <b-th class="text-center">Consumer</b-th>
                        <b-th class="text-center">Merchant</b-th>
                        <b-th width="8%" class="text-center">Store</b-th>
                        <b-th class="text-center">Amount ($)</b-th>
                        <b-th width="12%" class="text-center">Merchant Funded Points Used</b-th>
                        <b-th class="text-center">Total Fees ($)</b-th>
                        <b-th class="text-center">Reduced Fee Amount ($)</b-th>
                        <b-th class="text-center">Transaction Time</b-th>
                        <b-th class="text-center">Status</b-th>
                        </b-tr>
                    </b-thead>
                    <b-tbody v-for="(row, index) in report" :key="index">
                        <b-tr>
                        <b-td class="text-center text-gray">{{
                            row.transaction_number
                        }}</b-td>
                        <b-td class="text-center text-gray">{{
                        checkSpace(row.consumer_name)
                        }}</b-td>
                        <b-td class="text-center text-gray">{{
                            row.merchant_name
                        }}</b-td>
                        <b-td class="text-center text-gray">{{
                            row.store_name
                        }}</b-td>
                        <b-td class="text-center text-gray">
                            ${{row.amount}}
                        </b-td>
                        <b-td class="text-center text-gray">
                            {{row.merchant_reward_point_used}}
                        </b-td>
                        <b-td class="text-center text-gray">
                            ${{row.total_fees}}
                        </b-td>
                        <b-td class="text-center text-gray">
                            ${{row.total_fees_waived}}
                        </b-td>
                        <b-td v-html="row.transaction_time" class="text-center text-gray"></b-td>
                        <b-td class="text-center text-gray">{{
                            row.status
                        }}</b-td>
                        </b-tr>
                    </b-tbody>
                    </b-table-simple>
                </div>
                </div>
                </div>
                <!------ Table End ------------------->
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</div>
</template>
<script>
import api from "@/api/reports.js";
import moment from "moment";
import { saveAs } from "file-saver";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "./CustomLoader/CanPayLoader.vue"
export default {
  data() {
    return {
      headerTextVariant: 'light',
      report: [],
      fields: [],
      loading: false,
      storeList: [],
      selectedStore: [],
      reportGeneratedStore: [],
      cpList: [],
      rewardDetails: [],
      selectedCp: "",
      isLoadingCp: false,
      isLoadingSt: false,
      maxdateDiff: 1,
    };
  },
  components: {
    HourGlass,
    CanPayLoader
  },
  watch: {
  },
  mounted() {
    var self = this;
    $("#start-date").datepicker({
      format: "mm/dd/yyyy",
      autoclose: true,
      todayHighlight: true,
    }).on('changeDate', function (ev) {
        self.dateDiff();
    });
    $("#end-date").datepicker({
      format: "mm/dd/yyyy",
      autoclose: true,
      todayHighlight: true,
    }).on('changeDate', function (ev) {
        self.dateDiff();
    });
    $("#start-date , #end-date").datepicker("setDate", "-1d");
  },
  methods: {
    checkSpace(sentence) {
        let full_name = "";
        for(let curr_char of sentence){
        if(full_name.length>0 && full_name[full_name.length-1] == ' ' && curr_char == ' '){
            continue;
        }
        full_name+=curr_char;
        }
        return full_name;
    },
    dateDiff(){
      var self = this;
      if ($("#start-date").val() != "") {
        var from_date = moment($("#start-date").val()).format("YYYY-MM-DD");
      } else {
        var from_date = "";
      }
      if ($("#end-date").val() != "") {
        var to_date = moment($("#end-date").val()).format("YYYY-MM-DD");
      } else {
        var to_date = "";
      }
      if(from_date!='' && to_date!=''){
        //calculate the date Difference 
        var date1 = new Date(from_date);
        var date2 = new Date(to_date);

        // To calculate the time difference of two dates
        var Difference_In_Time = date2.getTime() - date1.getTime();
          
        // To calculate the no. of days between two dates
        var Difference_In_Days = Difference_In_Time / (1000 * 3600 * 24);

        if(self.selectedStore.length > 0 && self.selectedCp != ""){
          self.maxdateDiff = 7;
        } else {
          self.maxdateDiff = 1;
        }
        if(Difference_In_Days > self.maxdateDiff){
          $("#generateBtn").prop('disabled', true);
          $("#generateExportBtn").prop('disabled', true);
        }else{
          $("#generateBtn").prop('disabled', false);
          $("#generateExportBtn").prop('disabled', false);
        }
      }
    },
    //get the list of All CP
    getAllActiveCorporateParent(searchtxt) {
      var self = this;
      if(searchtxt.length >= 3){
        self.isLoadingCp = true;
        self.cpList = [];
        var request = {
          searchtxt: searchtxt,
        };
        api
          .getAllActiveCorporateParent(request)
          .then(function (response) {
            if (response.code == 200) {
              self.cpList = response.data;
              self.storeList = [];
              self.isLoadingCp = false;
            } else {
              error(response.message);
            }
          })
          .catch(function (error) {
            error(error);
          });
      }
    },
    //get the list of All Stores
    getAllActiveStores() {
      var self = this;
      self.isLoadingSt = true;
      self.storeList = [];
      var request = {
        user_id: self.selectedCp.id,
      };
      api
        .getAllActiveStores(request)
        .then(function (response) {
          if (response.code == 200) {
            self.storeList = response.data;
            self.isLoadingSt = false;
          } else {
            error(response.message);
          }
        })
        .catch(function (error) {
          error(error);
        });
    },
    // API call to generate the merchant location transaction report
    generateReport(isExport) {
      var self = this;
      if (
        moment($("#start-date").val()).format("YYYY-MM-DD") >
        moment().format("YYYY-MM-DD")
      ) {
        error("Start date cannot be from future.");
        return false;
      }
      if (
        moment($("#end-date").val()).format("YYYY-MM-DD") >
        moment().format("YYYY-MM-DD")
      ) {
        error("End date cannot be from future.");
        return false;
      }

      var stores = [];
      if (self.selectedStore != []) {
        $.each(self.selectedStore, function (key, value) {
          stores.push(value.id);
        });
      }
      self.reportGeneratedStore = self.selectedStore[0];
      self.report = [];
      var request = {
        from_date: moment($("#start-date").val()).format("YYYY-MM-DD"),
        to_date: moment($("#end-date").val()).format("YYYY-MM-DD"),
        store_id: stores,
        initiated_by: 'Admin'
      };
      if(request.from_date > request.to_date){
        error("To Date cannot be greater than From date");
        return false;
      }
      self.loading = true;
      api
        .getReducedTransactionFeesReport(request)
        .then(function (response) {
          if (response.code == 200) {
            self.report = response.data;
            if(isExport){
              self.exportReport();
            }
            self.loading = false;
          } else {
            error(response.message);
            self.loading = false;
          }
        })
        .catch(function (error) {
          // error(error);
          self.loading = false;
        });
    },
    // exports the report
    exportReport() {
        var self = this;
        self.loading = true;
        if(self.report.length == 0){
            error("No Record Found.");
            return;
        }
        var request = {
          report: self.report,
          from_date: moment($("#start-date").val()).format("YYYY-MM-DD"),
          to_date: moment($("#end-date").val()).format("YYYY-MM-DD"),
        };
        api
          .getReducedTransactionFeesExport(request)
          .then(function (response) {
            var FileSaver = require("file-saver");
            var blob = new Blob([response], {
              type: "application/xlsx",
            });
            FileSaver.saveAs(
              blob,
              moment().format("MM/DD/YYYY") + "_reduced_transaction_fees_report.xlsx"
            );
            self.loading = false;
          })
          .catch(function (error) {
            // error(error);
            self.loading = false;
          });
  },
    reset(){
      var self = this;
      self.selectedCp = "";
      self.maxdateDiff = 1;
      self.selectedStore = [];
      $("#start-date , #end-date").datepicker("setDate", "-1d");
    },
    refreshStores(selectedOption){
      var self = this;
      self.storeList = [];
      self.selectedStore = [];
      self.selectedCp = selectedOption;
      self.getAllActiveStores();
    },
    //View all merchant rewards
    viewMerchantRewarsList(sales_date){
      var self = this;
      self.loading = true;
      var request = {
        store_id: self.reportGeneratedStore.id,
        sales_date: moment(sales_date).format("YYYY-MM-DD"),
      };
      api
        .viewMerchantRewarsList(request)
        .then(function (response) {
          if (response.code == 200) {
            self.rewardDetails = response.data;
            self.$bvModal.show('view-merchant-rewards-modal');
            self.loading = false;
          } else {
            error(response.message);
            self.loading = false;
          }
        })
        .catch(function (error) {
          // error(error);
          self.loading = false;
        });
    }
  },
};
</script>
<style>
.bold-text {
  font-weight: bold;
}
#view-merchant-rewards-modal___BV_modal_content_{
  width: 900px !important;
  position: absolute;
  top: 50%;
  left: 50%;
}
</style>