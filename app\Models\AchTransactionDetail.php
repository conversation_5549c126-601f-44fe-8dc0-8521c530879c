<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class AchTransactionDetail extends Model
{
    /**
     * __construct
     * This function will insert an alphanumeric 32 character unique id as primary key in the table whenever a data get inserted
     * @param  mixed $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        $this->id = generateUUID();

        parent::__construct($attributes);
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'ach_file_id',
        'transaction_id',
        'entry_type',
        'merchant_id',
        'upload_date',
        'sec_code',
        'account_type',
        'routing_no',
        'account_no',
        'transaction_amount',
        'receiver_name',
        'originator_name',
        'company_entry_description',
        'posting_date',
        'is_voided',
        'batch_id',
    ];

    // Set the data type of the primary key
    protected $keyType = 'string';
    public $timestamps = false;
    public $incrementing = false;
    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
}
