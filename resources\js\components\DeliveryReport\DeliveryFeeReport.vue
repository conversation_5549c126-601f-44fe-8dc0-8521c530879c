<template>
<div>
    <div  v-if="loading">
        <CanPayLoader/>
    </div>
    <div class="content-wrapper" style="min-height: 36px;">
        <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
            <div class="col-sm-6"></div>
            </div>
        </div>
        </section>
        <div class="hold-transition sidebar-mini">
            <section class="content">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-12">
                            <div class="card card-success">
                                <div class="card-header">
                                    <h3 class="card-title">Delivery Fees Report</h3>
                                </div>
                                 <div class="card-body">
                                    <div class="row">
                                        <!---- Start Date Start ------>
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                <input
                                                    class="start-date form-control"
                                                    placeholder="Start Date"
                                                    id="start-date"
                                                    onkeydown="return false"
                                                    autocomplete="off"
                                                />
                                                </div>
                                            </div>
                                        <!---- Start Date End -------->
                                        <!---- End Date Start ------>
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <input
                                                        class="end-date form-control"
                                                        placeholder="End Date"
                                                        id="end-date"
                                                        onkeydown="return false"
                                                        autocomplete="off"
                                                    />
                                                </div>
                                            </div>
                                        <!---- End Date End -------->
                                        <!---- Merchant Select Start ------>
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <multiselect
                                                        id="merchant"
                                                        v-model="selectedMerchant"
                                                        placeholder="Select Merchant Or Delivery Partner (Min 3 chars)"
                                                        label="merchant_name"
                                                        :options="merchantList"
                                                        :loading="isLoadingMerchant"
                                                        :internal-search="false"
                                                        @search-change="getAllMerchant"
                                                    ></multiselect>
                                                </div>
                                            </div>
                                        <!---- Merchant Select End -------->
                                        <!---- Consumer Name Start -------------->
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <multiselect
                                                        v-model="selectedConsumer"
                                                        placeholder="Select Consumer (Min 3 chars)"
                                                        id="consumer"
                                                        label="consumer_name"
                                                        :options="consumerList"
                                                        :loading="isLoadingConsumer"
                                                        :internal-search="false"
                                                        @search-change="getConsumers"
                                                    ></multiselect>
                                                </div>
                                            </div>
                                        <!---- Consumer Name End ---------------->
                                        <!---- Consumer Phone Number ------------>
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                <input
                                                    class="form-control"
                                                    placeholder="Phone No."
                                                    id="phone_no"
                                                    v-model="phone_no"
                                                />
                                                </div>
                                            </div>
                                        <!---- Consumer Phone Number ------------>
                                        <!---- Consumer Email Number ------------>
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                <input
                                                    class="form-control"
                                                    placeholder="Email"
                                                    id="email"
                                                    v-model="email"
                                                />
                                                </div>
                                            </div>
                                        <!---- Consumer Email Number ------------>
                                    </div>
                                 </div>
                                 <!----- Button Section Start -------->
                                <div class="card-footer">
                                    <button
                                    type="button"
                                    class="btn btn-success"
                                    @click="generateReport(false)"
                                    >
                                    Generate
                                    </button>
                                    <button
                                    type="button"
                                    @click="generateReport(true)"
                                    class="btn btn-danger ml-10"
                                    >
                                    Generate & Export<i
                                        class="fa fa-download ml-10"
                                        aria-hidden="true"
                                    ></i>
                                    </button>
                                </div>
                                <!------ Button Section End ---------->
                                <!------ Table Start ----------------->
                                <div class="card-body">
                                <div class="row">
                                <div class="col-12"><b-table-simple
                                    responsive
                                    show-empty
                                    bordered
                                    sticky-header="800px"
                                    >
                                    <b-thead head-variant="light">
                                        <b-tr>
                                        <b-th width="8%" class="text-center">Transaction Number</b-th>
                                        <b-th class="text-center">Consumer</b-th>
                                        <b-th class="text-center">Delivery Partner</b-th>
                                        <b-th class="text-center">Merchant</b-th>
                                        <b-th width="8%" class="text-center">Store</b-th>
                                        <b-th class="text-center">Terminal</b-th>
                                        <b-th class="text-center">Amount ($)</b-th>
                                        <b-th class="text-center">Delivery Fee ($)</b-th>
                                        <b-th width="12%" class="text-center">Merchant Funding Amount ($)</b-th>
                                        <b-th class="text-center">Transaction Time</b-th>
                                        <b-th class="text-center">Status</b-th>
                                        </b-tr>
                                    </b-thead>
                                    <b-tbody v-for="(row, index) in report" :key="index">
                                        <b-tr>
                                        <b-td class="text-center text-gray">{{
                                            row.transaction_number
                                        }}</b-td>
                                        <b-td class="text-center text-gray">{{
                                        checkSpace(row.consumer_name)
                                        }}</b-td>
                                        <b-td class="text-center text-gray">{{
                                            row.delivery_partner
                                        }}</b-td>
                                        <b-td class="text-center text-gray">{{
                                            row.merchant_name
                                        }}</b-td>
                                        <b-td class="text-center text-gray">{{
                                            row.store_name
                                        }}</b-td>
                                        <b-td class="text-center text-gray">{{
                                            row.terminal_name
                                        }}</b-td>
                                        <b-td class="text-center text-gray">
                                            ${{row.amount == NULL ? '0.00': row.amount}}
                                        </b-td>
                                        <b-td class="text-center text-gray">
                                            ${{row.delivery_fee == NULL ? '0.00': row.delivery_fee}}
                                        </b-td>
                                        <b-td class="text-center text-gray">
                                            <p>${{row.merchant_funding_amount == NULL ? '0.00': row.merchant_funding_amount}}</p>
                                        </b-td>
                                        <b-td v-html="row.transaction_time" class="text-center text-gray"></b-td>
                                        <b-td class="text-center text-gray">{{
                                            row.status
                                        }}</b-td>
                                        </b-tr>
                                    </b-tbody>
                                    </b-table-simple>
                                </div>
                                </div>
                                </div>
                                <!------ Table End ------------------->
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </div>
</div>
</template>
<script>
import CanPayLoader from "../CustomLoader/CanPayLoader.vue";
import tranzactionApi from "@/api/transaction.js";
import merchantApi from "@/api/delivery.js";
import moment from "moment";
export default {
    name: "DeliveryFeeReport",
    components: {
        CanPayLoader
    },
    data() {
        return {
            loading:false,
            selectedMerchant: null,
            selectedConsumer: null,
            isLoadingConsumer:false,
            isLoadingMerchant:false,
            merchantList:[],
            consumerList:[],
            phone_no:"",
            email:"",
            report:[]
        }
    },
    methods:{
        checkSpace(sentence) {
            let full_name = "";
            for(let curr_char of sentence){
            if(full_name.length>0 && full_name[full_name.length-1] == ' ' && curr_char == ' '){
                continue;
            }
            full_name+=curr_char;
            }
            return full_name;
        },
        dateDiff(){
        var self = this;
        if ($("#start-date").val() != "") {
            var from_date = moment($("#start-date").val()).format("YYYY-MM-DD");
        } else {
            var from_date = "";
        }
        if ($("#end-date").val() != "") {
            var to_date = moment($("#end-date").val()).format("YYYY-MM-DD");
        } else {
            var to_date = "";
        }
        if(from_date!='' && to_date!=''){
            //calculate the date Difference
            var date1 = new Date(from_date);
            var date2 = new Date(to_date);
            var compDate = new Date(self.showOldSalesMaxDate);
            if (compDate >= date1) {
            self.showOldSalesReport = true;
            } else {
            self.showOldSalesReport = false;
            }

            // To calculate the time difference of two dates
            var Difference_In_Time = date2.getTime() - date1.getTime();

            // To calculate the no. of days between two dates
            var Difference_In_Days = Difference_In_Time / (1000 * 3600 * 24);

            if(Difference_In_Days > 60 || self.selectedStore.length!=1){
            $("#generateBtn").prop('disabled', true);
            }else{
            $("#generateBtn").prop('disabled', false);
            }
        }
        },
        getConsumers(searchtxt){
            var self = this;
            if(searchtxt.length >= 3){
                self.isLoadingConsumer = true;
                var request = {
                searchtxt: searchtxt,
            };
        tranzactionApi
          .getConsumers(request)
            .then(function (response) {
                if (response.code == 200) {
                self.consumerList = response.data;
                } else {
                error(response.message);
                }
                self.isLoadingConsumer = false;
            })
            .catch(function (err) {
            });
            }
        },
        getAllMerchant(searchtxt){
            var self = this;
            if(searchtxt.length >= 3){
                self.isLoadingMerchant = true;
                var request = {
                merchant_name: searchtxt,
            };
            merchantApi
                .getAllMerchantsAndDeliveryPartners(request)
                .then(function (response) {
                if (response.code == 200) {
                    self.merchantList = response.data;
                }else {
                    error(response.message);
                }
                self.isLoadingMerchant = false;
                })
                .catch(function (error) {
                });
                }
        },
        // exports the report
        exportReport() {
             var self = this;
             self.loading = true;
            if(self.report.length == 0){
                error("No Record Found.");
                return;
            }
             var request = {
               report: self.report,
             };
             merchantApi
               .getDeliveryFeeTransactionExport(request)
               .then(function (response) {
                 var FileSaver = require("file-saver");
                 var blob = new Blob([response], {
                   type: "application/xlsx",
                 });
                 FileSaver.saveAs(
                   blob,
                   moment().format("MM/DD/YYYY") + "_delivery_transaction_fee_report.xlsx"
                 );
                 self.loading = false;
               })
               .catch(function (error) {
                 // error(error);
                 self.loading = false;
               });
        },
        generateReport(isExport){
            let self = this;
        if(
         moment($("#start-date").val()).format("YYYY-MM-DD") == 'Invalid date' || moment($("#end-date").val()).format("YYYY-MM-DD") == 'Invalid date'
        ){
           error("Please Provide Start Date and End Date.")
           return false;
        }
        if (
         moment($("#start-date").val()).format("YYYY-MM-DD") >
         moment().format("YYYY-MM-DD")
        ) {
         error("Start date cannot be from future.");
         return false;
        }
        if (
         moment($("#end-date").val()).format("YYYY-MM-DD") >
         moment().format("YYYY-MM-DD")
        ) {
         error("End date cannot be from future.");
         return false;
        }
        const payload = {
            from_date: moment($("#start-date").val()).format("YYYY-MM-DD"),
            to_date: moment($("#end-date").val()).format("YYYY-MM-DD"),
        }
        if(self.email!=""){
            payload.email = self.email;
        }
        if(self.phone_no!=""){
            payload.phone_no = self.phone_no;
        }
        if(self.selectedMerchant != null){
            payload.merchant = self.selectedMerchant.id;
        }
        if(self.selectedConsumer != null){
            payload.selectedConsumer = self.selectedConsumer.user_id;
        }
        self.loading = true;
        merchantApi
        .getDeliveryFeeReport(payload)
        .then((response) => {
            self.report = response.data;
            if(isExport){
                self.exportReport();
            }

            self.loading = false;
        })
        .catch((err) => {
         self.loading = false;
         error(err);
        })
        }
    },
    mounted() {
        var self = this;
        $("#start-date").datepicker({
        format: "mm/dd/yyyy",
        autoclose: true,
        todayHighlight: true,
        });
        $("#end-date").datepicker({
        format: "mm/dd/yyyy",
        autoclose: true,
        todayHighlight: true,
        });
        $("#start-date , #end-date").datepicker("setDate", new Date());
    },
}
</script>
<style scoped>

</style>
