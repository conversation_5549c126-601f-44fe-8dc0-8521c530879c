import { loadProgressBar } from 'axios-progress-bar'

const getACHTransactionData = (request) => {
    var instance = axios.create({
        baseURL: process.env.MIX_ACH_APP_URL,
    });
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.post('/api/admin/getachfileslasttwotransactiondates', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getACHReportForId = (request) => {
    var instance = axios.create({
        baseURL: process.env.MIX_ACH_APP_URL,
    });
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.post('/api/admin/gettransactionreport', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const exportAchTransactionReturnList = (request) => {
    var header = {
        responseType: 'blob',
    };
    var instance = axios.create({
        baseURL: process.env.MIX_ACH_APP_URL,
    });
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.post('/api/admin/export/returnlist', request, header)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getAchReturnReport = (request) => {
    var instance = axios.create({
        baseURL: process.env.MIX_ACH_APP_URL,
    });
    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.post('/api/admin/fetchachreturnreport', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);   
            })
    })   
};

const exportAchReturnReport = (request) =>{

    var header = {
        responseType: 'blob',
    };

    var instance = axios.create({
        baseURL: process.env.MIX_ACH_APP_URL,
    });

    loadProgressBar({}, instance)
    return new Promise((res, rej) => {
        instance.post('/api/admin/export/achreturnreport', request, header)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);   
            })
    })  

};

export default {
    getACHTransactionData,
    getACHReportForId,
    exportAchTransactionReturnList,
    getAchReturnReport,
    exportAchReturnReport
}