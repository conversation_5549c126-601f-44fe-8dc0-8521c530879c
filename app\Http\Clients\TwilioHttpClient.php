<?php
namespace App\Http\Clients;

use Illuminate\Support\Facades\Log;
use Twilio\Rest\Client;

/**
 *
 * @package App\Http\Clients
 */
class TwilioHttpClient
{
    /**
     * @var Client
     */
    private $defaultClient;
    private $default_phone_no;
    /**
     * TwilioHttpClient constructor
     *
     * Creating new object with Twilio valid sId and Token
     */
    public function __construct()
    {
        // Default Twilio Credentials
        $sid = config('app.twilio_sid');
        $token = config('app.twilio_token');
        $this->default_phone_no = config('app.twilio_phone_no');
        $this->defaultClient = new Client($sid, $token);
    }
    /**
     * This function sends OTP to the given phone number during registration process
     */
    public function sendSMS($params)
    {
        try {
            $client = $params['client'] . 'Client';
            $phone_no = $params['client'] . '_phone_no';
            $message = $this->$client->messages->create(
                // the number you'd like to send the message to
                config('app.country_code') . $params['phone_no'],
                array(
                    // A Twilio phone number you purchased at twilio.com/console
                    'from' => $this->$phone_no,
                    // the body of the text message you'd like to send
                    'body' => $params['sms_body'],
                ));
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Twilio send sms for phone number: " . $params['phone_no'] . " from " . $params['notification_channel'] . " returned response : " . $message);
            return 1;
        } catch (\Twilio\Exceptions\RestException $ex) {
            // Update the exception in the notification log table
            // NotificationLog::find($params['notification_id'])->update(['exception' => $ex->getMessage()]);
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Error while sending request for phone number: " . $params['phone_no'] . " from " . $params['notification_channel'] . " to twilio : " . $ex);
            return 0;
        }
    }
}
