<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class FinancialInstitutionMaster extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'bank_name',
        'frontend_visibility',
        'logo_url',
        'is_finicity',
        'is_akoya',
        'is_mx',
        'akoya_provider_id',
        'mx_institution_code'
    ];
    public $timestamps = true;
    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
}
