<?php
namespace App\Http\Middleware;

use App\Models\UserRole;
use Closure;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class CheckRoleMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next, ...$roles)
    {
        if (!Auth::check()) {
            // Unauthorized response if token not there
            $message = trans('message.jwt_error');
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . $message);
            return renderResponse(UNAUTH, $message, null);
        }

        $user = Auth::user();
        $checkRole = UserRole::join('users', 'users.role_id', '=', 'user_roles.role_id')->where(['users.user_id' => $user->user_id])->whereIn('user_roles.role_name', $roles)->first();
        if (empty($checkRole)) {
            $message = trans('message.invalid_access_request');
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Invalid access request to Merchant Admin panel by User ID: " . $user->user_id);
            return renderResponse(UNAUTH, $message, null);
        }
        return $next($request);
    }
}
