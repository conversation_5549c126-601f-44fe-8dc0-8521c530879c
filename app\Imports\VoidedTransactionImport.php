<?php

namespace App\Imports;

use App\Models\ConsumerCardMap;
use App\Models\MerchantStores;
use App\Models\StatusMaster;
use App\Models\TransactionDetails;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class VoidedTransactionImport implements ToModel, WithHeadingRow
{
    private $rows = 0;

    /**
     * @param array $row
     * This function actully imports the data as row from Excel Sheet. Here we used the WithHeadingRow to get the Data with Heading. Do Not try to get the rows with index.
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row)
    {
        $card_number = $row['cardnumber'];
        if (!empty($card_number)) {
            try {
                // Fetch the consumer details via card number
                $consumer_card_details = ConsumerCardMap::where('card_number', $card_number)->first();
                if (!empty($consumer_card_details)) { // If the consumer exists then proceed for insertion
                    // Formatting the excel/csv time
                    if (is_numeric($row['transactiondate']) == 1) { // Checking is the sheet is excel
                        $UNIX_DATE = ($row['transactiondate'] - 25569) * 86400;
                        $transaction_time = gmdate("Y-m-d H:i:s", $UNIX_DATE);
                    } else { // It is CSV
                        $transaction_time = date("Y-m-d H:i:s", strtotime($row['transactiondate']));
                    }

                    $store = $row['storenumber1'];
                    // Check if store exists or not
                    $checkStoreExists = MerchantStores::where('store_id', $store)->first();
                    if (!empty($checkStoreExists)) {
                        $checkTransactionExists = TransactionDetails::join('terminal_master', 'terminal_master.id', '=', 'transaction_details.terminal_id')->join('merchant_stores', 'merchant_stores.id', '=', 'terminal_master.merchant_store_id')->join('consumer_card_map', 'consumer_card_map.user_id', '=', 'transaction_details.consumer_id')->select('transaction_details.id')->where(['merchant_stores.store_id' => $store, 'consumer_card_map.card_number' => $card_number])->whereRaw("transaction_details.local_transaction_time LIKE ?",['%' . substr($transaction_time, 0, -3) . '%'])->whereRaw("(transaction_details.amount + transaction_details.tip_amount) = ?",[floatval(preg_replace("/[^0-9.]/", '', $row['originalamount']))])->get();

                        if (!$checkTransactionExists->isEmpty()) {
                            if (count($checkTransactionExists) == 1) {
                                $transaction = TransactionDetails::find($checkTransactionExists[0]->id);
                                $status = StatusMaster::where('code', VOIDED)->first(); // As we are getting all the voided transactions from this sheet, so we are setting the status as voided for all records
                                $transaction->status_id = $status->id;
                                $transaction->voided_transaction_imported_by = Auth::user()->user_id;
                                $transaction->save();
                                ++$this->rows;
                                Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Voided status updated in Transaction Details Table with Transaction ID : " . $transaction->id . " for Consumer ID: " . $consumer_card_details->user_id);
                            } else {
                                Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Voided status updation skipped due to return of more than one transaction for Consumer Card Number: " . $row['cardnumber']);
                                insertSkippedDataLog('Voided Transaction', 'cardnumber', $row['cardnumber'], "Voided status updation skipped due to return of more than one transaction for Consumer Card Number: " . $row['cardnumber'], json_encode($row), 'Voided Transaction');
                            }
                        } else {
                            Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Voided status updation skipped due to non availability of Transaction for Consumer Card Number: " . $row['cardnumber']);
                            insertSkippedDataLog('Voided Transaction', 'cardnumber', $row['cardnumber'], "Voided status updation skipped due to non availability of Transaction for Consumer Card Number: " . $row['cardnumber'], json_encode($row), 'Voided Transaction');
                        }
                    }else {
                        Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Voided status updation skipped due to non availability of Store ".$store." for Consumer Card Number: " . $row['cardnumber']);
                        insertSkippedDataLog('Voided Transaction', 'cardnumber', $row['cardnumber'], "Voided status updation skipped due to non availability of Store ".$store." for Consumer Card Number: " . $row['cardnumber'], json_encode($row), 'Voided Transaction');
                    }

                } else { // Updation skipped due to non availability of consumer
                    Log::channel('datamigration')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Voided status updation skipped due to non availability of consumer in Transaction Details Table for Consumer Card Number: " . $row['cardnumber']);
                    insertSkippedDataLog('Voided Transaction', 'cardnumber', $row['cardnumber'], "Voided status updation skipped due to non availability of consumer in Transaction Details Table for Consumer Card Number: " . $row['cardnumber'], json_encode($row), 'Voided Transaction');
                }

            } catch (\Exception $e) {
                Log::channel('datamigration')->error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured during status updation of Voided Transaction in Transaction Details Table for Consumer Card Number: " . $row['cardnumber'] . ".", [EXCEPTION => $e]);
                insertSkippedDataLog('Voided Transaction', 'cardnumber', $row['cardnumber'], $e, json_encode($row), 'Voided Transaction');
            }
        }
    }

    public function getRowCount(): int
    {
        return $this->rows;
    }
}
