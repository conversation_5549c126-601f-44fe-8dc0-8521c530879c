<template>
<div>
  <div v-if="loading">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Consumer Bank Statement</h3>
                </div>

                <!-- /.card-header -->
                <div class="card-body">
                  <div class="row">
                  <div class="col-md-4">
                    <div class="form-group">
                     <input
                        class="form-control"
                        placeholder="Consumer Name (min 3 chars)"
                        id="consumer"
                        v-model="consumer"
                      />
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="form-control"
                        placeholder="Phone No (Exact)"
                        id="phone_no"
                        v-model="phone_no"
                      />
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="form-control"
                        placeholder="Email (Exact)"
                        id="email"
                        v-model="email"
                      />
                    </div>
                  </div>
                </div>
                </div>
                <div class="card-footer">
                    <button
                      type="button"
                      class="btn btn-success"
                      @click="searchConsumers()"
                    >
                      Search
                    </button>
                    <button
                      type="button"
                      @click="reset()"
                      class="btn btn-success margin-left-5"
                    >
                      Reset
                    </button>
                  </div>
                  <div class="card-body">
                  <b-table-simple
                      responsive
                      show-empty
                      bordered
                      sticky-header="800px"
                      v-if="allDetails.length > 0"
                    >
                      <b-thead head-variant="light">
                        <tr>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Phone</th>
                            <th class="text-center">Created On</th>
                            <th class="text-center">Action(s)</th>
                        </tr>
                      </b-thead>
                      <b-tbody v-for="(row, index) in allDetails" :key="index">
                        <b-tr>
                          <b-td class="text-left text-gray">{{
                            row.name
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.email
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.phone
                          }}</b-td>
                          <b-td class="text-center text-gray">{{
                            row.created_at
                          }}</b-td>
                          <b-td class="text-center text-gray">
                            <a :data-id="row.edit" :data-session="row.session_id" class="get-scores custom-edit-btn" title="Change Status" variant="outline-success"><i class="nav-icon fas fa-edit"></i></a></b-td>
                        </b-tr>
                      </b-tbody>
                    </b-table-simple>
                    <p v-else>No data displayed. Please refine your search criteria.</p>
                    </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>

    <!-- Edit Modal End -->

    <b-modal
      id="pending-edit-modal"
      ref="pending-edit-modal"
      title="Consumer Upload Bank Statement Validation Details"
      :no-close-on-esc="false"
      :no-close-on-backdrop="false"
      hide-footer
    >
      <div class="row">
        <div class="col-12">
          <span>
            <b>Consumer Bank Statement Verify</b>
          </span>
        </div>
      </div>
      <hr />
      <div class="row">
        <div class="col-6" style="text-align: center" v-for="(row, index) in allDocuments">
          <img
            slot="image"
            disabled
            v-bind:src="row.url"
            class="id-preview"
             v-if="!row.isPdf"
          />
          <embed
            v-bind:src="row.url"
            class="id-preview"
            type="application/pdf"
            v-else
          />
          <button @click="download(row.url)" class="btn btn-success m-2">
            <i class="fa fa-view"></i> Full View
          </button>
        </div>
      </div>
      <hr />
      <div class="row">
        <div class="col-12">
          <span>
            <b>Consumer Details:</b>
          </span>
        </div>
      </div>
      <hr />
      <div class="row">
        <div class="col-6">
          <span>
            <b>Name:</b>
            {{ input_name }}
          </span>
        </div>
        <div class="col-6">
          <span>
            <b>Mobile Number:</b>
            {{ input_phone }}
          </span>
        </div>
      </div>

      <br />
      <div class="row">
        <div class="col-6">
          <span>
            <b>Email:</b>
            {{ input_email }}
          </span>
        </div>
        <div class="col-6">
          <span>
            <b>Address:</b>
            {{ user_address }}
          </span>
        </div>
      </div>
      <div class="row" v-if="items.length > 0">
        <div class="col-12">
          <b-table striped hover :items="items" :fields="fields"></b-table>
        </div>
      </div>
      <hr />
      <div class="row">
        <div class="col-md-12 row-value">
          <label for="email">
            Comment
            <span class="red">*</span>
          </label>
          <textarea
            name="comment"
            id="comment"
            class="form-control"
            v-model="consumer_comment"
            v-validate="'required'"
          ></textarea>
          <span v-show="errors.has('comment')" class="text-danger">{{
            errors.first("comment")
          }}</span>
        </div>
      </div>
    </b-modal>
  </div>
</div>
</template>
<script>
import api from "@/api/review.js";
import moment from "moment";
import commonConstants from "@/common/constant.js";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "../CustomLoader/CanPayLoader.vue"
export default {
  data() {
    return {
      fields: ["source", "score"],
      items: [],
      review_flag: "",
      modalTitle: "",
      options: [
        { code: 1003, status: "Retake" }, //code is based on db status code
        { code: 600, status: "Archive" }, //code is based on db status code
      ],
      review_status: {}, //code is based on db status code
      record_id: "",
      allDetails: {},
      allDocuments: [],
      imagesrcback: "",
      source_name: "",
      source_phone: "",
      source_address: "",
      source_dob: "",
      source_ssn: "",
      input_name: "",
      input_phone: "",
      input_address: "",
      input_dob: "",
      input_ssn: "",
      name_score: "",
      address_score: "",
      phone_score: "",
      ssn_score: "",
      dob_score: "",
      average_score: "",
      reason: "",
      headerTextVariant: "light",
      input_email: "",
      currentUser: "",
      user_address: "",
      searchvalue: "",
      showReloadBtn: false,
      consumer_comment: null,
      is_retake: false,
      loading:false,
      consumer:"",
      phone_no:"",
      email:"",
    };
  },
  components: {
    HourGlass,
    CanPayLoader
  },
  created() {
    this.getDetails();
    this.email = this.$route.query.email;
  },
  methods: {
    searchConsumers(){
      var self = this;
      if((self.consumer).trim().length < 3 && $("#phone_no").val().trim() === '' &&  $("#email").val().trim() === ''){
        error("Please provide Consumer Name (Min 3 chars) or email(exact) or phone no(exact)");
        return false;
      }
      var request = {
        consumer: self.consumer,
        email:self.email,
        phone_no:self.phone_no,
        type:1003
      };
      self.loading = true;
      api
      .getAllConsumerUploadDocument(request)
      .then(function (response) {
        if (response.code == 200) {
          self.allDetails = response.data;
          self.loading = false;
        } else {
          error(response.message);
          self.loading = false;
        }
      })
      .catch(function (error) {
        // error(error);
        self.loading = false;
      });
    },
    getDetails() {
      var self = this;
      $(document).on("click", ".get-scores", function (e) {
        self.record_id = $(e.currentTarget).attr("data-id");
        self.allDocuments = [];
        var request = {
          id: self.record_id,
        };
        api
          .getConsumerDocumentDetails(request)
          .then((response) => {
            if (response.code == 200) {
              self.input_name = response.data.name;
              self.input_phone = response.data.details.phone;
              self.input_email = response.data.details.email;
              self.user_address = response.data.address;
              self.review_status = {
                code: response.data.details.status_code,
                status: response.data.details.status_name,
              };
              if(response.data.documents.length > 0){
                response.data.documents.forEach(document => {
                  var checkPdf = document.match(/.pdf/g);
                  self.allDocuments.push({url:document, isPdf:checkPdf});
                });
              }
              self.options = [
                { code: 1003, status: "Retake" }, //code is based on db status code
                { code: 600, status: "Archive" }, //code is based on db status code
              ];
              self.items = [];
              self.consumer_comment = response.data.details.comment;
              self.$refs["pending-edit-modal"].show();
            } else {
              error(response.message);
            }
          })
          .catch((err) => {
            console.log(err);
          });
      });
    },
    updateStatus() {
      var self = this;
      if (self.review_status.code == 600) {
        var r = confirm(
          "Do you want to update the Consumer upload document status?"
        );
      } else {
        var r = confirm(
          "Do you want to update the review status? Please note once you update it you cannot change it back."
        );
      }

      if (r == true) {
        var request = {
          id: self.record_id,
          code: self.review_status.code,
          status_updated_by: self.currentUser.user_id,
          comment: self.consumer_comment,
        };
        api
          .updateConsumerUploadDocumentStatus(request)
          .then((response) => {
            if (response.code == 200) {
              success(response.message);
              self.searchConsumers();
            } else {
              error(response.message);
            }
          })
          .catch((err) => {
            error(err);
          });
      } else {
        return false;
      }
    },
    download(url) {
      window.open(url, "_blank").focus();
    },
    reset(){
      var self = this;
      self.consumer = "";
      self.phone_no = "";
      self.email = "";
    }
  },
  mounted() {
    var self = this;
    if(self.email){
      self.searchConsumers();
    }
    document.title = "CanPay - V1 Pending Identity";

    self.currentUser = localStorage.getItem("user")
      ? JSON.parse(localStorage.getItem("user"))
      : null;
  },
  watch: {
    review_status: {
      handler(value) {
        console.log(value);
        if (value.code == 1003) {
          this.is_retake = true;
        } else {
          this.is_retake = false;
        }
      },
      immediate: true, // This ensures the watcher is triggered upon creation
    },
  },
};
</script>
