{"private": true, "scripts": {"dev": "npm run development", "development": "cross-env NODE_ENV=development node_modules/webpack/bin/webpack.js --progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js", "watch": "npm run development -- --watch", "watch-poll": "npm run watch -- --watch-poll", "hot": "cross-env NODE_ENV=development node_modules/webpack-dev-server/bin/webpack-dev-server.js --inline --hot --config=node_modules/laravel-mix/setup/webpack.config.js", "prod": "npm run production", "production": "cross-env NODE_ENV=production node_modules/webpack/bin/webpack.js --no-progress --hide-modules --config=node_modules/laravel-mix/setup/webpack.config.js"}, "devDependencies": {"@mdi/font": "2.5.94", "@vue/cli-plugin-babel": "3.0.1", "@vue/cli-plugin-eslint": "3.0.1", "@vue/cli-service": "3.0.1", "@vue/eslint-config-standard": "3.0.1", "axios": "^0.26.1", "bootstrap": "^4.4.1", "cross-env": "^5.1", "jquery": "^3.7.1", "laravel-mix": "^4.0.7", "lodash": "^4.17.13", "material-design-icons-iconfont": "3.0.3", "popper.js": "^1.16.0", "resolve-url-loader": "^2.3.1", "sass": "^1.89.2", "sass-loader": "^7.3.1", "stylus": "0.54.5", "stylus-loader": "3.0.1", "vue-analytics": "5.8.0", "vue-i18n": "7.4.0", "vue-router": "^3.6.5", "vue-template-compiler": "^2.7.16", "vuex": "3.0.1", "vuex-router-sync": "5.0.0"}, "dependencies": {"axios-progress-bar": "^1.2.0", "bootstrap-vue": "^2.23.1", "chartist": "0.11.0", "file-saver": "^2.0.5", "moment": "^2.30.1", "moment-timezone": "^0.5.48", "node-sass": "^4.13.1", "nprogress": "^0.2.0", "sweetalert2": "^9.17.4", "vee-validate": "^2.2.15", "vue": "^2.7.16", "vue-axios": "^2.1.5", "vue-chartist": "2.1.2", "vue-ckeditor2": "^2.1.5", "vue-clipboard2": "^0.3.3", "vue-firestore": "^0.3.30", "vue-google-autocomplete": "^1.1.4", "vue-loading-spinner": "^1.0.11", "vue-meta": "1.5.2", "vue-multiselect": "^2.1.9", "vue-search-select": "^2.9.6", "vue-select": "^3.20.4", "vue-smooth-dnd": "^0.8.1", "vue-swatches": "^2.1.1", "vue-sweetalert2": "^4.2.0", "vue2-google-maps": "^0.10.7", "vuejs-countdown-timer": "^2.1.3", "vuelidate": "^0.7.7", "vuetify": "1.4.3"}}