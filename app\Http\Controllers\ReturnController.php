<?php

namespace App\Http\Controllers;

use App\Exports\ReturnSkippedRecordsExport;
use App\Http\Factories\EmailExecutor\EmailExecutorFactory;
use App\Http\Factories\Transaction\TransactionFactory;
use App\Imports\ReturnDataImport;
use App\Models\BankDetailsForProbableReturn;
use App\Models\EmailTemplate;
use App\Models\MigrationLog;
use App\Models\ReturnManualPostLog;
use App\Models\ReturnReasonMaster;
use App\Models\TransactionDetails;
use App\Models\UnknownReturnReason;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use Smalot\PdfParser\Parser;

class ReturnController extends Controller
{
    public function __construct(Request $request)
    {
        $this->request = $request;
        $this->transaction = new TransactionFactory();
        $this->emailexecutor = new EmailExecutorFactory();
    }

    /**
     * getReturnLog
     * Listing for Return Posted Log
     * @param  mixed $request
     * @return void
     */
    public function getReturnLog(Request $request)
    {
        // Columns defined for Sorting
        $columns = array(
            0 => 'migration_log.summary',
            1 => 'migration_log.actual_data_imported',
            2 => 'migration_log.duplicate_date_imported',
            3 => 'users.first_name',
            4 => 'migration_log.created_at',
        );
        // Main Query
        $query = MigrationLog::on(MYSQL_RO)->join('users', 'users.user_id', '=', 'migration_log.uploaded_by')->select('migration_log.*', 'users.first_name', 'users.middle_name', 'users.last_name')->where('migration_log.type', $request->get('type'));

        //Count Query
        $queryCount = MigrationLog::on(MYSQL_RO)->join('users', 'users.user_id', '=', 'migration_log.uploaded_by')
            ->selectRaw('COUNT(*) as total_count')
            ->where('migration_log.type', $request->get('type'));

        $totalData = $queryCount->first()->total_count; // Getting total no of rows

        $totalFiltered = $totalData;
        $limit = intval($request->input('length'));
        $start = intval($request->input('start'));
        $order = $columns[$request->input('order.0.column')];
        $query_limit = $query->offset(0)->limit(3);
        $dir = $request->input('order.0.dir');
        if (empty($request->input('search.value')) && empty($order) && empty($dir)) {
            $migration_logs = $query_limit->orderBy('migration_log.created_at', 'DESC')->get();
        } else {
            $migration_logs = $query_limit->orderBy($order, $dir)->get();
        }

        $data = array();
        if (!empty($migration_logs)) {
            // Creating array to show the values in frontend
            foreach ($migration_logs as $migration_log) {
                $nestedData['summary'] = $migration_log->summary;
                $nestedData['actual_data_imported'] = $migration_log->actual_data_imported;
                $nestedData['duplicate_date_imported'] = $migration_log->duplicate_date_imported;
                $nestedData['uploaded_by'] = $migration_log->first_name . ' ' . $migration_log->middle_name . ' ' . $migration_log->last_name;
                $nestedData['edit'] = $migration_log->id;
                $nestedData['batch_id'] = $migration_log->batch_id;
                $nestedData['created_at'] = date('m-d-Y h:i A', strtotime($migration_log->created_at));
                $data[] = $nestedData;
            }
        }
        // Drawing the Datatable
        $json_data = array(
            "draw" => intval($request->input('draw')),
            "recordsTotal" => intval($totalData),
            "recordsFiltered" => intval($totalFiltered),
            "data" => $data,
        );

        Log::info(addslashes(__METHOD__) . "(" . LINE . __LINE__ . ") : Return Log List fetched successfully");
        echo json_encode($json_data); // Rerurning the data
    }

    /**
     * postReturnsFromExcel
     * This function is used to import and post returns
     * @return void
     */
    public function postReturnsFromExcel()
    {
        DB::beginTransaction();
        try {
            ini_set('max_execution_time', 6000);
            $return_post = $this->request->file('return_post');

            //check if excel is uploaded or not
            if (empty($return_post)) {
                $message = "Please upload the data in order to post return.";
                return renderResponse(FAIL, $message, null);
            }
            //====================Code Segment For Upload and Posting Returns==========================
            if (!empty($return_post)) {
                Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Return data excel upload done and import started");
                //Fetch the Batch ID for upload
                $returnLog = ReturnManualPostLog::orderBy('batch_id', 'DESC')->first();
                if (!empty($returnLog)) {
                    $batchID = $returnLog->batch_id + 1;
                } else {
                    $batchID = 1000;
                }
                $import = new ReturnDataImport($batchID);
                Excel::import($import, $return_post);
                Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Return data excel import completed");

                //Post the Canpay Offset for All the Returns posted
                $this->_postCanPayOffset();
            }

            // Insertion in Return Log table Started
            $count = explode('|', $import->getRowCount());
            $log = new MigrationLog();
            $log->type = 'ReturnPost';
            $log->summary = 'Success';
            $log->actual_data_imported = $count[0];
            $log->duplicate_date_imported = $count[1];
            $log->uploaded_by = Auth::user()->user_id;
            $log->batch_id = $batchID;
            $log->save();

            DB::commit();

            $message = "Total " . $count[0] . " returns posted successfully and " . $count[1] . " records skipped.";
            return renderResponse(SUCCESS, $message, null);
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured while importing return data.", [EXCEPTION => $e]);
            DB::rollback();
            $message = "There was some problem while trying to post returns. Processed data for current period has been rolled back.";
            return renderResponse(FAIL, $message, null);
        }
    }

    /**
     * This function represents manually linked consumer trasnactions who have opted to pay now but waiting for webhook call
     */
    private function _postCanPayOffset()
    {
        Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Posting CanPay Return Recovery transaction to acheck21 for representment");
        $transaction_sum = TransactionDetails::join('status_master', 'transaction_details.status_id', '=', 'status_master.id')->select(DB::raw('sum(transaction_details.amount) as sum'), DB::raw('sum(transaction_details.tip_amount) as tip_sum'))->where('transaction_details.is_represented', 1)->where('transaction_details.is_v1', 0)->where('transaction_details.transaction_ref_no', null)->where('status_master.code', APPROVED_BY_ADMIN)->first(); // sum query
        if ($transaction_sum->sum != null) {
            $amount = ($transaction_sum->sum + $transaction_sum->tip_sum);
            $params['amount'] = -$amount;
            $params['acheck_account_id'] = config('app.canpay_return_recovery');
            //calling the factory function to create consumer transaction into acheck21
            $response = $this->transaction->createCanPayReturnTransaction($params);
            $response_decoded = json_decode($response, true);
            if (isset($response_decoded['documentId'])) {
                //create a new transaction
                $transaction_details = new TransactionDetails();
                $transaction_details->transaction_number = generateTransactionId();
                $transaction_details->entry_type = "Cr";
                $transaction_details->transaction_time = Carbon::now();
                $transaction_details->local_transaction_time = Carbon::now();
                $transaction_details->local_transaction_year = date("Y", strtotime($transaction_details->local_transaction_time));
                $transaction_details->local_transaction_month = date("m", strtotime($transaction_details->local_transaction_time));
                $transaction_details->local_transaction_date = date("Y-m-d", strtotime($transaction_details->local_transaction_time));
                $transaction_details->amount = $transaction_sum->sum;
                $transaction_details->tip_amount = $transaction_sum->tip_sum;
                $transaction_details->status_id = getStatus(PROCESSED_FOR_ACHECK21);
                $transaction_details->transaction_place = ACHECK21;
                $transaction_details->acheck_document_id = $response_decoded['documentId'];
                $transaction_details->is_represented = 1;
                $transaction_details->save();

                //update the status of all the represented returned transactions from returned to pending
                $transactions = TransactionDetails::join('status_master', 'transaction_details.status_id', '=', 'status_master.id')->select("transaction_details.*")->where('transaction_details.is_represented', 1)->where('transaction_details.is_v1', 0)->where('transaction_details.transaction_ref_no', null)->where('status_master.code', APPROVED_BY_ADMIN)->get();
                foreach ($transactions as $transaction) {
                    $transaction->status_id = getStatus(PENDING);
                    $transaction->save();
                }
                Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "CanPay transaction details was stored successfully for amount(without tip): " . $transaction_sum->sum);
            } else {
                Log::channel('return-transaction')->error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "There was some problem trying to post transaction into Acheck21.");
            }
        } else {
            Log::channel('return-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No transactions found.");
        }
    }

    /**
     * getReturnSkippedLogsExcel
     * This is the export function for Store API Keys
     * @param  mixed $request
     * @return void
     */
    public function getReturnSkippedLogsExcel(Request $request)
    {
        return Excel::download(new ReturnSkippedRecordsExport($request), 'return_skipped_records' . date('m-d-Y H:i:s') . '.xlsx');
    }

    public function importReturnTransactions(Request $request)
    {
        Log::channel('return-transactions-from-pdf')->info("Importing return transactions from PDF process started...");
        $file = $request->file('return_transactions');
        if (empty($file)) {
            $message = trans('message.empty_file');
            return renderResponse(FAIL, $message, null);
        }
        $allowed = array('pdf');
        if (!in_array($file->getClientOriginalExtension(), $allowed)) {
            $message = trans('message.invalid_file');
            return renderResponse(FAIL, $message, null);
        }

        $pdfParser = new \Smalot\PdfParser\Parser(); // Parsing the PDF
        $pdf = $pdfParser->parseFile($file->path());
        $pages = $pdf->getPages(); // Fetch all pages of the PDF
        if (count($pages) > 0) {
            Log::channel('return-transactions-from-pdf')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . count($pages) . " return transactions found from the PDF file. Updating database..");
            $i = 1;
            $data_imported = 0;
            foreach ($pages as $page) {
                Log::channel('return-transactions-from-pdf')->info("Page number " . $i . " of PDF started processing...");
                try {
                    $result = $page->getText();
                    $res = explode("\n", $result);
                    // Gathering required informations from PDF file
                    $routing_number = trim($res[18]);
                    $account_number = trim($res[23]);

                    Log::channel('return-transactions-from-pdf')->info("Probable return processing started by inserting the bank detals in the table...");

                    // Check if the bank details already exists in the table
                    $checkBankDetailsExists = BankDetailsForProbableReturn::where(['account_no' => $account_number, 'routing_no' => $routing_number])->count();
                    if ($checkBankDetailsExists == 0) {
                        // Insert the account number and routing number in the probable return table if not exists previously
                        $probable_return = new BankDetailsForProbableReturn();
                        $probable_return->account_no = $account_number;
                        $probable_return->routing_no = $routing_number;
                        $probable_return->save();
                        $data_imported++;
                        Log::channel('return-transactions-from-pdf')->debug("Probable return data added in the table.");
                    } else {
                        Log::channel('return-transactions-from-pdf')->debug("Probable return data skipped as the data already exists in the table.");
                    }
                } catch (\Exception $e) {
                    Log::channel('return-transactions-from-pdf')->error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured while trying to mark the bank details has a probable return", [EXCEPTION => $e]);
                    continue; // Continue the process in case of an exception occured.
                }
                $i++;
            }
            Log::channel('return-transactions-from-pdf')->info("Saving import numbers in migration log table...");
            // Save import log in database
            $log = new MigrationLog();
            $log->type = 'ReturnTransactions';
            $log->summary = 'Success';
            $log->actual_data_imported = $data_imported;
            $log->duplicate_date_imported = 0;
            $log->migrated_data_updated = 0;
            $log->uploaded_by = Auth::user()->user_id;
            $log->save();
            Log::channel('return-transactions-from-pdf')->info("Importing return transactions from PDF process finished.");
            $message = "Total " . $data_imported . " account(s) imported successfully.";
            return renderResponse(SUCCESS, $message, null);
        } else {
            Log::channel('return-transactions-from-pdf')->error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No pages found in the PDF to proceed further.");
        }
    }

    /**
     * getUnknownReturnReasonList
     * Listing for Unknown Return Reasons received from Acheck21
     * @param  mixed $request
     * @return void
     */
    /**
     * Fetch all manual identity verification details
     */
    public function getUnknownReturnReasonList(Request $request)
    {
        // Columns defined for Sorting
        $columns = array(
            0 => 'unknown_return_reasons.return_code',
            1 => 'unknown_return_reasons.created_at',
        );
        // Main Query
        $query = UnknownReturnReason::on(MYSQL_RO)->leftJoin('return_reason_masters', 'return_reason_masters.reason_code', '=', 'unknown_return_reasons.return_code')->select('unknown_return_reasons.*')->whereNull('return_reason_masters.id')->groupBy('unknown_return_reasons.return_code');

        //Count Query
        $queryCount = UnknownReturnReason::on(MYSQL_RO)->leftJoin('return_reason_masters', 'return_reason_masters.reason_code', '=', 'unknown_return_reasons.return_code')
            ->selectRaw('COUNT(*) as total_count')->whereNull('return_reason_masters.id');

        $totalData = $queryCount->groupBy('unknown_return_reasons.return_code')->count(); // Getting total no of rows

        $totalFiltered = $totalData;
        $limit = intval($this->request->input('length'));
        $start = intval($this->request->input('start'));
        $order = $columns[$this->request->input('order.0.column')];
        $dir = $this->request->input('order.0.dir');
        if (empty($this->request->input('search.value')) && empty($order) && empty($dir)) {
            $unknown_return_reasons = $query->offset($start)->limit(intval($limit))->orderBy('unknown_return_reasons.return_code', 'ASC')->get();
        } else if (empty($this->request->input('search.value'))) {
            $unknown_return_reasons = $query->offset($start)->limit(intval($limit))->orderBy($order, $dir)->get();
        } else {

            $search = $this->request->input('search.value');
            $search_query = $query->where(function ($q) use ($search) {

                $q->where('unknown_return_reasons.id', 'LIKE', "%{$search}%")->orWhere('unknown_return_reasons.return_code', 'LIKE', "%{$search}%");
            });

            $search_query_count = $queryCount->where(function ($q) use ($search) {
                $q->where('unknown_return_reasons.id', 'LIKE', "%{$search}%")->orWhere('unknown_return_reasons.return_code', 'LIKE', "%{$search}%");
            });

            $totalFiltered = $search_query_count->first()->total_count;

            $unknown_return_reasons = $search_query->offset($start)->limit(intval($limit))->orderBy($order, $dir)->get();
        }

        $data = array();
        if (!empty($unknown_return_reasons)) {
            // Creating array to show the values in frontend
            foreach ($unknown_return_reasons as $unknown_return_reason) {
                $nestedData['return_code'] = $unknown_return_reason->return_code;
                $nestedData['canpay_represent'] = 1;
                $nestedData['new_banking'] = 1;
                $nestedData['new_banking_after_second_attempt'] = 1;
                $nestedData['bank_login'] = 1;
                $nestedData['bank_login_after_second_attempt'] = 1;
                $nestedData['edit'] = $unknown_return_reason->id;
                $nestedData['created_at'] = date('m-d-Y h:i A', strtotime($unknown_return_reason->created_at));
                $data[] = $nestedData;
            }
        }
        // Drawing the Datatable
        $json_data = array(
            "draw" => intval($request->input('draw')),
            "recordsTotal" => intval($totalData),
            "recordsFiltered" => intval($totalFiltered),
            "data" => $data,
        );

        Log::info(addslashes(__METHOD__) . "(" . LINE . __LINE__ . ") : Unknown Return Reasons fetched successfully");
        echo json_encode($json_data); // Rerurning the data
    }

    /**
     * addReturnReason
     * Function to add the unknown retrun code to the original return reason master table
     * @param  mixed $request
     * @return void
     */
    public function addReturnReason(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Saving process for unknown return reason started...");
        // Validating input requests
        $this->validate($request, [
            'reason_code' => VALIDATION_REQUIRED,
            'title' => VALIDATION_REQUIRED,
            'new_title' => VALIDATION_REQUIRED,
            'description' => VALIDATION_REQUIRED,
            'time_frame' => VALIDATION_REQUIRED,
            'canpay_represent' => VALIDATION_REQUIRED,
            'new_banking' => VALIDATION_REQUIRED,
            'new_banking_after_second_attempt' => VALIDATION_REQUIRED,
            'bank_login' => VALIDATION_REQUIRED,
            'bank_login_after_second_attempt' => VALIDATION_REQUIRED,
        ]);
        DB::beginTransaction();
        try {
            // Saving in the master table
            $return_reason_master = new ReturnReasonMaster();
            $return_reason_master->reason_code = $request->get('reason_code');
            $return_reason_master->title = $request->get('title');
            $return_reason_master->description = $request->get('description');
            $return_reason_master->new_title = $request->get('new_title');
            $return_reason_master->time_frame = $request->get('time_frame');
            $return_reason_master->canpay_represent = $request->get('canpay_represent');
            $return_reason_master->new_banking = $request->get('new_banking');
            $return_reason_master->new_banking_after_second_attempt = $request->get('new_banking_after_second_attempt');
            $return_reason_master->bank_login = $request->get('bank_login');
            $return_reason_master->bank_login_after_second_attempt = $request->get('bank_login_after_second_attempt');
            $return_reason_master->save();
            Log::debug(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Unknown return reason saved successfully in master table.");

            // Updating the return reason id of the transactions
            DB::statement("UPDATE transaction_details td JOIN unknown_return_reasons urr ON td.id = urr.transaction_id SET return_reason = '" . $return_reason_master->id . "' WHERE urr.return_code = '" . $request->get('reason_code') . "'");
            Log::debug(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction return reason Updated with the newly inserted id of the return reason.");

            // Create the Email Template for the newly created return reason
            $email_template = new EmailTemplate();
            $email_template->template_name = 'Return Transaction ' . $return_reason_master->reason_code . " (" . $return_reason_master->title . ")";
            $email_template->template_subject = 'Consumer Transaction Return For ' . $return_reason_master->title;
            $email_template->template_body = '<table role="presentation" class="main"><tr><td class="wrapper"><table role="presentation" border="0" cellpadding="0" cellspacing="0"><tr><td><p>Hi {{$user_details->first_name." ".$user_details->middle_name." ".$user_details->last_name}},</p><p> <b>Your transaction is returned due to {{$return_reason}}.</b></p><b>What you need to do : </b> Explanation<p></p><p>Below mentioned is your return transaction details.</p><p><table role="presentation" cellpadding="0" cellspacing="0" style="border-collapse: collapse; width: 100%;"><thead><tr><th style="padding: 8px;">Transaction No.</th><th style="padding: 8px;">Store</th><th style="padding: 8px; vertical-align: top; text-align: center;">Amount($)</th><th style="padding: 8px; text-align: right; vertical-align: top;">Transaction Time</th></tr></thead><tbody><tr style="background-color:#eee;"><td style="padding: 8px;">{{$transaction_details->transaction_number}}</td><td style="padding: 8px;">{{$transaction_details->retailer}}</td><td style="padding: 8px;text-align: center;">{{$transaction_details->amount}}</td><td style="padding: 8px; text-align: right;">{{$transaction_details->local_transaction_time}}</td></tr></tbody></table></p><p></p><p>Best Regards,</p><p>CanPay Team</p></td></tr></table></td></tr></table>';
            $email_template->return_reason_id = $return_reason_master->id;
            $email_template->save();
            DB::commit();
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Unknown return reason saved successfully in master table and Email Template added.");
            $message = trans('message.return_reason_add_suceess');
            return renderResponse(SUCCESS, $message, null);
        } catch (\Exception $e) {

            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while saving unknown reason code in master table.", [EXCEPTION => $e]);
            DB::rollback();
            $message = trans('message.db_transaction_failed');
            return renderResponse(FAIL, $message, $e);
        }
    }
}
