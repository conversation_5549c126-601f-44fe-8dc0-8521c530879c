<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
class ReturnManualPostLog extends Model
{
    /**
     * __construct
     * This function will insert an alphanumeric 32 character unique id as primary key in the table whenever a data get inserted
     * @param  mixed $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        $this->id = generateUUID();

        parent::__construct($attributes);
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'primary_identifier_name',
        'primary_identifier_value',
        'message',
        'row_data',
        'return_posted',
        'imported_by',
        'batch_id'
    ];
    public $timestamps = true;
    public $incrementing = false;
}
