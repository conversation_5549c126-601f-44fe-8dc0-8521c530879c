const cancelTransaction = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/cancelTransaction', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const revokeVoidTransaction = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/revokeVoidTransaction', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getConsumers = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/getAllConsumers', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const generateConsumerTransactionReport = (request) => {
    return new Promise((res, rej) => {
        axios.post('/api/getconsumertransactionreport', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const exportgenerateConsumerTransactionReport = (request) => {
    var header = {
        responseType: 'blob',
    };
    return new Promise((res, rej) => {
        axios.post('/api/export/getconsumertransactionexport', request, header)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getAllTransactions = (request) => {
    return new Promise((res, rej) => {
        axios.post('/api/alltransactions', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getPendingTransactions = (request) => {
    return new Promise((res, rej) => {
        axios.post('/api/allpendingtransactions', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const updateTransactionStatus = (request) => {
    return new Promise((res, rej) => {
        axios.post('/api/updatetransactionstatus', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const runMerchantScheduler = (request) => {
    return new Promise((res, rej) => {
        axios.post('/api/runmerchantscheduler', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getalltransactionexport = (request) => {
    var header = {
        responseType: 'blob',
    };
    return new Promise((res, rej) => {
        axios.post('/api/export/exportalltransactions', request, header)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const generateTransactionReport = (request) => {
    return new Promise((res, rej) => {
        axios.post('/api/generatetransactionreport', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const getTransactionDetails = (request) => {
    return new Promise((res, rej) => {
        axios.post('/api/dashboard/transactiondetails', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const getreturntransactionexport = (request) => {
    var header = {
        responseType: 'blob',
    };
    return new Promise((res, rej) => {
        axios.post('/api/export/exportreturntransactions', request, header)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getreturntransactiongroupexport = (request) => {
    var header = {
        responseType: 'blob',
    };
    return new Promise((res, rej) => {
        axios.post('/api/export/exportreturntransactionsgroup', request, header)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const generateReturnTransactionReport = (request) => {
    return new Promise((res, rej) => {
        axios.post('/api/getreturntransactionreport', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const generateReturnTransactionGroupReport = (request) => {
    return new Promise((res, rej) => {
        axios.post('/api/getreturntransactiongroupreport', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getReturnStatus = () => {
    return new Promise((res, rej) => {
        axios.get('/api/getreturnstatus')
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getReturnReasons = () => {
    return new Promise((res, rej) => {
        axios.get('/api/getreturnreasons')
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const waiveTransaction = (request) => {
    return new Promise((res, rej) => {
        axios.post('/api/waivetransaction', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getReturnTransactionDetails = (request) => {
    return new Promise((res, rej) => {
        axios.post('/api/genarteReturnTransactionDetails', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getStores = (request) => {
    return new Promise((res, rej) => {
        axios.post('/api/getauthorizedstores', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getUsersPpLists = () => {
    return new Promise((res, rej) => {
        axios.post('api/getUsersPpLists')
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getUsersPurchase = () => {
    return new Promise((res, rej) => {
        axios.post('api/getUsersPurchase')
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const exportUsersPpLists = () => {
    var header = {
        responseType: 'blob',
    };
    return new Promise((res, rej) => {
        axios.post('/api/export/exportUsersPpLists', header)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const exportUsersPurchase = () => {
    var header = {
        responseType: 'blob',
    };
    return new Promise((res, rej) => {
        axios.post('/api/export/exportUsersPurchase', header)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getReturnReasonList = () => {
    return new Promise((res, rej) => {
        axios.post('/api/getreturnreasonlist')
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const sendMailBasedOnReturn = (request) => {
    return new Promise((res, rej) => {
        axios.post('/api/sendmailbasedonreturn', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getPurchaseDetails = (request) => {
    return new Promise((res, rej) => {
        axios.post('/api/generatefraudreport', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getConsumerTransactionHistory = (request) => {
    return new Promise((res, rej) => {
        axios.post('/api/getconsumertransactionhistory', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const activateUser = (request) => {
    return new Promise((res, rej) => {
        axios.post('/api/makeuserinactive', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const exportReturnList = (request) => {
    var header = {
        responseType: 'blob',
    };
    return new Promise((res, rej) => {
        axios.post('/api/export/returnlist', request, header)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getManualvsDirectData = () => {
    return new Promise((res, rej) => {
        axios.get('/api/dashboard/getmanualvsdirectlinkeddata')
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const generateVoidTransactionReport = (request) => {
    return new Promise((res, rej) => {
        axios.post('/api/getvoidtransactionreport', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const exportgenerateVoidTransactionReport = (request) => {
    var header = {
        responseType: 'blob',
    };
    return new Promise((res, rej) => {
        axios.post('/api/export/getvoidtransactionexport', request, header)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const addTransactionModificationReason = (request) => {
    return new Promise((res, rej) => {
        axios.post('/api/addtransactionmodificationreason', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const editTransactionModReason = (request) => {
    return new Promise((res, rej) => {
        axios.post('/api/edittransactionmodification', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const deletetTransactionModReason = (request) => {
    return new Promise((res, rej) => {
        axios.post('/api/deletetransactionmodificationreason', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const searchTransactionModReason = (request) => {
    return new Promise((res, rej) => {
        axios.post('/api/searchtransactionmodificationreason', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const deletetTransactionModCustomReason = (request) => {
    return new Promise((res, rej) => {
        axios.post('/api/deletetransactionmodificationcustomreason', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const searchTransactionModCustomReason = (request) => {
    return new Promise((res, rej) => {
        axios.post('/api/searchtransactionmodificationcustomreason', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const modifiedTransactionHistory = (request) => {
    return new Promise((res, rej) => {
        axios.post('/api/modifiedtransactionhistory', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const getEnrollmentReport = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/getusersenrollmentreport', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const exportEnrollmentReport = (request) => {
    var header = {
        responseType: 'blob',
    };
    return new Promise((res, rej) => {
        axios.post('/api/export/exportusersenrollmentreport', request, header)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const saveUserReviewComment = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/saveuserreviewcomment', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const getUserReviewData = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/getuserreviewdata', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
const returnRepayment = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/makereturnrepayment', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};
export default {
    cancelTransaction,
    getConsumers,
    generateConsumerTransactionReport,
    exportgenerateConsumerTransactionReport,
    getAllTransactions,
    getPendingTransactions,
    updateTransactionStatus,
    runMerchantScheduler,
    getalltransactionexport,
    generateTransactionReport,
    getTransactionDetails,
    getreturntransactionexport,
    getreturntransactiongroupexport,
    generateReturnTransactionReport,
    generateReturnTransactionGroupReport,
    getReturnStatus,
    getReturnReasons,
    waiveTransaction,
    getReturnTransactionDetails,
    getStores,
    getUsersPpLists,
    getUsersPurchase,
    exportUsersPpLists,
    exportUsersPurchase,
    getReturnReasonList,
    sendMailBasedOnReturn,
    getPurchaseDetails,
    getConsumerTransactionHistory,
    activateUser,
    exportReturnList,
    getManualvsDirectData,
    generateVoidTransactionReport,
    exportgenerateVoidTransactionReport,
    revokeVoidTransaction,
    addTransactionModificationReason,
    editTransactionModReason,
    deletetTransactionModReason,
    searchTransactionModReason,
    searchTransactionModCustomReason,
    deletetTransactionModCustomReason,
    modifiedTransactionHistory,
    getEnrollmentReport,
    exportEnrollmentReport,
    saveUserReviewComment,
    getUserReviewData,
    returnRepayment
};
