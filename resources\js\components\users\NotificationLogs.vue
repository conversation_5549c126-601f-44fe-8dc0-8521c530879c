<template>
<div>
  <div v-if="loading">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Notifications</h3>
                </div>

                <!-- /.card-header -->
                <div class="card-body">
                  <div class="row">
                  <div class="col-md-4">
                    <div class="form-group">
                      <select name="type" id="type" class="form-control" v-model="type" @change="validateType">
                        <option value="email">Email</option>
                        <option value="sms">SMS</option>
                      </select>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="form-control"
                        placeholder="Phone No (Exact)"
                        :disabled="phone_disabled"
                        id="phone_no"
                        v-model="phone_no"
                      />
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                        class="form-control"
                        placeholder="Email (Exact)"
                        :disabled="email_disabled"
                        id="email"
                        v-model="email"
                      />
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-group">
                      <input
                          autocomplete="off"
                          class="start-date form-control"
                          placeholder="Date"
                          id="start-date"
                          onkeydown="return false"
                        />
                    </div>
                  </div>
                </div>
                </div>
                  <div class="card-footer">
                    <button
                      type="button"
                      class="btn btn-success"
                      @click="notificationLogs()"
                    >
                      Search
                    </button>
                    <button
                      type="button"
                      @click="reset()"
                      class="btn btn-success margin-left-5"
                    >
                      Reset
                    </button>
                  </div>
                  <div class="card-body">
                  <b-table-simple
                      responsive
                      show-empty
                      bordered
                      sticky-header="800px"
                      v-if="allUserModel.length > 0 && allUserModel[0].edit!=null"
                    >
                      <b-thead head-variant="light">
                        <b-tr>
                          <b-th class="text-left">Recipient's Name</b-th>
                          <b-th class="text-left">Notification Event</b-th>
                          <b-th class="text-left" v-if="type == 'email'">To Email</b-th>
                          <b-th class="text-center" v-if="type == 'sms'">User Phone</b-th>
                          <b-th class="text-center" v-if="type == 'email'">Email Subject</b-th>
                          <b-th class="text-center">Notification Body</b-th>
                          <b-th class="text-center">Notification Time</b-th>
                          <b-th class="text-center">Sent From</b-th>
                          <b-th class="text-center">Resend Count</b-th>
                          <b-th class="text-center">Action</b-th>
                        </b-tr>
                      </b-thead>
                      <b-tbody v-for="(row, index) in allUserModel" :key="index">
                        <b-tr>
                          <b-td class="text-left text-gray">{{
                            row.consumer_name
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.notification_event
                          }}</b-td>
                          <b-td class="text-left text-gray" v-if="type == 'email'">{{
                            row.to_email
                          }}</b-td>
                          <b-td class="text-center text-gray" v-if="type == 'sms'">{{
                            row.to_phone
                          }}</b-td>
                          <b-td class="text-center text-gray" v-if="type == 'email'">{{
                            row.email_subject
                          }}</b-td>
                          <b-td class="text-center text-gray">
                            <a :data-notification-id="row.edit" :data-notification-type="row.type" class="viewNotificationBody custom-edit-btn" title="View Notification Body" variant="outline-success"><i class="nav-icon fas fa-eye"></i></a>
                        </b-td>
                          <b-td class="text-center text-gray">{{
                            row.notification_time
                          }}</b-td>
                          <b-td class="text-center text-gray">{{
                            row.sent_from
                          }}</b-td>
                          <b-td class="text-center text-gray">{{
                            row.resend_count
                          }}
                            </b-td>
                          <b-td class="text-center text-gray">
                            <a :data-notification-id="row.edit" :data-notification-type="row.type" class="resendNotification custom-edit-btn" title="Resend Mail" variant="outline-success" style="border:none" v-if="row.type == 'email' && row.resend == 1 && row.is_cooldown == 0"><i class="nav-icon fas fa-paper-plane"></i></a>
                            <a :data-notification-id="row.edit" :data-notification-type="row.type" class="resendNotification custom-edit-btn" title="Resend SMS" variant="outline-success" style="border:none" v-else-if="row.resend == 1 && row.is_cooldown == 0"><i class="nav-icon fas fa-paper-plane"></i></a>
                            <span v-if="row.is_cooldown == 1">
                            <vue-countdown-timer
                            @end_callback="endCallBack(index)"
                            :start-time="row.timer_start_time"
                            :end-time="row.timer_end_time"
                            :interval="1000"
                            :end-label="'Resend in'"
                            label-position="begin"
                            :end-text="'Event ended!'"
                            :day-txt=false
                            :hour-txt=false
                            :minutes-txt="'minutes'"
                            :seconds-txt="'seconds'">
                            </vue-countdown-timer></span>
                          </b-td>
                        </b-tr>
                      </b-tbody>
                    </b-table-simple>
                    <p v-else>No data displayed. Please refine your search criteria.</p>
                    </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
    <b-modal
      id="notification-body-modal"
      ref="modal"
      :header-text-variant="headerTextVariant"
      title="Notification Body"
      @hidden="resetModal"
      ok-only
      ok-title="Close"
      ok-variant="success"
      :no-close-on-esc="true"
      :no-close-on-backdrop="true"
    >
        <div class="row" style="word-break:break-all;">
            <div class="col-md-12">
                <div style="width: 100%; padding: 0 3%; margin-bottom: 2px;" v-html="notificationBody"></div>
            </div>
        </div>
    </b-modal>
</div>
</div>
</template>
<script>
import moment from "moment";
import api from "@/api/notificationlog.js";
import { validationMixin } from "vuelidate";
import { required, minLength,decimal } from "vuelidate/lib/validators";
import commonConstants from "@/common/constant.js";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "../CustomLoader/CanPayLoader.vue"
export default {
  mixins: [validationMixin],
  data() {
    return {
      allUserModel: {},
      consumer:"",
      phone_no:"",
      email:"",
      type:"email",
      date:"",
      notification_id:"",
      notificationID: "",
      notificationModel: {},
      notificationDetails: "",
      notificationBody:"",
      headerTextVariant: "light",
      loading:false,
      phone_disabled:true,
      email_disabled:false
    };
  },
  components: {
    HourGlass,
    CanPayLoader
  },
  created() {
    var self = this;
    self.resendNotification();
    self.viewNotificationBody();
  },
  methods: {
    reset(){
      var self = this;
      self.consumer = "";
      self.phone_no = "";
      self.email = "";
      self.type = "email";
    },
    viewNotificationBody() {
      var self = this;
      $(document).on("click", ".viewNotificationBody", function(e) {
        var notificationDetails = self.allUserModel.find(
          p => p.edit == $(e.currentTarget).attr("data-notification-id")
        );
        self.notificationModel = notificationDetails;
        if($(e.currentTarget).attr("data-notification-type") === 'email'){
            self.notificationBody = notificationDetails.email_body;
        }else{
            self.notificationBody = notificationDetails.sms_body;
        }
        self.$bvModal.show("notification-body-modal");
      });
    },
    notificationLogs(){
      var self = this;
      if(self.type == 'email'){
        var validation_text = 'Email';
      }else{
        var validation_text = 'Phone No.';
      }
      if($("#phone_no").val().trim() === '' &&  $("#email").val().trim() === ''){
        error("Please provide "+validation_text);
        return false;
      }
      if($("#start-date").val()!=''){
        var date = moment($("#start-date").val()).format("YYYY-MM-DD");
      }else{
        var date = '';
      }
      var request = {
        consumer: self.consumer,
        email:self.email,
        phone_no:self.phone_no,
        type:self.type,
        date:date
      };
      self.loading = true;
      api
      .notificationLogs(request)
      .then(function (response) {
        if (response.code == 200) {
          self.allUserModel = response.data;
          self.loading = false;
        } else {
          error(response.message);
          self.loading = false;
        }
      })
      .catch(function (error) {
        // error(error);
        self.loading = false;
      });
    },
    resendNotification() {
      var self = this;
      $(document).on("click", ".resendNotification", function(e) {
        let request = {
            'notification_id': $(e.currentTarget).attr("data-notification-id"),
            'type': $(e.currentTarget).attr("data-notification-type"),
            'resend': 1
        };
        Vue.swal({
            title: "Are you sure to re-send this notification?",
            text: "You will not be able to undo this action!",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#149240",
            confirmButtonText: "Yes, send it!",
            cancelButtonText: "No, cancel it!",
            closeOnConfirm: true,
            closeOnCancel: true,
            }).then((result) => {
                if (result.isConfirmed) {
                    api
                    .resendNotification(request)
                    .then(response => {
                        if ((response.code == 200)) {
                            Vue.swal("Sent!", response.message, "success");
                            self.notificationLogs();
                        } else {
                            Vue.swal(response.message, '', 'error')
                        }
                    })
                    .catch(err => {
                        Vue.swal(err.response.data.message, '', 'error')
                    });
                }
            })
      });
    },
    resetModal() {
      var self = this;
      self.notificationModel = {};
      self.notificationDetails = null;
      self.notificationBody = null;
    },
    endCallBack: function(index) {
        var self = this;
        console.log(self.allUserModel[index]);
        self.allUserModel[index].is_cooldown = 0;
        console.log(self.allUserModel[index]);
    },
    validateType(){
        var self = this;
        if(self.type == 'email'){
            self.phone_disabled = true;
            self.email_disabled = false;
            self.phone_no = "";
        }else{
            self.phone_disabled = false;
            self.email_disabled = true;
            self.email = "";
        }
    }
  },
  mounted() {
    var self = this;
    $("#start-date").datepicker({
      format: "mm/dd/yyyy",
      autoclose: true,
      todayHighlight: true,
    });
    $("#start-date").datepicker("setDate", new Date());
    document.title = "CanPay - Notifications";
  },
};
</script>
