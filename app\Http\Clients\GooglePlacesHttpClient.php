<?php
namespace App\Http\Clients;

use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;

/**
 *
 * @package App\Http\Clients
 */
class GooglePlacesHttpClient
{
    /**
     * @var Client
     */
    private $client;
    /**
     * GooglePlacesHttpClient constructor.
     */
    public function __construct()
    {
        $this->client = new Client(['base_uri' => 'https://maps.googleapis.com/maps/api/']);
    }

    public function getLatLong($address)
    {
        try {
            $params['api'] = 'geocode/json?address=' . $address . '&key=' . config('app.google_places_api_key');
            $response = $this->client->get($params['api']);
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Google Places with header: " . $params['api'] . " returned response: " . $response->getBody());
            return $response->getBody();
        } catch (\GuzzleHttp\Exception\ClientException $ex) {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while sending request to Google Places with Store Address.", [EXCEPTION => $ex]);
            return $ex->getResponse()->getBody()->getContents();
        }
    }

    public function getTimezone($lat, $long)
    {
        try {
            $params['api'] = 'timezone/json?location=' . $lat . ',' . $long . '&timestamp=' . time() . '&key=' . config('app.google_places_api_key');
            $response = $this->client->get($params['api']);
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Google Places with header: " . $params['api'] . " returned response: " . $response->getBody());
            return $response->getBody();
        } catch (\GuzzleHttp\Exception\ClientException $ex) {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while sending request to Google Places with Store Lat Long.", [EXCEPTION => $ex]);
            return $ex->getResponse()->getBody()->getContents();
        }
    }
}
