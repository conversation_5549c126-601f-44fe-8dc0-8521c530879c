<?php

// PostController.php

namespace App\Http\Controllers;

use App\Http\Clients\ApiHttpClient;
use App\Http\Factories\EmailExecutor\EmailExecutorFactory;
use App\Models\ConsumerDocumentUpload;
use App\Models\StatusMaster;
use App\Models\UploadedFile;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class ConsumerDocumentUploadController extends Controller
{

    public function __construct(Request $request)
    {
        $this->request = $request;
        $this->emailexecutor = new EmailExecutorFactory();
        $this->api = new ApiHttpClient();
    }

    /**
     * Fetch all v1 manual identity verification details
     */
    public function getAllConsumerUploadDocument(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer Upload document search started...");
        // Validating input request
        $this->validate($request, [
            'type' => VALIDATION_REQUIRED,
        ]);

        // Search with in Manual Identity Review
        $manualReviews = $this->_getConsumerUploadDocuments($request);

        $message = trans('message.manual_review_search_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer Upload document search complete.");
        return renderResponse(SUCCESS, $message, $manualReviews);
    }

    /**
     * _getConsumerUploadDocuments
     * Fetch the consumer Upload documents
     * @param  mixed $searchArray
     * @return void
     */
    private function _getConsumerUploadDocuments($request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer Upload document Search Started.");

        $type = $request['type'];
        $status = StatusMaster::where('code', $type)->first();

        // Fetch the Consumer Document Upload
        $sql = 'SELECT consumer_document_upload.*,status_master.status as status_name,users.*,consumer_document_upload.created_at as cdu_created_at FROM consumer_document_upload LEFT JOIN status_master ON consumer_document_upload.status = status_master.id LEFT JOIN users ON users.user_id = consumer_document_upload.user_id WHERE consumer_document_upload.status = ?  ';

        $searchStr = [$status->id];
        if (strlen(trim($request['consumer'])) >= 3) {
            $consumer = $request['consumer'];
            $sql .= ' AND LOWER(
                REPLACE(CONCAT(COALESCE(
                REPLACE(users.first_name, " ",""), "")," ", COALESCE(
                REPLACE(users.middle_name, " ",""), "")," ", COALESCE(
                REPLACE(users.last_name, " ",""), "")),"  "," ")) LIKE ? ';
            array_push($searchStr, '%' . $consumer . '%');
        }
        if (trim($request['phone_no'])) {
            $sql .= " AND users.phone = " . "'" . $request['phone_no'] . "'";
        }
        if (trim($request['email'])) {
            $sql .= " AND users.email = ? ";
            array_push($searchStr, $request['email']);
        }
        $sql .= " ORDER BY consumer_document_upload.created_at DESC LIMIT 100";

        $consumers = DB::connection(MYSQL_RO)->Select($sql, $searchStr);

        Log::info($consumers);

        $consumerArr = [];
        if (!empty($consumers)) {
            foreach ($consumers as $consumer) {

                $data = [];
                $data['name'] = $consumer->first_name . ' ' . $consumer->middle_name . ' ' . $consumer->last_name;
                $data['edit'] = $consumer->id;
                $data['email'] = $consumer->email;
                $data['first_name'] = $consumer->first_name;
                $data['middle_name'] = $consumer->middle_name;
                $data['last_name'] = $consumer->last_name;
                $data['phone'] = $consumer->phone;
                $data['created_at'] = date('m-d-Y h:i A', strtotime($consumer->cdu_created_at));
                $data['status'] = $consumer->status_name;

                array_push($consumerArr, $data);
            }
        } else {
            $consumerArr = [];
        }

        return $consumerArr;
    }

    public function getConsumerDocumentDetails()
    {
        $data = $this->request->all();
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Getting  Consumer document for  review details with id: " . $data['id']);
        $details = ConsumerDocumentUpload::leftJoin('status_master', 'consumer_document_upload.status', '=', 'status_master.id')->leftJoin('users', 'consumer_document_upload.user_id', '=', 'users.user_id')->select('consumer_document_upload.*', 'status_master.status as status_name', 'status_master.code as status_code', 'users.*')->where('consumer_document_upload.id', $data['id'])->first();
        if ($details) {
            // get all uploaded files using referance id
            $uploaded_files = UploadedFile::where('reference_id', $data['id'])->get();
            $files = [];
            if (!empty($uploaded_files)) {
                $disk = Storage::disk('s3');
                foreach ($uploaded_files as $uploaded_file) {
                    // Praparing a temporary URL to fetch the image from S3. We are using temporary url so that the url gets invalid after defined minutes, so that the user data got protected.
                    $file_temp_url = $disk->temporaryUrl($uploaded_file->document, Carbon::now()->addMinutes(intval(config('app.s3_file_expiry_time'))), []);
                    array_push($files, $file_temp_url);
                }
            }
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Successfully fetched consumer upload document");
            $response['details'] = $details;
            $response['name'] = $details->first_name . " " . $details->middle_name . " " . $details->last_name;
            $response['documents'] = $files;
            $response['record_id'] = $details->id;
            $response['address'] = $details->street_address . " " . $details->city . " " . $details->state . " " . $details->zipcode;
            $message = trans('message.identity_details_success');
            return renderResponse(SUCCESS, $message, $response);
        } else {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Identity document not found.");
            $message = trans('message.identity_details_not_found');
            return renderResponse(FAIL, $message, null);
        }
    }

    public function updateConsumerUploadDocumentStatus()
    {
        $data = $this->request->all();

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Updating status of record with id: " . $data['id'] . " as reviewed");
        DB::beginTransaction();
        try {
            $details = ConsumerDocumentUpload::where('id', $data['id'])->first();
            $status = StatusMaster::where('code', $data['code'])->first();
            $details->status = $status->id;
            $details->comment = $data['comment'];
            $details->status_updated_by = $data['status_updated_by'];
            $details->save();
            if ($data['code'] == RETAKE) {
                $users = User::where('user_id', $details->user_id)->first();
                $users->required_upload_document = 1;
                if ($users && $users->manual_bank_microbilt_error) {
                    $users->microbilt_upload_document_show = 1;
                }
                $users->save();
                $transaction_details_params = [
                    'user_id' => $users->user_id,
                    'comment' => $data['comment'],
                    'email' => $users->email,
                ];

                $this->emailexecutor->consumerRetakeUploadDocumentMail($transaction_details_params);

            } elseif($data['code'] == ARCHIVED) {
                $users = User::where('user_id', $details->user_id)->first();
                if ($users && $users->manual_bank_microbilt_error && isset($data['daily_spending_limit'])  && isset($data['consumer_weekly_spending_limit'])) {
                    $users->standard_daily_limit = $data['daily_spending_limit'];
                    $users->purchase_power = $data['daily_spending_limit'];
                    $users->weekly_spending_limit = $data['consumer_weekly_spending_limit'];
                    $users->save();
                }
            }
            DB::commit();
            $params = DB::table('consumer_document_upload')->where('id', $data['id'])->first();
            $message = trans('message.mark_record_reviewed') . $status->status;
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") -" . $message);
            return renderResponse(SUCCESS, $message, null);
        } catch (\Exception $e) {

            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while consumer upload updating  details.", [EXCEPTION => $e]);
            DB::rollback();
            $message = trans('message.db_transaction_failed');
            return renderResponse(FAIL, $message, $e);
        }
    }
}
