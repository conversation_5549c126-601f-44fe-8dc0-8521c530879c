<?php

namespace App\Exports;

use DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Events\AfterSheet;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;

class StorewiseMonthlySalesExport implements FromCollection, WithHeadings, ShouldAutoSize, WithEvents, WithColumnFormatting
{
    protected $request;

    public function __construct($request)
    {
        $this->request = $request; // Declaring the request variable
    }

    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        $collection_array = $this->request->get('report'); // Storing the array received from request

        $results = array();
        foreach($collection_array as $result){
            $nestedData['store_id'] = $result['store_id'];
            $nestedData['retailer'] = $result['retailer'];
            $nestedData['routing_no'] = $result['routing_no'];
            $nestedData['transaction_count'] = $result['transaction_count'];
            $nestedData['sales'] = $result['sales'];

            array_push($results,$nestedData);
        }

        return collect([
            $results,
        ]);
    }

    public function headings(): array
    {
        $returnArray = array(
            // 1st header
            [
                'Storewise Monthly Sales Report',
            ],
            [
                'Report Date:',
                date('m/d/Y', strtotime($this->request->get('from_date'))) . ' - ' . date('m/d/Y', strtotime($this->request->get('to_date'))),
            ],
            [
                'Store ID',
                'Retailer',
                'Routing Number',
                'Transaction Count',
                'Sales'
            ],
        );

        return $returnArray;
    }

    public function columnFormats(): array
    {
        return [
            'E' => '0.00',
        ];
    }


    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event){
                $event->sheet->getStyle('A1:E1')->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);
                $event->sheet->getStyle('A2:E2')->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);
                $event->sheet->getStyle('A3:E3')->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);

                //Apply Center Alignment
                $event->sheet->getStyle('A:E')->getAlignment()->applyFromArray(
                    array('horizontal' => 'center')
                );
            },
        ];
    }
}
