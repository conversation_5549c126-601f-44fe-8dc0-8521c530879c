<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
class MigrationLog extends Model
{

    protected $table = 'migration_log';

    /**
     * __construct
     * This function will insert an alphanumeric 32 character unique id as primary key in the table whenever a data get inserted
     * @param  mixed $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        $this->id = generateUUID();

        parent::__construct($attributes);
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        "type",
        "sheet_name",
        "actual_data_imported",
        "duplicate_date_imported",
        "migrated_data_updated",
        "uploaded_by",
        "batch_id",
    ];
    public $timestamps = true;
    public $incrementing = false;
}
