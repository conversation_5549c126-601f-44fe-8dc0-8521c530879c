<template>
  <div :style="wheelPageStyle">
    <div class="wheel-preview-header" :style="headerStyle">
      <p class="wheel-preview-title" v-if="wheelData.wheel_options.header.header_type=='text'" :style="'font-size:' + (parseInt(wheelData.wheel_options.header.page_title_size) - 6) + 'px!important'">{{wheelData.wheel_options.header.page_title}}</p>
      <div style="width:44%;height:50px;margin-top: -6px;margin-right: 14px;" v-if="wheelData.wheel_options.header.header_type=='image'">
        <img style="object-fit:contain;object-position: center;width:100%;height:100%;" :src="wheelData.wheel_options.header.header_title_image" alt="Merchant Logo">
      </div>
      <div class="wheel-preview-close">
        <slot name="closeModal"></slot>
      </div>
    </div>
    <div :style="'position:absolute;text-align:center;top: 45px;width: 100%;color:'+wheelData.wheel_options.header.header_sub_title_color+';font-size:'+(wheelData.wheel_options.header.header_sub_title_size-2)+'px;'" v-if="wheelData.wheel_options.header.header_sub_title">
      <p>{{wheelData.wheel_options.header.header_sub_title}}</p>
    </div>
    <Jackpot
    class="my-3"
    style="position: relative; z-index: 2;"
    :jackpot-value="1000"
    :per-sec-value="'0.37'"
    />

    <div class="sparkle-container pt-3">
      <button 
      :style="rwPointButtonStyle"
      class="rw-timmer-chip">
          <svg width="30px" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
          viewBox="20 20 530 530" style="enable-background:new 0 0 570.4 570.4;" xml:space="preserve">
      
          <g>
          <path d="M285.4,286c-12.9,12.8-25.7,25.6-38.6,38.4c-45.7,45.7-91.5,91.4-137.2,137.2c-1.5,1.5-3.1,2.9-5.3,4.9
          c-6.9-8-13.9-15.4-20.1-23.4c-31.9-40.7-49.9-86.8-54-138.4c-0.2-1.6-0.5-3.2-0.9-4.8v-15c85.4,0,170.7,0,256.1-0.1L285.4,286z"/>
          <path d="M541.4,284.9c-85,0-170.1,0-255.1-0.1c-0.3,0-0.7,0.1-1,0.1c1.3-1.5,2.5-3.1,3.9-4.5c57.5-57.5,115-115,172.5-172.5
          c1.3-1.3,2.6-2.5,4.6-4.3c5.8,6.6,11.7,12.6,17,19.2c31.7,39,50.6,83.4,56.2,133.4c0.6,5.3,1.3,10.5,1.9,15.7L541.4,284.9z"/>
          <path class="st1" d="M466.1,103.3C406,163.4,345.8,223.5,285.7,283.6c-0.2,0.2-0.4,0.5-0.6,0.8c-0.2-2-0.5-4-0.5-6
          c0-81.3,0-162.6,0-243.9c0-1.8,0.1-3.6,0.2-6.3c8.8,0.5,17.2,0.7,25.6,1.5c50,5.1,94.7,23.2,134,54.6c4.1,3.3,8.3,6.5,12.5,9.8
          L466.1,103.3z"/>
          <path class="st2" d="M103.8,103.3c60.1,60.1,120.2,120.3,180.3,180.4c0.2,0.2,0.5,0.4,0.8,0.6c-2,0.2-4,0.5-6,0.5
          c-81.3,0-162.6,0-243.9,0c-1.8,0-3.6-0.1-6.3-0.2c0.5-8.8,0.7-17.2,1.5-25.6c5.1-50,23.2-94.7,54.6-134c3.3-4.1,6.5-8.3,9.8-12.5
          L103.8,103.3z"/>
          <path class="st1" d="M104.3,467c60.1-60.1,120.3-120.2,180.4-180.3c0.2-0.2,0.4-0.5,0.6-0.8c0.2,2,0.5,4,0.5,6
          c0,81.3,0,162.6,0,243.9c0,1.8-0.1,3.6-0.2,6.3c-8.8-0.5-17.2-0.7-25.6-1.5c-50-5.1-94.7-23.2-134-54.6c-4.1-3.3-8.3-6.5-12.5-9.8
          L104.3,467z"/>
          <path class="st2" d="M466.5,466.3C406.4,406.2,346.3,346,286.3,285.8c-0.2-0.2-0.5-0.4-0.8-0.6c2-0.2,4-0.5,6-0.5
          c81.3,0,162.6,0,243.9,0.1c1.8,0,3.6,0.1,6.3,0.2c-0.5,8.8-0.7,17.2-1.5,25.6c-5.1,50-23.2,94.7-54.6,134
          c-3.3,4.1-6.5,8.3-9.8,12.5L466.5,466.3z"/>
          <path class="st3" d="M284.5,28.3c0,85,0,170.1-0.1,255.1c0,0.3,0.1,0.7,0.1,1c-1.5-1.3-3.1-2.5-4.5-3.9
          C222.5,223,165,165.5,107.5,108c-1.3-1.3-2.5-2.6-4.3-4.6c6.6-5.8,12.6-11.7,19.2-17c39-31.7,83.4-50.6,133.4-56.2
          c5.3-0.6,10.5-1.3,15.7-1.9H284.5z"/>
          <path class="st4" d="M285.4,540.9c-0.1-1.7-0.2-3.3-0.2-5c0-81.9,0-163.8,0-245.6c0-1.5,0.1-3,0.2-4.5c1.1,0.9,2.4,1.8,3.4,2.8
          c58,57.9,115.9,115.9,173.8,173.8c0.9,0.9,1.8,1.9,3,3.3c-1,1.1-1.9,2.3-2.9,3.3c-37.9,36.2-82.5,59-134.2,67.7
          c-11,1.8-22.1,2.8-33.1,4.2L285.4,540.9z"/>
          </g>
          </svg>
          <span class="d-flex align-items-center ml-1" v-html="availableSpinHTML"></span>
      </button>

      <div class="wheel-container">
        <div class="wheel-skeleton"></div>
        <div id="wheelOfFortune" :style="wheelData.wheel_options.wheel && wheelData.wheel_options.wheel.outer_shadow == 1 ? 'box-shadow: 0px 0px 45px 2px #fffeec96; border-radius: 100%;' : ''">
          <div class="wheel-preview-pointer">
            <img :src="pointerArrow" style="width: 33px;" />
          </div>
          <div id="spin" class="spin-btn" :style="wheelData.wheel_options.wheel && wheelData.wheel_options.wheel.cp_shadow == 1 ?  'box-shadow: 1px 1px 25px 20px #57575754;' : ''">
            <img :src="CPLogo" style="width: 42px;" />
          </div>
          <canvas style="border-radius: 100%;" id="canvas" width="440" height="440">
            <p
              style="
                {
                  color: white;
                }
              "
              align="center"
            >
              Sorry, your browser doesn't support canvas. Please try another.
            </p>
          </canvas>
        </div>
      </div>
    </div>
    <div class="wheel-preview-name" v-if="(wheelData.wheel_options.button.wheel_bottom_button_text == null || wheelData.wheel_options.button.wheel_bottom_button_text == '' || wheelData.wheel_options.button.wheel_bottom_button_text == undefined)">
      <p>{{ wheelData.reward_wheel ? wheelData.reward_wheel : 'Wheel Name Here' }}</p>
    </div>
    <div style="text-align: center;" class="mb-2 mt-2" v-else>
      <!-- HTML !-->
      <button class="reward-wheel-below-button" :style="'background-color:'+wheelData.wheel_options.button.wheel_bottom_button_background_color+';color:'+wheelData.wheel_options.button.wheel_bottom_button_text_color+';font-size:'+(parseInt(wheelData.wheel_options.button.wheel_bottom_button_text_size)+4)+'px;important;'" role="button">{{wheelData.wheel_options.button.wheel_bottom_button_text}}&nbsp;
      <svg data-v-1a83a262="" xmlns="http://www.w3.org/2000/svg" width="7" height="9" viewBox="0 0 25 42" fill="#d4656e" style="position: absolute; right: 3%;margin-bottom:2px"><path data-v-1a83a262="" d="M24.0047 19.3612L5.31415 0.67097C4.88186 0.23834 4.30479 0 3.68948 0C3.07417 0 2.4971 0.23834 2.06481 0.67097L0.688385 2.04706C-0.207266 2.94373 -0.207266 4.40109 0.688385 5.2964L16.3833 20.9913L0.67097 36.7036C0.238681 37.1362 0 37.713 0 38.3279C0 38.9436 0.238681 39.5203 0.67097 39.9533L2.0474 41.329C2.48003 41.7617 3.05676 42 3.67207 42C4.28738 42 4.86445 41.7617 5.29674 41.329L24.0047 22.6218C24.438 22.1878 24.676 21.6083 24.6746 20.9923C24.676 20.3739 24.438 19.7948 24.0047 19.3612Z" fill="white"></path></svg>
      </button>
    </div>
    <div class="wheel-preview-footer" :style="footerStyle">
      <p class="wheel-preview-footer-title">Your Reward Points Balance <span>XXXX</span></p>
    </div>
  </div>
</template>

<script>
import Jackpot from './Jackpot.vue'
export default {
  name: "wheelPreview",
  props: {
    wheelData: {
      default: {},
      type: Object,
    },
    image: {
      default: "",
      type: String,
    },
  },
  data() {
    return {
      theWheel: {},
      pageImage: '',
      pointerArrow: '',
      CPLogo: '',
    };
  },
  components: {
    Jackpot
  },
  computed: {
    buttonStyle() {
        return {
            backgroundColor: this.wheelData.wheel_options.button.wheel_bottom_button_background_color,
            color: this.wheelData.wheel_options.button.wheel_bottom_button_text_color,
            fontSize: this.wheelData.wheel_options.button.wheel_bottom_button_text_size+"px!important"
        };
    },
    wheelPageStyle(){
      if(this.pageImage && this.wheelData.wheel_options.page.is_image){
        return 'background-image: url(' + this.pageImage + '); background-size: cover; background-position: center; padding-bottom: 40px; width: 300px; position: relative; overflow: hidden;'
      }else{
        return 'background-color: ' + this.wheelData.wheel_options.page.color_1 + '; background-image: linear-gradient( to bottom, ' + this.wheelData.wheel_options.page.color_1 + ',' + this.wheelData.wheel_options.page.color_2 + ',' + this.wheelData.wheel_options.page.color_3 + '); padding-bottom: 40px; width: 300px; position: relative; overflow: hidden;'
      }
    },
    rwPointButtonStyle(){
      if(this.wheelData.wheel_options && this.wheelData.wheel_options.spin_button){
          if(this.wheelData.wheel_options.spin_button.spin_transparent == 1){
            return 'background-color:'+ this.wheelData.wheel_options.spin_button.color + '96!important; padding: 1px 1px;';
          }else{
            return 'background-color:'+ this.wheelData.wheel_options.spin_button.color + '!important; padding: 1px 1px;';
          }
      }else{
          return ''
      }
    },
    availableSpinHTML(){
      let spin_text_color = 'color:#000!important; font-size: 6px;';

      if(this.wheelData.wheel_options && this.wheelData.wheel_options.spin_button){
        spin_text_color =  'color:'+ this.wheelData.wheel_options.spin_button.text_color + '!important; font-size: 6px;';
        return '<span class="free-spin-text d-flex flex-column justify-content-center"><span style="'+spin_text_color+'">FREE</span><span style="'+spin_text_color+'">SPIN</span></span><span class="spin-value-text" style="background-color: #007EE5; font-size: 8px; border-radius: 100%; padding: 2px; color: #fff; margin-left: 3px;">x10</span>';
      }else{
        return '<span class="free-spin-text d-flex flex-column justify-content-center"><span style="'+spin_text_color+'">FREE</span><span style="'+spin_text_color+'">SPIN</span></span><span class="spin-value-text" style="background-color: #007EE5; font-size: 8px; border-radius: 100%; padding: 2px; color: #fff; margin-left: 3px;">x10</span>';
      }
    },
    headerStyle(){
      if(this.wheelData.wheel_options.header.header_transparent == 1){
        return 'background-color:'+this.wheelData.wheel_options.header.color+'96'
      }else{
        return 'background-color:'+this.wheelData.wheel_options.header.color
      }
    },
    footerStyle(){
      if(this.wheelData.wheel_options.footer.footer_transparent == 1){
        return 'background-color:'+this.wheelData.wheel_options.footer.color+'96'
      }else{
        return 'background-color:'+this.wheelData.wheel_options.footer.color
      }
    }
  },
  methods: {
    resetWheel() {
      let self = this;
      self.theWheel.rotationAngle = 0; // Re-set the wheel angle to 0 degrees.
      self.theWheel.draw(); // Call draw to render changes to the wheel.
    },
    setwheel() {
      let self = this;

      var segments = [];

      if(self.wheelData){
        self.wheelData.rewardSegments.forEach((definitions, index) => {

            if(definitions.image.name){
              var image_url = URL.createObjectURL(definitions.image)
            }else{
              var image_url = definitions.image
            }
            var segment_number = ' ' + (index + 1)
            segments.push({
                image: image_url,
                text: segment_number,
                fillStyle: definitions.color ? definitions.color : '#fff',
            });
        });
      }

      // Create new wheel object specifying the parameters at creation time.
      self.theWheel = new Winwheel({
            'numSegments': segments.length, // Specify number of segments.
            'outerRadius': 200, // Set outer radius so wheel fits inside the background.
            'drawText': false, // Code drawn text can be used with segment images.
            'textAlignment': 'inner',
            'strokeStyle': '#0000',
            'drawMode': 'segmentImage', // Must be segmentImage to draw wheel using one image per segemnt.
            'segments' : segments,
            'pins': {
                'margin': -5,
                // Pins 
                'number': self.wheelData.wheel_options.wheel && self.wheelData.wheel_options.wheel.pin ? parseInt(segments.length * self.wheelData.wheel_options.wheel.pin.count) : '',
                'fillStyle': self.wheelData.wheel_options.wheel && self.wheelData.wheel_options.wheel.pin ? self.wheelData.wheel_options.wheel.pin.color : '',
                'strokeStyle': self.wheelData.wheel_options.wheel && self.wheelData.wheel_options.wheel.pin ? self.wheelData.wheel_options.wheel.pin.color : '',
                'outerRadius': self.wheelData.wheel_options.wheel && self.wheelData.wheel_options.wheel.pin ? parseInt(self.wheelData.wheel_options.wheel.pin.size) : '',
                'pinGlow': self.wheelData.wheel_options.wheel && self.wheelData.wheel_options.wheel.pin && self.wheelData.wheel_options.wheel.pin.glow == 1 ? true : false,

                // Inner 
                'borderLineWidth': self.wheelData.wheel_options.wheel && self.wheelData.wheel_options.wheel.inner ? parseInt(self.wheelData.wheel_options.wheel.inner.size) : '',
                'color_1': self.wheelData.wheel_options.wheel && self.wheelData.wheel_options.wheel.inner ? self.wheelData.wheel_options.wheel.inner.color_1 : '',
                'color_2': self.wheelData.wheel_options.wheel && self.wheelData.wheel_options.wheel.inner ? self.wheelData.wheel_options.wheel.inner.color_2 : '',
                'color_3': self.wheelData.wheel_options.wheel && self.wheelData.wheel_options.wheel.inner ? self.wheelData.wheel_options.wheel.inner.color_3 : '',

                // Outer 
                'outerBorderLineWidth': self.wheelData.wheel_options.wheel && self.wheelData.wheel_options.wheel.outer && self.wheelData.wheel_options.wheel.outer.size ? parseInt(self.wheelData.wheel_options.wheel.outer.size) : 0,
                'outerBorderStrokeStyle': self.wheelData.wheel_options.wheel && self.wheelData.wheel_options.wheel.outer ? self.wheelData.wheel_options.wheel.outer.color : '',
            },
            'animation': // Specify the animation to use.
            {
                'type': 'spinToStop',
                'duration': 10, // 8 10 // Duration in seconds.
                'spins': 4, // 8 // Number of complete spins.
                // 'callbackFinished': self.callbackPrize,
            },
            'callbackImageLoaded': self.callbackImageLoaded,
            'inner_shadow': self.wheelData.wheel_options.wheel && self.wheelData.wheel_options.wheel.inner_shadow == 1 ? true : false
        });
    },
    // Function to generate multiple sparkling balls
    generateSparkles() {
      const container = document.querySelector(".sparkle-container");
      const numSparkles = 300; // Adjust the number of sparkles as desired
      const centerX = container.offsetWidth / 2;
      const centerY = container.offsetHeight / 2;
      const radius = Math.min(centerX, centerY) * 1.2; // Adjust the spread radius as desired

      for (let i = 0; i < numSparkles; i++) {
        const sparkle = this.createSparkle();

        // Randomly position the sparkles closer to the center
        const angle = Math.random() * 2 * Math.PI;
        const distance = Math.random() * radius;
        const x = centerX + distance * Math.cos(angle);
        const y = centerY + distance * Math.sin(angle);

        sparkle.style.left = x + "px";
        sparkle.style.top = y + "px";

        container.appendChild(sparkle);
      }
  },
  // Function to create a sparkling ball element with random size
  createSparkle() {
      const sparkle = document.createElement("div");
      sparkle.classList.add("golden-sparkle");

      // Randomly set the size of the sparkles
      const size = this.getRandomNumber(2, 6); // Adjust the minimum and maximum size as desired
      sparkle.style.width = size + "px";
      sparkle.style.height = size + "px";

      return sparkle;
  },
  // Function to generate a random number between min and max
  getRandomNumber(min, max) {
      return Math.random() * (max - min) + min;
  }
  },
  mounted() {
    let self = this;
    self.setwheel();
    self.resetWheel();
    setTimeout(() => {
      if(self.wheelData.wheel_options.sparkles.show == 1){
        self.generateSparkles();
      }
    }, 100);
    
    // Set Page Image 
    if(self.wheelData.wheel_options.page.image && self.wheelData.wheel_options.page.image.name){
      this.pageImage = URL.createObjectURL(self.wheelData.wheel_options.page.image)
    }else{
      this.pageImage = self.wheelData.wheel_options.page.image
    }
    // Set Pointer Arrow 
    if(self.wheelData.wheel_options.wheel.pointer_arrow && self.wheelData.wheel_options.wheel.pointer_arrow.name){
      this.pointerArrow = URL.createObjectURL(self.wheelData.wheel_options.wheel.pointer_arrow)
    }else{
      this.pointerArrow = self.wheelData.wheel_options.wheel.pointer_arrow
    }
    // Set CP Logo 
    if(self.wheelData.wheel_options.wheel.cp_logo && self.wheelData.wheel_options.wheel.cp_logo.name){
      this.CPLogo = URL.createObjectURL(self.wheelData.wheel_options.wheel.cp_logo)
    }else{
      this.CPLogo = self.wheelData.wheel_options.wheel.cp_logo
    }
    console.log(self.wheelData)
  },

  watch: {
  },
};
</script>

<style lang="scss">
.wheel-container {
  position: relative;
  min-height: auto;
  padding: 0 15px;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1;
}
#wheelOfFortune {
  position: relative;
  overflow: hidden;
  border-radius: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

#canvas {
  width: 100%;
}

#wheel {
  display: block;
}

#spin {
  width: 40px;
  height: 40px;
  background: #3c3c3c;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.wheel-preview-header{
  padding: 15px 15px;
  border-radius: 0px 0px 10px 10px;
  position: relative;
  height: 65px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.wheel-preview-close{
  position: absolute;
  right: 15px;
  top: 10px;
}
.wheel-preview-title{
  margin: 0;
  font-size: 26px;
  font-weight: 700;
  text-align: center;
  color: #fff;
}
.wheel-preview-footer{
  padding: 15px 15px;
}
.wheel-preview-footer-title{
  margin: 0;
  text-align: center;
  font-size: 12px;
  font-weight: 500;
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.wheel-preview-footer-title span{
  font-size: 18px;
}
.wheel-preview-name{
  padding: 15px 15px;
  text-align: center;
}
.wheel-preview-name p{
  margin: auto;
  color: #fff;
  font-weight: 600;
  font-size: 18px;
}

.sparkle-container{
  position: relative;
}

.golden-sparkle {
    position: absolute;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #fffff2;
    box-shadow: 0 0 20px 3px #ffffd8;
    /* animation: sparkleAnimation 5s infinite; */
}

@keyframes sparkleAnimation {
    0% {
    transform: scale(0);
    opacity: 0;
    }
    50% {
    transform: scale(1.5);
    opacity: 1;
    box-shadow: 0 0 20px 5px #ffffad;
    }
    100% {
    transform: scale(0);
    opacity: 0;
    }
}

.rw-timmer-chip{
    background: #e7e7e7;
    color: black;
    padding: 0px 0px;
    border-radius: 15px;
    font-weight: bold;
    font-size: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 0;
    position: absolute;
    top: 0;
    left: 15px;
}
.rw-timmer-chip svg{
    width: 18px;
    height: 18px;
}
.st0{fill:#dbdbdb;}
.st1{fill:#0DD668;}
.st2{fill:#D3FCC8;}
.st3{fill:#007EE5;}
.st4{fill:#29576C;}
.wheel-preview-pointer{
  position: absolute;
  top: 0;
}
/* CSS */
.reward-wheel-below-button {
  align-items: center;
  appearance: none;
  background-color: #3EB2FD;
  background-size: calc(100% + 20px) calc(100% + 20px);
  border-radius: 100px;
  border-width: 0;
  box-shadow: none;
  box-sizing: border-box;
  color: #FFFFFF;
  cursor: pointer;
  display: inline-flex;
  font-family: CircularStd,sans-serif;
  font-size: 1rem;
  height: auto;
  justify-content: center;
  line-height: 1.5;
  padding: 4px 12px;
  position: relative;
  text-align: center;
  text-decoration: none;
  transition: background-color .2s,background-position .2s;
  user-select: none;
  -webkit-user-select: none;
  touch-action: manipulation;
  vertical-align: top;
  white-space: nowrap;
}

.reward-wheel-below-button:active,
.reward-wheel-below-button:focus {
  outline: none;
}

.reward-wheel-below-button:hover {
  background-position: -20px -20px;
}

.reward-wheel-below-button:focus:not(:active) {
  box-shadow: rgba(40, 170, 255, 0.25) 0 0 0 .125em;
}
</style>
