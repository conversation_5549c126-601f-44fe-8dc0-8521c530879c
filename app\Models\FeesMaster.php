<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
class FeesMaster extends Model
{
    protected $table = 'fees_master';

    /**
     * __construct
     * This function will insert an alphanumeric 32 character unique id as primary key in the table whenever a data get inserted
     * @param  mixed $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        $this->id = generateUUID();

        parent::__construct($attributes);
    }

    protected $fillable = [
        'fee_type',
        'volume_value_type',
        'per_transaction_value_type',
        'volume_value',
        'per_transaction_value',
    ];
    public $timestamps = true;
    public $incrementing = false;
}
