<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class UserRewardUsageHistory extends Model
{
    protected $table = 'user_reward_usage_history';

    protected $connection = MYSQL_REWARD_WHEEL;

    /**
     * __construct
     * This function will insert an alphanumeric 32 character unique id as primary key in the table whenever a data get inserted
     * @param  mixed $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        $this->id = generateUUID();

        parent::__construct($attributes);
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'reward_id',
        'reward_wheel_id',
        'entry_type',
        'transaction_id',
        'reward_point',
        'reward_point_left',
        'exchange_rate',
        'reward_amount',
        'reward_amount_left',
        'campaign_id',
        'petition_id',
        'corporate_parent_id',
    ];
    public $timestamps = true;
    public $incrementing = false;
    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
}
