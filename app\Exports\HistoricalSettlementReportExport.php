<?php

namespace App\Exports;

use DB;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Events\AfterSheet;
use Illuminate\Support\Facades\Log;

class HistoricalSettlementReportExport implements FromCollection, WithHeadings, ShouldAutoSize, WithEvents
{
    protected $request;

    public function __construct($request)
    {
        $this->request = $request; // Declaring the request variable
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        $collection_array = $this->request['report']; // Storing the array received from request
        return collect([
            $collection_array,
        ]);
    }

    public function headings(): array
    {
        $returnArray = array(
            // 1st header
            [
                'CanPay Settlement Report',
            ],
            // 2nd header
            [
                'Sales Date Range:', '',
                date('m/d/Y', strtotime($this->request['from_date'])) . ' - ' . date('m/d/Y', strtotime($this->request['to_date'])),
            ],
        );

        return $returnArray;
    }


    public function registerEvents(): array
    {
        $count = count((array) $this->request['report']);
        return [
            AfterSheet::class => function (AfterSheet $event) use ($count) {
                $event->sheet->getStyle('A3:P3')->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);
                $event->sheet->getStyle('A3:I3')->getAlignment()->applyFromArray(
                    array('horizontal' => 'center')
                );
                $event->sheet->getStyle('J3:P3')->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);
                $event->sheet->getStyle('J3:P3')->getAlignment()->applyFromArray(
                    array('horizontal' => 'center')
                );
                $event->sheet->getStyle('Q3:W3')->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);
                $event->sheet->getStyle('Q3:W3')->getAlignment()->applyFromArray(
                    array('horizontal' => 'center')
                );

                //Center align all columns
                $event->sheet->getStyle('A4:W' . ($count + 2))->getAlignment()->applyFromArray(
                    array('horizontal' => 'center')
                );

                //Bold Total Volume Column
                $event->sheet->getStyle('D4:D' . ($count + 2))->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);

                //Bold Total Fees Column
                $event->sheet->getStyle('E4:E' . ($count + 2))->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);

                //Bold Volume Column (Old Canpay Sales Activity)
                $event->sheet->getStyle('J4:J' . ($count + 2))->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);

                //Bold Sub Total Fees Column (Old Canpay Sales Activity)
                $event->sheet->getStyle('P4:P' . ($count + 2))->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);

                //Bold Sub Total Fees Column (New Canpay Sales Activity)
                $event->sheet->getStyle('Q4:Q' . ($count + 2))->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);
                $event->sheet->getStyle('W4:W' . ($count + 2))->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);

                $event->sheet->getStyle('A' . ($count + 2) . ':AB' . ($count + 5))->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);
                $event->sheet->getStyle('A3:I3')->applyFromArray([
                    'borders' => [
                        'allBorders' => [
                            'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                            'color' => ['argb' => '000000'],
                        ],
                    ],
                    'font' => [
                        'size' => 7,
                    ],
                ]);
                $event->sheet->getStyle('J3:W3')->applyFromArray([
                    'borders' => [
                        'allBorders' => [
                            'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                            'color' => ['argb' => '000000'],
                        ],
                    ],
                    'font' => [
                        'size' => 7,
                    ],
                ]);
                $event->sheet->getStyle('B4:W4')->applyFromArray([
                    'borders' => [
                        'allBorders' => [
                            'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                            'color' => ['argb' => '000000'],
                        ],
                    ],
                    'font' => [
                        'size' => 7,
                    ],
                ]);
                for ($i = 5; $i <= ($count + 2); $i++) {
                    $event->sheet->getStyle('B' . $i . ':W' . $i)->applyFromArray([
                        'borders' => [
                            'allBorders' => [
                                'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                                'color' => ['argb' => '000000'],
                            ],
                        ],
                    ]);
                }
                for ($i = 1; $i <= ($count + 2); $i++) {
                    $event->sheet->getStyle('A' . $i . ':AB' . $i)->applyFromArray([
                        'font' => [
                            'size' => 7,
                        ],
                    ]);
                }

                // Merging cells
                $event->sheet->mergeCells('A1:B1');
                $event->sheet->mergeCells('A2:B2');
                $event->sheet->mergeCells('A3:I3');
                $event->sheet->mergeCells('J3:P3');
                $event->sheet->mergeCells('Q3:W3');

                //Total For the First Store
                $event->sheet->getStyle('A5'.':W5')->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);

                //Style the Headings for different Stores
                $inc = $this->request['date_diff']+8;
                for($i=1;$i<$this->request['stores'];$i++){
                    if($i == 1){
                        $event->sheet->mergeCells('A'.($inc).':I'.($inc));
                        $event->sheet->mergeCells('J'.($inc).':P'.($inc));
                        $event->sheet->mergeCells('Q'.($inc).':W'.($inc));

                        //Total
                        $event->sheet->getStyle('A'.($inc+2).':W'.($inc+2))->applyFromArray([
                            'font' => [
                                'bold' => true,
                            ],
                        ]);
                        $event->sheet->getStyle('A'.($inc-1).':W'.($inc-1))->applyFromArray([
                            'font' => [
                                'bold' => true,
                            ],
                        ]);

                        //Heading
                        $event->sheet->getStyle('A'.($inc).':W'.($inc))->applyFromArray([
                            'font' => [
                                'bold' => true,
                            ],
                        ]);
                    }else{
                        $event->sheet->mergeCells('A'.($inc+$this->request['date_diff']+5).':I'.($inc+$this->request['date_diff']+5));
                        $event->sheet->mergeCells('J'.($inc+$this->request['date_diff']+5).':P'.($inc+$this->request['date_diff']+5));
                        $event->sheet->mergeCells('Q'.($inc+$this->request['date_diff']+5).':W'.($inc+$this->request['date_diff']+5));

                        //Total
                        $event->sheet->getStyle('A'.($inc+$this->request['date_diff']+7).':W'.($inc+$this->request['date_diff']+7))->applyFromArray([
                            'font' => [
                                'bold' => true,
                            ],
                        ]);
                        $event->sheet->getStyle('A'.($inc+$this->request['date_diff']+4).':W'.($inc+$this->request['date_diff']+4))->applyFromArray([
                            'font' => [
                                'bold' => true,
                            ],
                        ]);

                        //Heading
                        $event->sheet->getStyle('A'.($inc+$this->request['date_diff']+5).':W'.($inc+$this->request['date_diff']+5))->applyFromArray([
                            'font' => [
                                'bold' => true,
                            ],
                        ]);
                        $inc = $inc+$this->request['date_diff']+5;
                    }
                }
            },
        ];
    }
}
