<?php

namespace App\Http\Factories\CognitoValidationRules;

use App\Models\CognitoRulesLog;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 *
 * @package App\Http\Factories\IdValidator
 */
class CognitoRulesWithoutSSN implements CognitoRulesInterface
{
/* --------------------------------------------------------------------------------------------------- *
 *                                             FUNCTIONS                                               *
 * --------------------------------------------------------------------------------------------------- */

/**
 * This function takes the original User data array and add a few additional field like counters,
 * measures etc. For example, it will add some fields which contains the max values, or counts of something etc.
 * to the same $data array. The result is the original data array with some more information.
 *
 * The received data has nested arrays, means it has several rows under 'scores' elements. We will find
 * which is the max in those rows for a few fields, or how many times one field occured, etc.
 *
 * The objective is to have a Enriched set of data which can be evaluated against the Rules.
 *
 * @param [type] $data
 * @return $data Enriched Data
 */
    public function enrichCognitoData($data)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "====STARTING COGNITO RULE ENGINE WITHOUT SSN AS INPUT====");
        //  Add a few measures to the array received. by default, -1 if blank.
        $data['X_average_score_max'] = -1; //Will store max value from average fields
        $data['X_phone_score_max'] = -1; //Will store max value from phone fields... like that.
        $data['X_name_score_max'] = -1;
        $data['X_address_score_max'] = -1;
        $data['X_dob_score_max'] = -1;

        // This is a counter to count number of times SSN score is 100
        $data['X_ssn_score_100_count'] = 0; // Initially 0. Will increment whenever we find SSN = 100

        // This is a counter to count number of times phone score is 100
        $data['X_phone_score_100_count'] = 0; // Initially 0. Will increment whenever we find phone = 100

        // Loop through all data and update measures and counters. Two things happen here:
        // #1 It tried to find the max values for fields like name, phone etc. And update measure for that field.
        // $2 It updated the SSN counter when 100 is found in value.
        foreach ($data['scores'] as $scoreline) {
            // If counter value is less than current value, update measure.
            $data['X_average_score_max'] = $scoreline['average_score'] > $data['X_average_score_max'] ? $scoreline['average_score'] : $data['X_average_score_max'];
            $data['X_phone_score_max'] = $scoreline['phone_score'] > $data['X_phone_score_max'] ? $scoreline['phone_score'] : $data['X_phone_score_max'];
            $data['X_name_score_max'] = $scoreline['name_score'] > $data['X_name_score_max'] ? $scoreline['name_score'] : $data['X_name_score_max'];
            $data['X_address_score_max'] = $scoreline['address_score'] > $data['X_address_score_max'] ? $scoreline['address_score'] : $data['X_address_score_max'];
            $data['X_dob_score_max'] = $scoreline['birth_score'] > $data['X_dob_score_max'] ? $scoreline['birth_score'] : $data['X_dob_score_max'];

            // Increment counter where phone score is 100
            $data['X_phone_score_100_count'] = $scoreline['phone_score'] == 100 ? $data['X_phone_score_100_count'] + 1 : $data['X_phone_score_100_count'];
        }
        //getting the data of the maximum scores
        foreach ($data['scores'] as $scoreline) {
            if ($scoreline['phone_score'] == $data['X_phone_score_max']) {
                $phone = json_decode($scoreline['phone_components'], true);
                $data['source_phone'] = $phone['number']['source'];
            }
            if ($scoreline['name_score'] == $data['X_name_score_max']) {
                $name = json_decode($scoreline['name_components'], true);
                $data['source_name'] = $name['first']['source'] . $name['middle']['source'] . $name['last']['source'];
            }
            if ($scoreline['address_score'] == $data['X_address_score_max']) {
                $address = json_decode($scoreline['address_components'], true);
                $data['source_address'] = $address['street']['source'] . ' ' . $address['city']['source'] . ' ' . $address['subdivision']['source'] . ' ' . $address['postal_code']['source'];
            }
            if ($scoreline['birth_score'] == $data['X_dob_score_max']) {
                $dob = json_decode($scoreline['birth_components'], true);
                $data['source_dob'] = $dob['month']['source'] . '-' . $dob['day']['source'] . '-' . $dob['year']['source'];
            }
        }

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "====RETURNING ENRICH DATA====", (array) $data);
        return $data;
    }
/**
 * This function is responsible for checking against Congnito Rules as described under
 * https://docs.google.com/document/d/1aRUlh_we6ALXhTf4ypZAaftdqeqadlwAiltEs703L_s/edit
 *
 * Broadly, it will run a set of rules to see the outcome of a validation. See the details
 * in the function body to get an idea what exactly it is doing.
 *
 * Finally, it will add there more elements to the array as:
 *
 * 'outcome_status' If it is Approved or Manual Review or With Doubts
 * 'outcome_mustsubmitid' If ID submission is mandatory
 * 'outcome_reason' What caused this outcome_status. Like, not matched against anything. Or rule_id number
 *
 * @param [type] $data
 * @return $data with Outcomes
 */
    public function checkCognitoRules($data)
    {
        // Set a few default values. If nothing matches, then these defaults are returns.
        $data["outcome_status"] = MANUAL; // By default, manual review.
        $data["outcome_mustsubmitid"] = "Y (Default)"; // By default, manual review.
        $data["outcome_reason"] = "No Match. Manual. (Default). ";
        $data["outcome_ssnrequired"] = "N (Default)"; // By default, ssn does not require

        // Rule 2 - Lookup Decision Table and get outcomes from there.
        $data = $this->_lookupDecisionTable($data);

        // Rule 3(I) - 2 or more different sets of records based on Average Score
        //set it as approve with doubts
        if (count($data["average_scores"]) > 1) {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Checking up cognito rules. " . count($data["average_scores"]) . " record(s) found based on average score.");
            $data["outcome_reason"] = $data["outcome_reason"] . count($data["average_scores"]) . " record(s) found based on average score.";
            if ($data["outcome_status"] == APPROVE) { //if the status is approved then only it is eligible for the status Approve with doubts
                $data["outcome_status"] = APPROVED_WITH_DOUBTS; // By default, manual review.
            }
        }

        // Rule 3(II) - If phone score is 100 for more than 3 times, Approve with doubts
        if ($data['X_phone_score_100_count'] >= 2) {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Checking up cognito rules. Multiple phone records with 100 score found.");
            $data["outcome_reason"] = $data["outcome_reason"] . "Phone score is 100 for more than 3 records. ";
            if ($data["outcome_status"] == APPROVE) { //if the status is approved then only it is eligible for the status Approve with doubts
                $data["outcome_status"] = APPROVED_WITH_DOUBTS; // By default, manual review.
            }
        }

        // Rule 3(III) - Handle Date of Death cases. If any of the rows has got death date, then
        // set it to Approve with doubts

        if (!empty($data['deaths'])) {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Checking up cognito rules. Death date found.");
            $data["outcome_reason"] = $data["outcome_reason"] . "Date of death found. Needs checking. ";
            if ($data["outcome_status"] == APPROVE) {
                $data["outcome_status"] = APPROVED_WITH_DOUBTS; // By default, manual review.
            }
        }
        //need to store the whole data set into database
        $this->_storeEnrichData($data);
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Final result ready. Returning final result.", (array) $data);
        return $data;
    }

    private function _lookupDecisionTable($data)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "====Looking up in to decision table====");
        $row = DB::table('cognito_decision_table')
            ->select(DB::raw('rule_id,ssnrequired,mustsubmitid,finalstatus'))
            ->whereRaw(" ? between nameMin and nameMax",[$data['X_name_score_max']])
            ->whereRaw(" ? between phoneMin and phoneMax",[$data['X_phone_score_max']])
            ->whereRaw(" ? between addressMin and addressMax",[$data['X_address_score_max']])
            ->whereRaw(" ? between dobMin and dobMax",[$data['X_dob_score_max']])
            ->first();
        if ($row) {
            $data["outcome_status"] = $row->finalstatus; // By default, manual review.
            $data["outcome_mustsubmitid"] = $row->mustsubmitid;
            $data["outcome_ssnrequired"] = $row->ssnrequired;
            $data["outcome_reason"] = "Decision table matched on Rule ID: $row->rule_id. ";
        } else {
            $data["outcome_reason"] = "Decision table matched NONE.";
        }
        return $data;
    }

    /**
     *  This function stores the details to DB
     */
    private function _storeEnrichData($data)
    {
        $log = new CognitoRulesLog();
        $log->session_id = $data['session_id'];
        $log->phone = $data['input_phone'];
        $log->response = json_encode($data, true);
        $log->save();
        DB::commit();
    }

}
