<template>
<div>
    <div v-if="loading">
        <CanPayLoader/>
    </div>
    <div class="content-wrapper py-3" style="min-height: 36px">
        <div class="container-fluid">
            <h3 class="mb-3">Reward Wheel Probability Test</h3>
            <div class="card">
                <div class="card-body">
                    <form ref="jackpotValueResetForm" v-on:submit="generate">
                        <div class="row">
                            <div class="col-12 col-md-12 col-lg-12 mb-3">
                                <div class="row">
                                    <div class="col-12 col-md-6 col-lg-6">
                                        <div class="form-group">
                                        <label for="merchant">
                                            Reward Wheel <span class="red">*</span>
                                        </label>
                                        <multiselect
                                            id="reward_wheel"
                                            v-model="formData.reward_wheel"
                                            placeholder="Select Reward Wheel"
                                            label="reward_wheel"
                                            :options="rewardwheellist"
                                            :internal-search="true"
                                            v-validate="'required'"
                                        ></multiselect>
                                        <input
                                        class="form-control w-100"
                                        :name="'reward_wheel'"
                                        type="hidden"
                                        v-model="formData.reward_wheel"
                                        v-validate="'required'"
                                        />
                                        <span v-show="errors.has('reward_wheel')" class="text-danger">{{ errors.first('reward_wheel') }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-md-12 col-lg-12 mb-3">
                                <div class="row">
                                    <div class="col-12 col-md-6 col-lg-6">
                                        <label for="">No. of Spins <span class="red">*</span></label> <br>
                                        <NumberInput
                                        style="display: none;"
                                        v-model="formData.no_of_spins"
                                        length-type="jackpot"
                                        type="decimal"
                                        />
                                        <input
                                        class="form-control w-100"
                                        :name="'no_of_spins'"
                                        type="text"
                                        v-model="formData.no_of_spins"
                                        v-validate="'required|min_value:1|max_value:'+probability_test_minimum_value"
                                        />
                                        <span v-show="errors.has('no_of_spins')" class="text-danger">{{ errors.first('no_of_spins') }}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-md-12 col-lg-12">
                                <p>
                                    <svg width="30px" viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                        <title>warning</title>
                                        <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                            <g id="add" fill="#f94d4d" transform="translate(32.000000, 42.666667)">
                                                <path d="M246.312928,5.62892705 C252.927596,9.40873724 258.409564,14.8907053 262.189374,21.5053731 L444.667042,340.84129 C456.358134,361.300701 449.250007,387.363834 428.790595,399.054926 C422.34376,402.738832 415.04715,404.676552 407.622001,404.676552 L42.6666667,404.676552 C19.1025173,404.676552 7.10542736e-15,385.574034 7.10542736e-15,362.009885 C7.10542736e-15,354.584736 1.93772021,347.288125 5.62162594,340.84129 L188.099293,21.5053731 C199.790385,1.04596203 225.853517,-6.06216498 246.312928,5.62892705 Z M225.144334,42.6739678 L42.6666667,362.009885 L407.622001,362.009885 L225.144334,42.6739678 Z M224,272 C239.238095,272 250.666667,283.264 250.666667,298.624 C250.666667,313.984 239.238095,325.248 224,325.248 C208.415584,325.248 197.333333,313.984 197.333333,298.282667 C197.333333,283.264 208.761905,272 224,272 Z M245.333333,106.666667 L245.333333,234.666667 L202.666667,234.666667 L202.666667,106.666667 L245.333333,106.666667 Z" id="Combined-Shape">

                                    </path>
                                            </g>
                                        </g>
                                    </svg>
                                    <small>
                                    <strong>
                                            This report can not be generated until you have received the previous report data in your email. Generating the report frequently can cause database overload and slow down your system. We also recommend that you avoid generating the report during busy hours to prevent any potential issues.
                                    </strong>
                                </small>
                                </p>
                                <button v-if="!saving" class="btn btn-success px-3" type="submit">Generate</button>
                                <button v-if="saving" class="btn btn-success px-3 d-flex jutify-content-between align-items-center" type="button" disabled>
                                    <span>Generate</span>
                                    <div class="spinner-border spinner-border-sm text-light ml-2" role="status">
                                        <span class="sr-only">Loading...</span>
                                    </div>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>



        <!-- Confirmation modal start -->
        <b-modal ref="confirmation-modal" hide-footer hide-header id="confirmation-modal">
            <div class="color">
            <div class="col-12 text-center">
                <svg fill="none" width="100px" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M8.87 10.5046L10.8204 12.4504L9.76045 13.511L6 9.75456L9.76045 5.99805L10.8204 7.05871L8.87 9.00456H18V18H11.9532V16.5H16.5V10.5046H8.87Z" fill="#149240"/>
                </svg>
            </div>
            <div class="purchaserpower-modal-text">
                <div class="d-block text-center">
                <label class="text-justify text-secondary h4">
                    Are you sure ?
                </label>
                <br />
                <label class="text-justify text-secondary text-dark">
                    Are you sure want to generate report?
                </label>
                </div>
                <div class="row">
                <div class="col-12 text-center">
                    <button
                    @click="hideModal('confirmation-modal')"
                    class="btn btn-secondary btn-md center-block mr-2"
                    >
                    <label class="forgetpassword-ok-label my-0">Cancel</label>
                    </button>
                    <button
                    @click="confirmAndGenerate('confirmation-modal')"
                    class="btn btn-success btn-md center-block ml-2"
                    >
                    <label class="forgetpassword-ok-label my-0">Confirm</label>
                    </button>
                </div>
                </div>
            </div>
            </div>
        </b-modal>
        <!-- Confirmation modal end -->
    </div>
</div>
</template>

<script>
import { validationMixin } from "vuelidate";
import { required, minLength } from "vuelidate/lib/validators";
import { validate } from "json-schema";
import api from "@/api/rewardwheel.js";
import NumberInput from "./NumberInput.vue";
import CanPayLoader from "../CustomLoader/CanPayLoader.vue"
export default {
    mixins: [validationMixin],
    components: {
        NumberInput,
        CanPayLoader
    },
    data(){
        return{
            formData:{
                reward_wheel: '',
                no_of_spins: ''
            },
            saving: false,
            loading: false,
            probability_test_minimum_value: process.env.MIX_PROBABILITY_TEST_MINIMUM_VALUE,
            rewardwheellist: [],
        }
    },
    mounted(){
        var self = this;
        self.getAllRewardwheels();
    },
    methods:{
        hideModal(modal){
            var self = this;
            self.$bvModal.hide(modal);
        },
        generate(event){
            var self = this;
            event.preventDefault();
            self.$validator.validateAll().then(result => {
                if(result){
                    self.$bvModal.show('confirmation-modal');
                }
            })
            .catch(err => {
                error(err);
            });
        },
        confirmAndGenerate(modal){
            var self = this;

            self.$bvModal.hide(modal);

            let requestData = new FormData()
            requestData.append('reward_wheel_id', self.formData.reward_wheel.id)
            requestData.append('no_of_spins', self.formData.no_of_spins)

            self.saving = true;
            self.loading = true;
            api
            .generateReportAndPost(requestData)
            .then(response => {
                self.formData = {
                    reward_wheel: '',
                    no_of_spins: ''
                };
                self.saving = false;
                self.$validator.reset();
                self.$swal({
                  title: response.message,
                  icon: 'success',
                  width: 700,
                })
                self.loading = false;
            })
            .catch(err => {
                self.saving = false;
                self.loading = false;
                if(err.response.data.message){
                    self.$swal({
                        title: err.response.data.message,
                        icon: 'error',
                        width: 700,
                    })
                }else{
                    error(err);
                }
            });
        },
        getAllRewardwheels() {
            var self = this;
            self.loading = true;
            api
            .getAllRewardwheels()
            .then(function (response) {
                if (response.code == 200) {
                self.rewardwheellist = response.data;
                }else {
                error(response.message);
                }
                self.loading = false;
            })
            .catch(function (error) {
                self.loading = false;
            });
        },
    }
}
</script>
