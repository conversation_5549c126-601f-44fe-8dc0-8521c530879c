<?php

namespace App\Http\Factories\Transaction;

use App\Http\Clients\Acheck21HttpClient;
use App\Http\Clients\BLLHttpClient;
use App\Http\Factories\Webhook\WebhookFactory;
use App\Models\Acheck21HistoryTable;
use App\Models\BankAccountInfo;
use App\Models\LotteryUserReward;
use App\Models\MccMaster;
use App\Models\RegisteredMerchantMaster;
use App\Models\StatusMaster;
use App\Models\TerminalMaster;
use App\Models\TimezoneMaster;
use App\Models\TransactionDetails;
use App\Models\TransactionTypeMaster;
use App\Models\V1TransactionFailureMaster;
use App\Models\UserRewardUsageHistory;
use App\Models\Reward;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 *
 * @package App\Http\Factories\Transaction
 */
class TransactionFactory implements TransactionInterface
{

    public function __construct()
    {
        $this->acheck = new Acheck21HttpClient();
        $this->bll = new BLLHttpClient();
        $this->merchantWebhook = new WebhookFactory();
    }

    public function getAcheckBody($params)
    {
        switch ($params['type']) {
            case CONSUMER_CREATE_TRANSACTION:
                $bank_account_info = BankAccountInfo::join('status_master', 'user_bank_account_info.status', '=', 'status_master.id')
                    ->join('users', 'users.user_id', '=', 'user_bank_account_info.user_id')
                    ->where("user_bank_account_info.user_id", $params['consumer_id'])
                    ->where("status_master.code", BANK_ACTIVE)
                    ->first();
                $body = '{
                    "accountNumber": "' . $bank_account_info->account_no . '",
                    "accountType": "' . $bank_account_info->account_type . '",
                    "amount": ' . $params['amount'] . ',
                    "checkNumber": "' . str_pad(rand(0, pow(10, 15) - 1), 15, '0', STR_PAD_LEFT) . '",
                    "entryClass": "PPD",
                    "routingNumber": "' . $bank_account_info->routing_no . '",
                    "individualName": "' . $bank_account_info->first_name . ' ' . $bank_account_info->last_name . '"
                  }';
                break;
            case MERCHANT_CREATE_DEPOSIT_TRANSACTION:
                $bank_account_info = BankAccountInfo::join('status_master', 'user_bank_account_info.status', '=', 'status_master.id')
                    ->join('registered_merchant_master', 'registered_merchant_master.id', '=', 'user_bank_account_info.merchant_id')
                    ->join('merchant_stores', 'merchant_stores.merchant_id', '=', 'registered_merchant_master.id')
                    ->where("user_bank_account_info.merchant_id", $params['merchant_id'])
                    ->where("status_master.code", BANK_ACTIVE)
                    ->first();
                $body = '{
                        "accountNumber": "' . $bank_account_info->account_no . '",
                        "amount": ' . $params['amount'] . ',
                        "checkNumber": "' . str_pad(rand(0, pow(10, 15) - 1), 15, '0', STR_PAD_LEFT) . '",
                        "entryClass": "CCD",
                        "routingNumber": "' . $bank_account_info->routing_no . '",
                        "individualName": "' . $bank_account_info->retailer . '",
                        }';
                break;
            case MERCHANT_CREATE_FEE_TRANSACTION:
                $bank_account_info = BankAccountInfo::join('status_master', 'user_bank_account_info.status', '=', 'status_master.id')
                    ->join('registered_merchant_master', 'registered_merchant_master.id', '=', 'user_bank_account_info.merchant_id')
                    ->join('merchant_stores', 'merchant_stores.merchant_id', '=', 'registered_merchant_master.id')
                    ->where("user_bank_account_info.merchant_id", $params['merchant_id'])
                    ->where("status_master.code", BANK_ACTIVE)
                    ->first();
                $body = '{
                            "accountNumber": "' . $bank_account_info->fees_account_number . '",
                            "amount": ' . $params['amount'] . ',
                            "checkNumber": "' . str_pad(rand(0, pow(10, 15) - 1), 15, '0', STR_PAD_LEFT) . '",
                            "entryClass": "CCD",
                            "routingNumber": "' . $bank_account_info->routing_no . '",
                            "individualName": "' . $bank_account_info->retailer . '",
                            }';
                break;
            case CANPAY_CREATE_TRANSACTION:
                $body = '{
                    "accountNumber": "' . config('app.canpay_account_number') . '",
                    "accountType": "' . config('app.canpay_account_type') . '",
                    "amount": ' . $params['amount'] . ',
                    "checkNumber": "' . str_pad(rand(0, pow(10, 15) - 1), 15, '0', STR_PAD_LEFT) . '",
                    "entryClass": "CCD",
                    "routingNumber": "' . config('app.canpay_routing_number') . '",
                    "individualName": "' . config('app.canpay_account_holder_name') . '",
                    }';
                break;
            case VALIDATE_TRANSACTION:
                $body = '{
                    "accountNumber": "' . $params['accountNumber'] . '",
                    "amount": 0.01,
                    "checkNumber": "' . str_pad(rand(0, pow(10, 15) - 1), 15, '0', STR_PAD_LEFT) . '",
                    "individualName": "' . $params['name'] . '",
                    "routingNumber": "' . $params['routingNumber'] . '"
                  }';
                break;
            case CONSUMER_CREATE_RETURN_TRANSACTION:
                $bank_account_info = BankAccountInfo::join('users', 'users.user_id', '=', 'user_bank_account_info.user_id')
                    ->where("user_bank_account_info.user_id", $params['consumer_id'])
                    ->where("user_bank_account_info.id", $params['account_id'])
                    ->first();
                $body = '{
                    "accountNumber": "' . $bank_account_info->account_no . '",
                    "accountType": "' . $bank_account_info->account_type . '",
                    "amount": ' . $params['amount'] . ',
                    "checkNumber": "55550' . str_pad(rand(0, pow(5, 10) - 1), 10, '0', STR_PAD_LEFT) . '",
                    "entryClass": "PPD",
                    "routingNumber": "' . $bank_account_info->routing_no . '",
                    "individualName": "' . $bank_account_info->first_name . ' ' . $bank_account_info->last_name . '"
                    }';
                break;
            case CANPAY_CREATE_RETURN_TRANSACTION:
                $body = '{
                        "accountNumber": "' . config('app.canpay_return_account_number') . '",
                        "accountType": "' . config('app.canpay_account_type') . '",
                        "amount": ' . $params['amount'] . ',
                        "checkNumber": "' . str_pad(rand(0, pow(10, 15) - 1), 15, '0', STR_PAD_LEFT) . '",
                        "entryClass": "CCD",
                        "routingNumber": "' . config('app.canpay_routing_number') . '",
                        "individualName": "' . config('app.canpay_return_account_holder_name') . '",
                        }';
                break;
        }
        return $body;
    }

    public function createConsumerTransaction($params)
    {
        $params['type'] = CONSUMER_CREATE_TRANSACTION;
        //get the correct transaction body
        if (config('app.performance_testing_mode') == false) {
            $params['body'] = $this->getAcheckBody($params);
            $response = $this->acheck->createTransaction($params);
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer transaction with body: " . $params['body'] . " returned response: " . $response);
        } else {
            $params['body'] = '{
                "accountNumber": "****************",
                "accountType": "****************",
                "amount": "10",
                "checkNumber": "****************",
                "entryClass": "Test",
                "routingNumber": "****************",
                "individualName": "Test"
              }';
            $response = $this->acheck->createMockTransaction($params);
        }
        return $response;
    }

    public function cancelConsumerTransaction($params)
    {
        $response = $this->acheck->deleteTransaction($params);
        return $response;
    }

    public function createMerchantDepositTransaction($params)
    {
        $params['type'] = MERCHANT_CREATE_DEPOSIT_TRANSACTION;
        //get the correct transaction body
        $params['body'] = $this->getAcheckBody($params);
        $response = $this->acheck->createTransaction($params);
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Merchant deposit transaction with body: " . $params['body'] . " returned response: " . $response);
        return $response;
    }

    public function createMerchantFeeTransaction($params)
    {
        $params['type'] = MERCHANT_CREATE_FEE_TRANSACTION;
        //get the correct transaction body
        $params['body'] = $this->getAcheckBody($params);
        $response = $this->acheck->createTransaction($params);
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Merchant fee transaction with body: " . $params['body'] . " returned response: " . $response);
        return $response;
    }

    public function createCanPayTransaction($params)
    {
        $params['type'] = CANPAY_CREATE_TRANSACTION;
        //get the correct transaction body
        $params['body'] = $this->getAcheckBody($params);
        $response = $this->acheck->createTransaction($params);
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Canpay transaction with body: " . $params['body'] . " returned response: " . $response);
        return $response;
    }

    public function validateBankAccount($params)
    {
        $params['type'] = VALIDATE_TRANSACTION;
        //get the correct transaction body
        $params['body'] = $this->getAcheckBody($params);
        $response = $this->acheck->validateTransaction($params);
        return $response;
    }
    /**
     * voidV1Transaction
     * This function will void a non settled V1 transaction
     * @param  mixed $params
     * @return void
     */
    public function voidV1Transaction($params)
    {
        $transaction_details = TransactionDetails::find($params['transaction_id']); // Fetching Transaction details

        $return_array = new \stdClass();
        if (empty($transaction_details)) {
            $return_array->code = FAIL;
            $return_array->message = "Transaction not found.";
            $return_array->data = "";
            Log::error(__METHOD__ . "(Line: " . __LINE__ . ") - " . "Transaaction not found");
            return $return_array;
        }
        $timezone_details = TimezoneMaster::find($transaction_details->timezone_id);
        $transaction_local_time = Carbon::now();
        if ($transaction_details->timezone_id) {
            $transaction_local_time = Carbon::now()->timezone($timezone_details->timezone_name);
        }
        // check if testing mode is enabled and the payment pin is a 0000000 then dont post the transaction to the zipline server
        if ($transaction_details->v1_payment_pin === '0000000') {
            // Updation begins in Transaction Details Table
            $transaction_details->status_id = getStatus(VOIDED);
            $transaction_details->voided_by = isset($params['user_id']) ? $params['user_id'] : null;
            $transaction_details->voided_time = Carbon::now();
            $transaction_details->voided_local_time = $transaction_local_time;
            $transaction_details->save();
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction voided successfully.");
            $return_array->code = SUCCESS;
            $return_array->message = trans('message.transaction_cancelled');
            $return_array->data = $transaction_details;
            return $return_array;
        }
        $headers = array(
            'Content-Type' => 'application/json',
            'X-API-Key' => config('app.bll_api_base_key'),
        );
        // Fetching merchant details
        $merchant_details = RegisteredMerchantMaster::join('merchant_stores', 'merchant_stores.merchant_id', '=', 'registered_merchant_master.id')->leftJoin('timezone_masters', 'timezone_masters.id', '=', 'merchant_stores.timezone_id')->join('terminal_master', 'terminal_master.merchant_store_id', '=', 'merchant_stores.id')->leftJoin('transaction_type_master', 'terminal_master.transaction_type_id', '=', 'transaction_type_master.id')->select('registered_merchant_master.*', 'merchant_stores.store_id', 'terminal_master.terminal_name', 'terminal_master.id as terminal_id', 'terminal_master.transaction_type_id', 'transaction_type_master.label as transaction_type', 'timezone_masters.timezone_name', 'timezone_masters.id as timezone_id')->where('terminal_master.id', $transaction_details->terminal_id)->first();

        //get transaction type
        $terminal_type = $merchant_details->transaction_type;
        if (empty($merchant_details->transaction_type)) {
            $transaction_type = TransactionTypeMaster::find($transaction_details->transaction_type_id);
            $terminal_type = $transaction_type->label;
        }
        // check if transaction type is present then is it a transaction from the 3rd party integrators and hence use that value
        $transactionArray = array(
            'Amount' => $transaction_details->amount + $transaction_details->tip_amount,
            'LaneID' => $merchant_details->terminal_name,
            'MerchantID' => $merchant_details->merchant_id,
            'StoreCode' => $merchant_details->store_id . $terminal_type,
            'TransactionDate' => Carbon::now()->format('Y-m-d H:i:s') . ".804",
            'TransactionID' => (int) $transaction_details->zipline_trans_id,
            'TransactionType' => 2, // TransactionType is an enumeration where 0 = Unattended, 1 = InsideStore, 2 = Reversal
        );
        $paymentArray = array(
            'Transaction' => $transactionArray,
        );
        $body = array(
            'Token' => $transaction_details->v1_payment_pin,
            'SessionID' => (int) $transaction_details->zipline_session_id,
            'OriginalTransactionType' => 1,
            'OriginalTransactionDate' => $transaction_details->local_transaction_time . ".802",
            'Token' => $transaction_details->v1_payment_pin,
            'Payment' => $paymentArray,
        );
        $data['headers'] = $headers;
        $data['body'] = json_encode($body);
        $response = $this->bll->payReversalWithToken($data);
        $result = json_decode($response);
        if ($result->Error->ID == 0) {
            // Payment Reversal Succeeded
            // Updation begins in Transaction Details Table
            $status_details = StatusMaster::where('code', VOIDED)->first(); // Getting Voided Status ID for the Transaction
            $transaction_details->status_id = $status_details->id;
            $transaction_details->voided_by = isset($params['user_id']) ? $params['user_id'] : null;
            $transaction_details->voided_time = Carbon::now();
            $transaction_details->voided_local_time = $transaction_local_time;
            $transaction_details->save();
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction voided successfully.");
            $return_array->code = SUCCESS;
            $return_array->message = trans('message.transaction_cancelled');
            $return_array->data = "";
            return $return_array;
        } else {
            // Payment Reversal Failed
            $error_message = V1TransactionFailureMaster::where('code', $result->Error->ID)->first();
            $return_array->code = FAIL;
            $return_array->message = $error_message->message;
            $return_array->data = $result;
            Log::channel('void-transaction-failure')->error(__METHOD__ . "(Line: " . __LINE__ . ") - " . "Exception while voiding transaction.", ['Exception' => $result]);
            return $return_array;
        }
    }
    /**
     * voidV2Transaction
     * This function will void a non settled V2 transaction
     * @param  mixed $params
     * @return void
     */
    public function voidV2Transaction($transaction, $data, $requestdata)
    {
        $user = Auth::user();
        $voided = getStatus(VOIDED);
        DB::beginTransaction();
        try {
            $before_void_change_request = $transaction->change_request;
            $terminal_details = TerminalMaster::join('merchant_stores', 'merchant_stores.id', '=', 'terminal_master.merchant_store_id')
                ->join('registered_merchant_master', 'registered_merchant_master.id', '=', 'merchant_stores.merchant_id')
                ->leftJoin('timezone_masters', 'timezone_masters.id', '=', 'merchant_stores.timezone_id')
                ->select('terminal_master.*', 'timezone_masters.timezone_name', 'timezone_masters.id as timezone_id', 'registered_merchant_master.live_webhook_url', 'registered_merchant_master.sandbox_webhook_url')->where('terminal_master.id', $transaction->terminal_id)->first();
            $transaction->status_id = $voided;
            $transaction->voided_time = Carbon::now();
            $transaction->change_request = 0;
            $transaction->voided_by = isset($data['user_id']) ? $data['user_id'] : $user->user_id;
            $transaction->voided_local_time = Carbon::now()->timezone($terminal_details->timezone_name);
            $transaction->comment = isset($data['comment']) ? $data['comment'] : null;
            $transaction->save();

            // Revert the most recent transaction's reward amount and associated reward points.
            $latestTransation = $transaction;

            // check modifield transction and cancel the modified transaction
            $transactionId = $transaction->change_request_transaction_ref_no ? $transaction->change_request_transaction_ref_no : $transaction->id;
            if ($transaction->change_request_transaction_ref_no != null) {

                $m_transaction = TransactionDetails::where('id', $transaction->change_request_transaction_ref_no)->first();
                $m_transaction->status_id = $voided;
                $m_transaction->voided_time = Carbon::now();
                $m_transaction->voided_by = isset($data['user_id']) ? $data['user_id'] : (!empty($user) ? $user->user_id : null);
                $m_transaction->voided_local_time = Carbon::now()->timezone($terminal_details->timezone_name);
                $m_transaction->comment = isset($data['comment']) ? $data['comment'] : null;
                $m_transaction->save();

                // Revert the most recent modified transaction's reward amount and associated reward points.
                $latestTransation = $m_transaction;
                $webhook_url = config('app.api_environment') . '_webhook_url';
                if ($terminal_details->$webhook_url != '') {
                    //webhook call after void
                    $expiry_date_for_book_now = '';
                    $this->merchantWebhook->modificationWebhookCall($transactionId, VOIDED, $expiry_date_for_book_now);
                }
            }
            if ($before_void_change_request != 1) {
                // Revert back the reward amount and reward amount back to the consumer as the transaction is voided
                addCreditAfterTransactionVoid($latestTransation, $data, $requestdata);
                Log::info("add credit: " . getTimeElapsedArr($requestdata));

                // Revert Back the Points Against the Transaction
                revertPointsAganistTransaction($latestTransation, 1);
                Log::info("revert points: " . getTimeElapsedArr($requestdata));


                // Void Lottery Rewards if the User has won Lottery
                $this->voidRevokeLotteryReward($latestTransation, 1);
                Log::info("void revoke: " . getTimeElapsedArr($requestdata));
            }

            DB::commit();
            $message = trans('message.transaction_cancelled');
            return renderResponse(SUCCESS, $message, null);
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(Line: " . __LINE__ . ") - " . "Exception while storing transaction details.", ['Exception' => $e]);
            $message = trans('message.db_transaction_failed');
            return renderResponse(FAIL, $message, $e);
        }
    }

    /**
     * revokeVoidTransaction
     * This function will revoke the Void Transaction
     * @param  mixed $params
     * @return void
     */
    public function revokeVoidTransaction($transaction, $data)
    {
        DB::beginTransaction();
        try {
            // Get the Status ID
            $pending = getStatus(PENDING);
            $void_revoked = getStatus(VOID_REVOKED);
            $voided = getStatus(VOIDED);
            $process_for_acheck21 = getStatus(PROCESSED_FOR_ACHECK21);
            $awaiting_consumer_approval = getStatus(AWAITINGCONSUMERAPPROVAL);
            $approved_by_consumer = getStatus(APPROVED_BY_CONSUMER);
            $request_timeout = getStatus(REQUEST_TIMEOUT);

            //get transaction id
            $transactionId = $transaction->id;


            $scheduled_posting_date = $transaction->scheduled_posting_date;
            if ($transaction->change_request_transaction_ref_no != null) {
                // get modified transaction
                $modified_transaction = TransactionDetails::where('id', $transaction->change_request_transaction_ref_no)->first();
                if ($modified_transaction) {

                    $scheduled_posting_date = $modified_transaction->scheduled_posting_date;
                    //get modified transaction id
                    $transactionId = $transaction->change_request_transaction_ref_no;

                    // if accpeted/approved modification then make it pending
                    if (in_array($transaction->consumer_approval_for_change_request, [$approved_by_consumer])) {
                        // Update the Child row to Pending
                        $modified_transaction->status_id = $pending;
                        $modified_transaction->save();
                    }
                }
                // Update the Parent row change request to 1
                $transaction->change_request = 1;
            } else {
                // if parent transaction not modified
                if ($transaction->consumer_approval_for_change_request == null) {
                    // Update the Parent row to Pending
                    $transaction->status_id = $pending;
                } else {
                    // Update the Parent row change request to 1
                    $transaction->change_request = 1;
                }
            }
            $transaction->save();

            // use for void previous
            $latestTransation = TransactionDetails::where('id', $transactionId)->first();
            $transaction_sql = "SELECT td1.acheck_document_id, rmm.acheck_account_id, rmm.is_enabled_new_ach_process
                FROM transaction_details td
                JOIN terminal_master tm ON tm.id = td.terminal_id
                JOIN merchant_stores ms ON tm.merchant_store_id = ms.id
                JOIN registered_merchant_master rmm ON rmm.id = ms.merchant_id
                LEFT JOIN transaction_details td1 FORCE INDEX(idx_trans_ref_no) ON td.id = td1.transaction_ref_no AND td1.status_id = ?
                WHERE td.id = ? ";

            $transaction_details = DB::select($transaction_sql, [$process_for_acheck21, $transactionId]);

            $is_enabled_new_ach_process = $transaction_details[0]->is_enabled_new_ach_process;
            $params['acheck_account_id'] = $transaction_details[0]->acheck_account_id;
            $params['acheck_document_id'] = $transaction_details[0]->acheck_document_id;

            // Check the Status of the Last row
            $transaction_lastest_row = TransactionDetails::where(['transaction_ref_no' => $transactionId])
                ->orWhere(['id' => $transactionId])
                ->orderBy('created_at', 'DESC')->first();

            // Check if the Transaction is posted to Acheck21 (If posted, then delete it from Acheck21 side)
            if (!empty($transaction_lastest_row) && $transaction_lastest_row->status_id == $process_for_acheck21) {

                // Void the Transaction on the Acheck21 side
                if (config('app.acheck_posting')) {
                    $response = $this->acheck->deleteTransaction($params);
                    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is enabled. Posting in ACHECK21.");
                    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction voided in the Acheck21 side for amount: " . $transaction->amount . " with Transaction Number: " . $transaction->transaction_number . " ,acheck_account_id: " . $params['acheck_account_id'] . " and acheck_document_id: " . $params['acheck_document_id']);
                } else {
                    $response = 204;
                    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is disabled. Continue with static 204 response.");
                }

                if ($response == 204) {
                    // If the Transaction is Voided From Admin Panel but the Voided Sub row does not exists then need to insert it from here to keep the log of voided transaction
                    $this->_createTransaction($latestTransation, $voided, $data);
                }
            } else if (!empty($transaction_lastest_row) && $transaction_lastest_row->status_id != $voided) {
                // If the Transaction is Voided From Admin Panel but the Voided Sub row does not exists then need to insert it from here to keep the log of voided transaction
                $this->_createTransaction($latestTransation, $voided, $data);
            }

            // use for webhook call
            $status = SETTLED;
            $expiry_date_for_book_now = '';
            // Add new row with status Void Revoked
            $this->_createTransaction($latestTransation, $void_revoked, $data);
            if (!empty($transaction_lastest_row) && $transaction_lastest_row->status_id == $process_for_acheck21) {
                // If new ACH proess is enabled then no need to post transation to ACHECK
                $enable_new_ach_process_for_all_merchant = getSettingsValue('enable_new_ach_process_for_all_merchant', 0);
                if ($enable_new_ach_process_for_all_merchant == 0 && $is_enabled_new_ach_process == 0) {
                    // Post to Acheck21 after Void Revoked
                    $this->_postTransactionIntoAcheck21($latestTransation, $params);
                }
            } else {

                // use for webhook call
                if ($transaction->attempt_count > 0) {
                    $transactionStatusId = $transaction->consumer_approval_for_change_request;
                    if ($transaction->consumer_approval_for_change_request == $awaiting_consumer_approval && $transaction->expiration_datetime) {
                        $store_current_date_in_utc = Carbon::now('UTC');
                        $expiration_datetime_in_utc = Carbon::parse($transaction->expiration_datetime_in_utc);
                        if ($expiration_datetime_in_utc->lt($store_current_date_in_utc)) {
                            $transactionStatusId = $request_timeout;
                        }
                    }
                } else {
                    $transactionStatusId = $pending;
                }
                $status = StatusMaster::find($transactionStatusId)->status;
                //need to Accept Payment if transaction is admin driven and status pending , approved or auto approved
                $expiry_date_for_book_now = '';
                if ($transaction->admin_driven && in_array($transactionStatusId, [$pending, $approved_by_consumer])) {
                    if (!isset($scheduled_posting_date)) {
                        $expiry_date_for_book_now = date('m-d-Y', strtotime('+' . config('app.admin_driven_transaction_expiry_days') . ' day', strtotime($transaction->local_transaction_date)));
                    }
                    if ($transactionStatusId == $pending && isset($scheduled_posting_date)) {
                        $status = SETTLED;
                    }
                }
            }
            //webhook call after revoke void
            $this->merchantWebhook->modificationWebhookCall($transactionId, $status, $expiry_date_for_book_now);

            if ($transaction->change_request != 1) {
                // After Void Revoked Revert back the Points to Original
                $reverted_status = addDebitAfterTransactionVoidRevoked($latestTransation);
                if (!$reverted_status) {
                    DB::rollback();
                    Log::info(__METHOD__ . "(Line: " . __LINE__ . ") - " . "Reward Point does not exists for Transaction ID: " . $transaction->id);
                    $message = trans('message.reward_point_deficit_error');
                    return renderResponse(FAILED, $message, null);
                }

                // Revert Back the Points Against the Transaction
                revertPointsAganistTransaction($latestTransation, 0);

                // Void Revoke Lottery Rewards if the User has won Lottery
                $this->voidRevokeLotteryReward($latestTransation, 0);
            }

            DB::commit();
            Log::info(__METHOD__ . "(Line: " . __LINE__ . ") - " . "Transaction ID: " . $transaction->id . " revoked successfully.");
            $message = trans('message.transaction_void_revoked');
            return renderResponse(SUCCESS, $message, null);
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(Line: " . __LINE__ . ") - " . "Exception while storing transaction details.", [EXCEPTION => $e]);
            DB::rollback();
        }
    }

    private function voidRevokeLotteryReward($transaction, $is_voided)
    {
        Log::info(__METHOD__ . "(Line: " . __LINE__ . ") - " . "Lottery details updation started for Transaction ID: " . $transaction->id);

        $check_user_lottery = LotteryUserReward::join('rewards', 'lottery_user_rewards.reward_id', '=', 'rewards.id')
            ->join(config('app.main_db') . '.transaction_details', 'transaction_details.id', '=', 'rewards.transaction_id')
            ->join(config('app.main_db') . '.terminal_master', 'terminal_master.id', '=', 'transaction_details.terminal_id')
            ->leftjoin('reward_wheels', 'rewards.reward_wheel_id', '=', 'reward_wheels.id')
            ->select('lottery_user_rewards.*', 'rewards.reward_wheel_id', 'terminal_master.merchant_store_id', 'reward_wheels.is_merchant_funded')
            ->where(['lottery_user_rewards.user_id' => $transaction->consumer_id, 'rewards.transaction_id' => $transaction->id])
            ->where('rewards.is_cashback_points', 0)
            ->first();

        if (!empty($check_user_lottery)) {
            // Remove the Lottery Reward if the Transaction is Voided
            $check_user_lottery->is_voided = $is_voided;
            $check_user_lottery->save();

            if ($check_user_lottery->winner == 1) {
                // Add Data in user_reward_usage_history
                $user_reward_usage_history = new UserRewardUsageHistory();
                $user_reward_usage_history->user_id = $transaction->consumer_id;
                $user_reward_usage_history->reward_id = $check_user_lottery->reward_id;
                $user_reward_usage_history->reward_wheel_id = $check_user_lottery->reward_wheel_id;
                $user_reward_usage_history->corporate_parent_id = getCorporateParentIdByStore($check_user_lottery->merchant_store_id);
                $user_reward_usage_history->entry_type = $is_voided == 1 ? DEBIT : CREDIT;
                $user_reward_usage_history->transaction_id = $transaction->id;
                $user_reward_usage_history->reason = $is_voided == 1 ? TRANSACTION_VOIDED : TRANSACTION_VOID_REVOKED;
                $user_reward_usage_history->reward_point = $check_user_lottery->winning_reward_point;
                $user_reward_usage_history->reward_point_left = $is_voided == 1 ? 0 : $check_user_lottery->winning_reward_point;
                $user_reward_usage_history->exchange_rate = config('app.exchange_rate');
                $user_reward_usage_history->reward_amount = $check_user_lottery->winning_amount;
                $user_reward_usage_history->reward_amount_left = $is_voided == 1 ? 0 : $check_user_lottery->winning_amount;
                $user_reward_usage_history->is_generic_point = 1;
                $user_reward_usage_history->is_merchant_funded = $check_user_lottery->is_merchant_funded ? $check_user_lottery->is_merchant_funded : 0;
                $user_reward_usage_history->save();
            }

            if ($is_voided == 0) {
                $transaction = Reward::join(config('app.main_db') . '.transaction_details', 'rewards.transaction_id', '=', 'transaction_details.id')->where('rewards.id', $check_user_lottery->reward_id)->first();
                $success = getStatus(SUCCESS);
                if (!empty($transaction) && $transaction->status_id != $success) {
                    $pending = getStatus(PENDING);
                    $rewards = Reward::find($check_user_lottery->reward_id);
                    $rewards->status_id = $pending;
                    $rewards->save();
                }
            }

            Log::info(__METHOD__ . "(Line: " . __LINE__ . ") - " . "Lottery details updated for Transaction ID: " . $transaction->id);
        } else {
            Log::info(__METHOD__ . "(Line: " . __LINE__ . ") - " . "Lottery not found aganist Transaction ID: " . $transaction->id);
        }
    }

    private function _createTransaction($transaction, $status_id, $data)
    {
        $voided = getStatus(VOIDED);
        $transaction_details = new TransactionDetails();
        $transaction_details->transaction_number = $transaction->transaction_number;
        $transaction_details->transaction_ref_no = $transaction->id;
        $transaction_details->user_id = $transaction->user_id;
        $transaction_details->consumer_id = $transaction->consumer_id;
        $transaction_details->terminal_id = $transaction->terminal_id;
        $transaction_details->transaction_time = $transaction->transaction_time;
        $transaction_details->local_transaction_time = $transaction->local_transaction_time;
        $transaction_details->local_transaction_year = date("Y", strtotime($transaction_details->local_transaction_time));
        $transaction_details->local_transaction_month = date("m", strtotime($transaction_details->local_transaction_time));
        $transaction_details->local_transaction_date = date("Y-m-d", strtotime($transaction_details->local_transaction_time));
        $transaction_details->timezone_id = $transaction->timezone_id;
        $transaction_details->amount = $transaction->amount;
        $transaction_details->tip_amount = $transaction->tip_amount;
        $transaction_details->tip_type = $transaction->tip_type;
        $transaction_details->pp_type = $transaction->pp_type;
        $transaction_details->pp_value = $transaction->pp_value;
        $transaction_details->pp_source = $transaction->pp_source;
        $transaction_details->tip_add_time = $transaction->tip_add_time;
        $transaction_details->used_qr_id = $transaction->used_qr_id;
        $transaction_details->status_id = $status_id;
        $transaction_details->account_id = $transaction->account_id;
        $transaction_details->authcode = $transaction->authcode;
        $transaction_details->zipline_session_id = $transaction->zipline_session_id;
        $transaction_details->zipline_trans_id = $transaction->zipline_trans_id;
        $transaction_details->bank_balance_at_transation_time = $transaction->bank_balance_at_transation_time;
        $transaction_details->transaction_type_id = $transaction->transaction_type_id;
        if ($status_id == $voided) {
            $time = Carbon::now()->addSeconds(1);
            $transaction_details->voided_time = $transaction->voided_time;
            $transaction_details->voided_by = $transaction->voided_by;
            $transaction_details->voided_local_time = $transaction->voided_local_time;
            $transaction_details->comment = $transaction->comment;
            $transaction_details->created_at = $time;
            $transaction_details->updated_at = $time;
        } else {
            $time = Carbon::now()->addSeconds(2);
            $transaction_details->comment = isset($data['comment']) ? $data['comment'] : null;
            $transaction_details->created_at = $time;
            $transaction_details->updated_at = $time;
        }
        $transaction_details->save();
        Log::info(__METHOD__ . "(Line: " . __LINE__ . ") - " . "Status ID: " . $status_id . " for Transaction ID: " . $transaction->id . "inserted successfully.");
    }

    private function _postTransactionIntoAcheck21($transaction, $params)
    {
        $params['amount'] = $transaction->amount + $transaction->tip_amount;
        $params['consumer_id'] = $transaction->consumer_id;

        // Fetch the Status ID
        $failed = getStatus(FAILED);
        $success = getStatus(SUCCESS);

        $history = array(
            'transaction_id' => $transaction->id,
            'transaction_posting' => 1,
            'status_id' => $failed,
        );
        //calling the factory function to create consumer transaction into acheck21
        try {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Posting transaction for amount: " . $params['amount']);
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Posting transaction for client id: " . $params['acheck_account_id']);
            $params['account_id'] = $transaction->account_id;
            if (config('app.acheck_posting')) {
                $response = $this->createConsumerTransaction($params);
                $response_decoded = json_decode($response, true);
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is enabled. Posting in ACHECK21.");
            } else {
                $response_decoded['documentId'] = rand(********, ********);
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Acheck Posting mode is disabled. Continue with a random number.");
            }
            //check if transaction posted successfully to acheck21
            if (isset($response_decoded['documentId'])) {
                $response_decoded['transaction_time'] = $transaction->transaction_time;
                $response_decoded['transaction_local_time'] = $transaction->local_transaction_time;
                //store transaction data into database
                $this->_createConsumerTransaction($transaction, $response_decoded);
                // store success log into transaction posting history table
                $history['status_id'] = $success;
                Acheck21HistoryTable::create($history);
            }
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transactions posted into acheck21 successfully.");
        } catch (\Exception $e) {
            Acheck21HistoryTable::create($history);
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Exception occured during Transaction", [EXCEPTION => $e]);
        }
    }

    /**
     * This function calls the api that posts consumer end transactions to acheck21
     * once the response returned from acheck21 it shores a new row to transaction_details table
     */
    private function _createConsumerTransaction($transaction, $data)
    {
        $time = Carbon::now()->addSeconds(3);
        // Fetch the Status ID
        $process_for_acheck21 = getStatus(PROCESSED_FOR_ACHECK21);
        //create a new transaction
        $transaction_details = new TransactionDetails();
        $transaction_details->transaction_number = $transaction->transaction_number;
        $transaction_details->transaction_ref_no = $transaction->id;
        $transaction_details->user_id = $transaction->user_id;
        $transaction_details->consumer_id = $transaction->consumer_id;
        $transaction_details->terminal_id = $transaction->terminal_id;
        $transaction_details->transaction_time = $data['transaction_time'];
        $transaction_details->local_transaction_time = $data['transaction_local_time'];
        $transaction_details->local_transaction_year = date("Y", strtotime($transaction_details->local_transaction_time));
        $transaction_details->local_transaction_month = date("m", strtotime($transaction_details->local_transaction_time));
        $transaction_details->local_transaction_date = date("Y-m-d", strtotime($transaction_details->local_transaction_time));
        $transaction_details->timezone_id = $transaction->timezone_id;
        $transaction_details->scheduled_posting_date = $transaction->scheduled_posting_date;
        $transaction_details->consumer_bank_posting_amount = $transaction->consumer_bank_posting_amount;
        $transaction_details->reward_amount_used = $transaction->reward_amount_used;
        $transaction_details->reward_point_used = $transaction->reward_point_used;
        $transaction_details->amount = $transaction->amount;
        $transaction_details->tip_amount = $transaction->tip_amount;
        $transaction_details->tip_type = $transaction->tip_type;
        $transaction_details->tip_add_time = $transaction->tip_add_time;
        $transaction_details->used_qr_id = $transaction->used_qr_id;
        $transaction_details->status_id = $process_for_acheck21;
        $transaction_details->transaction_type_id = $transaction->transaction_type_id;
        $transaction_details->transaction_place = ACHECK21;
        $transaction_details->acheck_document_id = $data['documentId'];
        $transaction_details->created_at = $time;
        $transaction_details->updated_at = $time;
        $transaction_details->save();

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction posting data stored into database successfully.");
    }
    /**
     * sandboxTransactionForVendors
     * This API gets called when 3rd party vendors performs a sandbox transaction. Zipline doesn't get called for this case and the whole transaction is handled from CanPay v2 end
     * @param  mixed $params
     * @return void
     */
    public function sandboxTransactionForVendors($params)
    {
        // Check mcc code exists in master tabel or not. If not then add it to master
        $checkMccExists = MccMaster::where('code', $params['mcc'])->first();
        if (empty($checkMccExists)) {
            $mcc_master = new MccMaster();
            $mcc_master->code = $params['mcc'];
            $mcc_master->save();
            $mcc_id = $mcc_master->id;
        } else {
            $mcc_id = $checkMccExists->id;
        }

        // Check if Terminal Unique Identification ID exists in Database and if exists then fetching the Terminal Details
        $terminal_details = TerminalMaster::join('merchant_stores', 'merchant_stores.id', '=', 'terminal_master.merchant_store_id')->leftJoin('timezone_masters', 'timezone_masters.id', '=', 'merchant_stores.timezone_id')->select('terminal_master.*', 'timezone_masters.timezone_name', 'timezone_masters.id as timezone_id')->where('terminal_master.unique_identification_id', $params['terminal_unique_id'])->first();
        if (empty($terminal_details)) {
            // Returning Exception as Terminal Not Found by Unique Identification ID
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No Terminal Unique Identification ID Found during Transaction");
            $message = trans('message.transaction_terminal_unique_id_error');
            return renderResponse(FAIL, $message, null); // Exception Returned
        }

        $transaction_time = Carbon::now();
        $transaction_time_with_timezone = Carbon::now()->timezone($terminal_details->timezone_name);

        // Insertion begins in Transaction Details Table
        $transaction = new TransactionDetails();
        $transaction->transaction_number = generateTransactionId();
        $transaction->terminal_id = $terminal_details->id;
        $transaction->transaction_time = $transaction_time;
        $transaction->local_transaction_time = $transaction_time_with_timezone;
        $transaction->local_transaction_year = date("Y", strtotime($transaction->local_transaction_time));
        $transaction->local_transaction_month = date("m", strtotime($transaction->local_transaction_time));
        $transaction->local_transaction_date = date("Y-m-d", strtotime($transaction->local_transaction_time));
        $transaction->timezone_id = $terminal_details->timezone_id;
        $transaction->amount = $params['amount'];
        $transaction->tip_amount = $params['tip_amount'];
        $transaction->tip_type = 1;
        $status_details = StatusMaster::where('code', SUCCESS)->first(); // Getting Success Status ID for the Transaction
        $transaction->status_id = $status_details->id;
        $transaction->mcc_id = $mcc_id;
        $transaction->pos_web_identifier = $params['pos_web_identifier'];
        $transaction->is_v1 = 1;
        $transaction->v1_transaction_id = $params['transaction_unique_id'];
        $transaction->save();

        // Preparing minified response
        $response = new \stdClass();

        $response->canpay_transaction_number = $transaction->transaction_number;
        $response->transaction_time = $transaction->local_transaction_time;
        $response->amount = $transaction->amount;
        $response->tip_amount = $transaction->tip_amount;

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction Completed Successfully and details are inserted in Database with Transaction ID : " . $transaction->id);
        $message = trans('message.transaction_success');
        return renderResponse(SUCCESS, $message, $response); // API Response returned with 200 status
    }

    public function createConsumerReturnTransaction($params)
    {
        $params['type'] = CONSUMER_CREATE_RETURN_TRANSACTION;
        //get the correct transaction body
        if (config('app.performance_testing_mode') == false) {
            $params['body'] = $this->getAcheckBody($params);
            $response = $this->acheck->createTransaction($params);
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer transaction with body: " . $params['body'] . " returned response: " . $response);
        } else {
            $params['body'] = '{
                "accountNumber": "****************",
                "accountType": "****************",
                "amount": "10",
                "checkNumber": "****************",
                "entryClass": "Test",
                "routingNumber": "****************",
                "individualName": "Test"
              }';
            $response = $this->acheck->createMockTransaction($params);
        }
        return $response;
    }

    public function createCanPayReturnTransaction($params)
    {
        $params['type'] = CANPAY_CREATE_RETURN_TRANSACTION;
        //get the correct transaction body
        $params['body'] = $this->getAcheckBody($params);
        $response = $this->acheck->createTransaction($params);
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "anpay return recovery transaction with body: " . $params['body'] . " returned response: " . $response);
        return $response;
    }
}
