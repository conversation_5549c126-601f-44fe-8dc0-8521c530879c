<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Events\AfterSheet;

class ReturnReportDashboardExport implements FromCollection, WithHeadings, ShouldAutoSize, WithEvents
{
    protected $request;

    public function __construct($request)
    {
        $this->request = $request; // Declaring the request variable
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        $collection_array = $this->request['report_body']; // Storing the array received from request
        $returns = [];
        foreach ($collection_array as $return) {
            $nestedData['heading'] = $return->heading != null ? $return->heading : 'Total Returns';
            $nestedData['total_transaction'] = $return->total_transaction != null ? '$' . number_format($return->total_transaction, 2) : $return->total_transaction;
            $nestedData['transaction_percent'] = $return->transaction_percent;
            $nestedData['transaction_count'] = number_format($return->transaction_count, 2);
            $nestedData['avg_transaction_value'] = $return->avg_transaction_value != null ? '$' . number_format($return->avg_transaction_value, 2) : $return->avg_transaction_value;
            $nestedData['paid_amount'] = $return->paid_amount != null ? '$' . number_format($return->paid_amount, 2) : $return->paid_amount;
            $nestedData['paid_percent'] = $return->paid_percent;
            $nestedData['paid_count'] = number_format($return->paid_count, 2);
            $nestedData['paid_count_percent'] = $return->paid_count_percent;
            $nestedData['pending_amount'] = $return->pending_amount != null ? '$' . number_format($return->pending_amount, 2) : $return->pending_amount;
            $nestedData['pending_percent'] = $return->pending_percent;
            $nestedData['pending_count'] = number_format($return->pending_count, 2);
            $nestedData['pending_count_percent'] = $return->pending_count_percent;
            $nestedData['represented_amount'] = $return->represented_amount != null ? '$' . number_format($return->represented_amount, 2) : $return->represented_amount;
            $nestedData['represented_percent'] = $return->represented_percent;
            $nestedData['represented_count'] = number_format($return->represented_count, 2);
            $nestedData['represented_count_percent'] = $return->represented_count_percent;
            $nestedData['not_represented_amount'] = $return->not_represented_amount != null ? '$' . number_format($return->not_represented_amount, 2) : $return->not_represented_amount;
            $nestedData['not_represented_percent'] = $return->not_represented_percent;
            $nestedData['not_represented_count'] = number_format($return->not_represented_count, 2);
            $nestedData['not_represented_count_percent'] = number_format($return->not_represented_count_percent, 2);

            array_push($returns, $nestedData);
        }

        $report_footer['heading'] = 'Avg Return Value';
        $report_footer['total_transaction'] = '$' . number_format($this->request['report_footer']->avg_return_value, 2);
        array_push($returns, $report_footer);

        $report_footer['heading'] = 'Unpaid Transactions';
        $report_footer['total_transaction'] = '$' . number_format($this->request['report_footer']->unpaid_transaction_amount, 2);
        $report_footer['transaction_percent'] = $this->request['report_footer']->unpaid_transaction_percent . '%';
        array_push($returns, $report_footer);

        $report_footer['heading'] = 'Pending Transactions';
        $report_footer['total_transaction'] = '$' . number_format($this->request['report_footer']->pending_transaction_amount, 2);
        $report_footer['transaction_percent'] = $this->request['report_footer']->pending_transaction_percent . '%';
        array_push($returns, $report_footer);

        $report_footer['heading'] = 'Paid Transactions';
        $report_footer['total_transaction'] = '$' . number_format($this->request['report_footer']->paid_transaction_amount, 2);
        $report_footer['transaction_percent'] = $this->request['report_footer']->paid_transaction_percent . '%';
        array_push($returns, $report_footer);

        return collect([
            $returns,
        ]);
    }

    public function headings(): array
    {
        $returnArray = array(
            [
                'CanPay Return Dashboard ',
            ],
            // 2nd header
            [
                ' Return Dashboard Report Date Range:',
                date('m/d/Y', strtotime($this->request['from_date'])) . ' - ' . date('m/d/Y', strtotime($this->request['to_date'])),
            ],
            [],
            [
                '',
                'Transaction Value',
                '%',
                'Count',
                'Average Transaction Value',
                'Paid',
                '%',
                'Count',
                'Count %',
                'Pending',
                '%',
                'Count',
                'Count %',
                'Reresented But Returned',
                '%',
                'Count',
                'Count %',
                'Not Represented',
                '%',
                'Count',
                'Count %',
            ],
        );

        return $returnArray;
    }

    public function registerEvents(): array
    {
        $column_count = count($this->request['report_body']) + 2;
        $report_body_count = $column_count + 2;
        return [
            AfterSheet::class => function (AfterSheet $event) use ($column_count, $report_body_count) {
                $event->sheet->getStyle('A1:M1')->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);

                $event->sheet->getStyle('A2:U2')->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);

                $event->sheet->getStyle('A4:U4')->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);

                $event->sheet->getStyle('A5:A' . ($column_count + 6))->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);

                //Background Color
                $event->sheet->getDelegate()->getStyle('A4:A' . $report_body_count)
                    ->getFill()
                    ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                    ->getStartColor()
                    ->setARGB('EFEFEF');
                $event->sheet->getDelegate()->getStyle('B4:E' . $report_body_count)
                    ->getFill()
                    ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                    ->getStartColor()
                    ->setARGB('D9EAD3');
                $event->sheet->getDelegate()->getStyle('F4:I' . $report_body_count)
                    ->getFill()
                    ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                    ->getStartColor()
                    ->setARGB('CFE2F3');
                $event->sheet->getDelegate()->getStyle('J4:M' . $report_body_count)
                    ->getFill()
                    ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                    ->getStartColor()
                    ->setARGB('FFF2CC');
                $event->sheet->getDelegate()->getStyle('N4:Q' . $report_body_count)
                    ->getFill()
                    ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                    ->getStartColor()
                    ->setARGB('F4CCCC');
                $event->sheet->getDelegate()->getStyle('R4:U' . $report_body_count)
                    ->getFill()
                    ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                    ->getStartColor()
                    ->setARGB('D9D2E9');
                $event->sheet->getDelegate()->getStyle('A' . ($report_body_count + 1) . ':U' . ($report_body_count + 2))
                    ->getFill()
                    ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                    ->getStartColor()
                    ->setARGB('F4CCCC');
                $event->sheet->getDelegate()->getStyle('A' . ($report_body_count + 3) . ':U' . ($report_body_count + 4))
                    ->getFill()
                    ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
                    ->getStartColor()
                    ->setARGB('FFF2CC');

                //Apply Borders in Cell
                $event->sheet->getStyle('A4:U' . ($report_body_count + 4))->applyFromArray([
                    'borders' => [
                        'allBorders' => [
                            'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                            'color' => ['argb' => '000000'],
                        ],
                    ],
                ]);

                //Apply Center Alignment
                $event->sheet->getStyle('A:U')->getAlignment()->applyFromArray(
                    array('horizontal' => 'center')
                );
            },
        ];
    }
}
