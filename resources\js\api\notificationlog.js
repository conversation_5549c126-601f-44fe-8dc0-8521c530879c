const notificationLogs = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/searchnotifications', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

const resendNotification = (request) => {
    return new Promise((res, rej) => {
        axios.post('api/resendnotification', request)
            .then((response) => {
                res(response.data);
            })
            .catch((err) => {
                rej(err);
            })
    })
};

export default {
    notificationLogs,
    resendNotification
};