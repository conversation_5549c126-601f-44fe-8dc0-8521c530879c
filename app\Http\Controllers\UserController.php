<?php

// PostController.php

namespace App\Http\Controllers;

use App\Exports\AllConsumersWitProbableReturnExport;
use App\Exports\SuspectedConsumersReportExport;
use App\Exports\UsersEnrollmentExport;
use App\Http\Clients\ApiHttpClient;
use App\Http\Factories\Akoya\AkoyaFactory;
use App\Http\Factories\BankValidator\BankValidatorFactory;
use App\Http\Factories\EmailExecutor\EmailExecutorFactory;
use App\Http\Factories\Firebase\FirebaseFactory;
use App\Http\Factories\Mx\MxFactory;
use App\Http\Factories\PurchasePower\PurchasePowerFactory;
use App\Models\AccessLevelMaster;
use App\Models\BankAccountInfo;
use App\Models\BankDetailsForProbableReturn;
use App\Models\CognitoRulesLog;
use App\Models\ConsumerAccountBalance;
use App\Models\ConsumerComment;
use App\Models\ConsumerPurchasePowerSourceHistory;
use App\Models\ConsumerSessionManagement;
use App\Models\ConsumerStatusUpdateHistory;
use App\Models\CustomPurchasePowerHistory;
use App\Models\FinancialInstitutionDeleteHistory;
use App\Models\GlobalRadarReview;
use App\Models\LandingPageInfo;
use App\Models\ManualReviewDetail;
use App\Models\MerchantStoreCashbackMap;
use App\Models\MerchantStoreCashbackProgram;
use App\Models\MerchantStores;
use App\Models\MicrobiltRuleMatchHistory;
use App\Models\Petition;
use App\Models\RegisteredMerchantMaster;
use App\Models\RewardStoreMap;
use App\Models\StatusMaster;
use App\Models\StoreUserMap;
use App\Models\TransactionDetails;
use App\Models\User;
use App\Models\UserBankAccountInfo;
use App\Models\UserBankAccountOwnerInfo;
use App\Models\UserBankAccountOwnerInfoMatchDetail;
use App\Models\UserCurrentRewardDetail;
use App\Models\UserRewardUsageHistory;
use App\Models\UserRole;
use App\Models\UsersEnrollmentReview;
use App\Models\ValidAccountNumber;
use App\Models\VoidMenuDisableStore;
use Carbon\Carbon;
use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use JWTAuth;
use Maatwebsite\Excel\Facades\Excel;

class UserController extends Controller
{

    public function __construct(Request $request)
    {
        $this->request = $request;
        $this->emailexecutor = new EmailExecutorFactory();
        $this->firebase = new FirebaseFactory();
        $this->bankvalidator = new BankValidatorFactory();
        $this->purchasePower = new PurchasePowerFactory();
        $this->akoyaFactory = new AkoyaFactory();
        $this->mxFactory = new MxFactory();
        $this->client = new Client();
    }
    /**
     * Updates users informations
     */
    public function updateUser()
    {
        $rule = array(
            'first_name' => VALIDATION_REQUIRED,
            'last_name' => VALIDATION_REQUIRED,
            'phone' => VALIDATION_REQUIRED . '|regex:' . PHONE_NUMBER_REGEX,
        );
        $this->__validate($this->request->all(), $rule);
        $data = $this->request->all();

        //updating the user details
        $user = Auth::user();
        DB::beginTransaction();
        try {
            $user = User::find($user["user_id"]);
            $user->first_name = $data['first_name'];
            $user->middle_name = $data['middle_name'];
            $user->last_name = $data['last_name'];
            $user->phone = $data['phone'];
            $user->date_of_birth = $data['date_of_birth'];
            $user->street_address = $data['street_address'];
            $user->state = $data['state'];
            $user->zipcode = $data['zipcode'];
            $user->city = $data['city'];
            $user->ssn_number = $data['ssn_number'];
            $user->save();
            DB::commit();
            $message = trans('message.user_update_success');
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . $message . $user);
            return renderResponse(SUCCESS, $message, $user);
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception caused while updating user details.", [EXCEPTION => $e]);
            DB::rollback();
            $message = trans('message.db_transaction_failed');
            return renderResponse(FAIL, $message, $e);
        }
    }

    /**
     * addCorporateParent
     * This function will add Corporate Parents along with Store mapping
     * @param  mixed $request
     * @return void
     */
    public function addCorporateParent(Request $request)
    {
        $rule = array(
            'contact_person_first_name' => VALIDATION_REQUIRED,
            'contact_person_last_name' => VALIDATION_REQUIRED,
            'contact_person_email' => VALIDATION_REQUIRED . ' | ' . VALIDATION_EMAIL,
            'contact_person_phone' => VALIDATION_REQUIRED . '|regex:' . PHONE_NUMBER_REGEX,
            'first_name' => VALIDATION_REQUIRED,
            'corporate_url' => VALIDATION_REQUIRED_WITH_ALL . ':display_name,corporate_vanity_url,corporate_color,logo_url',
            'display_name' => VALIDATION_REQUIRED_WITH_ALL . ':corporate_url,corporate_vanity_url,corporate_color,logo_url',
            'corporate_vanity_url' => VALIDATION_REQUIRED_WITH_ALL . ':corporate_color,logo_url,display_name,corporate_url',
            'corporate_color' => VALIDATION_REQUIRED_WITH_ALL . ':corporate_vanity_url,logo_url,display_name,corporate_url',
            'logo_url' => VALIDATION_REQUIRED_WITH_ALL . ':corporate_vanity_url,corporate_color,display_name,corporate_url',
        );
        $this->__validate($request->all(), $rule);

        $user = User::join('user_roles', 'users.role_id', '=', 'user_roles.role_id')->where('user_roles.role_name', '!=', CONSUMER)->whereRaw('(users.email = ? OR users.contact_person_email = ? OR users.contact_person_email = ?)', [$request->get('contact_person_email'), $request->get('contact_person_email'), $request->get('contact_person_email')])->first();

        if (empty($user)) {
            $role_name = ($request->get('cp_type') == BRAND) ? BRAND_ADMIN : CORPORATE_PARENT;
            $role_details = UserRole::where('role_name', $role_name)->first(); // Fetching Role ID with respect to User Type
            $user_details = Auth::user(); // Fetching Logged In User Details
            $password = substr(md5(microtime()), rand(0, 26), 7); // Generating Password
            $email = $request->get('email');

            //Fetch the Active Status ID from Status Master table (Set User Status to Default)
            $user_status = StatusMaster::where('code', USER_ACTIVE)->first();

            DB::beginTransaction();
            try {
                // Insertion begins in User Table
                $user = new User();
                $user->email = $email;
                $user->username = $request->get('username');
                $user->first_name = $request->get('first_name');
                $user->phone = $request->get('phone');
                $user->password = Hash::make($password);
                $user->contact_person_first_name = $request->get('contact_person_first_name');
                $user->contact_person_last_name = $request->get('contact_person_last_name');
                $user->contact_person_email = $request->get('contact_person_email');
                $user->contact_person_phone = $request->get('contact_person_phone');
                $user->role_id = $role_details->role_id;
                $user->added_by = $user_details->user_id;
                $user->status = $user_status->id;
                $user->show_void_transaction_menu = $request->get('show_void_transaction_menu');
                $user->save();

                if ($request->get('store_ids')) {
                    // Extract the `id` values from $current_store_ids
                    $new_store_ids = array_map(function ($store) {
                        return $store['id'];
                    }, $request->get('store_ids'));

                    if (!empty($new_store_ids)) {
                        replaceNonDefaultCorporateParents($new_store_ids, $user->user_id);
                    }

                    // Mapping Creation started in Store User Map
                    $access_level_details = AccessLevelMaster::where('label', ADMIN_RIGHT)->first();
                    foreach ($request->get('store_ids') as $stores) {
                        $storeusermap = new StoreUserMap();
                        $storeusermap->store_id = $stores['id'];
                        $storeusermap->user_id = $user->user_id;
                        $storeusermap->user_access_level_id = $access_level_details->id;
                        $storeusermap->save();

                        if ($request->get('show_void_transaction_menu')) {
                            $voidmenudisablestore = new VoidMenuDisableStore();
                            $voidmenudisablestore->store_id = $stores['id'];
                            $voidmenudisablestore->user_id = $user->user_id;
                            $voidmenudisablestore->save();
                        }
                    }
                    $petition_ids = MerchantStores::whereIn('id', $new_store_ids)->whereNotNull('petition_id')->distinct()->pluck('petition_id')->toArray();
                    if (!empty($petition_ids)) {
                        updateStorePetitionPoints($petition_ids, $user->user_id);
                    }
                }

                if ($request->get('corporate_vanity_url')) {
                    $landingPageInfo = new LandingPageInfo();
                    $theColor = $request->get('corporate_color');
                    if ($theColor[0] != "#") {
                        $theColor = '#' . $theColor;
                    }
                    $landingPageInfo->corporate_name = $request->get('first_name');
                    $landingPageInfo->corporate_url = $request->get('corporate_url');
                    $landingPageInfo->display_name = $request->get('display_name');
                    $landingPageInfo->vanity_url = $request->get('corporate_vanity_url');
                    $landingPageInfo->corporate_color = $theColor;
                    $landingPageInfo->logo_url = $request->get('logo_url');
                    $landingPageInfo->logo_original_name = $request->get('logo_original_name');
                    $landingPageInfo->corporate_parent_user_id = $user->user_id;
                    $landingPageInfo->active = $request->get('active');
                    $landingPageInfo->save();
                }

                DB::commit();

                // Sending Mail with password
                $params = [
                    'user_id' => $user->user_id,
                    'password' => $password,
                ];
                $this->emailexecutor->corporateParentWelcomeEmail($params);

                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "User (" . CORPORATE_PARENT . ") Created Successfully and inserted in Database with User ID : " . $user->id . " by User ID : " . $user_details->user_id);
                $message = trans('message.user_cp_creation_success');
                // API Response returned with 200 status
                return renderResponse(SUCCESS, $message, $user);
            } catch (\Exception $e) {
                Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured during User (" . CORPORATE_PARENT . ") Creation By User ID :  " . $user_details->user_id . ".", [EXCEPTION => $e]);
                DB::rollback();
                $message = trans('message.user_cp_creation_error');
                // Exception Returned
                return renderResponse(FAIL, $message, null);
            }
        } else {
            $message = trans('message.email_not_available');
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . $message);
            return renderResponse(FAIL, $message, $request->get('contact_person_email'));
        }
    }

    /**
     * editCorporateParent
     * This function will update Corporate Parents along with Store mapping
     * @param  mixed $request
     * @return void
     */
    public function editCorporateParent(Request $request)
    {
        $rule = array(
            'contact_person_first_name' => VALIDATION_REQUIRED,
            'contact_person_last_name' => VALIDATION_REQUIRED,
            'contact_person_email' => VALIDATION_REQUIRED . ' | ' . VALIDATION_EMAIL,
            'contact_person_phone' => VALIDATION_REQUIRED . '|regex:' . PHONE_NUMBER_REGEX,
            'first_name' => VALIDATION_REQUIRED,
            'user_id' => VALIDATION_REQUIRED,
            'corporate_url' => VALIDATION_REQUIRED_WITH_ALL . ':display_name,corporate_vanity_url,corporate_color',
            'display_name' => VALIDATION_REQUIRED_WITH_ALL . ':corporate_url,corporate_vanity_url,corporate_color',
            'corporate_vanity_url' => VALIDATION_REQUIRED_WITH_ALL . ':corporate_color,display_name,corporate_url',
            'corporate_color' => VALIDATION_REQUIRED_WITH_ALL . ':corporate_vanity_url,display_name,corporate_url',
            'logo_url' => VALIDATION_REQUIRED_WITH_ALL . ':corporate_vanity_url,corporate_color,display_name,corporate_url',

        );
        $this->__validate($request->all(), $rule);

        $user_details = Auth::user(); // Fetching Logged In User Details

        // Updation begins in User Table
        $user = User::find($request->get('user_id'));
        $email = $user->contact_person_email;
        // check if need to update to new email id
        if ($email != $request->get('contact_person_email')) { //if emails are not same
            $other_user_exists = User::join('user_roles', 'users.role_id', '=', 'user_roles.role_id')->where('user_roles.role_name', '!=', CONSUMER)->where('users.user_id', '!=', $request->get('user_id'))->whereRaw('(users.email = ? OR users.contact_person_email = ?)', [$request->get('contact_person_email'), $request->get('contact_person_email')])->first();
        } else {
            $other_user_exists = null;
        }

        // Check if the Store is Used for Any Reward Wheel, then Admin cannot remove that store
        $check_reward_wheel_exists_for_stores = RewardStoreMap::join(config('app.main_db') . '.merchant_stores', 'reward_store_maps.store_id', '=', 'merchant_stores.id')->join('reward_wheels', 'reward_wheels.id', '=', 'reward_store_maps.reward_wheel_id')->select('merchant_stores.retailer', 'reward_wheels.reward_wheel')->whereIn('reward_store_maps.store_id', $request->get('deleted_store_ids'))->get();
        if (count($check_reward_wheel_exists_for_stores) > 0) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Store is already used for Any Reward Wheel.");
            $message = trans('message.store_used_for_reward_wheel');
            return renderResponse(FAIL, $message, $check_reward_wheel_exists_for_stores);
        }

        if (empty($other_user_exists)) {
            $active = getStatus(ACTIVE_CODE);
            DB::beginTransaction();
            try {
                
                $user->first_name = $request->get('first_name');
                $user->phone = $request->get('phone');
                $user->status = $request->get('status');
                $user->contact_person_first_name = $request->get('contact_person_first_name');
                $user->contact_person_last_name = $request->get('contact_person_last_name');
                $user->contact_person_email = $request->get('contact_person_email');
                $user->contact_person_phone = $request->get('contact_person_phone');
                $user->show_void_transaction_menu = $request->get('show_void_transaction_menu');
                $user->save();

                // check if need to update to new email id
                if ($email != $request->get('contact_person_email')) { //if emails are not same
                    Log::debug("email does not match");
                    // generate and save new password
                    $password = substr(md5(microtime()), rand(0, 26), 7); // Generating Password
                    $user->password = Hash::make($password);
                    $user->save();
                    $params = [
                        'user_id' => $user->user_id,
                        'password' => $password,
                    ];
                    //send email to the corporate parent
                    $this->emailexecutor->corporateParentEmailChange($params);
                }
                $previous_store_ids = StoreUserMap::where('user_id', $user->user_id)->whereNotIn('store_id', $request->get('deleted_store_ids'))->distinct()->pluck('store_id')->toArray();
                $current_store_ids = $request->get('store_ids');

                // Extract the `id` values from $current_store_ids
                $current_store_ids_array = array_map(function ($store) {
                    return $store['id'];
                }, $current_store_ids);

                // Find the new store IDs that are in $current_store_ids_array but not in $previous_store_ids
                $new_store_ids = array_diff($current_store_ids_array, $previous_store_ids);

                if (!empty($new_store_ids)) {
                    replaceNonDefaultCorporateParents($new_store_ids, $user->user_id);
                }

                // Delete Previous Records
                $deleteOldData = StoreUserMap::where('user_id', $user->user_id)->delete();
                if (!empty($request->get('deleted_store_ids'))) {
                    $deleted_store_ids = array_unique($request->get('deleted_store_ids'));
                    MerchantStoreCashbackMap::whereIn('store_id', $request->get('deleted_store_ids'))->delete();
                    replaceNonDefaultCorporateParents($deleted_store_ids, $user->user_id, true);
                    foreach ($deleted_store_ids as $deleted_store_id) {
                        createDefaultCpUser($deleted_store_id, $user->user_id);
                    }
                }
                if (!empty($request->get('store_ids'))) {
                    // Mapping Creation started in Store User Map
                    $access_level_details = AccessLevelMaster::where('label', ADMIN_RIGHT)->first();
                    // If previous deletion of data is successfull then only proceed
                    foreach ($request->get('store_ids') as $stores) {
                        $storeusermap = new StoreUserMap();
                        $storeusermap->store_id = $stores['id'];
                        $storeusermap->user_id = $user->user_id;
                        $storeusermap->user_access_level_id = $access_level_details->id;
                        $storeusermap->save();
                    }

                    if (!empty($new_store_ids)) {

                        $add_store_cashback_programs = MerchantStoreCashbackProgram::where('corporate_parent_id', $user->user_id)->where('status_id', $active)->where('add_new_store', 1)->get();
                        if (!empty($add_store_cashback_programs)) {
                            $chunkSize = 50;
                            $insert_store_array = [];
                            $current_time = Carbon::now();
                            foreach ($add_store_cashback_programs as $add_store_cashback_program) {
                                foreach ($new_store_ids as $store_id) { // Loop through the store_ids so that every transaction data get stored against each store
                                    $queue = [];
                                    $queue['id'] = generateUUID();
                                    $queue['merchant_store_cashback_program_id'] = $add_store_cashback_program->id;
                                    $queue['store_id'] = $store_id;
                                    $queue['created_at'] = $current_time;
                                    $queue['updated_at'] = $current_time;
                                    $insert_store_array[] = $queue;
                                    if (count($insert_store_array) == $chunkSize) {
                                        MerchantStoreCashbackMap::insert($insert_store_array); // Insert in merchant_store_cashback_details table
                                        $insert_store_array = [];
                                    }
                                }
                            }
                            if (!empty($insert_store_array)) { // Processing the remaining data.
                                MerchantStoreCashbackMap::insert($insert_store_array);
                            }
                        }
                        $petition_ids = MerchantStores::whereIn('id', $new_store_ids)->whereNotNull('petition_id')->distinct()->pluck('petition_id')->toArray();
                        if (!empty($petition_ids)) {
                            updateStorePetitionPoints($petition_ids, $user->user_id);
                        }
                    }
                }

                // Delete Previous Records
                $deleteOldDataVoidMenu = VoidMenuDisableStore::where('user_id', $user->user_id)->delete();
                if ($request->get('show_void_transaction_menu')) {
                    // If previous deletion of data is successfull then only proceed
                    foreach ($request->get('store_ids') as $stores) {
                        $voidmenudisablestore = new VoidMenuDisableStore();
                        $voidmenudisablestore->store_id = $stores['id'];
                        $voidmenudisablestore->user_id = $user->user_id;
                        $voidmenudisablestore->save();
                    }
                }
                // Delete Previous Records
                $landingPageInfo = LandingPageInfo::where('corporate_parent_user_id', $user->user_id)->first();
                if (!empty($landingPageInfo)) {
                    if ($request->get('corporate_vanity_url')) {
                        if ($landingPageInfo == null) {
                            $landingPageInfo = new LandingPageInfo();
                            $landingPageInfo->corporate_parent_user_id = $user->user_id;
                        }
                        $theColor = $request->get('corporate_color');
                        if ($theColor[0] != "#") {
                            $theColor = '#' . $theColor;
                        }
                        $landingPageInfo->corporate_name = $request->get('first_name');
                        $landingPageInfo->corporate_url = $request->get('corporate_url');
                        $landingPageInfo->display_name = $request->get('display_name');
                        $landingPageInfo->vanity_url = $request->get('corporate_vanity_url');
                        $landingPageInfo->corporate_color = $theColor;
                        $landingPageInfo->logo_url = $request->get('logo_url');
                        $landingPageInfo->logo_original_name = $request->get('logo_original_name');
                        $landingPageInfo->active = $request->get('active');

                        $landingPageInfo->save();
                    } else {
                        $landingPageInfo->corporate_name = "";
                        $landingPageInfo->corporate_url = "";
                        $landingPageInfo->display_name = "";
                        $landingPageInfo->vanity_url = "";
                        $landingPageInfo->corporate_color = "";
                        $landingPageInfo->logo_url = $request->get('logo_url');
                        $landingPageInfo->logo_original_name = $request->get('logo_original_name');
                        $landingPageInfo->active = false;
                        $landingPageInfo->save();
                    }
                }
                DB::commit();
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "User (" . CORPORATE_PARENT . ") Updated Successfully User ID : " . $user->id . " by User ID : " . $user_details->user_id);
                $message = trans('message.user_cp_updation_success');
                // API Response returned with 200 status
                return renderResponse(SUCCESS, $message, $user);
            } catch (\Exception $e) {
                Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured during User (" . CORPORATE_PARENT . ") Updation By User ID :  " . $user_details->user_id . ".", [EXCEPTION => $e]);
                DB::rollback();
                $message = trans('message.user_cp_updation_error');
                // Exception Returned
                return renderResponse(FAIL, $message, null);
            }
        } else {
            $message = trans('message.email_not_available');
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . $message);
            return renderResponse(FAIL, $message, null);
        }
    }

    /**
     * getMerchantStores
     * Get all merchant stores which is not assigned to any corporate parents till now.
     * @param  mixed $request
     * @return void
     */
    public function getMerchantStores(Request $request)
    {
        //Fetch active status ID from status master
        $status = StatusMaster::where('status', ACTIVE)->first();
        $status_id = $status->id;

        $user_id = $request->get('user_id'); // Needed for Update function as we need to show the selected stores also
        if ($user_id) {
            $stores = DB::select("SELECT ms.id, CONCAT(ms.retailer, if(ms.state IS NOT NULL,' - ', ''), COALESCE(ms.state, '')) as retailer, rmm.type FROM merchant_stores ms JOIN registered_merchant_master rmm ON ms.merchant_id = rmm.id where ms.id NOT IN (SELECT DISTINCT(sm.store_id) FROM store_user_map sm WHERE sm.user_id IN (SELECT u.user_id FROM users u INNER JOIN user_roles ur ON u.role_id = ur.role_id WHERE ur.role_name in ('" . CORPORATE_PARENT . "', '" . BRAND_ADMIN . "') AND u.user_id != ? AND u.default_cp = 0)) AND status = '" . $status->id . "'", [$user_id]);
        } else {
            $petition_id = $request->get('petition_id'); 
            if ($petition_id) {
                $petition = Petition::find($petition_id);
                if ($petition) {
                    $stores = DB::select("SELECT ms.id, CONCAT( ms.retailer, IF(ms.address IS NOT NULL AND ms.address != '', ' - ', ''), COALESCE(ms.address, ''), IF(ms.city IS NOT NULL AND ms.city != '', ', ', ''), COALESCE(ms.city, ''), IF(ms.state IS NOT NULL AND ms.state != '', ', ', ''), COALESCE(ms.state, ''), IF(ms.zip IS NOT NULL AND ms.zip != '', ' ', ''), COALESCE(ms.zip, '') ) AS retailer, rmm.type FROM merchant_stores ms JOIN registered_merchant_master rmm ON ms.merchant_id = rmm.id where status = '" . $status->id . "' AND ms.created_at >= '" . $petition->created_at . "' AND ms.petition_id IS NULL");
                } else {
                    $message = trans('message.petition_not_found');
                    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . $message);
                    return renderResponse(FAIL, $message, null);
                }
            } else {
                $stores = DB::select("SELECT ms.id, CONCAT(ms.retailer, if(ms.state IS NOT NULL,' - ', ''), COALESCE(ms.state, '')) as retailer, rmm.type FROM merchant_stores ms JOIN registered_merchant_master rmm ON ms.merchant_id = rmm.id where ms.id NOT IN (SELECT DISTINCT(sm.store_id) FROM store_user_map sm WHERE sm.user_id IN (SELECT u.user_id FROM users u INNER JOIN user_roles ur ON u.role_id = ur.role_id WHERE ur.role_name in ('" . CORPORATE_PARENT . "', '" . BRAND_ADMIN . "') AND u.default_cp = 0 )) AND status = '" . $status->id . "'");
            }
        }
        $message = trans('message.store_fetch_success');
        return renderResponse(SUCCESS, $message, $stores);
    }

    /**
     * getAllCorporateParents
     * Listing page for Corporate parents
     * @param  mixed $request
     * @return void
     */
    public function getAllCorporateParents(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Corporate Parent search started...");
        // Validating input request
        $this->validate($request, [
            'corporate_parent' => VALIDATION_REQUIRED_WITHOUT_ALL . ':phone_no,email',
            'phone_no' => VALIDATION_REQUIRED_WITHOUT_ALL . ':email,corporate_parent',
            'email' => VALIDATION_REQUIRED_WITHOUT_ALL . ':phone_no,corporate_parent',
        ]);

        //Search with in Corpoarate Parents
        $corporateParents = $this->_getCorporateParentSearch($request);

        $message = trans('message.corporate_parent_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Corporate Parent search started complete.");
        return renderResponse(SUCCESS, $message, $corporateParents);
    }

    /**
     * _getCorporateParentSearch
     * Fetch the Corporate Parents
     * @param  mixed $searchArray
     * @return void
     */
    private function _getCorporateParentSearch($request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Corporate Parent Search Started.");

        $sql = "SELECT `users`.*, `status_master`.`status` AS `status_name`, landing_page_info.active AS active, landing_page_info.display_name AS display_name, landing_page_info.corporate_url AS corporate_url, landing_page_info.logo_original_name AS logo_name, landing_page_info.logo_url AS logo, landing_page_info.corporate_color AS cc, landing_page_info.vanity_url AS vu, GROUP_CONCAT(merchant_stores.id SEPARATOR ',') AS store_ids, GROUP_CONCAT(CONCAT(merchant_stores.retailer, ' - ', merchant_stores.state) SEPARATOR ',') AS retailers, GROUP_CONCAT(registered_merchant_master.type SEPARATOR ',') AS merchant_types, GROUP_CONCAT(registered_merchant_master.freeze_sponsor_points SEPARATOR ',') AS freeze_stores
        FROM `users`
        INNER JOIN `user_roles` ON `users`.`role_id` = `user_roles`.`role_id`
        INNER JOIN `status_master` ON `status_master`.`id` = `users`.`status`
        LEFT JOIN `store_user_map` ON `users`.`user_id` = `store_user_map`.`user_id`
        LEFT JOIN `merchant_stores` ON `merchant_stores`.`id` = `store_user_map`.`store_id`
        LEFT JOIN `registered_merchant_master` ON `registered_merchant_master`.`id` = `merchant_stores`.`merchant_id`
        LEFT JOIN `landing_page_info` ON `landing_page_info`.`corporate_parent_user_id` = `users`.`user_id`
        WHERE `user_roles`.`role_name` IN ('" . CORPORATE_PARENT . "', '" . BRAND_ADMIN . "') AND users.default_cp = 0 ";

        $searchStr = [];
        if (strlen(trim($request['corporate_parent'])) >= 3) {
            $corporate_parent = $request['corporate_parent'];
            $sql .= ' AND users.first_name LIKE ? ';
            array_push($searchStr, '%' . $request['corporate_parent'] . '%');
        }
        if (trim($request['phone_no'])) {
            $sql .= " AND users.contact_person_phone = ? ";
            array_push($searchStr, $request['phone_no']);
        }
        if (trim($request['email'])) {
            $sql .= " AND users.contact_person_email = ? ";
            array_push($searchStr, $request['email']);
        }
        $sql .= " GROUP BY `users`.`user_id`, `users`.`user_id` ORDER BY `users`.`created_at` DESC LIMIT 100";
        $corporateParents = DB::connection(MYSQL_RO)->Select($sql, $searchStr);

        $corporateParentsArr = [];
        if (!empty($corporateParents)) {
            foreach ($corporateParents as $user) {

                $data = [];
                $data['name'] = $user->first_name;
                $data['first_name'] = $user->first_name;
                $data['middle_name'] = $user->middle_name;
                $data['last_name'] = $user->last_name;
                $data['phone'] = $user->phone;
                $data['email'] = $user->email;
                $data['username'] = $user->username;
                $data['contact_person_first_name'] = $user->contact_person_first_name;
                $data['contact_person_last_name'] = $user->contact_person_last_name;
                $data['contact_person_email'] = $user->contact_person_email;
                $data['contact_person_phone'] = $user->contact_person_phone;
                $data['edit'] = $user->user_id;
                $data['status_name'] = str_replace('User ', '', $user->status_name);
                $data['status'] = $user->status;
                $data['created_at'] = date('m-d-Y h:i A', strtotime($user->created_at));
                $data['corporate_vanity_url'] = $user->vu;
                $data['corporate_color'] = $user->cc;
                $data['logo_url'] = $user->logo;
                $data['corporate_url'] = $user->corporate_url;
                $data['display_name'] = $user->display_name;
                $data['logo_file'] = $user->logo != null;
                $data['logo_original_name'] = $user->logo_name;
                $data['active'] = $user->active;
                $data['show_void_transaction_menu'] = $user->show_void_transaction_menu;
                $data['allow_merchant_fees_report'] = $user->allow_merchant_fees_report == 1 ? true : false;

                $retailer_array = explode(',', $user->retailers);
                $store_id_array = explode(',', $user->store_ids);
                $merchant_type_array = explode(',', $user->merchant_types);
                $freeze_stores_array = explode(',', $user->freeze_stores);
                $final_retailer_array = [];
                $i = 0;
                $data['is_sponsor_cp'] = in_array(SPONSOR, $merchant_type_array);
                $data['is_brand_cp'] = in_array(BRAND, $merchant_type_array);
                $data['freeze_sponsor_points'] = in_array(1, $freeze_stores_array);
                foreach ($retailer_array as $retailer) {
                    if ($retailer != '') {
                        $final_retailer_array[] = (object) array('id' => $store_id_array[$i], 'retailer' => $retailer, 'type' => $merchant_type_array[$i]);
                        $i++;
                    }
                }
                $data['retailer'] = $final_retailer_array;

                array_push($corporateParentsArr, $data);
            }
        } else {
            $corporateParentsArr = [];
        }

        return $corporateParentsArr;
    }

    /**
     * getAllAdminUsers
     * Listing page for Admin Users along with Server Side Pagination in Datatable
     * @param  mixed $request
     * @return void
     */
    public function getAllAdminUsers(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Admin users search started...");

        // Search with in Admin Users
        $adminUsers = $this->_getAdminUsersSearch($request);

        $message = trans('message.admin_users_fetch_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Admin users search started complete.");
        return renderResponse(SUCCESS, $message, $adminUsers);
    }

    /**
     * _getAdminUsersSearch
     * Fetch the Admin Users
     * @param  mixed $searchArray
     * @return void
     */
    private function _getAdminUsersSearch($request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Admin users Search Started.");

        // Default roles array
        $userRoles = [ADMIN, REPORT_ADMIN, HELPDESK];
        if ($request['roles']) {
            // If roles are provided in request, parse them properly
            // Assuming roles come as comma-separated string, clean and split
            $rolesString = str_replace(["'", '"'], '', $request['roles']);
            $userRoles = array_map('trim', explode(',', $rolesString));
        }

        $page = $request['page']; // Get the current page from the request
        $perPage = $request['per_page']; // Set the number of items per page
        $offset = ($page - 1) * $perPage; // Calculate the offset

        // Create placeholders for IN clause based on number of roles
        $placeholders = str_repeat('?,', count($userRoles) - 1) . '?';
        $sql = "SELECT users.*,user_roles.role_name,status_master.status as status_name FROM users force index(fk_user_role_id) straight_join user_roles ON users.role_id = user_roles.role_id straight_join status_master ON status_master.id = users.status WHERE user_roles.role_name IN ($placeholders)  ";

        // Start with user roles as parameters
        $searchStr = $userRoles;
        if (strlen(trim($request['consumer'])) >= 3) {
            $sql .= ' AND LOWER(
                REPLACE(CONCAT(COALESCE(
                REPLACE(users.first_name, " ",""), "")," ", COALESCE(
                REPLACE(users.middle_name, " ",""), "")," ", COALESCE(
                REPLACE(users.last_name, " ",""), "")),"  "," ")) LIKE ? ';
            array_push($searchStr, '%' . $request['consumer'] . '%');
        }
        if (trim($request['email'])) {
            $sql .= " AND users.email = ? ";
            array_push($searchStr, $request['email']);
        }
        $totalCount = count(DB::connection(MYSQL_RO)->select($sql, $searchStr));
        $sql .= "  ORDER BY users.created_at DESC LIMIT ? OFFSET ?";
        array_push($searchStr, $perPage, $offset);
        $adminUsers = DB::connection(MYSQL_RO)->Select($sql, $searchStr);

        $adminUsersArr = [];
        if (!empty($adminUsers)) {
            foreach ($adminUsers as $user) {

                $data = [];
                $data['name'] = $user->first_name . ' ' . $user->middle_name . ' ' . $user->last_name;
                $data['first_name'] = $user->first_name;
                $data['middle_name'] = $user->middle_name;
                $data['last_name'] = $user->last_name;
                $data['email'] = $user->email;
                $data['edit'] = $user->user_id;
                $data['status'] = $user->status;
                $data['role_id'] = $user->role_id;
                $data['role_name'] = $user->role_name;
                $data['status_name'] = str_replace('User ', '', $user->status_name);
                $data['created_at'] = date('m-d-Y h:i A', strtotime($user->created_at));

                array_push($adminUsersArr, $data);
            }
        } else {
            $adminUsersArr = [];
        }
        $data = [
            'data' => $adminUsersArr,
            'current_page' => $page,
            'per_page' => $perPage,
            'total' => $totalCount,
            'total_pages' => ceil($totalCount / $perPage),
        ];
        return $data;
    }

    /**
     * getAdminUserRoles
     * This function will return all the Admin Roles from Database
     * @return void
     */
    public function getAdminUserRoles()
    {
        $roles = UserRole::whereIn('role_name', array(ADMIN, REPORT_ADMIN, HELPDESK))->get();
        $message = trans('message.user_roles_fetch_success');
        return renderResponse(SUCCESS, $message, $roles); // API Response returned with 200 status
    }

    /**
     * addUser
     * This function will add Role Specific Admin USers
     * @param  mixed $request
     * @return void
     */
    public function addUser(Request $request)
    {
        $rule = array(
            'email' => VALIDATION_REQUIRED . ' | ' . VALIDATION_EMAIL,
            'first_name' => VALIDATION_REQUIRED,
            'last_name' => VALIDATION_REQUIRED,
            'role_id' => VALIDATION_REQUIRED,
        );
        $this->__validate($request->all(), $rule);

        $user = User::join('user_roles', 'users.role_id', '=', 'user_roles.role_id')->where('user_roles.role_name', '!=', CONSUMER)->whereRaw('(users.email = ? OR users.contact_person_email = ?)', [$request->get('email'), $request->get('email')])->first();

        if (empty($user)) {
            $email = $request->get('email');
            $user_role = UserRole::where('role_id', $request->get('role_id'))->first(); // fetching Role name for Logging Purpose
            $user_details = Auth::user(); // Fetching Logged In User Details
            $password = substr(md5(microtime()), rand(0, 26), 7); // Generating Password

            //Fetch the Active Status ID from Status Master table (Set User Status to Default)
            $user_status = StatusMaster::where('code', USER_ACTIVE)->first();

            DB::beginTransaction();
            try {
                // Insertion begins in User Table
                $user = new User();
                $user->email = $email;
                $user->first_name = $request->get('first_name');
                $user->middle_name = $request->get('middle_name');
                $user->last_name = $request->get('last_name');
                $user->password = Hash::make($password);
                $user->role_id = $request->get('role_id');
                $user->added_by = $user_details->user_id;
                $user->status = $user_status->id;
                $user->save();

                DB::commit();

                // Sending Mail with password
                $params = [
                    'user_id' => $user->user_id,
                    'password' => $password,
                    'user_role' => $user_role->role_name,
                ];
                if ($user_role->role_name == ADMIN || $user_role->role_name == REPORT_ADMIN) {
                    $this->emailexecutor->adminUsersWelcomeEmail($params);
                } else if ($user_role->role_name == HELPDESK) {
                    $this->emailexecutor->helpdeskWelcomeEmail($params);
                    $this->emailexecutor->helpdeskloginEmail($params);
                }

                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "User (" . $user_role->role_name . ") Created Successfully and inserted in Database with User ID : " . $user->id . " by User ID : " . $user_details->user_id);
                $message = trans('message.user_creation_success');
                // API Response returned with 200 status
                return renderResponse(SUCCESS, $message, $user);
            } catch (\Exception $e) {
                Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured during User (" . $user_role->role_name . ") Creation By User ID :  " . $user_details->user_id . ".", [EXCEPTION => $e]);
                DB::rollback();
                $message = trans('message.user_creation_error');
                // Exception Returned
                return renderResponse(FAIL, $message, null);
            }
        } else {
            $message = trans('message.email_not_available');
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . $message);
            return renderResponse(FAIL, $message, $request->get('email'));
        }
    }

    /**
     * editUser
     * This function will update the Admin User
     * @param  mixed $request
     * @return void
     */
    public function editUser(Request $request)
    {
        $rule = array(
            'status' => VALIDATION_REQUIRED,
            'user_id' => VALIDATION_REQUIRED,
            'email' => VALIDATION_REQUIRED . ' | ' . VALIDATION_EMAIL,
            'first_name' => VALIDATION_REQUIRED,
            'last_name' => VALIDATION_REQUIRED,
        );
        $this->__validate($request->all(), $rule);

        $user_details = Auth::user(); // Fetching Logged In User Details

        $user = User::join('user_roles', 'users.role_id', '=', 'user_roles.role_id')->where('user_roles.role_name', '!=', CONSUMER)->where('users.user_id', '!=', $request->get('user_id'))->whereRaw('(users.email = ? OR users.contact_person_email = ?)', [$request->get('email'), $request->get('email')])->first();

        if (empty($user)) {
            // Getting User Role for Log Purpose
            $user_role = User::join('user_roles', 'users.role_id', '=', 'user_roles.role_id')->select('user_roles.role_name')->where('users.user_id', $request->get('user_id'))->first();

            DB::beginTransaction();
            try {
                // Updation begins in User Table
                $user = User::find($request->get('user_id'));
                $user->email = $request->get('email');
                $user->first_name = $request->get('first_name');
                $user->middle_name = $request->get('middle_name');
                $user->last_name = $request->get('last_name');
                $user->status = $request->get('status');
                $user->save();

                DB::commit();
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "User (" . $user_role->role_name . ") Updated Successfully User ID : " . $user->id . " by User ID : " . $user_details->user_id);
                $message = trans('message.user_updation_success');
                // API Response returned with 200 status
                return renderResponse(SUCCESS, $message, $user);
            } catch (\Exception $e) {
                Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured during User (" . $user_role->role_name . ") Updation By User ID :  " . $user_details->user_id . ".", [EXCEPTION => $e]);
                DB::rollback();
                $message = trans('message.user_updation_error');
                // Exception Returned
                return renderResponse(FAIL, $message, null);
            }
        } else {
            $message = trans('message.email_not_available');
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . $message);
            return renderResponse(FAIL, $message, $request->get('email'));
        }
    }

    /**
     * statusChange
     * This Function is used to Update an User's Status
     * @return void
     */
    public function statusChange(Request $request)
    {
        $id = $request['id'];
        $user_details = User::where('user_id', $id)->first(); // Fetching User Details

        if (!empty($user_details->date_of_birth) && empty($request['date_of_birth'])) {
            return renderResponse(FAIL, 'Date of birth field is required', null);
        }
        $status_array = [
            'status' => $request['status'],
            'first_name' => $request['first_name'],
            'middle_name' => $request['middle_name'],
            'last_name' => $request['last_name'],
            'street_address' => $request['street_address'],
            'apt_number' => $request['apt_number'],
            'city' => $request['city'],
            'state' => $request['state'],
            'zipcode' => $request['zipcode'],
            'date_of_birth' => $request['date_of_birth'],
            'standard_daily_limit' => $request['purchase_power'] != "" && $user_details->existing_user == 1 && $request['automatic_purchase_power'] == 0 ? $request['purchase_power'] : $user_details->standard_daily_limit,
            'weekly_spending_limit' => $request['weekly_spending_limit'],
            'disable_automatic_purchase_power' => $request['automatic_purchase_power'] == 0 ? 1 : 0,
            'disable_automatic_weekly_spending_limit' => $request['automatic_weekly_spending_limit'] == 0 ? 1 : 0,
            'required_upload_document' => $request['required_upload_document'],
            'active_allow_transaction' => $request['active_allow_transaction'],
            'is_date_of_birth_update' => strtotime($request['date_of_birth']) && $user_details->existing_user == 1 ? 1 : 0,

            'active_allow_transaction_time' => $user_details->existing_user == 1 && $request['active_allow_transaction'] == 1 ? new Carbon() : $user_details->active_allow_transaction_time,
        ];
        $suspected_fraud_status = getStatus(SUSPECTED_FRAUD);

        DB::beginTransaction();
        try {
            if ($request->get('override_microbuilt_check') == 1) {

                $searchArray = [1, $id];
                $update_decision_code = "UPDATE microbilt_response_details
                SET is_override = ? WHERE consumer_id = ? AND (decision_code = 'D' OR (decision_code = 'W' AND properties_messagestr = '" . ACCOUNT_STRUCTURE . "') OR (decision_code = 'A' AND properties_negativetransactions > 0))";
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . " Overriding Existing Microbilt details with decision code D for consumer having user_id: " . $id);
                DB::connection(MYSQL)->update($update_decision_code, $searchArray);
            }
            // If weekly spending limit got updated then insert it into the history table
            storeWeeklySpendingLimitHistory($user_details, $request['weekly_spending_limit'], 1);

            // If purchase power got updated then insert it into the history table
            if ($request['purchase_power'] != "" && $user_details->existing_user == 1 && $status_array['disable_automatic_purchase_power'] == 1) {
                $pending = getStatus(PENDING);
                // Get the sum of all pending transactions from past
                $transaction = TransactionDetails::select(DB::raw('IFNULL(sum(amount + tip_amount),0) as pending_transaction'))->where('transaction_ref_no', null)->where('isCanpay', 0)->where('is_v1', 0)->where('status_id', $pending)->where('consumer_id', $user_details->user_id)->first();

                $imit = $request['weekly_spending_limit'] - $transaction->pending_transaction;
                $status_array['purchase_power'] = $status_array['standard_daily_limit'];
                if ($imit < $status_array['standard_daily_limit']) {
                    $status_array['purchase_power'] = $imit;
                }
                $history = new CustomPurchasePowerHistory();
                // Fetch last inserted record for old_purchase_power_date column
                $record = CustomPurchasePowerHistory::where('consumer_id', $user_details->user_id)->orderBy('created_at', 'DESC')->first();
                $history->consumer_id = $user_details->user_id;
                $history->old_purchase_power = $user_details->purchase_power;
                $history->old_purchase_power_date = !empty($record) ? $record->created_at : null;
                $history->total_pending_amount = $transaction->pending_transaction;
                $history->new_purchase_power = $status_array['purchase_power'];
                $history->admin_control = 1;
                $history->save();
            } else {
                $status_array['purchase_power'] = $request['purchase_power'] != "" ? $request['purchase_power'] : $user_details->purchase_power;
            }

            // Store Consumer Address to history table
            $address_changed = 0; // Flag to check address changed or not
            $state_changed = 0; // Flag to check state changed or not
            $birth_year_changed = 0; // Flag to check birth year changed or not
            if (
                $user_details->street_address . ' ' . $user_details->apt_number . ' ' . $user_details->city . ' ' . $user_details->state . ' ' . $user_details->zipcode
                !=
                $request['street_address'] . ' ' . $request['apt_number'] . ' ' . $request['city'] . ' ' . $request['state'] . ' ' . $request['zipcode']
            ) {
                savePhoneAddressHistory($status_array, ADDRESS_UPDATED_BY_ADMIN, $user_details->user_id);
                $address_changed = 1; // Set the flag to 1 as the adress has been changed
                if ($user_details->state != $request['state']) {
                    $state_changed = 1; // Set the flag to 1 as the state has been changed
                }
            }
            // Flag to check birth year changed or not
            $previous_birth_year = date('Y', strtotime($user_details->date_of_birth));
            $new_birth_year = date('Y', strtotime($request['date_of_birth']));
            if ($previous_birth_year != $new_birth_year) {
                $birth_year_changed = 1;
            }

            User::where('user_id', $user_details->user_id)->update($status_array);

            // Add the status in the history table for this consumer
            if ($user_details->status != $request['status']) {
                saveStatusHistory(STATUS_UPDATED_BY_ADMIN, 'Consumer Details Update from Consumer Listing Page', $request['status'], $user_details->user_id);
            }

            // Check if the address has changed. Should be after user details update. Otherwise in email the old address will show up.
            //Check if the birth year has changed
            if ($address_changed == 1 || $birth_year_changed == 1) {
                if ($request['state'] === ILLINIOS || $request['state'] === INDIANA || $request['state'] === PENNSYLVANIA || $request['state'] === DELAWARE || $state_changed == 1 || $birth_year_changed == 1) {
                    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Address changed and user resides in IL/IN state. Checking started for Fith Third User for User ID: " . $user_details->user_id);
                    // Get the active bank details
                    $bank_details = BankAccountInfo::join('status_master', 'status_master.id', '=', 'user_bank_account_info.status')->select('user_bank_account_info.*')->where(['user_bank_account_info.user_id' => $user_details->user_id, 'status_master.code' => BANK_ACTIVE])->first();
                    if (!empty($bank_details)) {
                        if ($bank_details->account_id != '') {
                            // prepare the data for Fifth Third user check for direct linked conusmer
                            $type = 1;
                            $last_balance = ConsumerAccountBalance::where(['consumer_id' => $user_details->user_id, 'account_id' => $bank_details->id])->orderBy('created_at', 'DESC')->first();
                            $data['balance'] = !empty($last_balance) ? $last_balance->balance : '';
                            $data['routingNumber'] = $bank_details->routing_no;
                            $data['realAccountNumber'] = $bank_details->account_no;
                            $data['institutionId'] = $bank_details->institution_id;
                            $notification_arr[] = $data;
                            // Check for Fifth Third User for direct linked consumer
                            // Check if the address has changed
                            if ($address_changed == 1) {
                                checkForFifthThirdUser($notification_arr, $type, 1, $user_details->user_id);
                            }
                            if ($state_changed == 1 || $birth_year_changed == 1) {
                                // Check for institution wise fraud
                                institutionwiseFraudUser($notification_arr, 1, 1, $user_details->user_id);
                            }
                        } else {
                            // prepare the data for Fifth Third user check for manual linked conusmer
                            $type = 0;
                            $data['routingNumber'] = $bank_details->routing_no;
                            $data['accountNumber'] = $bank_details->account_no;
                            // Check for Fifth Third User for manual linked consumer
                            // Check if the address has changed
                            if ($address_changed == 1) {
                                checkForFifthThirdUser($data, $type, 0, $user_details->user_id);
                            }
                        }
                    }
                }
            }

            if ($user_details->active_allow_transaction == 0 && $request['active_allow_transaction'] == '1') {

                $user_details->active_allow_transaction = 0;
                $user_details->purchase_power = config('app.purchase_power_for_manual_bank_linked_consumer');
                $user_details->standard_daily_limit = config('app.purchase_power_for_manual_bank_linked_consumer');
                $user_details->disable_automatic_purchase_power = 1;
                $user_details->save();
                $user_details = User::where('user_id', $id)->first(); // Fetching User Details

            }

            // Check if user status changed to suspected fraud
            if ($request['status'] == $suspected_fraud_status) {
                // Add all the bank accounts of the consumer to the blacklist table
                $bank_accounts = BankAccountInfo::where('user_id', $user_details->user_id)->get();
                foreach ($bank_accounts as $bank_details) {
                    addAccountToBlacklist($bank_details);
                }
                // Remove all accounts from whitelist table for this user
                ValidAccountNumber::where('consumer_id', $user_details->user_id)->delete();
            }
            $need_Lite_to_standard_api_call = false;
            // Check if the user status changed from suspected fraud to active
            if ($user_details->status == $suspected_fraud_status && $request['status'] == getStatus(USER_ACTIVE)) {
                // Add the consumer's current bank account in whitelist
                addAccountToWhitelist($user_details->user_id);
                // Mark the user as previously suspected so that the new bank added by the consumer doesn't get marked as balcklist
                $user_details->previously_marked_as_suspected = $user_details->status == $suspected_fraud_status ? 1 : 0;
                $user_details->save();
                $need_Lite_to_standard_api_call = true;
            }
            // Check if the user status changed from blocked to active
            if ($user_details->status == getStatus(USER_BLOCKED) && $request['status'] == getStatus(USER_ACTIVE)) {
                $returnCountHistory = DB::table('return_count_history')->where('consumer_id', $request['id'])->first();
                if (!empty($returnCountHistory)) {
                    // update return count history to reset max return count limit
                    DB::table('return_count_history')->where('consumer_id', $request['id'])
                        ->update([
                            'return_count' => 0,
                            'reset_count' => $returnCountHistory->reset_count + 1,
                        ]);
                }
            }

            $status = StatusMaster::where("id", $request['status'])->first();

            $userStatusArr = [
                'user_id' => $id,
                'status_name' => $status->id,
            ];
            // Update Status Into Firebase
            $this->firebase->storeUserStatusIntoFireStore($userStatusArr);

            // Update Processinng to 1 when user status is not active
            if ($status->code != USER_ACTIVE && $status->code != SUSPECTED_FRAUD) {
                $updateProcessingStatus = ConsumerSessionManagement::where([
                    'user_id' => $user_details->user_id,
                    'used' => 0,
                    'processing' => 0,
                ])
                    ->update(['processing' => 1]);
            }
            DB::commit();
            if ($need_Lite_to_standard_api_call && $user_details->consumer_type == LITE_CONSUMER) {
                $api = new ApiHttpClient();
                $api->updateConsumerLiteToStandard($user_details->user_id);
            }
            $message = "Consumer details updated successfully.";
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "User Status Updated Successfully to " . $status->status . " for User ID : " . $user_details->user_id);
            return renderResponse(SUCCESS, $message, null); // API Response returned with 200 status
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while updating status for user id: " . $request['id'], [EXCEPTION => $e]);
            DB::rollback();
            $message = trans('message.user_status_update_fail');
            return renderResponse(FAIL, $message, $e);
        }
    }

    /**
     * Change user existing password
     *
     * @return mixed
     */
    public function changePassword()
    {
        $user = Auth::user();
        $data = $this->request->all();
        //check the required field
        $this->validate($this->request, [
            'old_password' => VALIDATION_REQUIRED,
            'new_password' => VALIDATION_REQUIRED,
            'confirm_password' => VALIDATION_REQUIRED,
        ]);
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Begin password updation for user : " . $user->user_id);
        //check if password matches confirm password
        if ($data['new_password'] != $data['confirm_password']) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Passwords does not match for user : " . $user->user_id);
            $message = trans('message.passwords_does_not_match');
            return renderResponse(FAIL, $message, null);
        }
        //check if old password provided is incorrect
        if (!Hash::check($data['old_password'], $user->password)) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Incorrect current password for user : " . $user->user_id);
            $message = trans('message.incorrect_password');
            return renderResponse(FAIL, $message, null);
        }
        DB::beginTransaction();
        try {
            //updating db with new password
            User::where('user_id', $user->user_id)->update(['password' => Hash::make($data['new_password'])]);
            DB::commit();
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Password successfully updated for user : " . $user->user_id);
            $message = trans('message.password_update_success');
            return renderResponse(SUCCESS, $message, null);
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while updating password.", [EXCEPTION => $e]);
            DB::rollback();
            $message = trans('message.db_transaction_failed');
            return renderResponse(FAIL, $message, $e);
        }
    }

    /**
     * This API returns all the consumer details
     */
    public function getConsumers()
    {
        // Columns defined for Sorting
        $columns = array(
            0 => 'users.first_name',
            1 => 'users.phone',
            2 => 'users.email',
            3 => 'users.date_of_birth',
            4 => 'returns_present',
            5 => 'users.created_at',
            6 => 'status_master.status',
        );
        // Main Query
        $query = User::join('user_roles', 'users.role_id', '=', 'user_roles.role_id')
            ->join('status_master', 'status_master.id', '=', 'users.status')
            ->join('user_bank_account_info', 'user_bank_account_info.user_id', '=', 'users.user_id')
            ->join('status_master as bsm', 'bsm.id', '=', 'user_bank_account_info.status')
            ->select('users.*', 'status_master.status as status_name')
            ->selectRaw('RIGHT(user_bank_account_info.account_no,8) as account_no')
            ->selectRaw('(SELECT COUNT(*) FROM transaction_details as td INNER JOIN status_master as smm on td.status_id = smm.id where smm.code = ' . RETURNED . ' and td.transaction_ref_no is null and td.consumer_id = users.user_id) as returns_present')
            ->where('user_roles.role_name', CONSUMER)
            ->where('bsm.code', BANK_ACTIVE);
        $totalData = $query->get()->count(); // Getting total no of rows
        $totalFiltered = $totalData;
        $limit = intval($this->request->input('length'));
        $start = intval($this->request->input('start'));
        $order = $columns[$this->request->input('order.0.column')];
        $query_limit = $query->offset($start)->limit(intval($limit));
        $dir = $this->request->input('order.0.dir');
        if (empty($this->request->input('search.value')) && empty($order) && empty($dir)) {
            $users = $query_limit->orderBy('created_at', 'DESC')->get();
        } else if (empty($this->request->input('search.value'))) {
            $users = $query_limit->orderBy($order, $dir)->get();
        } else {
            $search = $this->request->input('search.value');
            $search_query = $query->where(function ($q) use ($search, $start, $limit, $order, $dir) {
                $q->where('users.user_id', 'LIKE', "%{$search}%")
                    ->orWhereRaw("lower(concat_ws(' ', first_name, middle_name, last_name)) LIKE ? ", ['%' . $search . '%'])->orWhere('phone', 'LIKE', "%{$search}%")
                    ->orWhere('.email', 'LIKE', "%{$search}%")
                    ->orWhere('date_of_birth', 'LIKE', "%{$search}%")
                    ->orWhereRaw("lower(concat_ws(' ', street_address, city, state, zipcode)) LIKE ? ", ['%' . $search . '%'])
                    ->orWhere('status_master.status', 'LIKE', "%{$search}%")
                    ->orWhere('user_bank_account_info.account_no', 'LIKE', "%{$search}%");
            })
                ->orderBy($order, $dir)
                ->offset($start)
                ->limit($limit);
            $users = $search_query->orderBy($order, $dir)->get();
            $totalFiltered = User::join('user_roles', 'users.role_id', '=', 'user_roles.role_id')
                ->join('status_master', 'status_master.id', '=', 'users.status')
                ->join('user_bank_account_info', 'user_bank_account_info.user_id', '=', 'users.user_id')
                ->join('status_master as bsm', 'bsm.id', '=', 'user_bank_account_info.status')
                ->select('users.users.*', 'status_master.status as status_name')
                ->selectRaw('RIGHT(user_bank_account_info.account_no,8) as account_no')
                ->where('user_roles.role_name', CONSUMER)
                ->where('bsm.code', BANK_ACTIVE)
                ->where('users.user_id', 'LIKE', "%{$search}%")
                ->orWhereRaw("lower(concat_ws(' ', first_name, middle_name, last_name)) LIKE ? ", ['%' . $search . '%'])
                ->orWhere('phone', 'LIKE', "%{$search}%")
                ->orWhere('.email', 'LIKE', "%{$search}%")
                ->orWhere('date_of_birth', 'LIKE', "%{$search}%")
                ->orWhereRaw("lower(concat_ws(' ', street_address, city, state, zipcode)) LIKE ? ", ['%' . $search . '%'])
                ->orWhere('status_master.status', 'LIKE', "%{$search}%")
                ->orWhere('user_bank_account_info.account_no', 'LIKE', "%{$search}%")
                ->count();
        }
        $data = array();
        if (!empty($users)) {
            // Creating array to show the values in frontend
            foreach ($users as $user) {
                $apt_number = !empty($user->apt_number) ? $user->apt_number . " " : "";
                $nestedData['name'] = $user->first_name . ' ' . $user->middle_name . ' ' . $user->last_name;
                $nestedData['edit'] = $user->user_id;
                $nestedData['first_name'] = $user->first_name;
                $nestedData['middle_name'] = $user->middle_name;
                $nestedData['last_name'] = $user->last_name;
                $nestedData['phone'] = $user->phone;
                $nestedData['email'] = $user->email;
                $nestedData['date_of_birth'] = $user->date_of_birth != '' && $user->date_of_birth != '0000-00-00' ? date('m-d-Y', strtotime($user->date_of_birth)) : '';
                $nestedData['address'] = $apt_number . $user->street_address . ' ' . $user->city . ' ' . $user->state . ' ' . $user->zipcode;
                $nestedData['status'] = $user->status;
                $nestedData['status_name'] = $user->status_name;
                $nestedData['created_at'] = date('m-d-Y h:i A', strtotime($user->created_at));
                $nestedData['account_no'] = substr_replace($user->account_no, 'XXXX', 0, 4);
                $nestedData['returns_present'] = $user->returns_present == 1 ? 'Yes' : 'No';
                $nestedData['disable_automatic_purchase_power'] = $user->disable_automatic_purchase_power;

                $data[] = $nestedData;
            }
        }
        // Drawing the Datatable
        $json_data = array(
            "draw" => intval($this->request->input('draw')),
            "recordsTotal" => intval($totalData),
            "recordsFiltered" => intval($totalFiltered),
            "data" => $data,
        );

        Log::info(addslashes(__METHOD__) . "(" . LINE . __LINE__ . ") : Consumer List fetched successfully");
        echo json_encode($json_data); // Rerurning the data
    }

    /**
     * Fetches all user status from table
     */
    public function getAllUserStatus()
    {
        //fetch all the user status that starts with 7
        //$status = StatusMaster::whereRaw("code LIKE '7%'")->orderby("code", "ASC")->get();
        $status = DB::select("SELECT id, code, REPLACE(STATUS,'User ','') as status FROM status_master WHERE code IN (" . USER_ACTIVE . "," . USER_EXPIRED . "," . USER_PENDING . "," . USER_REJECTED . "," . USER_BLOCKED . "," . USER_INACTIVE . "," . SUSPECTED_FRAUD . "," . RESTRICTED_USER . ")  order by status");
        $message = trans('message.user_status_success');
        return renderResponse(SUCCESS, $message, $status); // API Response returned with 200 status
    }

    /**
     * getAllConsumers
     * This function is used to get all the Consumers
     * @param  mixed $request
     * @return void
     */
    public function getAllConsumers(Request $request)
    {
        // Fetching all the consumers
        $sql = 'SELECT users.user_id,
        REPLACE(CONCAT(COALESCE(
        REPLACE(users.first_name, " ",""), "")," ", COALESCE(
        REPLACE(users.middle_name, " ",""), "")," ", COALESCE(
        REPLACE(users.last_name, " ",""), "")),"  "," ") AS consumer_name
        FROM users
        straight_join user_roles ON users.role_id = user_roles.role_id AND user_roles.role_name = "' . CONSUMER . '"
        INNER JOIN status_master ON status_master.id = users.status
        WHERE users.password != "" AND REPLACE(LOWER(CONCAT_WS(" ",users.first_name,users.middle_name,users.last_name)),"  "," ") LIKE ?
        ORDER BY consumer_name ASC
        LIMIT 50';
        $merchants = DB::connection(MYSQL_RO)->select($sql, ['%' . $request->get('searchtxt') . '%']);

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer List fetched Successfully ");
        $message = trans('message.consumer_list_fetch_success');
        return renderResponse(SUCCESS, $message, $merchants);
    }

    /**
     * getAllHelpdeskUsers
     * This function is used to get all the Helpdsesk
     * @param  mixed $request
     * @return void
     */
    public function getAllHelpdeskUsers(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Helpdesk users search started...");
        // Validating input request
        $this->validate($request, [
            'consumer' => VALIDATION_REQUIRED_WITHOUT_ALL . ':email',
            'email' => VALIDATION_REQUIRED_WITHOUT_ALL . ':consumer',
        ]);

        // Search with in Helpdesk Users
        $helpdeskUsers = $this->_getHelpdeskUsersSearch($request);

        $message = trans('message.helpdesk_users_fetch_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Helpdesk users search started complete.");
        return renderResponse(SUCCESS, $message, $helpdeskUsers);
    }

    /**
     * _getHelpdeskUsersSearch
     * Fetch the Helpdesk Users
     * @param  mixed $searchArray
     * @return void
     */
    private function _getHelpdeskUsersSearch($request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Helpdesk users Search Started.");

        $sql = "SELECT users.*,user_roles.role_name,status_master.status as status_name FROM users force index(fk_user_role_id) straight_join user_roles ON users.role_id = user_roles.role_id straight_join status_master ON status_master.id = users.status WHERE user_roles.role_name = ?  ";

        $searchStr = [HELPDESK];
        if (strlen(trim($request['consumer'])) >= 3) {
            $sql .= ' AND LOWER(
                REPLACE(CONCAT(COALESCE(
                REPLACE(users.first_name, " ",""), "")," ", COALESCE(
                REPLACE(users.middle_name, " ",""), "")," ", COALESCE(
                REPLACE(users.last_name, " ",""), "")),"  "," ")) LIKE ? ';
            array_push($searchStr, '%' . $request['consumer'] . '%');
        }
        if (trim($request['email'])) {
            $sql .= " AND users.email = ? ";
            array_push($searchStr, $request['email']);
        }
        $sql .= "  ORDER BY users.created_at DESC LIMIT 100";
        $helpdeskUsers = DB::connection(MYSQL_RO)->Select($sql, $searchStr);

        $helpdeskUsersArr = [];
        if (!empty($helpdeskUsers)) {
            foreach ($helpdeskUsers as $user) {

                $data = [];
                $data['name'] = $user->first_name . ' ' . $user->middle_name . ' ' . $user->last_name;
                $data['first_name'] = $user->first_name;
                $data['middle_name'] = $user->middle_name;
                $data['last_name'] = $user->last_name;
                $data['email'] = $user->email;
                $data['edit'] = $user->user_id;
                $data['status'] = $user->status;
                $data['role_id'] = $user->role_id;
                $data['role_name'] = $user->role_name;
                $data['status_name'] = str_replace('User ', '', $user->status_name);
                $data['created_at'] = date('m-d-Y h:i A', strtotime($user->created_at));

                array_push($helpdeskUsersArr, $data);
            }
        } else {
            $helpdeskUsersArr = [];
        }

        return $helpdeskUsersArr;
    }

    public function getHelpdeskUserRoles()
    {
        $roles = UserRole::whereIn('role_name', array(HELPDESK))->get();
        $message = trans('message.user_roles_fetch_success');
        return renderResponse(SUCCESS, $message, $roles); // API Response returned with 200 status
    }

    /**
     * getConsumerDetails
     * Fetch the Consumer Details for a particular User ID
     * @param  mixed $request
     * @return void
     */
    public function getConsumerDetails(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer Details Fetch w.r.t User ID started.");
        // Validating input request
        $this->validate($request, [
            'userID' => VALIDATION_REQUIRED,
        ]);

        $bankActiveStatus = getStatus(BANK_ACTIVE);
        //Fetch the Consumer Details for a parcticular User ID
        $consumerDetails = User::join('user_roles', 'users.role_id', '=', 'user_roles.role_id')
            ->join('status_master', 'status_master.id', '=', 'users.status')
            ->leftjoin('user_bank_account_info', function ($join) use ($bankActiveStatus) {
                $join->on("user_bank_account_info.user_id", "=", "users.user_id");
                $join->on("user_bank_account_info.status", "=", DB::Raw("'$bankActiveStatus'"));
            })
            ->leftjoin('bank_masters', 'bank_masters.routing_no', '=', 'user_bank_account_info.routing_no')
            ->select('users.*', 'status_master.status as status_name', 'user_bank_account_info.routing_no')
            ->selectRaw('if(user_bank_account_info.bank_name IS NOT NULL,user_bank_account_info.bank_name,bank_masters.bank_name) AS bank_name')
            ->selectRaw('RIGHT(user_bank_account_info.account_no,4) as account_no')
            ->selectRaw('REPLACE(CONCAT(COALESCE(users.apt_number, "")," ",COALESCE(users.street_address, "")," ",COALESCE(users.city, "")," ",COALESCE(users.state, "")," ",COALESCE(users.zipcode, "")),"  "," ") AS address')
            ->where('user_roles.role_name', CONSUMER)
            ->where('users.user_id', $request->get('userID'))
            ->limit(1)->offset(0)
            ->first();

        $enable_microbuilt_override_check = false;
        $checked_by_admin = false;
        
        if ($consumerDetails->consumer_type == LITE_CONSUMER) {
            $calculate_purchase_power = $effective_purchase_power = round(getCampaignRewardAmount($consumerDetails->user_id)->reward_amount, 2);
            $total_pending_transaction = $pending_amount_against_account = 0;
            $balanceDetails = [];
            $is_automatic_purchase_power_enable = false;
            $is_active_allow_transaction = false;
        } else {
            // Consumer balance details'
            $balance_details_sql = "WITH RankedConsumerAccountBalances AS (
                SELECT
                    balance,
                    created_at AS balance_check_date,
                    source,
                    ROW_NUMBER() OVER (PARTITION BY source ORDER BY created_at DESC) AS row_num
                FROM consumer_account_balances
                WHERE account_id IN (
                    SELECT id
                    FROM user_bank_account_info
                    WHERE user_id = ?
                    AND status = ?
                )
            )
            SELECT
                balance,
                balance_check_date,
                CASE
                    WHEN (source = '" . SCHEDULED_BALANCE_FETCH . "' || source = '" . ADMIN_BALANCE_FETCH . "') THEN 'Get Balance'
                    WHEN (source = '" . CONSUMER_LOGIN . "' || source = '" . SCHEDULED_REFRESH_BALANCE . "' || source = '" . ONE_TIME_BALANCE_FETCH . "' || source = '" . ADMIN_REFRESH_BALANCE . "') THEN 'Refresh Balance'
                    ELSE 'Others'
                END AS title,
                CASE
                    WHEN (source = '" . SCHEDULED_BALANCE_FETCH . "') THEN 'Schedule balance fetched'
                    WHEN (source = '" . ADMIN_BALANCE_FETCH . "') THEN 'Admin balance fetched'
                    WHEN (source = '" . CONSUMER_LOGIN . "') THEN 'Consumer refresh balance fetched'
                    WHEN (source = '" . SCHEDULED_REFRESH_BALANCE . "') THEN 'Schedule refresh balance fetched'
                    WHEN (source = '" . ONE_TIME_BALANCE_FETCH . "') THEN 'One time refresh balance fetched'
                    WHEN (source = '" . ADMIN_REFRESH_BALANCE . "') THEN 'Admin refresh balance fetched'
                    ELSE 'Balance fetched due to bank re-link or add new bank account'
                END AS reason
            FROM RankedConsumerAccountBalances
            WHERE row_num = 1
            order by balance_check_date desc
            LIMIT 3";
            $balanceDetails = DB::connection(MYSQL_RO)->select($balance_details_sql, [$request->get('userID'), $bankActiveStatus]);

            $pp_reason_msg = '';
            $pending_amount_against_account = 0;
            //Calculate the Current Purchase power
            if ($consumerDetails->purchase_power) {
                $pending = getStatus(PENDING);
                $returned = getStatus(RETURNED);
                $status_array = [$pending, $returned];
                $bank_account_no = getActiveBankAccountNo($consumerDetails->user_id); // Helper function called to get the Active Bank account no.
                // Getting the total Non-Settled Transactions for the Consumer
                $transaction = TransactionDetails::join('user_bank_account_info', 'transaction_details.account_id', '=', 'user_bank_account_info.id')->select(DB::raw('COALESCE(SUM(if(transaction_details.status_id IS NOT NULL, IF(transaction_details.consumer_bank_posting_amount IS NOT NULL, transaction_details.consumer_bank_posting_amount, transaction_details.amount + transaction_details.tip_amount), 0)),0) AS amount, transaction_details.status_id, transaction_details.pp_source'))->where('transaction_details.transaction_ref_no', null)->where('transaction_details.isCanpay', 0)->where('transaction_details.is_v1', 0)->wherein('transaction_details.status_id', $status_array)
                    ->where(function ($q) use ($bank_account_no, $consumerDetails) {
                        $q->where('user_bank_account_info.account_no', $bank_account_no)->orWhere('user_bank_account_info.user_id', $consumerDetails->user_id);
                    })->groupBy('transaction_details.status_id')->get();

                $pending_amount_against_account = $pending_amount = isset($transaction[0]->amount) ? $transaction[0]->amount : 0;

                // Check if the account number has any return transaction
                if (searchMultiArray('status_id', $returned, $transaction) == 1) {
                    $current_purchase_power = floor($consumerDetails->purchase_power - $pending_amount);
                    $pp_reason_msg = 'As there is active return against this same account number, so the actual purchase power is not visible to consumer. Consumers are going to see 0 as Purchase Power';
                } else {
                    $current_purchase_power = floor($consumerDetails->purchase_power - $pending_amount); // Calculating the current Purchase Power
                    if ($current_purchase_power < 0) {
                        $pp_reason_msg = 'Pending transaction of this account is higher than Purchase power';
                    }
                }
            } else {
                $current_purchase_power = '';
            }
            // if consumer is v1 consumer

            if ($consumerDetails->existing_user == 1 && $consumerDetails->disable_automatic_purchase_power == 1) {
                $calculate_purchase_power = $this->_getCustomPurchasePower($consumerDetails);
            } else {
                $calculate_purchase_power = $current_purchase_power;
            }
            $effective_purchase_power = comparePurchasePowerWeeklyLimits($consumerDetails, $calculate_purchase_power);
            if ($this->_checkLastTwoBalance($consumerDetails) && $consumerDetails->existing_user == '1') {
                $is_active_allow_transaction = true;
            } else {
                $is_active_allow_transaction = false;
            }

            if ($consumerDetails->active_allow_transaction == 1 && Carbon::now()->gte(Carbon::parse($consumerDetails->active_allow_transaction_time)->addMinutes(intval(config('app.active_allow_transaction_time_validity')))) && $is_active_allow_transaction) {

                $consumerDetails->active_allow_transaction = 0;
                $consumerDetails->purchase_power = 0;
                $consumerDetails->standard_daily_limit = null;
                $consumerDetails->disable_automatic_purchase_power = 0;
                $consumerDetails->save();
            }

            if ($this->_checkLastTwoBalance($consumerDetails) && $consumerDetails->disable_automatic_purchase_power == 0 && $consumerDetails->bank_link_type == 1 && $consumerDetails->existing_user == 1) {
                $is_automatic_purchase_power_enable = false;
            } else {
                $is_automatic_purchase_power_enable = true;
            }

            if ($pp_reason_msg == '' && (($consumerDetails->bank_link_type == 1 && $consumerDetails->disable_automatic_weekly_spending_limit == 1 && $consumerDetails->weekly_spending_limit > 0) || ($consumerDetails->bank_link_type == 0 && $consumerDetails->weekly_spending_limit > 0))) {
                $pp_reason_msg = 'Custom purchase power assigned to this consumer. Custom limit is $' . $consumerDetails->weekly_spending_limit;
            }

            $consumerDetails->pp_reason_msg = $pp_reason_msg;

            if (!empty($consumerDetails)) {
                if ($consumerDetails->bank_link_type == 0) {
                    $consumerDetails->calculated_purchase_power_source = $consumerDetails->weekly_spending_limit > 0
                    ? ucwords(str_replace('_', ' ', strtolower(CUSTOM_PURCHASE_POWER)))
                    : ($consumerDetails->is_algo_based == 1 && $consumerDetails->last_algo_data_failed == 0
                        ? ucfirst(strtolower(ALGO))
                        : ucwords(str_replace('_', ' ', strtolower($consumerDetails->purchase_power_source))));
                } elseif ($consumerDetails->disable_automatic_weekly_spending_limit == 1) {
                    $consumerDetails->calculated_purchase_power_source = $consumerDetails->disable_automatic_weekly_spending_limit == 1 && $consumerDetails->weekly_spending_limit > 0
                    ? ucwords(str_replace('_', ' ', strtolower(CUSTOM_PURCHASE_POWER)))
                    : ($consumerDetails->is_algo_based == 1 && $consumerDetails->last_algo_data_failed == 0
                        ? ucfirst(strtolower(ALGO))
                        : ucwords(str_replace('_', ' ', strtolower($consumerDetails->purchase_power_source))));
                } elseif ($consumerDetails->disable_automatic_purchase_power == 1) {
                    $consumerDetails->calculated_purchase_power_source = ucwords(str_replace('_', ' ', strtolower(CUSTOM_PURCHASE_POWER)));
                } else {
                    $consumerDetails->calculated_purchase_power_source = $consumerDetails->is_algo_based == 1 && $consumerDetails->last_algo_data_failed == 0
                    ? ucfirst(strtolower(ALGO))
                    : ucwords(str_replace('_', ' ', strtolower($consumerDetails->purchase_power_source)));
                }
            } else {
                $consumerDetails->calculated_purchase_power_source = null;
            }

            $consumerDetails->purchase_power_source = ucwords(str_replace('_', ' ', strtolower($consumerDetails->purchase_power_source)));
            //Fetch the Consumer transaction amount for status pending return and

            $total_pending_transaction = TransactionDetails::join('terminal_master', 'transaction_details.terminal_id', '=', 'terminal_master.id')
                ->join('status_master', 'transaction_details.status_id', '=', 'status_master.id')
                ->where('transaction_details.consumer_id', $request->get('userID'))
                ->where('status_master.code', '' . PENDING . '')
                ->whereNull('transaction_details.transaction_ref_no')
                ->whereNull('transaction_details.return_reason')
                ->where('transaction_details.isCanpay', 0)
                ->where('transaction_details.is_v1', 0)
                ->sum('transaction_details.consumer_bank_posting_amount');
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "All Transactions Fetched Successfully for User ID : " . $request->get('userID'));
            $message = trans('message.all_transactions_fetch_success');
            $blocked_status = getStatus(USER_BLOCKED);

            $fetch_microbuilt_error = ConsumerStatusUpdateHistory::where('consumer_id', $request->get('userID'))->orderBy('created_at', 'DESC')->first();
            if ($fetch_microbuilt_error) {
                $enable_microbuilt_override_check = $fetch_microbuilt_error->source == STATUS_UPDATED_BY_CONSUMER && $fetch_microbuilt_error->reason == SUSPENDED_STATUS_UPDATE_DUE_TO_MICROBILT && $fetch_microbuilt_error->status == $blocked_status ? true : false;
            }

            $sql_for_check_or_uncheck_by_admin = "SELECT * from microbilt_response_details
            WHERE consumer_id= ? AND (decision_code = 'D' OR (decision_code = 'W' AND properties_messagestr = '" . ACCOUNT_STRUCTURE . "') OR (decision_code = 'A' AND properties_negativetransactions > 0)) ORDER BY created_at DESC LIMIT 1";

            $sql_for_check_or_uncheck_by_admin = DB::connection(MYSQL_RO)->select($sql_for_check_or_uncheck_by_admin, [$request->get('userID')]);

            if ($sql_for_check_or_uncheck_by_admin) {
                $checked_by_admin = $sql_for_check_or_uncheck_by_admin[0]->is_override;
            }
        }

        // fetch the risk score for the consumer
        $get_risk_score = getRiskScore($consumerDetails->phone);
        $risk_score = !empty($get_risk_score) ? $get_risk_score : '';

        $returnResponse = [
            'consumerDetails' => $consumerDetails,
            'risk_score' => $risk_score,
            'calculate_purchase_power' => $calculate_purchase_power,
            'effective_purchase_power' => $effective_purchase_power,
            'total_pending_transaction' => $total_pending_transaction,
            'pending_amount_against_account' => $pending_amount_against_account,
            'is_active_allow_transaction' => $is_active_allow_transaction,
            'is_automatic_purchase_power_enable' => $is_automatic_purchase_power_enable,
            'latestBalanceDetails' => $balanceDetails,
            'enable_microbuilt_override_check' => $enable_microbuilt_override_check,
            'checked_by_admin' => $checked_by_admin,
        ];

        $message = trans('message.consumer_details_fetch_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer Details Fetched for User ID : " . $request->get('userID'));
        return renderResponse(SUCCESS, $message, $returnResponse);
    }

    /**
     * searchConsumer
     * Search the Consumers based on search Criteria
     * @param  mixed $request
     * @return void
     */
    public function searchConsumer(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer Global Search Started.");
        // Validating input request
        $this->validate($request, [
            'consumer_first_name' => VALIDATION_REQUIRED_WITHOUT_ALL . ':phone_no,email,consumer_middle_name,consumer_last_name',
            'consumer_middle_name' => VALIDATION_REQUIRED_WITHOUT_ALL . ':phone_no,email,consumer_first_name,consumer_last_name',
            'consumer_last_name' => VALIDATION_REQUIRED_WITHOUT_ALL . ':phone_no,email,consumer_first_name,consumer_middle_name',
            'phone_no' => VALIDATION_REQUIRED_WITHOUT_ALL . ':email,consumer,consumer_first_name,consumer_middle_name,consumer_last_name',
            'email' => VALIDATION_REQUIRED_WITHOUT_ALL . ':phone_no,consumer,consumer_first_name,consumer_middle_name,consumer_last_name',
        ]);

        $consumer = [];

        //Search with in Global Radar Review
        if ($request->get('global_radar_review') == 1) {
            $globalRadarReview = $this->_getGlobalRadarReviewConsumerSearch($request);
            if (count($globalRadarReview) > 0) {
                $consumer = array_merge($consumer, $globalRadarReview);
            }
        }

        //Search with in Manual Identity Review
        if ($request->get('manual_identity_review') == 1) {
            $manualReviewedConsumers = $this->_getManualReviewGlobalConsumerSearch($request);
            if (count($manualReviewedConsumers) > 0) {
                $consumer = array_merge($consumer, $manualReviewedConsumers);
            }
        }

        //Search with in V1 Manual Review
        if ($request->get('v1_manual_review') == 1) {
            $v1manualReviewedConsumers = $this->_getv1ManualReviewedGlobalConsumerSearch($request);
            if (count($v1manualReviewedConsumers) > 0) {
                $consumer = array_merge($consumer, $v1manualReviewedConsumers);
            }
        }

        //Search with in Registered Consumers
        if ($request->get('global_radar_review') != 1 && $request->get('manual_identity_review') != 1 && $request->get('v1_manual_review') != 1) {
            $registeredConsumers = $this->_getRegisteredConsumersGlobalSearch($request);
            if (count($registeredConsumers) > 0) {
                $consumer = $registeredConsumers;
            }
        }

        $message = trans('message.consumer_search_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer Search Complete");
        return renderResponse(SUCCESS, $message, $consumer);
    }

    /**
     * _getRegisteredConsumersGlobalSearch
     * Fetch the Registered Consumers for Global Consumer Search
     * @param  mixed $searchArray
     */
    private function _getRegisteredConsumersGlobalSearch($request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Registered Consumer Global Search Started.");
        //Get Status Pending and Archived
        $status_array = [PENDING, ARCHIVED];
        $statusArr = StatusMaster::whereIn('code', $status_array)->pluck('id');

        //Fetch Registered Consumers
        $registeredConsumers = User::on(MYSQL_RO)
            ->join('user_roles', 'users.role_id', '=', 'user_roles.role_id')
            ->join('status_master', 'status_master.id', '=', 'users.status')
            ->where('user_roles.role_name', CONSUMER)
            ->where('users.password', '!=', null)
            ->whereNotExists(function ($query) use ($statusArr) {
                $query->select(DB::raw(1))
                    ->from('global_radar_reviews as grr')
                    ->whereRaw('grr.user_id = users.user_id')
                    ->whereIn('grr.status', $statusArr)
                    ->orderBy('grr.updated_at', 'desc')
                    ->limit(1);
            });
        if (strlen(trim($request['consumer_first_name'])) >= 3) {
            $registeredConsumers = $registeredConsumers->whereRaw('users.first_name LIKE "%' . $request->get('consumer_first_name') . '%"');
        }
        if (strlen(trim($request['consumer_middle_name'])) >= 3) {
            $registeredConsumers = $registeredConsumers->whereRaw('users.middle_name LIKE "%' . $request->get('consumer_middle_name') . '%"');
        }
        if (strlen(trim($request['consumer_last_name'])) >= 3) {
            $registeredConsumers = $registeredConsumers->whereRaw('users.last_name LIKE "%' . $request->get('consumer_last_name') . '%"');
        }
        if ($request['phone_no']) {
            $registeredConsumers = $registeredConsumers->where('users.phone', $request['phone_no']);
        }
        if ($request['email']) {
            $registeredConsumers = $registeredConsumers->where('users.email', $request['email']);
        }
        $registeredConsumers = $registeredConsumers->selectRaw('REPLACE(CONCAT(COALESCE(REPLACE(users.first_name, " ",""), "")," ",COALESCE(REPLACE(users.middle_name, " ",""), "")," ",COALESCE(REPLACE(users.last_name, " ",""), "")),"  "," ") as consumer_name,users.email,users.phone,users.consumer_type')->get();
        $consumers = [];
        if (count($registeredConsumers) > 0) {
            foreach ($registeredConsumers as $consumer) {
                $data['name'] = $consumer->consumer_name;
                $data['email'] = $consumer->email;
                $data['phone'] = $consumer->phone;
                $data['status'] = $consumer->consumer_type == LITE_CONSUMER ? 'Lite Consumer' : 'Consumer Registered';
                $data['action'] = '<a href="/consumers?email=' . $consumer->email . '"><i class="fas fa-link"></i></a>';
                array_push($consumers, $data);
            }
        } else {
            $consumers = [];
        }

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Registered Consumer Search Complete");
        return $consumers;
    }

    /**
     * _getGlobalRadarReviewConsumerSearch
     * Fetch the Global Radar Review consumers for Global Consumer Search
     * @param  mixed $request
     */
    private function _getGlobalRadarReviewConsumerSearch($request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Global Radar Review Consumer Global Search Started.");

        //Get Status Pending and Archived
        $status_array = [PENDING, ARCHIVED];
        $statusArr = StatusMaster::whereIn('code', $status_array)->pluck('id');
        //Fetch Consumers in Global Radar Review
        $globalRadarReview = DB::table(DB::raw('(
            SELECT *,
                   ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY updated_at DESC) AS rn
            FROM global_radar_reviews
        ) as global_radar_reviews'))
            ->join('cognito_rules_log', 'cognito_rules_log.user_id', '=', 'global_radar_reviews.user_id')
            ->join('users', 'users.user_id', '=', 'global_radar_reviews.user_id')
            ->join('user_roles', 'users.role_id', '=', 'user_roles.role_id')
            ->join('status_master', 'status_master.id', '=', 'global_radar_reviews.status')
            ->where('global_radar_reviews.rn', '=', 1) // Ensure we only get the latest review for each user
            ->whereIn('global_radar_reviews.status', $statusArr)
            ->where('user_roles.role_name', CONSUMER);

        if (strlen(trim($request['consumer_first_name'])) >= 3) {
            $globalRadarReview = $globalRadarReview->whereRaw('users.first_name LIKE "%' . $request->get('consumer_first_name') . '%"');
        }
        if (strlen(trim($request['consumer_middle_name'])) >= 3) {
            $globalRadarReview = $globalRadarReview->whereRaw('users.middle_name LIKE "%' . $request->get('consumer_middle_name') . '%"');
        }
        if (strlen(trim($request['consumer_last_name'])) >= 3) {
            $globalRadarReview = $globalRadarReview->whereRaw('users.last_name LIKE "%' . $request->get('consumer_last_name') . '%"');
        }
        if ($request['phone_no']) {
            $globalRadarReview = $globalRadarReview->where('users.phone', $request['phone_no']);
        }
        if ($request['email']) {
            $globalRadarReview = $globalRadarReview->where('users.email', $request['email']);
        }
        $globalRadarReview = $globalRadarReview->selectRaw('REPLACE(CONCAT(COALESCE(REPLACE(users.first_name, " ",""), "")," ",COALESCE(REPLACE(users.middle_name, " ",""), "")," ",COALESCE(REPLACE(users.last_name, " ",""), "")),"  "," ") as consumer_name,users.email,users.phone,status_master.status')->groupBy('users.user_id')->get();

        $consumers = [];
        if (count($globalRadarReview) > 0) {
            foreach ($globalRadarReview as $consumer) {
                $data['name'] = $consumer->consumer_name;
                $data['email'] = $consumer->email;
                $data['phone'] = $consumer->phone;
                $data['status'] = 'Global Radar Review ' . $consumer->status;
                $data['action'] = '<a href="globalRadar' . $consumer->status . '?email=' . $consumer->email . '"><i class="fas fa-link"></i></a>';
                array_push($consumers, $data);
            }
        } else {
            $consumers = [];
        }

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Global Radar Review Consumer Search Complete");
        return $consumers;
    }

    /**
     * _getManualReviewGlobalConsumerSearch
     * Fetch the Manual Review for Global Consumer Search
     * @param  mixed $request
     */
    private function _getManualReviewGlobalConsumerSearch($request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Manual Review Consumer Global Search Started.");

        //Fetch Consumers in Global Radar Review
        $manualReviewedConsumers = ManualReviewDetail::on(MYSQL_RO)
            ->join('registration_session_details', 'manual_review_details.session_id', '=', 'registration_session_details.id')
            ->join('status_master', 'status_master.id', '=', 'manual_review_details.review_status')
            ->leftjoin('users as u', function ($leftjoin) {
                $leftjoin->on('u.phone', '=', 'registration_session_details.phone')->whereNotNull('u.password');
            })->whereNull('u.user_id');

        if (strlen(trim($request['consumer_first_name'])) >= 3) {
            $manualReviewedConsumers = $manualReviewedConsumers->whereRaw('registration_session_details.first_name LIKE "%' . $request->get('consumer_first_name') . '%"');
        }
        if (strlen(trim($request['consumer_middle_name'])) >= 3) {
            $manualReviewedConsumers = $manualReviewedConsumers->whereRaw('registration_session_details.middle_name LIKE "%' . $request->get('consumer_middle_name') . '%"');
        }
        if (strlen(trim($request['consumer_last_name'])) >= 3) {
            $manualReviewedConsumers = $manualReviewedConsumers->whereRaw('registration_session_details.last_name LIKE "%' . $request->get('consumer_last_name') . '%"');
        }
        if ($request['phone_no']) {
            $manualReviewedConsumers = $manualReviewedConsumers->where('registration_session_details.phone', $request['phone_no']);
        }
        if ($request['email']) {
            $manualReviewedConsumers = $manualReviewedConsumers->where('registration_session_details.email', $request['email']);
        }
        $manualReviewedConsumers = $manualReviewedConsumers->selectRaw('REPLACE(CONCAT(COALESCE(REPLACE(registration_session_details.first_name, " ",""), "")," ",COALESCE(REPLACE(registration_session_details.middle_name, " ",""), "")," ",COALESCE(REPLACE(registration_session_details.last_name, " ",""), "")),"  "," ") as consumer_name, registration_session_details.email, registration_session_details.phone,status_master.status')->get();

        $consumers = [];
        if (count($manualReviewedConsumers) > 0) {
            foreach ($manualReviewedConsumers as $consumer) {
                $data['name'] = $consumer->consumer_name;
                $data['email'] = $consumer->email;
                $data['phone'] = $consumer->phone;
                $data['status'] = 'Manual Identity Review ' . $consumer->status;
                $data['action'] = '<a href="' . strtolower(str_replace(' ', '', $consumer->status)) . '?email=' . $consumer->email . '"><i class="fas fa-link"></i></a>';
                array_push($consumers, $data);
            }
        } else {
            $consumers = [];
        }

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Manual Review Consumer Search Complete");
        return $consumers;
    }

    /**
     * _getv1ManualReviewedGlobalConsumerSearch
     * Fetch the V1 Manual Review consumers for Global Consumer Search
     * @param  mixed $request
     */
    private function _getv1ManualReviewedGlobalConsumerSearch($request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "V1 Manual Review Consumer Global Search Started.");

        //Fetch Consumers in Global Radar Review
        $sql = 'SELECT REPLACE(CONCAT(COALESCE(REPLACE(v1_onboarding_manual_reviews.first_name, " ",""), "")," ",COALESCE(REPLACE(v1_onboarding_manual_reviews.middle_name, " ",""), "")," ",COALESCE(REPLACE(v1_onboarding_manual_reviews.last_name, " ",""), "")),"  "," ") as consumer_name,v1_onboarding_manual_reviews.email, v1_onboarding_manual_reviews.phone,status_master.status as status_name
        FROM  v1_onboarding_manual_reviews
        JOIN status_master  ON v1_onboarding_manual_reviews.status = status_master.id
        LEFT JOIN users as u ON u.phone = v1_onboarding_manual_reviews.phone
        AND u.password IS NOT NULL WHERE u.user_id IS NULL AND v1_onboarding_manual_reviews.doc_front_side IS NOT NULL AND v1_onboarding_manual_reviews.doc_back_side IS NOT NULL';

        // Append dynamic conditions based on input
        $bindings = [];

        if (strlen(trim($request->input('consumer_first_name'))) >= 3) {
            $consumerFirstName = trim($request->input('consumer_first_name'));
            $sql .= " AND v1_onboarding_manual_reviews.first_name LIKE ?";
            $bindings[] = "%$consumerFirstName%";
        }

        if (strlen(trim($request->input('consumer_middle_name'))) >= 3) {
            $consumerMiddleName = trim($request->input('consumer_middle_name'));
            $sql .= " AND v1_onboarding_manual_reviews.middle_name LIKE ?";
            $bindings[] = "%$consumerMiddleName%";
        }

        if (strlen(trim($request->input('consumer_last_name'))) >= 3) {
            $consumerLastName = trim($request->input('consumer_last_name'));
            $sql .= " AND v1_onboarding_manual_reviews.last_name LIKE ?";
            $bindings[] = "%$consumerLastName%";
        }

        if ($phoneNo = $request->input('phone_no')) {
            $sql .= " AND v1_onboarding_manual_reviews.phone = ?";
            $bindings[] = $phoneNo;
        }

        if ($email = $request->input('email')) {
            $sql .= " AND v1_onboarding_manual_reviews.email = ?";
            $bindings[] = $email;
        }

        // Execute the raw SQL query with bindings
        $v1manualReviewedConsumers = DB::select($sql, $bindings);

        $consumers = [];
        if (count($v1manualReviewedConsumers) > 0) {
            foreach ($v1manualReviewedConsumers as $consumer) {
                $data['name'] = $consumer->consumer_name;
                $data['email'] = $consumer->email;
                $data['phone'] = $consumer->phone;
                $data['status'] = 'V1 Manual Review';
                $data['action'] = '<a href="/v1manualreview?email=' . $consumer->email . '"><i class="fas fa-link"></i></a>';
                array_push($consumers, $data);
            }
        } else {
            $consumers = [];
        }

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "V1 Manual Review Consumer Search Complete");
        return $consumers;
    }

    /**
     * getConsumersIncUnregistered
     * This function is used to get all the Consumers
     * @param  mixed $request
     * @return void
     */
    public function getConsumersIncUnregistered(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer List Including UnRegistered Consumers Started.");
        // Validating input request
        $this->validate($request, [
            'searchtxt' => VALIDATION_REQUIRED,
        ]);

        $consumers = 'SELECT REPLACE(CONCAT(COALESCE(REPLACE(users.first_name, " ",""), "")," ",COALESCE(REPLACE(users.middle_name, " ",""), "")," ",COALESCE(REPLACE(users.last_name, " ",""), "")),"  "," ") as consumer_name FROM users INNER JOIN user_roles ON users.role_id = user_roles.role_id WHERE user_roles.role_name = "' . CONSUMER . '" AND  users.password!= "" ';
        if ($request->get('searchtxt')) {
            $consumers .= ' AND  lower(REPLACE(CONCAT(COALESCE(REPLACE(users.first_name, " ",""), "")," ",COALESCE(REPLACE(users.middle_name, " ",""), "")," ",COALESCE(REPLACE(users.last_name, " ",""), "")),"  "," ")) LIKE ? ';
        }
        $consumers .= ' UNION ';
        $consumers .= 'SELECT REPLACE(CONCAT(COALESCE(REPLACE(registration_session_details.first_name, " ",""), "")," ",COALESCE(REPLACE(registration_session_details.middle_name, " ",""), "")," ",COALESCE(REPLACE(registration_session_details.last_name, " ",""), "")),"  "," ") as consumer_name FROM manual_review_details INNER JOIN registration_session_details ON manual_review_details.session_id = registration_session_details.id INNER JOIN status_master ON status_master.id =  manual_review_details.review_status ';
        if ($request->get('searchtxt')) {
            $consumers .= ' WHERE lower(REPLACE(CONCAT(COALESCE(REPLACE(registration_session_details.first_name, " ",""), "")," ",COALESCE(REPLACE(registration_session_details.middle_name, " ",""), "")," ",COALESCE(REPLACE(registration_session_details.last_name, " ",""), "")),"  "," ")) LIKE ? ';
        }
        $consumers .= ' UNION ';
        $consumers .= 'SELECT REPLACE(CONCAT(COALESCE(REPLACE(v1_onboarding_manual_reviews.first_name, " ",""), "")," ",COALESCE(REPLACE(v1_onboarding_manual_reviews.middle_name, " ",""), "")," ",COALESCE(REPLACE(v1_onboarding_manual_reviews.last_name, " ",""), "")),"  "," ") as consumer_name FROM v1_onboarding_manual_reviews Left JOIN status_master ON v1_onboarding_manual_reviews.status = status_master.id WHERE v1_onboarding_manual_reviews.doc_front_side IS NOT NULL AND v1_onboarding_manual_reviews.doc_back_side IS NOT NULL ';
        if ($request->get('searchtxt')) {
            $consumers .= ' AND lower(REPLACE(CONCAT(COALESCE(REPLACE(v1_onboarding_manual_reviews.first_name, " ",""), "")," ",COALESCE(REPLACE(v1_onboarding_manual_reviews.middle_name, " ",""), "")," ",COALESCE(REPLACE(v1_onboarding_manual_reviews.last_name, " ",""), "")),"  "," ")) LIKE ? ';
        }
        $consumers .= ' ORDER BY consumer_name ';
        $consumersArr = DB::connection(MYSQL_RO)->select($consumers, ['%' . $request->get('searchtxt') . '%', '%' . $request->get('searchtxt') . '%', '%' . $request->get('searchtxt') . '%']);

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer List fetched Successfully ");
        $message = trans('message.consumer_list_fetch_success');
        return renderResponse(SUCCESS, $message, $consumersArr);
    }

    /**
     * getV1Consumers
     * This function is used to get V1 Consumers that has not been converted to V2
     * @param  mixed $request
     * @return void
     */
    public function getV1Consumers(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer List V1 not yet Converted to V2.");
        // Validating input request
        $this->validate($request, [
            'searchtxt' => VALIDATION_REQUIRED,
        ]);

        $consumers = 'SELECT REPLACE(CONCAT(COALESCE(REPLACE(users.first_name, " ",""), "")," ",COALESCE(REPLACE(users.middle_name, " ",""), "")," ",COALESCE(REPLACE(users.last_name, " ",""), "")),"  "," ") as consumer_name FROM users INNER JOIN user_roles ON users.role_id = user_roles.role_id WHERE user_roles.role_name = "' . CONSUMER . '" AND  users.password IS NULL AND users.existing_user = 1 ';
        if ($request->get('searchtxt')) {
            $consumers .= ' AND  lower(REPLACE(CONCAT(COALESCE(REPLACE(users.first_name, " ",""), "")," ",COALESCE(REPLACE(users.middle_name, " ",""), "")," ",COALESCE(REPLACE(users.last_name, " ",""), "")),"  "," ")) LIKE ? ';
        }
        $consumersArr = DB::select($consumers, ['%' . $request->get('searchtxt') . '%']);

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "V1 Consumer List fetched Successfully ");
        $message = trans('message.consumer_list_fetch_success');
        return renderResponse(SUCCESS, $message, $consumersArr);
    }

    /**
     * searchV1Consumer
     * Search the V1 Consumers based on search Criteria
     * @param  mixed $request
     * @return void
     */
    public function searchV1Consumer(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "V1 Consumer Search Started.");

        //Fetch Registered Consumers
        $consumer = DB::table('users')
            ->join('user_roles', 'users.role_id', '=', 'user_roles.role_id')
            ->where('user_roles.role_name', CONSUMER)
            ->where('users.existing_user', 1)
            ->where('users.password', null);
        if (strlen(trim($request['consumer'])) >= 3) {
            $consumer = $consumer->whereRaw('lower(REPLACE(CONCAT(COALESCE(REPLACE(users.first_name, " ",""), "")," ",COALESCE(REPLACE(users.middle_name, " ",""), "")," ",COALESCE(REPLACE(users.last_name, " ",""), "")),"  "," ")) LIKE ?', '%' . $request->get('consumer') . '%');
        }
        if ($request['phone_no']) {
            $consumer = $consumer->where('users.phone', $request['phone_no']);
        }
        if ($request['email']) {
            $consumer = $consumer->where('users.email', $request['email']);
        }
        $consumer = $consumer->selectRaw('REPLACE(CONCAT(COALESCE(REPLACE(users.first_name, " ",""), "")," ",COALESCE(REPLACE(users.middle_name, " ",""), "")," ",COALESCE(REPLACE(users.last_name, " ",""), "")),"  "," ") as consumer_name,users.email,users.phone,users.user_id')->get();

        $message = trans('message.consumer_search_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "V1 Consumer Search Complete");
        return renderResponse(SUCCESS, $message, $consumer);
    }

    /**
     * getConsumerdetailsByUserid
     * Fetch the Consumer Details for a particular User ID
     * @param  mixed $request
     * @return void
     */
    public function getConsumerdetailsByUserid(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer Details Fetch w.r.t User ID started.");
        // Validating input request
        $this->validate($request, [
            'userID' => VALIDATION_REQUIRED,
        ]);

        //Fetch the Consumer Details for a parcticular User ID
        $consumerDetails = User::join('user_roles', 'users.role_id', '=', 'user_roles.role_id')
            ->where('user_roles.role_name', CONSUMER)
            ->where('users.user_id', $request->get('userID'))
            ->first();

        $message = trans('message.consumer_details_fetch_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer Details Fetched for User ID : " . $request->get('userID'));
        return renderResponse(SUCCESS, $message, $consumerDetails);
    }

    /**
     * updateConsumerDetails
     * Update The Email and Phone Number of V1 consumer which has not yet been converted to V2
     * @return void
     */
    public function updateConsumerDetails(Request $request)
    {
        $id = $request['id'];
        $user_details = User::where('user_id', $id)->first(); // Fetching User Details
        $userDetailsArr = [
            'first_name' => $request['first_name'],
            'middle_name' => $request['middle_name'],
            'last_name' => $request['last_name'],
            'email' => $request['email'],
            'phone' => $request['phone'],
        ];

        DB::beginTransaction();
        try {
            User::where('user_id', $user_details->user_id)->update($userDetailsArr);

            DB::commit();
            $message = "Consumer details updated successfully.";
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "User details updated Successfully for User ID : " . $user_details->user_id);
            return renderResponse(SUCCESS, $message, null); // API Response returned with 200 status
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while updating status for user id: " . $request['id'], [EXCEPTION => $e]);
            DB::rollback();
            $message = trans('message.user_details_update_fail');
            return renderResponse(FAIL, $message, $e);
        }
    }

    /**
     * logoutUser
     * This function is used to invalidate the JWT token of the user before logout
     * @param  mixed $request
     * @return void
     */
    public function logoutUser(Request $request)
    {
        $user = Auth::user();
        // Blacklisting the JWT token
        JWTAuth::invalidate($request->get('token'));
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Token blacklisted for User ID : " . $user->user_id);
        $message = trans('message.token_blacklisting_success');
        return renderResponse(SUCCESS, $message, null);
    }

    /**
     * searchRegisteredConsumer
     * Search the Consumers based on search Criteria
     * @param  mixed $request
     * @return void
     */
    public function searchRegisteredConsumer(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Registered Consumer Search Started.");
        // Validating input request
        $this->validate($request, [
            'consumer_first_name' => VALIDATION_REQUIRED_WITHOUT_ALL . ':phone_no,email,consumer_middle_name,consumer_last_name',
            'consumer_middle_name' => VALIDATION_REQUIRED_WITHOUT_ALL . ':phone_no,email,consumer_first_name,consumer_last_name',
            'consumer_last_name' => VALIDATION_REQUIRED_WITHOUT_ALL . ':phone_no,email,consumer_first_name,consumer_middle_name',
            'phone_no' => VALIDATION_REQUIRED_WITHOUT_ALL . ':email,consumer,consumer_first_name,consumer_middle_name,consumer_last_name',
            'email' => VALIDATION_REQUIRED_WITHOUT_ALL . ':phone_no,consumer,consumer_first_name,consumer_middle_name,consumer_last_name',
        ]);

        //Search with in Registered Consumers
        $registeredConsumers = $this->_getRegisteredConsumersSearch($request);

        $message = trans('message.consumer_search_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Registered Consumer Search Complete");
        return renderResponse(SUCCESS, $message, $registeredConsumers);
    }

    /**
     * _getRegisteredConsumersSearch
     * Fetch the Registered Consumers for Consumer Search
     * @param  mixed $searchArray
     * @return void
     */
    private function _getRegisteredConsumersSearch($request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Registered Consumer Global Search Started.");

        $bank_active = getStatus(BANK_ACTIVE);
        $mst_offset = getMstTime();
        //Fetch Registered Consumers
        $sql = 'SELECT `u`.*, ubai.id as active_account_exists, ubai.mx_user_action_needed, `smu`.`status` AS `status_name`, `td`.`id` AS `returns_present`,REPLACE(CONCAT(COALESCE(
            REPLACE(u.first_name, " ",""), "")," ", COALESCE(
            REPLACE(u.middle_name, " ",""), "")," ", COALESCE(
            REPLACE(u.last_name, " ",""), "")),"  "," ") AS consumer_name,u.email,u.phone, td.consumer_id,
            SUM(IF((sm.code = "511" || sm.code = "202") && td.return_reason IS NOT NULL, 1, 0)) AS active_return_count, SUM(IF(td.return_reason IS NOT NULL, 1, 0)) AS all_time_return_count,u.bank_link_type, u.existing_user,u.is_algo_based,(SELECT MAX(created_at) FROM consumer_account_balances WHERE consumer_id = u.user_id) AS last_balance_fetch_date,(SELECT COUNT(*) FROM consumer_account_balances WHERE consumer_id = u.user_id AND source = "' . ADMIN_REFRESH_BALANCE . '" AND date(CONVERT_TZ(created_at,"+00:00","' . $mst_offset . '")) = "' . Carbon::now("MST")->toDateString() . '") AS refresh_count, bsm.banking_solution_name, uvc.mx_consumer_id
            FROM users u force index(fk_user_role_id)
            straight_join user_roles ur on u.role_id = ur.role_id and ur.role_name = "' . CONSUMER . '"
            LEFT JOIN user_bank_account_info ubai ON u.user_id = ubai.user_id AND ubai.status = ?
            LEFT JOIN user_validation_credentials uvc ON uvc.user_id = u.user_id
            LEFT JOIN banking_solution_masters bsm ON ubai.banking_solution_id = bsm.id
            LEFT JOIN transaction_details td ON u.user_id = td.consumer_id AND td.return_reason IS NOT NULL AND td.is_v1 = 0 AND td.transaction_ref_no IS NULL AND td.isCanpay = 0
            LEFT JOIN status_master sm ON td.status_id = sm.id
            LEFT JOIN status_master smu ON u.status = smu.id ';
        $searchStr = [$bank_active];
        $search_by_name = false;
        if (strlen(trim($request['consumer_first_name'])) >= 3) {
            $search_by_name = true;
            $sql .= ' WHERE u.first_name LIKE ? ';
            array_push($searchStr, '%' . $request['consumer_first_name'] . '%');
        }
        if (strlen(trim($request['consumer_middle_name'])) >= 3) {
            if ($search_by_name) {
                $sql .= ' and u.middle_name LIKE ? ';
            } else {
                $search_by_name = true;
                $sql .= ' WHERE u.middle_name LIKE ? ';
            }
            array_push($searchStr, '%' . $request['consumer_middle_name'] . '%');
        }
        if (strlen(trim($request['consumer_last_name'])) >= 3) {
            if ($search_by_name) {
                $sql .= ' and u.last_name LIKE ? ';
            } else {
                $search_by_name = true;
                $sql .= ' WHERE u.last_name LIKE ? ';
            }
            array_push($searchStr, '%' . $request['consumer_last_name'] . '%');
        }
        if (trim($request['phone_no'])) {
            if ($search_by_name) {
                $sql .= ' and u.phone = ? ';
            } else {
                $sql .= ' where u.phone = ? ';
            }
            array_push($searchStr, $request['phone_no']);
        }
        if (trim($request['email'])) {
            if ($search_by_name || trim($request['phone_no'])) {
                $sql .= ' and u.email = ? ';
            } else {
                $sql .= ' where u.email = ? ';
            }
            array_push($searchStr, $request['email']);
        }
        $sql .= " GROUP BY `u`.`user_id` LIMIT 100";

        $registeredConsumers = DB::connection(MYSQL_RO)->Select($sql, $searchStr);

        $consumerArr = [];
        if (!empty($registeredConsumers)) {
            foreach ($registeredConsumers as $registeredConsumer) {

                $data = [];
                $data['name'] = $registeredConsumer->consumer_name;
                $data['email'] = $registeredConsumer->email;
                $data['phone'] = $registeredConsumer->phone;
                $data['date_of_birth'] = $registeredConsumer->date_of_birth != '' && $registeredConsumer->date_of_birth != '0000-00-00' ? date('m-d-Y', strtotime($registeredConsumer->date_of_birth)) : '';
                $data['active_return_count'] = $registeredConsumer->active_return_count;
                $data['all_time_return_count'] = $registeredConsumer->all_time_return_count;
                $data['status'] = $registeredConsumer->status;
                $data['status_name'] = str_replace("User ", "", $registeredConsumer->status_name);
                $data['bank_link_type'] = ($registeredConsumer->consumer_type == LITE_CONSUMER && $registeredConsumer->active_account_exists == null) ? 'N/A' : ($registeredConsumer->bank_link_type == 1 ? 'Direct Link' : 'Manual Link');
                $data['last_balance_fetch_date'] = $registeredConsumer->last_balance_fetch_date != '' ? date('m-d-Y', strtotime($registeredConsumer->last_balance_fetch_date)) : '';
                $data['refresh_count'] = $registeredConsumer->refresh_count;
                $data['edit'] = $registeredConsumer->user_id;
                $data['lockout'] = $registeredConsumer->lockout;
                $data['active_account_exists'] = $registeredConsumer->active_account_exists != null ? 1 : 0;
                $data['standard_daily_limit'] = $registeredConsumer->standard_daily_limit;
                $data['weekly_spending_limit'] = $registeredConsumer->weekly_spending_limit;
                $data['mx_consumer_id'] = $registeredConsumer->mx_consumer_id;
                $data['banking_solution_name'] = $registeredConsumer->banking_solution_name == MX ? strtoupper(MX) : ucwords($registeredConsumer->banking_solution_name);
                $data['purchase_power_source'] = $registeredConsumer->purchase_power_source;
                $data["show_purchase_power_change_option"] = ($registeredConsumer->bank_link_type == 1 && $registeredConsumer->existing_user == 0) ? true : false;
                $data["is_algo_based"] = $registeredConsumer->is_algo_based;
                $data["mx_user_action_needed"] = $registeredConsumer->mx_user_action_needed;
                $data["consumer_type"] = $registeredConsumer->consumer_type;
                array_push($consumerArr, $data);
            }
        } else {
            $consumerArr = [];
        }

        return $consumerArr;
    }

    /**
     * getAllActiveCorporateParent
     * This function will fetch All the Active CPs
     * @param  mixed $request
     * @return void
     */
    public function getAllActiveCorporateParent(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Corporate Parents Fetched Started.");
        // Validating input request
        $this->validate($request, [
            'searchtxt' => VALIDATION_REQUIRED,
        ]);

        $corporteParents = User::on(MYSQL_RO)->join('user_roles', 'users.role_id', '=', 'user_roles.role_id')
            ->join('status_master', 'status_master.id', '=', 'users.status')
            ->select('users.user_id as id')
            ->selectRaw('REPLACE(CONCAT(COALESCE(REPLACE(users.first_name, " ",""), "")," ",COALESCE(REPLACE(users.middle_name, " ",""), "")," ",COALESCE(REPLACE(users.last_name, " ",""), "")),"  "," ") as corporateparent')
            ->where('user_roles.role_name', CORPORATE_PARENT)
            ->where('status_master.code', USER_ACTIVE)
            ->where('users.default_cp', 0)
            ->whereRaw('lower(concat_ws(" ",first_name,middle_name,last_name)) like "%' . $request->get('searchtxt') . '%"')
            ->orderBy('users.first_name', 'ASC')
            ->limit(50)->get();

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Corporate Parents Fetched Successfully.");
        $message = trans('message.fetch_cp_success');
        // API Response returned with 200 status
        return renderResponse(SUCCESS, $message, $corporteParents);
    }

    /**
     * getCustomPurchasePower
     * This function will fetch v1 consumer purchase power
     * @param  mixed $request
     * @return void
     */
    private function _getCustomPurchasePower($user_details)
    {
        $pending = getStatus(PENDING);
        $returned = getStatus(RETURNED);
        $status_array = [$pending, $returned];
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "User is an existing consumer with id: " . $user_details->user_id);
        
        $bank_account_no = getActiveBankAccountNo($user_details->user_id); // Helper function called to get the Active Bank account no.
        $now = Carbon::now()->timezone(ACHECK21_TIMEZONE_FOR_WEBHOOK);
        $time_at = config('app.custom_purchase_power_update_time') . ':00:00';

        // Check if the current time is before the cutoff time
        $transaction_date = $now->lt($now->toDateString() . ' ' . $time_at) 
            ? $now->subDay()->toDateString() . ' ' . $time_at
            : $now->toDateString() . ' ' . $time_at;

        // Create the Carbon instance and convert to UTC
        $transaction_date_time_utc = Carbon::createFromFormat('Y-m-d H:i:s', $transaction_date, ACHECK21_TIMEZONE_FOR_WEBHOOK)
            ->setTimezone('UTC')
            ->toDateTimeString();

        // Pending transaction amount calculated for a active bank account no. for all consumers with the same account no.
        $transaction = TransactionDetails::join('user_bank_account_info', 'transaction_details.account_id', '=', 'user_bank_account_info.id')->select(DB::raw('COALESCE(SUM(if(transaction_details.status_id IS NOT NULL, consumer_bank_posting_amount+tip_amount, 0)),0) as amount, transaction_details.status_id'))->where('transaction_details.transaction_ref_no', null)->where('transaction_details.isCanpay', 0)->where('transaction_details.is_v1', 0)->wherein('transaction_details.status_id', $status_array)->where("transaction_details.transaction_time", '>', $transaction_date_time_utc)
            ->where(function ($q) use ($bank_account_no, $user_details) {
                $q->where('user_bank_account_info.account_no', $bank_account_no)->orWhere('user_bank_account_info.user_id', $user_details->user_id);
            })->groupBy('transaction_details.status_id')->get();

        $pending_amount = isset($transaction[0]->amount) ? $transaction[0]->amount : 0;
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Today's total pending amount: " . $pending_amount . " for consumer with id: " . $user_details->user_id);
        $purchase_power = $user_details->purchase_power - $pending_amount;

        // Check if the account number has any return transaction
        if (searchMultiArray('status_id', $returned, $transaction) == 1) {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Return exists for the account of consumer with User ID: " . $user_details->user_id . ". Returning Purchase Power 0.");
            return $purchase_power . "(As there is active return against this same account number, so the actual purchase power is not visible to consumer. Consumers are going to see 0 as Purchase Power)";
        }

        $pending_amount = isset($transaction[0]->amount) ? $transaction[0]->amount : 0;
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Today's total pending amount: " . $pending_amount . " for consumer with id: " . $user_details->user_id);
        $purchase_power = $user_details->purchase_power - $pending_amount;
        return $purchase_power;
    }

    /**
     * searchConsumersReturnTransactions
     * Search the Return and Represented Transaction for a particular Consumer
     * @param  mixed $request
     * @return void
     */
    public function searchConsumersReturnTransactions(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Return and Represented Transaction search for Consumer Started with Email : " . $request->get('email') . " AND Phone No." . $request->get('phone_no'));
        // Validating input request
        $this->validate($request, [
            'phone_no' => VALIDATION_REQUIRED_WITHOUT_ALL . ':email',
            'email' => VALIDATION_REQUIRED_WITHOUT_ALL . ':phone_no',
        ]);

        //Search with Return and Represented Transactions
        $transactions = $this->_getConsumerReturnRepresentedTransactions($request);

        $message = trans('message.transaction_search_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Return and Represented Transaction search for Consumer Completed with Email : " . $request->get('email') . " AND Phone No." . $request->get('phone_no'));
        return renderResponse(SUCCESS, $message, $transactions);
    }

    /**
     * _getConsumerReturnRepresentedTransactions
     * Fetch Return and Represented Transactions for a consumer
     * @param  mixed $request
     * @return void
     */
    private function _getConsumerReturnRepresentedTransactions($request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction fetch for a particular consumer started.");

        $sql = "SELECT transaction_details.id, transaction_details.transaction_number,transaction_details.return_reason,users.user_id,users.first_name, users.middle_name, users.last_name,users.phone,users.email,transaction_details.represent_count, transaction_details.amount, transaction_details.tip_amount, transaction_details.transaction_time, transaction_details.local_transaction_time, transaction_details.is_v1, status_master.status, timezone_masters.timezone_name ,return_reason_masters.reason_code,return_reason_masters.title,return_reason_masters.description, td.local_transaction_time as represented_on, (transaction_details.amount + transaction_details.tip_amount) as total_amount FROM transaction_details LEFT JOIN transaction_details td FORCE INDEX(idx_trans_ref_no)  ON td.transaction_ref_no = transaction_details.id AND td.is_represented = 1 LEFT JOIN users ON users.user_id = transaction_details.consumer_id LEFT JOIN timezone_masters ON timezone_masters.id = transaction_details.timezone_id LEFT JOIN return_reason_masters ON return_reason_masters.id = transaction_details.return_reason INNER JOIN status_master ON status_master.id = transaction_details.status_id WHERE transaction_details.transaction_ref_no is null AND transaction_details.isCanpay = 0 AND transaction_details.return_reason is not null AND status_master.code != '" . SUCCESS . "'";

        $searchArray = [];
        if ($request->get('phone_no')) {
            $sql .= " AND users.phone = ? ";
            array_push($searchArray, $request->get('phone_no'));
        }
        if ($request->get('email')) {
            $sql .= " AND users.email = ? ";
            array_push($searchArray, $request->get('email'));
        }

        $sql .= " GROUP BY transaction_details.transaction_number ORDER BY transaction_details.transaction_time DESC";
        $transactions = DB::connection(MYSQL_RO)->select($sql, $searchArray);

        $transactionArr = [];
        if (!empty($transactions)) {
            // Creating array to show the values in frontend
            foreach ($transactions as $transaction) {
                $data = [];
                $data['transaction_number'] = $transaction->transaction_number;
                $data['transaction_time'] = date('m-d-Y H:i:s', strtotime($transaction->local_transaction_time)) . ' <br/> (' . $transaction->timezone_name . ')';
                $data['total_amount'] = $transaction->total_amount;
                $data['status'] = $transaction->status;
                $data['reason_code'] = $transaction->reason_code;
                $data['expected_clearance'] = date('m-d-Y', strtotime($transaction->represented_on) + (24 * 3600 * 6));
                $data['represent_count'] = $transaction->represent_count;
                $data['represented_on'] = date('m-d-Y H:i:s', strtotime($transaction->represented_on)) . ' <br/> (' . $transaction->timezone_name . ')';
                $data['title'] = $transaction->title;
                $data['description'] = $transaction->description;
                $data['transaction_id'] = $transaction->id;

                array_push($transactionArr, $data);
            }
        } else {
            $transactionArr = [];
        }

        return $transactionArr;
    }

    /**
     * releaseConsumers
     * Release Consumers whose return are already posted to Acheck21
     * @param  mixed $request
     * @return void
     */
    public function releaseConsumers(Request $request)
    {
        Log::channel('update-final-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Fetching all the transactions selected transactions...");
        $id_array = explode(",", $request->get('transaction_ids'));
        $transactions = TransactionDetails::whereIn('id', $id_array)->get();
        $success = getStatus(SUCCESS);
        if (sizeof($transactions) != 0) {
            foreach ($transactions as $transaction) {
                // update the parent transaction
                TransactionDetails::where('id', $transaction->id)->update(array('status_id' => $success));
                // created new success row
                $this->_createTransaction($transaction, $success);
            }
            Log::channel('update-final-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transactions updated successfully.");
            $message = trans('message.consumers_release_success');
            return renderResponse(SUCCESS, $message, true);
        } else {
            Log::channel('update-final-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No transactions found to update.");
            $message = trans('message.consumers_release_error');
            return renderResponse(FAIL, $message, true);
        }
    }

    private function _createTransaction($transaction, $success)
    {
        //create a new transaction
        $transaction_details = new TransactionDetails();
        $transaction_details->transaction_number = $transaction->transaction_number;
        $transaction_details->transaction_ref_no = $transaction->id;
        $transaction_details->user_id = $transaction->user_id;
        $transaction_details->consumer_id = $transaction->consumer_id;
        $transaction_details->terminal_id = $transaction->terminal_id;
        $transaction_details->transaction_time = Carbon::now();
        $transaction_details->local_transaction_time = Carbon::now($transaction->timezone_name);
        $transaction_details->local_transaction_year = date("Y", strtotime($transaction_details->local_transaction_time));
        $transaction_details->local_transaction_month = date("m", strtotime($transaction_details->local_transaction_time));
        $transaction_details->local_transaction_date = date("Y-m-d", strtotime($transaction_details->local_transaction_time));
        $transaction_details->timezone_id = $transaction->timezone_id;
        $transaction_details->amount = $transaction->amount;
        $transaction_details->tip_amount = $transaction->tip_amount;
        $transaction_details->tip_type = $transaction->tip_type;
        $transaction_details->tip_add_time = $transaction->tip_add_time;
        $transaction_details->used_qr_id = $transaction->used_qr_id;
        $transaction_details->status_id = $success;
        $transaction_details->transaction_type_id = $transaction->transaction_type_id;
        $transaction_details->save();
        Log::channel('update-final-transaction')->info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction details was stored successfully for parent transaction: " . $transaction->id);
    }
    /**
     * getConsumerReturnTransaction
     * Fetch the Consumer return transaction  for a particular User ID
     * @param  mixed $request
     * @return void
     */
    public function getConsumerReturnTransaction(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer Details Fetch w.r.t User ID started.");
        // Validating input request
        $this->validate($request, [
            'userID' => VALIDATION_REQUIRED,
        ]);
        $transactionStatus = getStatus(SUCCESS);
        //Fetch the Consumer return transaction for a parcticular User ID
        $returnTransaction = TransactionDetails::on(MYSQL_RO)->join('return_reason_masters', 'return_reason_masters.id', '=', 'transaction_details.return_reason')->join('users', 'users.user_id', '=', 'transaction_details.consumer_id')
            ->join('status_master', 'status_master.id', '=', 'transaction_details.status_id')
            ->where('transaction_details.consumer_id', $request->get('userID'))
            ->whereRaw('transaction_details.transaction_ref_no is NULL')
            ->whereRaw('transaction_details.return_reason is NOT NULL')
            ->select('transaction_details.returned_on', 'transaction_details.represent_count', 'return_reason_masters.reason_code', 'transaction_details.updated_at', 'transaction_details.status_id', 'transaction_details.transaction_number', 'transaction_details.amount', 'transaction_details.created_at as transaction_date', 'status_master.status as status_name', 'users.*')
            ->get();
        $message = trans('message.consumer_details_fetch_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer return transaction Details Fetched for User ID : " . $request->get('userID'));

        return renderResponse(SUCCESS, $message, $returnTransaction);
    }

    /**
     * getConsumerComments
     * This function is used to fetch all the comments of a specific consumer
     * @param  mixed $request
     * @return void
     */
    public function getConsumerComments(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer Comments w.r.t User ID started.");
        // Validating input request
        $this->validate($request, [
            'userID' => VALIDATION_REQUIRED,
        ]);

        $comments = ConsumerComment::join('users', 'users.user_id', '=', 'consumer_comments.added_by')->selectRaw('consumer_comments.*, REPLACE(CONCAT(COALESCE(REPLACE(users.first_name, " ",""), "")," ",COALESCE(REPLACE(users.middle_name, " ",""), "")," ",COALESCE(REPLACE(users.last_name, " ",""), "")),"  "," ") as added_by_name, DATE_FORMAT(consumer_comments.created_at, "%D %b, %Y %r") as post_time')->where('consumer_id', $request->get('userID'))->orderBy('created_at', 'DESC')->get();

        $message = trans('message.consumer_comments_fetch_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer Comments Fetched for User ID : " . $request->get('userID'));

        return renderResponse(SUCCESS, $message, $comments);
    }

    /**
     * saveConsumerComment
     * This function is used to save a cooment for a specific consumer
     * @param  mixed $request
     * @return void
     */
    public function saveConsumerComment(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer Comments save process started.");
        // Validating input request
        $this->validate($request, [
            'userID' => VALIDATION_REQUIRED,
            'comment' => VALIDATION_REQUIRED,
        ]);
        $user_details = Auth::user();
        $comment = new ConsumerComment();
        $comment->consumer_id = $request->get('userID');
        $comment->comment = $request->get('comment');
        $comment->added_by = $user_details->user_id;
        $comment->save();

        $comments = ConsumerComment::join('users', 'users.user_id', '=', 'consumer_comments.added_by')->selectRaw('consumer_comments.*, REPLACE(CONCAT(COALESCE(REPLACE(users.first_name, " ",""), "")," ",COALESCE(REPLACE(users.middle_name, " ",""), "")," ",COALESCE(REPLACE(users.last_name, " ",""), "")),"  "," ") as added_by_name, DATE_FORMAT(consumer_comments.created_at, "%D %b, %Y %r") as post_time')->where('consumer_id', $request->get('userID'))->orderBy('created_at', 'DESC')->get();

        $message = trans('message.consumer_comment_save_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer Comment added for User ID : " . $request->get('userID'));

        return renderResponse(SUCCESS, $message, $comments);
    }

    /** This api refreshes balance and purchase power for a consumer in the Admin Panel
     */
    public function fetchConsumerAccountBalance(Request $request)
    {
        // Validating input request
        $this->validate($request, [
            'user_id' => VALIDATION_REQUIRED,
        ]);

        $user = User::where('user_id', $request->get('user_id'))->first(); // Fetching User Details
        //fetch all the finicicty linked accounts for the consumer
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Refreshing all the bank account balances and corresponding purchase power for consumer with User ID : " . $request->get('user_id'));
        $response = $this->bankvalidator->getConsumerAccountDetails($user);
        $status = getStatus(BANK_ACTIVE);
        if (sizeof($response) == 0) {
            $message = trans('message.all_accounts_delink_state');
            return renderResponse(FAIL, $message, null);
        }

        foreach ($response as $res) {
            $bank_account = BankAccountInfo::where(['user_id' => $request->get('user_id'), 'account_id' => $res['id']])->first();
            // Refresh balance for only Active Bank Account
            if (!empty($bank_account) && $status === $bank_account->status) {
                // calculate the purchase power
                $availableBalanceAmount = isset($res['detail']['availableBalanceAmount']) ? $res['detail']['availableBalanceAmount'] : 0;
                $balance = $availableBalanceAmount > $res['balance'] ? $availableBalanceAmount : $res['balance'];
                $purchase_power = $this->purchasePower->calculatePurchasePower($balance, $user);
                // add the bank balance into DB
                $consumer_balance = new ConsumerAccountBalance();
                $consumer_balance->consumer_id = $request->get('user_id');
                $consumer_balance->account_id = $bank_account->id;
                $consumer_balance->balance = $balance;
                $consumer_balance->response_raw_balance = $res['balance'];
                $consumer_balance->response_available_balance = $availableBalanceAmount;
                $consumer_balance->purchase_power = $purchase_power;
                $consumer_balance->source = ADMIN_BALANCE_FETCH;
                $consumer_balance->save();

                // Check if Refresh Balance is called from Admin Panel for the day in MST time
                $mst_offset = getMstTime();
                $admin_refresh_balance = ConsumerAccountBalance::where(['source' => ADMIN_REFRESH_BALANCE, 'consumer_id' => $request->get('user_id')])->whereRaw('date(CONVERT_TZ(created_at,"+00:00",?)) = ?', [$mst_offset, Carbon::now('MST')->toDateString()])->get()->count();
                if ($admin_refresh_balance == 0) {
                    // if this account is primary account then update purchase power in the users table
                    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Calculated purchase power for phone: " . $user->phone . " is : $" . $purchase_power);
                    User::where('user_id', $request->get('user_id'))->update(array('purchase_power' => $purchase_power, 'disable_automatic_purchase_power' => 0));
                    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Consumer purchase power updated into database successfully.");
                }
            }
        }
        $message = trans('message.balance_refresh');
        return renderResponse(SUCCESS, $message, null);
    }

    /**
     * _checkLastTwoBalance
     * This function will check if last two bank balance is nagative return true else false
     */
    private function _checkLastTwoBalance($user_details)
    {

        //fetch last two balance amount
        $bank_balance = BankAccountInfo::join('status_master', 'user_bank_account_info.status', '=', 'status_master.id')->join('consumer_account_balances', 'user_bank_account_info.id', '=', 'consumer_account_balances.account_id')->select('consumer_account_balances.balance as balance')
            ->where('user_id', $user_details->user_id)
            ->where('status_master.code', BANK_ACTIVE)
            ->orderBy('consumer_account_balances.created_at', 'desc')
            ->limit(2)
            ->get();

        //check last two balance any of them is positive or not
        foreach ($bank_balance as $balance) {
            if ($balance->balance >= 0) {
                return false;
            }
        }
        return true;
    }

    /** This api refreshes balance and purchase power for a consumer in the Admin Panel
     */
    public function refreshConsumerAccountBalance(Request $request)
    {
        $token = generateToken();
        $params['api'] = config('app.canpay_api_url') . '/admin/refreshconsumeraccountbalance';
        $params['headers'] = [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
            'Authorization' => extractTokenFromHttpResponse($token),
        ];
        $params['body'] = [
            'user_id' => $request->get('user_id'),
        ];
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Calling refresh balance with body: ", $params['body']);
        try {
            $response = $this->client->request('POST', $params['api'], [
                'headers' => $params['headers'],
                'json' => $params['body'],
            ]);
            Log::info(__METHOD__ . "(" . __LINE__ . ") - Refresh Balance called for API " . $params['api'] . " returned response code: " . $response->getStatusCode());
            $message = trans('message.balance_refresh');
            return renderResponse(SUCCESS, $message, null);
        } catch (\GuzzleHttp\Exception\ClientException $ex) {
            $responseCode = $ex->getResponse()->getStatusCode();
            $responseBody = json_decode($ex->getResponse()->getBody()->getContents(), true);
            $errorMessage = isset($responseBody['message']) ? $responseBody['message'] : trans('message.balance_refresh_error');
            Log::error(__METHOD__ . "(Line: " . __LINE__ . ") - ClientException while calling refresh balance function from Factory.", ['Exception' => $ex]);
            return renderResponse($responseCode, $errorMessage, null);
        } catch (\GuzzleHttp\Exception\RequestException $ex) {
            $responseCode = $ex->hasResponse() ? $ex->getResponse()->getStatusCode() : 500;
            $responseBody = $ex->hasResponse() ? json_decode($ex->getResponse()->getBody()->getContents(), true) : null;
            $errorMessage = isset($responseBody['message']) ? $responseBody['message'] : trans('message.balance_refresh_error');
            Log::error(__METHOD__ . "(Line: " . __LINE__ . ") - RequestException while calling refresh balance function from Factory.", ['Exception' => $ex]);
            return renderResponse($responseCode, $errorMessage, null);
        } catch (\Exception $ex) {
            $errorMessage = $ex->getMessage();
            Log::error(__METHOD__ . "(Line: " . __LINE__ . ") - Exception while calling refresh balance function from Factory.", ['Exception' => $ex]);
            return renderResponse(FAIL, $errorMessage, null);
        }
    }

    /**
     * viewConsumerBalance
     * Get the list of Balances for a Consumer and a particular date
     * @param  mixed $request
     * @return void
     */
    public function viewConsumerBalance(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer View Balance started...");
        // Validating input request
        $this->validate($request, [
            'consumer' => VALIDATION_REQUIRED_WITHOUT_ALL . ':phone_no,email',
            'phone_no' => VALIDATION_REQUIRED_WITHOUT_ALL . ':email,consumer',
            'email' => VALIDATION_REQUIRED_WITHOUT_ALL . ':phone_no,consumer',
        ]);

        // Balance fetch for a consumer and date
        $consumersBalance = $this->_getConsumerBalances($request);

        $message = trans('message.consumer_balance_fetch_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer View Balance complete...");
        return renderResponse(SUCCESS, $message, $consumersBalance);
    }

    /**
     * _getConsumerBalances
     * Function to get the Balance for a Consumer
     * @param  mixed $request
     * @return void
     */
    private function _getConsumerBalances($request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Fetch the Consumer Account Balance for a particular Consumer and active account for date " . $request['start_date']);

        //Bank Active Status ID
        $bankActiveStatus = getStatus(BANK_ACTIVE);

        $searchStr = [];
        $consumer = 'SELECT user_id,first_name,middle_name,last_name,phone,email FROM users WHERE 1 ';
        if (trim($request['consumer'])) {
            $consumer .= ' AND LOWER(
                REPLACE(CONCAT(COALESCE(
                REPLACE(first_name, " ",""), "")," ", COALESCE(
                REPLACE(middle_name, " ",""), "")," ", COALESCE(
                REPLACE(last_name, " ",""), "")),"  "," ")) LIKE  ? ';
            array_push($searchStr, '%' . $request['consumer'] . '%');
        }
        if (trim($request['phone_no'])) {
            $consumer .= ' AND phone = ' . "?";
            array_push($searchStr, $request['phone_no']);
        }
        if (trim($request['email'])) {
            $consumer .= ' AND email = ' . "?";
            array_push($searchStr, $request['email']);
        }
        $consumerID = DB::connection(MYSQL_RO)->select($consumer, $searchStr);

        $pst_offset = getPstTime();
        $userDetails = [];
        $consumerBalanceFetchHistory = [];
        if (!empty($consumerID)) {
            //Fetch Consumer Fetched Balance
            $sql = 'SELECT cab.source, cab.balance, cab.response_available_balance, cab.created_at FROM consumer_account_balances AS cab INNER JOIN user_bank_account_info AS ubai ON ubai.user_id = cab.consumer_id AND ubai.status = ? WHERE date(CONVERT_TZ(cab.created_at,"+00:00","' . $pst_offset . '")) = ? AND cab.consumer_id = ?  AND cab.source  NOT IN ("' . OTHERS . '")  ORDER BY CONCAT(SOURCE),cab.created_at ASC ';
            $consumerBalanceFetchHistory = DB::connection(MYSQL_RO)->select($sql, [$bankActiveStatus, $request['start_date'], $consumerID[0]->user_id]);
            $userDetails = $consumerID[0];
        }

        $consumerArr = [];
        if (!empty($consumerBalanceFetchHistory)) {
            foreach ($consumerBalanceFetchHistory as $balanceHistory) {

                $data = [];
                $data['source'] = ucwords(str_replace("_", " ", $balanceHistory->source));
                $data['balance'] = $balanceHistory->balance;
                $data['available_balance'] = $balanceHistory->response_available_balance;
                $data['balance_fetch_time'] = Carbon::parse($balanceHistory->created_at)->timezone(PST)->format('m-d-Y h:i:s A');
                array_push($consumerArr, $data);
            }
        }

        return [
            'userDetails' => $userDetails,
            'balanceFetchArr' => $consumerArr,
        ];
    }

    /**
     * searchConsumers
     * Search the Consumers based on search Criteria
     * @param  mixed $request
     * @return void
     */
    public function searchConsumers(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Suspected Consumer Search Started.");
        // Validating input request
        $this->validate($request, [
            'consumer' => VALIDATION_REQUIRED_WITHOUT_ALL . ':phone_no,email,start_date,end_date',
            'phone_no' => VALIDATION_REQUIRED_WITHOUT_ALL . ':email,consumer,start_date,end_date',
            'email' => VALIDATION_REQUIRED_WITHOUT_ALL . ':phone_no,consumer,start_date,end_date',
            'start_date' => VALIDATION_REQUIRED_WITHOUT_ALL . ':phone_no,consumer,email,end_date',
            'end_date' => VALIDATION_REQUIRED_WITHOUT_ALL . ':phone_no,consumer,start_date,email',
        ]);

        $bankActiveStatus = getStatus(BANK_ACTIVE);
        $suspected_fraud_status = getStatus(SUSPECTED_FRAUD);
        //Fetch Consumers
        $sql = 'SELECT `u`.* ,REPLACE(CONCAT(COALESCE(
            REPLACE(u.first_name, " ",""), "")," ", COALESCE(
            REPLACE(u.middle_name, " ",""), "")," ", COALESCE(
            REPLACE(u.last_name, " ",""), "")),"  "," ") AS consumer_name, ubai.routing_no, ubai.account_no
            FROM users u force index(fk_user_role_id)
            straight_join user_roles ur on u.role_id = ur.role_id and ur.role_name = "' . CONSUMER . '"
            left join user_bank_account_info ubai ON ubai.user_id = u.user_id AND ubai.status = "' . $bankActiveStatus . '"
            WHERE 1 ';

        $searchStr = [];
        if (strlen(trim($request['consumer'])) >= 3) {
            $sql .= ' AND LOWER(
                REPLACE(CONCAT(COALESCE(
                REPLACE(u.first_name, " ",""), "")," ", COALESCE(
                REPLACE(u.middle_name, " ",""), "")," ", COALESCE(
                REPLACE(u.last_name, " ",""), "")),"  "," ")) LIKE ? ';
            array_push($searchStr, '%' . $request['consumer'] . '%');
        }
        if (trim($request['phone_no'])) {
            $sql .= ' and u.phone = ? ';
            array_push($searchStr, $request['phone_no']);
        }
        if (trim($request['email'])) {
            $sql .= ' and u.email = ? ';
            array_push($searchStr, $request['email']);
        }
        if (trim($request['start_date']) && trim($request['end_date'])) {
            $sql .= ' and IF(u.existing_user = 1, DATE(u.migrated_at), DATE(u.created_at)) BETWEEN ? and ? ';
            array_push($searchStr, $request['start_date']);
            array_push($searchStr, $request['end_date']);
        }
        $sql .= " GROUP BY `u`.`user_id` LIMIT 100";

        $consumers = DB::connection(MYSQL_RO)->Select($sql, $searchStr);

        $consumerArr = [];
        if (!empty($consumers)) {
            foreach ($consumers as $consumer) {

                $data = [];
                $data['name'] = $consumer->consumer_name;
                $data['email'] = $consumer->email;
                $data['phone'] = $consumer->phone;
                $data['age'] = $consumer->date_of_birth != '' && $consumer->date_of_birth != '0000-00-00' ? date_diff(date_create($consumer->date_of_birth), date_create('today'))->y . '<br> (' . date('m-d-Y', strtotime($consumer->date_of_birth)) . ')' : '';
                $data['address'] = ($consumer->street_address ?? '') . (!empty($consumer->street_address) && $consumer->city ? ', ' : '') . ($consumer->city ?? '') . (!empty($consumer->city) && $consumer->state ? ', ' : '') . ($consumer->state ?? '') . (!empty($consumer->state) && $consumer->zipcode ? ', ' : '') . ($consumer->zipcode ?? '');
                $data['enroll_date'] = $consumer->existing_user == 1 ? date('m-d-Y', strtotime($consumer->migrated_at)) : date('m-d-Y', strtotime($consumer->created_at));
                $data['bank_link_type'] = ($consumer->consumer_type == LITE_CONSUMER && !$consumer->account_no) ? 'N/A' : ($consumer->bank_link_type == 1 ? 'Direct Link' : 'Manual Link');
                $data['routing_no'] = ($consumer->consumer_type == LITE_CONSUMER && !$consumer->account_no) ? 'N/A' : $consumer->routing_no;
                $data['account_no'] = ($consumer->consumer_type == LITE_CONSUMER && !$consumer->account_no) ? 'N/A' : $consumer->account_no;
                $data['hidden_account_no'] = ($consumer->consumer_type == LITE_CONSUMER && !$consumer->account_no) ? 'N/A' : preg_replace('/[A-Za-z0-9]/', 'X', $consumer->account_no);
                $data['is_suspected'] = $consumer->status == $suspected_fraud_status ? 1 : 0;
                $data['edit'] = $consumer->user_id;
                $data['consumer_type'] = $consumer->consumer_type;

                array_push($consumerArr, $data);
            }
        } else {
            $consumerArr = [];
        }

        $message = trans('message.consumer_search_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Suspected Consumer Search Complete");
        return renderResponse(SUCCESS, $message, $consumerArr);
    }

    /**
     * This API will mark a consumer as a suspected one or remove him/her from the suspected list
     */
    public function toggleSuspectedConsumer(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer suspected status change process started...");
        // Validating input request
        $this->validate($request, [
            'id' => VALIDATION_REQUIRED,
        ]);

        try {
            $suspected_fraud_status = getStatus(SUSPECTED_FRAUD);
            $active_status = getStatus(USER_ACTIVE);
            $user = User::where("user_id", $request->get('id'))->first();
            // toggle the value
            $user->status = $user->status == $suspected_fraud_status ? $active_status : $suspected_fraud_status;
            $currentstatus = $user->status == $suspected_fraud_status ? $active_status : $suspected_fraud_status;

            // Add the status in the history table for this consumer
            saveStatusHistory(STATUS_UPDATED_BY_ADMIN, 'Suspected fraud status update from Suspected Fraud Listing Page', $currentstatus, $user->user_id);

            $user->previously_marked_as_suspected = $user->status == $suspected_fraud_status ? 0 : 1;
            $user->save();
            if ($user->status == $suspected_fraud_status) {
                // Add all the bank accounts of the consumer to the blacklist table
                $bank_accounts = BankAccountInfo::where('user_id', $user->user_id)->get();
                foreach ($bank_accounts as $bank_details) {
                    addAccountToBlacklist($bank_details);
                }
                // Remove all accounts from whitelist table for this user
                ValidAccountNumber::where('consumer_id', $user->user_id)->delete();
            } else {
                // Add the consumer's current bank account in whitelist
                addAccountToWhitelist($user->user_id);
                if ($user->consumer_type == LITE_CONSUMER) {
                    $api = new ApiHttpClient();
                    $api->updateConsumerLiteToStandard($user->user_id);
                }
            }
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Consumer with ID: " . $user->user_id . " " . $user->status != $suspected_fraud_status ? " removed from suspected list." : " marked as a Suspected Consumer.");
            $message = $user->status != $suspected_fraud_status ? trans('message.unmarked_as_suspected') : trans('message.marked_as_suspected');
            // API Response returned with 200 status
            return renderResponse(SUCCESS, $message, null);
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured while updating consumer suspected status", [EXCEPTION => $e]);
            $message = trans('message.db_transaction_failed');
            // Exception Returned
            return renderResponse(FAIL, $message, null);
        }
    }

    /**
     * exportSuspectedConsumersReport
     * This function will export all the suspected consumers in Excel file
     * @return void
     */
    public function exportSuspectedConsumersReport()
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Export operation for all Suspected Consuemrs started...");

        $bankActiveStatus = getStatus(BANK_ACTIVE);
        $suspected_fraud_status = getStatus(SUSPECTED_FRAUD);
        //Fetch Consumers
        $consumers = DB::connection(MYSQL_RO)->select('SELECT `u`.* ,REPLACE(CONCAT(COALESCE(
            REPLACE(u.first_name, " ",""), "")," ", COALESCE(
            REPLACE(u.middle_name, " ",""), "")," ", COALESCE(
            REPLACE(u.last_name, " ",""), "")),"  "," ") AS consumer_name, ubai.routing_no, ubai.account_no
            FROM users u force index(fk_user_role_id)
            straight_join user_roles ur on u.role_id = ur.role_id and ur.role_name = "' . CONSUMER . '"
            straight_join user_bank_account_info ubai ON ubai.user_id = u.user_id  AND ubai.status = "' . $bankActiveStatus . '"
            WHERE u.status = "' . $suspected_fraud_status . '" GROUP BY `u`.`user_id`');

        $consumerArr = [];
        if (!empty($consumers)) {
            foreach ($consumers as $consumer) {

                $data = [];
                $data['name'] = $consumer->consumer_name;
                $data['email'] = $consumer->email;
                $data['phone'] = $consumer->phone;
                $data['age'] = $consumer->date_of_birth != '' && $consumer->date_of_birth != '0000-00-00' ? date_diff(date_create($consumer->date_of_birth), date_create('today'))->y . ' (' . date('m-d-Y', strtotime($consumer->date_of_birth)) . ')' : '';
                $data['address'] = $consumer->street_address . ', ' . $consumer->city . ', ' . $consumer->state . ', ' . $consumer->zipcode;
                $data['enroll_date'] = $consumer->existing_user == 1 ? date('m-d-Y', strtotime($consumer->migrated_at)) : date('m-d-Y', strtotime($consumer->created_at));
                $data['bank_link_type'] = $consumer->bank_link_type == 1 ? 'Direct Link' : 'Manual Link';
                $data['routing_no'] = $consumer->routing_no;
                $data['account_no'] = $consumer->account_no;

                array_push($consumerArr, $data);
            }
        } else {
            $consumerArr = [];
        }
        $returnResponse = array(
            'report' => $consumerArr,
        );

        return Excel::download(new SuspectedConsumersReportExport($returnResponse), 'suspected_consumers_' . date('m-d-Y H:i:s') . '.xlsx');
    }

    /**
     * searchConsumersWithProbableRetuns
     * This fucntion will search the consumer with probable returns
     * @param  mixed $request
     * @return void
     */
    public function searchConsumersWithProbableRetuns(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer search started for probale return...");
        // Validating input request
        $this->validate($request, [
            'consumer' => VALIDATION_REQUIRED_WITHOUT_ALL . ':phone_no,email',
            'phone_no' => VALIDATION_REQUIRED_WITHOUT_ALL . ':email,consumer',
            'email' => VALIDATION_REQUIRED_WITHOUT_ALL . ':phone_no,consumer',
        ]);

        $sql = 'SELECT REPLACE(CONCAT(COALESCE(
            REPLACE(u.first_name, " ",""), "")," ", COALESCE(
            REPLACE(u.middle_name, " ",""), "")," ", COALESCE(
            REPLACE(u.last_name, " ",""), "")),"  "," ") AS consumer_name, u.user_id, u.email, u.phone, bdfpr.id, bdfpr.created_at
            FROM users u force index(fk_user_role_id)
            straight_join user_roles ur on u.role_id = ur.role_id and ur.role_name = "' . CONSUMER . '"
            JOIN user_bank_account_info ubai ON ubai.user_id = u.user_id
            JOIN bank_details_for_probable_returns bdfpr ON ubai.account_no = bdfpr.account_no AND ubai.routing_no = bdfpr.routing_no';

        $searchStr = [];
        if (strlen(trim($request['consumer'])) >= 3) {
            $sql .= ' WHERE LOWER(
                REPLACE(CONCAT(COALESCE(
                REPLACE(u.first_name, " ",""), "")," ", COALESCE(
                REPLACE(u.middle_name, " ",""), "")," ", COALESCE(
                REPLACE(u.last_name, " ",""), "")),"  "," ")) LIKE ? ';
            array_push($searchStr, '%' . $request['consumer'] . '%');
        }
        if (trim($request['phone_no'])) {
            if (strlen(trim($request['consumer'])) >= 3) {
                $sql .= ' and u.phone = ? ';
            } else {
                $sql .= ' where u.phone = ? ';
            }
            array_push($searchStr, $request['phone_no']);
        }
        if (trim($request['email'])) {
            if (strlen(trim($request['consumer'])) >= 3 || trim($request['phone_no'])) {
                $sql .= ' and u.email = ? ';
            } else {
                $sql .= ' where u.email = ? ';
            }
            array_push($searchStr, $request['email']);
        }
        $sql .= " GROUP BY `u`.`user_id` LIMIT 100";

        $consumers = DB::connection(MYSQL_RO)->Select($sql, $searchStr);

        $consumerArr = [];
        if (!empty($consumers)) {
            foreach ($consumers as $consumer) {

                $data = [];
                $data['consumer_id'] = $consumer->user_id;
                $data['name'] = $consumer->consumer_name;
                $data['email'] = $consumer->email;
                $data['phone'] = $consumer->phone;
                $data['created_at'] = $consumer->created_at;
                $data['edit'] = $consumer->id;

                array_push($consumerArr, $data);
            }
        } else {
            $consumerArr = [];
        }

        $message = trans('message.consumer_search_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer Search Completed for probable return");
        return renderResponse(SUCCESS, $message, $consumerArr);
    }

    /**
     * releaseConsumerFromReturn
     * This function will release the consumer from probable return
     * @param  mixed $request
     * @return void
     */
    public function releaseConsumerFromReturn(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Release consuemr from probable return started...");
        // Validating input request
        $this->validate($request, [
            'id' => VALIDATION_REQUIRED,
            'consumer_id' => VALIDATION_REQUIRED,
        ]);

        $checkIdExists = BankDetailsForProbableReturn::find($request->get('id'));
        if (empty($checkIdExists)) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "No bank details found for given id in Probable return list.");
            $message = trans('message.release_conusmer_failed');
            // Exception Returned
            return renderResponse(FAIL, $message, null);
        }
        $checkIdExists->delete();
        $message = trans('message.release_conusmer_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer with ID: " . $request->get('consumer_id') . " released successfully from probable return.");
        return renderResponse(SUCCESS, $message, null);
    }

    /**
     * exportAllConsumersWithProbableRetuns
     * This function will export all the consuemrs data with probable return
     * @return void
     */
    public function exportAllConsumersWithProbableRetuns()
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "All consuemrs with probable return export started...");

        $sql = 'SELECT REPLACE(CONCAT(COALESCE(
        REPLACE(u.first_name, " ",""), "")," ", COALESCE(
        REPLACE(u.middle_name, " ",""), "")," ", COALESCE(
        REPLACE(u.last_name, " ",""), "")),"  "," ") AS consumer_name, u.user_id, u.email, u.phone, bdfpr.id, bdfpr.created_at
        FROM users u force index(fk_user_role_id)
        straight_join user_roles ur on u.role_id = ur.role_id and ur.role_name = "' . CONSUMER . '"
        JOIN user_bank_account_info ubai ON ubai.user_id = u.user_id
        JOIN bank_details_for_probable_returns bdfpr ON ubai.account_no = bdfpr.account_no AND ubai.routing_no = bdfpr.routing_no GROUP BY `u`.`user_id`';

        $consumers = DB::connection(MYSQL_RO)->Select($sql);

        $consumerArr = [];
        if (!empty($consumers)) {
            foreach ($consumers as $consumer) {

                $data = [];
                $data['consumer_name'] = $consumer->consumer_name;
                $data['email'] = $consumer->email;
                $data['phone'] = $consumer->phone;
                $data['on_hold_from'] = $consumer->created_at;

                array_push($consumerArr, $data);
            }
        } else {
            $consumerArr = [];
        }
        $returnResponse = array(
            'report' => $consumerArr,
        );

        return Excel::download(new AllConsumersWitProbableReturnExport($returnResponse), 'all_consumers_with_probable_return_' . date('m-d-Y H:i:s') . '.xlsx');
    }

    public function unlockConsumer(Request $request)
    {

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer lockout unlocking process start USER ID:" . $request['user_id']);

        $user = User::where('user_id', $request->user_id)->first();

        if ($user && $user->lockout === 1) {

            User::where('user_id', $request->user_id)->update([
                'lockout' => 0,
            ]);

            $message = 'User Successfully Unlocked.';
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer lockout unlocking process complete USER ID:" . $request['user_id']);
            return renderResponse(SUCCESS, $message, null);
        } else {
            $message = 'No User Found';
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer lockout unlocking process revoked USER ID:" . $request['user_id']);
            return renderResponse(SUCCESS, $message, null);
        }
    }

    /**
     * searchConsumerAccounts
     * Search the Consumers based on search Criteria
     * @param  mixed $request
     * @return void
     */
    public function searchConsumerAccounts(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer Accounts Search Started...");
        // Validating input request
        $this->validate($request, [
            'consumer' => VALIDATION_REQUIRED_WITHOUT_ALL . ':phone_no,email',
            'phone_no' => VALIDATION_REQUIRED_WITHOUT_ALL . ':email,consumer',
            'email' => VALIDATION_REQUIRED_WITHOUT_ALL . ':phone_no,consumer',
        ]);

        // Search the bank account lists for the consumers
        $consumers = $this->_getConsumersBankList($request);

        $message = trans('message.consumer_accounts_search_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer Accounts Search Complete.");
        return renderResponse(SUCCESS, $message, $consumers);
    }

    /**
     * _getConsumersBankList
     * Fetch the Consumers bank account lists
     * @param  mixed $searchArray
     * @return void
     */
    private function _getConsumersBankList($request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer Bank list search started...");

        $bank_active = getStatus(BANK_ACTIVE);
        $bank_inactive = getStatus(BANK_INACTIVE);
        //Fetch Registered Consumers
        $sql = "SELECT REPLACE(CONCAT(COALESCE(
            REPLACE(u.first_name, ' ', ''), ''), ' ', COALESCE(
            REPLACE(u.middle_name, ' ', ''), ''), ' ', COALESCE(
            REPLACE(u.last_name, ' ', ''), '')), '  ', ' ') AS consumer_name, u.email, u.phone, count(ubaoi.id) AS account_owner_called, MAX(ubaoi.created_at) AS last_call_date, ubai.*,IF(ubai1.id IS NOT NULL, CONCAT('(x',SUBSTRING(ubai1.account_no,-4),') ','x',SUBSTRING(ubai.account_no,-4)), CONCAT('x',SUBSTRING(ubai.account_no, -4))) acc_no, IF(ubai1.id IS NOT NULL, ubai1.routing_no, ubai.routing_no) r_no,sm.status, frnm.akoya_provider_id, bsm.banking_solution_name, mimach.id AS mx_record_id
            FROM users u
            JOIN user_bank_account_info ubai ON u.user_id = ubai.user_id
            LEFT JOIN user_bank_account_info as ubai1 ON ubai.id = ubai1.ref_no
            LEFT join fed_routing_number_masters as frnm on frnm.id = ubai.fed_bank_id
            LEFT join banking_solution_masters as bsm on bsm.id = ubai.banking_solution_id
            LEFT JOIN user_bank_account_owner_info AS ubaoi ON ubaoi.account_id = ubai.id
            LEFT JOIN mx_identify_member_api_call_history AS mimach ON u.user_id = mimach.consumer_id AND mimach.completed_at IS NULL AND mimach.source = '" . ADMIN . "' AND mimach.retry_count > 0
            JOIN status_master sm ON sm.id = ubai.status
            WHERE ubai.ref_no IS NULL AND ubai.account_id IS NOT NULL and ubai.status IN (?, ?)";

        $searchStr = [$bank_active, $bank_inactive];
        if (strlen(trim($request['consumer'])) >= 3) {
            $sql .= ' AND LOWER(
                REPLACE(CONCAT(COALESCE(
                REPLACE(u.first_name, " ",""), "")," ", COALESCE(
                REPLACE(u.middle_name, " ",""), "")," ", COALESCE(
                REPLACE(u.last_name, " ",""), "")),"  "," ")) LIKE ? ';
            array_push($searchStr, '%' . $request['consumer'] . '%');
        }
        if (trim($request['phone_no'])) {
            $sql .= ' AND u.phone = ? ';
            array_push($searchStr, $request['phone_no']);
        }
        if (trim($request['email'])) {
            $sql .= ' AND u.email = ? ';
            array_push($searchStr, $request['email']);
        }
        $sql .= " GROUP BY ubai.id ORDER BY ubai.created_at DESC ";

        $consumerBankLists = DB::connection(MYSQL_RO)->Select($sql, $searchStr);

        $consumerArr = [];
        if (!empty($consumerBankLists)) {
            foreach ($consumerBankLists as $bank) {

                $data = [];
                $data['name'] = $bank->consumer_name;
                $data['email'] = $bank->email;
                $data['phone'] = $bank->phone;
                $data['account_no'] = $bank->acc_no;
                $data['routing_no'] = $bank->r_no;
                $data['status'] = $bank->status;
                $data['bank_name'] = $bank->bank_name;
                $data['akoya_provider_id'] = $bank->akoya_provider_id;
                $data['banking_solution_name'] = $bank->banking_solution_name;
                $data['account_owner_called'] = $bank->account_owner_called != null ? 1 : 0;
                $data['account_id'] = $bank->id;
                $data['last_call_date'] = $bank->last_call_date != null ? date("m-d-Y", strtotime($bank->last_call_date)) : null;
                $data['created_on'] = date("m-d-Y", strtotime($bank->created_at));
                $data['edit'] = $bank->user_id;
                $data['mx_record_id'] = $bank->mx_record_id;

                array_push($consumerArr, $data);
            }
        } else {
            $consumerArr = [];
        }

        return $consumerArr;
    }

    /**
     * storeFinicityAccountOwnerInfo
     * This fucntion will fetch and store the account owner information from finicity for a consumer's active bank account
     * @return void
     */
    public function storeFinicityAccountOwnerInfo(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Account owner information fetch process started...");

        // Validate input requests
        $this->validate($request, [
            'user_id' => VALIDATION_REQUIRED,
            'account_id' => VALIDATION_REQUIRED,
        ]);

        // Get consumer's current active bank account details.
        $bank_details = UserBankAccountInfo::leftJoin('banking_solution_masters as bsm', 'user_bank_account_info.banking_solution_id', '=', 'bsm.id')->join('status_master', 'status_master.id', '=', 'user_bank_account_info.status')->join('users', 'users.user_id', '=', 'user_bank_account_info.user_id')->select('user_bank_account_info.*', 'users.phone', 'bsm.banking_solution_name')->where(['user_bank_account_info.user_id' => $request['user_id'], 'user_bank_account_info.id' => $request['account_id']])->whereNotNull('user_bank_account_info.account_id')->whereNotNull('user_bank_account_info.account_id')->first();

        $data['user_id'] = $bank_details->user_id;
        $data['account_id'] = $bank_details->id;
        $account_count = 0;
        if ($bank_details->banking_solution_name == AKOYA) {
            // Call the akoya to get the owner information and store it in database
            $response = $this->akoyaFactory->getAccountOwnerInfo($bank_details);
            $account_count++;
        } else if ($bank_details->banking_solution_name == MX) {
            if ($bank_details->mx_user_action_needed == 1) {
                $message = trans('message.consumer_problematic_status_detected');
                return renderResponse(FAIL, $message, null);
            } else if ($bank_details->mx_non_actionable_status_detected == 1) {
                $message = trans('message.consumer_non_actionable_problematic_status_detected');
                return renderResponse(FAIL, $message, null);
            }
            // Call the account onwer API for MX
            $response = $this->_callIdentifyMember($bank_details);
            return $response;
        } else {
            // Call the finicity to get the owner information and store it in database
            $response = $this->bankvalidator->getAccountOwnerInfo($bank_details);
            $account_count++;
        }

        if ($bank_details->banking_solution_name != MX) {
            $response_array = json_decode(json_encode($response), true);
            Log::info($response_array);
            $data['owner_name'] = isset($response_array['ownerName']) ? $response_array['ownerName'] : null;
            $data['owner_address'] = isset($response_array['ownerAddress']) ? $response_array['ownerAddress'] : null;
            $data['as_of_date'] = isset($response_array['asOfDate']) ? $response_array['asOfDate'] : null;
            $data['raw_response'] = json_encode($response);
            $this->_addBankAccountOwnerInfo($data);
        }
        DB::commit();

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Account owner information stored into database for Consumer ID: " . $request['user_id']);
        $message = trans('message.consumer_account_owner_info_success');
        return renderResponse(SUCCESS, $message, $account_count);
    }

    /** This api refreshes balance and purchase power for a consumer in the Admin Panel
     */
    private function _callIdentifyMember($bank_details)
    {
        $token = generateToken();
        $params['api'] = config('app.canpay_api_url') . '/admin/identifymembercall';
        $params['headers'] = [
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
            'Authorization' => extractTokenFromHttpResponse($token),
        ];
        $params['body'] = [
            'bank_details' => $bank_details,
        ];
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Calling Identify member API with body: ", $params['body']);
        try {
            $response = $this->client->request('POST', $params['api'], [
                'headers' => $params['headers'],
                'json' => $params['body'],
            ]);
            // Extract the status code and the response message
            $statusCode = $response->getStatusCode();
            $responseBody = $response->getBody()->getContents();
            // Log the status code and response message
            Log::info(__METHOD__ . "(" . __LINE__ . ") - Identify Member API called for API " . $params['api'] . " returned response code: " . $statusCode . ", response body: " . $responseBody);

            // Define the message to return based on the status code
            $message = ($statusCode == 200) ? trans('message.member_identity_call_success') : trans('message.member_identity_call_fail');
            return renderResponse(($statusCode == 200) ? SUCCESS : FAIL, $message, null);
        } catch (\GuzzleHttp\Exception\ClientException $ex) {
            $responseCode = $ex->getResponse()->getStatusCode();
            $responseBody = json_decode($ex->getResponse()->getBody()->getContents(), true);
            $errorMessage = isset($responseBody['message']) ? $responseBody['message'] : trans('message.identify_member_error');
            Log::error(__METHOD__ . "(Line: " . __LINE__ . ") - ClientException while calling identify member function from Factory.", ['Exception' => $ex]);
            return renderResponse($responseCode, $errorMessage, null);
        } catch (\GuzzleHttp\Exception\RequestException $ex) {
            $responseCode = $ex->hasResponse() ? $ex->getResponse()->getStatusCode() : 500;
            $responseBody = $ex->hasResponse() ? json_decode($ex->getResponse()->getBody()->getContents(), true) : null;
            $errorMessage = isset($responseBody['message']) ? $responseBody['message'] : trans('message.identify_member_error');
            Log::error(__METHOD__ . "(Line: " . __LINE__ . ") - RequestException while identify member function from Factory.", ['Exception' => $ex]);
            return renderResponse($responseCode, $errorMessage, null);
        } catch (\Exception $ex) {
            $errorMessage = $ex->getMessage();
            Log::error(__METHOD__ . "(Line: " . __LINE__ . ") - Exception while identify member function from Factory.", ['Exception' => $ex]);
            return renderResponse(FAIL, $errorMessage, null);
        }
    }

    /**
     * _addBankAccountOwnerInfo
     * Add Account Owner information
     * @param  mixed $data
     * @return void
     */
    private function _addBankAccountOwnerInfo($data)
    {
        $account_owner_info = new UserBankAccountOwnerInfo();
        $account_owner_info->consumer_id = $data['user_id'];
        $account_owner_info->account_id = $data['account_id'];
        $account_owner_info->owner_name = $data['owner_name'];
        $account_owner_info->owner_address = $data['owner_address'];
        $account_owner_info->as_of_date = $data['as_of_date'];
        $account_owner_info->raw_response = $data['raw_response'];
        $account_owner_info->save();

        // Compare the data and store it in a table
        $user = User::where('user_id', $data['user_id'])->select('first_name', 'middle_name', 'last_name', 'street_address', 'city', 'state', 'zipcode')->first();
        // Concatenate user's full name, replacing multiple spaces with a single space
        $userFullName = preg_replace('/\s+/', ' ', trim($user->first_name . ' ' . ($user->middle_name ?? '') . ' ' . $user->last_name));

        // Normalize owner name from $data, replacing multiple spaces with a single space
        $ownerName = preg_replace('/\s+/', ' ', trim($data['owner_name'] ?? ''));

        // Compare name
        $nameMatchPercentage = round(getWeightedMatch(strtolower($userFullName), strtolower($ownerName)), 2);

        // Normalize and concatenate user's full address, replacing multiple spaces with a single space
        $userFullAddress = preg_replace('/\s+/', ' ', trim($user->street_address . ', ' . $user->city . ', ' . $user->state . ' ' . $user->zip));

        // Normalize owner address from $data, replacing multiple spaces with a single space
        $ownerFullAddress = preg_replace('/\s+/', ' ', trim($data['owner_address'] ?? ''));

        // Compare address
        $addressMatchPercentage = round(getWeightedMatch(strtolower($userFullAddress), strtolower($ownerFullAddress)), 2);

        // Insert the comparison data into user_bank_account_owner_info_match_details table
        $match_details = new UserBankAccountOwnerInfoMatchDetail();
        $match_details->user_bank_account_owner_info_id = $account_owner_info->id;
        $match_details->consumer_id = $data['user_id'];
        $match_details->account_id = $data['account_id'];
        $match_details->name_during_registration = $userFullName;
        $match_details->address_during_registration = $userFullAddress;
        $match_details->name_from_banking_solution = $ownerName;
        $match_details->address_from_banking_solution = $ownerFullAddress;
        $match_details->name_match_percentage = round($nameMatchPercentage, 2);
        $match_details->address_match_percentage = round($addressMatchPercentage, 2);
        $match_details->save();
        DB::commit();
    }

    /**
     * showAccountOwnerHistory
     * Show History of the account Owner API called
     * @param  mixed $request
     * @return void
     */
    public function showAccountOwnerHistory(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Account Owner API history fetch started...");
        // Validating input request
        $this->validate($request, [
            'user_id' => VALIDATION_REQUIRED,
            'account_id' => VALIDATION_REQUIRED,
        ]);

        $lists = UserBankAccountOwnerInfoMatchDetail::join('user_bank_account_info', 'user_bank_account_info.id', '=', 'user_bank_account_owner_info_match_details.account_id')->selectRaw('CONCAT("x",SUBSTRING(user_bank_account_info.account_no, -4)) as account_no,user_bank_account_info.routing_no,user_bank_account_owner_info_match_details.*,DATE_FORMAT(user_bank_account_owner_info_match_details.created_at, "%m-%d-%Y") as created_date')->where(['user_bank_account_owner_info_match_details.consumer_id' => $request['user_id'], 'user_bank_account_owner_info_match_details.account_id' => $request['account_id']])->orderBy('user_bank_account_owner_info_match_details.created_at', 'DESC')->get();

        $message = trans('message.account_owner_history_fetch_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Account Owner API history fetched successfully.");
        return renderResponse(SUCCESS, $message, $lists);
    }

    /**
     * This API will enable merchant fees report in dashboard
     */
    public function toggleAllowMerchantFeesReport(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Merchant fees report toggle process started...");
        // Validating input request
        $this->validate($request, [
            'id' => VALIDATION_REQUIRED,
        ]);

        try {
            $user = User::where("user_id", $request->get('id'))->first();
            // toggle the value
            $user->allow_merchant_fees_report = $user->allow_merchant_fees_report == 1 ? 0 : 1;
            $user->save();

            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Merchant with ID: " . $user->user_id . " updating allow merchant fees report feature");
            $message = $user->allow_merchant_fees_report == 1 ? trans('message.enable_merchant_fees_report') : trans('message.disable_merchant_fees_report');
            // API Response returned with 200 status
            return renderResponse(SUCCESS, $message, null);
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured while toggling merchant fees report", [EXCEPTION => $e]);
            $message = trans('message.db_transaction_failed');
            // Exception Returned
            return renderResponse(FAIL, $message, null);
        }
    }

    /**
     * This API will enable merchant fees report in dashboard
     */
    public function toggleFreezeSponsorPoints(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Freeze sponsor points toggle process started...");
        // Validating input request
        $this->validate($request, [
            'id' => VALIDATION_REQUIRED,
            'freeze_sponsor_points' => VALIDATION_REQUIRED,
        ]);

        try {
            $updated_freeze_value = $request->get('freeze_sponsor_points') ? 0 : 1;
            // toggle the value
            $sql = "SELECT GROUP_CONCAT(merchant_stores.id SEPARATOR ',') AS store_ids, GROUP_CONCAT(merchant_stores.merchant_id SEPARATOR ',') AS merchant_ids
            FROM `store_user_map`
            LEFT JOIN `merchant_stores` ON `merchant_stores`.`id` = `store_user_map`.`store_id`
            LEFT JOIN `registered_merchant_master` ON `registered_merchant_master`.`id` = `merchant_stores`.`merchant_id`
            WHERE `store_user_map`.`user_id` = ? group by `store_user_map`.`user_id` limit 1";

            $CPStores = DB::Select($sql, [$request->get('id')]);
            $store_ids_array = explode(',', $CPStores[0]->store_ids);
            $merchant_ids_array = explode(',', $CPStores[0]->merchant_ids);

            $sponsors_sql = "SELECT GROUP_CONCAT(sponsor_consumer_maps.id SEPARATOR ',') AS sponsor_link_ids FROM `sponsor_consumer_maps` WHERE `sponsor_consumer_maps`.`corporate_parent_id` = ? limit 1";
            $Sponsors = DB::Select($sponsors_sql, [$request->get('id')]);
            $sponsor_link_ids_array = explode(',', $Sponsors[0]->sponsor_link_ids);
            if (count($sponsor_link_ids_array) > 0) {
                UserRewardUsageHistory::whereIn('sponsor_link_id', $sponsor_link_ids_array)->update(['freeze_sponsor_points' => $updated_freeze_value]);
                UserCurrentRewardDetail::whereIn('sponsor_link_id', $sponsor_link_ids_array)->update(['freeze_sponsor_points' => $updated_freeze_value]);
            }
            DB::beginTransaction();

            if (count($merchant_ids_array) > 0) {
                RegisteredMerchantMaster::whereIn('id', $merchant_ids_array)->update(['freeze_sponsor_points' => $updated_freeze_value]);
            }
            $message = $updated_freeze_value == 1 ? trans('message.enable_sponsor_points') : trans('message.disable_sponsor_points');
            // API Response returned with 200 status
            DB::commit();
            return renderResponse(SUCCESS, $message, null);
        } catch (\Exception $e) {
            DB::rollback();
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception occured while toggling freeze sponsor points", [EXCEPTION => $e]);
            $message = trans('message.db_transaction_failed');
            // Exception Returned
            return renderResponse(FAIL, $message, null);
        }
    }

    /**
     * saveBankDisable
     * This function is will disable banks for a particular user and add a update a flag in Firebase
     * @param  mixed $request
     * @return void
     */
    public function saveBankDelinked(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer Bank Details Delink process started.");
        // Validating input request
        $this->validate($request, [
            'userID' => VALIDATION_REQUIRED,
            'change_bank_link_type' => VALIDATION_REQUIRED,
        ]);
        // validating input request when admin choice 'manual link account type'
        if ($request->get('change_bank_link_type') != 1) {
            $this->validate($request, [
                'daily_spending_limit' => VALIDATION_REQUIRED,
                'consumer_weekly_spending_limit' => VALIDATION_REQUIRED,
            ]);
        }
        // validating input request when admin choice 'activate the previous manual link account'
        if ($request->get('change_bank_link_type') == 3 || $request->get('change_bank_link_type') == 4) {
            $this->validate($request, [
                'selecte_bank_account' => VALIDATION_REQUIRED,
            ]);
        }

        // Get the bank delink status_id
        $bank_delink = getStatus(BANK_DELINK);
        $bank_inactive = getStatus(BANK_INACTIVE);
        $bank_active = getStatus(BANK_ACTIVE);
        $bank_link_type = 0;
        $active_account_no = 'XXXX';
        $update_user_data = [];
        $admin_driven_bank_show_flag = 1;
        $active_account_id = '';

        // Direct to Manual conversion
        if ($request->get('change_bank_link_type') == 4) {
            $data = [
                'user_id' => $request->get('userID'),
                'account_id' => $request->get('selecte_bank_account'),
            ];
            $response = $this->directLinkToManualConversion($data);
            if ($response['code'] == FAIL) {
                return renderResponse(FAIL, $data['message'], null);
            }
        } else {
            // Update All manual link accounts to Inactive state, because we need to show manual bank in the consumer
            UserBankAccountInfo::where(['user_id' => $request->get('userID'), 'status' => $bank_active])->whereNull('account_id')
                ->update(['status' => $bank_inactive]);
            // Update All direct link accounts to Delinked state
            UserBankAccountInfo::where(['user_id' => $request->get('userID'), 'status' => $bank_active])->whereNotNull('account_id')
                ->update(['status' => $bank_delink]);
        }

        // If bank link type 3 'activate the previous manual link account'
        // Make the selected bank an active bank account
        if ($request->get('change_bank_link_type') == 3) {
            $admin_driven_bank_show_flag = 0;
            $selecte_bank_account = UserBankAccountInfo::find($request->get('selecte_bank_account'));
            if ($selecte_bank_account) {
                if ($selecte_bank_account->ref_no) {
                    $newReplicateBankAccount = $selecte_bank_account->replicate();
                    $newReplicateBankAccount->id = generateUUID();
                    $newReplicateBankAccount->ref_no = null;
                    $newReplicateBankAccount->status = $bank_active;
                    $newReplicateBankAccount->created_at = Carbon::now();
                    $newReplicateBankAccount->save();
                    $active_account_no = substr($newReplicateBankAccount->account_no, -4);
                    $active_account_id = $newReplicateBankAccount->id;
                } else {
                    $selecte_bank_account->status = $bank_active;
                    $selecte_bank_account->save();
                    $active_account_no = substr($selecte_bank_account->account_no, -4);
                    $active_account_id = $selecte_bank_account->id;
                }
            }
        }

        if ($request->get('change_bank_link_type') == 1) {
            // Update User bank link type to 1
            $bank_link_type = 1;
        } else {
            // If bank link type is manual or Hybrid
            $update_user_data['standard_daily_limit'] = $request->get('daily_spending_limit');
            $update_user_data['purchase_power'] = $request->get('daily_spending_limit');
            $update_user_data['weekly_spending_limit'] = $request->get('consumer_weekly_spending_limit');
        }
        $update_user_data['bank_link_type'] = $bank_link_type;
        $update_user_data['admin_driven_bank_link'] = 1;
        $update_user_data['admin_driven_bank_show_flag'] = $admin_driven_bank_show_flag;
        // set microbilt error 0 in intial
        $update_user_data['manual_bank_microbilt_error'] = 0;
        $update_user_data['microbilt_upload_document_show'] = 0;

        // Update User data
        User::where(['user_id' => $request->get('userID')])
            ->update($update_user_data);

        if ($request->get('change_bank_link_type') != 4) {
            // Firebase call after delink bank account
            $all_bank_delinked = ($request->get('change_bank_link_type') != 3) ? 1 : 0;
            $userArr = [
                'user_id' => $request->get('userID'),
                'all_bank_delinked' => $all_bank_delinked,
                'bank_link_type' => $bank_link_type,
                'active_account_no' => $active_account_no,
                'active_account_id' => $active_account_id,
            ];
            // Update Bank Delink status in Firebase
            $this->firebase->storebankdelikedstatus($userArr);
        }
        // Update the firebase to remove the MX problematic status for the consumer
        $data = [
            'user_id' => $request->get('userID'),
        ];
        $this->firebase->removeMxActionNeededData($data);

        $message = trans('message.consumer_bank_delinked_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer Bank Details Delinked for User ID : " . $request->get('userID'));
        return renderResponse(SUCCESS, $message, null);
    }

    /**
     * directLinkToManualConversion
     * This function is used to convert a direct link bank to a manual bank conversion
     */
    public function directLinkToManualConversion($data)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Direct Link to manual link process started...");
        //get the acccount details for current user
        $user = User::find($data['user_id']);
        DB::beginTransaction();
        try {
            if ($user) {
                $active_status = getStatus(BANK_ACTIVE);
                $previous_active_bank = BankAccountInfo::where('user_id', $user->user_id)->where('status', $active_status)->orderBy('created_at', 'DESC')->first();
                $selected_bank = BankAccountInfo::where('id', $data['account_id'])->first();
                if ($selected_bank) {
                    $request_data['bank_name'] = $selected_bank->bank_name;
                    $routing_no_exists = fedRoutingNumberExistsCheck($request_data, $selected_bank->routing_no);
                    if (!$routing_no_exists) { //if exists then return right response
                        $message = trans('message.routing_number_not_matched');
                        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Routing number not matches for Consumer with User ID: " . $user->user_id);
                        return [
                            'code' => FAIL,
                            'message' => $message,
                            'data' => null,
                        ];
                    }

                    // Inactive previous active bank
                    if ($previous_active_bank) {
                        $previous_active_bank->status = getStatus(BANK_INACTIVE);
                        $previous_active_bank->save();
                    }

                    //check if the accounts with banking details already exists in our system then update else insert a new row
                    $existing_bank_details = BankAccountInfo::where('user_id', $user->user_id)->where('routing_no', $selected_bank->routing_no)->where('account_no', $selected_bank->account_no)->whereNull('account_id')->first();

                    if (!empty($existing_bank_details)) {
                        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - This account was previously linked manually, so we are now updating the existing bank details.");
                        $existing_bank_details->status = $active_status;
                        $existing_bank_details->fed_bank_id = $routing_no_exists->id;
                        $existing_bank_details->bank_name = $routing_no_exists->bank_name;
                        $existing_bank_details->save();
                        $bank_details = $existing_bank_details;
                    } else {
                        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - We haven't manually linked this account before, so we're now inserting the bank details.");
                        //creating a new active bank account details
                        $bank_details = new BankAccountInfo();
                        $bank_details->user_id = $user->user_id;
                        $bank_details->routing_no = $selected_bank->routing_no;
                        $bank_details->account_no = $selected_bank->account_no;
                        $bank_details->account_no_last_four = getAccountNumberLastFourDigit($selected_bank->account_no);
                        $bank_details->fed_bank_id = $routing_no_exists->id;
                        $bank_details->bank_name = $routing_no_exists->bank_name;
                        $bank_details->account_type = $selected_bank->account_type;
                        $bank_details->account_verified = 1;
                        $bank_details->status = $active_status;
                        $bank_details->save();
                    }

                    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Successfully converted to manual link...");
                    DB::commit();
                    $message = trans('message.bank_account_update');
                    return [
                        'code' => SUCCESS,
                        'message' => $message,
                        'data' => null,
                    ];

                } else {
                    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Selected bank account not found for Consumer ID: " . $user->user_id);
                    $message = trans('message.selected_account_not_found');
                    return [
                        'code' => FAIL,
                        'message' => $message,
                        'data' => null,
                    ];
                }
            } else {
                $message = trans('message.user_not_found');
                return [
                    'code' => FAIL,
                    'message' => $message,
                    'data' => null,
                ];
            }
        } catch (\Exception $e) {
            DB::rollback();
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception while direct to manual bank convert for Consumer ID: " . $user->user_id . " .", [EXCEPTION => $e]);
            $message = trans('message.db_transaction_fail');
            return [
                'code' => FAIL,
                'message' => $message,
                'data' => $e,
            ];
        }
    }

    /**
     * getTransactionDetailsForConsumer
     * This fucntion will return the transaction details for a specific consumer
     * @param  mixed $request
     * @return void
     */
    public function getTransactionDetailsForConsumer(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction details fetch process started for a specific consumer...");
        // Validating input request
        $this->validate($request, [
            'userID' => VALIDATION_REQUIRED,
        ]);

        $status_ids = getPendingSuccessReturnedStatus();
        $success_status = getStatus(SUCCESS);

        $sql = "SELECT COUNT(*) as total_transactions, COALESCE(SUM(IF(status_id = ?, 1, 0)),0) successful_transactions, COALESCE(ROUND(AVG(amount+tip_amount), 2),0) average_ticket
        FROM transaction_details
        WHERE transaction_ref_no IS NULL AND consumer_id = ? AND status_id IN (" . $status_ids . ")";

        $transaction_details = DB::connection(MYSQL_RO)->select($sql, [$success_status, $request->get('userID')]);
        $user = User::on(MYSQL_RO)->find($request->get('userID'));
        $transaction_details['bank_link_type'] = $user->bank_link_type;
        // get R02, R03, R04 reason ID
        $returnReasons = DB::connection(MYSQL_RO)->table('return_reason_masters')->whereIn('reason_code', ['R02', 'R03', 'R04'])->pluck('id')->toArray();

        $bank_active = getStatus(BANK_ACTIVE);
        $bank_inactive = getStatus(BANK_INACTIVE);
        $delink_bank_status = getStatus(BANK_DELINK);
        // Get consumer's all bank accounts.
        $bank_accounts = UserBankAccountInfo::on(MYSQL_RO)
            ->leftJoin('blacklisted_account_numbers as ban', function ($query) {
                $query->on('ban.account_no', '=', 'user_bank_account_info.account_no')->where('ban.is_delete', 0);
            })
            ->leftJoin('transaction_details as td', function ($query) use ($returnReasons) {
                $query->on('td.account_id', '=', 'user_bank_account_info.id')->whereIn('td.return_reason', $returnReasons);
            })
            ->select('user_bank_account_info.id', 'user_bank_account_info.ref_no', 'user_bank_account_info.account_id')
            ->selectRaw('SUBSTRING(user_bank_account_info.account_no, -4) as account_no')
            ->where('user_bank_account_info.user_id', $request->get('userID'))
            ->where(function ($query) {
                $query->whereNull('user_bank_account_info.account_id') // For manual bank accounts
                    ->orWhereNotNull('user_bank_account_info.account_id'); // For direct bank accounts
            })
            ->whereIn('user_bank_account_info.status', [$bank_active, $bank_inactive, $delink_bank_status])
            ->whereNull('ban.id')
            ->whereNull('td.id')
            ->groupBy('user_bank_account_info.account_no', 'user_bank_account_info.routing_no')
            ->orderBy('user_bank_account_info.created_at', 'desc')
            ->get();

        // Separate manual banks and direct banks
        $manual_banks = $bank_accounts->filter(function ($bank_account) use ($delink_bank_status) {
            return $bank_account->account_id === null && $bank_account->status !== $delink_bank_status;
        })->values();

        $direct_banks = $bank_accounts->filter(function ($bank_account) use ($delink_bank_status) {
            return $bank_account->account_id !== null && $bank_account->status !== $delink_bank_status;
        })->values();

        $transaction_details['manual_bank_accounts'] = $manual_banks;
        $transaction_details['direct_bank_accounts'] = $direct_banks;
        $transaction_details['all_banks'] = $bank_accounts;
        $transaction_details['default_daily_spending_limit'] = config('app.purchase_power_for_manual_bank_linked_consumer');
        $transaction_details['default_weekly_spending_limit'] = config('app.max_transaction_limit_weekly_for_bank_linked');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Transaction details fetched successfully for Consumer ID: " . $request->get('userID'));
        $message = trans('message.transaction_details_fetch_success');
        return renderResponse(SUCCESS, $message, $transaction_details);
    }

    /**
     * getConsumerFinancialInstitutions
     * This function will fetch the list of disabled bank accounts of a consumer.
     * @param  mixed $request
     * @return void
     */
    public function getConsumerFinancialInstitutions(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Fetching Consumer Financial Institutions started...");
        // Validating input request
        $this->validate($request, [
            'userID' => VALIDATION_REQUIRED,
        ]);
        $user = User::find($request->get('userID'));
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Getting financial institutions for Consumer ID: " . $user->user_id);
        //fetching bank details for consumer
        $statusArray = [BANK_DELINK, BANK_DISABLE];
        $bank_details = BankAccountInfo::join('status_master', 'status_master.id', '=', 'user_bank_account_info.status')->select('user_bank_account_info.*')->where("user_id", $user->user_id)->whereNotIn("status_master.code", $statusArray)->where('account_id', '!=', '')->where('institution_id', '!=', '')->groupby("institution_login_id", "account_no")->get();

        $bank_array = [];
        foreach ($bank_details as $bank_detail) {
            $params['phoneNo'] = $user->phone;
            $params['customer_id'] = $bank_detail->finicity_id;
            $params['account_id'] = $bank_detail->account_id;
            $params['institution_id'] = $bank_detail->institution_id;
            $bank_name = $this->bankvalidator->getConsumerBankName($params);
            $institutionLoginId = $bank_detail->institution_login_id ? $bank_detail->institution_login_id : $this->bankvalidator->getFinicityInstituionLoginId($params);
            $array_bank_details = array(
                "id" => $bank_detail->id,
                "account_no" => substr($bank_detail->account_no, -4),
            );
            $bank_array[$institutionLoginId]['bank_name'] = $bank_name;
            $bank_array[$institutionLoginId]['institution_id'] = $bank_detail->institution_id;
            $bank_array[$institutionLoginId]['institution_login_id'] = $institutionLoginId;
            $bank_array[$institutionLoginId][$bank_detail->account_type][] = $array_bank_details;
        }

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Financial Institutions fetched for Consumer ID: " . $user->user_id);
        $message = trans('message.financial_institutions_fetch_success');
        return renderResponse(SUCCESS, $message, $bank_array); // API Response returned with 200 status
    }
    /**
     * removeFinancialInstitution
     * Delete Consumer account by Institution Login from finicity
     * @param  mixed $request
     * @return void
     */
    public function removeFinancialInstitution(Request $request)
    {
        // Validating input request
        $this->validate($request, [
            'institution_id' => VALIDATION_REQUIRED,
            'institution_login_id' => VALIDATION_REQUIRED,
            'userID' => VALIDATION_REQUIRED,
        ]);

        DB::beginTransaction();
        $user = User::find($request->get('userID'));
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Removing bank account from finicity for consumer with id: " . $user->user_id . " and Institution ID : " . $request->get('institution_id') . " and Institution Login Id : " . $request->get('institution_login_id'));
        try {
            $pending = getStatus(PENDING);

            //Fetching Pending Transactions for the Consumer and Institution ID
            $pending_transactions_count = TransactionDetails::join('user_bank_account_info', 'transaction_details.consumer_id', '=', 'user_bank_account_info.user_id')
                ->where(['transaction_details.transaction_ref_no' => null, 'transaction_details.is_v1' => 0, 'transaction_details.consumer_id' => $user->user_id, 'user_bank_account_info.institution_id' => $request->get('institution_id'), 'transaction_details.status_id' => $pending])->get()->count();

            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Pending Transactions for Consumer ID: " . $user->user_id . " is " . $pending_transactions_count);
            if ($pending_transactions_count > 0) {
                $message = trans('message.pending_transactions_exists');
                return renderResponse(ACCOUNT_HAS_REMAINING_BALANCE, $message, $pending_transactions_count); // API Response returned with 200 status
            } else {
                $account = BankAccountInfo::join('users', 'users.user_id', '=', 'user_bank_account_info.user_id')->select('user_bank_account_info.*', 'users.phone')->where(['user_bank_account_info.institution_id' => $request->get('institution_id'), 'user_bank_account_info.user_id' => $user->user_id])->where(function ($query) use ($request) {
                    $query->whereNull('user_bank_account_info.institution_login_id')->orWhere('user_bank_account_info.institution_login_id', '')->orWhere('user_bank_account_info.institution_login_id', $request->get('institution_login_id'));
                })->first();
                $response = $this->bankvalidator->deleteConsumerAccounts($account, $request->get('institution_login_id'));
                if (!$response) {
                    Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "There was some problem removing account for consumer with id: " . $user->user_id . " and finicity institution id: " . $request->get('institution_id') . " and Institution Login Id : " . $request->get('institution_login_id'));
                    $message = trans('message.institution_delete_fail');
                    return renderResponse(FAIL, $message, null);
                }
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Bank account removed successfully for consumer with id: " . $user->user_id . " and finicity institution id: " . $request->get('institution_id') . " and Institution Login Id : " . $request->get('institution_login_id'));
                //Update Bank Account status to Delink
                $delinkStatus = getStatus(BANK_DELINK);
                $userBankAccountInfo = BankAccountInfo::where(['user_id' => $user->user_id, 'institution_id' => $request->get('institution_id')])->where(function ($query) use ($request) {
                    $query->whereNull('institution_login_id')->orWhere('institution_login_id', '')->orWhere('institution_login_id', $request->get('institution_login_id'));
                })->update(['status' => $delinkStatus]);

                // Storing delete history into databse
                $fi_del_his = new FinancialInstitutionDeleteHistory();
                $fi_del_his->consumer_id = $user->user_id;
                $fi_del_his->institution_id = $request->get('institution_id');
                $fi_del_his->institution_login_id = $request->get('institution_login_id');
                $fi_del_his->reason = $request->get('reason');
                $fi_del_his->deleted_by = Auth::user()->user_id; // login user id
                $fi_del_his->save();
                DB::commit();
                $message = trans('message.institution_removed_success');
                return renderResponse(SUCCESS, $message, null);
            }
        } catch (\Exception $e) {
            DB::rollback();
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception caused while removing institution for consumer with id: " . $user->user_id . " and finicity institution id: " . $request->get('institution_id') . " and Institution Login Id : " . $request->get('institution_login_id'), [EXCEPTION => $e]);
            $message = trans('message.institution_delete_fail');
            return renderResponse(FAIL, $message, $e);
        }
    }
    /**
     * getusersenrollmentreport
     * get all enrerollment user details
     * @param  mixed $request
     * @return void
     */
    public function getUsersEnrollmentReport(Request $request)
    {
        // Validating input request
        $this->validate($request, [
            'from_date' => VALIDATION_REQUIRED,
            'to_date' => VALIDATION_REQUIRED,
        ]);

        $page = '';
        $perPage = '';
        $bank_active = getStatus(BANK_ACTIVE);
        $pending = getStatus(PENDING);
        $returned = getStatus(RETURNED);

        $sql = "SELECT u.user_id,
                        u.state,
                        u.consumer_type,
                        ubai.id as active_bank_id,
                        concat_ws(' ', NULLIF(u.first_name, ''), NULLIF(u.middle_name, ''), NULLIF(u.last_name, '')) AS name,
                        u.date_of_birth,
                        concat_ws(' ', u.street_address, u.city, u.state, u.zipcode) AS address,
                        u.email,
                        (CASE
                         WHEN u.purchase_power_source = 'old_rule' THEN 'Old Rule'
                         WHEN u.purchase_power_source = 'new_rule' THEN 'New Rule'
                         WHEN u.purchase_power_source = 'reversed_to_old_rule' THEN 'Reversed to Old Rule'
                        END) as purchase_power_rule,
                        IF(u.bank_link_type = 1, cab.balance, 'N/A') AS bank_balance,
                        u.purchase_power,
                        u.phone,
                        concat_ws(' | ', ubai.routing_no, ubai.bank_name) AS routing_no,
                        u.bank_link_type,
                        u.weekly_spending_limit,
                        u.disable_automatic_weekly_spending_limit,
                        u.is_algo_based,
                        IF(u.existing_user = 1, u.migrated_at, u.created_at) enrollment_date,
                        bank_matched_users,
                        IF(reviewed_user.user_id != '', concat_ws(' ', NULLIF(reviewed_user.first_name, ''), NULLIF(reviewed_user.middle_name, ''), NULLIF(reviewed_user.last_name, '')), '') AS reviewed_user_name,
                        COALESCE(SUM(IF(td.consumer_bank_posting_amount IS NOT NULL, td.consumer_bank_posting_amount, td.amount + td.tip_amount)),0) as pending_amount

                FROM users u force index(fk_user_role_id)
                straight_join user_roles ur on u.role_id = ur.role_id and ur.role_name = '" . CONSUMER . "'
                Left JOIN user_bank_account_info AS ubai ON ubai.user_id = u.user_id
                AND ubai.status = ?
                LEFT JOIN consumer_account_balances AS cab ON cab.id =
                (SELECT id
                    FROM consumer_account_balances cab2
                    WHERE cab2.account_id = ubai.id
                    ORDER BY cab2.created_at DESC
                    LIMIT 1)
                LEFT JOIN
                (SELECT account_no,
                        routing_no,
                        GROUP_CONCAT(DISTINCT CONCAT_WS(' ', users.first_name, users.middle_name, users.last_name, users.phone, users.email) SEPARATOR '|') AS bank_matched_users
                    FROM user_bank_account_info matches_ubai
                    JOIN users ON users.user_id = matches_ubai.user_id
                    GROUP BY account_no,
                            routing_no
                    HAVING COUNT(DISTINCT matches_ubai.user_id) > 1) shared_accounts ON shared_accounts.account_no = ubai.account_no AND shared_accounts.routing_no = ubai.routing_no
                LEFT JOIN users_enrollment_reviews AS uer ON uer.id =
                (SELECT id
                    FROM users_enrollment_reviews uer2
                    WHERE uer2.user_id = u.user_id
                    ORDER BY uer2.created_at DESC
                    LIMIT 1)
                LEFT JOIN users AS reviewed_user ON reviewed_user.user_id = uer.reviewed_by
                LEFT JOIN transaction_details AS td ON td.consumer_id = u.user_id and td.transaction_ref_no is null and td.is_v1 = 0 and td.isCanpay = 0 and (td.status_id = ? OR td.status_id = ?)
                WHERE u.password IS NOT NULL
                AND (u.created_at BETWEEN ? AND ?
                        OR u.migrated_at BETWEEN ? AND ?)";

        $searchArray = [$bank_active];
        array_push($searchArray, $pending, $returned);

        $from_date = $request->get('from_date') . " 00:00:00";
        $to_date = $request->get('to_date') . " 23:59:59";
        $pacific_from_date = Carbon::parse($from_date, 'America/Los_Angeles');
        $from_date = $pacific_from_date->utc()->toDateTimeString();
        $pacific_to_date = Carbon::parse($to_date, 'America/Los_Angeles');
        $to_date = $pacific_to_date->utc()->toDateTimeString();

        array_push($searchArray, $from_date, $to_date);
        array_push($searchArray, $from_date, $to_date);

        $sql .= " Group By u.user_id ORDER BY u.created_at DESC ";
        $usersCountdata = DB::connection(MYSQL_RO)->select($sql, $searchArray);
        if (!$request->has('is_export')) {
            $page = $request->get('currentPage'); // Get the current page from the request
            $perPage = $request->get('perPage'); // Set the number of items per page
            $offset = ($page - 1) * $perPage; // Calculate the offset
            $sql .= " LIMIT " . $offset . ", " . $perPage;
        }
        $users = DB::select($sql, $searchArray);
        if ($users) {
            foreach ($users as $user) {
                $user->max_average_score = '';
                $user->risk_score = '';
                $user->bank_balance = $user->bank_balance != 'N/A' ? number_format($user->bank_balance, 2) : $user->bank_balance;
                $cognito_rules_log = CognitoRulesLog::where('phone', $user->phone)->orderby('created_at', 'DESC')->first();
                if (!empty($cognito_rules_log)) {
                    $log = json_decode($cognito_rules_log->response, true);
                    $user->max_average_score = isset($log['X_average_score_max']) ? $log['X_average_score_max'] : '';
                }
                $request->merge([
                    'phone' => $user->phone,
                ]);
                $manualReviewController = new ManualReviewController($request);
                $risk_score = $manualReviewController->getRiskScoreByPhone($request);
                $user->risk_score = !$risk_score ? 'N/A' : $risk_score;
                if ($user->bank_matched_users) {
                    $bankMatchedUsersArray = explode("|", $user->bank_matched_users);
                    $searchTerm = $user->phone . ' ' . $user->email;
                    $bankMatchedUsersArray = array_filter($bankMatchedUsersArray, function ($value) use ($searchTerm) {
                        return strpos($value, $searchTerm) === false;
                    });
                    $user->bank_matched_users = implode(" | ", $bankMatchedUsersArray);
                }
                if ($user->consumer_type == LITE_CONSUMER) {
                    $user->purchase_power = $user->effective_purchase_power = round(getCampaignRewardAmount($user->user_id)->reward_amount, 2);
                    $user->purchase_power_rule = 'N/A';
                } else {
                    $purchase_power = $user->purchase_power - $user->pending_amount;
                    $user->purchase_power = (($purchase_power) > 0) ? number_format($purchase_power, 2) : 0.00;
                    $effective_purchase_power = comparePurchasePowerWeeklyLimits(json_decode(json_encode($user), true), $purchase_power);
                    $user->effective_purchase_power = (($effective_purchase_power) > 0) ? number_format($effective_purchase_power, 2) : 0.00;
                }
                $user->date_of_birth = $user->date_of_birth ? date('m-d-Y', strtotime($user->date_of_birth)) : '';
                if (!$user->active_bank_id) {
                    $user->purchase_power = '0.00';
                    $user->routing_no = 'No active bank account';
                }
                if ($user->enrollment_date) {
                    $utc_enrollment_date = Carbon::createFromFormat('Y-m-d H:i:s', $user->enrollment_date, 'UTC');
                    $user->enrollment_date = $utc_enrollment_date->setTimezone('America/Los_Angeles')->format('m-d-Y H:i:s');
                }
                if ($request->has('is_export')) {
                    $user->consumer_type = ucfirst($user->consumer_type);
                }
            }
        }
        $data = [
            'users' => $users,
            'current_page' => $page,
            'per_page' => $perPage,
            'total' => count($usersCountdata),
        ];
        if (!$request->has('is_export')) {
            $message = trans('message.fetch_new_enrollment_users');
            // API Response returned with 200 status
            return renderResponse(SUCCESS, $message, $data);
        } else {
            $returnResponse = [
                'users' => $users,
                'from_date' => $request->get('from_date'),
                'to_date' => $request->get('to_date'),
            ];
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Users Enrollment Export Complete.");
            return Excel::download(new UsersEnrollmentExport($returnResponse), 'users_enrollment_list' . date('m-d-Y H:i:s') . '.xlsx');
        }
    }
    /**
     * saveUserReviewComment
     * Add new rerollment user review data
     * @param  mixed $request
     * @return void
     */
    public function saveUserReviewComment(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "User Review Comments save process started.");
        // Validating input request
        $this->validate($request, [
            'userIDs' => VALIDATION_REQUIRED,
        ]);
        $userIDs = $request->get('userIDs');
        $user_details = Auth::user();

        foreach ($userIDs as $userID) {
            $comment = new UsersEnrollmentReview();
            $comment->user_id = $userID;
            $comment->comment = $request->get('comment') ?? null;
            $comment->reviewed_by = $user_details->user_id;
            $comment->save();
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "User Review Comments for User ID : " . $userID);
        }
        $message = trans('message.user_review_save_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "User Review Comments added Successfully.");

        return renderResponse(SUCCESS, $message, null);
    }
    /**
     * getUserReviewData
     * get all rerollment user review details
     * @param  mixed $request
     * @return void
     */
    public function getUserReviewData(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "User Review API history fetch started...");
        // Validating input request
        $this->validate($request, [
            'userID' => VALIDATION_REQUIRED,
        ]);
        $userID = $request->get('userID');
        $lists = UsersEnrollmentReview::join('users as u', 'u.user_id', '=', 'users_enrollment_reviews.user_id')
            ->join('users as reviewed_user', 'reviewed_user.user_id', '=', 'users_enrollment_reviews.reviewed_by')
            ->selectRaw("concat_ws(' ', NULLIF(u.first_name, ''), NULLIF(u.middle_name, ''), NULLIF(u.last_name, '')) AS consumer_name, concat_ws(' ', NULLIF(reviewed_user.first_name, ''), NULLIF(reviewed_user.middle_name, ''), NULLIF(reviewed_user.last_name, '')) AS reviewed_user_name, users_enrollment_reviews.comment")->where('users_enrollment_reviews.user_id', $userID)->orderBy('users_enrollment_reviews.created_at', 'DESC')->get();

        $message = trans('message.user_review_history_fetch_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "User Review API history fetched successfully.");
        return renderResponse(SUCCESS, $message, $lists);
    }

    /** This api bypass microbilt error handling
     */
    public function bypassMicrobiltError(Request $request)
    {
        // Validating input request
        $this->validate($request, [
            'user_id' => VALIDATION_REQUIRED,
        ]);
        DB::beginTransaction();
        try {
            $user = User::where('user_id', $request->get('user_id'))->first(); // Fetching User Details
            if ($user) {
                $previousMicrobiltError = microbiltRuleMatchHistoryCount($user->phone, null, null, false);
                if ($previousMicrobiltError) {
                    // Add the status in the history table for this consumer
                    $active_status = getStatus(USER_ACTIVE);
                    $user_update_data = ['admin_driven_bank_show_flag' => 1];
                    if ($user->status != $active_status) {
                        $user_update_data['status'] = $active_status;
                        saveStatusHistory(STATUS_UPDATED_BY_ADMIN, 'Microbilt error bypass', $active_status, $user->user_id);
                    }

                    User::where('user_id', $user->user_id)->update($user_update_data);
                    MicrobiltRuleMatchHistory::where('id', $previousMicrobiltError->id)->update([
                        'is_admin_driven' => 1,
                    ]);
                    DB::commit();
                    $message = trans('message.microbilt_bypass_error');
                    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer microbilt error bypass complete USER ID:" . $request['user_id']);
                    return renderResponse(SUCCESS, $message, null);
                } else {
                    $message = trans('message.previous_microbilt_not_found');
                    Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Previous microbilt error not found USER ID:" . $request['user_id']);
                    return renderResponse(FAIL, $message, null);
                }
            } else {
                $message = trans('message.user_not_found');
                Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer microbilt error bypass process revoked USER ID:" . $request['user_id']);
                return renderResponse(FAIL, $message, null);
            }
        } catch (\Exception $e) {
            Log::error(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Exception caused while bypass microbilt error.", [EXCEPTION => $e]);
            DB::rollback();
            $message = trans('message.db_transaction_failed');
            return renderResponse(FAIL, $message, null);
        }
    }

    /**
     * fetchMxBankData
     * Fetch consumer MX link bank data
     * @param  mixed $request
     * @return void
     */
    public function fetchMxBankData(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer MX link bank data Fetch started.");
        // Validating input request
        $this->validate($request, [
            'userID' => VALIDATION_REQUIRED,
            'mxConsumerID' => VALIDATION_REQUIRED,
        ]);

        $user_id = $request->get('userID');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - Consumer MX link bank data Fetch started for Consumer ID: " . $user_id);
        $data['consumer_id'] = $user_id;
        $data['mx_consumer_id'] = $request->get('mxConsumerID');
        $data['skip_not_connected'] = $request->get('skipNotConnectedMember') ?? false;
        $merberGuid = $this->mxFactory->getMembers($data);
        return renderResponse($merberGuid['code'], $merberGuid['message'], $merberGuid['data']);
    }

    /**
     * updatePurchasePowerRule
     * Change the purchase power rule for user
     * @param  mixed $request
     * @return void
     */
    public function updatePurchasePowerRule(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . " Change in purchase power rule started...");

        $this->validate($request, [
            'user_id' => VALIDATION_REQUIRED,
        ]);

        $bank_active = getStatus(BANK_ACTIVE);
        $user_details = User::where("user_id", $request->get('user_id'))->first();
        $new_pp_status = "";
        if ($user_details->purchase_power_source == OLD_RULE || $user_details->purchase_power_source == REVERSED_TO_OLD_RULE) {
            $new_pp_status = NEW_RULE;
            $purchase_power = DB::SELECT(
                "SELECT FLOOR((ppll.percentage / 100) * cab.balance) AS purchase_power, cab.balance FROM user_bank_account_info ubai
            JOIN consumer_account_balances cab ON ubai.id = cab.account_id AND cab.consumer_id = ubai.user_id
            JOIN purchase_power_lower_limit ppll ON cab.balance BETWEEN ppll.low_amount AND ppll.high_amount
            WHERE ubai.status = ? AND ubai.user_id = ? ORDER BY cab.created_at DESC LIMIT 1",
                [$bank_active, $request->get('user_id')]
            );
        } else if ($user_details->purchase_power_source == NEW_RULE) {
            $new_pp_status = $user_details->is_algo_based == 1 ? OLD_RULE : REVERSED_TO_OLD_RULE;
            $purchase_power = DB::SELECT(
                "SELECT CASE WHEN tdt.fixed_amount IS NULL THEN FLOOR((tdt.percentage / 100) * cab.balance) ELSE tdt.fixed_amount END AS purchase_power, cab.balance FROM user_bank_account_info ubai
            JOIN consumer_account_balances cab ON ubai.id = cab.account_id AND cab.consumer_id = ubai.user_id
            JOIN transaction_decision_table tdt ON cab.balance BETWEEN tdt.low_amount AND tdt.high_amount
            WHERE ubai.status = ? AND ubai.user_id = ? ORDER BY cab.created_at DESC LIMIT 1",
                [$bank_active, $request->get('user_id')]
            );
        }

        if (!empty($purchase_power)) {
            if ($new_pp_status == NEW_RULE) {
                $final_purchase_power = $purchase_power[0]->purchase_power > config('app.pp_lower_limit_max_value') ? config('app.pp_lower_limit_max_value') : $purchase_power[0]->purchase_power;
            } else {
                $final_purchase_power = $purchase_power[0]->purchase_power > config('app.max_transaction_limit_for_single_transaction') ? config('app.max_transaction_limit_for_single_transaction') : $purchase_power[0]->purchase_power;
            }

            if ($user_details->is_algo_based == 0) {
                User::where('user_id', $request->get('user_id'))->update(['purchase_power' => $final_purchase_power, 'purchase_power_source' => $new_pp_status, 'disable_automatic_purchase_power' => 0]);
            } else { // If the user is algo based then don't update the purchase power. Just update the failover rule for Algo.
                User::where('user_id', $request->get('user_id'))->update(['purchase_power_source' => $new_pp_status]);
            }

            $consumer_purchase_power_source_history = new ConsumerPurchasePowerSourceHistory();
            $consumer_purchase_power_source_history->user_id = $request->get('user_id');
            $consumer_purchase_power_source_history->balance = $purchase_power[0]->balance;
            $consumer_purchase_power_source_history->purchase_power = $final_purchase_power;
            $consumer_purchase_power_source_history->purchase_power_source = $new_pp_status;
            $consumer_purchase_power_source_history->save();

            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . " purchase power rule changed successfully to " . $new_pp_status . " for consumer with user_id: " . $request->get('user_id'));
            $message = trans("message.purchase_power_rule_change_success");

            return renderResponse(SUCCESS, $message, null);
        } else {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . " Cannot change Purchase power rule as balance does not exixts for user_id: " . $request->get('user_id'));
            $message = trans("message.purchase_power_rule_change_fail");
            return renderResponse(FAIL, $message, null);
        }
    }

    /**
     * getConsumerBankList
     * Fetch the list of account numbers
     * @param  mixed $request
     */
    public function getConsumerBankList(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer bank account list fetch started...");

        // Validating input request
        $this->validate($request, [
            'id' => VALIDATION_REQUIRED,
        ]);
        $consumer_details = User::find($request->get('id'));
        // get R02, R03, R04 reason ID
        $returnReasons = DB::connection(MYSQL_RO)->table('return_reason_masters')->whereIn('reason_code', ['R02', 'R03', 'R04'])->pluck('id')->toArray();
        // Convert the array to a string with each ID enclosed in single quotes and separated by commas
        $return_reason_str = implode(',', array_map(function ($id) {
            return "'$id'";
        }, $returnReasons));

        $bank_active = getStatus(BANK_ACTIVE);
        $bank_inactive = getStatus(BANK_INACTIVE);
        $bank_delink = getStatus(BANK_DELINK);

        $status_str = "'" . $bank_active . "','" . $bank_inactive . "','" . $bank_delink . "'";
        $returned = getStatus(RETURNED);
        // Fetch Bank Accounts of a specific consumers
        $sql = "SELECT
            ubai.id,
            ubai.account_no,
            ubai.institution_id,
            ubai.status,
            ban.id AS blacklisted_id
        FROM
            user_bank_account_info AS ubai
        LEFT JOIN
            blacklisted_account_numbers AS ban ON ban.account_no = ubai.account_no AND ban.is_delete = 0
        LEFT JOIN
            transaction_details AS td ON td.account_id = ubai.id AND td.return_reason IN ($return_reason_str) AND td.status_id = ?
        WHERE
            td.id IS NULL
            AND ubai.status IN ($status_str)
            AND ubai.user_id = ?
        GROUP BY
            ubai.account_no, ubai.routing_no";

        $bank_accounts = DB::select($sql, [$returned, $request->get('id')]);

        $accountsArr = [];
        if (count($bank_accounts) > 0) {
            Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Consumer bank account list count: " . count($bank_accounts));
            foreach ($bank_accounts as $account) {
                $data = [];
                $data['account_id'] = $account->id;
                $account_no_str = (string) $account->account_no;
                $data['account_no'] = 'x' . substr($account_no_str, -4);
                $data['bank_link_type'] = !is_null($account->institution_id) ? 'Direct Link' : 'Manual Link';
                $data['status'] = $account->status;
                $data['blacklisted'] = !is_null($account->blacklisted_id) ? 1 : 0;

                array_push($accountsArr, $data);
            }
        } else {
            $accountsArr = [];
        }
        $data = [
            'accounts' => $accountsArr,
            'bank_link_type' => $consumer_details->bank_link_type,
        ];
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "After loop Consumer bank account list count: " . count($accountsArr));
        return $data;
    }
    /**
     * getRegistrationFailure
     * Fetch the list of consumer who failed registration due to mx error
     * @param  mixed $request
     */
    public function getRegistrationFailure(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . " Fetching Consumer detail for those who have failed due to MX error....");

        $this->validate($request, [
            'consumer' => VALIDATION_REQUIRED_WITHOUT_ALL . ':phone_no,email,from_date,to_date',
            'phone_no' => VALIDATION_REQUIRED_WITHOUT_ALL . ':email,consumer,from_date,to_date',
            'email' => VALIDATION_REQUIRED_WITHOUT_ALL . ':phone_no,consumer,from_date,to_date',
            'v1_consumer' => VALIDATION_REQUIRED,
            'from_date' => VALIDATION_REQUIRED_WITHOUT_ALL . ':consumer,phone_no,email',
            'to_date' => VALIDATION_REQUIRED_WITHOUT_ALL . ':consumer,phone_no,email',
        ]);
        $fromDate = Carbon::parse($request->input('from_date'));
        $toDate = Carbon::parse($request->input('to_date'));
        if ($fromDate > $toDate) {
            $message = trans("message.fail_mx_due_to_date_difference");
            return renderResponse(FAIL, $message, null);
        }
        // Check if the difference between from_date and to_date is more than 14 days
        if ($fromDate->diffInDays($toDate) + 1 > config('app.mx_failure_date_difference')) {
            $message = trans("message.fail_mx_due_to_date_range");
            return renderResponse(FAIL, $message, null);
        }
        // Validating input request
        $page = $request->currentPage;
        $perPage = $request->perPage;
        $offset = ($page - 1) * $perPage;
        $searchParams = [0]; // Initialize with registration_complete = 0
        $selectedTable = "registration_session_details";
        $alias = "rsd";

        if ($request->get('v1_consumer') == 1) {
            $selectedTable = "consumer_onboarding_session_details";
            $alias = "cosd";
        }

        $baseSql = "
            FROM {$selectedTable} AS {$alias}
            INNER JOIN user_validation_credentials AS uvc ON uvc.phone = {$alias}.phone
            WHERE {$alias}.registration_complete = ?
            AND uvc.mx_consumer_id IS NOT NULL
        ";

        if (!empty($request['consumer']) && strlen(trim($request['consumer'])) >= 3) {
            $baseSql .= " AND LOWER(REPLACE(CONCAT(COALESCE(REPLACE({$alias}.first_name, ' ', ''), ''), ' ', COALESCE(REPLACE({$alias}.middle_name, ' ', ''), ''), ' ', COALESCE(REPLACE({$alias}.last_name, ' ', ''), '')), '  ', ' ')) LIKE ? ";
            $searchParams[] = '%' . strtolower($request['consumer']) . '%';
        }
        if (!empty($request['phone_no'])) {
            $baseSql .= " AND {$alias}.phone = ? ";
            $searchParams[] = $request['phone_no'];
        }
        if (!empty($request['email'])) {
            $baseSql .= " AND {$alias}.email = ? ";
            $searchParams[] = $request['email'];
        }
        if (!empty($request['from_date']) && !empty($request['to_date'])) {
            $baseSql .= " AND {$alias}.created_at BETWEEN ? AND ? ";
            $searchParams[] = $request['from_date'];
            $searchParams[] = $request['to_date'] . " 23:59:59";
        }

        // Calculate total count of unique consumers
        $totalCountSql = "
            SELECT COUNT(DISTINCT uvc.mx_consumer_id) AS total
            {$baseSql}
        ";
        $totalCountResult = DB::connection(MYSQL_RO)->select($totalCountSql, $searchParams);
        $totalCount = $totalCountResult[0]->total ?? 0;

        // Get paginated data
        $dataSql = "
            WITH ranked_rows AS (
                SELECT {$alias}.*, uvc.mx_consumer_id,
                       ROW_NUMBER() OVER (PARTITION BY uvc.mx_consumer_id ORDER BY {$alias}.created_at DESC) AS rn
                {$baseSql}
            )
            SELECT * FROM ranked_rows
            WHERE rn = 1
            ORDER BY created_at DESC
            LIMIT ? OFFSET ?
        ";
        $searchParams[] = $perPage;
        $searchParams[] = $offset;
        $fetchConsumer = DB::connection(MYSQL_RO)->select($dataSql, $searchParams);

        // Prepare the response data
        $arrayData = [];
        foreach ($fetchConsumer as $currConsumer) {
            $nestedData = [];
            $nestedData['name'] = (isset($currConsumer->middle_name) && $currConsumer->middle_name !== null) ? "{$currConsumer->first_name} {$currConsumer->middle_name} {$currConsumer->last_name}" : "{$currConsumer->first_name} {$currConsumer->last_name}";
            $nestedData['session_id'] = $currConsumer->session_id;
            $nestedData['email'] = $currConsumer->email;
            $nestedData['phone'] = $currConsumer->phone;
            $nestedData['mx_consumer_id'] = $currConsumer->mx_consumer_id;
            $arrayData[] = $nestedData;
        }

        $data = [
            'result' => $arrayData,
            'current_page' => $page,
            'per_page' => $perPage,
            'total' => $totalCount,
            'total_pages' => ceil($totalCount / $perPage),
        ];

        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . " Consumer detail fetched successfully.");
        $message = trans("message.fetch_registration_failed_consumer_due_to_mx");
        return renderResponse(SUCCESS, $message, $data);
    }

    /**
     * searchConsumerAlgoHistory
     * Search the Consumers Algo history based on search Criteria
     * @param  mixed $request
     * @return void
     */
    public function searchConsumerAlgoHistory(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Registered Consumer Algo history Search Started.");
        // Validating input request
        $this->validate($request, [
            'consumer' => VALIDATION_REQUIRED_WITHOUT_ALL . ':phone_no,email,from_date,to_date',
            'phone_no' => VALIDATION_REQUIRED_WITHOUT_ALL . ':email,consumer,from_date,to_date',
            'email' => VALIDATION_REQUIRED_WITHOUT_ALL . ':phone_no,consumer,from_date,to_date',
            'from_date' => VALIDATION_REQUIRED_WITHOUT_ALL . ':phone_no,consumer,email,to_date',
            'to_date' => VALIDATION_REQUIRED_WITHOUT_ALL . ':phone_no,consumer,from_date,email',
        ]);
        $bank_active = getStatus(BANK_ACTIVE);
        $page = $request['page']; // Get the current page from the request
        $perPage = $request['per_page']; // Set the number of items per page
        $offset = ($page - 1) * $perPage; // Calculate the offset
        $consumer_role_id = getRole(CONSUMER);
        $searchArray = [$bank_active, $consumer_role_id];
        $sql = 'SELECT
                    ubai.id AS active_account_id,
                    smu.status AS status_name,
                    active_bank_cppdt.id AS active_bank_algo_id,
                    REPLACE(CONCAT(COALESCE(REPLACE(u.first_name, " ", ""), ""), " ", COALESCE(REPLACE(u.middle_name, " ", ""), ""), " ", COALESCE(REPLACE(u.last_name, " ", ""), "")), "  ", " ") AS name,
                    u.email,
                    u.phone,
                    u.consumer_type,
                    u.user_id,
                    u.purchase_power,
                    CASE
                        WHEN u.disable_automatic_purchase_power = 1 THEN "' . CUSTOM_PURCHASE_POWER . '"
                        WHEN u.is_algo_based = 1 AND u.last_algo_data_failed = 0 THEN "' . ALGO . '"
                        ELSE u.purchase_power_source
                    END AS pp_source,
                    CASE
                        WHEN active_bank_cppdt.created_at IS NOT NULL
                            AND (cppach.created_at IS NULL OR active_bank_cppdt.created_at >= cppach.created_at) THEN DATE_FORMAT(active_bank_cppdt.created_at, "%m-%d-%Y %h:%i:%s %p")
                        WHEN cppach.created_at IS NOT NULL
                            AND cppach.created_at > active_bank_cppdt.created_at THEN IF(cppachd.created_at IS NOT NULL, DATE_FORMAT(cppachd.created_at, "%m-%d-%Y %h:%i:%s %p"), "Not Received")
                        ELSE "N/A"
                    END AS received_at,
                    CASE
                        WHEN active_bank_cppdt.created_at IS NOT NULL
                            AND (cppach.created_at IS NULL OR active_bank_cppdt.created_at >= cppach.created_at) THEN IF(active_bank_cppdt.percentage IS NOT NULL, active_bank_cppdt.percentage, "Not Received")
                        WHEN cppach.created_at IS NOT NULL
                            AND cppach.created_at > active_bank_cppdt.created_at THEN IF(cppachd.percentage IS NOT NULL, cppachd.percentage, "Not Received")
                        ELSE "N/A"
                    END AS percentage,
                    CASE
                        WHEN active_bank_cppdt.created_at IS NOT NULL
                            AND (cppach.created_at IS NULL OR active_bank_cppdt.created_at >= cppach.created_at) THEN IF(active_bank_cppdt.balance IS NOT NULL, active_bank_cppdt.balance, "Not Received")
                        WHEN cppach.created_at IS NOT NULL
                            AND cppach.created_at > active_bank_cppdt.created_at THEN IF(cppach.balance IS NOT NULL, cppach.balance, "Not Received")
                        ELSE "N/A"
                    END AS balance,
                    IF(u.date_of_birth IS NOT NULL AND u.date_of_birth != "0000-00-00", DATE_FORMAT(u.date_of_birth, "%m-%d-%Y"), "") AS date_of_birth,
                    CASE
                        WHEN ubai.account_no IS NULL THEN "N/A"
                        ELSE CONCAT("x", RIGHT(ubai.account_no, 4))
                    END AS masked_account_no,
                    CASE
                        WHEN cppdt.risk_score IS NULL THEN "N/A"
                        WHEN ROUND(cppdt.risk_score, 2) = ROUND(cppdt.risk_score) THEN CAST(ROUND(cppdt.risk_score) AS VARCHAR(255))
                        ELSE CAST(cppdt.risk_score AS VARCHAR(255))
                    END AS risk_score
                FROM
                    users u
                LEFT JOIN
                    user_bank_account_info ubai ON u.user_id = ubai.user_id AND ubai.status = ?
                LEFT JOIN
                    consumer_purchase_power_api_call_history cppach ON cppach.id = (
                        SELECT id
                        FROM consumer_purchase_power_api_call_history cppach2
                        WHERE cppach2.account_id = ubai.id AND cppach2.consumer_id = u.user_id
                        ORDER BY cppach2.created_at DESC
                        LIMIT 1
                    )
                LEFT JOIN
                    consumer_purchase_power_decision_table cppachd ON cppach.uid = cppachd.uid AND cppachd.immediate_response = 1
                LEFT JOIN
                    consumer_purchase_power_decision_table active_bank_cppdt ON active_bank_cppdt.id = (
                        SELECT id
                        FROM consumer_purchase_power_decision_table active_bank_cppdt2
                        WHERE active_bank_cppdt2.account_id = ubai.id AND active_bank_cppdt2.consumer_id = u.user_id
                        ORDER BY active_bank_cppdt2.created_at DESC
                        LIMIT 1
                    )
                JOIN
                    registration_session_details rsd ON u.phone = rsd.phone AND u.registration_ref_id = rsd.id
                LEFT JOIN
                    consumer_purchase_power_decision_table cppdt ON cppdt.id = (
                        SELECT id
                        FROM consumer_purchase_power_decision_table cppdt2
                        WHERE cppdt2.risk_score IS NOT NULL
                            AND (cppdt2.consumer_id = rsd.id OR cppdt2.consumer_id = u.user_id)
                        ORDER BY cppdt2.created_at DESC
                        LIMIT 1
                    )
                LEFT JOIN
                    status_master smu ON u.status = smu.id
                WHERE
                    u.password IS NOT NULL AND u.role_id = ?';

        if (trim($request['from_date'])) {
            $from_date = $request->get('from_date') . " 00:00:00";
            $to_date = $request->get('to_date') . " 23:59:59";
            $sql .= ' AND (u.created_at BETWEEN ? AND ? OR u.migrated_at BETWEEN ? AND ?)';
            array_push($searchArray, $from_date, $to_date);
            array_push($searchArray, $from_date, $to_date);
        }
        if (strlen(trim($request['consumer'])) >= 3) {
            $sql .= ' AND LOWER(
                REPLACE(CONCAT(COALESCE(
                REPLACE(u.first_name, " ",""), "")," ", COALESCE(
                REPLACE(u.middle_name, " ",""), "")," ", COALESCE(
                REPLACE(u.last_name, " ",""), "")),"  "," ")) LIKE ? ';
            array_push($searchArray, '%' . $request['consumer'] . '%');
        }
        if (trim($request['phone_no'])) {
            $sql .= ' AND u.phone = ? ';
            array_push($searchArray, $request['phone_no']);
        }
        if (trim($request['email'])) {
            $sql .= ' AND u.email = ? ';
            array_push($searchArray, $request['email']);
        }
        $sql .= " GROUP BY `u`.`user_id`";

        $totalCount = count(DB::connection(MYSQL_RO)->select($sql, $searchArray));
        $sql .= "  ORDER BY u.created_at DESC LIMIT ? OFFSET ?";
        array_push($searchArray, $perPage, $offset);
        $users = DB::connection(MYSQL_RO)->Select($sql, $searchArray);
        $data = [
            'data' => $users,
            'current_page' => $page,
            'per_page' => $perPage,
            'total' => $totalCount,
            'total_pages' => ceil($totalCount / $perPage),
        ];

        $message = trans('message.consumer_search_success');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . "Registered Consumer Algo history Search Completed");
        return renderResponse(SUCCESS, $message, $data);
    }

    /**
     * consumerAccountAlgoHistory
     * Get Consumer Algo history
     * @param  mixed $request
     * @return void
     */
    public function consumerAccountAlgoHistory(Request $request)
    {
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . " Consumer account's Algo history Fetching started.");
        // Validating input request
        $this->validate($request, [
            'user_id' => VALIDATION_REQUIRED,
            'account_id' => VALIDATION_REQUIRED,
        ]);
        $searchArray = [$request['user_id'], $request['account_id'], $request['user_id'], $request['account_id']];
        $page = $request['page']; // Get the current page from the request
        $perPage = $request['per_page']; // Set the number of items per page
        $offset = ($page - 1) * $perPage; // Calculate the offset

        $sql = 'SELECT a.uid,
                        a.consumer_id,
                        a.account_id,
                        IF(a.balance IS NOT NULL, a.balance, "Not Received") AS balance,
                        CONCAT(
                            LPAD(FLOOR(a.execution_time / 60), 2, "0"),
                            ":",
                            LPAD(FLOOR(a.execution_time) % 60, 2, "0"),
                            ".",
                            LPAD(FLOOR((a.execution_time - FLOOR(a.execution_time)) * 100), 2,  "0")
                        ) AS execution_time,
                        a.created_at as created_at,
                        CASE
                            WHEN d.created_at IS NOT NULL
                                THEN DATE_FORMAT(DATE_SUB(d.created_at, INTERVAL (d.execution_time) SECOND), "%m-%d-%Y %h:%i:%s %p")
                            WHEN a.created_at IS NOT NULL
                                THEN DATE_FORMAT(DATE_SUB(a.created_at, INTERVAL (a.execution_time) SECOND), "%m-%d-%Y %h:%i:%s %p")
                            ELSE "N/A"
                        END AS called_at,
                        IF(d.created_at IS NOT NULL, DATE_FORMAT(d.created_at, "%m-%d-%Y %h:%i:%s %p"), "Not Received") AS received_at,
                        IF(d.percentage IS NOT NULL, d.percentage, "Not Received") AS percentage,
                        IF(d.calculated_purchase_power IS NOT NULL, d.calculated_purchase_power, "N/A") AS calculated_purchase_power,
                        "API" AS response_source,
                        CASE
                            WHEN a.exception_message IS NULL THEN "N/A"
                            WHEN position("cURL error 28:" in a.exception_message) > 0
                            THEN "Connection Timeout"
                            WHEN position("cURL error 6:" in a.exception_message) > 0
                            THEN "Could not resolve host"
                            ELSE "Server Error"
                        END AS exception_message
                FROM consumer_purchase_power_api_call_history a
                LEFT JOIN consumer_purchase_power_decision_table d ON a.uid = d.uid AND d.immediate_response = 1
                WHERE a.uid IS NOT NULL AND a.consumer_id = ? AND a.account_id = ?
                UNION ALL
                SELECT d.uid,
                        d.consumer_id,
                        d.account_id,
                        IF(d.balance IS NOT NULL, d.balance, "Not Received") AS balance,
                        "N/A" AS execution_time,
                        d.created_at as created_at,
                        "N/A" as called_at,
                        IF(d.created_at IS NOT NULL, DATE_FORMAT(d.created_at, "%m-%d-%Y %h:%i:%s %p"), "Not Received") AS received_at,
                        IF(d.percentage IS NOT NULL, d.percentage, "Not Received") AS percentage,
                        IF(d.calculated_purchase_power IS NOT NULL, d.calculated_purchase_power, "Not Received") AS calculated_purchase_power,
                        "Batch" AS response_source,
                        "N/A" AS exception_message
                FROM consumer_purchase_power_decision_table d
                WHERE d.uid IS NULL AND d.consumer_id = ? AND d.account_id = ?';

        $totalCount = count(DB::connection(MYSQL_RO)->select($sql, $searchArray));
        $sql .= "  ORDER BY created_at DESC LIMIT ? OFFSET ?";
        array_push($searchArray, $perPage, $offset);
        $algoHistory = DB::connection(MYSQL_RO)->Select($sql, $searchArray);
        $data = [
            'data' => $algoHistory,
            'current_page' => $page,
            'per_page' => $perPage,
            'total' => $totalCount,
            'total_pages' => ceil($totalCount / $perPage),
        ];

        $message = trans('message.consumer_algo_history');
        Log::info(__METHOD__ . "(" . LINE . ": " . __LINE__ . ") - " . " Consumer account's Algo history Fetching Completed.");
        return renderResponse(SUCCESS, $message, $data);
    }
}
