<template>
<div>
  <div  v-if="loading">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px;">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">eCommerce Void Transactions</h3>
                </div>
                <div class="card-body">
                  <div class="row">
                    <div class="col-md-4">
                      <div class="form-group">
                        <input
                          autocomplete="off"
                          class="start-date form-control"
                          placeholder="Start Date"
                          id="start-date"
                          onkeydown="return false"
                        />
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group">
                        <input
                          autocomplete="off"
                          class="end-date form-control"
                          placeholder="End Date"
                          id="end-date"
                          onkeydown="return false"
                        />
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group">
                        <input
                          autocomplete="off"
                          class="form-control"
                          placeholder="Amount"
                          id="amount"
                          v-model="amount"
                        />
                      </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                        <label for="merchant">
                          Select Store
                          <span class="red">*</span>
                        </label>
                        <multiselect
                            id="store"
                            v-model="selectedStore"
                            placeholder="Select Store (Min 3 chars)"
                            label="retailer"
                            :options="storelist"
                            :loading="isLoading"
                            :internal-search="false"
                            v-validate="'required'"
                            @search-change="getAllStores"
                        ></multiselect>
                        </div>
                    </div>
                </div>
                </div>
              <div class="card-footer">
                <button
                  type="button"
                  class="btn btn-success"
                  @click="generateReport(false)"
                >
                  Generate
                </button>
                <button
                  type="button"
                  @click="generateReport(true)"
                  class="btn btn-danger ml-10"
                >
                  Generate & Export<i
                    class="fa fa-download ml-10"
                    aria-hidden="true"
                  ></i>
                </button>
                <button
                    type="button"
                    @click="reset()"
                    class="btn btn-success margin-left-5"
                >
                    Reset
                </button>
              </div>
                <!-- /.card-header -->
                <div class="card-body">
                  <div class="row">
                  <div class="col-12"><b-table-simple
                      responsive
                      show-empty
                      bordered
                      sticky-header="800px"
                    >
                      <b-thead head-variant="light">
                        <b-tr>
                          <b-th class="text-center">Transaction Number</b-th>
                          <b-th class="text-center">Consumer</b-th>
                          <b-th class="text-center">Merchant</b-th>
                          <b-th class="text-center">Store</b-th>
                          <b-th class="text-center">Terminal</b-th>
                          <b-th class="text-center">Amount ($)</b-th>
                          <b-th class="text-center">Tip Amount ($)</b-th>
                          <b-th class="text-center">Delivery Fee ($)</b-th>
                          <b-th class="text-center">Transaction Time</b-th>
                          <b-th class="text-center">Status</b-th>
                          <b-th class="text-center">Action</b-th>
                        </b-tr>
                      </b-thead>
                      <b-tbody v-for="(row, index) in report" :key="index">
                        <b-tr>
                          <b-td class="text-left text-gray">{{
                            row.transaction_number
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.consumer_name
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.merchant_name
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.store_name
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.terminal_name
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.amount
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.tip_amount
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.delivery_fee
                          }}</b-td>
                          <b-td v-html="row.transaction_time" class="text-left text-gray"></b-td>
                          <b-td class="text-left text-gray">{{
                            row.status
                          }}</b-td>
                          <b-td class="text-left text-gray">
                            <a style="color: white !important" class="btn btn-success ecommerce-void-leave-comment" :data-id="row.edit" @click="$bvModal.show('void-revoke-comment-modal')" v-if="row.status == 'Voided' && row.cancelable && user.role_name !== constants.role_helpdesk">Revoke Void</a>
                            <a style="color: white !important" class="btn btn-danger ecommerce-void-leave-comment" :data-id="row.edit" @click="$bvModal.show('comment-modal')"  v-if="row.status != 'Voided' && row.cancelable">Cancel</a>
                          </b-td>
                        </b-tr>
                      </b-tbody>
                    </b-table-simple>
                  </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
    <!-- Transaction cancellation comment modal start -->
    <b-modal
      id="comment-modal"
      ref="comment-modal"
      ok-title="Save & Cancel"
      cancel-title="Close"
      ok-variant="success"
      @ok="cancelTransaction"
      cancel-variant="outline-secondary"
      hide-header
      :no-close-on-esc="true"
      :no-close-on-backdrop="true"
    >
      <div class="row">
        <div class="col-12">
          <label for="comment">
            Tell us why you want to cancel this transaction
            <span class="red">*</span>
          </label>
          <textarea name="comment" type="text" v-model="comment" class="form-control" />
        </div>
        <input type="text" v-model="transaction_id" hidden />
      </div>
      <div class="row" v-if="showMsg">
        <div class="col-12">
          <label for="comment" class="red">Please fill in the required fields.</label>
        </div>
      </div>
      <div class="row" style="margin-bottom: 40px;"></div>
    </b-modal>
    <!-- Transaction cancellation comment modal end -->
    <!-- Transaction void revoked modal start -->
    <b-modal
      id="void-revoke-comment-modal"
      ref="void-revoke-comment-modal"
      ok-title="Save & Cancel"
      cancel-title="Close"
      ok-variant="success"
      @ok="revokeVoidTransaction"
      cancel-variant="outline-secondary"
      hide-header
      :no-close-on-esc="true"
      :no-close-on-backdrop="true"
    >
      <div class="row">
        <div class="col-12">
          <label for="comment">
            Tell us why you want to revoke this void transaction
            <span class="red">*</span>
          </label>
          <textarea name="void_revoke_comment" type="text" v-model="void_revoke_comment" class="form-control" />
        </div>
        <input type="text" v-model="transaction_id" hidden />
      </div>
      <div class="row" v-if="showVoidMsg">
        <div class="col-12">
          <label for="comment" class="red">Please fill in the required fields.</label>
        </div>
      </div>
      <div class="row" style="margin-bottom: 40px;"></div>
    </b-modal>
    <!-- Transaction void revoked comment modal end -->
  </div>
</div>
</template>
<script>

import api from "@/api/transaction.js";
import moment from "moment";
import { validationMixin } from "vuelidate";
import { required, minLength } from "vuelidate/lib/validators";
import { HourGlass } from "vue-loading-spinner";
import commonConstants from "@/common/constant.js";
import CanPayLoader from "./CustomLoader/CanPayLoader.vue";
export default {
  mixins: [validationMixin],
  data() {
    return {
      allTransactionModel: {},
      showMsg: false,
      showVoidMsg: false,
      transaction_id: null,
      comment: "",
      void_revoke_comment:"",
      currentUser: localStorage.getItem("user")
        ? JSON.parse(localStorage.getItem("user"))
        : null,
      loading: false,
      report: [],
      email:"",
      phone_no:"",
      isLoading: false,
      updatedDetails:{},
      storelist: [],
      selectedStore: null,
      amount:"",
      constants: commonConstants,
      user: JSON.parse(localStorage.getItem("user")),
    };
  },
  components: {
    HourGlass,
    CanPayLoader
  },
  created() {
    this.eCommerceVoidLeaveComment();
  },
  methods: {
    //API call to fetch All stores
    getAllStores(searchtxt) {
      var self = this;
      if(searchtxt.length >= 3){
        self.isLoading = true;
        var request = {
          searchtxt: searchtxt,
          is_ecommerce: 1,
        };
      api
        .getStores(request)
        .then(function (response) {
          if (response.code == 200) {
            self.storelist = response.data;
            self.isLoading = false;
          }else {
            error(response.message);
            self.isLoading = false;
          }
        })
        .catch(function (error) {
        });
      }
    },
    // API call to generate the merchant location transaction report
    generateReport(reportExport) {
      var self = this;
      if (
        moment($("#start-date").val()).format("YYYY-MM-DD") >
        moment().format("YYYY-MM-DD") && $("#start-date").val()!= ''
      ) {
        error("Start date cannot be from future.");
        return false;
      }
      if (
        moment($("#end-date").val()).format("YYYY-MM-DD") >
        moment().format("YYYY-MM-DD") && $("#end-date").val()!= ''
      ) {
        error("End date cannot be from future.");
        return false;
      }

      var from_date = $("#start-date").val()!=''?moment($("#start-date").val()).format("YYYY-MM-DD"):'';
      var to_date = $("#end-date").val()!=''?moment($("#end-date").val()).format("YYYY-MM-DD"):'';

      if(self.selectedStore === null){
        var store_id = '';
        error("Please select a store");
        return false;
      }else{
        var store_id = self.selectedStore.id;
      }

      self.report = [];
      var request = {
        from_date: from_date,
        to_date: to_date,
        amount: self.amount,
        store_id:store_id
      };
      if(request.from_date > request.to_date){
        error("To Date cannot be greater than From date");
        return false;
      }
      self.loading = true;
      api
        .generateVoidTransactionReport(request)
        .then(function (response) {
          if (response.code == 200) {
            self.report = response.data;
            if(self.report.length > 0){
              if (reportExport) {
                self.exportReport();
              } else {
                self.loading = false;
              }
            }else {
              error("No records found!");
              self.loading = false;
            }
          } else {
            error(response.message);
            self.loading = false;
          }
        })
        .catch(function (error) {
          // error(error);
          self.loading = false;
        });
    },

    // exports the report
    exportReport() {
      var self = this;
      self.loading = true;

      if($("#start-date").val()!=''){
        var from_date = moment($("#start-date").val()).format("YYYY-MM-DD");
      }else{
        var from_date = '';
      }
      if($("#end-date").val()!=''){
        var to_date = moment($("#end-date").val()).format("YYYY-MM-DD");
      }else{
        var to_date = '';
      }

      var request = {
        report: self.report,
      };
      api
        .exportgenerateVoidTransactionReport(request)
        .then(function (response) {
          var FileSaver = require("file-saver");
          var blob = new Blob([response], {
            type: "application/xlsx",
          });
          FileSaver.saveAs(
            blob,
            moment().format("MM/DD/YYYY") + "_transaction_report.xlsx"
          );
          self.loading = false;
        })
        .catch(function (error) {
          // error(error);
          self.loading = false;
        });
    },
    eCommerceVoidLeaveComment() {
      var self = this;
      $(document).on("click", ".ecommerce-void-leave-comment", function (e) {
        //open the modal
        self.comment = "";
        // self.$refs["comment-modal"].show();
        //set transaction id to a hidden field for future use
        self.transaction_id = $(e.currentTarget).attr("data-id");
      });
    },
    cancelTransaction(bvModalEvt) {
      var self = this;
      self.updatedDetails = self.report.find(
            p => p.edit == self.transaction_id
        );
      if (self.comment == "") {
        self.showMsg = true;
        bvModalEvt.preventDefault();
      } else {
        self.showMsg = false;
        var request = {
          transaction_id: self.transaction_id,
          comment: self.comment,
          transaction_place: "Admin",
        };
        api
          .cancelTransaction(request)
          .then((response) => {
            if ((response.code = 200)) {
              success(response.message);
              self.$refs["comment-modal"].hide();
              self.generateReport(false);
            } else {
              error(response.message);
            }
          })
          .catch((err) => {
            error(err);
          });
      }
    },
    revokeVoidTransaction(bvModalEvt){
      var self = this;
      self.updatedDetails = self.report.find(
            p => p.edit == self.transaction_id
        );
      if (self.void_revoke_comment == "") {
        self.showVoidMsg = true;
        bvModalEvt.preventDefault();
      } else {
        self.showVoidMsg = false;
        var request = {
          transaction_id: self.transaction_id,
          comment: self.void_revoke_comment,
          transaction_place: "Admin",
        };
        api
          .revokeVoidTransaction(request)
          .then((response) => {
            if ((response.code = 200)) {
              success(response.message);
              self.$refs["void-revoke-comment-modal"].hide();
              self.generateReport(false);
            } else {
              error(response.message);
            }
          })
          .catch((err) => {
            error(err);
          });
      }
    },
    reset(){
        var self = this;
        self.amount = "";
        self.selectedStore = null;
    }
  },
  mounted() {
    $("#start-date").datepicker({
      format: "mm/dd/yyyy",
      autoclose: true,
      todayHighlight: true,
    });
    $("#end-date").datepicker({
      format: "mm/dd/yyyy",
      autoclose: true,
      todayHighlight: true,
    });
    $("#start-date , #end-date").datepicker("setDate", new Date());
  },
};
</script>

