<template>
  <div class="card-body row">
    <div class="col-12 d-flex justify-content-end">
      <slot name="delete-btn"></slot>
    </div>
    <div class="col-6">
      <label for="segment_id">
        Segment
        <span class="red">*</span>
      </label>
      <select
        class="form-control"
        :name="'segment[' + index + '][segment_id]'"
        v-model="input.segment_id"
        v-validate="'required'"
        style="text-transform: capitalize"
      >
        <option
          v-for="segmentData in segments"
          :selected="segmentData.id == input.segment_id"
          :key="segmentData.id"
          :value="segmentData.id"
        >
          {{ segmentData.segment_name }}
        </option>
      </select>
      <span
        v-show="errors.has('segment[' + index + '][segment_id]')"
        class="text-danger"
        >{{ errors.first("segment[" + index + "][segment_id]") }}</span
      >
    </div>
    <div class="col-6">
      <label for="value">
        Value
        <span class="red">*</span>
      </label>
      <input
        v-model="input.value"
        type="number"
        :name="'segment[' + index + '][value]'"
        class="form-control"
        placeholder="Enter value"
        v-validate="'required'"
      />
      <span
        v-show="errors.has('segment[' + index + '][value]')"
        class="text-danger"
        >{{ errors.first("segment[" + index + "][value]") }}</span
      >
    </div>
    <div class="col-6">
      <label for="probability">
        Probability
        <span class="red">*</span>
      </label>
      <input
        v-model="input.probability"
        type="number"
        :name="'segment[' + index + '][probability]'"
        class="form-control"
        placeholder="Enter probability"
        v-validate="'required'"
      />
      <span
        v-show="errors.has('segment[' + index + '][probability]')"
        class="text-danger"
        >{{ errors.first("segment[" + index + "][probability]") }}</span
      >
    </div>
    <div class="col-6">
      <label for="definition">
        Definition
      </label>
      <input
        v-model="input.definition"
        type="text"
        :name="'segment[' + index + '][definition]'"
        class="form-control"
        placeholder="Enter definition"
      />
    </div>
    <div class="col-6">
      <label for="use_within">
        Use Within (Hours)
        <span class="red">*</span>
      </label>
      <input
        v-model="input.use_within"
        type="number"
        :name="'segment[' + index + '][use_within]'"
        class="form-control"
        placeholder="Enter use within"
        v-validate="'required'"
      />
      <span
        v-show="errors.has('segment[' + index + '][use_within]')"
        class="text-danger"
        >{{ errors.first("segment[" + index + "][use_within]") }}</span
      >
    </div>

    <div class="col-4">
      <label for="color">
        Color <small>(Hex color code)</small>
        <span class="red">*</span>
      </label>
      <br />
      <v-swatches
        v-model="input.color"
        :name="'segment[' + index + '][color]'"
        :swatches="swatches"
        row-length="5"
        popover-x="left, right"
      ></v-swatches>
    </div>
    
    <div class="col-4">
      <label for="image">
        Image
        <span class="red">*</span>
      </label>
      <Uploader v-model="input.image" />
    </div>
  </div>
</template>

<script>
import Uploader from "./Uploader.vue";
import VSwatches from 'vue-swatches'
import 'vue-swatches/dist/vue-swatches.css'

export default {
    components:{
        Uploader,
        VSwatches
    },
    props:{
        input: {
            default: {},
            type: Object
        },
        index: {
            default: 0,
            type: Number
        },
        segments: {
            default: [],
            type: Array
        }
    },
    data(){
        return{
            swatches: [
                ['#00D760', '#C1FCC7', '#F493A7', '#F891A6', '#FFCCD5' ],
                ['#8b5aff', '#a27bff', '#b99cff', '#d0bdff', '#e8deff' ],
                ['#51e5db', '#74ebe3', '#96f0ea', '#b9f5f1', '#dcfaf8' ],
                ['#ffa51a', '#ffb748', '#ffc976', '#ffdba3', '#ffedd1', '#000', '#ffcb23', '#fff' ]
            ],
        }
    },
    methods:{
        
    }
}
</script>