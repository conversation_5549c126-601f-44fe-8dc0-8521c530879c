<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
class TransactionDetails extends Model
{
    protected $table = 'transaction_details';

    /**
     * __construct
     * This function will insert an alphanumeric 32 character unique id as primary key in the table whenever a data get inserted
     * @param  mixed $attributes
     * @return void
     */
    public function __construct(array $attributes = [])
    {
        $this->id = generateUUID();

        parent::__construct($attributes);
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'transaction_number',
        'user_id',
        'consumer_id',
        'merchant_id',
        'terminal_id',
        'pos_transaction_time',
        'amount',
        'used_token_id',
        'used_qr_id',
        'transaction_type_id',
        'status_id',
        'actual_transaction_time',
        'transaction_ref_no',
        'acheck_document_id',
        'acheck_client_id',
        'entry_type',
        'isCanpay',
        'comment',
        'transaction_after_store_closed',
        'change_request',
        'consumer_approval_for_change_request',
        'price_increase',
        'updated_amount',
        'change_request_transaction_ref_no',
        'scheduled_posting_date',
        'attempt_count'
    ];
    public $timestamps = true;
    public $incrementing = false;
    /**
     * The attributes excluded from the model's JSON form.
     *
     * @var array
     */
}
