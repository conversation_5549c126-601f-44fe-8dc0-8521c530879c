<template>
  <!-- Navbar -->
  <nav class="main-header navbar navbar-expand navbar-white navbar-light">
    <!-- Left navbar links -->
    <ul class="navbar-nav">
      <li class="nav-item">
        <a class="nav-link" data-widget="pushmenu" href="#" role="button">
          <i class="fas fa-bars"></i>
        </a>
      </li>
    </ul>

    <!-- Right navbar links -->
    <ul class="navbar-nav ml-auto">
      <li class="nav-item">
        Logged in as
        <b>{{user.first_name}}  {{user.last_name}}</b>
      </li>
    </ul>
  </nav>
  <!-- /.navbar -->
</template>
<script>
export default {
  name: "TopNavbar",
  data() {
    return {
      user: JSON.parse(localStorage.getItem("user")),
    };
  },
};
</script>
