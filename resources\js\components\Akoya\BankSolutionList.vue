<template>
<div>
  <div  v-if="loading">
    <CanPayLoader/>
  </div>
  <div class="content-wrapper" style="min-height: 36px">
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6"></div>
        </div>
      </div>
    </section>
    <div class="hold-transition sidebar-mini">
      <section class="content">
        <div class="container-fluid">
          <div class="row">
            <div class="col-12">
              <div class="card card-success">
                <div class="card-header">
                  <h3 class="card-title">Banking Solution List</h3>
                </div>

                 <div class="card-body">
                  <b-table-simple
                      responsive
                      show-empty
                      bordered
                      sticky-header="800px"
                      v-if="allBankSolutionModel.length > 0"
                    >
                      <b-thead head-variant="light">
                        <tr>
                          <th>Bank Solution Name</th>
                          <th>Sequence</th>
                          <th>Status</th>
                          <th>Available for Registration / Onboarding</th>
                          <th>Available for Bank Change</th>
                          <th class="text-center">Action</th>
                        </tr>
                      </b-thead>
                      <b-tbody v-for="(row, index) in allBankSolutionModel" :key="index">
                        <b-tr>
                          <b-td class="text-left text-gray">{{
                            capitalizeFirstLetter(row.banking_solution_name)
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.sequence_no
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.status_name
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.on_registration == 1 ? 'Yes' : 'No'
                          }}</b-td>
                          <b-td class="text-left text-gray">{{
                            row.on_bank_change == 1 ? 'Yes' : 'No'
                          }}</b-td>
                          <b-td class="text-center text-gray">
                            <a :data-user-id="row.id" class="custom-edit-btn" title="Edit Banking Solution" variant="outline-success" style="border:none" @click="editBank(row)"><i class="nav-icon fas fa-edit"></i></a>
                          </b-td>
                        </b-tr>
                      </b-tbody>
                    </b-table-simple>
                    <p v-else>No data displayed. Please refine your search criteria.</p>
                    </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
    <!-- CP Modal Start -->
    <b-modal
      id="bank-modal"
      ref="modal"
      :header-text-variant="headerTextVariant"
      :title="modalTitle"
      @show="resetModal"
      @hidden="resetModal"
      ok-title="Save"
      ok-variant="success"
      cancel-variant="outline-secondary"
      @ok="handleOk"
      :no-close-on-esc="true"
      :no-close-on-backdrop="true"
    >
      <form ref="form" @submit.stop.prevent="save" class="needs-validation">

        <div class="row">
          <div class="col-md-12">
            <label for="bank_name">
              Bank Name
            </label>
            <input
              id="bank_name"
              name="bank_name"
              type="text"
              disabled='disabled'
              v-model="bankModel.banking_solution_name"
              class="form-control"
            />
          </div>
          <div class="col-md-12">
            <label for="sequence">
              Sequence
              <span class="red">*</span>
            </label>
            <input
              id="sequence"
              name="sequence"
              type="text"
              v-validate="'required|numeric|max:2'"
              v-model="bankModel.sequence_no"
              class="form-control"
            />
            <span v-show="errors.has('sequence')" class="text-danger">{{
              errors.first("sequence")
            }}</span>
          </div>
        </div>
        <div class="row">
          <div class="col-md-12">
            <label for="bank_status">
              Status
              <span class="red">*</span>
            </label>
            <select
              class="form-control"
              id="bank_status"
              name="bank_status"
              v-model="bankModel.status_id"
              placeholder="Select Status"
              v-validate="'required'"
              @change="validateStatus"
            >
              <option
                v-for="(status, index) in banksolutionstatus"
                :key="index"
                :value="status.id"
              >
                {{ status.status }}
              </option>
            </select>
          </div>
        </div>
          <div class="row" v-if="bankModel.status_id == activeStatusID">
            <div class="col-md-12">
              <input type="checkbox" id="checkbox1" v-model="bankModel.on_registration" />
              <label for="checkbox1">Available for Registration / Onboarding</label>
            </div>
            <div class="col-md-12">
              <input type="checkbox" id="checkbox2" v-model="bankModel.on_bank_change" />
              <label for="checkbox2">Available for Bank Change</label>
            </div>
          </div>
      </form>
    </b-modal>
    <!-- CP Modal End -->
  </div>
</div>
</template>
<script>
import api from "@/api/bank.js";
import { validationMixin } from "vuelidate";
import { HourGlass } from "vue-loading-spinner";
import CanPayLoader from "../CustomLoader/CanPayLoader.vue"
export default {
  mixins: [validationMixin],
  data() {
    return {
      bankModel: {},
      allBankSolutionModel: {},
      banksolutionstatus: [],
      currentUser: localStorage.getItem("user")
        ? JSON.parse(localStorage.getItem("user"))
        : null,
      loading:false,
      activeStatusID:'',
      headerTextVariant: "light",
      modalTitle: "Edit Banking Solution",
    };
  },
  components: {
    HourGlass,
    CanPayLoader
  },
  created() {
    this.searchBank();
    this.getBankSolutionStatus();
  },
  watch: {
  },
  methods: {
    resetModal() {
      var self = this;
    },
    handleOk(bvModalEvt) {
      var self = this;
      // Prevent modal from closing
      bvModalEvt.preventDefault();
      // Trigger submit handler
      self.editSaveBank();
    },
    searchBank(){
      var self = this;
      self.loading = true;
      api
      .bankSolutionList()
      .then(function (response) {
        if (response.code == 200) {
          self.allBankSolutionModel = response.data;
          self.loading = false;
        } else {
          error(response.message);
          self.loading = false;
        }
      })
      .catch(function (error) {
        // error(error);
        self.loading = false;
      });
    },
    getBankSolutionStatus(){
      var self = this;
      self.loading = true;
      api
      .getBankSolutionStatus()
      .then(function (response) {
        if (response.code == 200) {
          self.banksolutionstatus = response.data;
          self.activeStatusID = response.data.find(item => item.code == '204').id;
          console.log(self.activeStatusID);
          self.loading = false;
        } else {
          error(response.message);
          self.loading = false;
        }
      })
      .catch(function (error) {
        // error(error);
        self.loading = false;
      });
    },
    editBank(bank){
      var self = this;
      self.bankModel = Object.assign({}, bank);
      self.bankModel.banking_solution_name = self.capitalizeFirstLetter(bank.banking_solution_name);
      self.$bvModal.show("bank-modal");
    },
    editSaveBank(bank){
      var self = this;
      // Exit when the form isn't valid
      this.$validator.validateAll().then((result) => {
        if (result) {
          self.loading = true;
          self.editBankApi(self);
        }
      });
    },
    editBankApi(self) {
      api
        .editBankSolution(self.bankModel)
        .then((response) => {
          self.loading = false;
          if (response.code == 200) {
            self.$bvModal.hide("bank-modal");
            success(response.message);
            self.searchBank();
          } else {
            error(response.message);
          }
        })
        .catch((err) => {
          self.loading = false;
          error(err.response.data.message);
        });
    },
    capitalizeFirstLetter(str) {
      return str.charAt(0).toUpperCase() + str.slice(1);
    },
    validateStatus(){
      var self = this;
      if (self.bankModel.status_id == self.activeStatusID) {
        self.bankModel.on_registration = 1;
        self.bankModel.on_bank_change = 1;
      } else {
        self.bankModel.on_registration = 0;
        self.bankModel.on_bank_change = 0;
      }
    }

  },
  mounted() {
    var self = this;
    document.title = "CanPay - Bank Solution List";
  },
};
</script>

