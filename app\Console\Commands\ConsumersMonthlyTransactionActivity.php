<?php

namespace App\Console\Commands;

use App\Http\Factories\EmailExecutor\EmailExecutorFactory;
use App\Models\User;
use Illuminate\Console\Command;

class ConsumersMonthlyTransactionActivity extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'consumers:email_transactions';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This command will email the previous months transaction to all the consumers';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->emailexecutor = new EmailExecutorFactory();
    }
    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->info("Processing....");
        $consumers = User::join('user_roles', 'users.role_id', '=', 'user_roles.role_id')->where('user_roles.role_name', CONSUMER)->get();
        foreach ($consumers as $consumer) {
            // Sending Monthly Activity Mail to the Consumer
            $email_params = [
                'user_id' => $consumer->user_id,
                'month_year' => date('F, Y', strtotime(date('Y-m') . " -1 month")),
            ];
            $this->emailexecutor->consumerMonthlyTransactionActivity($email_params);
        }
        $this->info("Consumers Monthly Activity Email sent successfully.");
    }
}
