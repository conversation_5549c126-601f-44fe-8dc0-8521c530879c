<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Events\AfterSheet;
use Illuminate\Support\Facades\Log;

class ReturnTransactionGroupExport implements FromCollection, WithHeadings, ShouldAutoSize, WithEvents
{
    protected $request;

    public function __construct($request)
    {
        $this->request = $request; // Declaring the request variable
    }

    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        $collection_array = $this->request->get('returnTransaction'); // Storing the array received from request

        $transactions = [];
        foreach($collection_array as $transaction){
            $nestedData['reason_code'] = $transaction['status'] == null ? $transaction['reason_code']. ' Total' : $transaction['reason_code'];
            $nestedData['total_count'] = $transaction['total_count'];
            $nestedData['total_return_amount'] = $transaction['total_return_amount'];
            $nestedData['status'] = $transaction['status'];
            array_push($transactions,$nestedData);
        }

        return collect([
            $transactions,
        ]);
    }

    public function headings(): array
    {
        //Return Details Array For the Top table
        $i = 0;
        foreach($this->request->get('returnDetails') as $valReturn){
            $returnArr[$i] = [];
            array_push($returnArr[$i],$valReturn['title'],"$".$valReturn['amount']);
            $i++;
        }

        $returnArray = array(
            [
                'CanPay Group Return Transaction ',
            ],
            // 2nd header
            [
                ' Group Return Transaction Transaction Date Range:',
                date('m/d/Y', strtotime($this->request->get('from_date'))) . ' - ' . date('m/d/Y', strtotime($this->request->get('to_date'))),
            ],
            [],
            $returnArr[0],
            $returnArr[1],
            $returnArr[2],
            $returnArr[3],
            [],
            [
                'Return Reason',
                'Count',
                'Amount ($)',
                'Status'

            ],
        );

        return $returnArray;
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event){
                $event->sheet->getStyle('A1:E1')->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);

                //Returns total Value
                $event->sheet->getStyle('A7:B7')->applyFromArray([
                    'font' => [
                        'bold' => true,
                    ],
                ]);

                //Apply Center Alignment
                $event->sheet->getStyle('A:E')->getAlignment()->applyFromArray(
                    array('horizontal' => 'center')
                );
            },
        ];
    }
}
